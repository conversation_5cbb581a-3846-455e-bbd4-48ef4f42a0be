version: '1.0'
name: frontend-build-optimized
stages:
  - stage: 
    name: build
    jobs:
      - job:
        name: build-job
        pool:
          vmImage: ubuntu-latest
        variables:
          NODE_VERSION: '18'
          PNPM_VERSION: '8'
        steps:
          - task: NodeTool@0
            displayName: '安装 Node.js'
            inputs:
              versionSpec: $(NODE_VERSION)
          
          - script: |
              npm install -g pnpm@$(PNPM_VERSION)
              echo "pnpm version: $(pnpm --version)"
            displayName: '安装 pnpm'
          
          - task: Cache@2
            displayName: '缓存 pnpm 依赖'
            inputs:
              key: 'pnpm | "$(Agent.OS)" | pnpm-lock.yaml'
              restoreKeys: |
                pnpm | "$(Agent.OS)"
              path: ~/.pnpm-store
          
          - script: |
              pnpm config set store-dir ~/.pnpm-store
              pnpm install --frozen-lockfile
            displayName: '安装依赖'
          
          - task: Cache@2
            displayName: '缓存 Vite 构建缓存'
            inputs:
              key: 'vite | "$(Agent.OS)" | package.json'
              restoreKeys: |
                vite | "$(Agent.OS)"
              path: node_modules/.vite
          
          - script: |
              pnpm run build:prod
            displayName: '构建项目'
            env:
              NODE_OPTIONS: '--max-old-space-size=4096'
          
          - task: PublishBuildArtifacts@1
            displayName: '发布构建产物'
            inputs:
              pathToPublish: 'dist'
              artifactName: 'frontend-dist'
