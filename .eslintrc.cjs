/*
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2025-02-21 11:15:25
 * @Description:
 *
 */
module.exports = {
  root: true,
  parser: 'babel-eslint',
  parserOptions: {
    // 想使用的额外的语言特性
    ecmaFeatures: {
      legacyDecorators: true, // 装饰器
    },
  },
  // 添加共享设置, 在共享设置中设置的对象能被任一即将执行的规则获取到，所以这在开发自定义规则插件而言很重要
  settings: {
    polyfills: ['fetch', 'promises', 'url', 'object-assign'],
  },
  // extends 属性值可以省略包名的前缀 eslint-config-
  // 你可能想要将你的配置信息分享给其他项目或人。可分享的配置允许你在 npm 发布你的配置设置并且其他人可以在他的 ESLint 项目中下载使用这些配置。
  // 可共享的配置 是一个 npm 包，它输出一个配置对象。
  extends: ['airbnb', 'prettier', 'plugin:compat/recommended'],
  // 插件一般是输出规则,一些插件也可以输出一个或多个命名的 配置
  plugins: ['react'],
  // 指定脚本的运行环境。每种环境都有一组特定的预定义全局变量。
  env: {
    browser: true,
    node: true,
    es6: true,
    mocha: true,
    jest: true,
    jasmine: true,
  },
  globals: {
    localStorage: true,
    sessionStorage: true,
    WeixinJSBridge: true,
    screen: true,
    wx: true,
    qq: true,
    alert: true,
    Image: true,
    File: true,
    FormData: true,
    IntersectionObserver: true,
    fiboSDK: true,
  },
  rules: {
    // 规则集，会覆盖extends中的规则，多用于关闭不符合项目使用场景的规则//  不检验函数入参是否被使用
    'no-unused-vars': ['warn', { args: 'none' }],
    indent: ['off', 2],
  },
}
