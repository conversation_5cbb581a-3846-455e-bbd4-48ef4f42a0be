/*
 * @Author: llm
 * @Date: 2025-05-23 11:16:15
 * @LastEditors: llm
 * @LastEditTime: 2025-06-18 16:54:16
 * @Description:
 */
module.exports = {
  parser: '@babel/eslint-parser',
  extends: [
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/typescript/recommended',
    '@vue/prettier',
    '@vue/prettier/@typescript-eslint',
    './.eslintrc-auto-import.json', // 在这里配置生成的JSON文件 需要和vite.config.ts文件保持一致
  ],
}
