{"arrowParens": "always", "bracketSameLine": false, "bracketSpacing": true, "semi": false, "experimentalTernaries": false, "singleQuote": true, "jsxSingleQuote": false, "quoteProps": "as-needed", "trailingComma": "all", "singleAttributePerLine": false, "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": true, "proseWrap": "preserve", "insertPragma": false, "printWidth": 160, "requirePragma": false, "tabWidth": 2, "useTabs": false, "embeddedLanguageFormatting": "auto"}