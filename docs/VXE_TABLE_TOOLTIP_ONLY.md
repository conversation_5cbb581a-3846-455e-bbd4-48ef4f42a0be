# VXE Table 专用 Tooltip 层级修复方案

## 问题描述

之前的全局 tooltip 层级修复影响了所有组件中的 tooltip，现在需要只针对 VXE Table 中的 tooltip 进行层级修复，不影响其他组件。

## 解决方案

### 1. 移除全局 tooltip 样式修复

**文件**: `src/styles/index.scss`

移除了全局的 tooltip z-index 设置，改为只针对 VXE Table 容器的样式。

### 2. 创建 VXE Table 专用样式文件

**文件**: `src/styles/vxe-table-tooltip.scss`

创建了专门针对 VXE Table 的 tooltip 样式文件，包含：
- `.vxe-table .el-tooltip__popper` - VXE Table 容器内的 tooltip
- `.vxe-table .smart-tooltip-high-z-index` - 智能 tooltip 样式类
- 弹窗中的 VXE Table tooltip 样式
- 多种选择器确保覆盖

### 3. 更新 SmartTooltip 组件

**文件**: `src/components/SmartTooltip/index.vue`

修改了 SmartTooltip 组件的逻辑：
- 只在检测到 VXE Table 环境时应用高层级样式
- 移除了全局弹窗检测逻辑
- 只对 VXE Table 容器内的 tooltip 进行强制层级设置

### 4. 更新 tooltipConfig.ts

**文件**: `src/utils/tooltipConfig.ts`

修改了 tooltip 配置工具：
- `getTooltipZIndex()` 只在 VXE Table 环境中返回高层级
- `updateTooltipZIndex()` 只更新 VXE Table 容器内的 tooltip
- 移除了全局 tooltip 处理逻辑

### 5. 移除全局自动修复

**文件**: `src/main.ts`

移除了以下全局自动调用：
- `setTooltipZIndex()` - 全局 tooltip 层级设置
- `initTooltipForceFix()` - 强制修复初始化
- 相关导入语句

### 6. VXE Table 组件专用管理

**文件**: `src/components/VxeTableComponent/index.vue`

添加了 `initVxeTableTooltipZIndex()` 函数：
- 只监听和处理 VXE Table 容器内的 tooltip
- 在组件挂载时自动初始化
- 实时监听新创建的 tooltip

### 7. 禁用自动执行的修复工具

**文件**: `src/utils/quickTooltipFix.ts`

移除了自动执行逻辑，只保留手动调用接口。

## 使用方法

### 在 VXE Table 中使用 SmartTooltip

```vue
<template>
  <vxe-table>
    <vxe-column>
      <template #default="scope">
        <SmartTooltip :content="scope.row.content">
          <span>{{ scope.row.text }}</span>
        </SmartTooltip>
      </template>
    </vxe-column>
  </vxe-table>
</template>

<script setup>
import SmartTooltip from '@/components/SmartTooltip/index.vue'
</script>
```

### 在普通组件中使用 el-tooltip

```vue
<template>
  <!-- 普通组件中的 tooltip 不会被影响，使用默认层级 -->
  <el-tooltip content="这是普通 tooltip">
    <el-button>普通按钮</el-button>
  </el-tooltip>
</template>
```

## 层级规划

- **VXE Table 中的 tooltip**: z-index: 9999
- **其他组件中的 tooltip**: 使用 Element Plus 默认层级
- **弹窗**: z-index: 2000-3000 (Element Plus 默认)

## 验证方法

### 1. 检查 VXE Table 中的 tooltip

1. 打开包含 VXE Table 的页面
2. 打开弹窗
3. 将鼠标悬停在弹窗中的 VXE Table 列上
4. 确认 tooltip 显示在正确位置，不被遮挡

### 2. 检查其他组件中的 tooltip

1. 在非 VXE Table 的组件中使用 el-tooltip
2. 确认 tooltip 使用默认层级，不会过高

### 3. 浏览器控制台检查

```javascript
// 检查 VXE Table 中的 tooltip
document.querySelectorAll('.vxe-table .el-tooltip__popper').forEach(el => {
  console.log('VXE Table tooltip z-index:', window.getComputedStyle(el).zIndex)
})

// 检查其他 tooltip
document.querySelectorAll('.el-tooltip__popper:not(.vxe-table .el-tooltip__popper)').forEach(el => {
  console.log('Other tooltip z-index:', window.getComputedStyle(el).zIndex)
})
```

## 优势

1. **精确控制**: 只影响 VXE Table 中的 tooltip
2. **不影响其他组件**: 其他组件的 tooltip 保持默认行为
3. **向后兼容**: 不需要修改现有的非 VXE Table 代码
4. **性能优化**: 移除了全局监听和修复逻辑
5. **易于维护**: 样式和逻辑都集中在 VXE Table 相关文件中

## 相关文件清单

- `src/styles/vxe-table-tooltip.scss` - VXE Table 专用样式
- `src/styles/index.scss` - 主样式文件（引入专用样式）
- `src/components/SmartTooltip/index.vue` - 更新的智能组件
- `src/components/VxeTableComponent/index.vue` - VXE Table 组件
- `src/utils/tooltipConfig.ts` - 更新的配置工具
- `src/main.ts` - 移除全局调用
- `src/utils/quickTooltipFix.ts` - 禁用自动执行

## 总结

通过这次修改，我们实现了：

1. **精确控制**: 只有 VXE Table 中的 tooltip 使用高层级
2. **不影响其他组件**: 其他组件的 tooltip 保持原有行为
3. **性能提升**: 移除了全局监听和修复逻辑
4. **代码清晰**: 相关逻辑集中在 VXE Table 相关文件中

现在 VXE Table 中的 tooltip 会正确显示在弹窗的最上层，而其他组件中的 tooltip 不会受到影响。
