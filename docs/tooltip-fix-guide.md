# VXETable Tooltip 层级问题解决方案

## 问题描述

在页面有弹窗的情况下，当鼠标移入弹窗中的 VXETable 列时，tooltip 会穿透到下面的表格中显示内容。这是一个常见的 z-index 层级问题。

## 已实施的解决方案

我们已经实施了多层次的解决方案来彻底解决这个问题：

### 方案一：强力全局 CSS 样式修复（已实施）

在 `src/styles/index.scss` 中添加了强力的样式规则：

```scss
/* 强制所有 tooltip 使用高层级 */
.el-tooltip__popper {
  z-index: 3000 !important;
}

/* 使用多种选择器确保覆盖 */
.el-tooltip__popper[data-popper-placement],
div[role="tooltip"].el-tooltip__popper,
body .el-tooltip__popper,
[class*="el-tooltip__popper"] {
  z-index: 3000 !important;
}
```

这个方案的优点：
- 全局生效，无需修改任何组件代码
- 使用多种选择器确保覆盖所有情况
- 立即生效，无需重启应用

### 方案二：全局自动化层级管理（已实施）

在 `src/main.ts` 中启用了全局 tooltip 层级管理：

```typescript
import { setTooltipZIndex } from '@/utils/tooltipConfig'

// 应用启动时初始化
setTooltipZIndex()
```

这个系统会：
- 自动监听弹窗的打开和关闭
- 实时更新所有 tooltip 的 z-index
- 定期检查并修复层级问题

### 方案三：智能 Tooltip 组件（最佳实践）

创建了 `src/components/SmartTooltip/index.vue` 组件，自动处理层级问题：

```vue
<template>
  <SmartTooltip content="提示内容" placement="top">
    <div>需要提示的内容</div>
  </SmartTooltip>
</template>

<script setup>
import SmartTooltip from '@/components/SmartTooltip/index.vue'
</script>
```

SmartTooltip 组件特性：
- 自动检测是否在弹窗中
- 动态调整 z-index
- 监听 DOM 变化，实时更新层级
- 完全兼容 Element Plus Tooltip 的所有属性

## 使用建议

1. **新项目**：推荐使用 SmartTooltip 组件
2. **现有项目**：先应用全局 CSS 样式修复，然后逐步替换为 SmartTooltip 组件
3. **特殊场景**：使用 tooltipConfig 工具进行精细控制

## 层级规划

- 默认 tooltip：z-index: 2500
- 弹窗中的 tooltip：z-index: 3000
- 抽屉中的 tooltip：z-index: 2800
- 气泡框中的 tooltip：z-index: 2600

## 测试方法

1. 打开包含 VXETable 的页面
2. 打开一个弹窗，弹窗中也包含 VXETable
3. 将鼠标悬停在弹窗中的表格列上
4. 确认 tooltip 显示在正确的位置，不会穿透到下层

## 注意事项

1. 如果使用了自定义的弹窗组件，可能需要调整 CSS 选择器
2. 某些第三方组件可能有自己的 z-index 设置，需要相应调整
3. 在使用 SmartTooltip 时，确保正确导入组件

## 故障排除

如果问题仍然存在：

1. 检查浏览器开发者工具中的 z-index 值
2. 确认 CSS 样式是否正确加载
3. 检查是否有其他样式覆盖了我们的设置
4. 尝试增加 z-index 的值

## 相关文件

- `src/styles/index.scss` - 全局样式修复
- `src/utils/tooltipConfig.ts` - 工具函数
- `src/components/SmartTooltip/index.vue` - 智能 Tooltip 组件
- `src/components/VxeTableComponent/index.vue` - VXETable 组件（已更新）
