<!--
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2025-03-31 16:43:41
 * @Description:
 *
-->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="一体化数智平台" />
    <meta name="keywords" content="一体化数智平台" />
    <title></title>
    <script>
      window.forceWebGL = true
      // if ("<%= process.env.NODE_ENV %>" === "development") {
      //   window._AMapSecurityConfig = {
      //     serviceHost: "https://hapi.haodaoda.com/_AMapService",
      //   };
      // } else {
      //   window._AMapSecurityConfig = {
      //     serviceHost: window.location.origin + "/_AMapService",
      //   };
      // }
    </script>
    <script src="https://webapi.amap.com/loader.js"></script>
    <!-- <script
    src="https://webapi.amap.com/maps?v=2.0&key=7d71c09f69eab6fb826909264fb32d97&plugin=AMap.PolygonEditor,AMap.Autocomplete,AMap.PlaceSearch,AMap.Scale,AMap.OverView,AMap.ToolBar,AMap.MapType,AMap.PolyEditor,AMap.CircleEditor,AMap.Geolocation,AMap.Geocoder,AMap.DistrictSearch,AMap.AMapUI,AMap.MarkerClusterer">

    </script> -->
    <!-- 高德地图本地化 -->
    <script src="https://api.haodaoda.com/static/map/maps.js"></script>
    <!--引入UI组件库（1.1版本） -->
    <script src="https://webapi.amap.com/ui/1.1/main.js"></script>
    <!-- 引入百度地图gl -->
    <!--    <script src="https://unpkg.com/mapvgl@1.0.0-beta.186/dist/mapvgl.min.js"></script>-->
    <!--    <script src="https://unpkg.com/mapvgl@1.0.0-beta.186/dist/mapvgl.threelayers.min.js"></script>-->
    <script src="https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.188/dist/mapvgl.min.js"></script>
    <!-- 如果使用到Three.js相关的图层需要引用 -->
    <script src="https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.188/dist/mapvgl.threelayers.min.js"></script>
  </head>

  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
