server {
    listen       80;
    listen  [::]:80;
    server_name  localhost;

    access_log  /var/log/nginx/host.access.log  main;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;

        # 新增下面这句，其他是默认nginx配置
        # 解决部分前端框架的路由问题，在浏览器刷新报错404
        try_files $uri $uri/ /index.html;
    }
    location /dev-api/ {
            proxy_pass https://hapi.haodaoda.com/api/hdd;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            rewrite ^/dev-api/(.*)$ /$1 break;
        }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
