{"name": "vehicle_transportation_management_system", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite serve --mode development", "build:prod": "vite build --mode production", "build:prod-with-check": "vite build --mode production && vue-tsc --noEmit", "build:analyze": "vite build --mode production && npx vite-bundle-analyzer dist/stats.html", "type-check": "vue-tsc --noEmit", "prepare": "husky install", "lint:eslint": "eslint  --fix --ext .ts,.js,.vue ./src ", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "commit": "git-cz", "test": "vitest", "test:coverage": "vitest --coverage"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@jiaminghi/data-view": "^2.10.0", "@kjgl77/datav-vue3": "^1.7.1", "@vitejs/plugin-vue": "^5.0.4", "@vueup/vue-quill": "1.0.0-alpha.40", "@vueuse/core": "^10.1.2", "@vxe-ui/plugin-render-element": "4.0.11", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "5.1.10", "axios": "^1.4.0", "bignumber.js": "^9.1.2", "bootstrap": "^5.3.3", "clipboard": "^2.0.11", "echarts": "^5.4.2", "element-plus": "^2.10.5", "eslint-config-airbnb": "^19.0.4", "file-saver": "^2.0.5", "fuse.js": "^7.0.0", "jquery": "^3.7.1", "jsbarcode": "^3.11.6", "lodash": "^4.17.21", "lodash.clonedeep": "^4.5.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.0", "pinia": "^2.0.33", "pinia-plugin-persistedstate": "^3.1.0", "popper.js": "^1.16.1", "qrcode": "^1.5.4", "qrcodejs": "^1.0.0", "quill-image-uploader": "^1.3.0", "react": "^18.2.0", "screenfull": "^6.0.0", "sortablejs": "^1.15.2", "uuid": "^11.0.3", "view-ui-plus": "^1.3.17", "vite-plugin-html": "^3.2.2", "vite-plugin-vue-setup-extend": "^0.4.0", "vue": "^3.4.21", "vue-baidu-map-3x": "^1.0.37", "vue-draggable-plus": "^0.6.0", "vue-i18n": "9.2.2", "vue-plugin-hiprint": "^0.0.60", "vue-router": "^4.2.0", "vue3-print-nb": "^0.1.4", "vuedraggable": "^2.24.3", "vxe-pc-ui": "4.6.14", "vxe-table": "4.9.5", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/eslint-parser": "^7.22.0", "@commitlint/cli": "^17.6.3", "@commitlint/config-conventional": "^17.6.3", "@esbuild/linux-x64": "^0.18.20", "@iconify-json/ep": "^1.1.10", "@swc/core": "^1.11.16", "@types/echarts": "^4.9.22", "@types/node": "^20.12.7", "@types/nprogress": "^0.2.0", "@types/path-browserify": "^1.0.0", "@types/rollup-plugin-visualizer": "^5.0.3", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^5.13.0", "@typescript-eslint/parser": "^5.0.0", "@vitejs/plugin-vue-jsx": "^3.0.0", "@vitest/coverage-v8": "^3.0.9", "autoprefixer": "^10.4.14", "commitizen": "^4.3.0", "cross-env": "^7.0.3", "cz-git": "^1.6.1", "eslint": "^8.57.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-compat": "^6.0.2", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-react": "^7.32.2", "eslint-plugin-vue": "^9.13.0", "fast-glob": "^3.2.11", "husky": "^8.0.3", "lint-staged": "^13.2.2", "postcss": "^8.4.23", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.6", "prettier": "3.1.0", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.69.5", "stylelint": "^15.5.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.0.0", "stylelint-config-recommended-scss": "^11.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^33.0.0", "stylelint-config-standard-scss": "^9.0.0", "terser": "^5.37.0", "typescript": "^4.9.5", "unocss": "^0.45.6", "unplugin-auto-import": "^0.15.3", "unplugin-icons": "^0.16.1", "unplugin-vue-components": "^0.24.1", "vite": "^4.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-enhance-log": "^0.5.2", "vite-plugin-lazy-import": "^1.0.7", "vite-plugin-legacy-swc": "^1.2.3", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-static-copy": "^2.3.0", "vite-plugin-svg-icons": "^2.0.1", "vitest": "^3.0.9", "vue-print-nb": "^1.7.5", "vue-tsc": "^1.8.27"}, "author": "文诚恒远技术部", "license": "MIT"}