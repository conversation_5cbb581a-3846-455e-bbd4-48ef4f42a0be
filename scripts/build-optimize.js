#!/usr/bin/env node

/**
 * 构建优化脚本
 * 用于在构建前清理缓存，优化构建速度
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 开始构建优化...');

// 清理缓存目录
const cacheDirs = [
  'node_modules/.vite',
  'node_modules/.cache',
  'dist',
];

cacheDirs.forEach(dir => {
  const fullPath = path.join(process.cwd(), dir);
  if (fs.existsSync(fullPath)) {
    console.log(`🧹 清理缓存目录: ${dir}`);
    fs.rmSync(fullPath, { recursive: true, force: true });
  }
});

// 检查 pnpm 是否可用
try {
  execSync('pnpm --version', { stdio: 'ignore' });
  console.log('📦 使用 pnpm 安装依赖...');
  execSync('pnpm install --frozen-lockfile', { stdio: 'inherit' });
} catch (error) {
  console.log('📦 使用 npm 安装依赖...');
  execSync('npm ci', { stdio: 'inherit' });
}

console.log('🏗️  开始构建...');
const startTime = Date.now();

try {
  execSync('npm run build:prod', { stdio: 'inherit' });
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  console.log(`✅ 构建完成！耗时: ${duration}s`);
} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}
