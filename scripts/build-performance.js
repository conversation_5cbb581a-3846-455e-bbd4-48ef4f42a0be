#!/usr/bin/env node

/**
 * 构建性能监控脚本
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function formatTime(ms) {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${remainingSeconds}s`;
}

function getDirectorySize(dirPath) {
  let totalSize = 0;

  function calculateSize(currentPath) {
    const stats = fs.statSync(currentPath);
    if (stats.isDirectory()) {
      const files = fs.readdirSync(currentPath);
      files.forEach(file => {
        calculateSize(path.join(currentPath, file));
      });
    } else {
      totalSize += stats.size;
    }
  }

  if (fs.existsSync(dirPath)) {
    calculateSize(dirPath);
  }

  return totalSize;
}

function formatSize(bytes) {
  const sizes = ['B', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 B';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

console.log('📊 开始构建性能测试...');

const startTime = Date.now();

try {
  // 清理旧的构建产物
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true });
  }

  // 执行构建
  execSync('npm run build:prod', { stdio: 'inherit' });

  const endTime = Date.now();
  const buildTime = endTime - startTime;

  // 计算构建产物大小
  const distSize = getDirectorySize('dist');

  // 统计文件数量
  let fileCount = 0;
  function countFiles(dir) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);
      if (stats.isDirectory()) {
        countFiles(filePath);
      } else {
        fileCount++;
      }
    });
  }
  countFiles('dist');

  // 输出性能报告
  console.log('\n📈 构建性能报告:');
  console.log('='.repeat(50));
  console.log(`⏱️  构建时间: ${formatTime(buildTime)}`);
  console.log(`📦 产物大小: ${formatSize(distSize)}`);
  console.log(`📄 文件数量: ${fileCount}`);
  console.log('='.repeat(50));

  // 保存性能数据
  const performanceData = {
    timestamp: new Date().toISOString(),
    buildTime: buildTime,
    distSize: distSize,
    fileCount: fileCount,
    buildTimeFormatted: formatTime(buildTime),
    distSizeFormatted: formatSize(distSize)
  };

  fs.writeFileSync('build-performance.json', JSON.stringify(performanceData, null, 2));
  console.log('💾 性能数据已保存到 build-performance.json');

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}
