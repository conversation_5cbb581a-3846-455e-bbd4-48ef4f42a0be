#!/usr/bin/env node

/**
 * 批量替换 VxeTableComponent 中的 el-tooltip 为 SmartTooltip
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, '../src/components/VxeTableComponent/index.vue');

// 读取文件内容
let content = fs.readFileSync(filePath, 'utf8');

// 替换所有的 el-tooltip 为 SmartTooltip
content = content.replace(/<el-tooltip/g, '<SmartTooltip');
content = content.replace(/<\/el-tooltip>/g, '</SmartTooltip>');

// 写回文件
fs.writeFileSync(filePath, content, 'utf8');

console.log('✅ 已成功将所有 el-tooltip 替换为 SmartTooltip');
console.log(`📁 文件路径: ${filePath}`);
