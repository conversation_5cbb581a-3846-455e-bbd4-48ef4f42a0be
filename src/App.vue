<!--
 * @Author: llm
 * @Date: 2023-07-28 09:44:37
 * @LastEditors: llm
 * @LastEditTime: 2025-03-05 19:49:00
 * @Description:
 *
-->
<script setup lang="ts">
  import { ElConfigProvider } from 'element-plus'
  import { useAppStore } from '@/store/modules/app'
  const appStore = useAppStore()
</script>

<template>
  <el-config-provider :locale="appStore.locale" :size="appStore.size">
    <router-view />
  </el-config-provider>
</template>

<style>
  .el-table .cell.el-tooltip {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* 隐藏地图图标等信息 */
  .anchorBL img {
    display: none;
  }

  .BMap_cpyCtrl {
    display: none;
  }

  /* 去掉高德logo */
  .amap-logo {
    display: none;
    opacity: 0 !important;
  }

  .amap-copyright {
    opacity: 0;
  }

  /* 百度地图搜索框搜索项层级 */
  .el-popup-parent--hidden {
    position: relative;
  }

  .tangram-suggestion-main {
    z-index: 9999;
    width: 220px !important;
    padding-top: 4px;
  }

  /* 左右布局收缩 */
  .tipTagContraction {
    position: absolute;
    top: calc(50% - 45px);
    right: -20px;
    z-index: 9;
    width: 20px;
    height: 90px;
    font-size: 20px;
    font-weight: 800;
    line-height: 90px;
    color: #fff;
    text-align: center;
    cursor: pointer;
    background: rgb(100 100 100 / 50%);
    background-size: 100% 100%;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
  }

  /* 左右布局收缩 */
  .tipTagContractionRight {
    position: absolute;
    top: calc(50% - 45px);
    right: -20px;
    z-index: 9;
    width: 20px;
    height: 90px;
    font-size: 20px;
    font-weight: 800;
    line-height: 90px;
    color: #fff;
    text-align: center;
    cursor: pointer;
    background: rgb(100 100 100 / 50%);
    background-size: 100% 100%;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
  }

  /* 上下布局收缩 */
  .topBottomContraction {
    position: absolute;
    top: -20px;
    left: calc(50% - 45px);
    z-index: 9;
    width: 90px;
    height: 20px;
    font-size: 20px;
    font-weight: 800;
    line-height: 20px;
    color: #fff;
    text-align: center;
    cursor: pointer;
    background: rgb(100 100 100 / 50%);
    background-size: 100% 100%;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }

  .tip-tag {
    position: absolute;
    width: 20px;
    height: 90px;
    background: rgba(100, 100, 100, 0.5);
    background-size: 100% 100%;
    right: -20px;
    top: calc(50% - 45px);
    color: #fff;
    font-weight: 500;
    line-height: 90px;
    text-align: center;
    font-size: 20px;
    z-index: 9;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    cursor: pointer;
    font-weight: 800;
  }
</style>
<!-- 去除打印的页眉页脚 -->
<style media="print">
  @page {
    size: A4;
    margin: 0;
  }

  html {
    background-color: #ffffff;
    height: auto;
    margin: 0;
    padding: 0;
  }

  body {
    margin: 0;
    padding: 0;
    border: none;
  }

  .el-select__wrapper.is-disabled .el-select__selected-item,
  .el-input.is-disabled .el-input__inner {
    color: #606266 !important;
    -webkit-text-fill-color: #606266 !important;
  }
  .el-descriptions__cell .el-form-item--default {
    margin-bottom: 0px !important;
  }
  .el-descriptions__cell .el-form-item--small {
    margin-bottom: 0px !important;
  }
</style>
