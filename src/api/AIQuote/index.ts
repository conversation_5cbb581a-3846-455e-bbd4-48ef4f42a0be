/*
 * @Author: llm
 * @Date: 2024-04-16 14:36:26
 * @LastEditors: llm
 * @LastEditTime: 2025-06-20 17:22:11
 * @Description:
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
export function axisListApi(data: any): AxiosPromise<any> {
  return request({
    url: '/logistics/route/vehicle/axisList ',
    method: 'get',
    data,
  })
}
export function brandListApi(data: any): AxiosPromise<any> {
  return request({
    url: '/logistics/route/vehicle/brandList',
    method: 'get',
    data,
  })
}
export function citySelectApi(data: any): AxiosPromise<any> {
  return request({
    url: '/logistics/route/city/select/option',
    method: 'get',
    data,
  })
}
// 城市下拉带经纬度
export function xianSelectApi(data: any): AxiosPromise<any> {
  return request({
    url: '/tms/routePlan/xian/select/option',
    method: 'get',
    data,
  })
}
export function AIOfferSingleApi(data: any): AxiosPromise<any> {
  return request({
    url: '/logistics/route/AIOffer/single',
    method: 'post',
    data,
  })
}
export function oilPriceApi(params: any): AxiosPromise<any> {
  return request({
    url: '/routeNet/v1/getOilPrice',
    method: 'get',
    params,
  })
}
/**
 * 里程校对
 */
export function mileageComparisonApi(data: any): AxiosPromise<any> {
  return request({
    url: '/tms/routePlan/mileage/comparison',
    method: 'post',
    data,
  })
}
