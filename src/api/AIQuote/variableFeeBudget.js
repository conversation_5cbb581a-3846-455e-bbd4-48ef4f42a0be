/*
 * <AUTHOR> llm
 * @Date         : 2022-06-02 14:59:48
 * @LastEditors  : llm
 * @LastEditTime : 2022-06-02 15:18:44
 * @FilePath     : /src/api/AIQuote/variableFeeBudget.js
 * @Description  : 变动费测算api
 */
import request from '@/utils/request'

/**
 * @description: 查询
 * @param {*} data
 * @return {*}
 */
export function getAIOffer(data) {
    return request({
        url: 'api/hdd/backend/backendAIOffer/getAIOffer',
        method: 'post',
        data
    })
}

export function getAISingle(data) {
    return request({
        url: 'api/hdd/backend/backendAIOffer/single/route',
        method: 'post',
        data
    })
}

// 获取车辆品牌
export function getVehicleBrands(data) {
    return request({
        url: 'api/hdd/backend/backendAIOffer/getVehicleBrands',
        method: 'get',
        data
    })
}

// 获取油价
export function getOilPrice(params) {
    return request({
        url: 'api/hdd/routeNet/v1/getOilPrice',
        method: 'get',
        params
    })
}

// 获取设置其他的车辆类型等
export function getClassifications(data) {
    return request({
        url: 'api/hdd/backend/backendAIOffer/vehicle/configs',
        method: 'get',
        data
    })
}

// 获取车辆类型下的长度及属性等
export function getClassificationsV2(params, typeValue) {
    return request({
        url: 'api/hdd/backend/backendAIOffer/vehicle/configs/v2?position=' + typeValue,
        method: 'get',
        params
    })
}

// 获取设置其他的车辆类型等
export function exportSingle(data) {
    return request({
        url: 'api/hdd/backend/backendAIOffer/single/export',
        method: 'get',
        responseType: 'blob',
        data
    })
}
