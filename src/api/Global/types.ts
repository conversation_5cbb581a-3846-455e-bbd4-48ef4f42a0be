/*
 * @Author: llm
 * @Date: 2025-02-21 10:49:03
 * @LastEditors: llm
 * @LastEditTime: 2025-03-27 15:55:40
 * @Description:
 */
export interface DynamicDetailVO {
  /**
   * 标题
   */
  title: string
  /**
   * 项目
   */
  items: Item[]
  /**
   * 按钮
   */
  btns: BtnRequestVO[]
}

export interface Item {
  /**
   * 类型
   */
  type: 'object' | 'list' | 'auditFlow'
  /**
   * 子项
   */
  children: Item[]
  /**
   * 标题
   */
  title: string
  /**
   * 排序
   */
  sortNo: number
  /**
   * 列宽
   */
  colSpan: number
  /**
   * 扩展
   */
  ext: ExtVO | null
  /**
   * 数据列
   */
  dataColumn: DataColumn[]
  /**
   * 对象
   */
  object: any
  /**
   * 列表
   */
  list: any[] | null
  /**
   * 组
   */
  groupKey: string
  /**
   * 组排序
   */
  groupSortNo: number
  /**
   * 组列宽
   */
  groupColSpan: number
}

export interface DataColumn {
  tableId: number
  columnId: number
  label: string
  name: string
  type: string
  sortNo: number
  form: FormVO | null
  attributes: Attributes | null
  jump: JumpVO | null
}

export interface Attributes {
  fixed: string
}

export interface FormVO {
  sortNo: number
  canEdit: boolean
  name: string
  width: string
  canShow: boolean
  rules: RuleVO[]
  label: string
  type: string
  colSpan: number
  labelWidth: string
}

export interface RuleVO {
  min: number
  max: number
  trigger: string
  message: string
  required: boolean
}
