/*
 * @Author: llm
 * @Date: 2024-12-06 15:02:53
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-14 18:09:33
 * @Description:
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
/**
 * 下拉选择审批人接口
 */
export function getUserSelectOptionApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/company/user/select/option',
    method: 'get',
    params,
  })
}
/**
 * 应用场景下拉接口
 */
export function getFeesTypeSelectOptionApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/system/common/fees/cost/type/select/option',
    method: 'get',
    params,
  })
}
/**
 * 新增审批流程
 */
export function postOutFleetFeesAuditSettingApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/config/outFleetFeesAuditSetting',
    method: 'post',
    data,
  })
}
/**
 * 编辑审批流程
 */
export function putOutFleetFeesAuditSettingApi(data: any, id: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/config/outFleetFeesAuditSetting/' + id,
    method: 'put',
    data,
  })
}
/**
 * 审批流详情
 */
export function getOutFleetFeesAuditSettingApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/config/outFleetFeesAuditSetting/' + data.id,
    method: 'get',
  })
}
/**
 * 审批流详情
 */
export function distributeSelectApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/common/option/distribute/select',
    method: 'get',
    params,
  })
}

/**
 * 获取系统配置
 */
export function getSystemSettingApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/config/system/setting',
    method: 'get',
    params,
  })
}

/**
 * 系统配置提交 - 单个
 */
export function setItemSystemSettingApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/config/system/alter',
    method: 'post',
    data,
  })
}

/**
 * 系统配置 - 罚款配置 - 新增配置
 */
export function addFineConfigApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/config/fine',
    method: 'post',
    data,
  })
}
/**
 * 系统配置 - 罚款配置 - 编辑配置
 */
export function editFineConfigApi(data: any, id: any): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/config/fine/selective/${id}`,
    method: 'put',
    data,
  })
}

/**
 * 系统配置 - 动态接口
 * @param data 数据
 * @returns
 */
export function publicDataApi(params: Object, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}`,
    method: 'get',
    params,
  })
}

/**
 * 车队下拉列表
 */
export function getBusinessSelectApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/common/option/business/select`,
    method: 'GET',
    params,
  })
}
