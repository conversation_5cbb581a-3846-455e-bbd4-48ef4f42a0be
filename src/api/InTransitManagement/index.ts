/*
 * @Author: llm
 * @Date: 2023-07-13 09:54:24
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-08-06 11:35:49
 * @Description: 计划管理api
 *
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
import { ShareFormVO, vehicleParams } from './types'

/**
 * 获取承运商列表
 * @param type 3公路 4 铁水 9分拨
 */
export function carrierListApi(params?: Object, type?: number): AxiosPromise<ResponseResult> {
  return request({
    url: type === 3 || type === 4 ? `/tms/company/carrier/${type}/select/option` : `/tms/company/carrier/select/option?simple=true`,
    method: 'get',
    params,
  })
}

/**
 * 获取客户列表
 * @param data
 */
export function customerListApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: `/tms/company/customer/select/option`,
    method: 'get',
    params,
  })
}
/**
 * 获取订单状态
 * @param data
 */
export function orderStatusApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/tms/intransit/location/orderstatus/select/option`,
    method: 'get',
    data,
  })
}
/**
 * 获取车辆状态
 * @param data
 */
export function vehicleStatusApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/tms/intransit/location/vehiclestatus/select/option`,
    method: 'get',
    data,
  })
}
/**
 * 获取搜索项查询车辆
 * @param data
 */
export function getVehicleListByQueryApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/tms/intransit/location/vehicle/select/option`,
    method: 'post',
    data,
  })
}

/**
 * 根据承运商获取车辆
 * @param {string} carrierId 承运商id
 */
export function carrierVehicleSelect(carrierId: string): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/company/carrier/vehicle/select/option?relationId=' + carrierId,
    method: 'get',
  })
}

/**
 * 根据客户获取客户vin
 * @param {string} customerId 客户id
 */
export function customerVinSelect(customerId: string): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/company/customer/vin/select/option?customerId=' + customerId,
    method: 'get',
  })
}

/**
 * 车辆定位
 * @param {string} data {"vehicles": [], "vin": ""}
 */
export function vehiclesLocation(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/intransit/location/vehicles',
    method: 'post',
    data,
  })
}

/**
 * vin定位
 * @param {string} data {"vehicles": [], "vin": ""}
 */
export function vinsLocation(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/intransit/location/vins',
    method: 'post',
    data,
  })
}

/**
 * 车辆轨迹
 * @param {string} data {"vehicles": [], "vin": ""}
 */
export function vehicleTrace(data: vehicleParams): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/intransit/trace/vehicle',
    method: 'post',
    data,
  })
}

/**
 * 车辆轨迹
 * @param {string} data {"vehicles": [], "vin": ""}
 */
export function vinTrace(data: vehicleParams): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/intransit/trace/vin',
    method: 'post',
    data,
  })
}
/**
 * 干线排程分享路线
 * @param {ShareFormVO} data
 */
export function mainLineShare(data: ShareFormVO): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/mainLine/schedule/route/share',
    method: 'post',
    data,
  })
}

/**
 * 天气预报
 * @param {string} data {}
 */
export function weatherForecast(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/intransit/severe/weather',
    method: 'get',
    data,
  })
}
