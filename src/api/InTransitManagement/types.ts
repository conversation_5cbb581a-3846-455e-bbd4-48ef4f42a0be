/**
 * 车辆轨迹
 */
export interface vehicleParams {
  endTime?: string
  speedLimit?: string
  startTime?: string
  stayLimit?: string
  vehicle?: string
  vin?: string
}

/**
 * 排程路线分享
 */
export interface ShareFormVO {
  shipmentNo: string
  tracks: string
  startInfo: PointProps
  endInfo: PointProps
  waypoints: PointProps[]
  vinLoadList: string[]
  shareTargetList: string[]
}

export interface PointProps {
  coordinate: string
  name: string
  address: string
  adcode: string
  hddCode: any
}
