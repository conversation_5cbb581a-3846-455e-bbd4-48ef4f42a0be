/*
 * @Author: llm
 * @Date: 2025-02-12 12:05:42
 * @LastEditors: llm
 * @LastEditTime: 2025-02-14 18:16:32
 * @Description:
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'

/**
 * 获取离职人员详情
 *
 * @param data
 * @returns
 */
export function postOutFleetAdministrationOutFleetStaffManagementSubmitRecognitionApi(data: any): AxiosPromise<any> {
  return request({
    url: '/logistics/api/outfleet/administration/outFleetStaffManagement/submitRecognition',
    method: 'post',
    data,
  })
}
/**
 * 更新离职人员
 *
 * @param data
 * @returns
 */
export function putOutFleetAdministrationStaffSalaryManagementApi(id: string, data: any): AxiosPromise<any> {
  return request({
    url: '/logistics/api/out/fleet/administration/staff/salary/management/' + id,
    method: 'put',
    data,
  })
}
