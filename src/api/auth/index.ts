/*
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2025-07-25 11:08:34
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
import { LoginData, LoginResult, MenuPermissionsResult } from './types'
import { CustomerQuery, CustomerVO, UserQuery } from '../customerCenter/customerBaseData/types'

/**
 * 登录API
 *
 * @param data {LoginData}
 * @returns
 */
export function loginApi(data: LoginData): AxiosPromise<LoginResult> {
  return request({
    url: '/logistics/company/user/password/login',
    method: 'post',
    data,
  })
}
/**
 * 获取验证码
 *
 * @param checkKey 时间戳
 * @returns
 */
export function getCaptchaApi(checkKey: string): AxiosPromise<any> {
  return request({
    url: '/logistics/system/common/randomImage/' + checkKey,
    method: 'get',
  })
}

/**
 * 账号首次登录修改密码
 *
 * @param data {password: string}
 * @returns
 */
export function changePasswordApi(data: any): AxiosPromise<any> {
  return request({
    url: 'logistics/company/user/changePassword',
    method: 'post',
    data
  })
}


/**
 * 获取左侧菜单
 */
export function getUserMenuList(): AxiosPromise<Array<any>> {
  return request({
    url: '/logistics/system/authorization/menu/tree/left',
    method: 'get',
  })
}
/**
 * 登录API
 *
 * @param menuId 当前菜单id
 * @returns quan'xian
 */
export function getUserMenuPermission(menuId: string): AxiosPromise<MenuPermissionsResult> {
  return request({
    url: '/logistics/system/authorization/permissions',
    method: 'get',
    params: { menuId },
  })
}

/**
 * 修改用户密码
 *
 * @returns
 */
export function editUserPassword(id: string, data: any) {
  return request({
    url: `/logistics/company/user/password/modify/${id}`,
    method: 'post',
    data,
  })
}
/**
 * 切换状态
 *
 * @param id
 * @param uri 请求地址前缀
 */
export function switchStatus(id: string, uri: string) {
  return request({
    url: `${uri}/switch/enable/${id}`,
    method: 'post',
  })
}
/**
 * 数据导出
 * @param searchParams
 * @param uri 请求地址前缀
 * @returns
 */
export function exportExcel(searchParams: Object, uri: string) {
  const params = JSON.parse(JSON.stringify(searchParams))
  delete params.page
  delete params.limit
  const formData = new FormData()
  Object.keys(params).forEach((key: string) => {
    formData.append(key, params[key])
  })
  return request({
    url: `${uri}/export`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    responseType: 'arraybuffer',
  })
}
/**
 * 公共数据导出
 * @param uri 请求地址前缀
 * @returns
 */
export function globalExportExcel(searchParams: Object, uri: string) {
  const params = JSON.parse(JSON.stringify(searchParams))
  delete params.page
  delete params.limit
  const formData = new FormData()
  Object.keys(params).forEach((key: string) => {
    formData.append(key, params[key])
  })
  return request({
    url: `${uri}`,
    method: 'post',
    data: formData,
    responseType: 'arraybuffer',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
/**
 * 下载模版
 * @param uri 请求地址前缀(全局的)
 * @returns
 */
export function globalDownloadTemplate(uri: string) {
  return request({
    url: `${uri}/template`,
    method: 'get',
    responseType: 'arraybuffer',
  })
}
/**
 * 下载模版
 * @param uri 请求地址前缀(uri的)
 * @returns
 */
export function downloadTemplate(uri: string, params?: any) {
  return request({
    url: `${uri}`,
    method: 'get',
    responseType: 'arraybuffer',
    params,
  })
}
/**
 * 下载模版
 * @param uri 请求地址前缀(uri的)
 * @returns
 */
export function downloadTemplateUrl(params: any, method = 'get', uri: string) {
  return request({
    url: `${uri}`,
    method,
    params,
    responseType: 'arraybuffer',
  })
}
/**
 * 导入Excel(按钮上没有uri)
 * @param file 文件
 * @param uri 请求地址前缀
 * @returns
 */
export function importFileGlobalFun(file: any, uri: string, data: any = {}) {
  const formData = new FormData()
  formData.append('file', file)
  //遍历data  append 到formData
  Object.keys(data).forEach((key: string) => {
    formData.append(key, data[key])
  })
  return request({
    url: `${uri}/import`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

export function importFileGlobalFun1(file: any, uri: string, data: any = {}) {
  const formData = new FormData()
  formData.append('file', file)
  //遍历data  append 到formData
  Object.keys(data).forEach((key: string) => {
    formData.append(key, data[key])
  })
  return request({
    url: `${uri}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
/**
 * 导入Excel(按钮上有uri)
 * @param file 文件
 * @param uri 请求地址前缀
 * @returns
 */
export function importFileGlobalBtnUriFun(file: any, uri: string, data: any = {}) {
  const formData = new FormData()
  formData.append('file', file)
  //遍历data  append 到formData
  Object.keys(data).forEach((key: string) => {
    formData.append(key, data[key])
  })
  return request({
    url: `${uri}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
/**
 * 获取列表全部数据
 *
 * @param queryParams
 * @param uri 请求地址前缀
 */
export function getListAll(params: any, uri: string): AxiosPromise<any[]> {
  return request({
    url: `${uri}/all`,
    method: 'get',
    params,
  })
}
/**
 * 分页获取列表数据
 *
 * @param params
 * @param uri 请求地址前缀
 * @param method
 */
export function getListPage(params: PageQuery, uri: string): AxiosPromise<PageResult> {
  return request({
    url: `${uri}/page`,
    method: 'get',
    params,
  })
}

export function getListPagePost(data: PageQuery, uri: string): AxiosPromise<PageResult> {
  return request({
    url: `${uri}/page`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data,
  })
}
/**
 * 重新计算
 * @param params
 * @param uri 请求地址前缀
 */
export function getRecompute(params: PageQuery, uri: string): AxiosPromise<any> {
  return request({
    url: `${uri}/recompute`,
    method: 'get',
    params,
  })
}
/**
 * 获取列表tree数据
 *
 * @param queryParams
 * @param uri 请求地址前缀
 */
export function getListTree(params: PageQuery, uri: string) {
  return request({
    url: `${uri}/tree`,
    method: 'get',
    params,
  })
}
/**
 * 查详情
 *
 * @param id
 * @param uri 请求地址前缀
 */
export function getDetailById(id: string, uri: string) {
  return request({
    url: `${uri}/${id}`,
    method: 'get',
  })
}
/**
 * 公共接口(地址栏上)
 *
 * @param id
 * @param uri 请求地址前缀
 */
export function globalUrlApi(params: any, uri: string) {
  return request({
    url: `${uri}`,
    method: 'get',
    params,
  })
}
/**
 * 获取tab下的列表
 *
 * @param data
 * @param uri 请求地址
 */
export function bottomTableList(data: any, uri: string) {
  return request({
    url: `${uri}/page`,
    method: 'get',
    params: data,
  })
}
/**
 * 获取tab下singlePage数据
 *
 * @param data
 * @param uri 请求地址
 */
export function bottomSinglePage(data: any, uri: string) {
  return request({
    url: `${uri}`,
    method: 'get',
    params: data,
  })
}
/**
 * 获取tab下动态表头数据
 *
 * @param data
 * @param uri 请求地址
 */
export function bottomDynamicColumns(data: any, uri: string) {
  return request({
    url: `${uri}/all`,
    method: 'get',
    params: data,
  })
}
/**
 * 新增数据
 *
 */
export function addItemApi(data: CustomerVO, uri: string) {
  return request({
    url: uri,
    method: 'post',
    data,
  })
}
/**
 * 更新数据
 * @param id
 * @param data 请求数据
 * @param uri
 * @returns
 */
export function updateItemApi(id: string, data: CustomerVO, uri: string) {
  return request({
    url: `${uri}/${id}`,
    method: 'put',
    data,
  })
}
/**
 * 单条删除 *
 */
export function deleteItemApi(id: string, uri: string) {
  return request({
    url: `${uri}/${id}`,
    method: 'delete',
  })
}

/**
 * 批量删除
 * @param ids 删除的id
 * @param uri 请求地址前缀
 */
export function batchDeleteApi(ids: string, uri: string) {
  return request({
    url: `${uri}/batch?ids=${ids}`,
    method: 'delete',
  })
}

//批量删除，大批量删除数据
export function batchDeleteBigApi(ids: string, uri: string) {
  return request({
    url: `${uri}/batchDelete`,
    method: 'post',
    data: {
      ids,
    },
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 批量已读
 * @param ids 删除的id
 * @param uri 请求地址前缀
 */
export function batchReadApi(data: any, uri: string) {
  return request({
    url: `${uri}/read`,
    method: 'post',
    data,
  })
}
/**
 * 获取form表单中的下拉
 * @param uri 请求地址
 * @param params 请求参数
 */
export function getFormItemSelectOptionsApi(uri?: string, params?: Object) {
  return request({
    url: `${uri}`,
    method: 'get',
    params,
  })
}
/**
 * 图片上传
 * @param uri 请求地址
 * @param params 请求参数
 */
export function uploadApi(uri?: string, data?: Object) {
  return request({
    url: `${uri}`,
    method: 'post',
    data,
  })
}
/**
 * 文件上传
 * @param uri 请求地址
 * @param params 请求参数
 */
export function uploadFileApi(uri: string, formData: any) {
  // const formData = new FormData();
  // formData.append("file", file);
  return request({
    url: `${uri}`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
/**
 * 打印table
 */
export function printTableApi(uri: string, params: any): any {
  delete params.page
  delete params.limit
  return request({
    url: `${uri}`,
    method: 'get',
    params,
  })
}
/**
 * 获取自定义表头
 */
export function getCustomTableHeaderApi(uri: string): any {
  return request({
    url: `${uri}/define/page/header`,
    method: 'get',
  })
}
/**
 * 获取自定义排序
 */
export function getCustomTableSearchApi(uri: string): any {
  return request({
    url: `${uri}/define/page/search`,
    method: 'get',
  })
}

/**
 * 更新自定义表头
 */
export function postCustomTableHeaderApi(uri: string, data: any): any {
  return request({
    url: `${uri}/define/page/header`,
    method: 'post',
    data,
  })
}
/**
 * 更新自定义排序
 */
export function postCustomTableSearchApi(uri: string, data: any): any {
  return request({
    url: `${uri}/define/page/search`,
    method: 'post',
    data,
  })
}
/**
 * 客户阶梯价格
 */
export function postLadderPriceApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/business/customer/contract/detail/view/ladder/edit',
    method: 'post',
    data,
  })
}
/**
 * 客户阶梯价格参数回显
 */
export function getLadderPriceEchoApi(id: any): any {
  return request({
    url: `logistics/api/out/fleet/business/customer/contract/detail/view/getFormula/${id}`,
    method: 'get',
  })
}

/**
 * 外协阶梯价格
 */
export function postOutsourcePriceApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/carrier/contract/detail/view/ladder/edit',
    method: 'post',
    data,
  })
}
/**
 * 外协阶梯价格参数回显
 */
export function getOutsourcePriceEchoApi(id: any): any {
  return request({
    url: `logistics/api/out/fleet/carrier/contract/detail/view/getFormula/${id}`,
    method: 'get',
  })
}

/**
 * 编辑对账金额
 */
export function editReconCiliApi(params: any, method: string = 'post', url: string) {
  return request({
    url,
    method,
    params,
  })
}

/**
 * 获取客户补贴/扣款
 */
export function getDeduction(params: any): any {
  return request({
    url: '/logistics/api/out/fleet/settlement/customer/deduction/all',
    method: 'post',
    params,
  })
}

/**
 * 客户下拉接口
 */
export function dropDownApi(data: any): any {
  return request({
    url: '/logistics/api/out/fleet/business/customer/select/option',
    method: 'get',
    data,
  })
}
/**
 * 承运商下拉接口
 */
export function carrierApi(data: any): any {
  return request({
    url: '/logistics/api/out/fleet/carrier/select/option',
    method: 'get',
    data,
  })
}
/**
 * 批量对账->搜索接口
 */
export function settlementApi(params: any): any {
  return request({
    url: '/logistics/api/outfleet/order/scattered/settlement/detail/view/all',
    method: 'post',
    params,
  })
}

/**
 * 批量对账->确定接口
 */
export function generateSettlementApi(data: any): any {
  return request({
    url: '/logistics/api/outfleet/order/scattered/settlement/generateSettlement',
    method: 'post',
    data,
  })
}

/**
 * 付款-批量对账->搜索接口
 */
export function settlementSearchApi(params: any): any {
  return request({
    url: '/logistics/api/outfleet/order/scattered/payment/detail/view/all?settlementStatus=未对账',
    method: 'post',
    params,
  })
}
/**
 * 付款-批量对账->历史挂账
 */
export function historicalOutstandingApi(params: any): any {
  return request({
    url: '/logistics/api/outfleet/order/scattered/payment/getCharge',
    method: 'get',
    params,
  })
}
/**
 * 付款-批量对账->确定接口
 */
export function fkgenerateSettlementApi(data: any): any {
  return request({
    url: '/logistics/api/outfleet/order/scattered/payment/generateSettlement',
    method: 'post',
    data,
  })
}
/**
 * 付款-批量对账->数据获取接口
 */
export function historyGenerateSettlementApi(params: any): any {
  return request({
    url: '/logistics/api/outfleet/order/scattered/payment/detail/view/queryAll',
    method: 'get',
    params,
  })
}
/**
 * 阶梯价格, 获取阶梯价格计算规则下拉列表
 */
export function getLadderPriceRuleOptionApi(params: any): any {
  return request({
    url: '/logistics/api/out/fleet/business/customer/contract/detail/view/ladder/price/rule/option',
    method: 'get',
    params,
  })
}
