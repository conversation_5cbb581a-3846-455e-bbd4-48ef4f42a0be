/*
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2025-03-28 18:03:59
 * @Description:
 *
 */

/**
 * 系统选择
 */
export interface SystemsVO {
  systemCode?: string
  systemIcon?: string
  systemName?: string
  systemPath?: string
  systemUri?: string
  systemType?: string
}

/**
 * 登录请求参数
 */
export interface LoginData {
  /**
   * 用户名
   */
  username?: string
  /**
   * 密码
   */
  password?: string

  /**
   * 手机号
   */
  mobile?: null

  /**
   * 验证码
   */
  captcha?: string

  /** 验证码缓存key */
  checkKey: string
}

/**
 * 登录响应
 */
export interface LoginResult {
  /**
   * 访问token
   */
  token?: string
  /**
   * 过期时间(单位：毫秒)
   */
  menus?: Array<any>
  /**
   * 账号
   */
  userName?: string
  /**
   * 公司名称
   */
  companyName?: string
  /**
   * 真实姓名
   */
  realName?: string
  /**
   * 多系统选择
   */
  systems: Array<SystemsVO>
}
/**
 * 菜单数据列和按钮权限响应
 */
export interface MenuPermissionsResult {
  /**
   * 按钮权限id
   */
  menuId: string
  parentId: string
  name: string | null
  path: string | null
  component: string | null
  redirect: string | null
  permissions: string | null
  meta: Meta
  sort: string
  children: MenuPermissionsResult[]
}
export interface Meta {
  menuId: string
  parentId: string
  type: number
  title: string
  icon: string
  hidden: boolean
  dataColumn: DataColumn[]
}
export interface DataColumn {
  menuId: string
  columnId: string
  label: string
  name: string
  type: string
  operation: string
  routerLink: string | null
  align: string
  width: string
  widthMin: string
  sortNo: number
}
