/*
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2023-07-04 14:33:46
 * @Description: 部门api
 *
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
import { DeptForm, DeptQuery, DeptVO } from './types'

/**
 * 部门树形表格
 *
 * @param queryParams
 */
export function listDepts(queryParams?: DeptQuery): AxiosPromise<DeptVO[]> {
  return request({
    url: '/tms/company/dept/tree',
    method: 'get',
    params: queryParams,
  })
}

/**
 * 更改部门状态
 *
 * @param data
 */
export function updateDeptStatus(id: string) {
  return request({
    url: `/tms/company/dept/switch/enable/${id}`,
    method: 'post',
  })
}
/**
 * 新增部门
 *
 * @param data
 */
export function addDept(data: DeptForm) {
  return request({
    url: '/tms/company/dept',
    method: 'post',
    data,
  })
}

/**
 *  修改部门
 *
 * @param id
 * @param data
 */
export function updateDept(id: string, data: DeptForm) {
  return request({
    url: `/tms/company/dept/${id}`,
    method: 'put',
    data,
  })
}

/**
 * 删除部门
 *
 * @param ids
 */
export function deleteDept(ids: string) {
  return request({
    url: `/tms/company/dept/batch?ids=${ids}`,
    method: 'delete',
  })
}
