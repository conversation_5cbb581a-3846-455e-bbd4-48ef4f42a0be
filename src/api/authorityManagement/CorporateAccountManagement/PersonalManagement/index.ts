/*
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2024-10-29 15:53:34
 * @Description: 用户管理api
 *
 */
import request from '@/utils/request'
import { CheckedMenuColumnVO, UserForm, UserPageVO, UserQuery } from './types'

/**
 * 获取用户菜单数据
 *
 * @param assignUserId 当前用户id
 * @param uri 请求地址前缀
 */
export function userMenulist(assignUserId?: string, uri?: string) {
  return request({
    url: `/logistics/company/role/menu/tree/columns?roleId=` + assignUserId,
    method: 'get',
  })
}

/**
 * 获取当前登录用户下的当前菜单下的数据列
 *
 * @param menuId 菜单id
 * @param uri 请求地址前缀
 */
export function currentLoginUserMenuColumnlist(menuId?: string, uri?: string) {
  return request({
    url: `${uri}?menuId=${menuId}`,
    method: 'get',
  })
}
/**
 * 获取用户详情
 *
 * @param id 用户id
 * @param uri 请求地址前缀
 */
export function userDetail(id: string, uri: string) {
  return request({
    url: `${uri}/${id}`,
    method: 'get',
  })
}

/**
 * 修改用户密码
 *
 * @param id
 * @param password
 * @param uri 请求地址前缀
 */
export function updateUserPassword(id: string, password: string, uri: string) {
  return request({
    url: `${uri}/password/modify/${id}`,
    method: 'post',
    data: { password },
  })
}

/**
 * 为当前用户下的菜单分配数据列
 *
 * @param menuColumn
 * @param uri 请求地址前缀
 */
export function distributeMenuColumn(menuColumn: CheckedMenuColumnVO, uri: string) {
  return request({
    url: '/logistics/company/role/assign/columns',
    method: 'post',
    data: menuColumn,
  })
}
