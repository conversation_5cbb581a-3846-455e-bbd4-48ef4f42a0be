/*
 * @Author: llm
 * @Date: 2023-06-30 14:45:55
 * @LastEditors: llm
 * @LastEditTime: 2023-07-04 14:33:57
 * @Description:
 *
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
import { PostQuery, PostPageResult, PostForm, PostPageVO } from './types'

/**
 * 获取岗位分页数据
 *
 * @param queryParams
 */
export function getPostPage(queryParams?: PostQuery): AxiosPromise<PostPageResult> {
  return request({
    url: 'tms/company/post/page',
    method: 'get',
    params: queryParams,
  })
}
/**
 * 获取岗位下拉数据
 *
 * @param queryParams
 */
export function listPostOptions(queryParams?: PostQuery): AxiosPromise<PostPageVO[]> {
  return request({
    url: '/tms/company/post/all',
    method: 'get',
    params: queryParams,
  })
}
/**
 * 更新状态
 *
 * @param data
 */
export function updatePostStatus(id: string) {
  return request({
    url: `/tms/company/post/switch/enable/${id}`,
    method: 'post',
  })
}
/**
 * 添加岗位
 *
 * @param data
 */
export function addPost(data: PostForm) {
  return request({
    url: '/tms/company/post',
    method: 'post',
    data,
  })
}

/**
 * 更新岗位
 *
 * @param id
 * @param data
 */
export function updatePost(id: string, data: PostForm) {
  return request({
    url: `/tms/company/post/${id}`,
    method: 'put',
    data,
  })
}
/**
 * 删除岗位
 * @param id
 */
export function deletePost(id: string) {
  return request({
    url: `/tms/company/post/${id}`,
    method: 'delete',
  })
}
/**
 * 批量删除岗位，多个以英文逗号(,)分割
 *
 * @param ids
 */
export function deletePosts(ids?: string) {
  return request({
    url: `/tms/company/post/batch?ids=${ids}`,
    method: 'delete',
  })
}
