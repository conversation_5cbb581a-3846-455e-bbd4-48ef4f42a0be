/**
 * 岗位查询参数
 */
export interface PostQuery extends PageQuery {
  keywords?: string
}

/**
 * 岗位分页对象
 */
export interface PostPageVO {
  /**
   * id
   */
  id: string
  /**
   * 岗位名称
   */
  postName?: string
  /**
   * 排序
   */
  postSort?: number
  /**
   * 岗位状态(true-启用；false-停用)
   */
  enable?: boolean
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 修改时间
   */
  modifyTime?: Date
}

/**
 * 岗位分页
 */
export type PostPageResult = PageResult

/**
 * 岗位表单对象
 */
export interface PostForm {
  /**
   * 岗位ID
   */
  id: string
  /**
   * 岗位名称
   */
  postName: string
  /**
   * 排序
   */
  postSort?: number
  /**
   * 岗位状态(true-启用；false-停用)
   */
  enable?: boolean
}
export interface CheckedPost {
  /**
   * 岗位id
   */
  id?: string
  /**
   * 岗位名称
   */
  postName?: string
}

/**
 * 操作按钮组
 */
export interface Operation {
  /**
   * 列表项名称
   */
  label?: string
  /**
   * 列表项
   */
  items?: Array<OperationItem>
  /**
   * 列表项宽度
   */
  width?: string
}
/**
 * 操作项
 */
export interface OperationItem {
  /**
   * 操作按钮的类型 (add-添加;delete-删除;edit-编辑;view-查看;auth-分配权限)
   */
  type?: string
  /**
   * 操作按钮名称
   */
  title?: string
}

/**
 * 查询条件
 */
export interface PostQuery extends PageQuery {}
