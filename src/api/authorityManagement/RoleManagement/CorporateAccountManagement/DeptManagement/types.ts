/*
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2023-07-04 14:38:00
 * @Description:
 *
 */
/**
 * 部门查询参数
 */
export interface DeptQuery {
  deptName?: string
  status?: boolean
  enable?: boolean
}

/**
 * 部门类型
 */
export interface DeptVO {
  /**
   * 子部门
   */
  children?: DeptVO[]
  /**
   * 部门ID
   */
  deptId?: string
  /**
   * 部门名称
   */
  deptName?: string
  /**
   * 父部门ID
   */
  parentId?: string
  /**
   * 排序
   */
  deptSort?: number
  /**
   * 状态(true:启用；false:禁用)
   */
  enable?: boolean
}

/**
 * 部门表单类型
 */
export interface DeptForm {
  /**
   * 部门ID(新增不填)
   */
  deptId: string
  /**
   * 部门名称
   */
  deptName?: string
  /**
   * 父部门ID
   */
  parentId?: string
  /**
   * 排序
   */
  deptSort?: number
  /**
   * 状态(true:启用；false:禁用)
   */
  enable?: boolean
}
