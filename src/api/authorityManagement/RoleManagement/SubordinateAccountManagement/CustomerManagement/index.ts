/*
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2023-12-25 14:24:06
 * @Description: 客户管理api
 *
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
import { CheckedMenuColumnVO, UserForm, UserPageVO, UserQuery } from './types'

/**
 * 获取用户分页列表
 *
 * @param queryParams
 */
export function getUserPage(queryParams: UserQuery): AxiosPromise<PageResult> {
  return request({
    url: '/tms/customer/user/page',
    method: 'get',
    params: queryParams,
  })
}

/**
 * 获取用户菜单数据
 *
 * @param assignUserId 当前用户id
 */
export function userMenulist(assignUserId?: string) {
  return request({
    url: `/tms/user/menu/assignUser/columnTree?assignUserId=${assignUserId}`,
    method: 'get',
  })
}

/**
 * 获取用户当前菜单下的数据列
 *
 * @param menuId 菜单id
 */
export function userMenuColumnlist(menuId?: string) {
  return request({
    url: `/tms/user/menu/columns?menuId=${menuId}`,
    method: 'get',
  })
}

/**
 * 添加用户
 *
 * @param data
 */
export function addUser(data: UserForm) {
  return request({
    url: '/tms/customer/user',
    method: 'post',
    data,
  })
}
/**
 * 获取用户详情
 *
 * @param id 用户id
 */
export function userDetail(id: string) {
  return request({
    url: `/tms/customer/user/${id}`,
    method: 'get',
  })
}

/**
 * 修改用户
 *
 * @param id
 * @param data
 */
export function updateUser(id: string, data: UserForm) {
  return request({
    url: `/tms/customer/user/${id}`,
    method: 'put',
    data,
  })
}

/**
 * 修改用户状态
 *
 * @param id
 */
export function updateUserStatus(id: string) {
  return request({
    url: `/tms/customer/user/switch/enable/${id}`,
    method: 'post',
  })
}

/**
 * 修改用户密码
 *
 * @param id
 * @param password
 */
export function updateUserPassword(id: string, password: string) {
  return request({
    url: `/tms/user/password/modify/${id}`,
    method: 'post',
    data: { password },
  })
}

/**
 * 删除用户
 *
 * @param ids
 */
export function deleteUsers(ids: string) {
  return request({
    url: `/tms/customer/user/batch?ids=${ids}`,
    method: 'delete',
  })
}

/**
 * 为当前用户下的菜单分配数据列
 *
 * @param menuColumn
 */
export function distributeMenuColumn(menuColumn: CheckedMenuColumnVO) {
  return request({
    url: '/tms/user/menu/column/assign',
    method: 'post',
    data: menuColumn,
  })
}
