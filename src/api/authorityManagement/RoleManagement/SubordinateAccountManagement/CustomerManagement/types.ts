/*
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2023-07-04 14:34:02
 * @Description:
 *
 */
/**
 * 登录用户信息
 */
export interface UserInfo {
  nickname: string
  avatar: string
  roles: string[]
  perms: string[]
}

/**
 * 用户查询对象类型
 */
export interface UserQuery extends PageQuery {
  /**
   * 状态(true-启用 false-禁用)
   */
  enable?: boolean
  /**
   * 部门id
   */
  deptId?: string
}

/**
 * 用户分页对象
 */
export interface UserPageVO {
  /**
   * 用户头像
   */
  avatar?: string
  /**
   * 角色ID
   */
  roleId?: string
  /**
   * 部门ID
   */
  deptId?: string
  /**
   * 岗位ID
   */
  postId?: string
  /**
   * 邮箱
   */
  email?: string
  /**
   * 性别
   */
  gender?: number
  /**
   * 用户ID
   */
  id: string
  /**
   * 手机号
   */
  mobile?: string
  /**
   * 昵称
   */
  nickname?: string
  /**
   * 姓名
   */
  realName?: string
  /**
   * 角色ID集合
   */
  roleIds?: string[]
  /**
   * 用户状态(true:启用;false:禁用)
   */
  enable?: boolean
  /**
   * 用户名
   */
  name?: string
  /**
   * 描述
   */
  description?: string
  /**
   * 密码
   */
  password?: string
}

/**
 * 用户表单类型
 */
export interface UserForm {
  /**
   * 用户头像
   */
  avatar?: string
  /**
   * 角色ID
   */
  roles?: Array<any>
  /**
   * 部门ID
   */
  deptId?: string
  /**
   * 岗位ID
   */
  postId?: string
  /**
   * 邮箱
   */
  email?: string
  /**
   * 性别
   */
  gender?: number
  /**
   * 用户ID
   */
  id: string
  /**
   * 手机号
   */
  mobile?: string
  /**
   * 昵称
   */
  nickname?: string
  /**
   * 姓名
   */
  realName?: string
  /**
   * 角色ID集合
   */
  roleIds?: string[]
  /**
   * 用户状态(true:启用;false:禁用)
   */
  enable?: boolean
  /**
   * 用户名
   */
  name?: string
  /**
   * 描述
   */
  description?: string
  /**
   * 密码
   */
  password?: string
}
/**
 * 菜单下的数据列
 */
export interface MenuColumnVO {
  checked: boolean
  columnName: string
  columnValue: string
  menuId: string
  sortNo: number
}
/**
 * 菜单分配的数据列
 */
export interface CheckedMenuColumnVO {
  /**
   * 用户id
   */
  assignUserId: string
  /**
   * 选中的数据列list
   */
  menuColumn: CheckedMenuColumnItemVO[]
}
/**
 * 选中的数据列list
 */
export interface CheckedMenuColumnItemVO {
  /**
   * 数据列名称
   */
  columnDisplayName: string
  /**
   * 数据列value
   */
  columnValueName: string
  /**
   * 排序
   */
  sortNo: number
}
