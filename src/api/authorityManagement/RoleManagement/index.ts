import { AxiosPromise } from 'axios'
import request from '@/utils/request'
import { RoleQuery, RolePageResult, RoleForm, setMenus, RolePageVO } from './types'

/**
 * 获取角色分页数据
 *
 * @param queryParams
 */
export function getRolePage(queryParams?: RoleQuery): AxiosPromise<RolePageResult> {
  return request({
    url: '/tms/company/role/page',
    method: 'get',
    params: queryParams,
  })
}

/**
 * 获取角色下拉数据
 *
 * @param queryParams
 */
export function listRoleOptions(queryParams?: RoleQuery): AxiosPromise<RolePageVO[]> {
  return request({
    url: '/tms/company/role/all',
    method: 'get',
    params: queryParams,
  })
}
/**
 * 修改角色状态
 *
 * @param id
 */
export function updateRoleStatus(id: string) {
  return request({
    url: `/tms/company/role/switch/enable/${id}`,
    method: 'post',
  })
}

/**
 * 分配菜单权限给角色
 *
 * @param queryParams
 */
export function updateRoleMenus(roleId: string, data: number[]): AxiosPromise<any> {
  return request({
    url: `/api/v1/roles/${roleId}/menus`,
    method: 'put',
    data,
  })
}

/**
 * 获取角色详情
 *
 * @param id
 */
export function getRoleForm(id: number): AxiosPromise<RoleForm> {
  return request({
    url: `/api/v1/roles/${id}/form`,
    method: 'get',
  })
}

/**
 * 添加角色
 *
 * @param data
 */
export function addRole(data: RoleForm) {
  return request({
    url: '/tms/company/role',
    method: 'post',
    data,
  })
}

/**
 * 更新角色
 *
 * @param id
 * @param data
 */
export function updateRole(id: string, data: RoleForm) {
  return request({
    url: `/tms/company/role/${id}`,
    method: 'put',
    data,
  })
}
/**
 * 删除角色
 * @param id
 */
export function deleteRole(id: string) {
  return request({
    url: `/tms/company/role/${id}`,
    method: 'delete',
  })
}
/**
 * 批量删除角色，多个以英文逗号(,)分割
 *
 * @param ids
 */
export function deleteRoles(ids?: string) {
  return request({
    url: `/tms/company/role/batch?ids=${ids}`,
    method: 'delete',
  })
}
/**
 * 获取菜单下拉树形列表(包含按钮)
 */
export function listMenuOptions(): AxiosPromise<OptionType[]> {
  return request({
    url: '/logistics/system/authorization/menu/tree/owned',
    method: 'get',
  })
}
/**
 * 分配角色权限
 *
 * @param data
 */
export function setMenus(data: setMenus) {
  return request({
    url: '/logistics/company/role/assign/menus',
    method: 'post',
    data,
  })
}
