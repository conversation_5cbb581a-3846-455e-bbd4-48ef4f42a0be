/**
 * 角色查询参数
 */
export interface RoleQuery extends PageQuery {
  keywords?: string
}

/**
 * 角色分页对象
 */
export interface RolePageVO {
  /**
   * id
   */
  id: string
  /**
   * roleId
   */
  roleId: string
  /**
   * 角色名称
   */
  roleName?: string
  /**
   * 排序
   */
  roleSort?: number
  /**
   * 角色状态(true:启用;false:禁用)
   */
  enable?: boolean
  /**
   * 创建时间
   */
  createTime?: Date
  /**
   * 修改时间
   */
  modifyTime?: Date
}

/**
 * 角色分页
 */
export type RolePageResult = PageResult

/**
 * 角色表单对象
 */
export interface RoleForm {
  /**
   * 状态名称
   */
  statusName: string
  /**
   * 角色ID
   */
  id?: string
  /**
   * 角色名称
   */
  roleName: string
  /**
   * 排序
   */
  roleSort?: number
  /**
   * 角色状态(true-启用；0-停用)
   */
  enable?: boolean
}
export interface CheckedRole {
  /**
   * 角色id
   */
  id?: string
  /**
   * 角色名称
   */
  name?: string
  roleName?: string
  /**
   * 菜单权限ids
   */
  menuIds?: string[]
}

/**
 * 操作按钮组
 */
export interface Operation {
  /**
   * 列表项名称
   */
  label?: string
  /**
   * 列表项
   */
  items?: Array<OperationItem>
  /**
   * 列表项宽度
   */
  width?: string
}
/**
 * 操作项
 */
export interface OperationItem {
  /**
   * 操作按钮的类型 (add-添加;delete-删除;edit-编辑;view-查看;auth-分配权限)
   */
  type?: string
  /**
   * 操作按钮名称
   */
  title?: string
}

/**
 * 查询条件
 */
export interface RoleQuery extends PageQuery {}
export interface setMenus {
  /**
   * 角色id
   */
  roleId?: string
  /**
   * 选中的菜单id
   */
  menuIds: string[]
}

export interface CustomTableHeaderVO {
  columnName: string
  displayName: string
  no: number
}
