/*
 * <AUTHOR> llm
 * @Date         : 2022-04-25 14:55:10
 * @LastEditors: llm
 * @LastEditTime: 2024-12-09 10:25:55
 * @FilePath: /haodaoda-tms/src/api/basicData/organizationManage/personnelList.js
 * @Description  :组织架构api
 */

import request from '@/utils/request'

/**
 * @description: 获取权限菜单
 * @param {*} data
 * @return {*}
 */
export function getAuthMenu(data) {
  return request({
    url: 'api/hdd/business/v1/menu/mine',
    method: 'post',
    data,
  })
}

/**
 * @description: 保存人员
 * @param {*} data
 * @param {*} type add or edit
 * @return {*}
 */

export function savePersonInfo(data, type, staffType, id) {
  let url = ''
  let method = null
  if (staffType == 'admin') {
    url = type == 'add' ? 'api/hdd/business/v1/' + staffType + '/add' : 'business/v1/' + staffType + '/modify'
    method = 'post'
  } else {
    url = type == 'add' ? 'api/hdd/backend/' + staffType : 'backend/' + staffType + '/' + id
    method = type == 'add' ? 'post' : 'put'
  }
  return request({
    url,
    method,
    data,
  })
}

/**
 * @description: 删除人员
 * @param {*} data
 * @return {*}
 */
export function deletePerson(data, staffType) {
  let url = ''
  let method = null
  if (staffType == 'admin') {
    url = 'api/hdd/business/v1/' + staffType + '/delete'
    method = 'post'
  } else {
    url = 'api/hdd/backend/' + staffType + '/' + data.id
    method = 'delete'
  }
  return request({
    url,
    method,
    data,
  })
}

/**
 * @description: 批量删除
 * @param {*} ids
 */
export function batchDelete(ids, staffType) {
  return request({
    url: 'api/hdd/backend/' + staffType + '/batch?' + ids,
    method: 'delete',
    // data,
  })
}

/**
 * @description: 获取人员列表
 * @param {*} data
 * @param {*} staffType 人员类型: 管理员 车队长 业务员 其他
 * @return {*}
 */
export function getPersonListForAdmin(data, staffType) {
  let url = 'api/hdd/business/v1/' + staffType + '/list'
  return request({
    url,
    method: 'post',
    data,
  })
}

export function getPersonListForOther(params, staffType) {
  let url = 'api/hdd/backend/' + staffType + '/page'
  return request({
    url,
    method: 'get',
    params,
  })
}

/**
 * @description: 导出
 */
export function exportExcel(data, staffType) {
  return request({
    url: 'api/hdd/backend/' + staffType + '/export',
    method: 'get',
    data,
  })
}

/**
 * @description: 导入
 */
export function importExcel(data, staffType) {
  return request({
    url: 'api/hdd/backend/' + staffType + '/import',
    ContentType: 'multipart/form-data',
    method: 'post',
    data,
  })
}

/**
 * @description: 获取人员总表数据
 * @returns {*}
 */
export function getGeneralPersonalTableData(params) {
  return request({
    url: 'logistics/api/out/fleet/outFleetDriver/page',
    method: 'get',
    params,
  })
}

/**
 * @description: 添加人员
 * @returns {*}
 */
export function addPersonalData(data) {
  return request({
    url: 'logistics/api/out/fleet/driver/dynamic/column',
    method: 'post',
    data,
  })
}

/**
 * @description: 删除人员
 * @returns {*}
 */
export function deletePersonalData(id) {
  return request({
    url: 'logistics/api/out/fleet/driver/dynamic/column/' + id,
    method: 'delete',
  })
}

/**
 * @description: 更新人员
 * @returns {*}
 */
export function updatePersonalData(data, id) {
  return request({
    url: 'logistics/api/out/fleet/outFleetDriver/self/column/' + id,
    method: 'put',
    data,
  })
}

/**
 * @description: 动态创建工资项
 * @returns {*}
 */
export function createDynamicColumn(data) {
  return request({
    url: 'logistics/api/out/fleet/driver/dynamic/column',
    method: 'post',
    data,
  })
}

/**
 * @description: 获取工资项
 * @returns {*}
 */
export function getDynamicColumn(params) {
  return request({
    url: 'logistics/api/out/fleet/driver/dynamic/column/all',
    method: 'get',
    params,
  })
}

/**
 * @description: 批量保存工资项
 * @returns {*}
 */
export function batchSaveDynamicColumn(data) {
  return request({
    url: 'logistics/api/out/fleet/driver/dynamic/column/batch',
    method: 'post',
    data,
  })
}

/**
 * @description: 删除工资项
 * @returns {*}
 */
export function deleteColumn(id) {
  return request({
    url: 'logistics/api/out/fleet/driver/dynamic/column/' + id,
    method: 'delete',
  })
}

/**
 * @description: 导出人员总表
 * @returns {*}
 */
export function exportPersonalExcel(params) {
  return request({
    url: 'logistics/api/out/fleet/outFleetDriver/self/column/export',
    method: 'get',
    params,
    responseType: 'arraybuffer',
  })
}

/**
 * @description: 导入人员总表
 * @returns {*}
 */
export function importPersonalExcel(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: 'logistics/api/out/fleet/outFleetDriver/self/column/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * @description: 获取考勤列表
 * @returns {*}
 */
export function getClockInList(params) {
  return request({
    url: 'logistics/api/out/fleet/driver/ref/clockin/page',
    method: 'get',
    params,
  })
}

/**
 * @description: 获取打卡详情
 * @returns {*}
 */
export function getClockInDetail(id) {
  return request({
    url: 'logistics/api/out/fleet/driver/ref/clockin/' + id,
    method: 'get',
  })
}

/**
 * @description: 修改考勤打开绑定信息
 * @returns {*}
 */
export function clockinUpdate(data) {
  return request({
    url: 'logistics/api/out/fleet/driver/ref/clockin/update',
    method: 'post',
    data,
  })
}

/**
 * @description: 导出考勤打卡设定表
 * @returns {*}
 */
export function exportDakaExcel() {
  return request({
    url: 'logistics/api/out/fleet/driver/ref/clockin/export',
    method: 'get',
  })
}

/**
 * @description: 工作日历设定
 * @returns {*}
 */
export function setWorkCalendar(data) {
  return request({
    url: 'api/hdd/backend/company/setWorkCalendar',
    method: 'post',
    data,
    headers: { templateId: 0 },
  })
}

/**
 * @description: 工作日历查询
 * @returns {*} year
 */
export function showWorkCalendar(data) {
  return request({
    url: 'api/hdd/backend/company/showWorkCalendar',
    method: 'post',
    data,
  })
}

/**
 * @description: 考勤打卡设定 -- 车辆列表
 * @returns {*}
 */
export function getVehiclesAll(params) {
  return request({
    url: 'api/hdd/backend/vehicles/all',
    method: 'get',
    params,
  })
}

/**
 * @description: 修改人员手机号
 * @param id 企业司机关联标识
 * @param driverMobile 新的手机号码
 * @returns {*}
 */
export function modifyMobile(data) {
  return request({
    url: 'logistics/api/out/fleet/driver/ref/modify/mobile',
    method: 'post',
    data,
  })
}

/**
 * @description: 获取人员绑定关系
 * @param driverId 司机id
 * @param year 新的手机号码
 * @returns {*}
 */
export function getPersonalBindingInfo(params) {
  return request({
    url: 'logistics/api/out/fleet/driver/ref/enterprise/clockin/personal',
    method: 'get',
    params,
  })
}

/**
 * @description: 设定离职人员&&时间
 * @param id 人员id
 * @returns {*}
 */
export function termedData(data, id) {
  return request({
    url: 'logistics/api/out/fleet/driver/ref/have/termed/' + id,
    method: 'POST',
    data,
  })
}
