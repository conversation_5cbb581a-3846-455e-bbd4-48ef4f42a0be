/*
 * @Author: zzw
 * @Date: 2021-07-07 11:27:18
 * @LastEditTime: 2025-04-16 14:15:58
 * @FilePath: \tms\src\api\bigScreen\index.js
 * @Description: 大屏api
 */
import request from '@/utils/request'

/**
 * @description: 获取企业常用功能列表
 * @returns {*}
 */
export function getBigScreenCommonFunctionApi(params) {
  return request({
    url: 'logistics/api//bigScreen/commonFunction/all',
    method: 'get',
    params,
  })
}

/**
 * @description: 获取待办事项列表
 * @returns {*}
 */
export function getBigScreenToDoListApi(params) {
  return request({
    url: 'logistics/api//bigScreen/toDoList/all',
    method: 'get',
    params,
  })
}

/**
 * @description: 获取企业所有常用功能列表
 * @returns {*}
 */
export function getBigScreenCommonAllFunctionApi(params) {
  return request({
    url: 'logistics/api//bigScreen/commonFunction/settingAll',
    method: 'get',
    params,
  })
}

/**
 * @description:获取7选4初始数据
 * @returns {*}
 */

export function getBigScreenDefaultDataApi(params) {
  return request({
    url: 'logistics/api//bigScreen/selfDefineChart/dataAll',
    method: 'get',
    params,
  })
}

/**
 * @description:获取7选4切换年月日数据数据
 * @returns {*}
 */

export function getDataSingleInfoApi(params) {
  return request({
    url: 'logistics/api//bigScreen/selfDefineChart/dataSingle',
    method: 'get',
    params,
  })
}

/**
 * @description: 保存企业常用功能列表
 * @returns {*}
 */
export function postBigScreenCommonFunctionApi(data) {
  return request({
    url: 'logistics/api//bigScreen/commonFunction/saveBatch',
    method: 'post',
    data,
  })
}

/**
 * @description: 获取自定义图标列表
 * @returns {*}
 */
export function getBigScreenSelfDefiningIconApi(params) {
  return request({
    url: 'logistics/api//bigScreen/selfDefineChart/all',
    method: 'get',
    params,
  })
}

/**
 * @description: 保存自定义图标列表
 * @returns {*}
 */
export function postBigScreenSelfDefiningIconApi(data) {
  return request({
    url: 'logistics/api//bigScreen/selfDefineChart/saveBatch',
    method: 'post',
    data,
  })
}

/**
 * @description:获取自定义数据
 * @returns {*}
 */

export function getBigScreenSelfDefiningDataApi(params) {
  return request({
    url: 'logistics/api/out/fleet/workbenche/datastatistics/companyStatisticsTemplate',
    method: 'get',
    params,
  })
}

/**
 * @description:获取自定义数据
 * @returns {*}
 */

export function getBigScreenSelfDefiningDataAllApi(params) {
  return request({
    url: 'logistics/api/out/fleet/workbenche/datastatistics/systemStatisticsTemplate',
    method: 'get',
    params,
  })
}

/**
 * @description: 保存自定义数据列表
 * @returns {*}
 */
export function postBigScreenSelfDefiningDataApi(data) {
  return request({
    url: 'logistics/api/out/fleet/workbenche/datastatistics/saveCompanyStatisticsTemplate',
    method: 'post',
    data,
  })
}

/**
 * @description: 获取自定义数据列表
 * @returns {*}
 */
export function getStatisticsDataApi(params) {
  return request({
    url: 'logistics/api/out/fleet/workbenche/datastatistics/getStatisticsData',
    method: 'get',
    params,
  })
}
