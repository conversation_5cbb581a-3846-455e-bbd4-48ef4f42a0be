/*
 * @Author: llm
 * @Date: 2024-12-09 19:43:27
 * @LastEditors: llm
 * @LastEditTime: 2025-07-16 18:12:00
 * @Description:
 */
import request from '@/utils/request'
import { DispatchNoVO, DispatchVinReplaceVO } from './type'
import { AxiosPromise } from 'axios'

/**
 * @description: 获取订单列表
 * @param {*} data
 * @return {*}
 */
export function getOrderListApi(params: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/page',
    method: 'get',
    params,
  })
}

// GET logistics/api/out/fleet/order/dispatch/vin/view/page?orderStatus = 2

/**
 * @description: 获取一键换车订单列表
 * @param {*} data
 * @return {*}
 * orderStatus
 */
export function getdispatchListApi(params: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/vin/view/page',
    method: 'get',
    params,
  })
}

/**
 * @description: 根据车辆查询未完成的调度单
 * @param {*} data
 * @return {*}
 * orderStatus
 */
export function getvehicleWholeId(params: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/task/list/vehicleWholeId',
    method: 'get',
    params,
  })
}

/**
 * @description: 根据车辆查询未完成的调度单
 * @param {*} data
 * @return {*}
 * orderStatus
 */
export function postvehicleWholeId(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/transfer/vin',
    method: 'post',
    data,
  })
}

/**
 * @description: 站点围栏
 * @param {*} params
 * @return {*}
 */
export function getGeofenceListApi(params: any): any {
  return request({
    url: 'logistics/api/common/option/business/select',
    method: 'get',
    params,
  })
}
/**
 * @description: 保存调度路线
 * @param {*} data
 * @return {*}
 */
export function postDispatchLineApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/line/generate',
    method: 'post',
    data,
  })
}
/**
 * @description: 重新路线规划
 * @param {*} data
 * @return {*}
 */
export function postDispatchLineReGenerateApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/line/reGenerate',
    method: 'post',
    data,
  })
}
/**
 * @description: 调度单创建
 * @param {*} data
 * @return {*}
 */
export function postDispatchCreateApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/create',
    method: 'post',
    data,
  })
}
/**
 * @description: 车辆列表
 * @param {*} params
 * @return {*}
 */
export function getFleetVehicleDispatchListApi(params: any): any {
  return request({
    url: 'logistics/api/out/fleet/vehicle/dispatch/page',
    method: 'get',
    params,
  })
}
/**
 * 承运商类型下拉
 * @param params 数据
 * @returns
 */
export function getCommonOptionDistributeSelectApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/common/option/distribute/select',
    method: 'get',
    params,
  })
}
/**
 * @description: 清空调度单
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchClearApi(params: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/clear',
    method: 'get',
    params,
  })
}
/**
 * @description: 删除调度单
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchRemoveVinApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/remove/vin',
    method: 'post',
    data,
  })
}
/**
 * @description: 调度单现金借支详情
 * @param {*} data
 * @return {*}
 */
export function getFleetOrderDispatchVinDetailApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/vin/view/detail/' + data.id,
    method: 'get',
  })
}
/**
 * @description: 调度单现金借支草稿态列表
 * @param {*} data
 * @return {*}
 */
export function getFleetOrderDispatchCashAddDraftListApi(params: DispatchNoVO): any {
  return request({
    url: 'logistics/api/out/fleet/settlement/loan/draft/page',
    method: 'get',
    params,
  })
}
/**
 * @description: 调度单油费借支草稿态列表
 * @param {*} data
 * @return {*}
 */
export function getFleetOrderDispatchOilFeeDraftListApi(params: DispatchNoVO): any {
  return request({
    url: 'logistics/api/out/fleet/loan/oil/fee/draft/page',
    method: 'get',
    params,
  })
}
/**
 * @description: 调度单发布
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchPublishApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/publish',
    method: 'post',
    data,
  })
}
/**
 * @description: 批量装车
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchBatchLoadingApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/batchLoading',
    method: 'post',
    data,
  })
}
/**
 * @description: 批量交车
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchBatchDeliveryApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/batchDelivery',
    method: 'post',
    data,
  })
}
/**
 * @description: 变更调度单
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchChangeApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/change',
    method: 'post',
    data,
  })
}
/**
 * @description: vin取消调度
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchVinCancelApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/vin/cancel',
    method: 'post',
    data,
  })
}
/**
 * @description: 添加路线
 * @param {*} data
 * @return {*}
 */
export function postFleetRouteDispatchMileageApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/line/mileage',
    method: 'post',
    data,
  })
}
/**
 * @description: 调度单路线详情
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchLineDetailApi(params: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/summary',
    method: 'get',
    params,
  })
}
/**
 * @description: 调度单类型枚举
 * @param {*} params
 * @return {*}
 */
export function getDispatchOrderTypeApi(params: any): any {
  return request({
    url: 'logistics/api/common/option/distribute/select?selectType=dispatchOrderType',
    method: 'get',
    params,
  })
}
/**
 * @description: 修改调度单状态
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchVinViewTypeAlterApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/type/alter',
    method: 'post',
    data,
  })
}
/**
 * @description: 根据调度单id查询司机补贴(司机预估收入)调整信息
 * @param {*} data
 * @return {*}
 */
export function getDispatchFeeAdjustInfoApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/outFleetOrderDispatchFee/getDispatchFeeAdjustInfo/' + data.id,
    method: 'get',
  })
}
/**
 * @description: 根据调度单id查询司机补贴(司机预估收入)调整信息
 * @param {*} data
 * @return {*}
 */
export function postAdjustDriverDispatchSubsidyApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/outFleetOrderDispatchFee/adjustDriverDispatchSubsidy',
    method: 'post',
    data,
  })
}
/**
 * @description: 编辑调度
 * @param {*} data
 * @return {*}
 */
export function putFleetOrderDispatchApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/' + data.id,
    method: 'put',
    data,
  })
}
/**
 * @description: 创建空调度单
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchCreateEmptyApi(data: any): any {
  return request({
    url: 'logistics/api/out/fleet/order/dispatch/create/empty',
    method: 'post',
    data,
  })
}
/**
 * @description: 撤回交车
 * @param {*} data
 * @return {*}
 */
export function getFleetOrderDispatchVinCancelDeliveryApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/vin/cancel/delivery/${data.id}`,
    method: 'get',
    data,
  })
}
/**
 * @description: 订单替换
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchVinReplaceApi(data: DispatchVinReplaceVO): any {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/vin/replace`,
    method: 'post',
    data,
  })
}
/**
 * @description: 借支新增按钮展示权限
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchCashAddPermissionApi(params: DispatchNoVO): any {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/cash/add/permission`,
    method: 'get',
    params,
  })
}
/**
 * @description: 现金借支列表
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchCashAddDetailApi(params: DispatchNoVO): any {
  return request({
    url: `logistics/api/out/fleet/loan/cash/dispatch/detail`,
    method: 'get',
    params,
  })
}
/**
 * @description: 草稿态现金借支详情
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchCashAddDraftDetailApi(params: any): any {
  return request({
    url: `logistics/api/out/fleet/settlement/loan/draft/detail/${params.id}`,
    method: 'get',
  })
}
/**
 * @description: 草稿态油费借支详情
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchOilFeeDraftDetailApi(params: any): any {
  return request({
    url: `logistics/api/out/fleet/loan/oil/fee/draft/detail/${params.id}`,
    method: 'get',
  })
}
/**
 * @description: 油费借支列表
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchOilFeeDetailApi(params: DispatchNoVO): any {
  return request({
    url: `logistics/api/out/fleet/loan/oil/fee/dispatch/detail`,
    method: 'get',
    params,
  })
}
/**
 * @description: 路桥费借支列表
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchEtcFeeDetailApi(params: DispatchNoVO): any {
  return request({
    url: `logistics/api/out/fleet/settlement/loan/etc/fee
/dispatch/detail`,
    method: 'get',
    params,
  })
}
/**
 * @description: 新增借支预览信息
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchCashAddPreviewApi(params: DispatchNoVO): any {
  return request({
    url: `logistics/api/out/fleet/loan/cash/add/preview`,
    method: 'get',
    params,
  })
}
/**
 * @description: 新增油费借支预览信息
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchOilFeePreviewApi(params: DispatchNoVO): any {
  return request({
    url: `logistics/api/out/fleet/loan/oil/fee/add/preview`,
    method: 'get',
    params,
  })
}
/**
 * @description: 新增etc借支预览信息
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchEtcFeePreviewApi(params: DispatchNoVO): any {
  return request({
    url: `logistics/api/out/fleet/settlement/loan/etc/fee/add/preview`,
    method: 'get',
    params,
  })
}

/**
 * @description: 新增现金借支
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchCashAddApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/loan/cash/add`,
    method: 'post',
    data,
  })
}
/**
 * @description: 新增油费借支
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchOilFeeAddApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/loan/oil/fee/add`,
    method: 'post',
    data,
  })
}
/**
 * @description: 新增路桥费借支
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchEtcFeeAddApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/settlement/loan/etc/fee/add`,
    method: 'post',
    data,
  })
}
/**
 * @description: 根据vin码查询调度单下拉
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchSelectOptionByVinApi(params: any): any {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/select/optionByVin`,
    method: 'get',
    params,
  })
}
/**
 * @description: 承运商下拉
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchCarrierSelectOptionApi(params: any): any {
  return request({
    url: `logistics/api/common/option/business/select?label=name&dataSource=车队-自有车队,车队-承运商&value=id,name,type`,
    method: 'get',
  })
}
/**
 * @description: 司机下拉
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchDriverSelectOptionApi(params: any): any {
  return request({
    url: `logistics/api/out/fleet/outFleetDriver/select/optionTree`,
    method: 'get',
    params,
  })
}
/**
 * @description: 质损类型下拉/质损部位下拉/处理模式下拉
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchDealTypeSelectOptionApi(params: any): any {
  return request({
    url: `logistics/api/config/qualityLoss/type/select/option`,
    method: 'get',
    params,
  })
}
/**
 * @description: 车牌号下拉
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchPlateNumberSelectOptionApi(params: any): any {
  return request({
    url: `logistics/api/common/option/business/select`,
    method: 'get',
    params,
  })
}
/**
 * @description: 车牌号下拉
 * @param {*} params
 * @return {*}
 */
export function orderDispatchCancelApi(params: any): any {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/cancel`,
    method: 'get',
    params,
  })
}
/**
 * @description: 获取事故调度单列表数据
 * @param {*} params
 * @return {*}
 */
export function getQualityLossListApi(params: any): any {
  return request({
    url: `logistics/api/out/fleet/outFleetVehicleAccident/dynamic0/qualityLoss`,
    method: 'get',
    params,
  })
}
/**
 * @description: 更新事故调度单列表数据
 * @param {*} data
 * @return {*}
 */
export function updateQualityLossListApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/outFleetVehicleAccident/dynamic1/qualityLoss`,
    method: 'post',
    data,
  })
}
/**
 * @description: 删除 - 事故调度单列表数据
 * @param {*} data
 * @return {*}
 */
export function deleteQualityLossListApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/outFleetVehicleAccident/dynamic1/qualityLoss`,
    method: 'delete',
    data,
  })
}
/**
 * @description: 事故 - 根据id获取金额
 * @param {*} data
 * @return {*}
 */
export function getFineAmountApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLoss/getAccidentFineAmount`,
    method: 'post',
    data,
  })
}
/**
 * @description: 事故单 - 提交审批
 * @param {*} data
 * @return {*}
 */
export function stepSubmitApprovalApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/outFleetVehicleAccident/dynamic4/stepThree/${data.id}`,
    method: 'put',
    data,
  })
}
/**
 * @description: 事故单 - 获取详情
 * @param {*} data
 * @return {*}
 */
export function getAccidentApprovalDetailsApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/outFleetVehicleAccident/${data.id}`,
    method: 'get',
  })
}
/**
 * @description: 事故单 - 新增对公支付信息
 * @param {*} data
 * @return {*}
 */
export function addPublicFeeApplyApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/outFleetVehicleAccident/dynamic2/publicFeeApply`,
    method: 'post',
    data,
  })
}
/**
 * @description: 事故单 - 修改对公支付信息
 * @param {*} data
 * @return {*}
 */
export function editPublicFeeApplyApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/outFleetVehicleAccident/dynamic2/publicFeeApply`,
    method: 'put',
    data,
  })
}
/**
 * @description: 事故单 - 删除对公支付信息
 * @param {*} data
 * @return {*}
 */
export function deletePublicFeeApplyApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/outFleetVehicleAccident/dynamic2/publicFeeApply`,
    method: 'delete',
    data,
  })
}
/**
 * @description: 事故单 - 新增对私支付信息
 * @param {*} data
 * @return {*}
 */
export function addPrivateFeeApplyApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/outFleetVehicleAccident/dynamic3/privateFeeApply`,
    method: 'post',
    data,
  })
}
/**
 * @description: 事故单 - 修改对私支付信息
 * @param {*} data
 * @return {*}
 */
export function editPrivateFeeApplyApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/outFleetVehicleAccident/dynamic3/privateFeeApply`,
    method: 'put',
    data,
  })
}
/**
 * @description: 事故单 - 删除对私支付信息
 * @param {*} data
 * @return {*}
 */
export function deletePrivateFeeApplyApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/outFleetVehicleAccident/dynamic3/privateFeeApply`,
    method: 'delete',
    data,
  })
}
/**
 * @description: 事故单 - 司机罚款类型下拉
 * @param {*} params
 * @return {*}
 */
export function fineSelectOptionApi(params: any): any {
  return request({
    url: `logistics/api/config/fine/select/option`,
    method: 'get',
    params,
  })
}
/**
 * @description: 保存质损信息
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchQualityLossApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLoss`,
    method: 'post',
    data,
  })
}
/**
 * @description: 编辑质损信息
 * @param {*} data
 * @return {*}
 */
export function putFleetOrderDispatchQualityLossApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLoss/${data.id}`,
    method: 'put',
    data,
  })
}
/**
 * @description: 保存保险信息
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchQualityLossInsuranceApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLossInsurance`,
    method: 'post',
    data,
  })
}
/**
 * @description: 编辑保险信息
 * @param {*} data
 * @return {*}
 */
export function putFleetOrderDispatchQualityLossInsuranceApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLossInsurance/${data.id}`,
    method: 'put',
    data,
  })
}

/**
 * @description: 保存费用申请
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchQualityLossFeeApplyApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLoss/feeApply`,
    method: 'post',
    data,
  })
}
/**
 * @description: 质损确认提交审批
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchQualityLossSubmitApproveApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLoss/submitApprove`,
    method: 'post',
    data,
  })
}

/**
 * @description: 变更起点
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchStartAreaAlterApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/startArea/alter`,
    method: 'post',
    data,
  })
}
/**
 * @description: 变更终点
 * @param {*} data
 * @return {*}
 */
export function postFleetOrderDispatchEndAreaAlterApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/endArea/alter`,
    method: 'post',
    data,
  })
}
//新增围栏
export function postGeoFenceAddApi(data: any): any {
  return request({
    url: `logistics/api/company/geofence`,
    method: 'post',
    data,
  })
}
/**
 * @description: 围栏模糊搜索
 * @param {*} params
 * @return {*}
 */
export function getFleetOrderDispatchGeofenceSearchApi(params: any): any {
  return request({
    url: `logistics/api/company/geofence/page`,
    method: 'get',
    params,
  })
}
/**
 * @description: 现金借支编辑详情
 * @param {*} params
 * @return {*}
 */
export function getOutFleetLoanCashDetailApi(params: any): any {
  return request({
    url: `logistics/api/out/fleet/loan/cash/${params.id}`,
    method: 'get',
  })
}
/**
 * @description: 现金借支编辑
 * @param {*} data
 * @return {*}
 */
export function postOutFleetLoanCashEditApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/loan/cash/edit`,
    method: 'post',
    data,
  })
}
/**
 * @description: 草稿态现金借支编辑
 * @param {*} data
 * @return {*}
 */
export function postOutFleetLoanCashDraftEditApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/settlement/loan/draft/edit`,
    method: 'post',
    data,
  })
}
/**
 * @description: 草稿态现金借支删除
 * @param {*} data
 * @return {*}
 */
export function deleteOutFleetLoanCashDraftApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/settlement/loan/draft/${data.id}`,
    method: 'delete',
    data,
  })
}
/**
 * @description: 油费借支编辑详情
 * @param {*} params
 * @return {*}
 */
export function getOutFleetLoanOilFeeDetailApi(params: any): any {
  return request({
    url: `logistics/api/out/fleet/loan/oil/fee/${params.id}`,
    method: 'get',
  })
}
/**
 * @description: 油费借支编辑
 * @param {*} data
 * @return {*}
 */
export function postOutFleetLoanOilFeeEditApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/loan/oil/fee/edit`,
    method: 'post',
    data,
  })
}
/**
 * @description: 草稿态油费借支删除
 * @param {*} data
 * @return {*}
 */
export function deleteOutFleetLoanOilFeeDraftApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/loan/oil/fee/draft/${data.id}`,
    method: 'delete',
    data,
  })
}
/**
 * @description: 草稿态油费借支编辑
 * @param {*} data
 * @return {*}
 */
export function postOutFleetLoanOilFeeDraftEditApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/loan/oil/fee/draft/edit`,
    method: 'post',
    data,
  })
}
/**
 * @description: 路桥费借支编辑详情
 * @param {*} params
 * @return {*}
 */
export function getOutFleetLoanEtcFeeDetailApi(params: any): any {
  return request({
    url: `logistics/api/out/fleet/settlement/loan/etc/fee/${params.id}`,
    method: 'get',
  })
}
/**
 * @description: 路桥费借支编辑
 * @param {*} data
 * @return {*}
 */
export function postOutFleetLoanEtcFeeEditApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/settlement/loan/etc/fee/edit`,
    method: 'post',
    data,
  })
}
/**
 * @description: 质损司机罚款金额(挂账金额)
 * @param {*} data
 * @return {*}
 */
export function postQualityLossFineAmountApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLoss/getFineAmount`,
    method: 'post',
    data,
  })
}
/**
 * @description: 新增回收
 * @param {*} data
 * @return {*}
 */
export function postQualityLossRecycleBatchAddApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLossRecycle/batchAdd`,
    method: 'post',
    data,
  })
}
/**
 * @description: 编辑回收
 * @param {*} data
 * @return {*}
 */
export function postQualityLossRecycleBatchEditApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLossRecycle/batchEdit`,
    method: 'post',
    data,
  })
}
/**
 * @description: 质损详情
 * @param {*} data
 * @return {*}
 */
export function getQualityLossApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLoss/${data.id}`,
    method: 'get',
  })
}
/**
 * @description: 保险详情
 * @param {*} data
 * @return {*}
 */
export function getQualityLossInsuranceInfoApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLossInsurance/insuranceInfo/${data.id}`,
    method: 'get',
  })
}
/**
 * @description: 费用详情
 * @param {*} data
 * @return {*}
 */
export function getQualityLossFeeInfoApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLoss/feeInfo/${data.id}`,
    method: 'get',
  })
}
/**
 * @description: 回收详情
 * @param {*} params
 * @return {*}
 */
export function getQualityLossRecycleAllApi(params: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLossRecycle/all`,
    method: 'get',
    params,
  })
}
/**
 * @description: 获取所有有订单的vin
 * @param {*} params
 * @return {*}
 */
export function getOutFleetOrderPageApi(params: any): any {
  return request({
    url: `logistics/api/out/fleet/order/page`,
    method: 'get',
    params,
  })
}
/**
 * @description: 调度单上订单装车时间变更
 * @param {*} params
 * @return {*}
 */
export function postOutFleetOrderDispatchAlterLoadTimeVinApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/alter/loadTime/vin`,
    method: 'post',
    data,
  })
}
/**
 * @description: 调度单上订单装车时间变更
 * @param {*} params
 * @return {*}
 */
export function postOutFleetOrderDispatchAlterActualDropTimeVinApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/alter/actualDropTime/vin`,
    method: 'post',
    data,
  })
}
/**
 * @description: 调度单装车时间批量变更
 * @param {*} data
 * @return {*}
 */
export function postOutFleetOrderDispatchAlterLoadTimeApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/alter/loadTime`,
    method: 'post',
    data,
  })
}
/**
 * @description: 调度单交车时间批量变更
 * @param {*} data
 * @return {*}
 */
export function postOutFleetOrderDispatchAlterActualDropTimeApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/alter/actualDropTime`,
    method: 'post',
    data,
  })
}
/**
 * @description: 根据调度单号查询详情
 * @param {*} data
 * @return {*}
 */
export function getOutFleetOrderDispatchVinViewByDispatchNoApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/vin/view/getByDispatchNo`,
    method: 'post',
    params: data,
  })
}
/**
 * @description: 补发票
 * @param {*} data
 * @return {*}
 */
export function postQualityLossPublicFeeApplyAddInvoiceApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/qualityLossPublicFeeApply/addInvoice`,
    method: 'post',
    data,
  })
}
/**
 * @description: 质损原因下拉
 * @param {*} data
 * @return {*}
 */
export function getQualityLossQualityDamageReasonsSelectOptionApi(params: any): any {
  return request({
    url: `logistics/api/config/qualityLoss/qualityDamageReasons/selectOption/quality`,
    method: 'get',
    params,
  })
}
/**
 * 基地下拉
 * @param params 数据
 * @returns
 */
export function getDepartmentTreeOptionApi(params: any): AxiosPromise<SelectOptions[]> {
  return request({
    url: '/logistics/company/department/tree/option',
    method: 'get',
    params,
  })
}

/**
 * 保险类型下拉
 * @param params 数据
 * @return {*}
 */
export function getQualityLossInsuranceTypeSelectOptionApi(params: any): any {
  return request({
    url: `logistics/api/config/qualityLoss/insurancetype/all`,
    method: 'get',
    params,
  })
}
/**
 * @description: 添加vin
 * @param {*} data
 * @return {*}
 */
export function postOutFleetOrderDispatchJoinApi(data: any): any {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/join`,
    method: 'post',
    data,
  })
}
/**
 * @description: 油费明细
 * @param {*} data
 * @return {*}
 */
export function getOutFleetOrderDispatchOilDetailApi(params: any): any {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/oil/detail`,
    method: 'get',
    params,
  })
}

/**
 * @description: 修改VIN下拉接口 模糊查询
 * @param {*} keyword
 * @return {*}
 */
export function selectvin(params: any): any {
  return request({
    url: `logistics/api/out/fleet/order/driver/select/vin`,
    method: 'get',
    params,
  })
}


/**
 * @description: 新增线路里程
 * @data {*} data
 * @return {*}
 */
export function postmileage(data: any): any {
  return request({
    url: `logistics/api/out/fleet/business/line/mileage`,
    method: 'post',
    data,
  })
}
