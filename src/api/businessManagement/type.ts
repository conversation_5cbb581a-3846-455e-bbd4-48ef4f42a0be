import { UploadUserFile } from 'element-plus'

/*
 * @Author: llm
 * @Date: 2024-12-26 19:53:14
 * @LastEditors: llm
 * @LastEditTime: 2025-07-16 17:57:54
 * @Description:
 */
export interface DispatchVinReplaceVO {
  /**
   * 调度单号
   */
  dispatchNo: string
  /**
   * 订单id
   */
  orderId: string
  /**
   * 系统订单号
   */
  orderCode: string
}

export interface DispatchNoVO {
  /**
   * 调度单号
   */
  dispatchNo: string
  /**
   * 草稿态 0:草稿态 1:发布态
   */
  draft?: number
}

export interface QualityLossVO {
  /**
   * 基地id
   */
  baseId: string
  /**
   * 基地名称
   */
  baseName: string
  /**
   * 事故编号
   */
  accidentNo: string
  /**
   * 质损金额
   */
  amount: number
  /**
   * 品牌名称
   */
  brandName: string
  /**
   * 是否买断
   */
  buyUp: string
  /**
   * 承运商id
   */
  carrierInfo: string
  /**
   * 承运商名称
   */
  carrierName: string
  /**
   * 承运商类型
   */
  carrierType: string
  /**
   * 司机挂账类型
   */
  chargeType: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人
   */
  createUserName: string
  /**
   * 客户名称
   */
  customerName: string
  /**
   * 质损部位
   */
  damagedArea: string[]
  /**
   * 完成情况
   */
  dealStatus: string
  /**
   * 完成时间
   */
  dealTime: string
  /**
   * 处理模式
   */
  dealType: string
  /**
   * 	完成人
   */
  dealUserName: string
  /**
   * 经销商联系人
   */
  dealerContacts: string
  /**
   * 	经销商联系人
   */
  dealerMobile: string
  /**
   * 经销商
   */
  dealerName: string
  /**
   * 部门id
   */
  deptId: number
  /**
   * 调度单号
   */
  dispatchNo: string
  /**
   * 司机垫付金额
   */
  driverAdvancePaymentAmount: string
  /**
   * 	司机现金挂账金额
   */
  driverChargeAmount: string
  /**
   * 责任司机id
   */
  driverId: number
  /**
   * 司机信息
   */
  driverInfo: string
  /**
   * 责任人
   */
  driverName: string
  /**
   * 司机类型
   */
  driverType: string
  /**
   * 	发生地点
   */
  happenAddress: string
  /**
   * 发生时间
   */
  happenTime: string
  /**
   * 维修费用
   */
  maintenanceFee: number
  /**
   * 更新时间
   */
  modifyTime: string
  /**
   * 更新人
   */
  modifyUserName: string
  /**
   * 质损编号
   */
  no: string
  /**
   * 问题图片
   */
  picsList: VideoImageVO[]
  /**
   * 问题描述
   */
  problemDesc: string
  /**
   * 降价费用
   */
  reducePrice: number
  /**
   * 备注
   */
  remark: string
  /**
   * 责任类型
   */
  responsibilityType: string
  /**
   * 安全员
   */
  safetyOfficer: string
  /**
   * 质损类型
   */
  type: string[]
  /**
   * 车型名称
   */
  vehicleModelName: string
  /**
   * 车牌号
   */
  vehicleNo: string
  /**
   * 车牌颜色
   */
  plateColor: string
  /**
   * 问题视频
   */
  videosList: VideoImageVO[]
  /**
   * 	质损VIN
   */
  vin: string
  /**
   * 质损原因
   */
  qualityDamageReasons: string
  /**
   * 是否出口车
   */
  exportCar: string
  /**
   * 是否取消装车
   */
  cancelLoad: boolean
}

export interface VideoImageVO {
  url: string
  name: string
}
/**
 * 质损保险信息
 */
export interface QuailtyLossInsuranceVO {
  /**
   * 是否受理
   */
  accept: boolean
  /**
   * 受理时间
   */
  acceptTime: string
  /**
   * 保险公司赔付金额
   */
  compensationAmount: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人
   */
  createUserName: string
  /**
   * 材料提供状态
   */
  dataSubmitStatus: string
  /**
   * 部门id
   */
  deptId: number
  /**
   * 销案时间
   */
  finishTime: string
  /**
   * 保险材料图片
   */
  insurancePics: string
  /**
   * 保险类型
   */
  insuranceType: string
  /**
   * 邮寄信息
   */
  mails: Mail[]
  /**
   * 更新时间
   */
  modifyTime: string
  /**
   * 更新人
   */
  modifyUserName: string
  /**
   * 回款状态
   */
  paymentCollectionStatus: string
  /**
   * 回款时间
   */
  paymentCollectionTime: string
  /**
   * 质损id
   */
  qualityLossId: number
  /**
   * 备注
   */
  remark: string
  /**
   * 报案号
   */
  reportNo: string
  /**
   * 报案时间
   */
  reportTime: string
  /**
   * 是否勘察
   */
  survey: string
}

export interface Mail {
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人
   */
  createUserName: string
  /**
   * 部门id
   */
  deptId: number
  /**
   * 邮寄编号
   */
  mailNo: string
  /**
   * 邮寄人
   */
  mailPerson: string
  /**
   * 邮寄时间
   */
  mailTime: string
  /**
   * 更新时间
   */
  modifyTime: string
  /**
   * 更新人
   */
  modifyUserName: string
  /**
   * 质损保险id
   */
  qualityLossInsuranceId: number
  /**
   * 备注
   */
  remark: string
}
export interface QualityLossFeeApplyVO {
  /**
   * 挂账类型
   */
  chargeType: string
  /**
   * 罚款类型
   */
  fineConfigId: string
  /**
   * 罚款类型
   */
  fineConfigType: string
  /**
   * 司机承担金额
   */
  driverBearAmount: number
  /**
   * 司机垫付金额
   */
  driverAdvancePaymentAmount: number
  /**
   * 司机挂账金额
   */
  driverChargeAmount: number
  /**
   * 私对私费用
   */
  privateFees: PrivateFee[]
  /**
   * 公对公费用
   */
  publicFees: PublicFee[]
  /**
   * 质损id
   */
  qualityLossId: string
  /**
   * 承运商名称
   */
  carrierName: string
  /**
   * 司机名称
   */
  driverName: string
}

export interface PrivateFee {
  /**
   *  前端临时主键id
   */
  fontTempId?: string
  /**
   * 事故id
   */
  accidentId?: string
  /**
   * 银行卡号
   */
  accountNo: string
  /**
   * 金额
   */
  amount: number
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * 创建人
   */
  createUserName?: string
  /**
   * 部门id
   */
  deptId?: string
  /**
   * 修改时间
   */
  modifyTime?: string
  /**
   * 修改人
   */
  modifyUserName?: string
  /**
   * 名称
   */
  name: string
  /**
   * 质损id
   */
  qualityLossId: string
}

export interface PublicFee {
  /**
   * 主键id
   */
  id?: string
  /**
   *  前端临时主键id
   */
  fontTempId?: string
  /**
   * 事故id
   */
  accidentId?: string
  /**
   * 账户名称
   */
  accountName: string
  /**
   * 银行卡号
   */
  accountNo: string
  /**
   * 开户银行
   */
  bank: string
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * 创建人
   */
  createUserName?: string
  /**
   * 部门id
   */
  deptId?: string
  /**
   * 公对公发票号
   */
  invoiceNo: string
  /**
   * 发票图片
   */
  invoicePicList: UploadUserFile[]
  /**
   * 修改时间
   */
  modifyTime?: string
  /**
   * 修改人
   */
  modifyUserName?: string
  /**
   * 质损id
   */
  qualityLossId: string
  /**
   * 税额
   */
  taxAmount: number
  /**
   * 含税金额
   */
  taxedAmount: number
  /**
   * 未税金额
   */
  untaxedAmount: number
}

/**
 * 回收信息
 */
export interface QualityLossRecycleVO {
  /**
   * 质损id
   */
  qualityLossId: string
  /**
   * 回收信息
   */
  recycles: Recycle[]
}

export interface Recycle {
  /**
   * 回收物品去向
   */
  destination: string
  /**
   * 回收物品名称
   */
  name: string
  /**
   * 质损id
   */
  qualityLossId: string
  /**
   * 接收人
   */
  recipient: string
}
