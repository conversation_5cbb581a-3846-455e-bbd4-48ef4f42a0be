/*
 * @Author: llm
 * @Date: 2024-04-26 15:33:26
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-05-27 11:24:10
 * @Description:
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'

/**
 * 获取列表
 */
export function carbonEmission(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/report/business/report/carbon/emission',
    method: 'get',
    params,
  })
}
/**
 * 导出
 */
export function exportEmission(params: any) {
  return request({
    url: 'tms/report/business/report/carbon/emission/export',
    method: 'get',
    params,
    responseType: 'arraybuffer',
  })
}
