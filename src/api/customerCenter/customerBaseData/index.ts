/*
 * @Author: llm
 * @Date: 2023-07-05 09:21:48
 * @LastEditors: llm
 * @LastEditTime: 2024-09-02 10:18:55
 * @Description:
 *
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
/**
 * 新增联运线路
 */
export function postCustomerLineApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/customer/line',
    method: 'post',
    data,
  })
}
/**
 * 新增子联运线路
 */

export function postAddSubLineApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/customer/sub/line/view/addSubLine',
    method: 'post',
    data,
  })
}
