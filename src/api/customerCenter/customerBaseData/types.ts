/*
 * @Author: llm
 * @Date: 2023-07-05 09:21:59
 * @LastEditors: llm
 * @LastEditTime: 2023-07-07 16:21:43
 * @Description: 客户基础数据
 *
 */

/**
 * 客户表单form
 */
export interface CustomerForm {
  id?: string
}
/**
 * 用户查询对象类型
 */
export interface UserQuery extends PageQuery {
  /**
   * 客户id
   */
  relationId?: string
}
/**
 * 客户信息
 */
export interface CustomerVO {
  /**
   * id
   */
  id?: string
  /**
   * 关联id
   */
  relationId?: string
  /**
   * 中文名称
   */
  [name: string]: any
  /**
   * 中文简称
   */
  nameShort?: string
  /**
   * 英文名称
   */
  english?: string
  /**
   * 英文简称
   */
  englishShort?: string
  /**
   * 地址
   */
  address?: string
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * 修改时间
   */
  modifyTime?: string
  /**
   * 客户编号
   */
  customerNo?: string
  /**
   * 是否禁用
   */
  enable?: boolean
  /**
   * 备注
   */
  remarks?: string
  /**
   * 等级
   */
  level?: number
}
/**
 * 客户查询条件
 */
export interface CustomerQuery {
  /**
   * 启用禁用
   */
  enable?: Boolean | string
}
export interface DescriptionsConfigVO extends CustomerVO {
  label?: string
  type?: string
}
/**
 * 用户TableItem
 */
export interface UserTableItem {
  id?: string
}
/**
 * 工厂TableItem
 */
export interface FactoryTableItem {}
/**
 * 仓库TableItem
 */
export interface WareHouseTableItem {}
/**
 * 4s店TableItem
 */
export interface ForeStoreTableItem {}
/**
 * 客户id
 */
export interface RelationId {
  /**
   * 客户id
   */
  relationId?: string
}
