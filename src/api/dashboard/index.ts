/*
 * @Author: llm
 * @Date: 2023-10-18 11:45:11
 * @LastEditors: llm <EMAIL>
 * @LastEditTime: 2023-10-28 09:34:14
 * @Description:
 *
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
/**
 * 在途跟踪车辆列表
 * @param data ids
 */
export function getLargeScreenTransitApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/company/statistics/large/screen/transit`,
    method: 'post',
    data,
  })
}
/**
 * 获取配置下拉
 */
export function getLargeScreenOptionsApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/company/statistics/large/screen/index/option`,
    method: 'get',
    data,
  })
}
/**
 * 大屏设置
 */
export function setLargeScreenConfigApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/company/statistics/large/screen/config`,
    method: 'post',
    data,
  })
}
/**
 * 获取大屏设置
 */
export function getLargeScreenConfigApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/company/statistics/large/screen/select`,
    method: 'get',
    data,
  })
}
/**
 * 左侧数据
 */
export function getLargeScreenLeftApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/company/statistics/large/screen/index/value`,
    method: 'post',
    data,
  })
}
/**
 * 右侧数据
 */
export function getLargeScreenRightApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/company/statistics/large/screen/index/chart`,
    method: 'post',
    data,
  })
}
/**
 * 底部预警数据
 */
export function getLargeScreenBottomApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/company/statistics/large/screen/fore/warning`,
    method: 'get',
    data,
  })
}
/**
 * 获取刷新时间
 */
export function getLargeScreenRefreshTimeApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/company/statistics/large/screen/refresh/time`,
    method: 'get',
    data,
  })
}
