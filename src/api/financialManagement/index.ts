/*
 * @Author: llm
 * @Date: 2024-12-27 20:10:23
 * @LastEditors: llm
 * @LastEditTime: 2025-05-27 17:24:12
 * @Description:
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
/**
 * 驾驶员下拉
 */
export function getFleetOutFleetDriverSelectOptionApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/outFleetDriver/select/option',
    method: 'get',
    params,
  })
}
/**
 * 客户下拉
 */
export function getFleetOutFleetBusinessCustomerSelectOptionApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/business/customer/select/option',
    method: 'get',
    params,
  })
}
/**
 * 维修供应商下拉
 */
export function getFleetOutFleetSupplierSelectOptionApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/supplier/repair/supplier/select/option',
    method: 'get',
    params,
  })
}
/**
 * 维修结算-维修单列表
 */
export function getFleetSettlementSupplierBillAllApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/repair/management/getAllAndSummary',
    method: 'get',
    params,
  })
}
/**
 * 维修结算-维修单金额汇总
 */
export function postFleetSettlementRepairReconciliationManagementSummaryApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/repair/management/summary',
    method: 'post',
    data,
  })
}
/**
 * 新增维修结算单
 */
export function postFleetSettlementRepairAddBillApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/repair/management/addBill',
    method: 'post',
    data,
  })
}
/**
 * 编辑维修结算单
 */
export function postFleetSettlementRepairManagementBindChangeApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/repair/management/updateBill',
    method: 'post',
    data,
  })
}
/**
 * 维修结算单详情
 */
export function postFleetSettlementRepairManagementInfoApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/repair/management/billInfo',
    method: 'post',
    data,
  })
}
/**
 * 司机对账明细弹窗查询汇总
 */
export function postFleetOrderSettlementDetailViewDriverDetailSummaryApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/settlementDetailView/driverDetail/summary',
    method: 'post',
    data,
  })
}
/**
 * 司机对账明细弹窗查询列表
 */
export function getFleetOrderSettlementDetailViewDriverDetailPageApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/settlementDetailView/driverDetail/page',
    method: 'get',
    params,
  })
}
/**
 * 生成结算单
 */
export function postFleetOrderSettlementGenerateSettlementApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/settlement/generateSettlement',
    method: 'post',
    data,
  })
}
/**
 * 结算单详情
 */
export function getFleetOrderSettlementGetSettlementInfoApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/settlement/getSettlementInfo',
    method: 'get',
    params,
  })
}
/**
 * 编辑结算单
 */
export function postFleetOrderSettlementUpdateSettlementApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/settlement/updateSettlement',
    method: 'post',
    data,
  })
}
/**
 * 新增对账单tab页list
 */
export function getFleetSettlementCustomerReconciliationManagementComputeAllApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/customer/reconciliation/management/compute/all',
    method: 'post',
    data,
  })
}
/**
 * 新增对账单
 */
export function postFleetSettlementCustomerReconciliationManagementAddBillApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/customer/reconciliation/management/add/bill',
    method: 'post',
    data,
  })
}
/**
 * 编辑对账单
 */
export function postFleetSettlementCustomerReconciliationManagementBindChangeApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/customer/reconciliation/management/bind/change',
    method: 'post',

    data,
  })
}
/**
 * 对账单详情
 */
export function postFleetSettlementCustomerReconciliationManagementComputeApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/customer/reconciliation/management/compute',
    method: 'post',
    data,
  })
}
/**
 * 点击新增对账单按钮调用计算
 */
export function postFleetSettlementCustomerReconciliationManagementSummaryApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/customer/reconciliation/management/summary',
    method: 'post',
    data,
  })
}
/**
 * 获取奖惩
 */
export function getDistributeSelectReceivePaymentMethodselectApi(): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/common/option/distribute/select?selectType=ReceivePaymentMethod',
    method: 'get',
  })
}
/**
 * 承运商下拉
 */
export function getFleetCarrierSelectOptionApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/carrier/select/option?simple=true',
    method: 'get',
    params,
  })
}
/**
 * 汇总
 */
export function getFleetOrderCarrierCostComputeSummaryApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/carrier/cost/compute/summary',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data,
  })
}
/**
 * 对账详情
 */
export function getFleetOrderCarrierCostComputeSummaryDetailApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/order/carrier/cost/summary/${data.id}`,
    method: 'get',
  })
}
/**
 * 保存结算单
 */
export function postFleetOrderCarrierCostSummaryApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/carrier/cost/summary',
    method: 'post',
    data,
  })
}
/**
 * 修改结算单
 */
export function putFleetOrderCarrierCostSummaryApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/order/carrier/cost/summary/${data.id}`,
    method: 'put',
    data,
  })
}
/**
 * 客户结算-开票管理已开发票详情
 */
export function postFleetSettlementCustomerInvoicesManagementAllApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/customer/invoices/management/get/invoice',
    method: 'post',
    data,
  })
}

/**
 * 客户结算-开票管理绑定发票
 */
export function postFleetSettlementCustomerInvoicesManagementBindApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/customer/open/invoices/management/bind',
    method: 'put',
    data,
  })
}

/**
 * 客户结算-开票管理解绑发票
 */
export function postFleetSettlementCustomerInvoicesManagementUnBindApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/customer/open/invoices/management/unbind',
    method: 'put',
    data,
  })
}
/**
 * 客户结算-开票管理新增发票
 */
export function postFleetSettlementCustomerInvoicesManagementAddInvoiceApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/customer/open/invoices/management/addInvoice',
    method: 'POST',
    data,
  })
}
/**
 * 维修结算-开票管理已开发票详情
 */
export function postFleetSettlementRepairInvoicesManagementAllApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/repair/invoice/management/get/invoice',
    method: 'post',
    data,
  })
}

/**
 * 维修结算-开票管理绑定发票
 */
export function postFleetSettlementRepairInvoicesManagementBindApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/repair/invoice/management/bind',
    method: 'put',
    data,
  })
}

/**
 * 维修结算-开票管理解绑发票
 */
export function postFleetSettlementRepairInvoicesManagementUnBindApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/repair/invoice/management/unbind',
    method: 'put',
    data,
  })
}
/**
 * 维修结算-开票管理新增发票
 */
export function postFleetSettlementRepairInvoicesManagementAddInvoiceApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/settlement/repair/invoice/management/addInvoice',
    method: 'POST',
    data,
  })
}
/**
 * 维修结算-账单开票金额详情
 */
export function getFleetSettlementRepairInvoicesManagementByIdApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/settlement/repair/management/${data.id}`,
    method: 'GET',
    data,
  })
}
/**
 * 外协开票管理详情
 */
export function postOutsourcingFleetSettlementCustomerInvoicesManagementAllApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/order/carrier/cost/openInvoice/${data.id}`,
    method: 'GET',
    data,
  })
}
/**
 * 外协开票管理解绑发票
 */
export function postOutsourcingFleetSettlementCustomerInvoicesManagementUnBindApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/order/carrier/cost/invoice/unBound/${data.id}`,
    method: 'POST',
    data,
  })
}
/**
 * 外协开票管理绑定发票
 */
export function postOutsourcingFleetSettlementCustomerInvoicesManagementBindApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/order/carrier/cost/invoice/bound/${data.id}`,
    method: 'POST',
    data,
  })
}
/**
 * 外协开票管理新增发票
 */
export function postOutsourcingFleetSettlementCustomerInvoicesManagementAddInvoiceApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/order/carrier/cost/invoice`,
    method: 'POST',
    data,
  })
}
/**
 * 客户结算-开票管理更新已开票金额
 */
export function getFleetSettlementCustomerInvoicesManagementByIdApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/settlement/customer/open/invoices/management/${data.id}`,
    method: 'GET',
    data,
  })
}
/**
 * 外协付款详情-开票详情
 */
export function getOutFleetOrderCarrierCostPaymentDetailApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/order/carrier/cost/payment/${data.id}`,
    method: 'GET',
  })
}
/**
 * 付款管理-补贴付款详情
 */
export function getOutFleetOrderCarrierCostPaymentSubsidyDetailApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/outfleet/payment/subsidy/${data.id}`,
    method: 'GET',
  })
}
/**
 * 外协付款详情-新增打款
 */
export function postOutFleetOrderCarrierCostPaymentPartApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/order/carrier/cost/payment/part`,
    method: 'POST',
    data,
  })
}
/**
 * 付款管理-补贴付款
 */
export function postSubsidyPaymentApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/outfleet/payment/subsidyRecord`,
    method: 'POST',
    data,
  })
}
/**
 * 外协付款详情-发票列表
 */
export function getOutFleetOrderCarrierCostPaymentInvoiceListApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/order/carrier/cost/payment/invoiceList/${data.id}`,
    method: 'GET',
  })
}
/**
 * 外协付款详情-查询付款明细
 */
export function postOutFleetOrderCarrierCostPaymentPartAllApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/order/carrier/cost/payment/part/all`,
    method: 'get',
    params,
  })
}
/**
 * 获取事故列表
 */
export function getOutFleetVehicleAccidentPageApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/outFleetVehicleAccident/page`,
    method: 'get',
    params,
  })
}
/**
 * 获取客户扣款列表
 */
export function getFleetSettlementCustomerDeductionPageApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/settlement/customer/deduction/unbind/page`,
    method: 'get',
    params,
  })
}
// /**
//  * 获取客户补贴列表
//  */
// export function getFleetSettlementCustomerSubsidyPageApi(params: any): AxiosPromise<ResponseResult> {
//   return request({
//     url: `logistics/api/out/fleet/settlement/customer/deduction`,
//     method: 'get',
//     params,
//   })
// }
/**
 * 分子公司下拉
 */
export function getFleetCompanyDepartmentSelectOptionApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/company/department/select/option`,
    method: 'get',
    params,
  })
}
/**
 * 借支明细下拉
 */
export function getFleetOrderCarrierCostSummaryCashLoanSelectOptionApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/order/carrier/cost/summary/cash/loan/select/option`,
    method: 'get',
    params,
  })
}
/**
 * 获取对账期和收款期下拉框
 */
export function getFleetSettlementCustomerReconciliationManagementOptionsReconciliationDateApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: `logistics/api/out/fleet/settlement/customer/reconciliation/management/options/reconciliationDate/${params.customerId}`,
    method: 'get',
    params,
  })
}

/**
 * 明细修改费用
 */
export function expenseForModificationApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/order/settlementDetailView/updateDetailSubsidy`,
    method: 'post',
    data,
  })
}

/**
 * 应付油费明细接口 / 基地油明细接口 / 油费借支明细接口
 */

export function shouldPaymentOilFeeDetailApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/order/settlement/shouldPaymentOilFeeDetail',
    method: 'post',
    data,
  })
}

export function baseOilFeeDetailApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/order/settlement/baseOilFeeDetail',
    method: 'post',
    data,
  })
}

export function loanOilFeeDetailApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/order/settlement/loanOilFeeDetail',
    method: 'post',
    data,
  })
}

// 获取编辑月份数据
export function getMonthApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/business/vehicle/mileage/getEditData',
    method: 'post',
    params,
  })
}
// 保存编辑月份
export function saveEditDataApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/business/vehicle/mileage/saveEditData',
    method: 'post',
    data,
  })
}
// 新增结算账单
export function outFleetSettlementCarrierDeductionApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/outfleet.settlement.carrier/outFleetSettlementCarrierDeduction/carrier/deduction/select/option',
    method: 'get',
    params,
  })
}
// 新增油费借支
export function postOutFleetLoanOilFeeAddApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/loan/oil/fee/manual/add',
    method: 'post',
    data,
  })
}
// 编辑油费借支
export function postOutFleetLoanOilFeeEditApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/loan/oil/fee/manual/edit',
    method: 'post',
    data,
  })
}
// 新增借支，选择调度单
export function getCommonOptionBusinessSelectApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/common/option/business/select',
    method: 'get',
    params,
  })
}
// 获取油费借支详情
export function getOutFleetLoanOilFeeDetailApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/loan/oil/fee/detail/${params.id}`,
    method: 'get',
  })
}
// 新增路桥费借支
export function postOutFleetLoanEtcFeeAddApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/settlement/loan/etc/fee/manual/add',
    method: 'post',
    data,
  })
}
// 编辑路桥费借支
export function postOutFleetLoanEtcFeeEditApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/settlement/loan/etc/fee/manual/edit',
    method: 'post',
    data,
  })
}
// 获取路桥费借支详情
export function getOutFleetLoanEtcFeeDetailApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/settlement/loan/etc/fee/detail/${params.id}`,
    method: 'get',
  })
}
// 油费补贴明细
export function postOilFeeSubsidyDetailApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/order/settlement/oilFeeSubsidyDetail`,
    method: 'post',
    data,
  })
}
