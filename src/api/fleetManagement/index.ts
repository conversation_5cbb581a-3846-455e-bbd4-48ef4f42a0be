/*
 * @Author: llm
 * @Date: 2025-02-12 17:32:29
 * @LastEditors: llm
 * @LastEditTime: 2025-02-21 16:28:01
 * @Description:
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'

/**
 * 获取维修类别下拉
 */
export function getMaintenanceTypeSelectApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/maintenance/submission/maintenanceType/select`,
    method: 'get',
    params,
  })
}

/**
 * 获取维修车辆下拉
 */
export function selectvehicleOption(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/insurance/select/vehicleOption`,
    method: 'get',
    params,
  })
}
/**
 * 获取维修基地下拉
 */
export function getDepartmentTreeOptionApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/company/department/tree/option`,
    method: 'get',
    params,
  })
}
/**
 * 获取维修单位下拉
 */
export function getSupplierSelectOptionApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/supplier/repair/supplier/select/option`,
    method: 'get',
    params,
  })
}
/**
 * 获取维修车辆下拉
 */
export function getVehicleSelectOptionApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/common/option/business/select`,
    method: 'get',
    params,
  })
}
/**
 * 获取维修项目下拉
 */
export function getRepairProjectSelectOptionApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/common/option/business/select`,
    method: 'get',
    params,
  })
}
/**
 * 获取零件类别下拉
 */
export function getPartsUseSelectOptionApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/maintenance/submission/partsUse/select`,
    method: 'get',
    params,
  })
}
/**
 * 维修项目费用预估项
 */
export function postProjectCostApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/maintenance/submission/project/cost`,
    method: 'post',
    data,
  })
}
/**
 * 新增车队维修项目
 */
export function postMaintenanceSubmissionSaveApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/maintenance/submission/save`,
    method: 'post',
    data,
  })
}
/**
 * 编辑车队维修项目
 */
export function putMaintenanceSubmissionEditApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/maintenance/submission/edit`,
    method: 'put',
    data,
  })
}
/**
 * 车队维修编辑详情
 */
export function getMaintenanceSubmissionEditDetailApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/maintenance/submission/edit/detail`,
    method: 'get',
    params,
  })
}
/**
 * 车队维修调整费用
 */
export function postAdjustCostChangeApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/maintenance/submission/management/adjust/cost/change`,
    method: 'post',
    data,
  })
}
/**
 * 车队维修调整费用提交
 */
export function postAdjustCostChangeSubmitApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/maintenance/submission/management/adjust/cost`,
    method: 'post',
    data,
  })
}
