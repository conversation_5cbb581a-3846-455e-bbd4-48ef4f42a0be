/*
 * @Author: llm
 * @Date: 2025-02-13 10:58:54
 * @LastEditors: llm
 * @LastEditTime: 2025-04-02 12:15:36
 * @Description:
 */
export interface FormDataVO {
  id?: string
  adjustCost?: number //调整费用
  removeItemIds: string[] //删除的维修项目列表
  sourceType: string //车队维修 这个值默认是"车队"
  supplierName: string //供应商名称
  supplierId: string //供应商id
  baseName: string ///部门
  baseId: string //维修基地
  itemList: ItemListVO[]
  vehicleNo?: string //维修车辆
  vehicleMileage: number //里程
  entrustUser: string //委托人
  contact: string //联系电话
  remark?: string //备注
  totalEstimatedCost: string //预估费用
  totalCashPayment: string //现金付款
  totalTransferWithoutTaxPayment: string //转账未税付款
}
export interface ItemListVO {
  itemId?: string //维修项目行id
  maintenanceCount: number //维修次数
  projectId: string //项目id
  projectName: string //维修项目
  packageNumber: number //件数，
  maintenanceType: string //维修类别
  partsUse: string //零件类别
  partPrice: string //零件单价
  laborPrice: string //人工单价
  estimatedCost: string //维修费用
  cashPayment: string //现金付款
  transferWithoutTaxPayment: string //转账未税
  historyMaintenanceCost: string //历史费用
  projectItemId: string //维修项目行id
  maintenanceDate: string //维修日期
  vehicleMileage: number //里程
}
