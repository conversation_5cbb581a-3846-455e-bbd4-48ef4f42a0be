/*
 * @Author: llm
 * @Date: 2024-06-15 09:46:12
 * @LastEditors: llm
 * @LastEditTime: 2024-06-15 11:23:08
 * @Description:会议中心
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
/**
 * 会议纪要
 */
export function postMeetingDraftSave(data: Object, url: string): AxiosPromise<ResponseResult> {
  return request({
    url,
    method: 'post',
    data,
  })
}
/**
 * 会议纪要详情
 */
export function getMeetingDraftDetail(data: Object, url: string): AxiosPromise<ResponseResult> {
  return request({
    url,
    method: 'get',
    data,
  })
}
/**
 * 会议纪要临时保存
 */
export function postMeetingDraftRecord(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/meeting/draft/record',
    method: 'post',
    data,
  })
}
