/*
 * @Author: llm
 * @Date: 2022-08-27 10:44:00
 * @LastEditors: llm
 * @LastEditTime: 2023-06-15 16:02:12
 * @FilePath: /haodaoda-tms/src/api/multiRoute/multiRoute.js
 * @Description:
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
// 批量路线规划
import request from '@/utils/request'

// 算路列表
export function getBatchRoadCompute(data, params) {
    return request({
        url: 'api/hdd/backend/batchRoadCompute/page?' + 'page=' + params.page + '&limit=' + params.limit,
        method: 'GET',
        data
    })
}

// 批量算路上传模版文件接口
export function uploadTemplateFile(data) {
    return request({
        ContentType: 'multipart/form-data',
        url: 'api/hdd/backend/batchRoadCompute/import',
        method: 'POST',
        data
    })
}
// 批量算路下载模版文件接口
export function downloadTemplateFile(data) {
    return request({
        url: 'api/hdd/backend/batchRoadCompute/template',
        method: 'GET',
        responseType: 'blob',
        data
    })
}
// 启动批量算路
export function startUpTemplate(data, id) {
    return request({
        url: 'api/hdd/backend/batchRoadCompute/execute/' + id,
        method: 'POST',
        data
    })
}
// 获取批量算路详情列表数据
export function getBatchDetailList(data, id) {
    return request({
        url: 'api/hdd/backend/batchRoadCompute/' + id,
        method: 'GET',
        data
    })
}
// 删除已导入任务
export function deleteTemplate(data, id) {
    return request({
        url: 'api/hdd/backend/batchRoadCompute/' + id,
        method: 'DELETE',
        data
    })
}
// 删除单条业务线
export function deleteDetail(data, id) {
    return request({
        url: 'api/hdd/backend/batchRoadCompute/detail/' + id,
        method: 'DELETE',
        data
    })
}
// 修改单条业务线
export function editDetail(data, id) {
    return request({
        url: 'api/hdd/backend/batchRoadCompute/detail/' + id,
        method: 'PUT',
        data
    })
}
// 批量算路下载已完成算路的任务列表
export function downloadCompleteFile(data, id) {
    return request({
        url: 'api/hdd/backend/batchRoadCompute/download?id=' + id,
        method: 'GET',
        responseType: 'blob',
        data
    })
}
// 根据业务线标识，查询路线列表数据
export function getResultDetailList(data, id) {
    return request({
        url: 'api/hdd/backend/batchRoadCompute/detail/result/' + id,
        method: 'GET',
        data
    })
}


// 优化路线报价
// 下载模版文件接口
export function carCarrierTemplateFile(data) {
    return request({
        url: 'api/hdd/backend/carCarrier/template',
        method: 'GET',
        responseType: 'blob',
        data
    })
}

// 导入模版文件
export function carCarrierImportFile(data) {
    return request({
        ContentType: 'multipart/form-data',
        url: 'api/hdd/backend/carCarrier/import',
        method: 'POST',
        data
    })
}

// 优化路线列表
export function carCarriererPageList(params) {
    return request({
        url: 'api/hdd/backend/carCarrier/page',
        method: 'GET',
        params
    })
}

// 创建路线保存
export function carCarriererSave(data) {
    return request({
        url: 'api/hdd/backend/carCarrier/single/route',
        method: 'POST',
        data
    })
}

// 编辑路线保存
export function carCarriererUpdateDetail(data, id) {
    return request({
        url: '/api/hdd/backend/carCarrier/update/detail/' + id,
        method: 'PUT',
        data
    })
}

// 详情列表路线
export function carCarriererDetail(data, id) {
    return request({
        url: 'api/hdd/backend/carCarrier/detail/' + id,
        method: 'GET',
        data
    })
}

// 启动批量算路
export function carCarrierExecute(data, id) {
    return request({
        url: 'api/hdd/backend/carCarrier/execute/' + id,
        method: 'POST',
        data
    })
}

// 算路完成后列表路线
export function carCarriererResult(data,id) {
    return request({
        url: 'api/hdd/backend/carCarrier/detail/result',
        method: 'POST',
        data
    })
}

// 下载已算路完成后的文件
export function carCarrierDownloadFile(params) {
    return request({
        url: 'api/hdd/backend/carCarrier/download',
        method: 'GET',
        responseType: 'blob',
        params
    })
}

// 列表删除
export function carCarrierDeleteCompute(data, id) {
    return request({
        url: 'api/hdd/backend/carCarrier/delete/compute/' + id,
        method: 'DELETE',
        data
    })
}
// 详情列表删除
export function carCarrierDeleteDetail(data, id) {
    return request({
        url: 'api/hdd/backend/carCarrier/delete/detail/' + id,
        method: 'DELETE',
        data
    })
}
