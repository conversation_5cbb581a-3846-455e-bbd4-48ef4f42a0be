/*
 * @Author: llm
 * @Date: 2025-02-26 18:13:03
 * @LastEditors: llm
 * @LastEditTime: 2025-04-07 11:57:34
 * @Description:
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'

/**
 * 获取系统默认模板字段
 * @param data 数据
 * @returns
 */
export function getSystemDefaultTemplateField(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/custom/template/field/system/default',
    method: 'get',
    data,
  })
}
/**
 * 新增模版
 * @param data 数据
 * @returns
 */
export function postAddTemplate(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/custom/template/add',
    method: 'post',
    data,
  })
}
/**
 * 获取模版列表
 * @param data 数据
 * @returns
 */
export function getTemplatePageApi(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/custom/template/page',
    method: 'get',
    data,
  })
}
/**
 * 订单自定义模版筛选
 * @param data 数据
 * @returns
 */
export function getFleetOrderCustomTemplateOptionApi(data: Object, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}`,
    method: 'get',
    data,
  })
}

/**
 * 获取模版详情
 * @param data 数据
 * @returns
 */
export function getTemplateDetailApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/custom/template/detail',
    method: 'get',
    params,
  })
}
/**
 * 编辑详情
 * @param data 数据
 * @returns
 */
export function putUpdateTemplate(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/custom/template/edit',
    method: 'put',
    data,
  })
}
/**
 * 删除模版
 * @param id 模版id
 * @returns
 */
export function deleteTemplateApi(id: string): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/custom/template/' + id,
    method: 'delete',
  })
}
/**
 * 下载模版
 * @param uri 请求地址前缀(uri的)
 * @returns
 */
export function downloadCustomTemplate(uri: string, params?: any) {
  return request({
    url: `${uri}`,
    method: 'get',
    responseType: 'arraybuffer',
    params,
  })
}
