/*
 * @Author: llm
 * @Date: 2025-02-26 18:13:03
 * @LastEditors: llm
 * @LastEditTime: 2025-06-24 16:27:00
 * @Description:
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
import { FleetOrderVO } from './types'

/**
 * 解绑轮胎
 * @param data 数据
 * @returns
 */
export function postOutFleetUnBindTire(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/outfleet/fleet/tire/unBind',
    method: 'post',
    data,
  })
}
/**
 * 解绑并报废
 * @param data 数据
 * @returns
 */
export function postOutFleetUnBindAndScrap(data: Object): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/outfleet/fleet/tire/unBindAndScrap',
    method: 'post',
    data,
  })
}
/**
 * 所属基地筛选
 * @param params 数据
 * @returns
 */
export function getDepartmentTreeOptionApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/company/department/tree/option',
    method: 'get',
    params,
  })
}
/**
 * 客户名称筛选
 * @param params 数据
 * @returns
 */
export function getFleetOutFleetBusinessCustomerSelectOptionApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/common/option/business/select',
    method: 'get',
    params,
  })
}
/**
 * 起点，终点，提车点，交车点 模糊
 * @param params 数据
 * @returns
 */
export function getCommonOptionBusinessSelectApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/common/option/business/select',
    method: 'get',
    params,
  })
}
/**
 * 订单类型
 * @param params 数据
 * @returns
 */
export function getCommonOptionDistributeSelectApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/common/option/distribute/select',
    method: 'get',
    params,
  })
}
/**
 * 根据用户查询所属基地
 * @param params 数据
 * @returns
 */
export function getFleetOrderAddCheckApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/order/add/check',
    method: 'get',
    params,
  })
}
/**
 * 交车点查询交车地址
 * @param data 数据
 * @returns
 */
export function getFleetOrderDropUpCheckApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/order/dropUp/check',
    method: 'get',
    params,
  })
}
/**
 * 提车地查询提车地址
 * @param data 数据
 * @returns
 */
export function getFleetOrderPickUpCheckApi(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/order/pickUp/check',
    method: 'get',
    params,
  })
}
/**
 * 订单查看详情
 * @returns
 */
export function getFleetOrderDetailApi(params: { id: string }): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/order/${params.id}`,
    method: 'get',
  })
}
/**
 * 订单编辑
 * @returns
 */
export function putFleetOrderDetailApi(data: { id: string }): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/order/${data.id}`,
    method: 'put',
    data,
  })
}
/**
 * 订单新增
 * @returns
 */
export function postFleetOrderApi(data: FleetOrderVO): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/out/fleet/order`,
    method: 'post',
    data,
  })
}
/**
 * 订单类型下拉
 * @param uri 请求地址前缀(uri的)
 * @returns
 */
export function getOutFleetBusinessTypeEntitySelectOptionApi() {
  return request({
    url: `logistics/api/outfleet.business/outFleetBusinessTypeEntity/select/option/name`,
    method: 'get',
  })
}
/**
 * 获取调度单详情
 * @param uri 请求地址前缀(uri的)
 * @returns
 */
export function getOutFleetOrderDispatchOptionDispatchInfoApi(params: { dispatchNo: string }) {
  return request({
    url: `logistics/api/out/fleet/order/dispatch/option/dispatchInfo`,
    method: 'get',
    params,
  })
}
/**
 * 维修单新增
 * @param uri 请求地址前缀(uri的)
 * @returns
 */
export function postOutFleetOrderRepairApi(data: any) {
  return request({
    url: `logistics/api/out/fleet/repair`,
    method: 'post',
    data,
  })
}
/**
 * 维修单编辑
 * @param data 数据
 * @returns
 */
export function putOutFleetOrderRepairApi(data: any) {
  return request({
    url: `logistics/api/out/fleet/repair/updateRepairItems`,
    method: 'post',
    data,
  })
}
