/*
 * @Author: llm
 * @Date: 2025-03-19 15:43:52
 * @LastEditors: llm
 * @LastEditTime: 2025-05-12 12:22:25
 * @Description:
 */
export interface FleetOrderVO {
  id?: string
  childCompanyId: string
  customerId: string
  orderType: string
  orderNo: string
  orderIssueDatetime: string
  vin: string
  twicePlan: number
  expectedLoadTime: string
  brandName: string
  modelName: string
  predictDeliveryDatetime: string
  startFenceId: string
  endFenceId: string
  customerOrderNo: string
  dealerName: string
  dealerAddress: string
  pickUpPointId: string
  pickUpPointAddress: string
  dropUpPointId: string
  dropUpPointAddress: string
  paymentMethod: string
  amountReceivable: number
  collectUserName: string
  haveDeliverySlip: number
  whetherDriverCollect: number
  planRefundDate: string
  remark: string
  businessType: string
  downPaymentSeparateSettlement: string
  downPaymentAmount: number
  importType?: string
}
