/*
 * @Author: llm
 * @Date: 2024-06-20 11:22:46
 * @LastEditors: llm
 * @LastEditTime: 2025-01-25 14:11:49
 * @Description:
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
import { RoutePlanRoutingVO, manyRoutePlanRoutingVO } from './types'

/**
 * 按路线规划查询
 * @returns
 */
export function postRoutePlanRoutingApi(data: RoutePlanRoutingVO): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/route/routing',
    method: 'post',
    data,
  })
}
/**
 * 按轨迹查询
 * @returns
 */
export function postRoutePlanVehicleTrackApi(data: RoutePlanRoutingVO): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/routePlan/vehicle/track',
    method: 'post',
    data,
  })
}
/**
 * 生成路线
 * @returns
 */
export function postRouteLibraryManageGenerateApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/route/library/manage/generate',
    method: 'post',
    data,
  })
}

/**
 * 多路线规划
 * @returns
 */
export function postMultiRoutePlanningApi(data: manyRoutePlanRoutingVO): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/routePlan/multi/routing',
    method: 'post',
    data,
  })
}
