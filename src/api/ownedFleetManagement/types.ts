/*
 * @Author: llm
 * @Date: 2024-06-20 11:22:52
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-06-28 12:06:21
 * @Description:
 */
/**
 * 轿运路线规划服务
 */
export interface RoutePlanRoutingVO {
  fromAddress: string
  fromCity: string
  fromCityCode: string
  fromCoordinate: string
  toAddress: string
  toCity: string
  toCityCode: string
  toCoordinate: string
  startRadius: number
  endRadius: number
  vehicleNo?: string
  startTime?: string
  endTime?: string
}

/**
 * 多点路线规划服务
 */
export interface manyRoutePlanRoutingVO {
  fromAddress: string
  fromCoordinate: string
  toAddress: string
  toCoordinate: string
  startRadius: number
  endRadius: number
  storePoints?: any[]
}
