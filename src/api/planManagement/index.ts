/*
 * @Author: llm
 * @Date: 2023-07-13 09:54:24
 * @LastEditors: llm
 * @LastEditTime: 2025-05-12 12:00:50
 * @Description: 计划管理api
 *
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
import {
  BatchEnterFormVO,
  ShipmentOrderConfirmFormVO,
  VinVO,
  CarrierVehiclesVO,
  CarrierDriverVO,
  CarrierWarehouseVO,
  SearchVinParamsVO,
  ExchangeVehicleParamsVo,
  CommonAreaFormData,
  SpecifyQuantityVO,
  OrderPlanSubmitReleaseVO,
  OrderPlanMergeOrderVO,
  CarrierVehicleSelectVO,
} from './type'

/**
 * 计划下发
 * @param data ids
 * @param uri 请求地址前缀
 */
export function submitPlanApi(data: Object, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}/distribute`,
    method: 'post',
    data,
  })
}
/**
 * 查看当前行所有vin
 * @param {string} data ids
 * @param {string} uri 请求地址前缀
 */
export function viewAllVins(data: Object, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}/submit/preview?planOrderIds=${data}`,
    method: 'get',
  })
}
/**
 * 倒板申请-查看当前行所有vin
 * @param {string} data ids
 * @param {string} uri 请求地址前缀
 */
export function viewFlipBoardApplyAllVins(params: Object, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}/vin`,
    method: 'get',
    params,
  })
}
/**
 * 自动分配预览
 * @param {string} data ids
 * @param {string} uri 请求地址前缀
 */
export function planSubmitPreApi(params: Object, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}/submit/preview/detail`,
    method: 'get',
    params,
  })
}
/**
 * 手动分配下发
 * @param {string} data 分配后的数据
 * @param {string} uri 请求地址前缀
 */
export function planSubmitSpecifyApi(data: Object, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}/submit/specify`,
    method: 'post',
    data,
  })
}
/**
 * 车道下拉
 * @param {string} relationId
 */
export function factoryLaneSelect(relationId: string): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/company/customer/factory/lane/select/option?relationId=' + relationId,
    method: 'get',
  })
}
/**
 * 承运商司机下拉
 * @param {string} carrierId 承运商id
 */
export function carrierDriverSelect(carrierId: string): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/company/carrier/driver/select/option?relationId=' + carrierId,
    method: 'get',
  })
}
/**
 * 承运商车辆下拉
 * @param {string} carrierId 承运商id
 */
export function carrierVehicleSelect(carrierId: string): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/company/carrier/vehicle/select/option?relationId=' + carrierId,
    method: 'get',
  })
}
/**
 * 承运商车辆下拉
 * @param {string} carrierId 承运商id
 */
export function carrierVehicleSimpleSelect(params: CarrierVehicleSelectVO): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/company/shipment/order/confirm/vehicleOptions',
    method: 'get',
    params,
  })
}
/**
 * 车辆任务下拉
 * @param {string} vehicleId 车辆id
 */
export function vehicleTaskSelect(vehicleId: string): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/order/shipment/lane/appointment/3/task/select/option?vehicleId=' + vehicleId,
    method: 'get',
  })
}
/**
 * 承运商仓库下拉
 * @param {string} carrierId 承运商id
 */
export function carrierWarehouseSelect(carrierId: string): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/company/carrier/warehouse/select/option?relationId=' + carrierId,
    method: 'get',
  })
}
/**
 * 承运商申请
 * @param {string} data
 * @param {string} uri
 */
export function carrierSubmitApply(data: BatchEnterFormVO, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}`,
    method: 'post',
    data,
  })
}
/**
 * 总包确认
 * @param {string} data
 * @param {string} uri
 */
export function shipmentOrderConfirm(data: ShipmentOrderConfirmFormVO, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}/confirm`,
    method: 'post',
    data,
  })
}
/**
 * 超位记录详情
 * @param {string} params
 * @param {string} uri
 */
export function extVin(params: VinVO, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}/vin`,
    method: 'get',
    params,
  })
}
/**
 * 运单调度中vin详情
 * @param {string} params
 * @param {string} uri
 */
export function extVinHistory(params: VinVO, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}/vin/history`,
    method: 'get',
    params,
  })
}
/**
 * vin中转详情
 * @param {string} params
 * @param {string} uri
 */
export function vinRelationViewPage(params: Object): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/shipment/vin/relation/view/page`,
    method: 'get',
    params,
  })
}
/**
 * 确认申请
 * @param {string} params
 * @param {string} uri
 */
export function confirmApplyApi(params: VinVO, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}`,
    method: 'get',
    params,
  })
}
/**
 * 查看换车申请信息
 * @param {string} params
 * @param {string} uri
 */
export function viewApplyApi(params: Object, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}`,
    method: 'get',
    params,
  })
}
/**
 * 承运商车辆列表（模糊搜索）
 * @param {string} params
 */
export function carrierVehiclesApi(params: CarrierVehiclesVO): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/vehicle/carrier',
    method: 'get',
    params,
  })
}
/**
 * 承运商司机列表（模糊搜索）
 * @param {string} params
 */
export function carrierDriversApi(params: CarrierDriverVO): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/driver/carrier',
    method: 'get',
    params,
  })
}
/**
 * 承运商仓库列表（模糊搜索）
 * @param {string} params
 */
export function carrierWarehouseApi(params: CarrierWarehouseVO): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/carrier/warehouse/carrier',
    method: 'get',
    params,
  })
}
/**
 * 获取板车上所有vin
 * @param {string} params
 */

export function vehicleVinList(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/shipment/schedule/view/vehicle/vin/list',
    method: 'get',
    params,
  })
}
/**
 * 倒板
 */
export function invertedDistribution(data: ExchangeVehicleParamsVo): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/shipment/schedule/view/inverted/distribution',
    method: 'post',
    data,
  })
}
/**
 * 换车
 */
export function transferVehicle(data: ExchangeVehicleParamsVo): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/shipment/schedule/view/transfer/vehicle',
    method: 'post',
    data,
  })
}
/**
 * 调度从公海调度分配
 */
export function commonAreaDistribution(data: CommonAreaFormData): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/shipment/schedule/view/common/area/distribution',
    method: 'post',
    data,
  })
}
/**
 * 手动设置数量提交
 */
export function submitSpecifyQuantity(data: SpecifyQuantityVO): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/order/plan/submit/specify/quantity',
    method: 'post',
    data,
  })
}
/**
 * 手动设置VIN提交
 */
export function submitSpecifyVin(data: SpecifyQuantityVO): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/order/plan/submit/specify/vin',
    method: 'post',
    data,
  })
}
/**
 * 关闭手动分配锁
 */
export function orderPlanSubmitRelease(params: OrderPlanSubmitReleaseVO): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/order/plan/submit/release',
    method: 'get',
    params,
  })
}
/**
 * 手动合并
 */
export function orderPlanMergeOrder(data: OrderPlanMergeOrderVO): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/order/plan/merge/order',
    method: 'post',
    data,
  })
}
/**
 * 取消配板申请
 */
export function cancelBoardApply(id: string): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/board/apply/cancel/' + id,
    method: 'get',
  })
}

/**
 * 公共请求
 */
export function globalRequestApi(data: any, method: string, url: string) {
  return request({
    url,
    method,
    data,
  })
}
/**
 * 公共请求(参数在地址栏上)
 */
export function globalRequestUrlApi(params: any, method: string = 'get', url: string) {
  return request({
    url,
    method,
    params,
  })
}
/**
 * 取消配板申请
 */
export function getPictureConfig(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/take/turns/apply/picture/config',
    method: 'get',
    params,
  })
}
/**
 * 公共上传图片
 */
export function globalUpload(data: any): AxiosPromise<any> {
  return request({
    url: 'tms/system/image/upload',
    method: 'post',
    data,
  })
}
/**
 *取消配板申请
 */
export function boardApplyCancel(id: string): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/board/apply/cancel/' + id,
    method: 'get',
  })
}
/**
 *获取菜单上的数字
 */
export function getMenuCount(): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/config/outFleet/timeoutRemindConfig/showTimeoutMessageCount',
    method: 'get',
  })
}
/**
 *重新分配
 */
export function reAllocation(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/order/plan/submit/planId',
    method: 'get',
    params,
  })
}
/**
 *一键换车 根据车牌号获取绑定的所有运单号
 */
export function getShipmentNoByVehicleId(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/shipment/schedule/view/shipmentNo/list',
    method: 'get',
    params,
  })
}
/**
 * 更新对账单
 */
export function updateCarrierStatement(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/finance/downstream/detail/carrier/statement/update',
    method: 'post',
    data,
  })
}
/**
 * 获取行政区
 */
export function getDistrictSelectOptionApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/system/district/select/option',
    method: 'get',
    data,
  })
}
/**
 * 获取雷达图扫描的车辆数据
 */
export function getVehicleRadarApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/intransit/vehicle/radar',
    method: 'post',
    data,
  })
}
/**
 * 获取雷达范围
 */
export function getVehicleRadarRangeApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/intransit/vehicle/radar/range',
    method: 'post',
    data,
  })
}
/**
 * 获取雷达范围内车辆（新）
 */
export function getVehicleRadarScanApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/intransit/vehicle/radar/scan',
    method: 'post',
    data,
  })
}
/**
 * 替换承运商
 */
export function replaceCarrierApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/shipment/schedule/view/carrier/replace',
    method: 'post',
    data,
  })
}
