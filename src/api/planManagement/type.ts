/*
 * @Author: llm
 * @Date: 2023-07-13 09:54:36
 * @LastEditors: llm
 * @LastEditTime: 2024-03-21 10:38:42
 * @Description: 计划管理
 *
 */
/**
 * 批量录入表单form
 */
export interface BatchEnterFormVO {
  /**
   * 承运商仓库
   */
  carrierWareInfo?: string
  /**
   * 司机id
   */
  driverId?: string
  /**
   * 期望装车日期
   */
  expectDate?: string
  /**
   * 期望装车时间
   */
  expectTime?: string
  /**
   * 是否倒板
   */
  invertPlate?: boolean
  /**
   * 订单id
   */
  orderIds?: string[]
  /**
   * 板车号
   */
  vehicleInfo?: string
}
/**
 * 总包确认表单
 */
export interface ShipmentOrderConfirmFormVO {
  /**
   * 确认日期
   */
  confirmDate?: string
  /**
   * 确认时间
   */
  confirmTime?: string
  /**
   * 车道信息
   */
  laneInfo?: string
  /**
   * 订单id
   */
  orderIds?: string[]
}
/**
 * 超位记录详情
 */
export interface VinVO {
  /**
   * 行id
   */
  vinGroupId?: string
  /**
   * 行id
   */
  currentNo?: string
  /**
   * 行id
   */
  id?: string
}
/**
 * 承运商车辆参数
 */
export interface CarrierVehiclesVO extends PageQuery {
  /**
   * 车牌号
   */
  vehicleNo: string
}
/**
 * 承运商司机参数
 */
export interface CarrierDriverVO {
  /**
   * 司机姓名
   */
  name: string
}
/**
 * 承运商仓库参数
 */
export interface CarrierWarehouseVO {
  /**
   * 司机姓名
   */
  name: string
}
/**
 * 获取板车上所有vin参数
 */
export interface SearchVinParamsVO {
  /**
   * 车辆id
   */
  vehicleId: string
}
/**
 * 倒板、换车参数
 */
export interface ExchangeVehicleParamsVo {
  body?: ExchangeVehicleParamsBodyVo[]
  /**
   * 操作类型1-倒板 2-换车
   */
  operationType?: number
  /**
   * 申请来源：0-调度 1-司机
   */
  source?: number
  /**
   * 原车辆、司机信息
   */
  origin?: {
    vehicleId?: string
    driverId?: string
    idList?: string[]
  }
  /**
   * 运单号
   */
  shipmentNo?: string
}
/**
 * 替换承运商
 */
export interface ReplaceCarrierParamsVO {
  idList?: string[]
  vehicleId?: string
  carrierId?: string
  shipmentNo?: string
  replaceVehicleId?: string
  replaceDriverId?: string
  replaceCarrierId?: string
}
export interface ExchangeVehicleParamsBodyVo {
  /**
   * 位置类型typeOption，提交时删掉 1自选 2仓库
   */
  addressType?: number
  /**
   * 车辆下的所有vin
   */
  idList?: VinInfoVO[] | string[]
  /**
   * 车辆下的所有vin
   */
  vinItemList?: VinInfoVO[] | string[]
  /**
   * 选中的车辆id
   */
  vehicleId?: string
  /**
   * 选中的司机id
   */
  driverId?: string
  /**
   * 选中的仓库id
   */
  warehouseId?: string
  /**
   * 选中的仓库地点
   */
  warehousePlace?: string
  /**
   * 选中的仓库经纬度
   */
  coordinate?: string //经纬度
}

/**
 *vin数据
 */
export interface VinInfoVO {
  id: string
  /**
   * vin
   */
  vin: string
  /**
   * 提车时间
   */
  takeCarTime: string | null
  /**
   * 提车地
   */
  takeCarFactory: string | null
}
/**
 * 调度从公海调度分配 参数
 */
export interface CommonAreaFormData {
  idList?: VinInfoVO[]
  vehicleId?: string
  driverId?: string
  warehouseId?: string
  warehousePlace?: string
  coordinate?: string
  addressType?: number
}
/**
 * 手动设置数量提交
 */
export interface SpecifyQuantityVO {
  /**
   * 原承运商id
   */
  originalCarrierId?: string
  /**
   * 目标承运商id
   */
  targetCarrierId?: string
  /**
   * 计划id
   */
  orderPlanId?: string
  /**
   * 配板数
   */
  matchPlate?: number
  /**
   * 配板数
   */
  allocate?: number
  /**
   * 资源类型 1：配板 2：分配
   */
  sourceType?: number
  /**
   * 第一次传0 第二次不传 （返回的状态码是219）
   */
  realDistribution?: number | undefined
}
/**
 * 手动设置VIN提交
 */
export interface SpecifyVinVO {
  /**
   * 原承运商id
   */
  originalCarrierId?: string
  /**
   * 目标承运商id
   */
  targetCarrierId?: string
  /**
   * 计划id
   */
  orderPlanId?: string
  /**
   * vin列表
   */
  vinList?: string[]
  /**
   * 第一次传0 第二次不传 （返回的状态码是219）
   */
  realDistribution?: number | undefined
}
/**
 * 计划分派预览
 */
export interface PreviewDetailVO {
  /**
   * 线路下的承运商列表
   */
  carriers?: any[]
  /**
   * 客户id
   */
  customerId?: string
  /**
   * 客户线路id
   */
  customerLineId?: string
  /**
   * id
   */
  id?: string
  /**
   * 线路名称
   */
  lineName?: string
}
/**
 * 手动分配确认
 */
export interface OrderPlanSubmitReleaseVO {
  planOrderId: string
}
/**
 * 手动合并
 */
export interface OrderPlanMergeOrderVO {
  planOrderIds: string[]
}
/**
 * 计划分配-手动分配-列表搜索参数
 */
export interface PlanAllocationSearchVO {
  /**
   * Y号
   */
  volkswagenNo: string
  /**
   * 城市
   */
  targetCenterCity: string
}
/**
 * 一键确认 车辆下拉参数
 */
export interface CarrierVehicleSelectVO {
  simple: boolean
}
