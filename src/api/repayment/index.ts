/*
 * @Author: llm
 * @Date: 2024-06-29 17:10:22
 * @LastEditors: llm
 * @LastEditTime: 2024-06-29 17:10:22
 * @Description: 贷款还款api
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'

/**
 * 贷款select列表
 */
export function getselectOptionApi(): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/outfleet/fleet/vehicle/loan/info/select/option',
    method: 'get',
  })
}

/**
 * 根据id请求列表
 * id请求列表
 */
export function getOptionSelectApi(id:string): AxiosPromise<ResponseResult> {
    return request({
      url: `/logistics/api/outfleet/fleet/vehicle/loan/info/${id}`,
      method: 'get',
    })
  }

/**
 * 根据id请求列表
 * id请求列表
 */
export function PostOptionSelectApi(data:any): AxiosPromise<ResponseResult> {
    return request({
      url: `/logistics/api/outfleet/fleet/vehicle/repayment/info`,
      method: 'post',
      data,
    })
  }

/**
 * 根据dispatchNo请求路线对比
 */
export function getroutecompare(dispatchNo:string): AxiosPromise<ResponseResult> {
  return request({
    url: `/logistics/api/outfleet/order/dispatch/route/compare/${dispatchNo}`,
    method: 'get'
  })
}

  