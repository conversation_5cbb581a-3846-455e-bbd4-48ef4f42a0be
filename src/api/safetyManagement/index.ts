/*
 * @Author: llm
 * @Date: 2024-06-29 17:10:22
 * @LastEditors: llm
 * @LastEditTime: 2024-06-29 17:10:22
 * @Description: 安全管理api
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'

/**
 * 仓库列表
 */
export function getWarehouseBaseSelectOptionApi(): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/warehouse/base/select/option/v2',
    method: 'get',
  })
}

/**
 * 安全标准新增
 */
export function postSafetyStandardTemplateAddApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/safety/standard/template/add',
    method: 'post',
    data,
  })
}

/**
 * 安全标准详情
 */
export function getSafetyStandardTemplateViewDetailApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/safety/standard/template/view/detail',
    method: 'get',
    params,
  })
}

/**
 * 更新安全标准
 */
export function updateSafetyStandardTemplateApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/safety/standard/template/update',
    method: 'post',
    data,
  })
}
/**
 * 巡视路径详情
 */
export function getSafetyPatorlPathDetailApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/safety/patorl/path/detail',
    method: 'get',
    params,
  })
}
/**
 * 保存管理适用仓库
 */
export function postSafetyStandardTemplateSaveApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/safety/standard/template/save',
    method: 'post',
    data,
  })
}
