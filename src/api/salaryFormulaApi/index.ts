/*
 * @Author: llm
 * @Date: 2024-09-25 09:35:20
 * @LastEditors: llm
 * @LastEditTime: 2025-07-02 10:16:44
 * @Description:
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
import { ItemsParamsVO } from './type'

/**
 * 获取 固定工资 设置数据
 */
export function queryFixedSalaryDataApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/queryFixedSalarySetData',
    method: 'post',
    data,
  })
}
/**
 * 更新 固定收入 设置数据
 */
export function updateFixedSalaryDataApi(data: ItemsParamsVO): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/updateFixedSalaryData',
    method: 'post',
    data,
  })
}

/**
 * @description: 新建应用场景导出
 */
export function subsidyApplyTo(uri: string = 'logistics/api/out/fleet/subsidy/subsidyApplyTo/template') {
  return request({
    url: `${uri}`,
    method: 'get',
    responseType: 'arraybuffer',
  })
}

/**
 * @description: 新建补贴设制导出
 */
export function subsidyApplyTo1(uri: string = 'logistics/api/out/fleet/subsidy/level2Template') {
  return request({
    url: `${uri}`,
    method: 'get',
    responseType: 'arraybuffer',
  })
}

/**
 * @description: 新建应用场景导入
 */
export function subsidyApplyGo() {
  return request({
    url: `logistics/api/out/fleet/subsidy/subsidyApplyTo/import`,
    method: 'post',
    responseType: 'arraybuffer',
  })
}

/**
 * 获取 变动收入 设置数据
 */
export function queryChangeItemsDataApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/queryChangeItems',
    method: 'post',
    data,
  })
}
/**
 * 更新 变动收入 设置数据
 */
export function updateChangeItemDataApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/saveChangeItem',
    method: 'post',
    data,
  })
}
/**
 * 更新 变动收入 绑定计算关系
 */
export function bindSubsidyItemRelationApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/bindSubsidyItemRelation',
    method: 'post',
    data,
  })
}
/**
 * 删除 变动收入 绑定计算关系
 */
export function deleteItemApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/deleteItem',
    method: 'post',
    data,
  })
}
/**
 * 导出 变动收入 数据
 */
export function exportChangeItemApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/exportChangeItem',
    method: 'get',
    responseType: 'arraybuffer',
    params,
  })
}
/**
 * 获取二级补贴项目 列表
 */
export function getLevel2SubsidyItemsApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/queryLevel2SubsidyItems',
    method: 'get',
    params,
  })
}
/**
 * 获取 变动收入 计算基础数据
 */
export function baseInfoApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/baseInfo',
    method: 'get',
    params,
  })
}

/**
 * 获取 自动收入 数据
 */
export function autoCalculateBaseInfoApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/autoCalculateBaseInfo',
    method: 'get',
    params,
  })
}
/**
 * 编辑 自动收入 数据
 */
export function saveAutoCalculateItemApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/saveAutoCalculateItem',
    method: 'post',
    data,
  })
}

/**
 * 获取 补贴收入 数据
 */
export function subsidyApplyToPageApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/subsidyApplyTo/page',
    method: 'get',
    params,
  })
}
/**
 * 获取 起点终点选择下拉列表
 */
export function startEndSelectApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/common/option/business/select',
    method: 'get',
    params,
  })
}
/**
 * 获取 车牌号选择下拉列表
 */
export function vehicleNoSelectApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/common/option/business/select',
    method: 'get',
    params,
  })
}
/**
 *  补贴收入 - 新增、编辑
 */
export function saveSubsidyApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/saveSubsidyItem',
    method: 'post',
    data,
  })
}
/**
 *  应用场景 - 新增
 */
export function saveSubsidyApplyToApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/subsidyApplyTo',
    method: 'post',
    data,
  })
}
/**
 *  应用场景 - 编辑
 */
export function updateSubsidyApplyToApi(id: any, data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/subsidy/subsidyApplyTo/' + id,
    method: 'put',
    data,
  })
}

/**
 *  司机计算补贴 - 过去单个详细信息
 */
export function getDispatchSubsidyApi(id: any, params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/settlementDetailView/getDispatchLineItems/' + id,
    method: 'get',
    params,
  })
}

/**
 *  司机计算补贴 - 过去单个详细信息
 */
export function updateDispatchSubsidyApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/order/settlementDetailView/updateDispatchSubsidy',
    method: 'post',
    data,
  })
}

/**
 *  应用场景 - 模糊查询
 */
export function subsidyIncomeQueryApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/subsidy/subsidyApplyTo/page',
    method: 'get',
    params,
  })
}
/**
 *  补贴收入 - 模糊查询
 */
export function applicationScenariosApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/subsidy/queryLevel2SubsidyItems',
    method: 'get',
    params,
  })
}
/**
 *  数据统计列表
 */
export function postDataStatisticsListApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/subsidy/queryChangeItems?type=dataStatisticsItem',
    method: 'post',
  })
}
/**
 *  数据统计列表
 */
export function getDataStatisticsValuesApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/logistics/api/out/fleet/subsidy/getDataStatisticsValues',
    method: 'get',
    params,
  })
}
