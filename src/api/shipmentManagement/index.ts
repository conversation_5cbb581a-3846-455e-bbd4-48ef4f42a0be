/*
 * @Author: llm
 * @Date: 2023-07-13 09:54:24
 * @LastEditors: llm
 * @LastEditTime: 2025-03-17 18:27:08
 * @Description: 计划管理api
 *
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
import {
  AppointmentRequest,
  ConfirmFormDataVO,
  appointmentIdsRequest,
  appointmentInfoByVehicleRequestVO,
  appointmentVehicleShipmentInfoRequestVO,
  laneAppointmentSubmitRequestVO,
  laneAppointmentUpdateVinRequestVO,
  shipmentOrderConfirmSubmitVO,
  appointmentTaskListVO,
} from './type'

/**
 * 运单申请预约信息查询
 * @param data ids
 */
export function appointmentApi(data: AppointmentRequest): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/order/shipment/lane/appointment/appointmentInfo',
    method: 'post',
    data,
  })
}
/**
 * 运单申请预约信息查询
 * @param data ids
 */
export function appointmentIdsApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/order/shipment/lane/appointment/appointmentIds',
    method: 'post',
    data,
  })
}

/**
 * 运单申请预约信息查询
 * @param data ids
 * @param type 3公路
 * @param type 4铁水
 */
export function appointmentSubmitApi(data: ConfirmFormDataVO, type: string): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/company/order/shipment/lane/appointment/${type}/appointmentSubmit`,
    method: 'post',
    data,
  })
}

/**
 * 预约申请
 * @param params
 * @param type
 */
export function appointmentTaskListApi(data: appointmentTaskListVO, transportType: string | number): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/company/order/shipment/lane/appointment/${transportType}/taskList`,
    method: 'post',
    data,
  })
}

/**
 * 车道预约车辆运单信息查询
 * @param carrierType 固定值 公路3 铁水4
 */
export function appointmentVehicleShipmentInfoApi(params: appointmentVehicleShipmentInfoRequestVO): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/order/shipment/lane/appointment/appointmentVehicleShipmentInfo',
    method: 'get',
    params,
  })
}

/**
 * 车道预约车辆运单信息查询
 * @param vehicleId
 */
export function appointmentInfoByVehicleApi(params: appointmentInfoByVehicleRequestVO): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/order/shipment/lane/appointment/appointmentInfoByVehicle',
    method: 'get',
    params,
  })
}

/**
 * 车道预约增加-删除vin
 * @param vehicleId
 * @param vinUpdateType  1 删除 2 增加
 * @param vins
 */
export function laneAppointmentUpdateVinApi(data: laneAppointmentUpdateVinRequestVO): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/order/shipment/lane/appointment/laneAppointmentUpdateVin',
    method: 'post',
    data,
  })
}

/**
 * 车道预约提交
 * @param laneAppointmentType  3 公路 4 铁水
 */
export function laneAppointmentSubmitApi(data: laneAppointmentSubmitRequestVO, laneAppointmentType: string): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/company/order/shipment/lane/appointment/${laneAppointmentType}/laneAppointmentSubmit`,
    method: 'post',
    data,
  })
}

/**
 * 公路发运-批量录入
 */
export function shipmentOrderConfirmCheckApi(data: AppointmentRequest): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/shipment/order/confirm/check',
    method: 'post',
    data,
  })
}
/**
 * 公路发运-批量录入
 */
export function shipmentOrderConfirmSubmitApi(uri: string, data: shipmentOrderConfirmSubmitVO): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}/submit`,
    method: 'post',
    data,
  })
}

/**
 * 一键录入-申请车辆信息
 */
export function shipmentOrderApplyInfoByVehicleApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/company/shipment/order/apply/applyInfoByShipmentNo`,
    method: 'get',
    params,
  })
}

/**
 *
 */
export function refreshCheckGetApi(uri: string, params: any): any {
  return request({
    url: `${uri}`,
    method: 'get',
    params,
  })
}
/**
 * formData 请求
 */
export function postFormApi(uri: string, responseType?: string, data?: any): any {
  const formData = new FormData()
  Object.keys(data).forEach((key) => {
    formData.append(key, data[key])
  })
  if (responseType && responseType === 'blob') {
    return request({
      url: `${uri}`,
      method: 'post',
      responseType: 'blob',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      data: formData,
    })
  } else if (responseType && responseType === 'arraybuffer') {
    return request({
      url: `${uri}`,
      method: 'post',
      responseType: 'arraybuffer',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      data: formData,
    })
  } else {
    return request({
      url: `${uri}`,
      method: 'post',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      data: formData,
    })
  }
}

/**
 *
 */
export function refreshCheckPostApi(uri: string, data: any): any {
  return request({
    url: `${uri}`,
    method: 'post',
    data,
  })
}

/**
 * 打印提货单
 */
export function printPickUpOrderInfo(data: any): any {
  return request({
    url: `tms/company/order/shipment/lane/appointment/pickUpOrderInfo`,
    method: 'post',
    data,
  })
}

/**
 * 下级审批操作
 */
export function approvalOperationApi(data: any): any {
  return request({
    url: `tms/shipment/apply/transfer/view/select`,
    method: 'post',
    data,
  })
}
/**
 * 可用预约时段查询
 */
export function laneAppointmentByDateApi(data: any): any {
  return request({
    url: `tms/company/order/shipment/lane/appointment/queryAppointmentItemsByDate`,
    method: 'post',
    data,
  })
}
/**
 * 根据时间段查询可用预约车道
 */
export function laneAppointmentByDateTimeApi(data: any): any {
  return request({
    url: `tms/company/order/shipment/lane/appointment/queryLaneItemsByDateTime`,
    method: 'post',
    data,
  })
}
/**
 * OTD查询条件获取
 */
export function otdQueryParamsApi(data: any): any {
  return request({
    url: `tms/company/statistics/otd/queryParams`,
    method: 'get',
    data,
  })
}
/**
 * OTD查询条件获取
 */
export function getTreeOptionByVehicle(params: any): any {
  return request({
    url: `tms/company/carrier/select/treeOptionByVehicle`,
    method: 'get',
    params,
  })
}
