/*
 * @Author: llm
 * @Date: 2024-01-19 16:17:49
 * @LastEditors: llm
 * @LastEditTime: 2024-01-19 16:25:14
 * @Description:
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
import { OrderPlanMergeOrderVO, OrderPlanSubmitReleaseVO } from '@/api/planManagement/type'

/**
 * 手动合并
 */
export function orderPlanMergeOrder(data: OrderPlanMergeOrderVO): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/order/plan/railway/merge/order',
    method: 'post',
    data,
  })
}

/**
 * 关闭手动分配锁
 */
export function orderPlanSubmitRelease(params: OrderPlanSubmitReleaseVO): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/order/plan/railway/submit/release',
    method: 'get',
    params,
  })
}
/**
 * 自动分配预览
 * @param {string} data ids
 * @param {string} uri 请求地址前缀
 */
export function planSubmitPreApi(params: Object, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}/submit/preview/detail`,
    method: 'get',
    params,
  })
}
/**
 * 手动分配下发
 * @param {string} data 分配后的数据
 * @param {string} uri 请求地址前缀
 */
export function planSubmitSpecifyApi(data: Object, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}/submit/specify`,
    method: 'post',
    data,
  })
}
/**
 *重新分配
 */
export function reAllocation(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/order/plan/railway/submit/planId',
    method: 'get',
    params,
  })
}
/**
 * 计划下发
 * @param data ids
 * @param uri 请求地址前缀
 */
export function submitPlanApi(data: Object, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}/distribute`,
    method: 'post',
    data,
  })
}
/**
 * 查看当前行所有vin
 * @param {string} data ids
 * @param {string} uri 请求地址前缀
 */
export function viewAllVins(data: Object, uri: string): AxiosPromise<ResponseResult> {
  return request({
    url: `${uri}/submit/preview?planOrderIds=${data}`,
    method: 'get',
  })
}
