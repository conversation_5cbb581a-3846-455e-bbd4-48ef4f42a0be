import { VinInfoVO } from './../planManagement/type'
/*
 * @Author: llm
 * @Date: 2023-07-13 09:54:36
 * @LastEditors: llm
 * @LastEditTime: 2024-09-21 17:23:23
 * @Description: 计划管理
 *
 */

export interface StateVO {
  formData?: AppointmentFormVO
  submitParams?: ConfirmFormDataVO | null
}
/**
 * 运单申请合并任务列表参数
 */
export interface appointmentTaskListVO {
  vehicleInfo: string
  driverInfo: string
}
/**
 * 批量录入表单
 */
export interface BatchShipmentConfirmVO {
  formData?: shipmentOrderConfirmSubmitVO
  batchShipmentInfo?: BatchShipmentInfoVO
}
export interface BatchShipmentInfoVO extends ApplyVehicleInfoVO {
  customerName?: string
  lineName?: string
  carrierName?: string
  carrierId?: string
  vehicleNo?: string
  vehicleId?: string
  shipmentNo?: string
}
export interface ApplyVehicleInfoVO {
  /**
   * 装车日期
   */
  expectDateTime?: string
  baseWareItems?: baseWareItemsVO[]
}
export interface StateLaneReservationVO {
  formData?: LaneAppointmentFormVO
  /**
   * 三级联动
   */
  carriers: levelOptionsVO
}
export interface LaneAppointmentFormVO {
  vehicleId?: string
  carrierId?: string
  shipmentNo?: string
  driverId?: string
  driverName?: string
  driverMobile?: string
  vins?: string
  wares?: waresVO[]
}
/**
 * 三级联动
 */
export interface levelOptionsVO {
  /**
   * 供应商信息
   */
  carriers?: CarriersVO[]
}
export interface CarriersVO {
  carrierId: string
  carrierName: string
  vehicles: CarriersVehiclesVO[]
}
/**
 * 三级联动 供应商下的车辆信息
 */
export interface CarriersVehiclesVO extends VehiclesVO {
  shipmentNos: string[]
}
/**
 * 请求参数
 */
export interface AppointmentRequest {
  /**
   * 列表项选中的ids数组
   */
  orderIds?: string[]
}
/**
 * 请求参数
 */
export interface appointmentIdsRequest {
  baseWareId?: any
  appointmentDateList?: any
  /**
   * 预约日期
   */
  appointmentDate?: string
  /**
   * 车道id
   */
  laneId?: string
}
/**
 * 表单提交
 */
export interface ConfirmFormDataVO {
  /**
   * 承运商ID
   */
  carrierId: string
  /**
   * 承运商仓库ID
   */
  carrierWareId: string
  /**
   * 司机ID
   */
  driverId: string
  /**
   * 下级承运商
   */
  levelCarrierIds: Array<string>
  /**
   * 司机手机号
   */
  driverMobile: string
  /**
   * 司机姓名
   */
  driverName: string
  /**
   * 是否倒板
   */
  invertPlate: boolean
  /**
   * 车道信息
   */
  laneItems: laneItemsVO[]
  /**
   * 车辆ID
   */
  vehicleId: string
  /**
   * 车牌号
   */
  vehicleNo: string
  /**
   * 仓库ID
   */
  warehouseId: string
  /**
   * 需要合并的运单号
   */
  shipmentNo?: string
}

/**
 * 提交预约
 */
export interface laneAppointmentSubmitRequestVO {
  /**
   * 供应商id
   */
  carrierId?: string
  /**
   * 车道信息
   */
  laneItems?: laneItemsVO[]
  /**
   * 运单号
   */
  shipmentNo?: string
}
/**
 * 申请预约响应数据
 */
export interface AppointmentResponse {
  drivers?: DiverVO[]
  vehicles?: VehiclesVO[]
  carriers?: CarriersVO[]
  wares?: waresVO[]
}
export interface DiverVO {
  driverId: string
  driverName: string
  mobile: string
}

export interface VehiclesVO {
  /**
   * 是否倒板
   */
  invertPlate: boolean
  vehicleId: string
  vehicleNo: string
  warehouseId: string
  wares: vehicleWaresVO[]
}
export interface vehicleWaresVO {
  /**
   * 倒板仓id
   */
  wareId: string
  /**
   * 倒板仓名称
   */
  wareName: string
}
/**
 * 公路发运-运单申请-申请预约form
 * @param orderIds 订单id数组
 */
export interface AppointmentFormVO extends AppointmentResponse {
  /**
   * 车道信息
   */
  laneItems?: laneItemsVO[]
  /**
   * 车辆ID
   */
  vehicleId?: string
  /**
   * 车牌号
   */
  vehicleNo?: string
  /**
   * 下级承运商
   */
  levelCarrierIds?: Array<string>
  /**
   * 倒板仓
   */
  warehouseId?: string
  /**
   * 是否倒板
   */
  invertPlate?: boolean
  /**
   * 联系方式
   */
  driverMobile?: string
  /**
   * 司机ID
   */
  driverId?: string
  /**
   * 司机姓名
   */
  driverName?: string
  /**
   * 承运商id
   */
  carrierId?: string
  /**
   * 承运商名称
   */
  carrierName?: string
  /**
   * 倒板仓
   */
  carrierWareId?: string
}
export interface laneItemsVO {
  appointmentDate: string
  appointmentId: string
  laneId: string
  laneName: string
  orderIds: string[]
  /**
   * 仓库id
   */
  warehouseId?: string
}

export interface waresVO {
  laneList: AppointmentsVO[]
  appointmentDateList: any
  /**
   * 仓库id
   */
  baseWareId: string

  /**
   * 仓库名称
   */
  baseWareName?: string

  /**
   * 车道
   */
  lanes: lanesVO[]

  /**
   * orders
   */
  orders: ordersVO[]

  /**
   * 预约日期
   */
  appointmentDate?: string
  /**
   * 预约时间点id
   */
  appointmentId?: string
  /**
   * 之前预约过的时间点id
   */
  selectedAppointmentId?: string
  /**
   * 之前预约过的车道id
   */
  selectedAppointmentLaneId?: string
  /**
   * 车道id
   */
  laneId?: string
  /**
   * 车道名称
   */
  laneName?: string
  /**
   * 订单ids
   */
  orderIds?: string[]
  /**
   * 临时车道时间段，提交前删掉
   */
  tempLaneDate?: tempLaneDateVO[]
  /**
   * 临时车道下的table配置，提交前删掉
   */
  tempTableConfig?: TableConfig
  /**
   * 临时车道下的已预约信息，提交前删掉
   */
  tempTableData?: any[]
  /**
   * 临时选中的车道，提交前删掉
   */
  tempCurrentLanes: lanesVO
}
interface tempLaneDateVO {
  /**
   * 时间段id
   */
  id: string
  /**
   * 时间段名称
   */
  name: string
}
export interface lanesVO {
  /**
   * 车道名称
   */
  laneName: string
  /**
   * 车道id
   */
  laneId: string
  /**
   * 时间段
   */
  appointments: AppointmentsVO[]
  /**
   * 已预约日时间
   */
  appointmentDate: string
}

interface ordersVO {
  customerName: string
  driverName: string
  mobile: string
  vehicleNo: string
  vin: string
  id: string
}

/**
 * 车道预约时间段
 */
export interface AppointmentsVO {
  /**
   * 车道名称
   */
  laneName: string
  /**
   * 车道ID
   */
  laneId: string
  /**
   * 时间段id
   */
  appointmentId: string
  /**
   * 当前时间段是否被预约
   */
  appointmentStatus: number | null
  /**
   * 时间段
   */
  timeInterval: string

  /**
   * 车道可预约vin数
   */
  vinCount: number | null
  /**
   * 选中的时间段
   */
  active?: boolean
  /**
   * 已被预约时间段
   */
  disabled?: boolean
}

/**
 * 车道预约请求
 */
export interface appointmentVehicleShipmentInfoRequestVO {
  /**
   * 固定传3
   */
  carrierType: string
}
/**
 * 根据运单号查询车道预约信息请求
 */
export interface appointmentInfoByVehicleRequestVO {
  /**
   *
   */
  vehicleId: string
}

/**
 * 根据运单号查询车道预约的运单信息
 */
export interface shipmentInfoVO {
  /**
   * 司机手机号
   */
  driverMobile: string
  /**
   * 司机姓名
   */
  driverName: string
  /**
   * 倒板仓信息
   */
  wares: waresVO[]
}

/**
 * 根据运单号查询车道预约的运单信息
 */
export interface laneAppointmentUpdateVinRequestVO {
  /**
   * 车辆id
   */
  vehicleId?: string
  /**
   * 1 删除 2 增加
   */
  vinUpdateType?: number
  /**
   * 倒板仓信息
   */
  vins?: string[]
}

/**
 * 公路发运-运单确认form
 */
export interface highWayBatchShipmentConfirmVO {}
export interface baseWareItemsVO extends lanesVO {
  /**
   * 装车日期
   */
  confirmDate: string

  /**
   * 装车时间段
   */
  confirmTime?: any
  /**
   * 仓库id
   */
  baseWareId: string

  /**
   * 仓库名称
   */
  baseWareName?: string

  /**
   * 车道
   */
  lanes: lanesVO[]

  /**
   * orders
   */
  orders: ordersVO[]
}
export interface shipmentOrderConfirmSubmitItemsVO {
  /**
   * 装车日期
   */
  confirmDate: string

  /**
   * 装车时间段
   */
  confirmTime?: string[]

  /**
   * 车道id
   */
  laneId: string

  /**
   * 车道名称
   */
  laneName: string
  /**
   * 订单ids
   */
  orderIds: string[]
}
export interface shipmentOrderConfirmSubmitVO {
  items?: shipmentOrderConfirmSubmitItemsVO[]
}
/**
 * 车辆预约时间段
 */
export interface AppointmentItemsVO {
  appointmentDateList: any
  appointmentDate: any
  baseWareId: any
  laneList: any
  selectedAppointmentId: string
  laneId: string
  appointmentId: string
  appointmentStatus: any
  timeInterval: string
  workEnd: string
  workStart: string
}
/**
 * 需要合并的运单号列表
 */
export interface MergeShipmentNoVO {
  shipmentNo: string
  taskName: string
}
