import { AxiosPromise } from 'axios'
import request from '@/utils/request'

/**
 * 获取主维度
 */
export function mainDimensionApi(type: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/report/forms/template/dimension/select/option/' + type,
    method: 'get',
  })
}
/**
 * 根据主维度获取次纬度
 */
export function otherDimensionByMainApi(type: any, id: any): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/report/forms/template/getOtherDimensionByMain/${type}/${id}`,
    method: 'get',
  })
}
/**
 * 获取报表类型
 */
export function formsTemplateTypeSelectOptionsApi(): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/report/forms/template/type/select/option`,
    method: 'get',
  })
}
/**
 * 根据纬度获取指标
 */
export function formsTemplateGetIndicatorByDimensionApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: `tms/report/forms/template/getIndicatorByDimension`,
    method: 'post',
    data,
  })
}
