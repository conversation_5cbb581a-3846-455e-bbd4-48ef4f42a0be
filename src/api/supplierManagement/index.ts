/*
 * @Author: llm
 * @Date: 2024-04-26 15:33:26
 * @LastEditors: llm
 * @LastEditTime: 2024-04-29 18:05:13
 * @Description:
 */
import { ExamineTemplateVO } from './type'
import { AxiosPromise } from 'axios'
import request from '@/utils/request'

/**
 * 获取主维度
 */
export function examineTemplateUseTargetSelectOptionsApi(): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/carrier/examine/template/useTarget/select/option',
    method: 'get',
  })
}
/**
 * 新增考核模板
 */
export function examineTemplateAddApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/carrier/examine/template/add',
    method: 'post',
    data,
  })
}
/**
 * 新增考核模板
 */
export function examineListAddApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: '/tms/carrier/examine/list/add',
    method: 'post',
    data,
  })
}
/**
 * 编辑考核模板
 */
export function examineTemplateUpdateApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/carrier/examine/template/update',
    method: 'post',
    data,
  })
}
/**
 * 编辑考核项目
 */
export function examineListUpdateApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/carrier/examine/list/update',
    method: 'post',
    data,
  })
}
/**
 * 考核类别下拉
 */
export function examineTemplateCategorySelectOptionsApi(): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/carrier/examine/template/category/select/option',
    method: 'get',
  })
}
/**
 * 考核大项下拉
 */
export function examineTemplateMajorSelectOptionsApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/carrier/examine/template/major/select/option',
    method: 'get',
    params,
  })
}
/**
 * 查看模板详情
 */
export function examineTemplateDetailApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/carrier/examine/template/select/detail',
    method: 'get',
    params,
  })
}
/**
 * 根据模板编号查看模板详情
 */
export function examineTemplateNoApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/carrier/examine/template/select/templateNo',
    method: 'get',
    params,
  })
}
/**
 * 查看考核项目详情
 */
export function examineListDetailApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/carrier/examine/list/select/detail',
    method: 'get',
    params,
  })
}
/**
 * 考核模板下拉
 */
export function examineTemplateOptionsApi(): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/carrier/examine/template/list',
    method: 'get',
  })
}
/**
 * 考核项目发布
 */
export function examineListPublishApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/carrier/examine/list/publish',
    method: 'get',
    params,
  })
}
/**
 * 考核项目打分
 */
export function examineListMarkApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/carrier/examine/list/mark',
    method: 'post',
    data,
  })
}
