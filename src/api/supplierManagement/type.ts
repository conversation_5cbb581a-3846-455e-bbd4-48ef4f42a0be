/*
 * @Author: llm
 * @Date: 2024-04-26 15:33:39
 * @LastEditors: llm
 * @LastEditTime: 2024-04-29 18:02:59
 * @Description: 考核模板
 */
/**
 * 考核模板
 */
export interface ExamineTemplateVO {
  id?: string
  /**
   * 模版名称
   */
  templateName: string
  /**
   * 适用对象
   */
  useTarget: number
  /**
   * 备注
   */
  desc: string
  /**
   * 系统类型
   */
  systemType: string
  /**
   * 模版明细类别
   */
  majorItemList: MajorItemList[]
}
/**
 * 考核项目
 */
export interface ExamineProjectVO {
  id?: string
  /**
   * 考核项目名称
   */
  examineName: string
  /**
   * 模版名称
   */
  templateName: string
  /**
   * 模版编号
   */
  templateNo: string
  /**
   * 适用对象
   */
  useTarget: number
  /**
   * 考核类型
   */
  examineType: number
  /**
   * 开始时间
   */
  startTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 考核结果异议期
   */
  objectionPeriod: string
  /**
   * 附加分
   */
  extraPoint: string
  /**
   * 附加分标准
   */
  extraPointStandard: string
  /**
   * 系统类型
   */
  systemType: string
  /**
   * 附件地址
   */
  attachment: string
  /**
   * 附加分
   */
  extraPointScore: string
  /**
   * 模版明细类别
   */
  majorItemList: MajorItemList[]
}

export interface MajorItemList {
  /**
   * 考核类别
   */
  category: number
  /**
   * 考核大项
   */
  majorItem: string
  /**
   * 大项权重
   */
  majorItemWeight: number
  /**
   * 序号
   */
  serialNumber: number
  /**
   * 分项
   */
  subItemList: SubItemList[]
}

interface SubItemList {
  /**
   * 考核分项
   */
  subItem: string
  /**
   * 顺序
   */
  subItemSort: number
  /**
   * 考核标准
   */
  standard: string
  /**
   * 标准分数
   */
  assessMark: number
  /**
   * 分项权重
   */
  subItemWeight: number
}
