/*
 * @Author: llm
 * @Date: 2025-03-14 15:55:41
 * @LastEditors: llm
 * @LastEditTime: 2025-03-14 18:21:27
 * @Description:
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'

/**
 * 点检对象下拉框 / 需要视频/图片下拉框
 */
export function getDistributeSelectOptionsApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/common/option/distribute/select',
    method: 'get',
    params,
  })
}
/**
 * 点检对象下拉框
 */
export function getSpotCheckNodeOptionsApi(params: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/config/spot/check/configuration/point/check/node/options',
    method: 'get',
    params,
  })
}
/**
 * 新增配置
 */
export function postSpotCheckConfigurationApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/config/spot/check/configuration',
    method: 'post',
    data,
  })
}
/**
 * 编辑配置
 */
export function putSpotCheckConfigurationApi(data: any): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/config/spot/check/configuration/' + data.id,
    method: 'put',
    data,
  })
}
/**
 * 编辑配置
 */
export function getSpotCheckConfigurationDetailApi(id: string): AxiosPromise<ResponseResult> {
  return request({
    url: 'logistics/api/out/fleet/config/spot/check/configuration/cfg/detail/' + id,
    method: 'get',
  })
}
