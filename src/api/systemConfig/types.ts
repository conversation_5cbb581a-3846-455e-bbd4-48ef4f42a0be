export interface InspectionConfigVO {
  id?: string
  checkNodeList: string[]
  checkObject: string
  remark: string
  standardName: string
  cfgs: CFG[]
}

export interface CFG {
  checkItem: string
  content: Content[]
}

export interface Content {
  checkContent: string
  needMultimedia: string
  sampleImageList: SampleImageList[]
}

export interface SampleImageList {
  name: string
  ossPath: string
  size: number
  sizeDesc: string
  type: string
  url: string
}
