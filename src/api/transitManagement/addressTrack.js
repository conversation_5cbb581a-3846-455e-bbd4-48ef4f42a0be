import request from '@/utils/request'
/**
 * @description: 在途跟踪导出
 * @param {*} data
 * @return {*}
 */

// 在途跟踪列表
export function getListVehicles(data) {
  return request({
    url: 'api/hdd/enterprise/vehicle/v1/backendlistVehiclesInTrace',
    method: 'post',
    data,
  })
}

// 在途跟踪车辆列表
export function getVehiclesList(data) {
  return request({
    url: 'api/hdd/enterprise/vehicle/v1/track/vehicles',
    method: 'post',
    data,
  })
}
// 在途跟踪车辆详情
export function getVehiclesDetail(data) {
  return request({
    url: 'api/hdd/enterprise/vehicle/v1/track/vehicle/detail',
    method: 'post',
    data,
  })
}

// 轨迹查询 ——轨迹导出接口
export function exportTrace(data) {
  return request({
    url: 'api/hdd/enterprise/vehicle/v1/route/export/segmentTraceV2',
    method: 'post',
    responseType: 'blob',
    data,
  })
}

// 轨迹查询列表
export function getTravelList(data) {
  return request({
    url: 'api/hdd/route/v1/BackendTravelList',
    method: 'post',
    data,
  })
}

// 轨迹查询 添加共享轨迹
// export function addShareTrack(data) {
//     return request({
//         url: 'api/hdd/trackCollection/v1/company/add',
//         method: 'post',
//         data
//     })
// }

export function addShareTrack(data) {
  return request({
    url: 'userCenter/app/companyTrackCollection/add',
    method: 'post',
    data,
  })
}

// 轨迹查询 取消共享轨迹 --> 删除共享数据
export function deleteShareTrack(data) {
  return request({
    url: 'api/hdd/trackCollection/v1/company/delete',
    method: 'post',
    data,
  })
}

// 路线共享页面 ——> 路线共享列表
// export function getRoutelist(data) {
//     return request({
//         url: 'api/hdd/trackCollection/v1/company/list',
//         method: 'post',
//         data
//     })
// }

// 路线共享页面 ——> 路线详情
export function getRouteDetail(params) {
  return request({
    url: 'userCenter/app/companyTrackCollection/getDetail',
    method: 'get',
    params,
  })
}

export function getRoutelist(params) {
  return request({
    url: 'userCenter/app/companyTrackCollection/page',
    method: 'get',
    params,
  })
}

// 路线共享页面 ——> 设置共享对象
export function updateShared(data) {
  return request({
    url: 'api/hdd/trackCollection/v1/company/update',
    method: 'post',
    data,
  })
}

// 查询所有车队列表
export function getFleetAll(data) {
  return request({
    url: 'api/hdd/backend/fleets/all',
    method: 'get',
    data,
  })
}

// 查询所有司机
export function getDriversAll(data, id) {
  return request({
    url: 'api/hdd/backend/drivers/fleet/' + id,
    method: 'get',
    data,
  })
}

// 查询当前车队下 所有司机
export function getFleetUsers(data) {
  return request({
    url: 'api/hdd/backend/geoFence/fleetUsers',
    method: 'get',
    data,
  })
}

// 在途跟踪  导出
export function exportVehicles(data) {
  return request({
    url: 'api/hdd/enterprise/vehicle/v1/exportCompanyVehiclesV2',
    method: 'post',
    data,
  })
}

// 查询业务线轨迹围栏等穿行信息
export function getRouteLine(params) {
  return request({
    url: 'api/hdd/backend/company/business/line/vehicle/geofence/getBusinessRelatedGeofencesAndVehicleTracks',
    method: 'get',
    params,
  })
}

// 获取 路线链接 列表数据
export function routeShareLinkData(params) {
  return request({
    url: 'api/hdd/backend/routeShare/page',
    method: 'get',
    params,
  })
}

// 获取 路线链接 使用记录
export function routeShareRecord(params) {
  return request({
    url: 'api/hdd/backend/routeShare/record/page',
    method: 'get',
    params,
  })
}

// 路线链接使用记录 ——> 查看轨迹
export function routeTrajectoryDetail(params) {
  return request({
    url: 'api/hdd/backend/routeShare/shareRecordTrack',
    method: 'get',
    params,
  })
}

// 路线链接 ——> 复制链接
export function routeGenerateUrl(params) {
  return request({
    url: 'api/hdd/backend/routeShare/generateUrl',
    method: 'get',
    params,
  })
}

// 路线链接 ——> 复制编码
export function routeGenerateCode(params) {
  return request({
    url: 'api/hdd/backend/routeShare/generateCode',
    method: 'get',
    params,
  })
}

// 根据车牌号获取自有+外协车辆列表 任务跳转-路线规划, 任务跳转轨迹查询
export function listVehiclesIncludeOuterByNos(params) {
  return request({
    url: 'vehiclecenter/enterprise/vehicle/listVehiclesIncludeOuterByNos',
    method: 'get',
    params,
  })
}

// 生成链接接口 到链接列表
export function generateRouteLinkShare(data) {
  return request({
    url: 'api/hdd/backend/routeShare/generateRouteShare',
    method: 'post',
    data,
  })
}

// 运力雷达 -- 获取行政区
export function cityDistrictOption(params) {
  return request({
    url: 'api/hdd/backend/system/district/select/option',
    method: 'get',
    params,
  })
}

// 运力雷达 -- 获取雷达图扫描区域
export function vehicleRadarRange(data) {
  return request({
    url: 'api/hdd/backend/company/vehicle/radar/range',
    method: 'post',
    data,
  })
}

// 运力雷达 -- 获取雷达图扫描的车辆数据
export function vehicleRadarData(data) {
  return request({
    url: 'api/hdd/backend/company/vehicle/radar/scan',
    method: 'post',
    data,
  })
}

// 运力雷达 -- 获取承运商下拉
export function carrierOption(params) {
  return request({
    url: 'api/hdd/backend/company/carrier/select/option',
    method: 'get',
    params,
  })
}

// 获取车辆轨迹-车辆搜索
export function inTransitVehicleSelect(params) {
  return request({
    url: 'logistics/api/common/option/business/select',
    method: 'get',
    params,
  })
}
// 获取车辆轨迹-车辆搜索
export function getSegmentTrace(data) {
  return request({
    url: 'logistics/api/vehicle/intransit/trace/vehicle',
    method: 'post',
    data,
  })
}

// 获取共享路线列表
export function getCollectionDriverPage(params) {
  return request({
    url: 'logistics/api/out/fleet/route/collection/driver/page',
    method: 'GET',
    params,
  })
}

// 保存共享路线
export function saveShareLine(data) {
  return request({
    url: 'logistics/api/out/fleet/route/collection/addOrUpdate',
    method: 'POST',
    data,
  })
}

// 路线规划
export function routePlanDataApi(data) {
  return request({
    url: 'logistics/route/routing',
    method: 'POST',
    data,
  })
}
// 路线规划 - 省市区下拉选择
export function districtSelectOptionApi(params) {
  return request({
    url: 'logistics/system/common/district/select/option',
    method: 'get',
    params,
  })
}
