import request from '@/utils/request'

// 基础围栏列表
export function getListSingleGeoFences(data) {
  return request({
    url: 'api/hdd/backend/geoFence/singleGeoFences',
    method: 'post',
    data,
  })
}

// 基础围栏列表
export function getListSingleGeoFencesByDriver(params) {
  return request({
    url: 'api/hdd/backend/company/driver/punch/card/station/getPunchCardStation',
    method: 'get',
    params,
  })
}

// 基础围栏新增or编辑
export function getAddOrUpdateSingle(data) {
  return request({
    url: 'api/hdd/backend/geoFence/addOrUpdateSingleGeoFence',
    method: 'post',
    data,
  })
}

// 基础围栏删除
export function deleteSingleGeoFence(data) {
  return request({
    url: 'api/hdd/backend/geoFence/deleteSingleGeoFence',
    method: 'post',
    data,
  })
}

// 基础围栏列表导出
export function exportSingleExcel(data, params) {
  return request({
    url: 'api/hdd/backend/geoFence/exportSingle?geoFenceName=' + params.geoFenceName + '&province=' + params.province + '&nameId=' + params.nameId,
    method: 'get',
    data,
  })
}

// 电子围栏列表
export function getListData(data, params) {
  return request({
    url: 'api/hdd/backend/geoFence/list',
    method: 'get',
    params,
  })
}

// 电子围栏新增or编辑
export function getAddOrUpdate(data) {
  return request({
    url: 'api/hdd/backend/geoFence/addOrUpdate',
    method: 'post',
    data,
  })
}

// 电子围栏删除
export function fenceDelete(data) {
  return request({
    url: 'api/hdd/backend/geoFence/delete',
    method: 'post',
    data,
  })
}

// 电子围栏报警接收人
export function getlistReceipt(data) {
  return request({
    url: 'api/hdd/backend/geoFence/listReceipt',
    method: 'get',
    data,
  })
}

// 围栏名称列表
export function getGeoFenceName(data) {
  return request({
    url: 'api/hdd/backend/geoFence/geoFenceNameTypes',
    method: 'get',
    data,
  })
}

// 电子围栏导出
export function exportExcel(data, params) {
  return request({
    url:
      'api/hdd/backend/geoFence/export?fleetIds=' +
      params.fleetIds +
      '&geoFenceName=' +
      params.geoFenceName +
      '&province=' +
      params.province +
      '&nameId=' +
      params.nameId,
    method: 'get',
    data,
  })
}

// 电子围栏履历列表
export function EnterpriseList(params) {
  return request({
    url: 'api/hdd/backend/backendEnterpriseMessage/page',
    method: 'get',
    params,
  })
}

// 电子围栏履历导出
export function EnclosureExcel(data, params) {
  return request({
    url:
      'api/hdd/backend/backendEnterpriseMessage/export?vehicleNo=' +
      params.vehicleNo +
      '&beginDate=' +
      params.beginDate +
      '&endDate=' +
      params.endDate +
      '&geofenceId=' +
      params.geofenceId,
    method: 'get',
    data,
  })
}
//192.168.1.16:8920/api/hdd/backend/company/geofence/delay/export?beginReportTime=2023-07-08&endReportTime=2023-07-08
// 电子围栏履历导出类型是3
export function EnclosureExcelType3(data) {
  return request({
    url: 'api/hdd/backend/company/geofence/delay/export',
    method: 'get',
    params: data,
  })
}

// 路线规划接口
export function getPlan(data) {
  return request({
    url: 'routeplan/api/net/route/v2/plan',
    method: 'post',
    data,
  })
}

// 路线规划查询路线接口
export function viewSelect(data) {
  return request({
    url: 'api/hdd/routeNet/v1/select',
    method: 'post',
    data,
  })
}

// 围栏监控配置
export function fanceConfig(data) {
  return request({
    url: 'api/hdd/backend/geoFence/list/out/addOrUpdate',
    method: 'post',
    data,
  })
}

// 围栏监控接收人
export function getReceiptData(data) {
  return request({
    url: 'api/hdd/backend/geoFence/listReceiptV2',
    method: 'get',
    data,
  })
}

// 围栏监控配置查看
export function showConfig(data) {
  return request({
    url: 'api/hdd/backend/geoFence/list/out/showConfig',
    method: 'post',
    data,
  })
}
// 围栏外监控列表
export function getFenceList(params) {
  return request({
    url: 'api/hdd/backend/geoFence/list/out/list',
    method: 'get',
    params,
  })
}
// 围栏外监控列表导出
export function fenceListExport(params) {
  return request({
    url: 'api/hdd/backend/geoFence/list/out/exportStayEvents',
    method: 'get',
    params,
  })
}

// 围栏概览列表
export function fenceWithinVehicle(params) {
  return request({
    url: 'api/hdd/backend/geoFence/list/out/getVehicleInGeoFenceList',
    method: 'get',
    params,
  })
}
// 围栏概览列表导出
export function exportWithinVehicle(params) {
  return request({
    url: 'api/hdd/backend/geoFence/list/out/exportVehicleInGeoFenceList',
    method: 'get',
    params,
  })
}

// 超时监控详情
export function geofenceDelayPage(params) {
  return request({
    url: 'api/hdd/backend/company/geofence/delay/page',
    method: 'get',
    params,
  })
}

// 获取省市区县
export function getCitiesList(data) {
  return request({
    url: 'routeplan/v1/city/list/onlyCity',
    method: 'post',
    data,
  })
}

// 获取行政区域围栏
export function getDistrictFanceData(params) {
  return request({
    url: 'api/hdd/backend/system/district/tree',
    method: 'get',
    params,
  })
}

// 根据城市查询通行证
export function passcheckCitys(params) {
  return request({
    url: 'api/hdd/route/v1/passcheck/citys',
    method: 'get',
    params,
  })
}
/**
 * @description: 百度逆地理解析
 * @param {*} data
 * @return {*}
 */
export function bdRegeoCoding(data) {
  return request({
    url: 'api/hdd/backend/utils/bd/regeoCoding',
    method: 'post',
    data,
  })
}
