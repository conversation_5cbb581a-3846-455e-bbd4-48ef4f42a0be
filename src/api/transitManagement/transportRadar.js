/*
 * @Author: llm
 * @Date: 2024-12-19 12:03:04
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-01 12:11:50
 * @Description:
 */
import request from '@/utils/request'

/**
 * 子公司汇总数据
 */
export function getFleetTransportCapacitySummaryQueryApi(params) {
  return request({
    url: 'logistics/api/out/fleet/transport/capacity/summaryQuery',
    method: 'get',
    params,
  })
}
/**
 * 查询所有在途车辆
 */
export function getFleetTransportCapacityAllVehicleApi(params) {
  return request({
    url: 'logistics/api/out/fleet/transport/capacity/allVehicle',
    method: 'get',
    params,
  })
}
/**
 * 查询所有围栏
 */
export function getFleetTransportCapacityFenceQueryApi(params) {
  return request({
    url: 'logistics/api/out/fleet/transport/capacity/fenceQuery',
    method: 'get',
    params,
  })
}
/**
 * 运力雷达-车辆信息及调度单信息
 */
export function getVehicleDispatchInfoApi(params) {
  return request({
    url: 'logistics/api/out/fleet/transport/capacity/vehicle/dispatch/info',
    method: 'get',
    params,
  })
}
