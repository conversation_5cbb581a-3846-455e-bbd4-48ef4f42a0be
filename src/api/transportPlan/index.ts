/*
 * @Author: llm
 * @Date: 2024-09-25 09:35:20
 * @LastEditors: 周宗文
 * @LastEditTime: 2024-10-23 16:57:11
 * @Description:
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
import { BaseParamsVO, MonthParamsVO, saveParamsVO, CrossParamsVO } from './type'

/**
 * 保存全局设置
 */
export function saveSetUpPlanApi(id: string, data: saveParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/transport/plan/total/limit/' + id,
    method: 'put',
    data,
  })
}

/**
 *  按钮 - 规划方案
 */

export function taskStartPlanApi(params: BaseParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/transport/plan/task/startPlan',
    method: 'get',
    params,
  })
}

/**
 * 获取规划设置信息
 */
export function getTransportConfig(params: BaseParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/transport/plan/total/limit/getConfig',
    method: 'get',
    params,
  })
}
/**
 * 获取历史规划设置信息
 */
export function getHistoryTransportConfig(params: BaseParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/transport/plan/total/limit/getHistoryConfig',
    method: 'get',
    params,
  })
}

/**
 * 获取可选择版本列表
 */
export function getTaskSelectOption(params: BaseParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/transport/plan/data/summary/select/option',
    method: 'get',
    params,
  })
}

/**
 * 跨方案对比
 */
export function taskCrossCompare(data: CrossParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/transport/plan/task/crossCompare',
    method: 'post',
    data,
  })
}

/**
 *  根据月份设置限制
 */

export function getMonthSetLimitationApi(data: MonthParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/transport/plan/task/setMonth',
    method: 'post',
    data,
  })
}
/**
 * 线路约束列表
 */

export function getPlanLinePageApi(id: string): AxiosPromise<any> {
  return request({
    url: 'tms/transport/plan/line/limit/page',
    method: 'get',
  })
}
/**
 * 省份约束列表
 */

export function getPlanProvincePageApi(id: string): AxiosPromise<any> {
  return request({
    url: 'tms/transport/plan/province/limit/page',
    method: 'get',
  })
}
/**
 * 港站约束列表
 */

export function getPlanPortPageApi(id: string): AxiosPromise<any> {
  return request({
    url: 'tms/transport/plan/port/limit/page',
    method: 'get',
  })
}
/**
 * 月份约束列表
 */

export function getPlanMonthPageApi(id: string): AxiosPromise<any> {
  return request({
    url: 'tms/transport/plan/month/limit/page',
    method: 'get',
  })
}
