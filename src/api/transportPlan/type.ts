/*
 * @Author: 周宗文
 * @Date: 2024-10-18 18:46:21
 * @LastEditors: 周宗文
 * @LastEditTime: 2024-10-22 17:08:19
 * @Description:
 */
export interface BaseParamsVO {
  taskId?: string
  dataId?: string
}
export interface MonthParamsVO {
  taskId?: string
  startMonth: string
  endMonth: string
}
export interface CrossParamsVO {
  taskId?: string
  month: string
  dataIdList: Array<string>
}
export interface saveParamsVO {
  id: string
  dataId: string
  brandName: string //品牌
  expectNum: number | null //预估量
  roadRatio: number | null //公路占比
  roadOffsetRatio: number | null //比例偏差
  railwayRatio: number | null //铁路占比
  railwayOffsetRatio: number | null //比例偏差
  waterwayRatio: number | null //水路占比
  waterwayOffsetRatio: number | null //比例偏差
  newPortUpperLimit: number | null //新港站数
  otdUpperLimit: number | null //otd上限
  startMonth: string
  endMonth: string
}
