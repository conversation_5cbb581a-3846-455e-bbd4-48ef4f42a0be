/*
 * @Author: llm
 * @Date: 2024-04-26 15:33:26
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-08-06 10:27:48
 * @Description:
 */
import { WarningProjectVO, VehicleModelOptionVO } from './type'
import { AxiosPromise } from 'axios'
import request from '@/utils/request'

/**
 * 保存 待配板划分 参数设置
 */
export function waitingAllocateVinWarningApi(): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/customer/base/waitingAllocateVinWarning',
    method: 'post',
  })
}
/**
 * 获取车型列表
 */
export function vehicleModelSelectOptionApi(params: any): AxiosPromise<VehicleModelOptionVO> {
  return request({
    url: 'tms/company/customer/vehicleModel/select/option',
    method: 'get',
    params,
  })
}
/**
 * 后端-川 没配置按钮接口 前端写死请求保存 - 策略配置
 */
export function saveConfigurationApi(data: any): AxiosPromise<any> {
  return request({
    url: 'tms/company/switch/config/loadVin/detailById',
    method: 'post',
    data,
  })
}
