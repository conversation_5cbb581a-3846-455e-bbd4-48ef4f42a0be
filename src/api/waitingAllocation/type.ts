/*
 * @Author: llm
 * @Date: 2024-04-26 15:33:39
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-07-30 14:36:06
 * @Description: 待配板配置项
 */

/**
 * 待配板配置项
 */
export interface WarningProjectVO {
  id?: string
  /**
   * 绿色
   */
  warningDayOne: number
  /**
   * 黄色
   */
  warningDayTwo: number
  /**
   * 橙色
   */
  warningDayThree: number
  /**
   * 红色
   */
  warningDayFour: number
}
export interface VehicleModelOptionVO {
  value: string | number
  label: string
  children?: VehicleModelOptionVO[] | undefined
}

/**
 * 带配板分配设置
 */
export interface plateAllocationProjectVO {
  id?: string
  /**
   * 装载位
   */
  standardLoadCapacity: number
  customerId: string
  /**
   * 模版明细类别
   */
  list: itemList[]
}

export interface itemList {
  id?: string
  /**
   * 车型1数量
   */
  totalVinId1: number
  /**
   * 车型2数量
   */
  totalVinId2: number
  /**
   * 车型1
   */
  vehicleModelId1: string
  /**
   * 车型2
   */
  vehicleModelId2: string
}
