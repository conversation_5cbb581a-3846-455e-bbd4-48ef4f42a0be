/*
 * @Author: llm
 * @Date: 2024-09-25 09:35:20
 * @LastEditors: llm
 * @LastEditTime: 2024-09-25 17:38:44
 * @Description:
 */
import { AxiosPromise } from 'axios'
import request from '@/utils/request'
import { BaseParamsVO, WarehouseMoveInventoryPlanParamsVO, WarehouseMoveInventoryPlanVO, WarehouseParamsVO, WarePartitionParamsVO } from './type'

/**
 * 保存 待配板划分 参数设置
 */
export function waitingAllocateVinWarningApi(): AxiosPromise<ResponseResult> {
  return request({
    url: 'tms/company/customer/base/waitingAllocateVinWarning',
    method: 'post',
  })
}
/**
 * 获取基地下拉
 */

export function getBaseSelectOptionsApi(params: BaseParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/company/customer/base/select/option',
    method: 'get',
    params,
  })
}
/**
 * 根据基地, 查询仓库列表
 */

export function getWarehouseSelectOptionsByBaseIdApi(params: WarehouseParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/screen/common/base/warehouse/select/option',
    method: 'get',
    params,
  })
}
/**
 *  根据仓库标识, 查询库区列表
 */

export function getWarePartitionSelectOptionsByRelationIdApi(params: WarePartitionParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/vin/transport/ware/partition/select/option',
    method: 'get',
    params,
  })
}
/**
 *  根据列区查询列
 */

export function getWarePartitionColumnSelectOptionsByRelationIdApi(params: WarePartitionParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/vin/transport/ware/partition/column/select/option',
    method: 'get',
    params,
  })
}
/**
 *  根据基地标识, 查询品牌下拉
 */

export function getBaseBrandSelectOptionsApi(params: WarePartitionParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/screen/common/base/brand/select/option',
    method: 'get',
    params,
  })
}
/**
 *  根据基地标识, 查询品牌下拉
 */

export function getVehicleModelSelectOptionsApi(params: WarePartitionParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/company/customer/vehicleModel/select/option',
    method: 'get',
    params,
  })
}
/**
 *  执行班长列表查询
 */

export function getExecuteLeaderSelectOptionsApi(params: WarehouseParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/warehouse/move/inventory/plan/execute/leader/select/option',
    method: 'get',
    params,
  })
}
/**
 *  根据参数, 查询 移库数量接口
 */

export function getWarehouseMoveInventoryPlanSelectOptionsApi(params: WarehouseMoveInventoryPlanVO): AxiosPromise<any> {
  return request({
    url: 'tms/warehouse/move/inventory/plan/move/total/vin',
    method: 'get',
    params,
  })
}
/**
 *  创建移库计划接口
 */

export function postWarehouseMoveInventoryPlanApi(data: WarehouseMoveInventoryPlanParamsVO): AxiosPromise<any> {
  return request({
    url: 'tms/warehouse/move/inventory/plan',
    method: 'post',
    data,
  })
}
/**
 *  移库计划详情
 */

export function getWarehouseMoveInventoryPlanDetailApi(id: string): AxiosPromise<any> {
  return request({
    url: 'tms/warehouse/move/inventory/plan/' + id,
    method: 'get',
  })
}
