/*
 * @Author: llm
 * @Date: 2024-10-16 17:20:56
 * @LastEditors: llm
 * @LastEditTime: 2024-10-22 10:17:50
 * @Description:
 */
export interface BaseParamsVO {
  simple: boolean
}
export interface WarehouseParamsVO {
  baseId: string
  simple: boolean
}
export interface WarePartitionParamsVO {
  relationId?: string
  tree?: boolean
  brandId?: string
  simple?: boolean
}
export interface WarehouseMoveInventoryPlanVO {
  warehouseId: string
  partitionId: string
  columnId: string
  brandId: string
  vehicleModelId: string
  stockAge: string
  inStoreTime: string
}
export interface WarehouseMoveInventoryPlanParamsVO {
  baseParam: string
  brandParam: string
  columnParam: string
  leaderParam: string
  partitionParam: string
  vehicleModelParam: string
  warehouseParam: string
  moveTotal: string
  name: string
  planInList: PlanInListVO
}
export interface PlanInListVO {
  columnParam: string
  partitionParam: string
  warehouseParam: string
}
