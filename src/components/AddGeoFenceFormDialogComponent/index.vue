<!--
 * @Author: llm
 * @Date: 2025-04-08 16:17:35
 * @LastEditors: llm
 * @LastEditTime: 2025-04-09 17:37:29
 * @Description: 新增围栏弹窗
-->
<template>
  <div>
    <el-dialog v-model="dialogVisible" title="新增围栏" width="900px" @close="handleClose">
      <div class="flex justify-between">
        <el-form ref="formRef" :model="formData" label-width="120px" :rules="rules">
          <el-form-item label="围栏名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入围栏名称" />
          </el-form-item>
          <el-form-item label="地区" prop="area">
            <el-input v-model="formData.area" placeholder="请输入地区" />
          </el-form-item>
          <el-form-item label="地址" prop="address">
            <el-input v-model="formData.address" placeholder="请输入地址" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" type="textarea" placeholder="描述信息，最多64个字" maxlength="64" show-word-limit :rows="4" />
          </el-form-item>
        </el-form>
        <div class="ml-2 pos-relative">
          <area-map-component
            height="600px"
            :enclosureData="{
              coordinate: formData.coordinate,
              quyu: formData.quyu,
              radius: formData.radius,
            }"
            ref="rightAreaMapComponent"
          ></area-map-component>
        </div>
      </div>
      <template #footer>
        <el-button type="primary" @click="handleSubmit(formRef)">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import { postGeoFenceAddApi } from '@/api/businessManagement'
  import AreaMapComponent from '@/components/AreaMapComponent/index.vue'
  import { FormInstance } from 'element-plus'
  const dialogVisible = ref(false)
  const rightAreaMapComponent = ref()
  const formRef = ref()
  const emit = defineEmits(['geoFenceAddSuccess', 'geoFenceAddClose'])
  const rules = ref({
    name: [{ required: true, message: '请输入围栏名称', trigger: 'blur' }],
  })
  //用于区分父组件表单中的字段
  const state = reactive({
    key: '',
  })
  const formData = ref({
    name: '',
    address: '',
    area: '',
    remark: '',
    coordinate: '',
    quyu: '',
    radius: '',
  })
  const handleSubmit = (formRef: FormInstance) => {
    formRef.validate(async (valid) => {
      if (valid) {
        const { quyu, radius, coordinate } = rightAreaMapComponent.value.formData
        formData.value.quyu = quyu
        formData.value.radius = radius
        formData.value.coordinate = coordinate
        const { data } = await postGeoFenceAddApi(formData.value)
        ElMessage.success('新增围栏成功')
        emit('geoFenceAddSuccess', formData.value, state.key)
        dialogVisible.value = false
        formRef.resetFields()
        formData.value.coordinate = ''
        formData.value.quyu = ''
        formData.value.radius = ''
      }
    })
  }
  const handleClose = () => {
    //销毁地图
    rightAreaMapComponent.value.clearAllOverlay()
    emit('geoFenceAddClose', state.key)
  }
  defineExpose({
    dialogVisible,
    state,
  })
</script>
