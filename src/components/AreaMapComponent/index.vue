<!--
 * @Author: llm
 * @Date: 2023-07-12 15:42:56
 * @LastEditors: llm
 * @LastEditTime: 2025-04-08 17:18:20
 * @Description: 画限行区域
 *
-->
<template>
  <div>
    <!-- 地图 -->
    <div id="container" ref="mapContainer" :style="{ height }"></div>
    <!-- 搜索框 -->
    <div class="inquireInput">
      <el-input class="inputWord" id="suggestId" v-model="state.keyword" placeholder="请输入查询位置" type="text" clearable />
    </div>
    <div v-if="state.isShow" class="input-card" style="width: 180px">
      <el-tabs type="border-card" v-model="state.activeName" class="changeMapType" :stretch="true" :before-leave="leaveTab">
        <el-tab-pane label="圆形" name="left">
          <el-button style="width: 100%; margin-bottom: 5px" @click.stop="drawCircle('circle')">开始编辑</el-button>
          <el-button style="width: 100%; margin-bottom: 5px" v-if="state.isOpenEditing" @click.stop="drawEdit('circle')">结束编辑</el-button>
          <el-button style="width: 100%" @click="clearAllOverlay()">清除选区</el-button>
        </el-tab-pane>
        <el-tab-pane label="多边形" name="right">
          <el-button style="width: 100%; margin-bottom: 5px" @click.stop="drawClick('polygon')">开始编辑</el-button>
          <el-button style="width: 100%; margin-bottom: 5px" v-if="state.isOpenEditing" @click.stop="drawEdit('polygon')">结束编辑</el-button>
          <el-button style="width: 100%" @click="clearAllOverlay()">清除选区</el-button>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script setup>
  // 引入异步引入地图的方法
  import { loadBaiDuDrawMap } from '@/utils/bmpgl_lib.ts'
  import { bd09togcj02, calculateCenter, color16, gcj02tobd09 } from '@/utils'
  const mapContainer = ref(null)
  const mapObj = ref(null)
  const formData = ref({
    coordinate: '', //中心点
    quyu: '', //区域
    radius: '', //半径
  })
  // 接受父组件传值
  const props = defineProps({
    /**
     * 当前是否为编辑状态
     */
    isEdit: {
      require: false,
      type: Boolean,
      default: false,
    },
    enclosureData: {
      type: Object,
      default: {},
    },
    /**
     * 地图高度 = 左侧表单高度
     */
    height: {
      type: String,
      default: '60vh',
    },
  })
  const state = reactive({
    miniMap: null,
    mapCenter: [116.397451, 39.909187], // 默认位置
    overlays: [],
    points: [],
    keyword: '',
    newPolygonList: null, //编辑时回显多边形数据
    newCircleList: null, //编辑时回显圆形数据
    isOpenEditing: false, //是否开启编辑
    activeName: 'left', //切换围栏状态
    isShow: false, //是否显示编辑围栏
    startPoint: null,
    endPoint: null,
    circleOverlay: null, //
  })

  // watch(() => newEnclosureData, (value) => {
  //   // if (
  //   //   JSON.parse(JSON.stringify(value)).quyu ||
  //   //   JSON.parse(JSON.stringify(value)).radius
  //   // ) {
  //   //   formData.value.quyu = JSON.parse(JSON.stringify(value)).quyu;
  //   //   formData.value.coordinate = JSON.parse(JSON.stringify(value)).coordinate;
  //   //   formData.value.radius = JSON.parse(JSON.stringify(value)).radius;
  //   //   state.isShow = false;
  //   // } else {
  //   //   state.isShow = true;
  //   // }
  //   // 使用 nextTick 确保在 DOM 更新后处理参数
  //   nextTick(() => {
  //   });
  // },
  //   { deep: true }
  // );

  onBeforeMount(() => {
    state.isShow = false
  })

  onMounted(() => {
    // 编辑
    if (props.isEdit) {
      watch(
        () => props.enclosureData,
        (newVal, oldVal) => {
          // 判断新旧
          if (newVal.quyu !== oldVal.quyu || newVal.radius !== oldVal.radius) {
            // 使用 nextTick 确保在 DOM 更新后处理参数
            nextTick(() => {
              formData.value = newVal
              initMiniMap()
            })
          }
        },
      )
    } else {
      // 新增
      state.isShow = true
      if (!mapObj.value) {
        initMiniMap()
      }
    }
  })

  /**
   * @description: 切换tab之前
   */
  const leaveTab = async (active) => {
    if (state.overlays.length > 0) {
      await ElMessageBox.confirm('确认切换将删除已编辑的数据！', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          clearAllOverlay()
          state.activeName = active
          return true
        })
        .catch(() => {
          return reject()
        })
    } else {
      state.activeName = active
      clearAllOverlay()
    }
  }

  // 初始化地图
  const initMiniMap = async () => {
    loadBaiDuDrawMap().then((_BMapGL) => {
      let BMapGL = null
      // 创建地图实例
      if (_BMapGL.BMapGL) {
        BMapGL = _BMapGL.BMapGL
      } else {
        BMapGL = _BMapGL
      }
      mapObj.value = new BMapGL.Map(mapContainer.value)

      // 添加比例尺控件
      mapObj.value.addControl(
        new BMapGL.ScaleControl({
          anchor: BMAP_ANCHOR_BOTTOM_LEFT,
          offset: new BMapGL.Size(10, 10),
        }),
      )
      // 添加缩放控件
      mapObj.value.addControl(
        new BMapGL.ZoomControl({
          anchor: BMAP_ANCHOR_BOTTOM_RIGHT,
          offset: new BMapGL.Size(10, 10),
        }),
      )
      // 保存地图
      state.miniMap = mapObj.value
      //清除地图上所有覆盖物
      state.miniMap.clearOverlays()
      // 创建点坐标 axios => res 获取的初始化定位坐标
      const point = new BMapGL.Point(Number(state.mapCenter[0]), Number(state.mapCenter[1]))
      // 初始化地图，设置中心点坐标和地图级别
      state.miniMap.centerAndZoom(point, 15)
      //开启鼠标滚轮缩放
      state.miniMap.enableScrollWheelZoom(true)

      // 回显围栏区域
      if (formData.value.quyu) {
        state.activeName = 'right'
        state.isShow = true
        let newList = formData.value.quyu.split(';')
        state.overlays = formData.value.quyu.split(';')
        areasData(newList)
      }
      // 回显围栏圆形
      if (formData.value.radius && formData.value.radius != 0) {
        state.activeName = 'left'
        state.isShow = true
        state.overlays = formData.value.coordinate.split(',')
        cricleData(formData.value.coordinate)
      }

      //建立一个自动完成的对象
      var ac = new BMapGL.Autocomplete({
        input: 'suggestId',
        location: state.miniMap,
      })

      // 鼠标点击下拉列表
      ac.addEventListener('onconfirm', (e) => {
        var v = e.item.value
        var keyword = v.province + v.city + v.district + v.street + v.business
        //清除地图上所有覆盖物
        state.miniMap.clearOverlays()
        //智能搜索
        var local = new BMapGL.LocalSearch(state.miniMap, {
          onSearchComplete: () => {
            var p = local.getResults().getPoi(0).point //获取第一个智能搜索的结果
            state.miniMap.centerAndZoom(p, 18)
            state.miniMap.addOverlay(new BMapGL.Marker(p)) //添加标注
          },
        })
        local.search(keyword)
      })
      return false
    })
  }

  // 回显多边形区域
  const areasData = async (data) => {
    let color = color16()
    const arr = []
    let str = ''
    let pointArray = []
    data.forEach((item) => {
      arr.push(gcj02tobd09(item.split(',')[0], item.split(',')[1]))
    })
    arr.forEach((event) => {
      state.points.push(new BMapGL.Point(Number(event[0]), Number(event[1])))
    })
    str = arr.join(';')
    const hole = new BMapGL.Polygon(str, {
      fillColor: color,
      fillOpacity: 0.3,
      strokeColor: color,
    })
    state.newPolygonList = hole
    state.miniMap.addOverlay(hole)
    //缩放合适的视野
    pointArray = pointArray.concat(hole.getPath())
    state.miniMap.setViewport(pointArray)
  }
  // 回显圆形区域
  const cricleData = async (data) => {
    let ponitList = data.split(',')
    let color = color16()
    let pointArray = []
    let newPonit = gcj02tobd09(ponitList[0], ponitList[1])
    var polygon = new BMapGL.Circle(new BMapGL.Point(newPonit[0], newPonit[1]), formData.value.radius, {
      strokeColor: color,
      fillColor: color, // 填充颜色。当参数为空时，圆形将没有填充效果。
      strokeWeight: 2,
      strokeOpacity: 1, // 边线透明度，取值范围0 - 1。
      fillOpacity: 0.2, // 填充的透明度，取值范围0 - 1。
    })
    //添加覆盖面到地图
    state.miniMap.addOverlay(polygon)
    //缩放合适的视野
    pointArray = pointArray.concat(polygon.getPath())
    state.miniMap.setViewport(pointArray)
  }

  /**
   * @description: 画多边形
   */
  const drawClick = (type) => {
    const styleOptions = {
      strokeColor: '#fa4444', // 边线颜色。
      fillColor: '#fa4444', // 填充颜色。当参数为空时，圆形将没有填充效果。
      strokeWeight: 2, // 边线的宽度，以像素为单位。
      strokeOpacity: 1, // 边线透明度，取值范围0 - 1。
      fillOpacity: 0.2, // 填充的透明度，取值范围0 - 1。
    }
    const labelOptions = {
      borderRadius: '2px',
      background: '#FFFBCC',
      border: '1px solid #E1E1E1',
      color: '#703A04',
      fontSize: '12px',
      letterSpacing: '0',
      padding: '5px',
    }
    // 实例化鼠标绘制工具
    const drawingManager = new BMapGLLib.DrawingManager(state.miniMap, {
      // isOpen: true,        // 是否开启绘制模式
      enableCalculate: false, // 绘制是否进行测距测面
      enableSorption: true, // 是否开启边界吸附功能
      sorptiondistance: 20, // 边界吸附距离
      polygonOptions: styleOptions, // 多边形的样式
      labelOptions: labelOptions, // label的样式
    })
    if (drawingManager.isOpen_ && drawingManager.getDrawingMode() === type) {
      drawingManager.close()
    } else {
      if (props.isEdit) {
        drawingManager.close()
      }
    }
    // 判断新增限行区域创建完毕后再次编辑
    if (state.overlays.length > 0) {
      var allOverlay = state.miniMap.getOverlays()
      if (allOverlay.length > 0) {
        state.newPolygonList = allOverlay[0]
        state.newPolygonList.enableEditing()
        state.newPolygonList.addEventListener('lineupdate', overlayComplete)
        state.isOpenEditing = true
      }
    } else {
      // 否则就是新增
      drawingManager.setDrawingMode(type)
      drawingManager.open()
      drawingManager.addEventListener('overlaycomplete', overlayComplete)
    }
    // 判断是否是编辑现行区域
    if (props.isEdit) {
      if (state.newPolygonList) {
        state.newPolygonList.enableEditing()
        state.newPolygonList.addEventListener('lineupdate', overlayComplete)
        state.isOpenEditing = true
      } else {
        // 否则就是新增
        drawingManager.setDrawingMode(type)
        drawingManager.open()
        drawingManager.addEventListener('overlaycomplete', overlayComplete)
      }
    }
  }

  /**
   * @description: 开始画圆形
   */
  const drawCircle = (type) => {
    const styleOptions = {
      strokeColor: '#fa4444', // 边线颜色。
      fillColor: '#fa4444', // 填充颜色。当参数为空时，圆形将没有填充效果。
      strokeWeight: 2, // 边线的宽度，以像素为单位。
      strokeOpacity: 1, // 边线透明度，取值范围0 - 1。
      fillOpacity: 0.2, // 填充的透明度，取值范围0 - 1。
      // strokeStyle: 'solid' //边线的样式，solid或dashed。
    }
    // 实例化鼠标绘制工具
    const drawingManager = new BMapGLLib.DrawingManager(state.miniMap, {
      isOpen: false, // 是否开启绘制模式
      enableDrawingTool: false, //是否显示工具栏
      enableCalculate: false, // 绘制是否进行测距测面
      drawingToolOptions: {
        anchor: BMAP_ANCHOR_TOP_RIGHT, //位置
        offset: new BMapGL.Size(5, 5), //偏离值
        drawingModes: [BMAP_DRAWING_CIRCLE],
        drawingTypes: [
          BMAP_DRAWING_CIRCLE, //圆的样式
        ],
      },
      circleOptions: styleOptions, //圆的样式
    })

    if (drawingManager.isOpen_ && drawingManager.getDrawingMode() === type) {
      drawingManager.close()
    } else {
      if (props.isEdit) {
        drawingManager.close()
      }
    }
    // 判断新增限行区域创建完毕后再次编辑
    if (state.overlays.length > 0) {
      var allOverlay = state.miniMap.getOverlays()
      if (allOverlay.length > 0) {
        state.newCircleList = allOverlay[0]
        state.newCircleList.enableEditing()
        state.newCircleList.addEventListener('lineupdate', overlayComplete)
        state.isOpenEditing = true
      }
    } else {
      // 否则就是新增
      drawingManager.open()
      drawingManager.setDrawingMode(BMAP_DRAWING_CIRCLE)
      drawingManager.addEventListener('overlaycomplete', overlayComplete)
    }
    // 判断是否是编辑现行区域
    if (props.isEdit) {
      if (state.newCircleList) {
        state.newCircleList.enableEditing()
        state.newCircleList.addEventListener('lineupdate', overlayComplete)
        state.isOpenEditing = true
      } else {
        drawingManager.open()
        drawingManager.setDrawingMode(BMAP_DRAWING_CIRCLE)
        drawingManager.addEventListener('overlaycomplete', overlayComplete)
      }
    }
  }

  // 结束编辑
  const drawEdit = (type) => {
    if (type == 'circle') {
      // 停止编辑
      state.newCircleList.disableEditing()
    } else {
      // 停止编辑
      state.newPolygonList.disableEditing()
    }
    // 隐藏结束编辑按钮
    state.isOpenEditing = false
  }

  /**
   * @description: 获取所画限行区的所有点坐标
   */
  const overlayComplete = (e) => {
    // 圆形
    if (e.drawingMode === 'circle' || state.activeName == 'left') {
      // 添加条件判断
      // 处理半径小于5的情况
      if (e.overlay.getRadius() < 5) {
        clearAllOverlay()
        return
      } else {
        state.overlays = []
        state.overlays.push(e.overlay)
        if (e.overlay.getCenter()) {
          let newCircle = bd09togcj02(e.overlay.getCenter().lng, e.overlay.getCenter().lat)
          // 处理中心点位置
          formData.value.coordinate = newCircle.join(',')
          // 圆形半径
          formData.value.radius = parseInt(e.overlay.getRadius())
        } else {
          formData.value.coordinate = '' // 清除中心点位
          formData.value.radius = '' // 清空半径
        }
      }
    }

    // 多边形
    if (e.drawingMode === 'polygon' || state.activeName == 'right') {
      state.overlays = []
      state.overlays.push(e.overlay)
      let points = []
      for (let i = 0; i < state.overlays.length; i++) {
        const overlay = state.overlays[i].getPath()
        for (let j = 0; j < overlay.length; j++) {
          const grid = overlay[j]
          let p = bd09togcj02(grid.lng, grid.lat)
          let point = p[0] + ',' + p[1]
          points.push(point)
        }
      }
      if (points.length > 0) {
        // 处理中心点
        var newPolygon = calculateCenter(points)
        // 多边形也要传递中心点位置
        formData.value.coordinate = newPolygon.lng + ',' + newPolygon.lat
        // 多边形数据
        formData.value.quyu = points.join(';')
      } else {
        // 多边形也要传递中心点位置
        formData.value.coordinate = '' // 清除中心点位
        formData.value.quyu = '' // 清空多边形点位
      }
    }
  }

  /**
   * @description: 清除自定义围栏
   * @return {*}
   */
  const clearAllOverlay = () => {
    //清除地图上所有覆盖物
    state.miniMap.clearOverlays()
    for (let i = 0; i < state.overlays.length; i++) {
      state.miniMap.removeOverlay(state.overlays[i])
    }
    state.overlays = []
    state.isOpenEditing = false
    state.newCircleList = null
    state.newPolygonList = null
    formData.value.coordinate = '' // 初始化中心点位
    formData.value.quyu = '' // 初始化限行数据
    formData.value.radius = '' // 初始化限行数据
  }

  // 围栏弹窗
  // const emit = defineEmits(["mapChild"]);

  onBeforeUnmount(() => {
    // 在组件销毁前手动释放地图实例
    mapObj.value && mapObj.value.destroy()
    state.miniMap = null
  })

  defineExpose({
    formData,
    state,
    mapObj,
    clearAllOverlay,
  })
</script>

<style scoped>
  #container {
    position: relative;
    width: 550px;
    height: 60vh;
  }

  .inquireInput {
    position: absolute;
    top: 10px;
    right: 300px;
    z-index: 99;
    width: 240px;
  }

  .inquireInput .inputWord {
    width: 100%;

    /* border: 1px solid #f5f5f5; */
    border-radius: 4px;
  }

  .input-card {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 99;
    display: flex;
    flex-direction: column;
    padding: 0.75rem 1.25rem;
    background-color: #fff;
    border-radius: 0.4rem;
    box-shadow: 0 2px 6px 0 rgb(114 124 245 / 50%);
  }

  .input-card :deep(.el-button) + .el-button {
    margin-left: 0;
  }

  .changeMapType :deep(.el-tabs__item) {
    padding: 0 12px;
    font-size: 12px;
  }
</style>
