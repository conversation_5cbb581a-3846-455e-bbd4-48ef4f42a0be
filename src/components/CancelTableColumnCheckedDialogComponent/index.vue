<!--
 * @Author: llm
 * @Date: 2025-05-08 18:06:18
 * @LastEditors: llm
 * @LastEditTime: 2025-05-09 10:29:06
 * @Description: 批量释放弹窗
-->
<template>
  <div>
    <el-button type="danger" icon="Delete" @click="showDialog">{{ props.btnText }}</el-button>
    <el-dialog v-model="state.dialog.visible" :title="props.btnText" width="40%" :draggable="true" :close-on-click-modal="false">
      <el-scrollbar height="300px" class="formClass">
        <el-form ref="ruleFormRef" label-width="120px" :inline="true" :model="state.formData" style="width: 93%; margin: 0 auto">
          <el-row>
            <el-col :span="24">
              <el-form-item label-width="80px" class="inputDeep" :label="targetField.label" prop="batchInput">
                <div style="position: relative; width: 100%; height: 100%">
                  <el-input
                    style="width: 400px"
                    v-model="state.formData.batchInput"
                    type="textarea"
                    :rows="4"
                    clearable
                    placeholder="批量搜索"
                    @click="openTextBatch('batchInput')"
                    @clear="emptyTextBatch(vinBatchRef)"
                  />
                  <div style="width: 100%; position: absolute; top: 0px; left: 0px; z-index: 9999">
                    <VINbatch
                      ref="vinBatchRef"
                      @arraySent="handleArrayReceived"
                      :showTextBatch="showTextBatch"
                      :targetField="targetField"
                      :closeTextBatch="closeTextBatch"
                      :initialValue="currentBatchValue"
                    />
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import VINbatch from '@/components/TopQueryGroupComponent/components/VINbatch.vue'
  const emits = defineEmits(['submitCancelTableColumnCheckedList'])
  const props = defineProps({
    targetField: {
      type: Object as PropType<{
        max: number
        message: string
        name?: string
        label?: string
      }>,
      default: () => ({}),
    },
    btnText: {
      type: String,
      default: '批量释放调度单',
    },
  })
  const state = reactive({
    dialog: {
      visible: false,
    },
    formData: {
      batchInput: '',
    },
  })
  const showTextBatch = ref(false)
  const currentBatchValue = ref('')
  const vinBatchRef = ref()
  const currentBatchField = ref('')
  function openTextBatch(name: keyof typeof state.formData) {
    currentBatchField.value = name
    currentBatchValue.value = state.formData[name] || '' // 保存当前值
    showTextBatch.value = true
  }

  const emptyTextBatch = (fieldRef: { list?: any[] }) => {
    if (fieldRef?.list && fieldRef.list.length > 0) {
      fieldRef.list = []
    }
  }
  const handleArrayReceived = (array: any, targetField: { name: keyof typeof state.formData }) => {
    state.formData[targetField.name] = Object.values(array).join(',')
    currentBatchValue.value = state.formData[targetField.name]
  }
  const closeTextBatch = () => {
    showTextBatch.value = false
  }
  const showDialog = () => {
    state.dialog.visible = true
  }
  const closeDialog = () => {
    state.dialog.visible = false
    state.formData.batchInput = ''
  }
  const handleSubmit = () => {
    emits('submitCancelTableColumnCheckedList', state.formData.batchInput)
  }

  defineExpose({
    state,
  })
</script>
