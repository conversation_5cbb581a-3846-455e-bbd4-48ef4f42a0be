<!--
 * @Author: llm
 * @Date: 2023-06-28 09:45:08
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-08-06 11:23:38
 * @Description: 待配板划分设置 表单组件
 *
-->
<template>
  <div>
    <el-scrollbar max-height="60vh" class="formClass">
      <el-form v-if="JSON.stringify(state.formData) != '{}'" :model="state.formData" label-width="120px" :style="{ width: '90%' }">
        <div class="flex justify-start items-center">
          <el-form-item label="标准装载位">
            <el-input-number v-model="state.formData.standardLoadCapacity" :min="1" :max="32" :precision="0" style="width: 200px" />
          </el-form-item>
          <el-form-item label="客户选择">
            <el-select
              v-model="state.formData.customerId"
              style="width: 200px"
              value-key="customerId"
              placeholder="请选择客户"
              @change="selectCustomerNo"
              filterable
            >
              <el-option v-for="item in state.customerListData" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </div>

        <el-form-item label="配载参数设置">
          <template v-if="state.formData.list">
            <div v-for="(item, index) in state.formData.list" :key="index">
              <div class="flex items-center justify-start configuration">
                <div class="items-left">
                  <el-select
                    v-model="item.vehicleModelId1"
                    style="width: 120px"
                    filterable
                    clearable
                    @change="selectChange(index, item.vehicleModelId1, item.vehicleModelId2, '1')"
                  >
                    <el-option v-for="(event, index) in state.vehicleModelOptions" :key="index" :label="event.label" :value="event.value"></el-option>
                  </el-select>
                  <el-input-number v-model="item.totalVinId1" :min="1" :max="32" :precision="0" style="width: 120px; margin-left: 10px" />
                </div>
                <div class="items-right">
                  <el-select
                    v-model="item.vehicleModelId2"
                    style="width: 120px"
                    filterable
                    clearable
                    @change="selectChange(index, item.vehicleModelId1, item.vehicleModelId2, '2')"
                  >
                    <el-option v-for="(event, index) in state.vehicleModelOptions" :key="index" :label="event.label" :value="event.value"></el-option>
                  </el-select>
                  <el-input-number v-model="item.totalVinId2" :min="1" :max="32" :precision="0" style="width: 120px; margin-left: 10px" />
                </div>
                <div style="margin-left: 20px">
                  <el-button type="primary" icon="Plus" size="small" circle @click="addTableItem" v-if="index === state.formData.list.length - 1" />
                  <el-button type="danger" icon="Delete" size="small" circle @click="deleteTableItem(item)" v-if="state.formData.list.length > 1" />
                </div>
              </div>
            </div>
          </template>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <div class="dialog-footer" v-if="JSON.stringify(state.formData) != '{}'">
      <div class="text-right">
        <el-button type="primary" @click="handleSubmit()">保存</el-button>
      </div>
    </div>
    <div v-else>
      <el-empty :image-size="100" description="暂无数据" />
    </div>
  </div>
</template>
<script setup lang="ts">
  import { plateAllocationProjectVO } from '@/api/waitingAllocation/type'
  import { vehicleModelSelectOptionApi, saveConfigurationApi } from '@/api/waitingAllocation/index'
  import { customerListApi } from '@/api/InTransitManagement'
  const props = defineProps({
    formData: {
      type: Object as PropType<any>,
      default: () => {
        return {}
      },
    },
  })

  watch(
    () => props.formData,
    (newState) => {
      // 赋值数据
      state.formData = newState
    },
    { deep: true },
  )

  /**
   * 表单参数
   */
  const state = reactive({
    customerListData: [] as any, //客户列表
    vehicleModelOptions: [] as any, //车辆型号
    formData: {
      id: '',
      standardLoadCapacity: 1,
      customerId: '',
      list: [
        {
          id: '',
          totalVinId1: 1,
          totalVinId2: 1,
          vehicleModelId1: '',
          vehicleModelId2: '',
        },
      ],
    } as plateAllocationProjectVO,
  })

  const currentCustomerId = ref('')
  const isConfirm = ref(true)

  onBeforeMount(async () => {
    getCustomerList() //获取客户
  })

  /**
   * 获取客户
   */
  const getCustomerList = async () => {
    customerListApi({ simple: true }).then((res) => {
      if (res.data && res.data.length > 0) {
        nextTick(() => {
          state.customerListData = res.data
          if (!state.formData.customerId) {
            state.formData.customerId = res.data[0].value
          }
          currentCustomerId.value = state.formData.customerId
          getCustomerVehicleModelOption(state.formData.customerId)
        })
      }
    })
  }

  const getCustomerVehicleModelOption = async (customerId: string) => {
    vehicleModelSelectOptionApi({ simple: true, relationId: customerId })
      .then((res) => {
        if (res && res.data) {
          state.vehicleModelOptions = res.data
        } else {
          state.vehicleModelOptions = []
        }
      })
      .catch(() => {})
  }

  // 选择客户
  const selectCustomerNo = async (value: any) => {
    // 只提示一次
    if (isConfirm.value) {
      ElMessageBox.confirm('变更客户将清空配载参数设置，是否确认变更？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          isConfirm.value = false
          getCustomerVehicleModelOption(value)
          state.formData.list = [
            {
              id: '',
              totalVinId1: 1,
              totalVinId2: 1,
              vehicleModelId1: '',
              vehicleModelId2: '',
            },
          ]
        })
        .catch(() => {
          state.formData.customerId = currentCustomerId.value
        })
    } else {
      getCustomerVehicleModelOption(value)
      state.formData.list = [
        {
          id: '',
          totalVinId1: 1,
          totalVinId2: 1,
          vehicleModelId1: '',
          vehicleModelId2: '',
        },
      ]
    }
  }

  /**
   * 提交加载动画
   */
  const submitLoading = ref(false)
  /**
   * 新增
   */
  const addTableItem = () => {
    state.formData.list.push({
      id: '',
      totalVinId1: 1,
      totalVinId2: 1,
      vehicleModelId1: '',
      vehicleModelId2: '',
    })
  }
  /**
   * 删除
   */
  const deleteTableItem = (row: any) => {
    state.formData.list.splice(state.formData.list.indexOf(row), 1)
  }

  /**
   * 对比已选择车型
   */
  const selectChange = (index: any, value1: any, value2: any, type: string) => {
    if (type === '1') {
      if (value1 === value2) {
        ElMessage.error('请选择不同的车型')
        state.formData.list[index].vehicleModelId1 = ''
        return
      }
    } else {
      if (value1 === value2) {
        ElMessage.error('请选择不同的车型')
        state.formData.list[index].vehicleModelId2 = ''
        return
      }
    }
  }

  /**
   * 提交表单
   */
  const handleSubmit = async () => {
    const formParams = JSON.parse(JSON.stringify(state.formData))
    formParams.list.map((item: any) => {
      item.totalVinId1 = Number(item.totalVinId1)
      item.totalVinId2 = Number(item.totalVinId2)
    })
    saveConfigurationApi(formParams).then((res) => {
      ElMessage.success('保存成功')
      sessionStorage.setItem('configDetail', JSON.stringify(formParams))
    })
  }

  // 暴露方法
  defineExpose({
    state,
  })
</script>

<style scoped>
  .configuration {
    width: 700px;
    margin-bottom: 8px;
  }
  .formClass :deep(.el-scrollbar__view) {
    display: flex;
    justify-content: space-between;
  }
  .formClass .items-left {
    margin-right: 40px;
  }

  :deep(.hide .el-upload--picture-card) {
    display: none;
  }
  :deep(.el-input.el-input--default.el-input--suffix),
  :deep(.el-input__wrapper) {
    width: 100% !important;
  }
</style>
