<!--
 * @Author: llm
 * @Date: 2025-03-20 18:10:44
 * @LastEditors: llm
 * @LastEditTime: 2025-04-02 16:23:27
 * @Description: 自定义打印按钮
-->
<template>
  <el-button type="primary" @click="handlePrint" :disabled="disabled" :loading="state.loading" v-if="showPrint">打印</el-button>
</template>

<script>
  import { getPrintTemplateConfigApi } from '@/api/Global'
  import { useGlobalPrintStore } from '@/store/modules/globalPrint'
  import { hiprint, defaultElementTypeProvider } from 'vue-plugin-hiprint'

  export default {
    name: 'CustomPrintButtonComponent',
    props: {
      disabled: {
        type: Boolean,
        default: false,
      },
      showPrint: {
        type: Boolean,
        default: true,
      },
      printId: {
        type: String,
        default: '',
      },
      requestUri: {
        type: String,
        default: '',
      },
      currentRow: {
        type: Object,
        default: () => ({}),
      },
    },
    data() {
      return {
        state: {
          loading: false,
          printDialogVisible: {
            visible: false,
            title: '打印',
          },
          previewDialogVisible: {
            visible: false,
            title: '打印预览',
          },
        },
        textareaContent: '',
        previewHtml: '',
        previewDiv: null,
        previewModal: null,
        iframeDom: null,
      }
    },
    mounted() {
      // Initialize hiprint when component is mounted
      hiprint.init({
        providers: [new defaultElementTypeProvider()],
      })
      // 还原配置
      hiprint.setConfig()
      // 替换配置
      hiprint.setConfig({
        movingDistance: 2.5,
        text: {
          supportOptions: [
            {
              name: 'styler',
              hidden: true,
            },
            {
              name: 'formatter',
              hidden: true,
            },
          ],
        },
      })
    },
    methods: {
      async handlePrint() {
        // 获取打印数据
        const { printConfigTemplate, printData } = await this.getPrintTemplateConfig()
        this.printFun(printConfigTemplate, printData)
      },
      printFun(printConfigTemplate, printData) {
        let hiprintTemplate = this.$print(
          undefined,
          printConfigTemplate,
          printData,
          {},
          {
            styleHandler: () => {
              let css = '<link href="/css/print-lock.css" media="print" rel="stylesheet">'
              return css
            },
          },
        )
      },
      async getPrintTemplateConfig() {
        const { data } = await getPrintTemplateConfigApi(this.currentRow.id, this.requestUri)
        const printConfigTemplate = JSON.parse(data.printConfigTemplate)
        const printData = data.printData
        return { printConfigTemplate, printData }
      },
    },
  }
</script>
