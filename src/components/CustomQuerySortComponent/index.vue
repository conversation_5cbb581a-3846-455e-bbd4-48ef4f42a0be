<!--
 * @Author: llm
 * @Date: 2025-01-03 17:10:07
 * @LastEditors: llm
 * @LastEditTime: 2025-05-20 10:26:25
 * @Description:自定义查询排序
-->
<template>
  <div>
    <el-dialog title="自定义查询排序" width="700px" v-model="state.visible" @close="handleCancel">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="mb-10px">隐藏项</h3>
          <el-checkbox-group v-model="state.fixedColumnsIds">
            <VueDraggable
              class="p-4px w-300px h-300px bg-gray-500/5 rounded overflow-auto"
              @add="onAdd1"
              v-model="state.fixedColumns"
              :animation="150"
              ghostClass="ghost"
              group="people"
            >
              <div v-for="(element, index) in state.fixedColumns" :key="element.columnId" class="w-100% my-4px px-4px">
                <el-checkbox :border="true" :label="element.columnId" :value="element.columnId" @change="(e: CheckboxValueType) => (element.highlight = e)">
                  {{ element.displayName }}
                </el-checkbox>
              </div>
            </VueDraggable>
          </el-checkbox-group>
        </div>
        <div>
          <h3 class="mb-10px">显示项</h3>
          <el-checkbox-group v-model="state.unFixedColumnsIds">
            <VueDraggable
              class="p-4px w-300px h-300px bg-gray-500/5 rounded overflow-auto"
              @add="onAdd2"
              v-model="state.unFixedColumns"
              :animation="150"
              ghostClass="ghost"
              group="people"
            >
              <div class="w-100% my-4px px-4px" v-for="(element, index) in state.unFixedColumns" :key="element.columnId">
                <el-checkbox :border="true" :label="element.columnId" @change="(e: CheckboxValueType) => (element.highlight = e)" :value="element.columnId">
                  {{ element.displayName }}
                </el-checkbox>
              </div>
            </VueDraggable>
          </el-checkbox-group>
        </div>
      </div>
      <div class="mt-20px">
        <el-tag type="warning">*向左拖拽隐藏查询项；显示项内拖拽调整到对应项前方位置；勾选常用查询项高亮显示！</el-tag>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="handleCancel">取 消</el-button>
          <el-button size="small" type="primary" @click="handleOk" :loading="state.confirmLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { getCustomTableSearchApi, postCustomTableSearchApi } from '@/api/auth'
  import { CheckboxValueType } from 'element-plus'
  import { reactive } from 'vue'
  import { VueDraggable } from 'vue-draggable-plus'
  const emit = defineEmits(['refreshPageTableColumn'])
  const props = defineProps({
    requestUri: {
      type: String,
      default: '',
    },
  })
  const state = reactive({
    visible: false,
    confirmLoading: false,
    fixedColumns: [] as ColumnVO[],
    unFixedColumns: [] as ColumnVO[],
    fixedColumnsIds: [] as string[],
    unFixedColumnsIds: [] as string[],
  })
  interface ColumnVO {
    columnId: string
    displayName: string
    highlight: CheckboxValueType
  }
  //获取自定义排序
  const getCustomTableSearch = async (uri: string) => {
    const { data } = await getCustomTableSearchApi(uri)
    state.fixedColumns = data.fixed
    state.unFixedColumns = data.unfixed
    state.fixedColumnsIds = data.fixed.filter((item: ColumnVO) => item.highlight).map((item: ColumnVO) => item.columnId)
    state.unFixedColumnsIds = data.unfixed.filter((item: ColumnVO) => item.highlight).map((item: ColumnVO) => item.columnId)
  }
  function onAdd1() {
    state.fixedColumnsIds = state.fixedColumns.filter((item: ColumnVO) => item.highlight).map((item: ColumnVO) => item.columnId)
  }
  function onAdd2() {
    state.unFixedColumnsIds = state.unFixedColumns.filter((item: ColumnVO) => item.highlight).map((item: ColumnVO) => item.columnId)
  }
  const handleCancel = () => {
    state.visible = false
  }
  const handleOk = async () => {
    state.confirmLoading = true
    const params = {
      fixed: state.fixedColumns,
      unfixed: state.unFixedColumns,
    }
    try {
      await postCustomTableSearchApi(props.requestUri, params)
      state.confirmLoading = false
      state.visible = false
      ElMessage.success('设置成功')
    } catch (error) {
      state.confirmLoading = false
      return
    }
    emit('refreshPageTableColumn')
  }
  defineExpose({
    state,
    getCustomTableSearch,
  })
</script>
<style lang="scss" scoped>
  :deep(.el-checkbox) {
    width: 100%;
  }
</style>
