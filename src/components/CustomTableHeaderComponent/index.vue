<!--
 * @Author: llm
 * @Date: 2025-01-03 17:10:07
 * @LastEditors: llm
 * @LastEditTime: 2025-01-08 10:08:21
 * @Description:自定义表头
-->
<template>
  <div>
    <el-dialog title="自定义表头" width="700px" v-model="state.visible" @close="handleCancel">
      <div class="box">
        <div class="left">
          <h3 class="freezetitle">冻结列</h3>
          <el-checkbox-group v-model="state.fixedColumnsIds">
            <VueDraggable class="freeze" @add="onAdd1" v-model="state.fixedColumns" :animation="150" ghostClass="ghost" group="people">
              <div v-for="(element, index) in state.fixedColumns" :key="element.columnId" class="item">
                <el-checkbox
                  size="default"
                  :border="true"
                  :label="element.columnId"
                  :value="element.columnId"
                  @change="(e: CheckboxValueType) => (element.displayable = e)"
                >
                  {{ element.displayName }}
                </el-checkbox>
              </div>
            </VueDraggable>
          </el-checkbox-group>
        </div>
        <div class="left">
          <h3 class="freezetitle">非冻结列</h3>
          <el-checkbox-group v-model="state.unFixedColumnsIds">
            <VueDraggable class="freeze" @add="onAdd2" v-model="state.unFixedColumns" :animation="150" ghostClass="ghost" group="people">
              <div class="item" v-for="(element, index) in state.unFixedColumns" :key="element.columnId">
                <el-checkbox
                  size="default"
                  :border="true"
                  :label="element.columnId"
                  @change="(e: CheckboxValueType) => (element.displayable = e)"
                  :value="element.columnId"
                >
                  {{ element.displayName }}
                </el-checkbox>
              </div>
            </VueDraggable>
          </el-checkbox-group>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="handleCancel">取 消</el-button>
          <el-button size="small" type="primary" @click="handleOk" :loading="state.confirmLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { getCustomTableHeaderApi, postCustomTableHeaderApi } from '@/api/auth'
  import { CheckboxValueType } from 'element-plus'
  import { reactive } from 'vue'
  import { VueDraggable } from 'vue-draggable-plus'
  const emit = defineEmits(['refreshPageTableColumn'])
  const props = defineProps({
    requestUri: {
      type: String,
      default: '',
    },
  })
  const state = reactive({
    visible: false,
    confirmLoading: false,
    fixedColumns: [] as ColumnVO[],
    unFixedColumns: [] as ColumnVO[],
    fixedColumnsIds: [] as string[],
    unFixedColumnsIds: [] as string[],
  })
  interface ColumnVO {
    columnId: string
    displayName: string
    displayable: CheckboxValueType
  }
  //获取自定义表头
  const getCustomTableHeader = async (uri: string) => {
    const { data } = await getCustomTableHeaderApi(uri)
    state.fixedColumns = data.fixed
    state.unFixedColumns = data.unfixed
    state.fixedColumnsIds = data.fixed.filter((item: ColumnVO) => item.displayable).map((item: ColumnVO) => item.columnId)
    state.unFixedColumnsIds = data.unfixed.filter((item: ColumnVO) => item.displayable).map((item: ColumnVO) => item.columnId)
  }
  function onAdd1() {
    state.fixedColumnsIds = state.fixedColumns.filter((item: ColumnVO) => item.displayable).map((item: ColumnVO) => item.columnId)
  }
  function onAdd2() {
    state.unFixedColumnsIds = state.unFixedColumns.filter((item: ColumnVO) => item.displayable).map((item: ColumnVO) => item.columnId)
  }
  const handleCancel = () => {
    state.visible = false
  }
  const handleOk = async () => {
    state.confirmLoading = true
    const params = {
      fixed: state.fixedColumns,
      unfixed: state.unFixedColumns,
    }
    try {
      await postCustomTableHeaderApi(props.requestUri, params)
      state.confirmLoading = false
      state.visible = false
      ElMessage.success('设置成功')
    } catch (error) {
      state.confirmLoading = false
      return
    }
    emit('refreshPageTableColumn')
  }
  defineExpose({
    state,
    getCustomTableHeader,
  })
</script>
<style lang="scss" scoped>
  :deep(.el-checkbox) {
    width: 100%;
  }
  .box {
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    width: 100%;
    .left {
      width: 300px;
      height: 300px;
      overflow: auto;
      border: 1px solid #f4f4f4;
      border-radius: 10px;
      .freezetitle {
        background: #f4f4f4;
        padding: 10px;
        border-radius: 10px 10px 0 0;
      }
      .freeze {
        padding: 10px;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        padding-bottom: 0;
        .item {
          margin-top: 8px;
        }
      }
    }
  }
  :deep(.el-checkbox__inner) {
    border-radius: 4px;
  }
  :deep(.el-checkbox.is-bordered.is-checked) {
    border-color: #b1d4ff;
  }
  :deep(.el-checkbox-group) {
    height: 400px !important;
  }
</style>
