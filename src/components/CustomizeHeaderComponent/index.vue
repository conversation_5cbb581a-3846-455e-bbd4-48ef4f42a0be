/* * @Author: llm * @Date: 2024-05-15 18:15:56 * @LastEditors: llm * @LastEditTime: 2024-05-16 17:31:45 * @Description: desc */
<template>
  <div>
    <el-dialog :draggable="true" v-model="showDialog" :title="title" width="1010px" :before-close="handleClose">
      <el-form :inline="true" :model="params" ref="ruleFormRef" :rules="rules">
        <el-form-item label="模板名称" v-if="isTemplate" prop="name">
          <el-input v-model="params.name" placeholder="请输入模板名称"></el-input>
        </el-form-item>
        <el-form-item label="备注" v-if="isTemplate">
          <el-input v-model="params.remark" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <div class="transfer flex" ref="transfer">
        <div class="left">
          <el-form :inline="true" :model="params" :rules="rules" ref="ruleFormRef1">
            <el-form-item label="选择报表" style="width: 220px" prop="type" v-if="isTemplate">
              <el-select v-model="params.type" @change="getMainDimension">
                <el-option v-for="item in formsTemplateTypeSelectOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="选择模板" style="width: 220px" v-else>
              <el-select v-model="templateId" @change="selectTemplate">
                <el-option v-for="item in templateSelectOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>

            <div class="flex">
              <div class="customize-panel">
                <div class="customize-panel-header">主维度</div>
                <div class="customize-panel-body">
                  <div class="list">
                    <el-radio-group v-model="params.mainDimension" @change="mainDimensionChange">
                      <div class="item" v-for="(item, index) in mainDimensionList" :key="index">
                        <el-radio :value="item.value">{{ item.label }}</el-radio>
                      </div>
                    </el-radio-group>
                  </div>
                </div>
              </div>
              <!-- <div class="w-134px"></div> -->
              <div class="customize-panel ml-20px">
                <div class="customize-panel-header">次维度(可多选)</div>
                <div class="customize-panel-body">
                  <div class="list">
                    <el-checkbox-group v-model="params.otherDimension" @change="changeOtherDimension">
                      <div class="item" v-for="(item, index) in otherDimension" :key="index">
                        <el-checkbox :value="item.value" :label="item.label" />
                      </div>
                    </el-checkbox-group>
                  </div>
                </div>
              </div>
            </div>
            <!-- <div class="mt-20px text-center">
						<el-button type="primary" @click="nextStep(ruleFormRef1)" :loading="prevBtnLoading">下一步</el-button>
					</div> -->
          </el-form>
        </div>
        <div class="right ml-20px" v-show="showNextStep">
          <el-form-item label="*将需要展示项移动到已选列表中" style="width: 220px" prop="type"></el-form-item>
          <el-transfer
            v-model="visibleValue"
            :data="indicatorList"
            :titles="titles"
            :props="{
              key: 'value',
              label: 'label',
            }"
            targetOrder="push"
          >
            <template #default="{ option }">
              <div class="pannel-content">
                <span :draggable="true" @dragstart="drag($event, option)">{{ option.label }}</span>
              </div>
            </template>
          </el-transfer>
          <!-- <div class="mt-20px text-center">
					<el-button type="primary" @click="showNextStep = false">上一步</el-button>
				</div> -->
        </div>
      </div>
      <template #footer v-if="showNextStep">
        <div class="dialog-footer">
          <el-button type="primary" @click="handleConfirm(ruleFormRef, false)">确 定</el-button>
          <el-button type="primary" @click="handleConfirm(ruleFormRef, true)" v-if="!isTemplate && !templateId">确定并保存</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 新增报表模板时保存为模板弹窗 -->
    <el-dialog :draggable="true" v-model="saveTemplateDialog" title="保存报表模板" width="500">
      <el-form :model="params" ref="ruleFormRef2" :rules="rules">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="params.name" autocomplete="off" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="params.remark" autocomplete="off" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="saveTemplateDialog = false">取消</el-button>
          <el-button type="primary" @click="saveTemplate(ruleFormRef2, true)">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import { FormInstance, TransferDataItem } from 'element-plus'
  import Sortable from 'sortablejs'
  import { ref, onMounted, computed } from 'vue'
  import { mainDimensionApi, otherDimensionByMainApi, formsTemplateTypeSelectOptionsApi, formsTemplateGetIndicatorByDimensionApi } from '@/api/statementCenter'
  import { globalRequestApi } from '@/api/planManagement'

  const emit = defineEmits(['handleCloseTableItem', 'handleConfirmVisible'])
  const ruleFormRef = ref()
  const ruleFormRef1 = ref()
  const ruleFormRef2 = ref()
  const rules = {
    name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
    type: [{ required: true, message: '请选择报表', trigger: 'change' }],
  }
  const props = withDefaults(
    defineProps<{
      data?: TransferDataItem[]
      titles?: [string, string]
      dialogVisible?: boolean
    }>(),
    {
      data: () => [],
      titles: () => ['待选列表', '已选列表'],
      dialogVisible: true,
    },
  )

  const templateId = ref('') //选择的模板id
  const showNextStep = ref(false)
  const isTemplate = ref(false) //是否为模板
  const params = ref({
    mainDimension: '',
    otherDimension: [],
    type: undefined,
    name: undefined, //模板名称
    remark: undefined, //备注
    id: undefined,
  })
  const title = ref()
  const formsTemplateTypeSelectOptions = ref()
  const showDialog = ref(false)
  const saveTemplateDialog = ref(false)
  const mainDimensionList = ref() //主维度
  const otherDimension = ref() //次维度
  const templateSelectOptions = ref() //模板下拉
  const visibleValue = ref([] as any)
  const draggingKey = ref(null as any)
  const transfer = ref<HTMLDivElement | null>(null)
  const drag = (ev: DragEvent, option: any) => {
    draggingKey.value = option.value
  }
  //获取主维度
  const getMainDimension = async () => {
    const { data } = await mainDimensionApi(params.value.type)
    mainDimensionList.value = data
  }
  //根据主维度获取次维度
  const mainDimensionChange = async (val: any) => {
    params.value.otherDimension = []
    const { data } = await otherDimensionByMainApi(params.value.type, val)
    otherDimension.value = data
    setTimeout(() => {
      nextStep(ruleFormRef1.value)
    }, 200)
  }
  //获取报表类型
  const getFormsTemplateTypeSelectOptions = async () => {
    const { data } = await formsTemplateTypeSelectOptionsApi()
    formsTemplateTypeSelectOptions.value = data
  }
  const templateInfo = ref()
  //选择模板
  const selectTemplate = (val: string) => {
    globalRequestApi(params, 'get', 'tms/report/forms/template/' + val).then(async (res) => {
      const { data } = res
      templateInfo.value = data
      //如果data.mainDimension类型是number则转成字符串
      if (typeof data.mainDimension === 'number') {
        data.mainDimension = data.mainDimension.toString()
      }
      params.value.mainDimension = data.mainDimension
      await mainDimensionChange(data.mainDimension)
      params.value.otherDimension = data.otherDimensionList
      setTimeout(() => {
        nextStep(ruleFormRef1.value)
      }, 200)
    })
  }

  //初始化穿梭框
  function initTransfer() {
    const leftPanel = transfer?.value?.getElementsByClassName('el-transfer-panel')[0].getElementsByClassName('el-transfer-panel__body')[0] as any
    const rightPanel = transfer?.value?.getElementsByClassName('el-transfer-panel')[1].getElementsByClassName('el-transfer-panel__body')[0] as any
    const leftEl = leftPanel?.getElementsByClassName('el-transfer-panel__list')[0]
    const rightEl = rightPanel?.getElementsByClassName('el-transfer-panel__list')[0]
    Sortable.create(rightEl, {
      onEnd: (evt: any) => {
        const { oldIndex, newIndex } = evt
        const temp = visibleValue.value[oldIndex]
        if (!temp || temp === 'undefined') return // 解决右边最后一项从右边拖左边，有undefined的问题
        visibleValue.value[oldIndex] = visibleValue.value[newIndex]
        visibleValue.value[newIndex] = temp
      },
    })
    Sortable.create(leftEl, {
      onEnd: (evt: any) => {
        const { oldIndex, newIndex } = evt
        if (oldIndex === newIndex) return
        const temp = indicatorList.value[oldIndex]
        if (!temp || (temp as any) === 'undefined') return // 解决右边最后一项从右边拖左边，有undefined的问题
        indicatorList.value[oldIndex] = indicatorList.value[newIndex]
        indicatorList.value[newIndex] = temp
      },
    })
    leftPanel.ondragover = (ev: any) => {
      ev.preventDefault()
    }
    leftPanel.ondrop = (ev: any) => {
      ev.preventDefault()
      const index = visibleValue.value.indexOf(draggingKey.value)
      if (index !== -1) {
        visibleValue.value.splice(index, 1)
      }
    }
    rightPanel.ondragover = (ev: any) => {
      ev.preventDefault()
    }
    rightPanel.ondrop = (ev: any) => {
      ev.preventDefault()
      if (visibleValue.value.indexOf(draggingKey.value) === -1) {
        visibleValue.value.push(draggingKey.value)
      }
    }
  }

  const prevBtnLoading = ref(false) //上一步按钮加载动画
  const nextBtnLoading = ref(false) //下一步按钮加载动画
  const indicatorList = ref() //指标列表
  const nextStep = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        if (!params.value.mainDimension) {
          ElMessage.info('请选择主维度')
          return
        }
        prevBtnLoading.value = true
        try {
          const { data } = await formsTemplateGetIndicatorByDimensionApi(params.value)
          prevBtnLoading.value = false
          showNextStep.value = true
          await nextTick(() => {
            initTransfer()
          })
          indicatorList.value = data
          await nextTick(() => {
            if (templateInfo.value) {
              visibleValue.value = templateInfo.value.indicatorList.map((item: any) => {
                return item.indicatorId
              })
            }
          })
        } catch (e) {
          prevBtnLoading.value = false
        }
      } else {
      }
    })
  }
  onMounted(() => {})

  //关闭弹框
  function handleClose() {
    emit('handleCloseTableItem')
  }

  //点击确定按钮
  async function handleConfirm(formEl: FormInstance | undefined, isSave: boolean = false) {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        if (visibleValue.value.length <= 0) {
          ElMessage({
            message: '未选中任何需要显示的数据',
            type: 'warning',
          })
          return
        }
        if (isSave) {
          saveTemplateDialog.value = true
        } else {
          //找出indicatorList.value 中 存在 visibleValue.value 中的数据
          const list = indicatorList.value.filter((item: any) => {
            return visibleValue.value.indexOf(item.value) !== -1
          })
          const _indicatorList = list.map((item: any) => {
            return {
              indicatorId: item.value,
              indicatorName: item.label,
            }
          })
          const _params = {
            indicatorList: _indicatorList,
            mainDimension: params.value.mainDimension,
            otherDimensionList: params.value.otherDimension,
            remark: params.value.remark,
            type: params.value.type,
            name: params.value.name,
          }
          emit('handleConfirmVisible', _params, isTemplate.value, isSave, params.value.id)
        }
      }
    })
  }

  async function saveTemplate(formEl: FormInstance | undefined, isSave: boolean = false) {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        //找出indicatorList.value 中 存在 visibleValue.value 中的数据
        const list = indicatorList.value.filter((item: any) => {
          return visibleValue.value.indexOf(item.value) !== -1
        })
        const _indicatorList = list.map((item: any) => {
          return {
            indicatorId: item.value,
            indicatorName: item.label,
          }
        })
        const _params = {
          indicatorList: _indicatorList,
          mainDimension: params.value.mainDimension,
          otherDimensionList: params.value.otherDimension,
          remark: params.value.remark,
          type: params.value.type,
          name: params.value.name,
        }
        emit('handleConfirmVisible', _params, isTemplate.value, isSave, params.value.id)
      }
    })
  }

  // 切换次维度
  function changeOtherDimension() {
    setTimeout(() => {
      nextStep(ruleFormRef1.value)
    }, 200)
  }

  defineExpose({
    showDialog,
    visibleValue,
    title,
    isTemplate,
    templateSelectOptions,
    params,
    mainDimensionChange,
    getFormsTemplateTypeSelectOptions,
    getMainDimension,
    selectTemplate,
  })
</script>
<style scoped lang="scss">
  :deep(.el-transfer__button) {
    width: 30px;
  }

  .left {
    --el-transfer-border-color: var(--el-border-color-lighter);
    --el-transfer-border-radius: var(--el-border-radius-base);
    --el-transfer-panel-width: 200px;
    --el-transfer-panel-header-height: 40px;
    --el-transfer-panel-header-bg-color: var(--el-fill-color-light);
    --el-transfer-panel-footer-height: 40px;
    --el-transfer-panel-body-height: 278px;
    --el-transfer-item-height: 30px;
    --el-transfer-filter-height: 32px;
    font-size: var(--el-font-size-base);
  }

  :deep(.el-radio-group) {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .customize-panel {
    background: var(--el-bg-color-overlay);
    box-sizing: border-box;
    display: inline-block;
    max-height: 100%;
    overflow: hidden;
    position: relative;
    text-align: left;
    vertical-align: middle;
    width: var(--el-transfer-panel-width);
  }

  .customize-panel-header {
    align-items: center;
    background: var(--el-transfer-panel-header-bg-color);
    border: 1px solid var(--el-transfer-border-color);
    border-top-left-radius: var(--el-transfer-border-radius);
    border-top-right-radius: var(--el-transfer-border-radius);
    box-sizing: border-box;
    color: var(--el-color-black);
    display: flex;
    height: var(--el-transfer-panel-header-height);
    margin: 0;
    padding-left: 15px;
  }

  .customize-panel-body {
    border-bottom: 1px solid var(--el-transfer-border-color);
    border-bottom-left-radius: var(--el-transfer-border-radius);
    border-bottom-right-radius: var(--el-transfer-border-radius);
    border-left: 1px solid var(--el-transfer-border-color);
    border-right: 1px solid var(--el-transfer-border-color);
    height: var(--el-transfer-panel-body-height);
    overflow: hidden;

    .list {
      box-sizing: border-box;
      height: var(--el-transfer-panel-body-height);
      list-style: none;
      margin: 0;
      overflow: auto;
      padding: 6px 0;

      .item {
        display: block !important;
        height: var(--el-transfer-item-height);
        line-height: var(--el-transfer-item-height);
        padding-left: 15px;
      }
    }
  }

  :deep(.el-checkbox) {
    margin-right: 0;
  }

  :deep(.el-transfer-panel) {
    width: 220px;
  }

  :deep(.el-transfer__buttons) {
    padding: 0 10px;
  }
</style>
