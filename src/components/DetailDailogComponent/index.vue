<!--
 * @Author: llm
 * @Date: 2023-10-06 11:32:26
 * @LastEditors: llm
 * @LastEditTime: 2023-10-06 12:19:49
 * @Description: table 详情弹窗
 *
-->
<template>
  <div>
    <el-dialog destroy-on-close v-model="detailDialogVisible" title="查看" width="80%" align-center>
      <BasePage1 />
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import BasePage1 from '@/views/Pages/basePage1.vue'
  const detailDialogVisible = ref(false)
  defineExpose({
    detailDialogVisible,
  })
</script>
