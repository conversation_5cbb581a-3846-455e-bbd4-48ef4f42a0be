<!--
 * @Author: 周宗文
 * @Date: 2024-12-26 16:48:06
 * @LastEditors: 周宗文
 * @LastEditTime: 2025-01-03 15:07:13
 * @Description:
-->
<template>
  <el-drawer v-model="props.modelValue" :title="props.title" :append-to-body="true" @close="close" size="600">
    <slot></slot>
    <div class="drawer-footer">
      <slot name="footer"></slot>
    </div>
  </el-drawer>
  <!-- <Teleport to="body" v-if="props.modelValue">
    <div class="drawer">
      <div class="drawer-header" v-if="!props.hideHeader">
        <svg-icon :icon-class="props.icon" class="drawer-header-icon" color="#5a5e66" v-if="props.icon" />
        <div class="drawer-header-title">{{ props.title }}</div>
        <svg-icon icon-class="close" class="drawer-header-close" color="#5a5e66" @click="close" v-if="!props.hideCloseBtn" />
      </div>
      <div class="drawer-body">
        <slot></slot>
      </div>
      <div class="drawer-footer">
        <slot name="footer"></slot>
      </div>
    </div>
    <div class="drawer-overlay"></div>
  </Teleport> -->
</template>

<script setup name="Drawer">
  import { ref, reactive, onMounted, onUnmounted, getCurrentInstance } from 'vue'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: null,
    },
    icon: {
      type: String,
      default: null,
    },
    'z-index': {
      type: Number,
      default: 2000,
    },
    hideHeader: {
      // 控制是否显示 header 栏, 默认为 false
      type: Boolean,
      default: false,
    },
    hideCloseBtn: {
      // 控制是否显示 关闭按钮, 默认为 false
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: '500px',
    },
  })

  const { proxy } = getCurrentInstance()

  onMounted(() => {
    window.addEventListener('keydown', escListener)
  })

  onUnmounted(() => {
    window.removeEventListener('keydown', escListener)
  })

  // 监听ESC事件
  const escListener = (event) => {
    if (event.keyCode === 27) {
      close()
    }
  }

  const emit = defineEmits(['update:modelValue', 'close'])
  const close = () => {
    // proxy.$emit("update:modelValue", false)
    emit('update:modelValue', false)
    emit('close')
  }
</script>

<style lang="scss" scoped>
  // .drawer {
  //   position: fixed;
  //   width: v-bind('props.width');
  //   height: 100%;
  //   background-color: #ffffff;
  //   box-shadow: -5px -5px 10px 2px rgba(0, 0, 0, 0.2);
  //   top: 0;
  //   right: 0;
  //   z-index: v-bind(2090);
  //   display: flex;
  //   flex-direction: column;
  // }

  // .drawer-header {
  //   padding: 16px;
  //   border-bottom: 1px solid #cacaca;
  //   min-height: 30px;
  //   display: flex;
  //   flex-direction: row;
  //   width: 100%;
  //   align-items: center;
  //   box-sizing: border-box;

  //   .drawer-header-icon {
  //     width: 20px;
  //     height: 20px;
  //     font-size: 16px;
  //     padding-right: 10px;
  //   }
  //   .drawer-header-title {
  //     flex: 1;
  //     color: #5a5e66;
  //     font-size: 16px;
  //     font-weight: bold;
  //   }
  //   .drawer-header-close {
  //     width: 20px;
  //     height: 20px;
  //     font-size: 16px;
  //     cursor: pointer;
  //   }
  // }

  // .drawer-body {
  //   flex: 1;
  //   padding: 16px;
  //   overflow-y: auto;
  // }

  .drawer-footer {
    display: flex;
    flex-direction: row;
    width: 100%;
    align-items: center;
    box-sizing: border-box;
    justify-content: right;
    position: absolute;
    bottom: 20px;
    right: 20px;
  }
  // .drawer-overlay {
  //   position: fixed;
  //   top: 0;
  //   right: 0;
  //   bottom: 0;
  //   left: 0;
  //   z-index: v-bind(2089);
  //   height: 100%;
  //   background-color: rgba(0, 0, 0, 0.5);
  //   overflow: auto;
  // }
</style>
