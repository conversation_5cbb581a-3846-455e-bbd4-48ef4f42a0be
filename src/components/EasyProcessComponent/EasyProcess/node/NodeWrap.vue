<!--
 * @Author: 周宗文
 * @Date: 2024-12-26 16:48:06
 * @LastEditors: 周宗文
 * @LastEditTime: 2025-01-02 15:21:05
 * @Description:
-->
<template>
  <div class="ep-node-wrap" v-if="props.node">
    <!-- 路由节点 -->
    <RouterNode :node="props.node" @removeNode="removeNode" v-if="props.node.nodeType == ROUTER" />
    <!-- 普通节点 -->
    <BaseNode :node="props.node" :conditionNodes="props.conditionNodes" :conditionIndex="props.conditionIndex" @removeNode="removeNode" v-else />
    <!-- 子节点 -->
    <NodeWrap :node="props.node.childNode" @removeNode="removeChildNode" v-if="props.node.childNode && props.node.childNode.nodeType" />
  </div>
</template>

<script setup name="NodeWrap">
  import BaseNode from './base/BaseNode.vue'
  import RouterNode from './router/RouterNode'
  import { ref, onMounted, getCurrentInstance } from 'vue'
  import { ROUTER } from '../config/nodeType'
  const props = defineProps({
    node: {
      // 当前流程节点数据
      type: Object,
      default: {},
    },
    conditionNodes: {
      // 条件集合，当节点类型为condition时有效
      type: Array,
      default: [],
    },
    conditionIndex: {
      // 当前条件节点的顺序，当节点类型为condition时有效
      type: Number,
      default: 0,
    },
    canRemoved: {
      // 当前节点是否可以移除
      type: Boolean,
      default: true,
    },
  })

  const { proxy } = getCurrentInstance()

  watch(
    () => props.node,
    (val) => {
      emit('outFleetData', val)
    },
    {
      deep: true,
    },
  )

  // 流程配置数据
  let config = ref({})

  onMounted(async () => {})

  // 移除当前节点
  const emit = defineEmits(['removeNode', 'outFleetData'])
  const removeNode = () => {
    emit('removeNode')
  }

  // 移除子节点
  const removeChildNode = () => {
    let nextChildNode = props.node.childNode.childNode
    props.node.childNode = nextChildNode
  }
</script>

<style lang="scss" scoped></style>
