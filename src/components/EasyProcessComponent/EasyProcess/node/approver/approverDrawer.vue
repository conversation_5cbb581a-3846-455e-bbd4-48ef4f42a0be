<!--
 * @Author: 周宗文
 * @Date: 2024-12-26 16:48:06
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-03-11 11:43:40
 * @Description:
-->
<template>
  <!-- 审批人配置 -->
  <div>
    <el-form :model="props.config">
      <el-form-item label="审批人">
        <el-select multiple collapse-tags filterable v-model="props.config.users" style="width: 220px" placeholder="请选择审批人">
          <el-option v-for="(item, index) in userSelectOptions" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="审批方式">
        <el-radio-group v-model="props.config.multi">
          <el-radio label="joint">会签（需要所有审批人都通过）</el-radio>
          <el-radio label="single">或签（其中一名审批人通过即可）</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup name="ApproverDrawer">
  import { getCurrentInstance, inject } from 'vue'
  import { KEY_PROCESS_DATA } from '../../config/keys'
  import { getUserSelectOptionApi } from '@/api/GlobalMenu/index'

  const props = defineProps({
    config: {
      // 传入的流程配置数据
      type: Object,
      default: {},
    },
  })

  let userSelectOptions = ref([])

  const { proxy } = getCurrentInstance()

  onMounted(async () => {
    getUserSelectOptionList()
  })

  //获取审批人下拉
  const getUserSelectOptionList = async () => {
    const { data } = await getUserSelectOptionApi({})
    userSelectOptions.value = data
  }

  // 获取流程数据
  const processData = inject(KEY_PROCESS_DATA)
</script>

<style lang="scss" scoped></style>
