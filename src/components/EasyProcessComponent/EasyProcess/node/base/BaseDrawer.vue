<!--
 * @Author: 周宗文
 * @Date: 2024-12-26 16:48:06
 * @LastEditors: 周宗文
 * @LastEditTime: 2025-01-03 11:45:52
 * @Description: 弹窗
-->
<template>
  <div v-if="node" class="ep-node-drawer-container">
    <Drawer v-model="isShow" :title="`${config.title}设置`" icon="setting" width="600px">
      <template #default>
        <component :is="drawerComponents[node.nodeType]" :config="node.config" />
      </template>
      <template #footer>
        <Button @click="cancelUpdateConfig">取消</Button>
        <Button type="primary" @click="updateConfig">确定</Button>
      </template>
    </Drawer>
  </div>
</template>
<script setup name="BaseDrawer">
  import Drawer from '@/components/EasyProcessComponent/Drawer/Drawer.vue'
  import Button from '@/components/EasyProcessComponent/Button/Button.vue'
  import { ref, shallowRef, getCurrentInstance, defineAsyncComponent } from 'vue'
  import { nodeConfig } from '../../config/nodeConfig'
  import { copy } from '../../utils/tools'

  const props = defineProps({})

  const { proxy } = getCurrentInstance()

  // 节点数据的副本
  let node = ref(null)
  // 节点配置数据
  const config = ref(null)
  // 是否显示配置界面
  let isShow = ref(false)

  // 加载节点抽屉组件
  const modules = import.meta.glob('../*/*Drawer.vue')
  const drawerComponents = shallowRef({})
  Object.keys(nodeConfig).forEach((key) => {
    let item = nodeConfig[key]
    if (item.hasDrawer) {
      let component = defineAsyncComponent(modules[`../${key}/${key}Drawer.vue`])
      drawerComponents.value[key] = component
    }
  })

  // 显示节点配置组件
  const show = (data) => {
    // 复制数据
    node.value = copy(data)
    config.value = nodeConfig[node.value.nodeType]

    isShow.value = true
  }

  const emit = defineEmits(['updateConfig', 'cancelUpdateConfig'])

  // 更新节点配置数据
  const updateConfig = () => {
    isShow.value = false
    emit('updateConfig', copy(node.value.config))
  }

  // 取消更新节点配置数据
  const cancelUpdateConfig = () => {
    isShow.value = false
    emit('cancelUpdateConfig')
  }

  defineExpose({
    show,
  })
</script>

<style lang="scss" scoped>
  .ep-node-drawer {
  }
</style>
