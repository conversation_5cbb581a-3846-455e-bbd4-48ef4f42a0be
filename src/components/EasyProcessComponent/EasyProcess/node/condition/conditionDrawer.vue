<template>
  <!-- 条件配置 -->
  <div>
    <!-- <el-input v-model="props.config.days" /> -->
    <div style="display: flex; align-items: center; justify-content: space-between">
      <el-input-number style="width: 140px" v-model="props.config.leftValue" size="default" :min="0" :precision="2" />
      <el-select v-model="props.config.leftOp" style="width: 75px" placeholder="符号">
        <el-option v-for="(item, index) in conditionList" :key="index" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <el-tag style="width: 50px" type="info" effect="plain" size="large">金额</el-tag>
      <el-select v-model="props.config.rightOp" style="width: 75px" placeholder="符号">
        <el-option v-for="(item, index) in conditionList" :key="index" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <el-input-number style="width: 140px" v-model="props.config.rightValue" size="default" :min="0" :precision="2" />
    </div>
  </div>
</template>

<script setup name="ConditionDrawer">
  import { getCurrentInstance, inject } from 'vue'
  import { KEY_PROCESS_DATA } from '../../config/keys'

  const conditionList = ref([
    {
      label: '=',
      value: '=',
    },
    {
      label: '<',
      value: '<',
    },
    {
      label: '≤',
      value: '≤',
    },
  ])

  const props = defineProps({
    config: {
      // 传入的流程配置数据
      type: Object,
      default: {},
    },
  })

  const { proxy } = getCurrentInstance()

  // 获取流程数据
  const processData = inject(KEY_PROCESS_DATA)
</script>

<style scoped>
  :deep(.el-select__popper) {
    z-index: 3000;
  }
</style>
