<template>
  <!-- 条件节点 -->
  <div class="ep-node-condition">
    <div v-if="!props.node.isLastCondition">
      {{ node.config.leftValue || node.config.leftValue == 0 ? node.config.leftValue + '元' : '' }} {{ node.config.leftOp }} 金额 {{ node.config.rightOp }}
      {{ node.config.leftValue || node.config.leftValue == 0 ? node.config.rightValue + '元' : '' }}
    </div>
    <div v-else>其他条件进入此流程</div>
  </div>
</template>

<script setup name="ConditionNode">
  import { getCurrentInstance, inject } from 'vue'
  import { KEY_PROCESS_DATA, KEY_VALIDATOR } from '../../config/keys'

  const props = defineProps({
    tempNodeId: {
      // 临时节点ID
      type: String,
    },
    node: {
      // 传入的流程配置数据
      type: Object,
      default: {},
    },
  })

  const { proxy } = getCurrentInstance()

  // 获取流程数据
  const processData = inject(KEY_PROCESS_DATA)
  // 获取流程验证器实例
  const validator = inject(KEY_VALIDATOR)

  // 注册验证器
  validator.register(props.tempNodeId, () => {
    let valid = true
    if (!props.node.isLastCondition) {
      if (!props.node.config.leftValue || !props.node.config.rightValue) {
        valid = false
      }
    }
    return {
      valid: valid,
      message: '请选择条件',
    }
  })
</script>

<style lang="scss" scoped>
  .ep-node-condition {
  }
</style>
