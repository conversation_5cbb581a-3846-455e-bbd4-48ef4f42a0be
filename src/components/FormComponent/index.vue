<!--
 * @Author: llm
 * @Date: 2023-06-28 09:45:08
 * @LastEditors: llm
 * @LastEditTime: 2025-06-21 11:40:34
 * @Description: 表单组件
 *
-->
<template>
  <div>
    <el-scrollbar max-height="60vh" class="formClass">
      <el-form ref="formRef" :model="formData" :inline="true" label-width="80px" :style="{ width: showMap === 'geoFence' ? 'auto' : '90%' }">
        <template v-for="(item, index) in filterDataColumn">
          <template v-if="(item?.insertEnable && !props.isEdit) || (item?.updateEnable && props.isEdit)">
            <!-- 如果 dependsOn 不存在 或者存在 并且dependsOn中的条件同时满足并且canShow=true 后不存在 再显示-->
            <el-form-item
              :label="item.form?.label"
              :prop="item.form?.name"
              :rules="item.form?.rules"
              :key="index"
              label-width="140px"
              v-if="
                (!item.form?.dependsOn ||
                  item.form.dependsOn.every((depend: DependsOn) => {
                    if (!depend.operator) {
                      depend.operator = 'eq'
                    }
                    var target = formData[depend.field!]
                    var when = depend.when!
                    return operatorCalculate(target, when, depend.operator)
                  })) &&
                (item.form?.canShow === true || !item.form)
              "
            >
              <div class="flex-row items-center" style="width: 100%">
                <!-- textarea -->
                <el-input
                  v-model="formData[item.form.name!]"
                  type="textarea"
                  :rows="5"
                  :disabled="!item.form?.canEdit"
                  :placeholder="item.form.placeholder ? item.form.placeholder : '请输入' + item.form.label"
                  v-if="item.form?.type === 'textarea'"
                />
                <!-- location 地址 -->
                <el-input
                  v-model="formData[item.form.name!]"
                  type="text"
                  :readonly="item.form?.canEdit"
                  :placeholder="item.form.placeholder ? item.form.placeholder : '请输入' + item.form.label"
                  v-else-if="item.form?.type === 'location'"
                  @click.native.stop="locationAddress(item.form)"
                />
                <!-- 数字输入框 -->
                <el-input-number
                  v-model="formData[item.form.name!]"
                  controls-position="right"
                  style="width: 100px"
                  :min="item.form?.min"
                  :max="item.form?.max"
                  :step="item.form?.step"
                  :disabled="item.canEdit"
                  v-else-if="item.form?.type === 'number'"
                />
                <!-- 开关 -->
                <el-switch
                  v-model="formData[item.form.name!]"
                  inline-prompt
                  active-text="启用"
                  inactive-text="禁用"
                  :disabled="item.canEdit"
                  v-else-if="item.form?.type === 'switch'"
                />
                <!-- 下拉选择 -->
                <el-select
                  v-model="formData[item.form.name!]"
                  style="width: 100%"
                  :value-key="item.form.name"
                  :multiple="item.form?.option?.multiple"
                  :collapse-tags="item.form?.option?.multiple"
                  :collapse-tags-tooltip="item.form?.option?.multiple"
                  :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                  :disabled="item.canEdit"
                  filterable
                  clearable
                  v-else-if="item.form?.type === 'select'"
                >
                  <el-option v-for="i in item.form?.option?.data" :key="i.value" :label="i.label" :value="i.value" />
                </el-select>
                <!-- 下拉树 -->
                <el-tree-select
                  v-model="formData[item.form.name!]"
                  default-expand-all
                  filterable
                  style="width: 100%"
                  :multiple="item.form?.option?.multiple"
                  :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                  :data="item.form.option?.data"
                  :props="{ value: 'value', label: 'label' }"
                  :disabled="item.canEdit"
                  clearable
                  :style="{ width: item.form?.width || '100%' }"
                  v-else-if="item.form?.type === 'selectTree'"
                >
                  <template #default="{ data }">{{ data.label }}</template>
                </el-tree-select>

                <!-- 级联选择器-省市区 -->
                <el-cascader
                  style="width: 100%"
                  v-model="formData[item.form.name!]"
                  filterable
                  collapse-tags
                  collapse-tags-tooltip
                  :options="item.form!.option?.data"
                  :props="{
                    multiple: item.form.option?.multiple,
                    checkStrictly: item.form!.option?.checkStrictly,
                  }"
                  clearable
                  :style="{ width: item.form?.width || '100%' }"
                  v-else-if="item.form?.type === 'cascader'"
                />
                <!-- 日区间、月区间-->
                <el-date-picker
                  v-model="formData[item.form.name as keyof TableItem] as any"
                  :type="item.form?.type"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                  :format="item.form.format"
                  :value-format="item.form.format"
                  clearable
                  :style="{ width: item.form?.width || '100%' }"
                  v-else-if="item.form?.type === 'daterange' || item.form?.type === 'monthrange'"
                />
                <!-- 周、年、月、日 -->
                <el-date-picker
                  v-model="formData[item.form.name as keyof TableItem] as any"
                  :type="item.form?.type"
                  :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                  :format="item.form.format"
                  :value-format="item.form.format"
                  v-else-if="item.form?.type === 'week' || item.form?.type === 'year' || item.form?.type === 'month' || item.form?.type === 'date'"
                  clearable
                  :style="{ width: item.form?.width || '100%' }"
                />

                <!-- 时分秒 -->
                <el-time-picker
                  v-model="formData[item.form.name!]"
                  :format="item.form.format"
                  :value-format="item.form.format"
                  :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                  :disabled="item.canEdit"
                  v-else-if="item.form?.type === 'time'"
                  clearable
                  :style="{ width: item.form?.width || '200px' }"
                />
                <!--  时分秒-时分秒 -->
                <el-time-picker
                  v-model="formData[item.form.name!]"
                  is-range
                  :format="item.form.format"
                  :value-format="item.form.format"
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  v-else-if="item.form?.type === 'timerange'"
                  clearable
                  :style="{ width: item.form?.width || '100%' }"
                />

                <!-- 年月日时分秒 -->
                <el-date-picker
                  v-model="formData[item.form.name!]"
                  type="datetime"
                  :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                  :format="item.form.format"
                  :value-format="item.form.format"
                  :disabled="item.canEdit"
                  v-else-if="item.form?.type === 'datetime'"
                  clearable
                  :style="{ width: item.form?.width || '100%' }"
                />
                <!-- 时间选择 -->
                <el-time-select
                  v-model="formData[item.form!.name!]"
                  start="08:30"
                  step="00:15"
                  end="18:30"
                  :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                  v-else-if="item.form?.type === 'timeSelect'"
                  clearable
                  :style="{ width: item.form?.width || '100%' }"
                />
                <!-- 单选 -->
                <el-radio-group v-model="formData[item.form.name!]" :disabled="!item.form.canEdit" v-else-if="item.form?.type === 'radiogroup'">
                  <el-radio :label="_item.value" v-for="_item in item.form.option">{{ _item.label }}</el-radio>
                </el-radio-group>
                <!-- 图片上传 -->
                <el-upload
                  v-else-if="item.form?.type === 'uploadImage'"
                  ref="uploadImageRef"
                  :class="{ hide: formData[item.form?.name!] && formData[item.form?.name!].length >= item.form.imageOption?.count! }"
                  :auto-upload="false"
                  :file-list="formData[item.form?.name!] ?? []"
                  :limit="item.form.imageOption?.count"
                  :on-change="
                    (file: any, fileList: any[]) => {
                      uploadImage(file, fileList, item)
                    }
                  "
                  :on-preview="handlePictureCardPreview"
                  :disabled="props.isEdit ? item.form?.canEdit === false || !item.form : item.form?.canInsert === false || !item.form"
                  list-type="picture-card"
                  action="#"
                >
                  <el-icon><Plus /></el-icon>
                </el-upload>
                <!-- 文件上传 -->
                <el-upload
                  :file-list="formData[item.form?.name!] ? [{ name: formData[item.form?.name!].replace('Url', ''), url: formData[item.form?.name!] }] : []"
                  v-else-if="item.form?.type === 'uploadFile'"
                  ref="uploadFileRef"
                  action="#"
                  :auto-upload="false"
                  :limit="item.form.fileOption?.count"
                  :on-change="
                    (file: any, fileList: any[]) => {
                      uploadFile(file, fileList, item)
                    }
                  "
                  :on-remove="
                    (file: any, fileList: any[]) => {
                      handleRemove(file, fileList, item)
                    }
                  "
                  :on-exceed="handleExceed"
                  :disabled="props.isEdit ? item.form?.canEdit === false || !item.form : item.form?.canInsert === false || !item.form"
                >
                  <template #trigger>
                    <el-button type="primary">点击上传</el-button>
                  </template>
                </el-upload>
                <!-- 默认都是input -->
                <el-input
                  :style="{ width: item.form?.width || '100%' }"
                  v-model="formData[item.form?.name!]"
                  :placeholder="item.form!.placeholder ? item.form!.placeholder : '请输入' + item.form!.label"
                  :disabled="props.isEdit ? item.form?.canEdit === false || !item.form : item.form?.canInsert === false || !item.form"
                  :type="item.form?.type === 'inputNumber' ? 'number' : 'input'"
                  clearable
                  v-else
                >
                  <template #append v-if="item.form?.units">
                    <el-select v-model="formData[item.form!.name! + 'Unit']" placeholder="请选择" style="width: 88px">
                      <el-option :label="_item.name" :value="_item.value" v-for="(_item, _index) in item.form?.units" :key="_index" />
                    </el-select>
                  </template>
                </el-input>
                <!-- 单位 -->
                <div class="ml-2" v-if="item.form?.unit">
                  {{ item.form?.unit }}
                </div>
                <el-tooltip effect="light" :content="item.form?.tips" placement="top" v-if="item.form?.tips">
                  <el-icon color="#f3d19e" style="margin-left: 10px; cursor: pointer"><WarningFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
          </template>
        </template>
      </el-form>
      <div v-if="showMap === 'geoFence'" class="ml-2">
        <area-map-component
          :isEdit="props.isEdit"
          :height="formHeight"
          :enclosureData="{
            coordinate: formData.coordinate,
            quyu: formData.quyu,
            radius: formData.radius,
          }"
          ref="rightAreaMapComponent"
        ></area-map-component>
      </div>
    </el-scrollbar>
    <div class="dialog-footer">
      <!-- 如果form存在并且formUri有值则使用数据返回的按钮，否则使用默认按钮 -->
      <div v-if="btnMenu.meta?.form?.btns">
        <div v-for="(item, index) in btnMenu.meta?.form?.btns" :key="index" class="inline-block text-right">
          <el-button
            class="mr-2"
            :color="item.background"
            v-loading="submitLoading"
            :data-uri="item.uri"
            @click="item.label === '取消' ? closeDialog() : handleSubmit(formRef, item)"
            v-if="
              !item.dependsOn ||
              (item.dependsOn &&
                item.dependsOn.every((depend: DependsOn) => {
                  //遍历item.dependsOn->depend，如果depend.field==='listData'的话selectTableColumn[0][depend.field!] === depend.when并且depend.field==='responseData'的话formData[depend.field!] === depend.when再显示
                  var target = ''
                  if (depend.dependFrom === 'listData') {
                    target = selectTableColumn[0][depend.field!]
                  } else if (depend.dependFrom === 'responseData') {
                    target = formData[depend.field!]
                  }
                  if (!depend.operator) {
                    depend.operator = 'eq'
                  }
                  var when = depend.when!
                  return operatorCalculate(target, when, depend.operator)
                }))
            "
          >
            {{ item.label }}
          </el-button>
        </div>
      </div>
      <div v-else class="text-right">
        <el-button type="primary" @click="handleSubmit(formRef)" :data-uri="requestUri">确定</el-button>
        <el-button @click="refresh">刷 新</el-button>
      </div>
    </div>
    <!-- 地图位置搜索 -->
    <!-- :pointData="{ coordinate: formData.coordinate, address: formData.address }" -->
    <map-search-dialog
      v-if="searchDialog.visible"
      :dialog="searchDialog"
      :pointData="formData"
      @closeMapDialog="closeMapDialog"
      @submitLocation="submitLocation"
    ></map-search-dialog>
    <!-- 图片查看弹窗 -->
    <pic-dialog-component ref="picDialogRef" :imageList="dialogImageUrl"></pic-dialog-component>
  </div>
</template>
<script setup lang="ts">
  import { cloneDeep } from 'lodash'
  import AreaMapComponent from '@/components/AreaMapComponent/index.vue'
  import MapSearchDialog from '@/components/MapSearchDialog/index.vue'
  import PicDialogComponent from '@/components/PicDialogComponent/index.vue'
  import { PropType } from 'vue'
  // 状态管理依赖
  import { useButtonGroupStore } from '@/store/modules/buttonGroup.js'
  import { getBase64 } from '@/utils'
  import { operatorCalculate } from '@/utils/common'
  import { uploadApi, uploadFileApi } from '@/api/auth'
  import { UploadInstance, UploadProps, UploadRawFile, genFileId } from 'element-plus'
  import { UploadImageVO } from '@/types/global'
  const formHeight = ref('') //左侧表单高度（让右侧地图高度=表单高度）
  const uploadFileRef = ref()
  const buttonGroupStore = useButtonGroupStore()
  /**
   * 是否展示地图 值为geoFence显示
   */
  const { showMap } = storeToRefs(buttonGroupStore)
  const props = defineProps({
    /**
     * 提交表单后是否需要刷新菜单数
     */
    refreshMenuCount: {
      require: true,
      type: Boolean,
      default: false,
    },
    /**
     * 当前是否为编辑状态
     */
    isEdit: {
      require: true,
      type: Boolean,
      default: false,
    },
    /**
     * 弹窗属性
     */
    dialog: {
      require: true,
      type: Object,
      default: () => {
        return {}
      },
    },
    /**
     * 列表项
     */
    dataColumn: {
      require: true,
      type: Array as PropType<TableItem[]>,
      default: () => {
        return []
      },
    },
    /**
     * 请求的地址前缀
     */
    requestUri: {
      require: true,
      type: String,
      default: '',
    },
    /**
     * 表单按钮组
     */
    btnMenu: {
      require: true,
      type: Object as PropType<MenuVO>,
      default: () => {
        return {}
      },
    },
    /**
     * 选择的列表项数据
     */
    selectTableColumn: {
      require: true,
      type: Array<any>,
      default: () => {
        return []
      },
    },
  })
  /**
   * 是否隐藏上传图片按钮
   */
  const hideUpload = ref<Boolean>(false)
  /**
   * 表单参数
   */
  const formData = ref<any>({})
  /**
   * 对dataColumn进行排序展示
   */
  const emit = defineEmits([
    'closeDialog',
    'handleSubmit',
    'formData', //表单
    'clearFormColumn', //清除表单
    'uploadImage', //上传图片
    'refresh',
  ])
  const formRef = ref()
  /**
   * 提交加载动画
   */
  const submitLoading = ref(false)

  const rightAreaMapComponent = ref()
  /**
   * 二级弹窗
   */
  const searchDialog = reactive<DialogOption>({
    visible: false,
  })
  const filterDataColumn = ref<TableItem[]>([])

  let oldState = cloneDeep(formData.value)

  /**
   * 对dataColumn进行排序展示
   */
  watch(
    () => props.dataColumn,
    (dataColumn: TableItem[]) => {
      filterDataColumn.value = dataColumn.filter((item) => item.form)
      filterDataColumn.value.sort((item1, item2) => item1.form?.sortNo! - item2.form?.sortNo!)
      //以下代码 => 当下拉项只有一条的时候默认选中
      filterDataColumn.value.forEach((item) => {
        if (item.form?.type === 'select' && item.form?.option?.data && item.form?.option?.data!.length === 1) {
          if (item.form.option.multiple) {
            formData.value[item.form.name!] = [item.form.option.data[0].value]
          } else {
            formData.value[item.form.name!] = item.form.option.data[0].value
          }
        }
        //开关默认启用
        if (item.form?.type === 'switch') {
          formData.value[item.form.name!] = true
        }
        //默认单位下拉框默认选中第一个
        if (item.form?.units && item.form.units.length > 0) {
          formData.value[item.form.name! + 'Unit'] = item.form.units[0].value
        }
      })
      nextTick(() => {
        //获取表单formRef高度
        if (formRef.value) {
          formHeight.value = formRef.value.$el.offsetHeight + 'px'
        }
      })
    },
    { deep: true, immediate: true },
  )
  watch(
    () => formData.value,
    (newState) => {
      var localNewState = toRaw(newState)
      for (const key in newState) {
        // 遍历 dataColumn 数组
        var depentOn = [] as any
        filterDataColumn.value.forEach((item) => {
          // if (item.form && item.form?.option?.dependOn === key && item.insertEnable && item.updateEnable)
          if (item.form && item.form?.option?.dependOn === key) {
            //判断toRaw(newState)[key] 和 oldState[key] 这两个数组是否相同，
            //如果相同说明没有变化，直接返回
            var dependOnValue = ''
            var oldStayKeyValue = ''
            if (Array.isArray(localNewState[key])) {
              dependOnValue = localNewState[key].join(',')
            } else {
              dependOnValue = localNewState[key]
            }
            if (Array.isArray(oldState[key])) {
              oldStayKeyValue = oldState[key]!.join(',')
            } else {
              oldStayKeyValue = oldState[key]
            }
            //防止其他表单项变化 触发多次调
            if (dependOnValue === oldStayKeyValue) {
              return
            }
            //新旧值不同 清空关联表单
            if (oldStayKeyValue && oldStayKeyValue != dependOnValue) {
              if (item.form?.type === 'number') {
                formData.value[item.form?.name!] = 0
              } else if ((item.form?.type === 'timerange' || item.form?.type === 'daterange') && Array.isArray(formData.value[item.form?.name!])) {
                formData.value[item.form?.name!] = []
              } else {
                formData.value[item.form?.name!] = null
              }
            }
            //防止多次调
            if (depentOn[dependOnValue]) {
              return
            }
            depentOn[dependOnValue] = formData.value[key]
            emit('formData', formData.value, item.form?.option?.dependOn)
          }
        })
      }

      oldState = cloneDeep(newState)
    },
    { deep: true },
  )
  //上传图片
  const uploadImage = async (file: any, fileList: any[], item: TableItem) => {
    if (file.raw.type !== 'image/jpeg' && file.raw.type !== 'image/png') {
      ElMessage.error('请上传图片格式')
      return
    }
    item.hideUpload = fileList.length >= item.form?.imageOption?.count!
    getBase64(file.raw).then((res) => {
      if (file.status !== 'ready') return
      const params = {
        uploadData: res,
        businessLine: item.form?.imageOption!.businessLine,
      }
      const tempArr = formData.value[item.form?.name!] ?? []
      uploadApi(item.form?.imageOption!.uri, params).then((res) => {
        formData.value[item.form?.name!] = tempArr.concat({ url: res.data })
      })
    })
  }
  //上传文件
  const uploadFile = (file: any, fileList: any[], item: TableItem) => {
    item.hideUpload = fileList.length >= item.form?.fileOption?.count!
    if (file.status !== 'ready') return
    const params = {
      file: file.raw,
      businessLine: item.form?.fileOption!.businessLine,
    }
    uploadFileApi(item.form?.fileOption!.uri!, params).then((res) => {
      formData.value[item.form?.name!] = res.data
      formData.value[item.form?.name!.replace('Url', '')!] = file.name
    })
  }

  const handleExceed: UploadProps['onExceed'] = (files) => {
    uploadFileRef.value[0]!.clearFiles()
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    uploadFileRef.value[0]!.handleStart(file)
  }
  const handleRemove = (file: any, fileList: any[], item: TableItem) => {
    //删除当前文件
    formData.value[item.form?.name!] = ''
    formData.value[item.form?.name!.replace('Url', '')!] = ''
  }
  // 点击位置
  const locationAddress = async (value: any) => {
    if (value.name == 'address') {
      searchDialog.visible = true //开启弹窗
      searchDialog.title = '选择位置'
    }
  }

  // 关闭子弹窗
  function closeMapDialog() {
    searchDialog.visible = false //关闭弹窗
  }

  // 子弹窗确认
  const submitLocation = (data: any) => {
    if (data) {
      formData.value.address = data.address
      formData.value.coordinate = data.coordinate
      closeMapDialog()
    }
  }
  /**
   * 提交表单
   */
  const handleSubmit = async (formEl: { validate: any }, btnRequest?: BtnRequestVO) => {
    if (!formEl) return
    await formEl.validate((valid: any) => {
      if (valid) {
        if (showMap.value === 'geoFence') {
          const { quyu, radius, coordinate } = rightAreaMapComponent.value.formData
          formData.value.quyu = quyu ?? null
          formData.value.radius = radius ?? null
          formData.value.coordinate = coordinate ?? null
        }
        const formParams = JSON.parse(JSON.stringify(formData.value))
        props.dataColumn.forEach((item: TableItem) => {
          if (item.insertEnable) {
            const { type, name } = item.form ?? {}
            if ((type === 'timerange' || type === 'daterange') && Array.isArray(formParams[name!])) {
              formParams[name!] = formParams[name!].join('~')
            } else if (formParams[name!] === '') {
              formParams[name!] = null
            }
            if (item.form?.type === 'uploadImage') {
              if (!formParams[item.form?.name!]) {
                formParams[item.form?.name!] = []
              } else {
                formParams[item.form?.name!] = formParams[item.form?.name!].map((item: UploadImageVO) => item.url)
              }
            }
          }
        })
        const filterFormData = props.dataColumn.filter((item) => item.insertEnable)
        const result = Object.keys(formParams)
          .filter((key) => key === 'id' || filterFormData.some((column) => column.name === key))
          .reduce((obj: any, key) => {
            obj[key] = formParams[key]
            return obj
          }, {})
        //如果按钮下存在请求操作，则优先使用按钮上的
        emit('handleSubmit', formParams, btnRequest ? btnRequest : props.requestUri, props.refreshMenuCount)
      }
    })
  }
  /**
   * 刷新
   */
  const refresh = () => {
    emit('refresh')
  }
  /**
   * 表单重置
   * @param formEl
   */
  const resetForm = () => {
    formRef.value?.resetFields()
    formRef.value?.clearValidate()
    formData.value.id = undefined
    formData.value.enable = true
  }
  /**
   * 关闭弹窗
   */
  const closeDialog = () => {
    //清除表单
    emit('clearFormColumn')
    resetForm()

    emit('closeDialog')
  }
  //预览图地址
  const dialogImageUrl = ref<string[]>()
  //预览弹窗
  const picDialogRef = ref<any>()
  /**
   * 图片预览
   */
  const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
    dialogImageUrl.value = [uploadFile.url!]
    picDialogRef.value.picDialogVisible = true
  }
  defineExpose({
    resetForm, //重置表单
    formData, //提交表单
  })
</script>

<style scoped>
  .formClass :deep(.el-scrollbar__view) {
    display: flex;
    justify-content: space-between;
  }

  :deep(.hide .el-upload--picture-card) {
    display: none;
  }
  :deep(.el-input.el-input--default.el-input--suffix),
  :deep(.el-input__wrapper) {
    width: 100% !important;
  }
</style>
