<!--
 * @Author: llm
 * @Date: 2025-05-19 14:45:56
 * @LastEditors: llm
 * @LastEditTime: 2025-05-23 14:53:22
 * @Description:
-->
<template>
  <div style="margin-bottom: 24px">
    <el-card shadow="never" :header="dataColumnForm.label">
      <!-- <el-divider content-position="left" border-style="dashed">{{ dataColumnForm.label }}</el-divider> -->
      <div style="margin-bottom: 12px">
        <!--        遍历dataColumnForm.btns 找出position=listTop项 -->
        <template v-for="item in dataColumnForm.btns?.filter((item: any) => item.position === 'listTop')" :key="item.purpose">
          <el-button
            :disabled="item.purpose === 'delete' && ids.length === 0"
            :type="item.purpose === 'delete' ? 'danger' : 'primary'"
            size="small"
            @click="handleClickBtn(item)"
          >
            {{ item.label }}
          </el-button>
        </template>
      </div>
      <TableComponent
        ref="tableComponentRef"
        :loading="tableLoading"
        :table-config="tableConfig"
        :table-data="tableData"
        :button-permission-group="listRightOperation"
        row-key="id"
        @default-handle="defaultHandleTable"
        @handle-selection-change="batchDelete"
      />
    </el-card>
    <form-dialog
      v-if="dialog.visible"
      ref="formDialogRef"
      :btn-menu="btnMenu"
      :current-row="currentRow"
      :ids="ids"
      :data-column="operationColumn"
      :dialog="dialog"
      :is-edit="isEdit"
      :loading="loading"
      :request-uri="btnRequestUri ?? requestUri"
      @close-dialog="closeDialog"
      @handle-submit="handleSave"
    />
  </div>
</template>
<script setup lang="ts">
  import { globalRequestApi, globalRequestUrlApi } from '@/api/planManagement'
  import FormDialog from '@/components/FormDialogComponent/index.vue'
  import { useFormStore } from '@/store/modules/form'
  import { composeRequestParams, getcurrentUserMenuColumnlist, getSelectOptions, resetFormGlobalFun } from '@/utils/common'
  import { PropType } from 'vue'
  const { proxy }: any = getCurrentInstance()
  const formDialogRef = ref()
  const btnMenu = ref<MenuVO>()
  const emit = defineEmits(['columnField'])
  const loading = ref(false)
  const tableLoading = ref(false)
  const ids = ref<string[]>([])
  const requestUri = ref('')
  const listRightOperation = ref<any[]>([])
  const formStore = useFormStore()
  const tableConfig = ref<TableConfig>({
    showHandleSelection: true,
    tableItem: [],
    operation: {
      label: '操作',
      items: [],
      width: '120px',
    },
  })
  const isEdit = ref(false)
  const currentRow = ref<any>({})
  const dialog = ref<any>({
    visible: false,
    title: '',
  })
  const content = ref()
  const state = reactive({
    textHeight: '',
    isShowMore: false,
    status: false,
  })
  const handleClickBtn = (item: any) => {
    if (item.purpose === 'add') {
      dialog.value.visible = true
      dialog.value.title = item.label
      isEdit.value = false
      fetchFormColumn()
    }
  }
  const tableData = ref<any[]>([])
  const props = defineProps({
    dataColumnForm: { type: Object as PropType<TableItem>, required: true },
  })
  // 将props中的dataColumnForm属性转换为响应式引用
  // 这样在模板中使用dataColumnForm时可以自动跟踪变化并更新视图
  // 注意:由于是props传入的数据,不能直接修改dataColumnForm,需要通过emit事件通知父组件修改
  const { dataColumnForm } = toRefs(props)

  // 获取菜单数据列
  const fetchMenuColumns = async () => {
    try {
      loading.value = true
      // 动态设置菜单数据列
      const { meta, children }: { meta: MetaVO; children: MenuVO[] } = await getcurrentUserMenuColumnlist(dataColumnForm.value.menuId!)
      //获取列表项
      const dataColumn: any = meta.dataColumn
      //右侧按钮组
      listRightOperation.value = children.filter((item: MenuVO) => item.meta?.position === 'listRight')
      console.log(listRightOperation.value)
      // 获取列表项的请求地址
      requestUri.value = meta.uri!
      tableConfig.value.tableItem = dataColumn
      // 计算展开更多内容超出显示
      await nextTick(() => {
        // 这里具体行数可依据需求自定义
        let lineHeight = 61 * 2
        state.textHeight = `${lineHeight}px`

        if (content.value.offsetHeight > lineHeight) {
          state.isShowMore = true
          state.status = true
        } else {
          state.isShowMore = false
          state.status = false
        }
      })
    } catch (error) {
      console.error('获取菜单数据列失败:', error)
    } finally {
      loading.value = false
    }
  }
  //获取表单项
  const fetchFormColumn = async () => {
    const { meta }: { meta: MetaVO } = await getcurrentUserMenuColumnlist(dataColumnForm.value.menuId!)
    operationColumn.value = meta.dataColumn
  }
  // 保存
  const handleSave = (formData: any, btnRequest: any, refreshMenuCount: any) => {
    console.log(formData, btnRequest, refreshMenuCount)
    //如果formData.id存在，则更新tableData中的数据
    if (formData.id) {
      tableData.value = tableData.value.map((item) => {
        if (item.id === formData.id) {
          return formData
        }
        return item
      })
    } else {
      //将formData中的数据push到tableData中,并创建一个id
      tableData.value.push({
        ...formData,
        id: Date.now(),
      })
    }
    dialog.value.visible = false
    //清空单个表单项的formData
    formDialogRef.value.formData = {}
  }
  const operationColumn = ref()
  const btnRequestUri = ref()
  const defaultHandleTable = async (row: TableItem, position: string, menu: MenuVO) => {
    currentRow.value = row
    btnMenu.value = menu
    btnRequestUri.value = menu.meta!.uri
    isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1

    //存储当前按钮下表单中的btns
    if (menu.meta?.form?.btns) {
      //存储选中项到pinia
      formStore.$patch((state) => {
        state.storeFormBtns = menu.meta?.form?.btns!
      })
    }
    if (menu.meta?.form?.formUri) {
      //定义传递的参数
      let params = {} as any
      const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
      menu.meta!.form!.params?.map((_item) => {
        composeRequestParams(params, _item, menu, storeDataParams, row, {})
      })
      if (menu.meta?.form?.method === 'get' || menu.meta?.form?.method === 'GET') {
        globalRequestUrlApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      } else {
        globalRequestApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      }
    } else {
      console.log(213)

      if (menu.meta!.dataColumn.length > 0) {
        operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn as any)
      } else {
        operationColumn.value = await refreshFormSelectOptions(tableConfig.value.tableItem as any)
      }
      await globalBtnForm(row, position, menu)
    }
    //如果menu.meta?.form?.trigerUris存在，遍历menu.meta?.form?.trigerUris，请求相应接口
    if (menu.meta?.form?.trigerUris && menu.meta?.form?.trigerUris.length > 0) {
      menu.meta?.form?.trigerUris?.map((item) => {
        //定义传递的参数
        let params = {} as any
        const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
        item.params?.map((_item) => {
          composeRequestParams(params, _item, menu, storeDataParams, row, {})
        })
        if (item.method === 'get' || item.method === 'GET') {
          globalRequestUrlApi(params, item.method!, item.uri!).then(async (res) => {})
        } else {
          globalRequestApi(params, item.method!, item.uri!).then(async (res) => {})
        }
      })
    }
  }
  /**
   * 批量删除
   * @param arr 选中的列表项数据
   */
  const batchDelete = (arr: any[]) => {
    ids.value = arr.map((item) => item.id)
  }

  const closeDialog = () => {
    dialog.value.visible = false
  }
  // 在组件挂载时获取数据
  onMounted(() => {
    fetchMenuColumns()
  })
  /**
   * 更新表单下拉
   */
  const refreshFormSelectOptions = async (items: SelectGroupItem[]) => {
    //重新获取form表单数据列，刷新筛选下拉项
    return new Promise(async (resolve, reject) => {
      const newDataColumn = await getSelectOptions(items, null, '', 'formSelect')
      console.log(newDataColumn)
      resolve(newDataColumn)
    })
  }
  /**
   * 默认按钮操作
   * @param row
   * @param position
   * @param menu
   */
  const buttonPosition = ref()
  const globalBtnForm = async (row: TableItem, position: string, menu: MenuVO) => {
    btnRequestUri.value = menu.meta?.form?.formUri ?? null
    if (menu.meta?.purpose === 'copy') {
      isEdit.value = false
    } else {
      isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1
    }
    buttonPosition.value = position //初始化表单
    const initFormData = resetFormGlobalFun(menu.meta?.dataColumn!)
    if (menu.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn as any)
    } else {
      operationColumn.value = await refreshFormSelectOptions(tableConfig.value.tableItem as any)
    }

    // 根据按钮返回数据渲染当前表单，如果是下拉表单 或者是级联表单项，需要将返回数据设置到表单项的option.data上
    //遍历数组operationColumn.value，找到里面的form的type是select，将row中和form的name相同的项赋值他
    if (position === 'listTop' || position === 'listTabTop') {
      operationColumn.value.map((item: TableItem) => {
        if (item?.form?.type === 'select' || item?.form?.type === 'selectTree' || item?.form?.type === 'cascader') {
          if (row[item.form.name!]) {
            if (!item.form!.option!.data) {
              item.form!.option!.data = []
            }
            //如果row[item.form.name!] 类型是数组则赋值给item.form.option!.data
            if (Array.isArray(row[item.form.name!])) {
              item.form!.option!.data = row[item.form.name!]
            }
            delete row[item.form.name!]
          }
        }
      })
    }
    if (position === 'listRight' || position === 'listTabRight') {
      operationColumn.value.map((item: TableItem) => {
        if (item?.form?.type === 'select' || item?.form?.type === 'selectTree' || item?.form?.type === 'cascader') {
          if (row[item!.form.name!]) {
            if (!item!.form!.option!.data) {
              item!.form!.option!.data = []
            }
            // item!.form.option!.data = row[item!.form.name!];
            // delete row[item.form.name!];
          }
        }
      })
    }
    dialog.value.visible = true
    dialog.value.title = menu.meta?.title || '操作'
    dialog.value.dialogWidth = menu.meta?.ext?.dialogWidth ?? '750px'
    const deepRow = JSON.parse(JSON.stringify(row))
    const filterOperationColumn = operationColumn.value.filter((item: TableItem) => {
      return item.form?.longTerm
    })
    filterOperationColumn.forEach((item: TableItem) => {
      if (item.form?.longTerm) {
        const longTermKey = item.form.name! + 'longTerm'
        if (deepRow[item.form?.name!] && deepRow[item.form?.name!].includes('9999')) {
          deepRow[longTermKey] = true
          deepRow[item.form.name!] = ''
        } else {
          deepRow[longTermKey] = false
        }
      }
    })
    //如果是复制，并且operationColumn.value中的每项的form.canCopy为false，则将deepRow中的对应项的value设置为空
    if (menu.meta?.purpose === 'copy') {
      operationColumn.value.forEach((item: TableItem) => {
        if (item.form?.canCopy === false) {
          deepRow[item.form.name!] = null
        }
      })
      deepRow.id = undefined
    }
    await nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, deepRow)
    })
  }
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    border-top-color: #f56c6c;
  }

  :deep(.el-divider__text) {
    color: #f56c6c;
  }
</style>
