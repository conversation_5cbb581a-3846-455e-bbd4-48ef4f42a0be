<!--
 * @Author: llm
 * @Date: 2025-05-20 16:16:23
 * @LastEditors: llm
 * @LastEditTime: 2025-05-24 10:29:50
 * @Description: 
-->
<template>
  <div style="width: 100%" class="flex items-center">
    <el-select class="mr-2px" v-model="formData[dataColumnForm.items![0].name!]" clearable placeholder="请选择" style="width: 100%">
      <el-option v-for="item in dataColumnForm.items![0].option?.data || []" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
    <!-- 遍历dataColumnForm.items!去掉第一项 -->
    <div v-for="item in dataColumnForm.items!.slice(1)" :key="item.name">
      <template
        v-if="
          !item.dependsOn ||
          (Array.isArray(item.dependsOn) &&
            item.dependsOn.every((depend: DependsOn) => {
              if (!depend) return true
              if (!depend.operator) {
                depend.operator = 'eq'
              }
              let target = formData[depend.field!]
              let when = depend.when!
              if (typeof target === 'undefined' || typeof when === 'undefined') {
                return false
              }
              if (!operatorCalculate(target, when, depend.operator)) {
                if (item.name) {
                  formData[item.name] = undefined
                }
              }
              return operatorCalculate(target, when, depend.operator)
            }))
        "
      >
        <!-- cascader -->
        <el-cascader
          v-if="item.type === 'cascader'"
          style="width: 100%"
          v-model="formData[item.name!]"
          filterable
          collapse-tags
          collapse-tags-tooltip
          :options="item!.option?.data as any"
          :props="{
            multiple: item.option?.multiple,
            checkStrictly: item!.option?.checkStrictly || false,
          }"
          clearable
        />
        <el-select
          v-else-if="item.type === 'fuzzySelect'"
          v-model="formData[item.name!]"
          style="width: 100%"
          remote-show-suffix
          clearable
          :value-key="item.name"
          :multiple="item.option?.multiple"
          filterable
          remote
          reserve-keyword
          placeholder="模糊搜索"
          :remote-method="(query: string) => remoteSelectMethod(query, item.name!)"
          :loading="fuzzySelectLoading"
          @keydown.space.prevent
        >
          <el-option v-for="i in item.option?.data || []" :key="i.value" :label="i.label" :value="i.value" />
        </el-select>
        <!-- 数字输入框 -->
        <el-input-number
          v-model="formData[item.name!]"
          controls-position="right"
          style="width: 100%"
          :min="item.min"
          :max="item.max"
          :precision="item.precision ?? 0"
          placeholder="请输入"
          v-else-if="item.type === 'number'"
        >
        </el-input-number>
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { getSelectOptions, operatorCalculate, getCustomSelectGroupLocalData } from '@/utils/common'
  import { cloneDeep } from 'lodash'

  const props = defineProps({
    dataColumnForm: { type: Object as PropType<TableItem>, required: true },
    parentFormData: { type: Object as PropType<any>, required: true },
  })
  const { dataColumnForm } = toRefs(props)
  const formData = reactive<any>({})
  let oldState = cloneDeep(formData)
  const fuzzySelectLoading = ref(false)
  watchEffect(() => {
    formData.value = props.parentFormData
  })
  // 在组件挂载时获取数据
  onMounted(async () => {
    const newDataColumn = await refreshFormSelectOptions(dataColumnForm.value.items!)
  })
  const isInitializing = ref(true) // 添加初始化标志位
  const isDefaultValueChange = ref(false) // 添加默认值变化标志位
  const lastFuzzySelectCall = ref<Record<string, number>>({}) // 存储最后一次模糊搜索调用的时间戳
  const fuzzySelectDebounceTime = 300 // 模糊搜索防抖时间
  watch(
    () => formData,
    (newState) => {
      try {
        const localNewState = toRaw(newState)
        for (const key in newState) {
          // 检查当前键的值是否已清空(null、undefined、空字符串或空数组)
          const isCleared =
            localNewState[key] === null ||
            localNewState[key] === undefined ||
            localNewState[key] === '' ||
            (Array.isArray(localNewState[key]) && localNewState[key].length === 0)

          // 如果值被清空且旧值不为空，则清空所有依赖于此字段的表单项
          if (isCleared && oldState[key]) {
            // 查找所有依赖于此字段的表单项并清空
            dataColumnForm.value.items!.forEach((item) => {
              if (item.option?.dependOn === key) {
                clearFormItem(item)
                // 递归清空所有依赖链
                if (item.name) {
                  clearDependentChain(item.name)
                }
              }
            })
          }

          // 遍历 dataColumn 数组
          let dependOn = [] as any
          dataColumnForm.value.items!.forEach(async (item: SelectGroupItem) => {
            // 只在满足所有条件时触发依赖更新
            if (item.dependsOn && item.dependsOn.some((depend: DependsOn) => depend.field === key)) {
              //判断toRaw(newState)[key] 和 oldState[key] 这两个数组是否相同，
              //如果相同说明没有变化，直接返回
              let dependOnValue = ''
              let oldStayKeyValue = ''
              if (Array.isArray(localNewState[key])) {
                dependOnValue = localNewState[key].join(',')
              } else {
                dependOnValue = localNewState[key]
              }
              if (Array.isArray(oldState[key])) {
                oldStayKeyValue = oldState[key]!.join(',')
              } else {
                oldStayKeyValue = oldState[key]
              }
              //防止其他表单项变化 触发多次调
              if (dependOnValue === oldStayKeyValue) {
                return
              }

              //新旧值不同 清空关联表单
              if (oldStayKeyValue && oldStayKeyValue != dependOnValue) {
                clearFormItem(item)
              }

              //防止多次调
              if (dependOn[dependOnValue]) {
                return
              }
              dependOn[dependOnValue] = formData[key]
              try {
                // 获取dependOn相关的参数
                const dependOnParams: any = {}
                // 如果item.option?.params存在，遍历对象并item.option?.params，如果当前项中的key对应的值为$#$#,则将值赋值为formData[dataColumnForm.items![0].name!]
                if (item.option?.params) {
                  for (const key in item.option!.params) {
                    if (item.option!.params[key] === '$#$#') {
                      dependOnParams[key] = formData[dataColumnForm.value.items![0].name!]
                    } else {
                      dependOnParams[key] = item.option!.params[key]
                    }
                  }
                }
                // 如果formdata[dataColumnForm.value.items![0].name!]为空，清空当前字段并返回
                if (!formData[dataColumnForm.value.items![0].name!]) {
                  formData[key] = undefined
                  if (item.option?.data) {
                    item.option.data = []
                  }
                  return
                }
                const { data } = (await getCustomSelectGroupLocalData(item, formData, key, {
                  ...dependOnParams,
                })) as any
                // 赋值下拉数据
                item.option!.data = data
              } catch (error) {
                console.error('搜索出错:', error)
              }
            }
            // 只在初始化时处理默认值，不要在每次值变化时都处理
            if (isInitializing.value) {
              handleDefaultValue(item)
            }
          })
        }
        //获取所有type是fuzzySelect的
        const fuzzySelectColumn = dataColumnForm.value.items!.filter((item) => item.type === 'fuzzySelect')
        //远程模糊搜索 - 只在非 defaultValue 变化时触发
        if (!isDefaultValueChange.value) {
          fuzzySelectColumn.forEach((item) => {
            if (item.name) {
              const nameKey = item.name.slice(0, -2) + 'Name'
              if (formData[nameKey]) {
                // 这里也添加防抖检查
                const now = Date.now()
                if (!lastFuzzySelectCall.value[item.name] || now - lastFuzzySelectCall.value[item.name] >= fuzzySelectDebounceTime) {
                  remoteSelectMethod(formData[nameKey], item.name)
                }
              }
            }
          })
        }

        oldState = cloneDeep(newState)
      } catch (error) {
        console.error('表单数据监听错误:', error)
      }
    },
    { deep: true },
  )
  const handleDefaultValue = (item: TableItem) => {
    if (
      (item.defaultValue ||
        item.defaultValue === false ||
        item.defaultValue === 0 ||
        item.defaultValue === '' ||
        item.defaultValue === 'null' ||
        item.defaultValue === 'undefined' ||
        item.defaultValue === null) &&
      !formData[item.name!]
    ) {
      formData[item.name!] = item.defaultValue
    }
  }
  // 清空单个表单项
  const clearFormItem = (item: TableItem) => {
    if (!item.form) return

    try {
      // 根据字段类型设置默认值
      if (item.type === 'number') {
        if (item.name) {
          formData[item.name] = 0
        }
      } else if ((item.type === 'timerange' || item.type === 'daterange') && Array.isArray(formData[item.name!])) {
        if (item.name) {
          formData[item.name] = []
        }
      } else if (item.type === 'switch') {
        if (item.name) {
          formData[item.name] = false
        }
      } else if (
        item.type === 'checkboxgroup' ||
        item.type === 'select' ||
        item.type === 'fuzzySelect' ||
        item.type === 'selectTree' ||
        item.type === 'cascader'
      ) {
        // 对于多选控件，设置为空数组
        if (item.name) {
          formData[item.name] = item.option?.multiple ? [] : null
        }

        // 清空选项数据
        if (item.option?.data) {
          item.option.data = []
        }
      } else {
        if (item.name) {
          formData[item.name] = null
        }
      }
    } catch (error) {
      console.error('Error clearing form item:', error)
    }
  }
  // 递归清空依赖链
  const clearDependentChain = (fieldName: string) => {
    try {
      dataColumnForm.value.items!.forEach((item) => {
        if (item.option?.dependOn === fieldName) {
          clearFormItem(item)
          // 递归清空依赖于此字段的其他字段
          if (item.name) {
            clearDependentChain(item.name)
          }
        }
      })
    } catch (error) {
      console.error('Error clearing dependent chain:', error)
    }
  }
  /**
   * 更新表单下拉
   */
  const refreshFormSelectOptions = async (items: SelectGroupItem[]) => {
    //重新获取form表单数据列，刷新筛选下拉项
    return new Promise(async (resolve, reject) => {
      try {
        // 获取dependOn相关的参数
        const dependOnParams: any = {}
        // 如果item.option?.params存在，遍历对象并item.option?.params，如果当前项中的key对应的值为$#$#,则将值赋值为formData[dataColumnForm.items![0].name!]
        const item = items[0]
        if (item.option?.params) {
          for (const key in item.option!.params) {
            if (item.option!.params[key] === '$#$#') {
              dependOnParams[key] = formData[dataColumnForm.value.items![0].name!]
            } else {
              dependOnParams[key] = item.option!.params[key]
            }
          }
        }
        // 如果formdata[dataColumnForm.value.items![0].name!]为空，清空当前字段并返回
        if (!formData[dataColumnForm.value.items![0].name!]) {
          formData[item.name!] = undefined
          if (item.option?.data) {
            item.option.data = []
          }
          return
        }
        const { data } = (await getCustomSelectGroupLocalData(item, formData, item.name!, {
          ...dependOnParams,
        })) as any
        item.option!.data = data
      } catch (error) {
        console.error('下拉数据获取出错:', error)
      }
      resolve(items)
    })
  }
  //远程模糊搜索下拉
  const remoteSelectMethod = async (query: string, key: string) => {
    if (query) {
      const item = dataColumnForm.value.items!.find((item) => item.name === key)
      if (item) {
        // 检查是否需要跳过此次调用（防抖）
        const now = Date.now()
        if (lastFuzzySelectCall.value[key] && now - lastFuzzySelectCall.value[key] < fuzzySelectDebounceTime) {
          return
        }
        // 记录本次请求的时间戳
        lastFuzzySelectCall.value[key] = now

        fuzzySelectLoading.value = true
        try {
          // 获取dependOn相关的参数
          const dependOnParams: any = {}
          // 如果item.option?.params存在，遍历对象并item.option?.params，如果当前项中的key对应的值为$#$#,则将值赋值为formData[dataColumnForm.items![0].name!]
          if (item.option?.params) {
            for (const key in item.option!.params) {
              if (item.option!.params[key] === '$#$#') {
                dependOnParams[key] = formData[dataColumnForm.value.items![0].name!]
              } else {
                dependOnParams[key] = item.option!.params[key]
              }
            }
          }
          // 如果formdata[dataColumnForm.value.items![0].name!]为空，清空当前字段并返回
          if (!formData[dataColumnForm.value.items![0].name!]) {
            formData[key] = undefined
            if (item.option?.data) {
              item.option.data = []
            }
            fuzzySelectLoading.value = false
            return
          }
          const { data } = (await getCustomSelectGroupLocalData(item, formData, key, {
            keyword: query,
            ...dependOnParams,
          })) as any
          item.option!.data = data
        } catch (error) {
          console.error('模糊搜索出错:', error)
        } finally {
          // 无论成功失败都关闭loading状态
          fuzzySelectLoading.value = false
        }
      }
    }
  }
  defineExpose({
    formData,
  })
</script>
