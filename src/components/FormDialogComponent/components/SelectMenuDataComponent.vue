<!--
 * @Author: llm
 * @Date: 2025-05-19 14:45:56
 * @LastEditors: llm
 * @LastEditTime: 2025-05-23 15:00:48
 * @Description: 
-->
<template>
  <div style="margin-bottom: 24px">
    <el-card shadow="never" :header="dataColumnForm.label">
      <!-- <el-divider content-position="left" border-style="dashed">{{ dataColumnForm.label }}</el-divider> -->
      <div style="margin-bottom: 12px">
        <template v-for="item in dataColumnForm.btns" :key="item.purpose">
          <el-button v-if="item.purpose === 'selectAdd'" type="primary" @click="handleAdd(item)">{{ item.label }}</el-button>
          <el-button v-if="item.purpose === 'selectDelete'" type="danger" @click="handleDelete" :disabled="ids.length === 0">{{ item.label }}</el-button>
        </template>
      </div>
      <TableComponent
        ref="tableComponentRef"
        :loading="tableLoading"
        :tableConfig="tableConfig"
        :tableData="tableData"
        rowKey="id"
        @handleSelectionChange="batchDelete"
      />
    </el-card>
    <el-dialog
      draggable
      :fullscreen="isFullscreen"
      :close-on-click-modal="false"
      :title="detailDialogVisible.title"
      width="80%"
      v-model="detailDialogVisible.visible"
      @close="onClose"
    >
      <div>
        <div ref="content" v-show="topQueryConfig.tableItem.filter((item: any) => item.query).length > 0" style="position: relative">
          <topQueryGroupComponent
            ref="topQueryGroupComponentRef"
            :query-permission-group="topQueryConfig.tableItem"
            :requestUri="requestUri"
            @handleSearchQuery="handleSearchQuery"
            @queryParams="getQueryData"
            @refreshPage="resetQuery"
          />
        </div>
        <div :style="{ paddingTop: topQueryConfig.tableItem.filter((item: any) => item.query).length > 0 ? '60px' : '0px' }">
          <TableComponent
            height="55vh"
            ref="dialogTableComponentRef"
            :loading="dialogTableLoading"
            :tableConfig="tableConfig"
            :tableData="dialogTableData"
            @handleSelectionChange="handleSelectionChange"
            rowKey="id"
          />
        </div>
      </div>
      <pagination v-model:limit="dialogTableParams.limit" v-model:page="dialogTableParams.page" v-model:total="total" @pagination="_pagination" />

      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="onClose">取 消</el-button>
          <el-button size="small" type="primary" @click="handleSave" :loading="confirmLoading">保 存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import { getListPage } from '@/api/auth'
  import { getcurrentUserMenuColumnlist } from '@/utils/common'
  import { getSelectOptions } from '@/utils/common'
  import { PropType } from 'vue'
  const emit = defineEmits(['columnField'])
  const loading = ref(false)
  const tableLoading = ref(false)
  const dialogTableLoading = ref(false)
  const ids = ref<string[]>([])
  const isFullscreen = ref(false)
  const requestUri = ref('')
  const tableConfig = ref<TableConfig>({
    showHandleSelection: true,
    tableItem: [],
  })
  const content = ref()
  const state = reactive({
    textHeight: '',
    isShowMore: false,
    status: false,
  })
  /**
   * 顶部搜索配置项
   */
  const topQueryConfig = reactive<TableConfig>({
    tableItem: [],
    operation: undefined,
  })
  const dialogTableParams = ref<any>({
    page: 1,
    limit: 50,
  })
  const detailDialogVisible = reactive({
    title: '',
    visible: false,
  })
  const selectDialogTableColumn = ref<any[]>([])
  const tableData = ref<any[]>([])
  const dialogTableData = ref<any[]>([])
  const props = defineProps({
    dataColumnForm: { type: Object as PropType<TableItem>, required: true },
  })
  // 将props中的dataColumnForm属性转换为响应式引用
  // 这样在模板中使用dataColumnForm时可以自动跟踪变化并更新视图
  // 注意:由于是props传入的数据,不能直接修改dataColumnForm,需要通过emit事件通知父组件修改
  const { dataColumnForm } = toRefs(props)

  // 获取菜单数据列
  const fetchMenuColumns = async () => {
    try {
      loading.value = true
      // 动态设置菜单数据列
      const { children, meta }: { children: MenuVO[]; meta: MetaVO } = await getcurrentUserMenuColumnlist(dataColumnForm.value.menuId!)
      //获取列表项
      const dataColumn: any = meta.dataColumn
      // 获取列表项的请求地址
      requestUri.value = meta.uri!
      tableConfig.value.tableItem = dataColumn
      topQueryConfig.tableItem = dataColumn
      // 计算展开更多内容超出显示
      nextTick(() => {
        // 这里具体行数可依据需求自定义
        let lineHeight = 61 * 2
        state.textHeight = `${lineHeight}px`

        if (content.value.offsetHeight > lineHeight) {
          state.isShowMore = true
          state.status = true
        } else {
          state.isShowMore = false
          state.status = false
        }
      })
    } catch (error) {
      console.error('获取菜单数据列失败:', error)
    } finally {
      loading.value = false
    }
  }
  // 新增
  const handleAdd = async (item: DataColumnBtnsVO) => {
    detailDialogVisible.visible = true
    detailDialogVisible.title = item.label
    dialogTableParams.value.page = 1
    dialogTableParams.value.limit = 50
  }
  // 批量删除
  const handleDelete = () => {
    tableData.value = tableData.value.filter((item) => !ids.value.includes(item.id))
  }
  // 保存
  const handleSave = () => {
    // 获取已选中的新数据
    const newSelectedData = selectDialogTableColumn.value

    // 将新选中的数据添加到tableData中，同时更新ids
    const newIds = new Set(tableData.value.map((item) => item.id))
    const updatedData = [...tableData.value]

    // 遍历新选中的数据，如果不存在则添加
    newSelectedData.forEach((newItem) => {
      if (!newIds.has(newItem.id)) {
        updatedData.push(newItem)
        newIds.add(newItem.id)
      }
    })

    // 更新tableData和ids，保持数据同步
    tableData.value = updatedData

    // 清空选中数据
    selectDialogTableColumn.value = []

    // 重置弹窗相关状态
    dialogTableData.value = []
    defaultTableIds.value = {}

    // 关闭弹窗
    detailDialogVisible.visible = false
    emit('columnField', tableData.value)
  }

  // 监听弹窗显示状态
  watch(
    () => detailDialogVisible.visible,
    async (newVal) => {
      if (newVal) {
        // 打开弹窗时才加载和处理数据
        dialogTableData.value = [] // 清空旧数据
        await getDialogDataList(dialogTableParams.value)
      }
    },
    { immediate: false }, // 确保只在值真正改变时才触发
  )

  // 监听dialogTableData变化，处理选择状态
  watch(
    () => dialogTableData.value,
    async (newData) => {
      if (detailDialogVisible.visible && newData.length > 0) {
        await nextTick()
        // 创建已有数据的ID集合，用于快速查找
        const selectedIds = new Set(tableData.value.map((item) => item.id))
        // 遍历弹窗数据，选中已存在的项
        newData.forEach((item) => {
          if (selectedIds.has(item.id)) {
            dialogTableComponentRef.value?.dataTableRef?.toggleRowSelection(item, true)
          }
        })
      }
    },
    { immediate: false },
  )

  const getDialogDataList = async (newParams: any = {}) => {
    dialogTableLoading.value = true
    try {
      const { data } = await getListPage(newParams, requestUri.value!)
      // 更新表格数据和统计信息
      dialogTableData.value = data.rows
      statisticColumn.value = data.statistics
      pageSummary.value = data.pageSummary
      totalSummary.value = data.totalSummary
      total.value = data.total

      // 清空现有的选择
      if (dialogTableComponentRef.value?.dataTableRef) {
        dialogTableComponentRef.value.dataTableRef.clearSelection()
      }

      // 如果有默认需要选中的ID，进行选择
      if (defaultTableIds.value.ids && data.rows?.length > 0) {
        const idsToSelect = new Set(defaultTableIds.value.ids)
        ids.value = defaultTableIds.value.ids
        // 遍历数据，选中指定ID的行
        data.rows.forEach((item) => {
          if (idsToSelect.has(item.id)) {
            dialogTableComponentRef.value?.dataTableRef?.toggleRowSelection(item, true)
          }
        })
      }
    } catch (error) {
      console.error('加载弹窗数据失败:', error)
    } finally {
      dialogTableLoading.value = false
    }
  }

  // 关闭弹窗
  const onClose = () => {
    // 清空弹窗相关数据
    dialogTableData.value = []
    selectDialogTableColumn.value = []
    defaultTableIds.value = {}
    detailDialogVisible.visible = false
  }

  const dialogTableComponentRef = ref()
  const defaultTableIds = ref<any>({})
  const statisticColumn = ref<any[]>([])
  const pageSummary = ref<any[]>([])
  const totalSummary = ref([])
  const total = ref(0)
  const confirmLoading = ref(false)
  /**
   * 分页
   */
  const _pagination = () => {
    getDialogDataList(dialogTableParams.value)
  }
  /**
   * 批量操作
   * @param arr 选中的列表项数据
   */
  const handleSelectionChange = (arr: any[]) => {
    selectDialogTableColumn.value = arr
  }
  /**
   * 批量删除
   * @param arr 选中的列表项数据
   */
  const batchDelete = (arr: any[]) => {
    ids.value = arr.map((item) => item.id)
  }
  /**
   * 筛选条件查询
   * @param searchParams 查询条件
   */
  const handleSearchQuery = (searchParams: any) => {
    //遍历dialogTableParams对象，删除所有属性
    for (const key in dialogTableParams.value) {
      if (key !== 'page' && key !== 'limit') {
        Reflect.deleteProperty(dialogTableParams.value, key)
      }
    }
    // queryParams.limit = 10;
    //遍历searchParams对象，将所有属性赋值给queryParams
    for (const key in searchParams) {
      dialogTableParams.value[key] = searchParams[key]
    }
    getDialogDataList(dialogTableParams.value)
  }
  /**
   * 获取表单项，用于表单中级联操作
   * @param formData 表单项
   * @param dependOn 当前修改的依赖项下拉
   */
  const getQueryData = async (formData: any, dependOn: string) => {
    //获取form表单数据列
    const newOperationColumn = await getSelectOptions(topQueryConfig.tableItem, formData, dependOn, 'topQuerySelect')
  }
  /**
   * 重置查询
   */
  function resetQuery() {
    dialogTableParams.value.page = 1
    getDialogDataList(dialogTableParams.value)
  }
  // 在组件挂载时获取数据
  onMounted(() => {
    fetchMenuColumns()
  })
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    border-top-color: #f56c6c;
  }

  :deep(.el-divider__text) {
    color: #f56c6c;
  }
</style>
