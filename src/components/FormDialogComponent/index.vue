<!--
 * @Author: llm
 * @Date: 2023-06-28 09:45:08
 * @LastEditors: llm
 * @LastEditTime: 2025-07-17 15:37:46
 * @Description: 新增or编辑弹窗组件
 *
-->
<template>
  <el-dialog
    v-model="dialog.visible"
    :title="dialog.title"
    :width="showMap === 'geoFence' ? '960px' : dialog.dialogWidth ? dialog.dialogWidth : '750px'"
    :draggable="true"
    :close-on-click-modal="false"
    @closed="closeDialog"
  >
    <el-scrollbar max-height="60vh" class="formClass">
      <div class="left-ml-2">
        <el-form
          ref="formRef"
          v-loading="formLoading"
          :model="formData"
          label-width="80px"
          :style="{ width: showMap === 'geoFence' ? 'auto' : '95%' }"
          class="form-inline"
        >
          <!-- 按groupKey分组展示 -->
          <template v-for="group in groupedFormFields" :key="group.key">
            <el-card v-if="group.key !== 'default'" class="mb-4 w-100%">
              <template #header>
                <div class="card-header">
                  <span>{{ group.key }}</span>
                  <!-- <span v-html="group.instructions"></span> -->
                </div>
              </template>
              <el-row>
                <template v-for="(item, index) in group.items" :key="index">
                  <template v-if="(item?.insertEnable && !props.isEdit) || (item?.updateEnable && props.isEdit)">
                    <el-col
                      v-if="
                        (!item.form?.dependsOn ||
                          (Array.isArray(item.form.dependsOn) &&
                            item.form.dependsOn.every((depend: DependsOn) => {
                              if (!depend) return true
                              if (!depend.operator) {
                                depend.operator = 'eq'
                              }
                              let target = formData[depend.field!]
                              let when = depend.when!
                              // if (typeof target === 'undefined' || typeof when === 'undefined') {
                              //   return false
                              // }

                              if (!operatorCalculate(target, when, depend.operator)) {
                                if (item.form?.name) {
                                  formData[item.form.name] = undefined
                                }
                              }
                              return operatorCalculate(target, when, depend.operator)
                            }))) &&
                        (item.form?.canShow === true || !item.form)
                      "
                      :span="item.form?.colSpan * 1 ? item.form?.colSpan * 1 : 24"
                    >
                      <div>
                        <el-form-item
                          :key="index"
                          :label="item.form?.label"
                          :prop="item.form?.name"
                          :rules="item.form?.rules"
                          :label-width="item.form?.labelWidth ?? '120px'"
                        >
                          <div class="" style="width: 100%; display: flex; align-items: center">
                            <!-- textarea -->
                            <el-input
                              v-if="item.form?.type === 'textarea'"
                              v-model="formData[item.form.name!]"
                              type="textarea"
                              :rows="5"
                              :disabled="isDisabled(item)"
                              :placeholder="item.form.placeholder ? item.form.placeholder : '请输入' + item.form.label"
                            />
                            <!-- location 地址 -->
                            <el-input
                              v-else-if="item.form?.type === 'location'"
                              v-model="formData[item.form.name!]"
                              type="text"
                              :readonly="item.form?.canEdit"
                              :placeholder="item.form.placeholder ? item.form.placeholder : '请输入' + item.form.label"
                              @click.native.stop="locationAddress(item.form)"
                              @input="
                                (value: string) => {
                                  formData[item.form?.name!] = value.replace(/\s+/g, '')
                                }
                              "
                            />
                            <!-- 数字输入框 -->
                            <el-input-number
                              v-else-if="item.form?.type === 'number'"
                              v-model="formData[item.form.name!]"
                              controls-position="right"
                              style="width: 100%"
                              :min="item.form?.min"
                              :max="item.form?.max"
                              :step="item.form?.step"
                              :precision="item.form?.precision ?? 0"
                              :placeholder="item.form.placeholder ? item.form.placeholder : '请输入' + item.form.label"
                              :disabled="isDisabled(item)"
                            >
                              <template v-if="item.form?.prefix" #prefix>
                                <div>
                                  {{ item.form?.prefix }}
                                </div>
                              </template>
                            </el-input-number>
                            <!-- 开关 -->
                            <el-switch
                              v-else-if="item.form?.type === 'switch'"
                              v-model="formData[item.form.name!]"
                              inline-prompt
                              active-text="启用"
                              inactive-text="停用"
                              :disabled="isDisabled(item)"
                            />
                            <div v-else-if="item.form?.type === 'select'" style="width: 100%">
                              <!-- 下拉选择 -->
                              <el-select
                                v-model="formData[item.form.name!]"
                                style="width: 100%"
                                :value-key="item.form.name"
                                :multiple="item.form?.option?.multiple"
                                :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                                :disabled="isDisabled(item)"
                                :no-data-text="item.form?.option?.emptyTip"
                                filterable
                                clearable
                                :class="item.form?.customClass || 'my-select'"
                              >
                                <el-option
                                  v-for="i in item.form?.option?.data"
                                  :key="i.value"
                                  :label="i.label"
                                  :value="i.value"
                                  :disabled="i?.disabled ? i.disabled : false"
                                />
                              </el-select>
                              <!-- 如果当前下拉项下存在desc,需要显示 -->
                              <div v-for="i in item.form?.option?.data" :key="i.value">
                                <div v-if="i.value === formData[item.form.name!] && i.desc" class="select-desc" v-html="i.desc" />
                              </div>
                            </div>
                            <div v-else-if="item.form?.type === 'fuzzySelect'" style="width: 100%">
                              <!-- 远程模糊搜索下拉 -->
                              <el-select
                                v-model="formData[item.form.name!]"
                                style="width: 100%"
                                remote-show-suffix
                                clearable
                                :value-key="item.form.name"
                                :multiple="item.form?.option?.multiple"
                                filterable
                                remote
                                reserve-keyword
                                :placeholder="item.form.placeholder ? item.form.placeholder : '模糊搜索' + item.form.label"
                                :remote-method="(query: string) => remoteSelectMethod(query, item.form?.name!)"
                                :disabled="isDisabled(item)"
                                :loading="fuzzySelectLoading"
                              >
                                <el-option
                                  v-for="i in item.form?.option?.data || []"
                                  :key="i.value"
                                  :label="i.label"
                                  :value="i.value"
                                  :disabled="i?.disabled ? i.disabled : false"
                                />
                              </el-select>
                            </div>
                            <!-- 下拉树 -->
                            <el-tree-select
                              v-else-if="item.form?.type === 'selectTree'"
                              v-model="formData[item.form.name!]"
                              default-expand-all
                              filterable
                              style="width: 100%"
                              :multiple="item.form?.option?.multiple"
                              :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                              :data="item.form.option?.data"
                              :check-strictly="item.form?.option?.checkStrictly || false"
                              :props="{ value: 'value', label: 'label' }"
                              :disabled="isDisabled(item)"
                              clearable
                              :style="{ width: item.form?.width || '100%' }"
                            >
                              <template #default="{ data }">
                                {{ data.label }}
                              </template>
                            </el-tree-select>

                            <!-- 级联选择器-省市区 -->
                            <el-cascader
                              v-else-if="item.form?.type === 'cascader'"
                              v-model="formData[item.form.name!]"
                              style="width: 100%"
                              filterable
                              collapse-tags
                              collapse-tags-tooltip
                              :options="item.form!.option?.data as any"
                              :props="{
                                multiple: item.form.option?.multiple,
                                checkStrictly: item.form!.option?.checkStrictly,
                              }"
                              clearable
                              :style="{ width: item.form?.width || '100%' }"
                              :disabled="isDisabled(item)"
                            />
                            <!-- 日区间、月区间-->
                            <el-date-picker
                              v-else-if="item.form?.type === 'daterange' || item.form?.type === 'monthrange' || item.form?.type === 'datetimerange'"
                              v-model="formData[item.form.name as keyof TableItem] as any"
                              :type="item.form?.type"
                              start-placeholder="开始时间"
                              end-placeholder="结束时间"
                              :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                              :disabled="isDisabled(item)"
                              :format="item.form.format"
                              :value-format="item.form.format"
                              clearable
                              :style="{ width: item.form?.width || '100%' }"
                              :shortcuts="ShortcutsList"
                            />
                            <!-- 周、年、月、日 -->
                            <el-date-picker
                              v-else-if="item.form?.type === 'week' || item.form?.type === 'year' || item.form?.type === 'month' || item.form?.type === 'date'"
                              v-model="formData[item.form.name as keyof TableItem] as any"
                              :type="item.form?.type"
                              :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                              :disabled="isDisabled(item)"
                              :format="item.form.format"
                              :value-format="item.form.format"
                              clearable
                              :style="{ width: item.form?.width || '100%' }"
                              :disabled-date="
                                (data: Date) => {
                                  if (!item.form?.disabledDateType) return false
                                  const type = item.form?.type || 'date'
                                  const disabledDateType = item.form?.disabledDateType
                                  const today = new Date()
                                  today.setHours(0, 0, 0, 0) // 清除时间部分

                                  if (type === 'date' || type === 'week') {
                                    const currentDate = new Date(data)
                                    currentDate.setHours(0, 0, 0, 0)
                                    return disabledDateType === 'after' ? currentDate > today : currentDate < today
                                  } else if (type === 'month') {
                                    const currentYear = today.getFullYear()
                                    const currentMonth = today.getMonth()
                                    const dataYear = data.getFullYear()
                                    const dataMonth = data.getMonth()
                                    return disabledDateType === 'after'
                                      ? dataYear > currentYear || (dataYear === currentYear && dataMonth > currentMonth)
                                      : dataYear < currentYear || (dataYear === currentYear && dataMonth < currentMonth)
                                  } else if (type === 'year') {
                                    const currentYear = today.getFullYear()
                                    const dataYear = data.getFullYear()
                                    return disabledDateType === 'after' ? dataYear > currentYear : dataYear < currentYear
                                  }
                                  return false
                                }
                              "
                              @change="(val: any) => handleDateChange(val, item)"
                            />
                            <!-- 时分秒 -->
                            <el-time-picker
                              v-else-if="item.form?.type === 'time'"
                              v-model="formData[item.form.name!]"
                              :format="item.form.format"
                              :value-format="item.form.format"
                              :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                              :disabled="isDisabled(item)"
                              clearable
                              :style="{ width: item.form?.width || '200px' }"
                            />
                            <!--  时分秒-时分秒 -->
                            <el-time-picker
                              v-else-if="item.form?.type === 'timerange'"
                              v-model="formData[item.form.name!]"
                              is-range
                              :format="item.form.format"
                              :disabled="isDisabled(item)"
                              :value-format="item.form.format"
                              range-separator="-"
                              start-placeholder="开始时间"
                              end-placeholder="结束时间"
                              clearable
                              :style="{ width: item.form?.width || '100%' }"
                            />

                            <!-- 年月日时分秒 -->
                            <el-date-picker
                              v-else-if="item.form?.type === 'datetime'"
                              v-model="formData[item.form.name!]"
                              type="datetime"
                              :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                              :format="item.form.format"
                              :value-format="item.form.format"
                              :disabled="isDisabled(item)"
                              :default-time="item.form?.defaultTime ? new Date(`2000-01-01 ${item.form.defaultTime}`) : new Date(2000, 0, 1, 23, 59, 59)"
                              clearable
                              :style="{ width: item.form?.width || '100%' }"
                            />
                            <!-- 时间选择 -->
                            <el-time-select
                              v-else-if="item.form?.type === 'timeSelect'"
                              v-model="formData[item.form!.name!]"
                              start="08:30"
                              step="00:15"
                              end="18:30"
                              :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                              :disabled="isDisabled(item)"
                              clearable
                              :style="{ width: item.form?.width || '100%' }"
                            />
                            <!-- 单选 -->
                            <el-radio-group v-else-if="item.form?.type === 'radiogroup'" v-model="formData[item.form.name!]" :disabled="isDisabled(item)">
                              <el-radio v-for="_item in item.form.option" :label="_item.value">
                                {{ _item.label }}
                              </el-radio>
                            </el-radio-group>
                            <!-- CheckBox多选 -->
                            <div v-else-if="item.form?.type === 'checkboxgroup'">
                              <el-input
                                v-model="fuzzyCheckboxInput"
                                placeholder="模糊搜索"
                                style="width: 220px"
                                @input="(e: string) => fuzzyInputFun(e, item)"
                              />
                              <div>
                                <el-checkbox-group v-model="formData[item.form.name!]" :disabled="isDisabled(item)">
                                  <div>
                                    <el-checkbox
                                      v-for="_item in item.form!.option?.filterData || item.form!.option?.data"
                                      class="mt-2 mr-10px!"
                                      :label="_item.value"
                                      :border="item.form?.showCheckboxBorder ? true : true"
                                    >
                                      {{ _item.label }}
                                    </el-checkbox>
                                  </div>
                                </el-checkbox-group>
                              </div>
                            </div>
                            <!-- 图片上传 -->
                            <el-upload
                              v-else-if="item.form?.type === 'uploadImage'"
                              ref="uploadImageRef"
                              accept="image/*"
                              drag
                              :class="{
                                hide:
                                  (item.form?.editOn && isDisabled(item)) ||
                                  (formData[item.form?.name!] && formData[item.form?.name!].length >= item.form.imageOption?.count!),
                              }"
                              :auto-upload="false"
                              :file-list="formData[item.form?.name!] ? formData[item.form?.name!] : []"
                              :limit="item.form.imageOption?.count"
                              :multiple="item.form.imageOption?.multiple"
                              :on-change="
                                (file: any, fileList: any[]) => {
                                  uploadImage(file, fileList, item)
                                }
                              "
                              :on-remove="
                                (file: any, fileList: any[]) => {
                                  handleOldRemove(file, fileList, item)
                                }
                              "
                              :on-preview="handlePictureCardPreview"
                              :disabled="isDisabled(item)"
                              list-type="picture-card"
                              action="#"
                            >
                              <img src="@/assets/images/upload_icon.png" mode="scaleToFill" style="width: 40px; height: auto" />
                              <div class="el-upload__text text-12px">
                                <em>拖拽或点击上传</em>
                              </div>
                              <template v-if="item.form.imageOption?.tips" #tip>
                                <div>
                                  <el-tag type="warning">
                                    <div class="flex items-center">
                                      <el-icon>
                                        <Warning />
                                      </el-icon>
                                      {{ item.form.imageOption?.tips }}
                                    </div>
                                  </el-tag>
                                </div>
                              </template>
                            </el-upload>
                            <el-upload
                              v-else-if="item.form?.type === 'uploadImageV2'"
                              ref="uploadImageRef"
                              accept="image/*"
                              drag
                              :class="{ hide: formData[item.form?.name!] && formData[item.form?.name!].length >= item.form.imageOption?.count! }"
                              :auto-upload="false"
                              :file-list="formData[item.form?.name!] ? formData[item.form?.name!] : []"
                              :limit="item.form.imageOption?.count"
                              :multiple="item.form.imageOption?.multiple"
                              :on-change="
                                (file: any, fileList: any[]) => {
                                  uploadImageV2(file, fileList, item)
                                }
                              "
                              :on-remove="
                                (file: any, fileList: any[]) => {
                                  handleOldRemove(file, fileList, item)
                                }
                              "
                              :on-preview="handlePictureCardPreview"
                              :disabled="isDisabled(item)"
                              list-type="picture-card"
                              action="#"
                            >
                              <!-- <el-icon size="20px">
                              <upload-filled />
                            </el-icon> -->
                              <img src="@/assets/images/upload_icon.png" mode="scaleToFill" style="width: 40px; height: auto" />
                              <div class="el-upload__text text-12px">
                                <em>拖拽或点击上传</em>
                              </div>
                              <template v-if="item.form.imageOption?.tips" #tip>
                                <div>
                                  <el-tag type="warning">
                                    <div class="flex items-center">
                                      <el-icon>
                                        <Warning />
                                      </el-icon>
                                      {{ item.form.imageOption?.tips }}
                                    </div>
                                  </el-tag>
                                </div>
                              </template>
                            </el-upload>
                            <!-- 文件上传 -->
                            <el-upload
                              v-else-if="item.form?.type === 'uploadFile'"
                              ref="uploadFileRef"
                              :accept="item.form?.fileOption?.accept || '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar,.7z'"
                              :file-list="
                                formData[item.form?.name!]
                                  ? formData[item.form?.name!].map((_item: any) => ({ name: _item.split('/').pop() || item, url: _item }))
                                  : []
                              "
                              action="#"
                              :auto-upload="false"
                              :multiple="item.form?.fileOption?.multiple || false"
                              :limit="item.form.fileOption?.count"
                              :on-change="
                                (file: any, fileList: any[]) => {
                                  uploadFile(file, fileList, item)
                                }
                              "
                              :on-remove="
                                (file: any, fileList: any[]) => {
                                  handleRemove(file, fileList, item)
                                }
                              "
                              :disabled="isDisabled(item)"
                            >
                              <img src="@/assets/images/upload_icon.png" mode="scaleToFill" style="width: 40px; height: auto" />
                              <div class="el-upload__text text-12px">
                                <em>拖拽或点击上传</em>
                              </div>
                              <template #trigger>
                                <el-button type="primary"> 点击上传 </el-button>
                              </template>
                            </el-upload>
                            <!-- ocr识别 -->
                            <div v-else-if="item.form?.type === 'fileOcr' || item.form?.type === 'uploadFileV2'" class="w-100%">
                              <el-upload
                                ref="uploadFileRef"
                                :accept="item.form?.fileOption?.accept || '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar'"
                                :file-list="formData[item.form?.name!] ? formData[item.form?.name!] : []"
                                action="#"
                                drag
                                list-type="picture-card"
                                :show-file-list="false"
                                :auto-upload="false"
                                :multiple="item.form?.fileOption?.multiple || false"
                                :limit="item.form.fileOption?.count"
                                :on-change="
                                  (file: any, fileList: any[]) => {
                                    uploadFileAdvance(file, fileList, item)
                                  }
                                "
                                :on-remove="
                                  (file: any, fileList: any[]) => {
                                    handleRemove(file, fileList, item)
                                  }
                                "
                                :on-exceed="
                                  (files: any, fileList: any[]) => {
                                    handleExceed(files, fileList, item)
                                  }
                                "
                                :disabled="isDisabled(item)"
                              >
                                <img src="@/assets/images/upload_icon.png" mode="scaleToFill" style="width: 40px; height: auto" />
                                <div class="el-upload__text text-12px">
                                  <em>拖拽或点击上传</em>
                                </div>
                                <!-- <template #trigger>
                                <el-button type="primary">点击上传</el-button>
                              </template> -->
                                <template v-if="item.form.fileOption?.tips" #tip>
                                  <div>
                                    <el-tag type="warning">
                                      <div class="flex items-center">
                                        <el-icon>
                                          <Warning />
                                        </el-icon>
                                        {{ item.form.fileOption?.tips }}
                                      </div>
                                    </el-tag>
                                  </div>
                                </template>
                              </el-upload>

                              <!-- 文件展示区域 -->
                              <div v-if="formData[item.form?.name!] && formData[item.form?.name!].length > 0" class="mt-4">
                                <!-- 图片展示区域 -->
                                <div v-if="hasImageFiles(formData[item.form?.name!])" class="mb-4">
                                  <div class="text-gray-600 mb-2">图片文件</div>
                                  <div class="flex flex-wrap gap-4">
                                    <div v-for="(file, fileIndex) in getImageFiles(formData[item.form?.name!])" :key="fileIndex" class="relative">
                                      <el-image
                                        :preview-src-list="[file.url || file]"
                                        :src="file.url || file"
                                        class="w-100px h-auto object-cover border rounded"
                                      />
                                      <el-button type="danger" size="small" circle class="absolute top-1 right-1" @click="handleImageRemove(file, item)">
                                        <el-icon>
                                          <Delete />
                                        </el-icon>
                                      </el-button>
                                    </div>
                                  </div>
                                </div>

                                <!-- 其他文件展示区域 -->
                                <div v-if="hasOtherFiles(formData[item.form?.name!])" class="mb-4 w-100%">
                                  <div class="text-gray-600 mb-2">其他文件</div>
                                  <el-table :data="getOtherFiles(formData[item.form?.name!])" style="width: 100%">
                                    <el-table-column prop="name" label="文件名">
                                      <template #default="{ row }">
                                        {{ getFileName(row.url || row) }}
                                      </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="120">
                                      <template #default="{ row }">
                                        <el-button type="danger" size="small" @click="handleOtherFileRemove(row, item)"> 删除 </el-button>
                                      </template>
                                    </el-table-column>
                                  </el-table>
                                </div>
                              </div>
                            </div>
                            <div v-else-if="item.form?.type === 'uploadVideoFull'">
                              <el-upload
                                ref="uploadVideoRef"
                                accept="video/*"
                                :auto-upload="false"
                                :on-change="
                                  (file: any, fileList: any[]) => {
                                    uploadVideo(file, fileList, item)
                                  }
                                "
                                :multiple="item.form?.videoOption?.multiple || false"
                                :limit="item.form.videoOption?.count"
                                list-type="text"
                                action="#"
                                :show-file-list="false"
                              >
                                <!-- 目前上传视频中状态，停用失效 -->
                                <el-button
                                  v-if="(formData[item.form?.name!]?.length || 0) < (item.form.videoOption?.count || 1)"
                                  :disabled="isDisabled(item) || item.uploadLoading"
                                  type="primary"
                                  :loading="item.uploadLoading"
                                >
                                  {{ item.uploadLoading ? '上传中...' : '点击上传' }}
                                </el-button>
                                <template v-if="item.form.videoOption?.tips" #tip>
                                  <div>
                                    <el-tag type="warning">
                                      <div class="flex items-center">
                                        <el-icon>
                                          <Warning />
                                        </el-icon>
                                        {{ item.form.videoOption?.tips }}
                                      </div>
                                    </el-tag>
                                  </div>
                                </template>
                              </el-upload>
                              <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px">
                                <div
                                  v-for="(_item, _index) in formData[item.form?.name!]"
                                  :key="_item.url"
                                  style="padding: 10px; border: 1px solid #ccc; border-radius: 5px; display: flex; flex-direction: column; align-items: center"
                                  class="w-200px"
                                >
                                  <video :src="_item.url" controls style="width: 100%; height: auto" />
                                  <el-text type="danger" size="small" class="mt-10px cursor-pointer" @click="handleVideoRemove(_index, item)"> 删除 </el-text>
                                </div>
                              </div>
                            </div>

                            <!-- 树形单选 -->
                            <tree-selector
                              v-else-if="item.form?.type === 'treeSelector'"
                              :tree-data="item.form.option?.data"
                              @update:selected-nodes="getSelectNodes($event, item.form?.name!)"
                            />
                            <!-- number input -->
                            <el-input
                              v-else-if="item.form?.type === 'textNumber'"
                              v-model="formData[item.form?.name!]"
                              :style="{ width: item.form?.width || '100%' }"
                              :placeholder="item.form!.placeholder ? item.form!.placeholder : '请输入' + item.form!.label"
                              :disabled="isDisabled(item)"
                              type="text"
                              clearable
                              @input="
                                (value: string) => {
                                  formData[item.form?.name!] = value.replace(/\D/g, '')
                                }
                              "
                            >
                              <template v-if="item.form?.prefix" #prepend>
                                <div>
                                  {{ item.form?.prefix }}
                                </div>
                              </template>
                              <template v-if="item.form?.units" #append>
                                <el-select v-model="formData[item.form!.name! + 'Unit']" placeholder="请选择" style="width: 88px">
                                  <el-option v-for="(_item, _index) in item.form?.units" :key="_index" :label="_item.name" :value="_item.value" />
                                </el-select>
                              </template>
                            </el-input>
                            <!-- 默认都是input -->

                            <el-input
                              v-else
                              v-model="formData[item.form?.name!]"
                              :style="{ width: item.form?.width || '100%' }"
                              :placeholder="item.form!.placeholder ? item.form!.placeholder : '请输入' + item.form!.label"
                              :disabled="isDisabled(item)"
                              :type="item.form?.type === 'inputNumber' ? 'number' : 'text'"
                              clearable
                              @input="
                                (value: string) => {
                                  if (item.form?.type == 'text') {
                                    formData[item.form?.name!] = value.replace(/\s+/g, '')
                                  }
                                }
                              "
                            >
                              <template v-if="item.form?.prefix" #prepend>
                                <div>
                                  {{ item.form?.prefix }}
                                </div>
                              </template>
                              <template v-if="item.form?.units" #append>
                                <el-select v-model="formData[item.form!.name! + 'Unit']" placeholder="请选择" style="width: 88px">
                                  <el-option v-for="(_item, _index) in item.form?.units" :key="_index" :label="_item.name" :value="_item.value" />
                                </el-select>
                              </template>
                            </el-input>
                            <!-- 单位 -->
                            <div v-if="item.form?.unit" class="ml-5px">
                              {{ item.form?.unit }}
                            </div>
                            <el-tooltip v-if="item.form?.tips" effect="light" :content="item.form?.tips" placement="top">
                              <el-icon color="#f3d19e" class="ml-5px">
                                <WarningFilled />
                              </el-icon>
                            </el-tooltip>
                            <div v-if="item.form?.longTerm" class="ml-5px">
                              <el-checkbox
                                v-model="formData[item.form?.name! + 'longTerm']"
                                label="长期"
                                :disabled="isDisabled(item)"
                                @change="(e: any) => handleLongTermChange(e, item)"
                              />
                            </div>
                          </div>
                        </el-form-item>
                      </div>
                    </el-col>
                  </template>
                </template>
              </el-row>
            </el-card>
            <!-- 未分组的字段 -->
            <el-row v-else>
              <template v-for="(item, index) in group.items" :key="index">
                <template v-if="(item?.insertEnable && !props.isEdit) || (item?.updateEnable && props.isEdit)">
                  <el-col
                    v-if="
                      (!item.form?.dependsOn ||
                        (Array.isArray(item.form.dependsOn) &&
                          item.form.dependsOn.every((depend: DependsOn) => {
                            if (!depend) return true
                            if (!depend.operator) {
                              depend.operator = 'eq'
                            }
                            let target = formData[depend.field!]
                            let when = depend.when!
                            // if (typeof target === 'undefined' || typeof when === 'undefined') {
                            //   return false
                            // }
                            if (!operatorCalculate(target, when, depend.operator)) {
                              if (item.form?.name) {
                                formData[item.form.name] = undefined
                              }
                            }
                            return operatorCalculate(target, when, depend.operator)
                          }))) &&
                      (item.form?.canShow === true || !item.form)
                    "
                    :span="item.form?.colSpan * 1 ? item.form?.colSpan * 1 : 24"
                  >
                    <!-- selectMenuData -->
                    <select-menu-data
                      v-if="item.form?.type === 'selectMenuData'"
                      :data-column-form="item.form"
                      @column-field="(e: any) => (formData[item.form?.name!] = e)"
                    />
                    <!-- addMenuData -->
                    <add-menu-data
                      v-else-if="item.form?.type === 'addMenuData'"
                      :data-column-form="item.form"
                      @column-field="(e: any) => (formData[item.form?.name!] = e)"
                    />
                    <div v-else>
                      <el-form-item
                        :key="index"
                        :label="item.form?.label"
                        :prop="item.form?.name"
                        :rules="item.form?.rules"
                        :label-width="item.form?.labelWidth ?? '120px'"
                      >
                        <div class="" style="width: 100%; display: flex; align-items: center">
                          <!-- textarea -->
                          <el-input
                            v-if="item.form?.type === 'textarea'"
                            v-model="formData[item.form.name!]"
                            type="textarea"
                            :rows="5"
                            :disabled="isDisabled(item)"
                            :placeholder="item.form.placeholder ? item.form.placeholder : '请输入' + item.form.label"
                          />
                          <!-- location 地址 -->
                          <el-input
                            v-else-if="item.form?.type === 'location'"
                            v-model="formData[item.form.name!]"
                            type="text"
                            :readonly="item.form?.canEdit"
                            :placeholder="item.form.placeholder ? item.form.placeholder : '请输入' + item.form.label"
                            @click.native.stop="locationAddress(item.form)"
                            @input="
                              (value: string) => {
                                formData[item.form?.name!] = value.replace(/\s+/g, '')
                              }
                            "
                          />
                          <!-- 数字输入框 -->
                          <el-input-number
                            v-else-if="item.form?.type === 'number'"
                            v-model="formData[item.form.name!]"
                            controls-position="right"
                            style="width: 100%"
                            :min="item.form?.min"
                            :max="item.form?.max"
                            :step="item.form?.step"
                            :precision="item.form?.precision ?? 0"
                            :placeholder="item.form.placeholder ? item.form.placeholder : '请输入' + item.form.label"
                            :disabled="isDisabled(item)"
                          >
                            <template v-if="item.form?.prefix" #prefix>
                              <div>
                                {{ item.form?.prefix }}
                              </div>
                            </template>
                          </el-input-number>
                          <!-- 开关 -->
                          <el-switch
                            v-else-if="item.form?.type === 'switch'"
                            v-model="formData[item.form.name!]"
                            inline-prompt
                            active-text="启用"
                            inactive-text="停用"
                            :disabled="isDisabled(item)"
                          />
                          <div v-else-if="item.form?.type === 'select'" style="width: 100%">
                            <!-- 下拉选择 -->
                            <el-select
                              v-model="formData[item.form.name!]"
                              style="width: 100%"
                              :value-key="item.form.name"
                              :multiple="item.form?.option?.multiple"
                              :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                              :disabled="isDisabled(item)"
                              :no-data-text="item.form?.option?.emptyTip"
                              filterable
                              clearable
                              @keydown.space.prevent
                            >
                              <el-option
                                v-for="i in item.form?.option?.data"
                                :key="i.value"
                                :label="i.label"
                                :value="i.value"
                                :disabled="i?.disabled ? i.disabled : false"
                              />
                            </el-select>
                            <!-- 如果当前下拉项下存在desc,需要显示 -->
                            <div v-for="i in item.form?.option?.data" :key="i.value">
                              <div v-if="i.value === formData[item.form.name!] && i.desc" class="select-desc" v-html="i.desc" />
                            </div>
                          </div>
                          <div v-else-if="item.form?.type === 'autocompleteSelect'" style="width: 100%">
                            <el-autocomplete
                              popper-class="my-selects"
                              :fit-input-width="true"
                              v-model="formData[item.form.name!]"
                              value-key="value"
                              style="width: 100%"
                              class="autocomplete"
                              clearable
                              @keydown.space.prevent
                              :placeholder="item.form.placeholder ? item.form.placeholder : '模糊搜索' + item.form.label"
                              :fetch-suggestions="((query: any, cb: any) => querySearchAsync(query, cb, item.form?.name || '')) as any"
                            >
                              <template #default="{ item }">
                                <!-- <span class="link">{{ item.label }}</span> -->
                                <div class="flex items-center justify-between">
                                  <span>{{ item.label }}</span>
                                </div>
                              </template>
                            </el-autocomplete>
                          </div>
                          <div v-else-if="item.form?.type === 'fuzzySelect'" style="width: 100%">
                            <!-- 远程模糊搜索下拉 -->
                            <el-select
                              popper-class="my-selects"
                              v-model="formData[item.form.name!]"
                              style="width: 100%"
                              :fit-input-width="true"
                              remote-show-suffix
                              clearable
                              :value-key="item.form.name"
                              :multiple="item.form?.option?.multiple"
                              filterable
                              remote
                              reserve-keyword
                              :placeholder="item.form.placeholder ? item.form.placeholder : '模糊搜索' + item.form.label"
                              :remote-method="(query: string) => remoteSelectMethod(query, item.form?.name!)"
                              :disabled="isDisabled(item)"
                              :loading="fuzzySelectLoading"
                              @keydown.space.prevent
                            >
                              <el-option
                                v-for="i in item.form?.option?.data || []"
                                :key="i.value"
                                :label="i.label"
                                :value="i.value"
                                :disabled="i?.disabled ? i.disabled : false"
                                style="width: 100%;"
                              >
                
                                 <div style="width: 100%;"> 
                                  {{ i.label }}
                                 </div>
                              </el-option>
                            </el-select>
                          </div>
                          <!-- 下拉树 -->
                          <el-tree-select
                            v-else-if="item.form?.type === 'selectTree'"
                            v-model="formData[item.form.name!]"
                            default-expand-all
                            filterable
                            style="width: 100%"
                            :multiple="item.form?.option?.multiple"
                            :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                            :data="item.form.option?.data"
                            :check-strictly="item.form?.option?.checkStrictly || false"
                            :props="{ value: 'value', label: 'label' }"
                            :disabled="isDisabled(item)"
                            clearable
                            :style="{ width: item.form?.width || '100%' }"
                            @keydown.space.prevent
                          >
                            <template #default="{ data }">
                              {{ data.label }}
                            </template>
                          </el-tree-select>

                          <!-- 级联选择器-省市区 -->
                          <el-cascader
                            v-else-if="item.form?.type === 'cascader'"
                            v-model="formData[item.form.name!]"
                            style="width: 100%"
                            filterable
                            collapse-tags
                            collapse-tags-tooltip
                            :options="item.form!.option?.data as any"
                            :props="{
                              multiple: item.form.option?.multiple,
                              checkStrictly: item.form!.option?.checkStrictly,
                            }"
                            clearable
                            :style="{ width: item.form?.width || '100%' }"
                            :disabled="isDisabled(item)"
                          />
                          <!-- 日区间、月区间-->
                          <el-date-picker
                            v-else-if="item.form?.type === 'daterange' || item.form?.type === 'monthrange' || item.form?.type === 'datetimerange'"
                            v-model="formData[item.form.name as keyof TableItem] as any"
                            :type="item.form?.type"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                            :disabled="isDisabled(item)"
                            :format="item.form.format"
                            :value-format="item.form.format"
                            clearable
                            :style="{ width: item.form?.width || '100%' }"
                            :shortcuts="ShortcutsList"
                          />
                          <!-- 周、年、月、日 -->
                          <el-date-picker
                            v-else-if="item.form?.type === 'week' || item.form?.type === 'year' || item.form?.type === 'month' || item.form?.type === 'date'"
                            v-model="formData[item.form.name as keyof TableItem] as any"
                            :type="item.form?.type"
                            :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                            :disabled="isDisabled(item)"
                            :format="item.form.format"
                            :value-format="item.form.format"
                            clearable
                            :style="{ width: item.form?.width || '100%' }"
                            :disabled-date="
                              (data: Date) => {
                                if (!item.form?.disabledDateType) return false
                                const type = item.form?.type || 'date'
                                const disabledDateType = item.form?.disabledDateType
                                const today = new Date()
                                today.setHours(0, 0, 0, 0) // 清除时间部分

                                if (type === 'date' || type === 'week') {
                                  const currentDate = new Date(data)
                                  currentDate.setHours(0, 0, 0, 0)
                                  return disabledDateType === 'after' ? currentDate > today : currentDate < today
                                } else if (type === 'month') {
                                  const currentYear = today.getFullYear()
                                  const currentMonth = today.getMonth()
                                  const dataYear = data.getFullYear()
                                  const dataMonth = data.getMonth()
                                  return disabledDateType === 'after'
                                    ? dataYear > currentYear || (dataYear === currentYear && dataMonth > currentMonth)
                                    : dataYear < currentYear || (dataYear === currentYear && dataMonth < currentMonth)
                                } else if (type === 'year') {
                                  const currentYear = today.getFullYear()
                                  const dataYear = data.getFullYear()
                                  return disabledDateType === 'after' ? dataYear > currentYear : dataYear < currentYear
                                }
                                return false
                              }
                            "
                            @change="(val: any) => handleDateChange(val, item)"
                          />

                          <!-- 时分秒 -->
                          <el-time-picker
                            v-else-if="item.form?.type === 'time'"
                            v-model="formData[item.form.name!]"
                            :format="item.form.format"
                            :value-format="item.form.format"
                            :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                            :disabled="isDisabled(item)"
                            clearable
                            :style="{ width: item.form?.width || '200px' }"
                          />
                          <!--  时分秒-时分秒 -->
                          <el-time-picker
                            v-else-if="item.form?.type === 'timerange'"
                            v-model="formData[item.form.name!]"
                            is-range
                            :format="item.form.format"
                            :disabled="isDisabled(item)"
                            :value-format="item.form.format"
                            range-separator="-"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            clearable
                            :style="{ width: item.form?.width || '100%' }"
                          />

                          <!-- 年月日时分秒 -->
                          <el-date-picker
                            v-else-if="item.form?.type === 'datetime'"
                            v-model="formData[item.form.name!]"
                            type="datetime"
                            :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                            :format="item.form.format"
                            :value-format="item.form.format"
                            :disabled="isDisabled(item)"
                            :default-time="item.form?.defaultTime ? new Date(`2000-01-01 ${item.form.defaultTime}`) : new Date(2000, 0, 1, 0, 0, 0)"
                            clearable
                            :style="{ width: item.form?.width || '100%' }"
                          />
                          <!-- 时间选择 -->
                          <el-time-select
                            v-else-if="item.form?.type === 'timeSelect'"
                            v-model="formData[item.form!.name!]"
                            start="08:30"
                            step="00:15"
                            end="18:30"
                            :placeholder="item.form.placeholder ? item.form.placeholder : '请选择' + item.form.label"
                            :disabled="isDisabled(item)"
                            clearable
                            :style="{ width: item.form?.width || '100%' }"
                          />
                          <!-- 单选 -->
                          <el-radio-group v-else-if="item.form?.type === 'radiogroup'" v-model="formData[item.form.name!]" :disabled="isDisabled(item)">
                            <el-radio v-for="_item in item.form.option" :label="_item.value">
                              {{ _item.label }}
                            </el-radio>
                          </el-radio-group>
                          <!-- CheckBox多选 -->
                          <div v-else-if="item.form?.type === 'checkboxgroup'">
                            <el-input v-model="fuzzyCheckboxInput" placeholder="模糊搜索" style="width: 220px" @input="(e: string) => fuzzyInputFun(e, item)" />
                            <div>
                              <el-checkbox-group v-model="formData[item.form.name!]" :disabled="isDisabled(item)">
                                <div>
                                  <el-checkbox
                                    v-for="_item in item.form!.option?.filterData || item.form!.option?.data"
                                    class="mt-2 mr-10px!"
                                    :label="_item.value"
                                    :border="item.form?.showCheckboxBorder ? true : true"
                                  >
                                    {{ _item.label }}
                                  </el-checkbox>
                                </div>
                              </el-checkbox-group>
                            </div>
                          </div>
                          <!-- 图片上传 -->
                          <el-upload
                            v-else-if="item.form?.type === 'uploadImage'"
                            ref="uploadImageRef"
                            accept="image/*"
                            drag
                            :class="{
                              hide:
                                (item.form?.editOn && isDisabled(item)) ||
                                (formData[item.form?.name!] && formData[item.form?.name!].length >= item.form.imageOption?.count!),
                            }"
                            :auto-upload="false"
                            :file-list="formData[item.form?.name!] ? formData[item.form?.name!] : []"
                            :limit="item.form.imageOption?.count"
                            :multiple="item.form.imageOption?.multiple"
                            :on-change="
                              (file: any, fileList: any[]) => {
                                uploadImage(file, fileList, item)
                              }
                            "
                            :on-remove="
                              (file: any, fileList: any[]) => {
                                handleOldRemove(file, fileList, item)
                              }
                            "
                            :on-preview="handlePictureCardPreview"
                            :disabled="isDisabled(item)"
                            list-type="picture-card"
                            action="#"
                          >
                            <img src="@/assets/images/upload_icon.png" mode="scaleToFill" style="width: 40px; height: auto" />
                            <div class="el-upload__text text-12px">
                              <em>拖拽或点击上传</em>
                            </div>
                            <template v-if="item.form.imageOption?.tips" #tip>
                              <div>
                                <el-tag type="warning">
                                  <div class="flex items-center">
                                    <el-icon>
                                      <Warning />
                                    </el-icon>
                                    {{ item.form.imageOption?.tips }}
                                  </div>
                                </el-tag>
                              </div>
                            </template>
                          </el-upload>
                          <el-upload
                            v-else-if="item.form?.type === 'uploadImageV2'"
                            ref="uploadImageRef"
                            accept="image/*"
                            drag
                            :class="{ hide: formData[item.form?.name!] && formData[item.form?.name!].length >= item.form.imageOption?.count! }"
                            :auto-upload="false"
                            :file-list="formData[item.form?.name!] ? formData[item.form?.name!] : []"
                            :limit="item.form.imageOption?.count"
                            :multiple="item.form.imageOption?.multiple"
                            :on-change="
                              (file: any, fileList: any[]) => {
                                uploadImageV2(file, fileList, item)
                              }
                            "
                            :on-remove="
                              (file: any, fileList: any[]) => {
                                handleOldRemove(file, fileList, item)
                              }
                            "
                            :on-preview="handlePictureCardPreview"
                            :disabled="isDisabled(item)"
                            list-type="picture-card"
                            action="#"
                          >
                            <!-- <el-icon size="20px">
                            <upload-filled />
                          </el-icon> -->
                            <img src="@/assets/images/upload_icon.png" mode="scaleToFill" style="width: 40px; height: auto" />
                            <div class="el-upload__text text-12px">
                              <em>拖拽或点击上传</em>
                            </div>
                            <template v-if="item.form.imageOption?.tips" #tip>
                              <div>
                                <el-tag type="warning">
                                  <div class="flex items-center">
                                    <el-icon>
                                      <Warning />
                                    </el-icon>
                                    {{ item.form.imageOption?.tips }}
                                  </div>
                                </el-tag>
                              </div>
                            </template>
                          </el-upload>
                          <!-- 文件上传 -->
                          <el-upload
                            v-else-if="item.form?.type === 'uploadFile'"
                            ref="uploadFileRef"
                            :accept="item.form?.fileOption?.accept || '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar,.7z'"
                            :file-list="
                              formData[item.form?.name!]
                                ? formData[item.form?.name!].map((_item: any) => ({ name: _item.split('/').pop() || item, url: _item }))
                                : []
                            "
                            action="#"
                            :auto-upload="false"
                            :multiple="item.form?.fileOption?.multiple || false"
                            :limit="item.form.fileOption?.count"
                            :on-change="
                              (file: any, fileList: any[]) => {
                                uploadFile(file, fileList, item)
                              }
                            "
                            :on-remove="
                              (file: any, fileList: any[]) => {
                                handleRemove(file, fileList, item)
                              }
                            "
                            :disabled="isDisabled(item)"
                          >
                            <el-icon>
                              <Plus />
                            </el-icon>
                            <div class="el-upload__text text-12px">
                              <em>拖拽或点击上传</em>
                            </div>
                            <template #trigger>
                              <el-button type="primary"> 点击上传 </el-button>
                            </template>
                          </el-upload>
                          <!-- ocr识别 -->
                          <div v-else-if="item.form?.type === 'fileOcr' || item.form?.type === 'uploadFileV2'" class="w-100%">
                            <el-upload
                              ref="uploadFileRef"
                              :accept="item.form?.fileOption?.accept || '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar'"
                              :file-list="formData[item.form?.name!] ? formData[item.form?.name!] : []"
                              drag
                              action="#"
                              list-type="picture-card"
                              :show-file-list="false"
                              :auto-upload="false"
                              :multiple="item.form?.fileOption?.multiple || false"
                              :limit="item.form.fileOption?.count"
                              :on-change="
                                (file: any, fileList: any[]) => {
                                  uploadFileAdvance(file, fileList, item)
                                }
                              "
                              :on-remove="
                                (file: any, fileList: any[]) => {
                                  handleRemove(file, fileList, item)
                                }
                              "
                              :on-exceed="
                                (files: any, fileList: any[]) => {
                                  handleExceed(files, fileList, item)
                                }
                              "
                              :disabled="isDisabled(item)"
                            >
                              <img src="@/assets/images/upload_icon.png" mode="scaleToFill" style="width: 40px; height: auto" />
                              <div class="el-upload__text text-12px">
                                <em class="text-12px">拖拽或点击上传</em>
                              </div>
                              <template v-if="item.form.fileOption?.tips" #tip>
                                <div>
                                  <el-tag type="warning">
                                    <div class="flex items-center">
                                      <el-icon>
                                        <Warning />
                                      </el-icon>
                                      {{ item.form.fileOption?.tips }}
                                    </div>
                                  </el-tag>
                                </div>
                              </template>
                              <!-- <template #trigger>
                              <el-button type="primary">点击上传</el-button>
                            </template> -->
                            </el-upload>

                            <!-- 文件展示区域 -->
                            <div v-if="formData[item.form?.name!] && formData[item.form?.name!].length > 0" class="mt-4">
                              <!-- 图片展示区域 -->
                              <div v-if="hasImageFiles(formData[item.form?.name!])" class="mb-4">
                                <div class="text-gray-600 mb-2">图片文件</div>
                                <div class="flex flex-wrap gap-4">
                                  <div v-for="(file, fileIndex) in getImageFiles(formData[item.form?.name!])" :key="fileIndex" class="relative">
                                    <el-image
                                      :preview-src-list="[file.url || file]"
                                      :src="file.url || file"
                                      class="w-100px h-auto object-cover border rounded"
                                    />
                                    <el-button type="danger" size="small" circle class="absolute top-1 right-1" @click="handleImageRemove(file, item)">
                                      <el-icon>
                                        <Delete />
                                      </el-icon>
                                    </el-button>
                                  </div>
                                </div>
                              </div>

                              <!-- 其他文件展示区域 -->
                              <div v-if="hasOtherFiles(formData[item.form?.name!])" class="mb-4 w-100%">
                                <div class="text-gray-600 mb-2 w-100%">其他文件</div>
                                <el-table :data="getOtherFiles(formData[item.form?.name!])" style="width: 100%">
                                  <el-table-column prop="name" label="文件名">
                                    <template #default="{ row }">
                                      <el-link :href="row.url" target="_blank">{{ getFileName(row.url || row) }}</el-link>
                                    </template>
                                  </el-table-column>
                                  <el-table-column label="操作" width="120">
                                    <template #default="{ row }">
                                      <el-button type="danger" size="small" @click="handleOtherFileRemove(row, item)"> 删除 </el-button>
                                    </template>
                                  </el-table-column>
                                </el-table>
                              </div>
                            </div>
                          </div>
                          <div v-else-if="item.form?.type === 'uploadVideoFull'">
                            <el-upload
                              ref="uploadVideoRef"
                              accept="video/*"
                              :auto-upload="false"
                              :on-change="
                                (file: any, fileList: any[]) => {
                                  uploadVideo(file, fileList, item)
                                }
                              "
                              :multiple="item.form?.videoOption?.multiple || false"
                              :limit="item.form.videoOption?.count"
                              list-type="text"
                              action="#"
                              :show-file-list="false"
                            >
                              <!-- 目前上传视频中状态，停用失效 -->
                              <el-button
                                v-if="(formData[item.form?.name!]?.length || 0) < (item.form.videoOption?.count || 1)"
                                :disabled="isDisabled(item) || item.uploadLoading"
                                type="primary"
                                :loading="item.uploadLoading"
                              >
                                {{ item.uploadLoading ? '上传中...' : '点击上传' }}
                              </el-button>
                              <template v-if="item.form.videoOption?.tips" #tip>
                                <div>
                                  <el-tag type="warning">
                                    <div class="flex items-center">
                                      <el-icon>
                                        <Warning />
                                      </el-icon>
                                      {{ item.form.videoOption?.tips }}
                                    </div>
                                  </el-tag>
                                </div>
                              </template>
                            </el-upload>
                            <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px">
                              <div
                                v-for="(_item, _index) in formData[item.form?.name!]"
                                :key="_item.url"
                                style="padding: 10px; border: 1px solid #ccc; border-radius: 5px; display: flex; flex-direction: column; align-items: center"
                                class="w-200px"
                              >
                                <video :src="_item.url" controls style="width: 100%; height: auto" />
                                <el-text type="danger" class="mt-10px cursor-pointer" @click="handleVideoRemove(_index, item)"> 删除 </el-text>
                              </div>
                            </div>
                          </div>

                          <!-- 树形单选 -->
                          <tree-selector
                            v-else-if="item.form?.type === 'treeSelector'"
                            :tree-data="item.form.option?.data"
                            @update:selected-nodes="getSelectNodes($event, item.form?.name!)"
                          />
                          <!-- number input -->
                          <el-input
                            v-else-if="item.form?.type === 'textNumber'"
                            v-model="formData[item.form?.name!]"
                            :style="{ width: item.form?.width || '100%' }"
                            :placeholder="item.form!.placeholder ? item.form!.placeholder : '请输入' + item.form!.label"
                            :disabled="isDisabled(item)"
                            type="text"
                            clearable
                            @input="
                              (value: string) => {
                                formData[item.form?.name!] = value.replace(/\D/g, '')
                              }
                            "
                          >
                            <template v-if="item.form?.prefix" #prepend>
                              <div>
                                {{ item.form?.prefix }}
                              </div>
                            </template>
                            <template v-if="item.form?.units" #append>
                              <el-select v-model="formData[item.form!.name! + 'Unit']" placeholder="请选择" style="width: 88px">
                                <el-option v-for="(_item, _index) in item.form?.units" :key="_index" :label="_item.name" :value="_item.value" />
                              </el-select>
                            </template>
                          </el-input>
                          <!-- 自定义选择组 -->
                          <custom-select-group
                            v-else-if="item.form?.type === 'customerSelectGroup'"
                            ref="customSelectGroupRef"
                            :parent-form-data="formData"
                            :data-column-form="item.form"
                          />
                          <!-- 默认都是input -->

                          <el-input
                            v-else
                            v-model="formData[item.form?.name!]"
                            :style="{ width: item.form?.width || '100%' }"
                            :placeholder="item.form!.placeholder ? item.form!.placeholder : '请输入' + item.form!.label"
                            :disabled="isDisabled(item)"
                            :type="item.form?.type === 'inputNumber' ? 'number' : 'text'"
                            clearable
                            @input="
                              (value: string) => {
                                formData[item.form?.name!] = value.replace(/\s+/g, '')
                              }
                            "
                          >
                            <template v-if="item.form?.prefix" #prepend>
                              <div>
                                {{ item.form?.prefix }}
                              </div>
                            </template>
                            <template v-if="item.form?.units" #append>
                              <el-select v-model="formData[item.form!.name! + 'Unit']" placeholder="请选择" style="width: 88px">
                                <el-option v-for="(_item, _index) in item.form?.units" :key="_index" :label="_item.name" :value="_item.value" />
                              </el-select>
                            </template>
                          </el-input>
                          <!-- 单位 -->
                          <div v-if="item.form?.unit" class="ml-5px">
                            {{ item.form?.unit }}
                          </div>
                          <el-tooltip v-if="item.form?.tips" effect="light" :content="item.form?.tips" placement="top">
                            <el-icon color="#f3d19e" class="ml-5px">
                              <WarningFilled />
                            </el-icon>
                          </el-tooltip>
                          <div v-if="item.form?.longTerm" class="ml-5px">
                            <el-checkbox
                              v-model="formData[item.form?.name! + 'longTerm']"
                              label="长期"
                              :disabled="isDisabled(item)"
                              @change="(e: any) => handleLongTermChange(e, item)"
                            />
                          </div>
                        </div>
                      </el-form-item>
                    </div>
                  </el-col>
                </template>
              </template>
            </el-row>
          </template>
        </el-form>
      </div>
      <div v-if="showMap === 'geoFence'" class="ml-2">
        <!-- 地图插件 -->
        <area-map-component
          ref="rightAreaMapComponent"
          :is-edit="props.isEdit"
          :height="formHeight"
          :enclosure-data="{
            coordinate: formData.coordinate,
            quyu: formData.quyu,
            radius: formData.radius,
          }"
        />
      </div>
    </el-scrollbar>
    <template #footer>
      <div class="dialog-footer">
        <!-- 如果form存在并且formUri有值则使用数据返回的按钮，否则使用默认按钮 -->
        <div v-if="btnMenu.meta?.form?.btns">
          <div v-for="(item, index) in btnMenu.meta?.form?.btns" :key="index" class="inline-block">
            <el-button
              v-if="
                !item.dependsOn ||
                (item.dependsOn &&
                  item.dependsOn.every((depend: DependsOn) => {
                    //遍历item.dependsOn->depend，如果depend.field==='listData'的话selectTableColumn[0][depend.field!] === depend.when并且depend.field==='responseData'的话formData[depend.field!] === depend.when再显示
                    var target = ''
                    if (depend.dependFrom === 'listData') {
                      target = selectTableColumn[0][depend.field!]
                    } else if (depend.dependFrom === 'responseData') {
                      target = formData[depend.field!]
                    }
                    if (!depend.operator) {
                      depend.operator = 'eq'
                    }
                    var when = depend.when!
                    return operatorCalculate(target, when, depend.operator)
                  }))
              "
              v-loading="submitLoading"
              class="mr-2"
              :color="item.background"
              :data-uri="item.uri"
              @click="item.label === '取消' ? closeDialog() : handleSubmit(formRef, item)"
            >
              {{ item.label }}
            </el-button>
          </div>
        </div>
        <div v-else>
          <el-button type="primary" :disabled="loading" :data-uri="requestUri" @click="handleSubmit(formRef)"> 确定 </el-button>
          <el-button @click="closeDialog()"> 取 消 </el-button>
        </div>
      </div>
    </template>
    <!-- 地图位置搜索 -->
    <!-- :pointData="{ coordinate: formData.coordinate, address: formData.address }" -->
    <map-search-dialog
      v-if="searchDialog.visible"
      :dialog="searchDialog"
      :point-data="formData"
      @close-map-dialog="closeMapDialog"
      @submit-location="submitLocation"
    />
    <!-- 图片查看弹窗 -->
    <pic-dialog-component ref="picDialogRef" :image-list="dialogImageUrl" />
  </el-dialog>
</template>
<script setup lang="ts">
  import { cloneDeep } from 'lodash'
  import AreaMapComponent from '@/components/AreaMapComponent/index.vue'
  import MapSearchDialog from '@/components/MapSearchDialog/index.vue'
  import PicDialogComponent from '@/components/PicDialogComponent/index.vue'
  import TreeSelector from '@/components/TreeSelector/index.vue'
  import { PropType } from 'vue'
  // 状态管理依赖
  import { useButtonGroupStore } from '@/store/modules/buttonGroup.js'
  import { getBase64 } from '@/utils'
  import { uploadApi, uploadFileApi } from '@/api/auth'
  import { genFileId, UploadProps, UploadRawFile } from 'element-plus'
  import { UploadImageVO } from '@/types/global'
  import { useFormStore } from '@/store/modules/form'
  import { composeRequestParams, getSelectOptions, operatorCalculate, getLocalData } from '@/utils/common'
  import { globalRequestApi, globalRequestUrlApi } from '@/api/planManagement'
  import { ShortcutsList } from '@/utils/index'
  import { Plus, WarningFilled, Delete } from '@element-plus/icons-vue'
  import SelectMenuData from './components/SelectMenuDataComponent.vue'
  import AddMenuData from './components/AddMenuDataComponent.vue'
  import CustomSelectGroup from './components/CustomSelectGroupComponent.vue'
  import { globalUploadVideoV2Api } from '@/api/Global'
  import type { AutocompleteFetchSuggestions } from 'element-plus'
  const formHeight = ref('') //左侧表单高度（让右侧地图高度=表单高度）
  const uploadFileRef = ref()
  const uploadVideoRef = ref()
  const buttonGroupStore = useButtonGroupStore()
  const formStore = useFormStore()
  const customSelectGroupRef = ref()
  /**
   * 是否展示地图 值为geoFence显示
   */
  const { showMap } = storeToRefs(buttonGroupStore)
  const props = defineProps({
    loading: {
      require: true,
      type: Boolean,
      default: false,
    },
    ids: {
      require: true,
      type: Array as PropType<string[]>,
      default: () => {
        return []
      },
    },
    /**
     * 提交表单后是否需要刷新菜单数
     */
    refreshMenuCount: {
      require: true,
      type: Boolean,
      default: false,
    },
    /**
     * 当前是否为编辑状态
     */
    isEdit: {
      require: true,
      type: Boolean,
      default: false,
    },
    /**
     * 当前是否为复制状态
     */
    isCopy: {
      require: true,
      type: Boolean,
      default: false,
    },
    /**
     * 弹窗属性
     */
    dialog: {
      require: true,
      type: Object,
      default: () => {
        return {}
      },
    },
    /**
     * 列表项
     */
    dataColumn: {
      require: true,
      type: Array as PropType<TableItem[]>,
      default: () => {
        return []
      },
    },
    /**
     * 请求的地址前缀
     */
    requestUri: {
      require: true,
      type: String,
      default: '',
    },
    /**
     * 表单按钮组
     */
    btnMenu: {
      require: true,
      type: Object as PropType<MenuVO>,
      default: () => {
        return {}
      },
    },
    /**
     * 选择的列表项数据
     */
    selectTableColumn: {
      require: true,
      type: Array<any>,
      default: () => {
        return []
      },
    },
    /**
     * 当前选中的行
     */
    currentRow: {
      require: true,
      type: Object,
      default: () => {
        return {}
      },
    },
    /**
     * 指定默认值
     */
    defaultValueParams: {
      require: true,
      type: String,
      default: '',
    },
  })
  const isDisabled = (item: TableItem) => {
    if (props.isEdit) {
      if (item.form?.editOn) {
        const result = item.form.editOn.every((depend: DependsOn) => {
          if (!depend.operator) {
            depend.operator = 'eq'
          }
          var target = formData.value[depend.field!]
          var when = depend.when!
          return !operatorCalculate(target, when, depend.operator)
        })
        return result
      } else {
        return item.form?.canEdit === false || !item.form
      }
    } else {
      return item.form?.canInsert === false || !item.form
    }
  }
  const isFullscreen = ref<any>(false)

  watch(
    () => props.dialog,
    (data) => {
      nextTick(() => {
        filterDataColumn.value.forEach((item) => {
          //弹窗初始化清空filterDataColumn.value对应项的下拉data
          if (item.form?.type === 'fuzzySelect') {
            item.form!.option!.data = []
          }
        })
      })
    },
    {
      immediate: true,
      deep: true,
    },
  )

  const querySearchAsync = async (query: string, cb: (suggestions: any) => void, key: string) => {
    if (query) {
      const item = filterDataColumn.value.find((item) => item.form?.name === key)
      if (item) {
        const now = Date.now()
        if (lastFuzzySelectCall.value[key] && now - lastFuzzySelectCall.value[key] < fuzzySelectDebounceTime) {
          cb([])
          return
        }
        lastFuzzySelectCall.value[key] = now

        fuzzySelectLoading.value = true
        try {
          const dependOnParams: any = {}
          if (item.form?.option?.dependOn) {
            dependOnParams[item.form.option.dependOn] = formData.value[item.form.option.dependOn]
            if (!dependOnParams[item.form.option.dependOn]) {
              formData.value[key] = undefined
              if (item.form?.option?.data) {
                item.form.option.data = []
              }
              fuzzySelectLoading.value = false
              cb([])
              return
            }
          }

          query = query.replace(/\s*/g, '')
          const items = filterDataColumn.value.find((item) => item.form?.name === key) as any
          getLocalData(items, {}, key, {
            keyword: query,
            ...dependOnParams,
          })
            .then((result: any) => {
              const { localData } = result
              // 确保返回正确的数据格式
              cb(
                localData.map((item: any) => ({
                  value: item.value,
                  label: item.label,
                })),
              )
              fuzzySelectLoading.value = false
            })
            .catch((error) => {
              console.error('模糊搜索出错:', error)
              cb([]) // 错误时返回空数组
              fuzzySelectLoading.value = false
            })
        } catch (error) {
          console.error('模糊搜索出错:', error)
          cb([]) // 错误时返回空数组
          fuzzySelectLoading.value = false
        }
      } else {
        cb([])
      }
    } else {
      cb([]) // 无查询条件时返回空数组
    }
  }
  /**
   * 模糊搜索checkbox
   */
  const fuzzyCheckboxInput = ref<string>('')
  //监听fuzzyCheckbox值的变化，模糊搜索，找出label中存在的
  const fuzzyInputFun = (e: string, item: TableItem) => {
    item.form!.option!.filterData = []
    let filterCheckBoxOptions = JSON.parse(JSON.stringify(item.form?.option.data ?? []))
    if (e) {
      filterCheckBoxOptions = item.form?.option.data.filter((item: any) => item.label.includes(e))
    } else {
      filterCheckBoxOptions = JSON.parse(JSON.stringify(item.form?.option.data ?? []))
    }
    item.form!.option.filterData = filterCheckBoxOptions
  }
  /**
   * 是否隐藏上传图片按钮
   */
  const hideUpload = ref<boolean>(false)
  /**
   * 表单参数
   */
  const formData = ref<any>({})
  /**
   * 对dataColumn进行排序展示
   */
  const emit = defineEmits([
    'closeDialog',
    'handleSubmit',
    'formData', //表单
    'clearFormColumn', //清除表单
    'uploadImage', //上传图片
  ])
  const formRef = ref()
  /**
   * 提交加载动画
   */
  const submitLoading = ref(false)

  const rightAreaMapComponent = ref()
  /**
   * 二级弹窗
   */
  const searchDialog = reactive<DialogOption>({
    visible: false,
  })
  const filterDataColumn = ref<TableItem[]>([])

  let oldState = cloneDeep(formData.value)
  const formLoading = ref(false)
  const isInitializing = ref(true) // 添加初始化标志位

  watch(
    () => props.dataColumn,
    (dataColumn: TableItem[]) => {
      // formLoading.value = true
      filterDataColumn.value = dataColumn.filter((item) => item.form)
      filterDataColumn.value.sort((item1, item2) => item1.form?.sortNo! - item2.form?.sortNo!)
      //以下代码 => 当下拉项只有一条的时候默认选中
      setTimeout(() => {
        // 设置标志位，表示这是 defaultValue 触发的变化
        isDefaultValueChange.value = true
        isInitializing.value = true // 设置初始化标志

        filterDataColumn.value.forEach((item: TableItem) => {
          let hasDefaultValue = Object.hasOwn(item.form || {}, 'defaultValue')
          //如果item.form中没有defaultValue这个字段，则设置单选项的默认值
          if (!hasDefaultValue && !props.isEdit) {
            if (item.form?.type === 'select' && item.form?.option?.data && item.form?.option?.data!.length === 1) {
              if (item.form.option.multiple) {
                formData.value[item.form.name!] = [item.form.option.data[0].value]
              } else {
                formData.value[item.form.name!] = item.form.option.data[0].value
              }
            }
          }
          //如果item.form.items存在，则遍历items.items
          if (item.form?.items) {
            item.form.items.forEach((item: any) => {
              handleCustomSelectGroupDefaultValue(item)
            })
          }

          //处理默认值
          handleDefaultValue(item)
          //默认单位下拉框默认选中第一个
          if (item.form?.units && item.form.units.length > 0 && !formData.value[item.form!.name!] && !props.isEdit && !props.isCopy) {
            formData.value[item.form!.name! + 'Unit'] = item.form!.units[0].value
          }
        })
        // 在下一个 tick 重置标志位
        nextTick(() => {
          isDefaultValueChange.value = false
          isInitializing.value = false // 重置初始化标志
        })
        formLoading.value = false
      }, 1000)

      nextTick(() => {
        //获取表单formRef高度
        if (formRef.value) {
          // formHeight.value = formRef.value.$el.offsetHeight + 'px'
          formHeight.value = '60vh' // 左侧表单和右侧表单最大值都为60vh
        }
      })
    },
    { deep: true, immediate: true },
  )

  // 添加一个全局的 ref 来跟踪是否是 defaultValue 触发的变化
  const isDefaultValueChange = ref(false)
  const handleCustomSelectGroupDefaultValue = (item: TableItem) => {
    if (
      (item.form?.defaultValue ||
        item.form?.defaultValue === false ||
        item.form?.defaultValue === 0 ||
        item.form?.defaultValue === '' ||
        item.form?.defaultValue === 'null' ||
        item.form?.defaultValue === 'undefined' ||
        item.form?.defaultValue === null) &&
      !formData.value[item.form!.name!]
    ) {
      formData.value[item.name!] = item.defaultValue
    }
  }
  const handleDefaultValue = (item: TableItem) => {
    //开关默认启用
    if (item.form?.type === 'switch') {
      if (!props.isEdit && !props.isCopy) {
        //如果item.form中存在defaultValue,则赋值给formData
        if (
          (item.form?.defaultValue ||
            item.form?.defaultValue === false ||
            item.form?.defaultValue === 0 ||
            item.form?.defaultValue === '' ||
            item.form?.defaultValue === 'null' ||
            item.form?.defaultValue === 'undefined' ||
            item.form?.defaultValue === null) &&
          !formData.value[item.form!.name!]
        ) {
          formData.value[item.form!.name!] = item.form!.defaultValue
        } else {
          formData.value[item.form!.name!] = true
        }
      }
    } else if (item.form?.needLeftId) {
      //如果是需要左侧id的下拉框默认回显
      if (!props.isEdit && !props.isCopy) {
        //如果item.form中存在defaultValue,则赋值给formData
        if (
          (item.form?.defaultValue ||
            item.form?.defaultValue === false ||
            item.form?.defaultValue === 0 ||
            item.form?.defaultValue === '' ||
            item.form?.defaultValue === 'null' ||
            item.form?.defaultValue === 'undefined' ||
            item.form?.defaultValue === null) &&
          !formData.value[item.form!.name!]
        ) {
          formData.value[item.form!.name!] = item.form!.defaultValue
        } else {
          formData.value[item.form!.name!] = props.defaultValueParams
        }
      }
    } else if (
      !props.isEdit &&
      !props.isCopy &&
      (item.form?.defaultValue ||
        item.form?.defaultValue === false ||
        item.form?.defaultValue === 0 ||
        item.form?.defaultValue === '' ||
        item.form?.defaultValue === 'null' ||
        item.form?.defaultValue === 'undefined' ||
        item.form?.defaultValue === null) &&
      !formData.value[item.form!.name!]
    ) {
      formData.value[item.form!.name!] = item.form!.defaultValue
    }
  }

  watch(
    () => formData.value,
    (newState) => {
      try {
        const localNewState = toRaw(newState)
        for (const key in newState) {
          // 检查当前键的值是否已清空(null、undefined、空字符串或空数组)
          const isCleared =
            localNewState[key] === null ||
            localNewState[key] === undefined ||
            localNewState[key] === '' ||
            (Array.isArray(localNewState[key]) && localNewState[key].length === 0)

          // 如果值被清空且旧值不为空，则清空所有依赖于此字段的表单项
          if (isCleared && oldState[key]) {
            // 查找所有依赖于此字段的表单项并清空
            filterDataColumn.value.forEach((item) => {
              if (item.form?.option?.dependOn === key) {
                clearFormItem(item)
                // 递归清空所有依赖链
                if (item.form?.name) {
                  clearDependentChain(item.form.name)
                }
              }
            })
          }

          // 遍历 dataColumn 数组
          let dependOn = [] as any
          filterDataColumn.value.forEach((item) => {
            // 只在满足所有条件时触发依赖更新
            if (item.form?.option?.dependOn === key) {
              //判断toRaw(newState)[key] 和 oldState[key] 这两个数组是否相同，
              //如果相同说明没有变化，直接返回
              let dependOnValue = ''
              let oldStayKeyValue = ''
              if (Array.isArray(localNewState[key])) {
                dependOnValue = localNewState[key].join(',')
              } else {
                dependOnValue = localNewState[key]
              }
              if (Array.isArray(oldState[key])) {
                oldStayKeyValue = oldState[key]!.join(',')
              } else {
                oldStayKeyValue = oldState[key]
              }
              //防止其他表单项变化 触发多次调
              if (dependOnValue === oldStayKeyValue) {
                return
              }

              //新旧值不同 清空关联表单
              if (oldStayKeyValue && oldStayKeyValue != dependOnValue) {
                clearFormItem(item)
              }

              //防止多次调
              if (dependOn[dependOnValue]) {
                return
              }
              dependOn[dependOnValue] = formData.value[key]
              if (item.form?.option?.dependOn) {
                emit('formData', formData.value, item.form.option.dependOn)
              }
            }
            // 只在初始化时处理默认值，不要在每次值变化时都处理
            if (isInitializing.value) {
              handleDefaultValue(item)
            }
          })
        }
        //获取所有type是fuzzySelect的
        const fuzzySelectColumn = filterDataColumn.value.filter((item) => item.form?.type === 'fuzzySelect')
        //远程模糊搜索 - 只在非 defaultValue 变化时触发
        if (!isDefaultValueChange.value) {
          fuzzySelectColumn.forEach((item) => {
            if (item.form?.name) {
              const nameKey = item.form.name.slice(0, -2) + 'Name'
              if (formData.value[nameKey]) {
                // 这里也添加防抖检查
                const now = Date.now()
                if (!lastFuzzySelectCall.value[item.form.name] || now - lastFuzzySelectCall.value[item.form.name] >= fuzzySelectDebounceTime) {
                  remoteSelectMethod(formData.value[nameKey], item.form.name)
                }
              }
            }
          })
        }

        oldState = cloneDeep(newState)
      } catch (error) {
        console.error('表单数据监听错误:', error)
      }
    },
    { deep: true },
  )

  // 清空单个表单项
  const clearFormItem = (item: TableItem) => {
    if (!item.form) return

    try {
      // 根据字段类型设置默认值
      if (item.form.type === 'number') {
        if (item.form.name) {
          formData.value[item.form.name] = 0
        }
      } else if ((item.form.type === 'timerange' || item.form.type === 'daterange') && Array.isArray(formData.value[item.form.name!])) {
        if (item.form.name) {
          formData.value[item.form.name] = []
        }
      } else if (item.form.type === 'switch') {
        if (item.form.name) {
          formData.value[item.form.name] = false
        }
      } else if (
        item.form.type === 'checkboxgroup' ||
        item.form.type === 'select' ||
        item.form.type === 'fuzzySelect' ||
        item.form.type === 'selectTree' ||
        item.form.type === 'cascader'
      ) {
        // 对于多选控件，设置为空数组
        if (item.form.name) {
          formData.value[item.form.name] = item.form.option?.multiple ? [] : null
        }

        // 清空选项数据
        if (item.form.option?.data) {
          item.form.option.data = []
        }
      } else {
        if (item.form.name) {
          formData.value[item.form.name] = null
        }
      }
    } catch (error) {
      console.error('Error clearing form item:', error)
    }
  }

  // 递归清空依赖链
  const clearDependentChain = (fieldName: string) => {
    try {
      filterDataColumn.value.forEach((item) => {
        if (item.form?.option?.dependOn === fieldName) {
          clearFormItem(item)
          // 递归清空依赖于此字段的其他字段
          if (item.form?.name) {
            clearDependentChain(item.form.name)
          }
        }
      })
    } catch (error) {
      console.error('Error clearing dependent chain:', error)
    }
  }

  const fuzzySelectLoading = ref(false)
  // 用于存储每个字段最后一次请求的时间戳
  const lastFuzzySelectCall = ref<{ [key: string]: number }>({})
  // 设置防抖时间为500毫秒
  const fuzzySelectDebounceTime = 500

  //远程模糊搜索下拉
  const remoteSelectMethod = async (query: string, key: string) => {
    console.log(query, key, 'query,key')
    if (query) {
      const item = filterDataColumn.value.find((item) => item.form?.name === key)
      if (item) {
        // 检查是否需要跳过此次调用（防抖）
        const now = Date.now()
        if (lastFuzzySelectCall.value[key] && now - lastFuzzySelectCall.value[key] < fuzzySelectDebounceTime) {
          return
        }
        // 记录本次请求的时间戳
        lastFuzzySelectCall.value[key] = now

        fuzzySelectLoading.value = true
        try {
          // 获取dependOn相关的参数
          const dependOnParams: any = {}
          if (item.form?.option?.dependOn) {
            dependOnParams[item.form.option.dependOn] = formData.value[item.form.option.dependOn]

            // 如果依赖字段为空，清空当前字段并返回
            if (!dependOnParams[item.form.option.dependOn]) {
              formData.value[key] = undefined
              if (item.form?.option?.data) {
                item.form.option.data = []
              }
              fuzzySelectLoading.value = false
              return
            }
          }

          const dataColumn = await getSelectOptions(filterDataColumn.value, null, '', 'formSelect', key, {
            keyword: query,
            ...dependOnParams,
          })

          console.log(dataColumn, '[[[[[]]]]]')
        } catch (error) {
          console.error('模糊搜索出错:', error)
        } finally {
          // 无论成功失败都关闭loading状态
          fuzzySelectLoading.value = false
        }
      }
    }
  }
  /**
   * 解析上传结果
   * @param res 上传结果
   * @param item 表单项
   * @param type 类型 imageOption fileOption videoOption
   */
  const parseUploadResult = (res: any, item: TableItem, type: string) => {
    // 上传图片，注意多张问题
    if (item.form?.[type]?.count! > 1) {
      formData.value[item.form?.name!] =
        (formData.value[item.form?.name!] || []).concat(res.data[item.form?.[type]?.uploadData || 'uploads']) ||
        res.data[item.form?.[type]?.uploadData || 'uploads']
    } else {
      formData.value[item.form?.name!] = res.data[item.form?.[type]?.uploadData || 'uploads']
    }
  }
  //上传图片
  const uploadImage = async (file: any, fileList: any[], item: TableItem) => {
    if (file.raw.type !== 'image/jpeg' && file.raw.type !== 'image/png') {
      // 删除文件
      fileList.splice(fileList.indexOf(file), 1)
      ElMessage.error('请上传图片格式')
      return
    }
    item.hideUpload = fileList.length >= item.form?.imageOption?.count!
    getBase64(file.raw).then(async (res) => {
      if (file.status !== 'ready') return
      const params = {
        uploadData: res,
        businessLine: item.form?.imageOption!.businessLine,
      }
      const tempArr = formData.value[item.form?.name!] ?? []
      await uploadApi(item.form?.imageOption!.uri, params).then((res) => {
        formData.value[item.form?.name!] = tempArr.concat(res.data)
      })
    })
  }
  const uploadImageV2 = async (file: any, fileList: any[], item: TableItem) => {
    if (file.raw.type !== 'image/jpeg' && file.raw.type !== 'image/png') {
      ElMessage.error('请上传图片格式')
      // 删除文件
      fileList.splice(fileList.indexOf(file), 1)
      return
    }
    //图片大小
    if (item.form?.imageOption?.size && file.raw.size > item.form?.imageOption?.size! * 1024 * 1024) {
      ElMessage.error('图片大小不能超过' + item.form?.imageOption?.size! + 'MB')
      // 删除文件
      fileList.splice(fileList.indexOf(file), 1)
      return
    }
    if (!item.form?.imageOption?.type) {
      item.form!.imageOption!.type = 'file'
    }
    if (item.form?.imageOption?.type === 'file') {
      const params = {
        file: file.raw,
        businessLine: item.form?.imageOption?.businessLine,
      }
      uploadFileApi(item.form?.imageOption!.uri!, params)
        .then((res) => {
          parseUploadResult(res, item, 'imageOption')
        })
        .catch((err) => {
          //删除当前图片
          fileList.splice(fileList.indexOf(file), 1)
        })
    } else if (item.form?.imageOption?.type === 'base64') {
      getBase64(file.raw).then(async (res) => {
        if (file.status !== 'ready') return
        const params = {
          uploadData: res,
          businessLine: item.form?.imageOption!.businessLine,
        }
        const tempArr = formData.value[item.form?.name!] ?? []
        await uploadApi(item.form?.imageOption!.uri, params).then((res) => {
          // formData.value[item.form?.name!] = tempArr.concat(res.data)
          parseUploadResult(res, item, 'imageOption')
        })
      })
    }
  }
  //上传文件
  const uploadFile = async (file: any, fileList: any[], item: TableItem) => {
    //文件大小
    if (item.form?.fileOption?.size && file.raw.size > item.form?.fileOption?.size! * 1024 * 1024) {
      ElMessage.error('文件大小不能超过' + item.form?.fileOption?.size! + 'MB')
      // 删除文件
      fileList.splice(fileList.indexOf(file), 1)
      return
    }
    item.hideUpload = fileList.length >= item.form?.fileOption?.count!
    if (file.status !== 'ready') return
    const params = {
      file: file.raw,
      businessLine: item.form?.fileOption!.businessLine,
    }

    uploadFileApi(item.form?.fileOption!.uri!, params)
      .then((res) => {
        formData.value[item.form?.name!] = formData.value[item.form?.name!]?.concat(res.data) || [res.data]
      })
      .catch((err) => {
        //删除当前图片
        fileList.splice(fileList.indexOf(file), 1)
      })
  }
  const uploadFileAdvance = async (file: any, fileList: any[], item: TableItem) => {
    item.hideUpload = fileList.length >= item.form?.fileOption?.count!
    // if (fileList.length > item.form?.fileOption?.count! && item.form?.fileOption?.count! > 1) {
    //   ElMessage.error('仅允许上传' + item.form?.fileOption?.count! + '个')
    //   return
    // }
    if (file.status !== 'ready') return
    const params = {
      file: file.raw,
      businessLine: item.form?.fileOption!.businessLine,
    }
    uploadFileApi(item.form?.fileOption!.uri!, params).then((res) => {
      if (item.form?.fileOption?.targetFields) {
        item.form!.fileOption!.targetFields.forEach((_item: any) => {
          // 上传图片，注意多张问题
          if (item.form?.fileOption?.count! > 1) {
            formData.value[_item.targetName!] = formData.value[_item.targetName!]?.concat(res.data[_item.name!]) || res.data[_item.name!]
          } else {
            formData.value[_item.targetName!] = res.data[_item.name!]
          }
        })
      } else {
        // 上传图片，注意多张问题
        parseUploadResult(res, item, 'fileOption')
        // if (item.form?.fileOption?.count! > 1) {
        //   formData.value[item.form?.name!] = formData.value[item.form?.name!]?.concat(res.data[item.form?.name!]) || res.data[item.form?.name!]
        // } else {
        //   formData.value[item.form?.name!] = res.data[item.form?.name!]
        // }
      }
    })
  }
  const videoList = ref()
  const uploadVideo = (file: any, fileList: any[], item: TableItem) => {
    if (file.status !== 'ready') return
    // Add file size check (20MB limit)
    const maxSize = 20 * 1024 * 1024 // 20MB in bytes
    if (file.raw.size > maxSize) {
      ElMessage.error(`视频大小不能超过${file.raw.size}MB`)
      return
    }
    videoList.value = fileList
    item.uploadLoading = true
    const params = {
      file: file.raw,
      businessLine: item.form?.videoOption?.businessLine,
    }
    uploadFileApi(item.form?.videoOption!.uri!, params)
      .then((res) => {
        parseUploadResult(res, item, 'videoOption')
        item.uploadLoading = false
      })
      .catch((err) => {
        //删除当前视频
        fileList.splice(fileList.indexOf(file), 1)
        item.uploadLoading = false
      })
  }
  const handleVideoRemove = (index: number, item: TableItem) => {
    formData.value[item.form?.name!]?.splice(index, 1)
    //删除当前视频
    videoList.value.splice(index, 1)
  }
  const handleRemove = (file: any, fileList: any[], _item: TableItem) => {
    _item.hideUpload = fileList.length >= _item.form?.fileOption?.count!
    const index = fileList.findIndex((item) => {
      if (item.url) {
        return item.url === file.url
      }
      if (item.uid) {
        return item.uid === file.uid
      }
      return false
    })
    if (index > -1) {
      fileList.splice(index, 1)
    }
    formData.value[_item.form?.name!] = fileList
  }
  const handleOldRemove = (file: any, fileList: any[], _item: TableItem) => {
    _item.hideUpload = fileList.length >= _item.form?.fileOption?.count!
    const index = fileList.findIndex((item) => {
      if (item.url) {
        return item === file.url
      }
      return false
    })
    if (index > -1) {
      fileList.splice(index, 1)
    }
    formData.value[_item.form?.name!] = fileList
  }

  // 点击位置
  const locationAddress = async (value: any) => {
    if (value.name == 'address') {
      searchDialog.visible = true //开启弹窗
      searchDialog.title = '选择位置'
    }
  }

  // 关闭子弹窗
  function closeMapDialog() {
    searchDialog.visible = false //关闭弹窗
  }

  // 子弹窗确认
  const submitLocation = (data: any) => {
    if (data) {
      formData.value.address = data.address
      formData.value.coordinate = data.coordinate
      closeMapDialog()
    }
  }
  /**
   * 提交表单
   */
  const handleSubmit = async (formEl: { validate: any }, btnRequest?: BtnRequestVO) => {
    if (!formEl) return
    await formEl.validate((valid: any) => {
      if (valid) {
        if (showMap.value === 'geoFence') {
          const { quyu, radius, coordinate } = rightAreaMapComponent.value.formData
          formData.value.quyu = quyu ?? null
          formData.value.radius = radius ?? null
          formData.value.coordinate = coordinate ?? null
        }
        const formParams = JSON.parse(JSON.stringify(formData.value))
        props.dataColumn.forEach((item: TableItem) => {
          if (item.insertEnable) {
            const { type, name } = item.form ?? {}
            if ((type === 'timerange' || type === 'daterange') && Array.isArray(formParams[name!])) {
              formParams[name!] = formParams[name!].join('~')
            } else if (formParams[name!] === '') {
              formParams[name!] = null
            }
            if (item.form?.name === 'ids') {
              formParams[item.form?.name!] = props.ids
            }
          }
          if (item.form?.type === 'uploadImage') {
            if (!formParams[item.form?.name!]) {
              formParams[item.form?.name!] = []
            }
          }
          const longTermKey = item.form?.name! + 'longTerm'
          // 处理长期状态
          if (item.form?.longTerm && longTermKey && formParams[longTermKey]) {
            formParams[item.form.name!] =
              item.form?.format === 'YYYY-MM'
                ? '9999-12-31'
                : item.form?.format === 'YYYY-MM-DD'
                  ? '9999-12-31'
                  : item.form?.format === 'YYYY-MM-DD HH:mm'
                    ? '9999-12-31 23:59'
                    : item.form?.format === 'YYYY-MM-DD HH:mm:ss'
                      ? '9999-12-31 23:59:59'
                      : '9999-12-31'
            // 删除longTerm字段，因为不需要提交
            // delete formParams[longTermKey]
          }
        })
        const filterFormData = props.dataColumn.filter((item) => item.insertEnable)
        //如果存在按钮，则获取当前点击按钮中的params
        if (formStore.storeFormBtns.length > 0) {
          formStore.storeFormBtns.forEach((item: BtnRequestVO) => {
            if (item.label === btnRequest?.label) {
              if (item.params) {
                const params: any = {}
                item.params?.map((_item) => {
                  composeRequestParams(params, item, null, null, props.currentRow, null, null)
                })
                Object.assign(formParams, params)
              }
            }
          })
        }
        //将customSelectGroupRef.value中的formData合并到formParams中
        let customSelectGroupFormData = {}
        customSelectGroupRef.value?.forEach((item: any) => {
          //遍历item.formData，将item.formData中的key和value赋值给customSelectGroupFormData
          Object.keys(item.formData).forEach((key: string) => {
            ;(customSelectGroupFormData as Record<string, any>)[key] = item.formData[key]
          })
        })
        const result = Object.keys(Object.assign(formParams, customSelectGroupFormData))
          .filter((key) => key === 'id' || filterFormData.some((column) => column.name === key))
          .reduce((obj: any, key) => {
            obj[key] = formParams[key]
            return obj
          }, {})
        //如果按钮下存在请求操作，则优先使用按钮上的
        emit('handleSubmit', formParams, btnRequest || props.requestUri, props.refreshMenuCount)
      }
    })
  }
  const handleExceed = (files: any, fileList: any[], item: TableItem) => {
    if (fileList.length >= item.form?.fileOption?.count! && item.form?.fileOption?.count! > 1) {
      ElMessage.error('仅允许上传' + item.form?.fileOption?.count! + '个')
      return
    }
    if (item.form?.fileOption?.count! === 1) {
      uploadFileRef.value[0]!.clearFiles()
    }
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    uploadFileRef.value[0]!.handleStart(file)
  }

  /**
   * 表单重置
   * @param formEl
   */
  const resetForm = () => {
    formRef.value?.resetFields()
    formRef.value?.clearValidate()
    formData.value.id = undefined
    formData.value.enable = true
  }
  /**
   * 关闭弹窗
   */
  const closeDialog = () => {
    //清空所有下拉
    filterDataColumn.value.forEach((item) => {
      if (
        (item.form?.type === 'select' ||
          item.form?.type === 'fuzzySelect' ||
          item.form?.type === 'selectTree' ||
          item.form?.type === 'cascader' ||
          item.form?.type === 'treeSelector') &&
        item.form.option!.uri
      ) {
        item.form!.option!.data = []
      }
    })
    //清除表单
    emit('clearFormColumn')
    resetForm()
    emit('closeDialog')
  }
  //预览图地址
  const dialogImageUrl = ref<string[]>()
  //预览弹窗
  const picDialogRef = ref<any>()
  /**
   * 图片预览
   */
  const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile: any) => {
    dialogImageUrl.value = [uploadFile.url!]
    picDialogRef.value.picDialogVisible = true
  }
  interface SelectValueObjVO {
    selected: any
    checked: any
  }
  const getSelectNodes = (selectValueObj: SelectValueObjVO, name: string) => {
    //遍历selectValueObj对象，将所有的value组成数组赋值给formData.value[name]
    formData.value[name] = Object.values(selectValueObj.selected)
  }
  const handleLongTermChange = (e: any, item: TableItem) => {
    if (e) {
      formData.value[item.form!.name!] = ''
    }
  }
  const handleDateChange = (val: string | string[] | null, item: TableItem) => {
    if (item.form?.longTerm) {
      const longTermKey = item.form.name! + 'longTerm'
      if (val?.includes('9999')) {
        formData.value[longTermKey] = true
      } else {
        formData.value[longTermKey] = false
      }
    }
  }
  const handleImageRemove = (file: any, item: TableItem) => {
    // 找到文件在数组中的索引
    const index = formData.value[item.form?.name!].findIndex((f: any) => f === file || f.url === file.url)
    if (index !== -1) {
      formData.value[item.form?.name!].splice(index, 1)
    }
  }
  const handleOtherFileRemove = (file: any, item: TableItem) => {
    // 找到文件在数组中的索引
    const index = formData.value[item.form?.name!].findIndex((f: any) => f === file || f.url === file.url)
    if (index !== -1) {
      formData.value[item.form?.name!].splice(index, 1)
    }
  }

  // 判断文件是否为图片
  const isImageFile = (file: any) => {
    const url = file.url || file
    return url.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i) || (typeof file === 'string' && file.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i))
  }

  // 获取图片文件
  const getImageFiles = (files: any[]) => {
    return files.filter((file) => isImageFile(file))
  }

  // 获取其他文件
  const getOtherFiles = (files: any[]) => {
    return files.filter((file) => !isImageFile(file))
  }

  // 判断是否有图片文件
  const hasImageFiles = (files: any[]) => {
    return files.some((file) => isImageFile(file))
  }

  // 判断是否有其他文件
  const hasOtherFiles = (files: any[]) => {
    return files.some((file) => !isImageFile(file))
  }

  // 获取文件名
  const getFileName = (url: string) => {
    if (!url) return ''
    const parts = url.split('/')
    return parts[parts.length - 1]
  }

  // 修改计算属性来处理分组
  const groupedFormFields = computed(() => {
    const groups: { [key: string]: TableItem[] } = {}

    // 先处理有分组的字段
    filterDataColumn.value.forEach((item) => {
      if (item.form?.groupKey) {
        if (!groups[item.form.groupKey]) {
          groups[item.form.groupKey] = []
        }
        groups[item.form.groupKey].push(item)
      }
    })

    // 对每个分组内的字段按sortNo排序
    Object.keys(groups).forEach((key) => {
      groups[key].sort((a, b) => (a.form?.sortNo || 0) - (b.form?.sortNo || 0))
    })

    // 将分组转换为有序数组，未分组的字段放在最前面
    const result: { key: string; items: TableItem[] }[] = []

    // 先添加未分组的
    const ungroupedItems = filterDataColumn.value.filter((item) => !item.form?.groupKey)
    if (ungroupedItems.length > 0) {
      ungroupedItems.sort((a, b) => (a.form?.sortNo || 0) - (b.form?.sortNo || 0))
      result.push({ key: 'default', items: ungroupedItems })
    }

    // 再添加有分组的
    Object.entries(groups).forEach(([key, items]) => {
      result.push({ key, items })
    })

    return result
  })

  defineExpose({
    resetForm, //重置表单
    formData, //提交表单
    closeDialog,
  })
</script>

<style  lang="scss">
  .formClass :deep(.el-scrollbar__view) {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  :deep(.hide .el-upload--picture-card) {
    display: none;
  }

  :deep(.el-input.el-input--default.el-input--suffix),
  :deep(.el-input__wrapper) {
    width: 100% !important;
  }

  .select-desc {
    line-height: 20px;
    background-color: #fdf6ec;
    color: #e6a23c;
    border: 1px solid #faecd8;
    padding: 5px;
    border-radius: 4px;
    margin-top: 10px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }


  .mb-4 {
    margin-bottom: 16px;
  }

  /* 新增左侧最大高度 */
  .left-ml-2 {
    max-height: 60vh;
    overflow-y: auto;
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;
    width: 100%;
  }

  .left-ml-2::webkit-scrollbar {
    display: none;
    /* 隐藏滚动条 */
  }
  .my-selects .el-select-dropdown__item {
  box-sizing: border-box;
  color: var(--el-text-color-regular);
  cursor: pointer;
  font-size: var(--el-font-size-base);
  line-height: 20px !important;
  height: auto !important;
  overflow: auto !important;
  padding: 10px 32px 10px 20px !important;
  position: relative;
  
  /* 文本溢出处理 - 兼容性优化 */
  text-overflow: clip !important;
  
  /* 换行处理 - 兼容性优化 */
  white-space: normal !important; /* wrap 不是标准值，改为 normal */
  word-wrap: break-word !important; /* 添加 word-wrap 兼容老版本浏览器 */
  word-break: break-all !important;
  
  /* 添加 Webkit 前缀支持 */
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

/* 为老版本 IE 添加回退样式 */
.my-selects .el-select-dropdown__item {
  *zoom: 1; /* IE6/7 触发 hasLayout */
}

/* 针对不支持 CSS 变量的浏览器添加回退值 */
.my-selects .el-select-dropdown__item {
  color: #606266; /* 回退颜色值 */
  font-size: 14px; /* 回退字体大小 */
}

.my-selects .el-select-dropdown__item {
  color: var(--el-text-color-regular, #606266);
  font-size: var(--el-font-size-base, 14px);
}


.my-selects .el-autocomplete-suggestion li  {
  box-sizing: border-box;
  color: var(--el-text-color-regular);
  cursor: pointer;
  font-size: var(--el-font-size-base);
  line-height: 20px !important;
  height: auto !important;
  overflow: auto !important;
  padding: 10px 32px 10px 20px !important;
  position: relative;
  
  /* 文本溢出处理 - 兼容性优化 */
  text-overflow: clip !important;
  
  /* 换行处理 - 兼容性优化 */
  white-space: normal !important; /* wrap 不是标准值，改为 normal */
  word-wrap: break-word !important; /* 添加 word-wrap 兼容老版本浏览器 */
  word-break: break-all !important;
  
  /* 添加 Webkit 前缀支持 */
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

/* 为老版本 IE 添加回退样式 */
.my-selects .el-autocomplete-suggestion li {
  *zoom: 1; /* IE6/7 触发 hasLayout */
}

/* 针对不支持 CSS 变量的浏览器添加回退值 */
.my-selects .el-autocomplete-suggestion li {
  color: #606266; /* 回退颜色值 */
  font-size: 14px; /* 回退字体大小 */
}

.my-selects .el-autocomplete-suggestion  li{
  color: var(--el-text-color-regular, #606266);
  font-size: var(--el-font-size-base, 14px);
}

</style>
