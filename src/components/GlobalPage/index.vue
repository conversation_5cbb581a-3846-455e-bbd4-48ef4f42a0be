<!--
 * @Author: llm
 * @Date: 2024-12-31 12:29:43
 * @LastEditors: llm
 * @LastEditTime: 2024-12-31 14:22:00
 * @Description:
-->
<template>
  <div>
    <BasePage1 v-if="props.pageType === 'basePage1'" />
    <BasePage v-else-if="props.pageType === 'basePage'" />
    <BaseTabPage v-else-if="props.pageType === 'baseTabPage'" />
    <BaseObjectPage v-else-if="props.pageType === 'baseObjectPage'" />
    <DevelopmentComponent v-else />
  </div>
</template>
<script setup lang="ts">
  import { BasePage, BasePage1, BaseTabPage, BaseObjectPage } from '@/utils/globalImport'
  import DevelopmentComponent from '@/components/DevelopmentComponent/index.vue'
  const props = defineProps({
    pageType: {
      type: String,
      default: 'basePage1',
    },
  })
</script>
