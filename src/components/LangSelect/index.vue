<!--
 * @Author: llm
 * @Date: 2025-01-25 17:35:16
 * @LastEditors: llm
 * @LastEditTime: 2025-03-27 12:12:18
 * @Description: 
-->
<script setup lang="ts">
  import { useI18n } from 'vue-i18n'
  import SvgIcons from '@/components/SvgIcons/index.vue'
  import { useAppStore } from '@/store/modules/app'

  const appStore = useAppStore()
  const { locale } = useI18n()

  function handleLanguageChange(lang: string) {
    locale.value = lang
    appStore.changeLanguage(lang)
    if (lang == 'en') {
      ElMessage.success('Switch Language Successful!')
    } else {
      ElMessage.success('切换语言成功！')
    }
  }
</script>

<template>
  <el-dropdown trigger="click" @command="handleLanguageChange">
    <div>
      <svg-icons icon-class="language" />
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item :disabled="appStore.language === 'zh-cn'" command="zh-cn">中文</el-dropdown-item>
        <el-dropdown-item :disabled="appStore.language === 'en'" command="en">English</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
