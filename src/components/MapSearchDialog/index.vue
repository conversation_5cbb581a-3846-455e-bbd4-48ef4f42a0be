<!--
 * @Author: zzw
 * @Date: 2023-07-18 09:45:08
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-06-28 10:51:31
 * @Description: 搜索-确定位置-弹窗组件
 *
-->
<template>
  <!-- 位置弹窗 -->
  <el-dialog :draggable="true" class="mapDialogClass" append-to-body v-model="dialog.visible" :title="dialog.title" width="900px">
    <!-- 搜索框 -->
    <div class="inquireInput">
      <el-input class="inputWord" id="searchId1" v-model="formData.address" placeholder="请输入查询位置" type="text" clearable />
    </div>
    <!-- 地图 -->
    <div id="container" ref="mapSearch"></div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" v-loading="submitLoading" @click="submitLocation()">确 定</el-button>
        <el-button @click="closeDialog()">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
  // 引入异步引入地图的方法
  import { loadBaiDuDrawMap } from '@/utils/bmpgl_lib.ts'
  import { bd09togcj02, gcj02tobd09 } from '@/utils'
  import { ref } from 'vue'

  const props = defineProps({
    /**
     * 弹窗属性
     */
    dialog: {
      require: true,
      type: Object,
      default: () => {
        return {}
      },
    },
    // 位置
    pointData: {
      require: true,
      type: Object,
      default: () => {
        return {}
      },
    },
  })

  const submitLoading = ref(false)
  const mapSearch = ref(null)
  const mapObj = ref(null)
  const formData = ref({
    coordinate: '', //经纬度
    address: '', //地址名称
    currentIndex: 0, //索引
  })

  const state = reactive({
    miniMap: null,
    mapCenter: [116.397451, 39.909187], // 默认位置
  })

  watch(
    () => props.pointData,
    (value) => {
      if (JSON.parse(JSON.stringify(value)).address) {
        formData.value.address = JSON.parse(JSON.stringify(value)).address
        formData.value.coordinate = JSON.parse(JSON.stringify(value)).coordinate
      }
      if (JSON.parse(JSON.stringify(value)).currentIndex) {
        if (formData.value.currentIndex != '' || formData.value.currentIndex != undefined || formData.value.currentIndex != null) {
          formData.value.currentIndex = JSON.parse(JSON.stringify(value)).currentIndex
        }
      }
    },
    { deep: true, immediate: true },
  )

  const emit = defineEmits(['closeMapDialog', 'submitLocation'])

  onMounted(() => {
    setTimeout(() => {
      initMiniMap()
    }, 200)
  })

  // 初始化地图
  const initMiniMap = async () => {
    loadBaiDuDrawMap().then((_BMapGL) => {
      let BMapGL = null
      // 创建地图实例
      if (_BMapGL.BMapGL) {
        BMapGL = _BMapGL.BMapGL
      } else {
        BMapGL = _BMapGL
      }
      // 创建地图实例
      mapObj.value = new BMapGL.Map(mapSearch.value)
      // 添加比例尺控件
      mapObj.value.addControl(
        new BMapGL.ScaleControl({
          anchor: BMAP_ANCHOR_BOTTOM_LEFT,
          offset: new BMapGL.Size(10, 10),
        }),
      )
      // 添加缩放控件
      mapObj.value.addControl(
        new BMapGL.ZoomControl({
          anchor: BMAP_ANCHOR_BOTTOM_RIGHT,
          offset: new BMapGL.Size(10, 10),
        }),
      )
      // 保存地图
      state.miniMap = mapObj.value
      //清除地图上所有覆盖物
      state.miniMap.clearOverlays()
      // 创建点坐标 axios => res 获取的初始化定位坐标

      // 回显围栏圆形
      if (formData.value.coordinate) {
        state.mapCenter = formData.value.coordinate.split(',')
        const point = new BMapGL.Point(Number(state.mapCenter[0]), Number(state.mapCenter[1]))
        state.miniMap.addOverlay(new BMapGL.Marker(point)) //添加标注
        // 初始化地图，设置中心点坐标和地图级别
        state.miniMap.centerAndZoom(point, 18)
      } else {
        //默认
        const point = new BMapGL.Point(Number(state.mapCenter[0]), Number(state.mapCenter[1]))
        // 初始化地图，设置中心点坐标和地图级别
        state.miniMap.centerAndZoom(point, 5)
      }
      //开启鼠标滚轮缩放
      state.miniMap.enableScrollWheelZoom(true)

      //建立一个自动完成的对象
      var ac = new BMapGL.Autocomplete({
        input: 'searchId1',
        location: state.miniMap,
      })
      ac.setInputValue(formData.value.address)
      // 鼠标点击下拉列表
      ac.addEventListener('onconfirm', (e) => {
        var v = e.item.value
        var keyword = v.province + v.city + v.district + v.street + v.business
        formData.value.address = keyword
        //清除地图上所有覆盖物
        state.miniMap.clearOverlays()
        //智能搜索
        var local = new BMapGL.LocalSearch(state.miniMap, {
          onSearchComplete: () => {
            var p = local.getResults().getPoi(0).point //获取第一个智能搜索的结果
            let newPoint = bd09togcj02(p.lng, p.lat)
            // 位置
            formData.value.coordinate = newPoint.join(',')
            state.miniMap.centerAndZoom(p, 16)
            state.miniMap.addOverlay(new BMapGL.Marker(p)) //添加标注
          },
        })
        local.search(keyword)
      })
    })
  }

  /**
   * 提交
   */
  const submitLocation = async () => {
    if (formData.value) {
      if (!formData.value.address) {
        ElMessage.error('位置名称不能为空！')
        return
      }
      if (!formData.value.coordinate) {
        ElMessage.error('请选定位置后提交！')
        return
      }
      emit('submitLocation', formData.value)
    }
  }
  onBeforeUnmount(() => {
    // 在组件销毁前手动释放地图实例
    mapObj.value && mapObj.value.destroy()
    state.miniMap = null
  })
  /**
   * 重置
   */
  const resetForm = () => {
    formData.value.address = '' //地址名称
    formData.value.coordinate = '' //经纬度
    formData.value.currentIndex = 0 //索引
  }
  /**
   * 关闭弹窗
   */
  const closeDialog = () => {
    resetForm()
    emit('closeMapDialog')
  }

  defineExpose({
    resetForm, //重置
    formData, //提交
  })
</script>

<style scoped>
  #container {
    position: relative;
    width: 100%;
    height: 60vh;
  }

  .inquireInput {
    z-index: 99;
    width: 300px;
    margin-bottom: 10px;
  }

  .inquireInput .inputWord {
    width: 100%;
    border-radius: 4px;
  }
</style>
