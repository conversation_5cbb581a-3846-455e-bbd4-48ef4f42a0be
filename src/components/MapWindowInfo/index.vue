<template>
  <!-- 地图展示信息 -->
  <!-- top: popupPixel.y + 'px', left: popupPixel.x + 'px' -->
  <div v-if="(JSON.stringify(dataInfo) != '{}' || dataInfo) && isShowHide" class="statisticsInfo" :style="{ width: '460px' }">
    <div class="topInfo">
      <div>
        <span :style="{ fontWeight: 600, fontSize: '16px' }">{{ dataInfo.vehicleNo }}</span>
        <span :style="{ fontWeight: 600, fontSize: '16px', color: '#409EFF' }">{{ dataInfo.vehicleStatus ? ' - ' + dataInfo.vehicleStatus : '' }}</span>
      </div>
      <el-button icon="Close" size="small" type="info" circle @click.stop="closeInfo()" />
    </div>
    <div class="buttonBox">
      <el-tabs v-model="newTypeName" class="tabChangeClass" type="border-card" @tab-change="handleChange">
        <el-tab-pane label="车辆信息" name="vehicle">
          <div class="vehicleInfo">
            <el-row>
              <el-col :span="12" class="colItem">
                <span class="lableText">承运商：</span>
                <span class="valueText">{{ dataInfo.vehicleInfo.carrierName || '--' }}</span>
              </el-col>
              <el-col :span="12" class="colItem">
                <span class="lableText">司机：</span>
                <span class="valueText">{{ dataInfo.vehicleInfo.driverName || '--' }}</span>
              </el-col>
              <el-col :span="12" class="colItem">
                <span class="lableText">挂车车牌：</span>
                <span class="valueText">{{ dataInfo.vehicleInfo.trailerNo || '--' }}</span>
              </el-col>
              <el-col :span="12" class="colItem">
                <span class="lableText">北斗状态：</span>
                <span class="valueText">{{ dataInfo.vehicleInfo.offline || '--' }}</span>
              </el-col>
              <el-col :span="12" class="colItem">
                <span class="lableText">定位来源：</span>
                <span class="valueText">{{ dataInfo.vehicleInfo.positionFrom || '--' }}</span>
              </el-col>
              <el-col :span="12" class="colItem">
                <span class="lableText">当前时速：</span>
                <span class="valueText">{{ dataInfo.vehicleInfo.speed || '--' }}</span>
              </el-col>
              <el-col :span="24" class="colItem">
                <span class="lableText">定位时间：</span>
                <span class="valueText">{{ dataInfo.vehicleInfo.positionTime || '--' }}</span>
              </el-col>
              <el-col :span="24" class="colItem">
                <span class="lableText">离线时长：</span>
                <span class="valueText">{{ dataInfo.vehicleInfo.offlineTime || '--' }}</span>
              </el-col>
              <el-col :span="24" class="colItem">
                <span class="lableText">当前位置：</span>
                <span class="valueText">{{ dataInfo.vehicleInfo.address || '--' }}</span>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
        <el-tab-pane label="调度单信息" name="dispatch" v-if="dataInfo.dispatchInfo && dataInfo.dispatchInfo.dispatchList.length > 0">
          <div class="containerInfo">
            <el-row class="itemInfo" v-for="(item, index) in dataInfo.dispatchInfo.dispatchList" :key="index">
              <el-col :span="24" class="colItem">
                <span class="lableText">调度单号：</span>
                <span class="valueText">{{ item.dispatchNo ? item.dispatchNo : '--' }}</span>
              </el-col>
              <el-col :span="12" class="colItem">
                <span class="lableText">司机：</span>
                <span class="valueText">{{ item.driverName ? item.driverName : '--' }}</span>
              </el-col>
              <el-col :span="12" class="colItem">
                <span class="lableText">执行状态：</span>
                <span class="valueText">{{ item.dispatchStatus ? item.dispatchStatus : '--' }}</span>
              </el-col>
              <el-col :span="12" class="colItem">
                <span class="lableText">承运商：</span>
                <span class="valueText">{{ item.carrierName ? item.carrierName : '--' }}</span>
              </el-col>
              <el-col :span="12" class="colItem">
                <span class="lableText">任务轨迹：</span>
                <el-button size="small" type="primary" v-if="item.dispatchStatus === '运输中'" @click="viewTrajectory(item)">查看轨迹</el-button>
              </el-col>
              <el-col :span="24" class="colItem">
                <span class="lableText">装车时间：</span>
                <span class="valueText">{{ item.departureTime ? item.departureTime : '--' }}</span>
              </el-col>
              <el-col :span="24" class="colItem">
                <span class="lableText">预计送达：</span>
                <span class="valueText">{{ item.estimateArriveTime ? item.estimateArriveTime : '--' }}</span>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
  import router from '@/router'
  import { useFormStore } from '@/store/modules/form'
  const formStore = useFormStore()
  export default {
    emits: ['closeInformation'],
    props: {
      dataInfo: {
        type: Object,
        default: {},
      },
      isShowHide: {
        type: Boolean,
        default: false,
      },
      popupPixel: {
        type: Object,
        default: {},
      },
    },
    data() {
      return {
        newTypeName: 'vehicle',
      }
    },
    watch: {},
    methods: {
      // 关闭信息弹窗
      closeInfo() {
        this.$emit('closeInformation', !this.isShowHide)
      },

      // tab切换
      handleChange(tab) {
        this.newTypeName = tab
      },

      // 查看轨迹
      viewTrajectory(data) {
        //不刷新查询条件
        formStore.setIsRefreshTopQueryParams(false)
        formStore.$patch((state) => {
          state.routerParams = {
            dispatchNo: data.dispatchNo,
          }
        })
        router.push({
          path: '/transportInTransit/vehicleTrack',
          query: {
            time: new Date().getTime(), //获取当前时间戳,防止不刷新页面
          },
        })
      },
    },
  }
</script>

<style scoped>
  /* 车辆信息展示 */
  .statisticsInfo {
    /* width: 480px; */
    padding: 0 10px 10px;
    background: #f9f9f9;
    position: absolute;
    bottom: 20px;
    right: 20px;
    border-radius: 10px;
    border: 1px solid #999;
    z-index: 9;
  }

  .statisticsInfo :deep(.el-tabs__content) {
    padding: 0;
  }

  .statisticsInfo .vehicleInfo {
    margin: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
    max-height: 240px;
    overflow: scroll;
    overflow-x: hidden;
    overflow-y: auto;
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
  }

  .statisticsInfo .containerInfo {
    margin: 10px;
    max-height: 240px;
    overflow: scroll;
    overflow-x: hidden;
    overflow-y: auto;
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
  }

  .statisticsInfo .vehicleInfo::-webkit-scrollbar,
  .statisticsInfo .containerInfo::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
  }

  .statisticsInfo .containerInfo .itemInfo {
    border: 1px solid #409eff;
    /* border-bottom: none; */
    border-radius: 4px;
    margin-bottom: 10px;
  }

  .statisticsInfo .topInfo {
    display: flex;
    align-items: center;
    height: 40px;
    justify-content: space-between;
    border-bottom: 1px solid #ccc;
  }

  .statisticsInfo .topInfo .title {
    font-size: 16px;
    padding: 0 4px;
    font-weight: bold;
  }

  .statisticsInfo .colItem {
    display: flex;
    padding: 4px 5px;
    font-size: 14px;
    border-bottom: 1px solid #eee;
  }

  /* .vehicleInfo .colItem:nth-last-child(2), */
  .statisticsInfo .colItem:last-child,
  .vehicleInfo .colItem:last-child {
    border-bottom: 0;
  }

  .statisticsInfo .lableText {
    color: #409eff;
    font-weight: 500;
  }

  .statisticsInfo .valueText {
    color: #333;
    flex: 1;
    font-weight: 500;
  }

  .statisticsInfo .rowText {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .statisticsInfo .buttonBox {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-top: 8px;
  }

  .colItem :deep(.el-button--mini) {
    padding: 2px 8px;
    margin-left: 4px;
  }
</style>
