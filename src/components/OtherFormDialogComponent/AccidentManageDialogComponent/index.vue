<!--
 * @Author: llm
 * @Date: 2023-06-28 09:45:08
 * @LastEditors: llm
 * @LastEditTime: 2025-04-11 10:54:20
 * @Description: 事故管理
 *
-->
<template>
  <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" :draggable="true" :close-on-click-modal="false" @close="closeDialogFun" width="70%">
    <el-scrollbar max-height="60vh" class="formClass">
      <div v-if="!state.paymentDialogVisible">
        <div style="display: flex; justify-content: start; align-items: center">
          <el-check-tag
            style="margin: 0px 10px 10px 0px"
            v-for="(item, index) in state.qualityLossData"
            :key="index"
            :checked="item.haveChosen"
            @change="onChange($event, item, index)"
            :disabled="state.statusType === 'editMoneyNumber'"
          >
            <div style="display: flex; align-items: center; justify-content: center; width: 100%">
              <el-icon><CirclePlus /></el-icon>{{ item.vin }}
            </div>
          </el-check-tag>
        </div>
        <el-table ref="qualityLossDataRef" show-overflow-tooltip :border="true" :data="state.selectedData">
          <!-- 序号 -->
          <el-table-column align="center" label="序号" type="index" width="60"></el-table-column>
          <el-table-column v-for="item in tableConfig.tableItem" :align="item.align" :key="item.name" :label="item.label" :prop="item.name">
            <template #default="{ row }">
              <div v-if="item.name === 'picsList' && row.picsList && row.picsList.length > 0">
                <el-button type="primary" link @click="showPic(row.picsList)">查看</el-button>
              </div>
              <div v-else style="display: flex; align-items: center; justify-content: center; width: 100%">
                <el-text>{{ item.name === 'picsList' ? '' : row[item.name] }}</el-text>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                <el-button type="primary" link @click="editColumn(scope.row, scope.$index)">编辑</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else>
        <PaymentInformation ref="paymentInformationRef" :statusType="state.statusType" />
      </div>
    </el-scrollbar>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="upStep()" v-if="state.paymentDialogVisible && state.statusType != 'invoiceNumber'">上一步</el-button>
        <el-button type="primary" @click="nextStep()" v-if="!state.paymentDialogVisible && state.selectedData.length > 0">下一步</el-button>
        <el-button type="primary" @click="saveForm()" v-if="state.paymentDialogVisible && state.statusType != 'invoiceNumber'">保存</el-button>
        <el-button type="primary" @click="submitApprove()" v-if="state.statusType != 'invoiceNumber' && state.statusType != 'editMoneyNumber'"
          >提交审批</el-button
        >
      </div>
    </template>
  </el-dialog>

  <el-dialog v-model="state.editVisible" title="编辑" :draggable="true" :close-on-click-modal="false" @close="close">
    <el-form ref="formRef" :model="formData" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="VIN">
            <el-input v-model="formData.vin" style="width: 240px" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户">
            <el-input v-model="formData.customerName" style="width: 240px" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经销商">
            <el-input v-model="formData.dealerName" style="width: 240px" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="质损费用" prop="actualAmount" :rules="[{ required: true, message: '请填写质损费用', trigger: 'change' }]">
            <el-input-number :style="{ width: '240px' }" v-model="formData.actualAmount" :min="0" :precision="2"
              ><template #suffix>
                <span>元</span>
              </template>
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="维修费用" prop="maintenanceFee">
            <el-input-number :style="{ width: '240px' }" v-model="formData.maintenanceFee" :min="0" :precision="2">
              <template #suffix>
                <span>元</span>
              </template>
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12"
          ><el-form-item label="降价费用" prop="reducePrice">
            <el-input-number :style="{ width: '240px' }" v-model="formData.reducePrice" :min="0" :precision="2">
              <template #suffix>
                <span>元</span>
              </template>
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="质损图片">
            <UploadImageComponent ref="uploadImageRef" :limit="20" :multiple="true" :disabled="state.statusType === 'editMoneyNumber'" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="质损备注" prop="remark">
            <el-input v-model="formData.remark" type="textarea" placeholder="请填写质损备注" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close()">取 消</el-button>
        <el-button type="primary" :loading="state.loading" @click="submitForm(formRef)">保存</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 查看图片 -->
  <PicDialogComponent :image-list="imageList" ref="picDialogComponent" />
</template>
<script setup lang="ts">
  import { getQualityLossListApi, updateQualityLossListApi, stepSubmitApprovalApi, deleteQualityLossListApi } from '@/api/businessManagement'
  import UploadImageComponent from '@/components/UploadImageComponent/index.vue'
  import PaymentInformation from '@/components/OtherFormDialogComponent/AccidentManageDialogComponent/paymentInformation.vue'
  import PicDialogComponent from '@/components/PicDialogComponent/index.vue' //查看照片组件
  import { ElLoading, UploadUserFile } from 'element-plus'
  const uploadImageRef = ref()
  const paymentInformationRef = ref()

  const dialogVisible = reactive<DialogOption>({
    visible: false,
    title: '新增事故单',
  })

  const state = reactive<any>({
    loading: false,
    editVisible: false,
    qualityLossData: [],
    selectedData: [],
    accidentId: '',
    paymentDialogVisible: false,
    newData: {},
    statusType: '', //状态类型
  })

  /**
   * 表单参数
   */
  const formData = ref<any>({})

  //查看图片组件
  const picDialogComponent = ref()
  //图片地址
  const imageList = ref<string[]>([])

  const tableConfig = {
    tableItem: [
      {
        name: 'vin',
        label: 'vin',
        align: 'center',
      },
      {
        name: 'customerName',
        label: '客户',
        align: 'center',
      },
      {
        name: 'dealerName',
        label: '经销商',
        align: 'center',
      },
      {
        name: 'actualAmount',
        label: '质损费用',
        align: 'center',
      },
      {
        name: 'maintenanceFee',
        label: '维修费用',
        align: 'center',
      },
      {
        name: 'reducePrice',
        label: '降价费用',
        align: 'center',
      },
      {
        name: 'picsList',
        label: '质损图片',
        align: 'center',
      },
      {
        name: 'remark',
        label: '质损备注',
        align: 'center',
      },
    ],
  }

  // 改变状态
  const onChange = (status: boolean, item: any, index: number) => {
    state.qualityLossData[index].haveChosen = status
    if (status) {
      updateQualityLossListApi(item)
        .then((res: any) => {
          state.selectedData.push(item)
        })
        .catch(() => {})
    } else {
      // 删除数组元素 item
      state.selectedData.map((event: any, _index: number) => {
        if (item.vin === event.vin) {
          deleteQualityLossListApi(event)
            .then((res: any) => {
              state.selectedData.splice(_index, 1)
            })
            .catch(() => {})
        }
      })
    }
  }

  // 编辑
  const editColumn = (row: any, index: number) => {
    formData.value = JSON.parse(JSON.stringify(row))
    state.editVisible = true
    setTimeout(() => {
      setUploadImageList(row.picsList ? row.picsList : [])
    }, 200)
  }

  // 获取数据
  const getQualityLoss = async (accidentId: string, data: any) => {
    state.accidentId = accidentId
    state.newData = data
    // "待提交","待审批","审批中","审批驳回","待申请付款","付款待审批","付款审批中","付款审批驳回","待付款","已付款"
    if (data.statusName === '待付款' || data.statusName === '已付款') {
      state.paymentDialogVisible = true
      state.statusType = 'invoiceNumber'
    } else if (data.statusName === '待申请付款' || data.statusName === '付款审批驳回') {
      state.paymentDialogVisible = false
      state.statusType = 'editMoneyNumber'
    } else {
      state.paymentDialogVisible = false
      state.statusType = ''
    }
    if (state.paymentDialogVisible) {
      // 仅可编辑发票号以及发票识别功能
      nextStep()
    } else {
      state.selectedData = [] //清除上次选中的数据
      const loading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      getQualityLossListApi({ accidentId: accidentId, dispatchNo: data.dispatchNo })
        .then((res: any) => {
          if (res.code === 200) {
            loading.close()
            state.qualityLossData = res.data
            if (state.qualityLossData.length > 0) {
              state.qualityLossData.map((item: any, index: number) => {
                if (item.haveChosen) {
                  state.selectedData.push(item)
                }
              })
            }
          } else {
            loading.close()
            state.qualityLossData = []
          }
        })
        .catch(() => {
          loading.close()
          state.qualityLossData = []
        })
      state.paymentDialogVisible = false
    }
  }

  // 获取上传图片列表
  const getUploadImageList = () => {
    return uploadImageRef.value.uploadImageList
  }

  //设置上传图片列表
  const setUploadImageList = (list: UploadUserFile[]) => {
    uploadImageRef.value.uploadImageList = list
  }

  const emit = defineEmits(['closeDialog'])
  const formRef = ref()

  // 上一步
  const upStep = () => {
    getQualityLoss(state.accidentId, state.newData)
    state.paymentDialogVisible = false
  }
  // 下一步
  const nextStep = async () => {
    state.paymentDialogVisible = true
    setTimeout(() => {
      paymentInformationRef.value.getDetails(state.accidentId)
    }, 100)
  }
  // 保存
  const saveForm = async () => {
    if (paymentInformationRef.value) {
      let newData = paymentInformationRef.value.state.detailsFormData
      let params = {
        driverDeductionType: newData.driverDeductionType, // 司机罚款类型
        personalAdvanceAmount: newData.personalAdvanceAmount, // 垫付金额
        driverDeductionAmount: newData.driverDeductionAmount, // 司机挂账
        chargeType: newData.chargeType, // 预计金额、实际金额
        isSubmit: false, // 是否提交审批
        step: 3, // 步骤
        id: state.accidentId, // 事故单id
      }
      stepSubmitApprovalApi(params)
        .then((res: any) => {
          ElMessage.success(res.message)
          closeDialog()
        })
        .catch(() => {})
    }
  }
  // 提交审批
  const submitApprove = async () => {
    // if (state.selectedData.length === 0) {
    //   return ElMessage.warning('请选择要提交的质损单')
    // } else {

    // }
    var params = {}
    if (state.paymentDialogVisible === false) {
      params = {
        isSubmit: true, // 是否提交审批
        step: 2, // 步骤
        id: state.accidentId, // 事故单id
      }
    } else if (state.paymentDialogVisible === true) {
      if (paymentInformationRef.value) {
        let newData = paymentInformationRef.value.state.detailsFormData
        params = {
          driverDeductionType: newData.driverDeductionType, // 司机罚款类型
          personalAdvanceAmount: newData.personalAdvanceAmount, // 垫付金额
          driverDeductionAmount: newData.driverDeductionAmount, // 司机挂账
          chargeType: newData.chargeType, // 预计金额、实际金额
          isSubmit: true, // 是否提交审批
          step: 3, // 步骤
          id: state.accidentId, // 事故单id
        }
      }
    }
    ElMessageBox.confirm('是否确认提交审批?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        stepSubmitApprovalApi(params)
          .then((res: any) => {
            ElMessage.success(res.message)
            closeDialog()
          })
          .catch(() => {})
      })
      .catch(() => {})
  }

  /**
   * 提交编辑质损表单
   */
  const submitForm = async (formEl: { validate: any }) => {
    if (!formEl) return
    await formEl.validate((valid: any) => {
      if (valid) {
        state.loading = true
        const formParams = JSON.parse(JSON.stringify(formData.value))
        formParams.picsList = getUploadImageList()
        if (formParams.picsList) {
          state.loading = false
        }
        //如果按钮下存在请求操作，则优先使用按钮上的
        updateQualityLossListApi(formParams)
          .then((res: any) => {
            if (res.data) {
              for (let i = 0; i < state.selectedData.length; i++) {
                for (let j = 0; j < res.data.length; j++) {
                  if (state.selectedData[i].vin === res.data[j].vin) {
                    state.selectedData[i] = res.data[j]
                  }
                }
              }
              state.editVisible = false
            }
          })
          .catch(() => {})
      }
    })
  }

  /**
   * 展示图片
   * @param row 当前行数据
   * @param name 图片字段名
   */
  const showPic = (row: any) => {
    if (row) {
      imageList.value = []
      row.map((item: any) => {
        imageList.value.push(item.url)
      })
      picDialogComponent.value.picDialogVisible = true
    }
  }

  /**
   * 关闭弹窗
   */
  const closeDialog = () => {
    emit('closeDialog')
  }

  const closeDialogFun = () => {
    dialogVisible.visible = false
  }

  const close = () => {
    state.editVisible = false
  }

  defineExpose({
    state, //状态
    getQualityLoss, //获取质损
    closeDialog, //关闭弹窗
    dialogVisible, //弹窗
  })
</script>

<style scoped></style>
