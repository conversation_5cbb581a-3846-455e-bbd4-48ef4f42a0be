<!--
 * @Author: llm
 * @Date: 2023-06-28 09:45:08
 * @LastEditors: 周宗文
 * @LastEditTime: 2025-02-18 20:42:44
 * @Description: 支付信息弹窗
 *
-->
<template>
  <div>
    <div class="topBox">
      <div style="display: flex; align-items: center; width: 100%; padding-bottom: 10px">
        <div>
          <span>质损总金额：</span>
          <span class="textColor">{{ state.detailsFormData.actualAmount || '-' }}元</span>
        </div>
        <div class="left_m_30">
          <span>维修总金额：</span>
          <span class="textColor">{{ state.detailsFormData.maintenanceFee || '-' }}元</span>
        </div>
        <div class="left_m_30">
          <span>降价总金额：</span>
          <span class="textColor">{{ state.detailsFormData.reducePrice || '-' }}元</span>
        </div>
        <div class="left_m_30">
          <span>承运商：</span>
          <span class="textColor">{{ state.detailsFormData.carrierName || '-' }}</span>
        </div>
        <div class="left_m_30">
          <span>司机：</span>
          <span class="textColor">{{ state.detailsFormData.driverName || '-' }}</span>
        </div>
      </div>

      <el-form ref="formRef" :model="state.detailsFormData">
        <div style="display: flex; align-items: center; width: 100%">
          <el-form-item label="垫付金额：">
            <el-input-number
              :style="{ width: '240px' }"
              :disabled="props.statusType === 'invoiceNumber'"
              v-model="state.detailsFormData.personalAdvanceAmount"
              :min="0"
              :precision="2"
              ><template #suffix>
                <span>元</span>
              </template>
            </el-input-number>
          </el-form-item>
          <el-form-item label="司机罚款类型：" class="left_m_20">
            <el-select
              v-model="state.detailsFormData.driverDeductionType"
              placeholder="请选择罚款类型"
              clearable
              :disabled="props.statusType === 'invoiceNumber'"
              :style="{ width: '240px' }"
              @change="changePenaltyType"
            >
              <el-option v-for="item in state.penaltyTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div style="display: flex; align-items: center; width: 100%">
          <el-form-item label="挂账金额：">
            <el-input-number
              :style="{ width: '240px' }"
              :disabled="props.statusType === 'invoiceNumber'"
              v-model="state.detailsFormData.driverDeductionAmount"
              :min="0"
              :precision="2"
            >
              <template #suffix>
                <span>元</span>
              </template>
            </el-input-number>
            <el-radio-group style="margin-left: 20px" :disabled="props.statusType === 'invoiceNumber'" v-model="state.detailsFormData.chargeType">
              <el-radio :value="'预计金额'">预计金额</el-radio>
              <el-radio :value="'实际金额'">实际金额</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <div class="formClass">
      <h3 style="font-weight: 600">公司对公支付信息</h3>
      <el-button type="primary" size="small" @click="addForm('public')" v-if="props.statusType === ''">新增</el-button>
    </div>
    <el-table show-overflow-tooltip :border="true" :data="state.qualityLossPublicFeeApplyList">
      <!-- 序号 -->
      <el-table-column align="center" label="序号" type="index" width="60" fixed="left"></el-table-column>
      <el-table-column v-for="item in publicTableConfig.tableItem" :align="item.align" :key="item.name" :label="item.label" :prop="item.name">
        <template #default="{ row }">
          <div v-if="item.name === 'invoiceFile' && row.invoiceUrl" style="display: flex; align-items: center; justify-content: center; width: 100%">
            <el-button type="primary" link @click="downLoadFile(row.invoiceUrl)">下载</el-button>
          </div>
          <div v-else style="display: flex; align-items: center; justify-content: center; width: 100%">
            <el-text>{{ row[item.name] }}</el-text>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180px" align="center" fixed="right">
        <template #default="scope">
          <div style="display: flex; align-items: center; justify-content: center; width: 100%">
            <el-button type="primary" link @click="editColumn(scope.row, scope.$index, 'public')">编辑</el-button>
            <el-button type="primary" link @click="deleteColumn(scope.row, scope.$index, 'public')" v-if="props.statusType === ''">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="formClass">
      <h3 style="font-weight: 600">公司对私支付信息</h3>
      <el-button type="primary" size="small" @click="addForm('private')" v-if="props.statusType === ''">新增</el-button>
    </div>
    <el-table show-overflow-tooltip :border="true" :data="state.qualityLossPrivateFeeApplyList">
      <!-- 序号 -->
      <el-table-column align="center" label="序号" type="index" width="60" fixed="left"></el-table-column>
      <el-table-column v-for="item in privateTableConfig.tableItem" :align="item.align" :key="item.name" :label="item.label" :prop="item.name">
        <template #default="{ row }">
          <div style="display: flex; align-items: center; justify-content: center; width: 100%">
            <el-text>{{ row[item.name] }}</el-text>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180px" align="center" fixed="right">
        <template #default="scope">
          <div style="display: flex; align-items: center; justify-content: center; width: 100%">
            <el-button
              type="primary"
              link
              @click="editColumn(scope.row, scope.$index, 'private')"
              v-if="props.statusType === '' || props.statusType === 'editMoneyNumber'"
              >编辑</el-button
            >
            <el-button type="primary" link @click="deleteColumn(scope.row, scope.$index, 'private')" v-if="props.statusType === ''">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>

  <!-- 新增编辑对公对私信息 -->
  <el-dialog
    v-model="state.publicDrivateDialog"
    append-to-body
    :title="state.isAdd ? '新增' : '编辑'"
    :draggable="true"
    :close-on-click-modal="false"
    @close="closeAddDialog"
  >
    <el-form ref="formRef" :model="publicDrivateFrom" label-width="80px">
      <el-row :gutter="20" v-if="state.formType === 'public'">
        <el-col :span="12">
          <el-form-item label="发票号">
            <el-input v-model="publicDrivateFrom.invoiceNo" style="width: 240px" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="未税金额">
            <el-input-number
              :style="{ width: '240px' }"
              v-model="publicDrivateFrom.untaxedAmount"
              :min="0"
              :precision="2"
              :disabled="!state.isAdd && props.statusType === 'invoiceNumber'"
              ><template #suffix>
                <span>元</span>
              </template>
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="税额">
            <el-input-number
              :style="{ width: '240px' }"
              v-model="publicDrivateFrom.taxAmount"
              :min="0"
              :precision="2"
              :disabled="!state.isAdd && props.statusType === 'invoiceNumber'"
              ><template #suffix>
                <span>元</span>
              </template>
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="含税金额">
            <el-input-number
              :style="{ width: '240px' }"
              v-model="publicDrivateFrom.taxedAmount"
              :min="0"
              :precision="2"
              :disabled="!state.isAdd && props.statusType === 'invoiceNumber'"
            >
              <template #suffix>
                <span>元</span>
              </template>
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户银行">
            <el-input v-model="publicDrivateFrom.bank" style="width: 240px" :disabled="!state.isAdd && props.statusType === 'invoiceNumber'" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户名称">
            <el-input v-model="publicDrivateFrom.accountName" style="width: 240px" :disabled="!state.isAdd && props.statusType === 'invoiceNumber'" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="银行账号">
            <el-input v-model="publicDrivateFrom.accountNo" style="width: 240px" :disabled="!state.isAdd && props.statusType === 'invoiceNumber'" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="发票文件">
            <text v-if="props.statusType === 'invoiceNumber'">{{ state.attachments }}</text>
            <el-upload
              v-else
              :file-list="state.attachmentsUrl ? [{ name: state.attachments, url: state.attachmentsUrl }] : []"
              ref="uploadFileRef"
              action="#"
              :auto-upload="false"
              :limit="1"
              :on-change="
                (file: any, fileList: any[]) => {
                  uploadFile(file, fileList)
                }
              "
              :on-remove="
                (file: any, fileList: any[]) => {
                  handleRemove(file, fileList)
                }
              "
              :on-exceed="handleExceed"
            >
              <template #trigger>
                <el-button type="primary">点击上传</el-button>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" v-if="state.formType === 'private'">
        <el-col :span="24">
          <el-form-item label="姓名">
            <el-input v-model="publicDrivateFrom.name" style="width: 240px" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="金额">
            <el-input-number :style="{ width: '240px' }" v-model="publicDrivateFrom.amount" :min="0" :precision="2">
              <template #suffix>
                <span>元</span>
              </template>
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="银行账号">
            <el-input v-model="publicDrivateFrom.accountNo" style="width: 240px" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeAddDialog()">取消</el-button>
        <el-button type="primary" @click="submitForm()">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
  import {
    getAccidentApprovalDetailsApi,
    addPublicFeeApplyApi,
    editPublicFeeApplyApi,
    deletePublicFeeApplyApi,
    addPrivateFeeApplyApi,
    editPrivateFeeApplyApi,
    deletePrivateFeeApplyApi,
    fineSelectOptionApi,
    getFineAmountApi,
  } from '@/api/businessManagement'
  import { UploadProps, UploadRawFile, genFileId } from 'element-plus'
  import { uploadFileApi } from '@/api/auth'

  const emit = defineEmits(['closeDialog'])
  const formRef = ref()
  const uploadFileRef = ref()

  const props = defineProps({
    statusType: {
      type: String,
      default: '',
    },
  })

  const paymentDialogVisible = reactive<DialogOption>({
    visible: false,
    title: '新增事故单',
  })

  const state = reactive<any>({
    isAdd: true,
    formType: '',
    detailsFormData: {},
    publicDrivateDialog: false,
    qualityLossPrivateFeeApplyList: [],
    qualityLossPublicFeeApplyList: [],
    attachmentsUrl: '',
    attachments: '',
    fileList: [],
    penaltyTypeList: [],
    accidentId: '',
  })

  /**
   * 表单参数
   */
  const publicDrivateFrom = ref<any>({
    accidentId: '',
    invoiceNo: '',
    untaxedAmount: 0,
    taxAmount: 0,
    taxedAmount: 0,
    bank: '',
    accountName: '',
    accountNo: '',
    fileList: [],
    amount: 0,
    name: '',
    id: '',
  })

  // 对公信息
  const publicTableConfig = {
    tableItem: [
      {
        name: 'invoiceNo',
        label: '发票号',
        align: 'center',
      },
      {
        name: 'untaxedAmount',
        label: '未税金额',
        align: 'center',
      },
      {
        name: 'taxAmount',
        label: '税额',
        align: 'center',
      },
      {
        name: 'taxedAmount',
        label: '含税金额',
        align: 'center',
      },
      {
        name: 'bank',
        label: '开户银行',
        align: 'center',
      },
      {
        name: 'accountName',
        label: '开户名称',
        align: 'center',
      },
      {
        name: 'accountNo',
        label: '银行账号',
        align: 'center',
      },
      {
        name: 'invoiceFile',
        label: '发票文件',
        align: 'center',
      },
    ],
  }
  // 对私信息
  const privateTableConfig = {
    tableItem: [
      {
        name: 'amount',
        label: '金额',
        align: 'center',
      },
      {
        name: 'name',
        label: '姓名',
        align: 'center',
      },
      {
        name: 'accountNo',
        label: '银行账户',
        align: 'center',
      },
    ],
  }

  // 选择罚款类型
  const changePenaltyType = (row: any) => {
    if (row || row == 0) {
      let params = {
        insuranceAmount: 0, //保险金额 - 没有传0
        fineConfigId: row,
        lossAmount: state.detailsFormData.actualAmount ? state.detailsFormData.actualAmount : 0, //质损金额
      }
      getFineAmountApi(params)
        .then((res: any) => {
          if (res.code === 200) {
            state.detailsFormData.driverDeductionAmount = res.data ? res.data : 0
          }
        })
        .catch(() => {})
    }
  }

  // 编辑
  const editColumn = (row: any, index: number, type: string) => {
    let rowData = JSON.parse(JSON.stringify(row))
    if (type === 'public') {
      publicDrivateFrom.value = {
        invoiceNo: rowData.invoiceNo,
        untaxedAmount: rowData.untaxedAmount ? rowData.untaxedAmount : 0,
        taxAmount: rowData.taxAmount ? rowData.taxAmount : 0,
        taxedAmount: rowData.taxedAmount ? rowData.taxedAmount : 0,
        bank: rowData.bank,
        accountName: rowData.accountName,
        accountNo: rowData.accountNo,
        fileList: rowData.invoiceUrl ? JSON.parse(rowData.invoiceUrl) : [],
        accidentId: rowData.accidentId,
        id: rowData.id,
      }
      state.attachmentsUrl = rowData.invoiceUrl ? JSON.parse(rowData.invoiceUrl)[0].url : ''
      state.attachments = rowData.invoiceUrl ? JSON.parse(rowData.invoiceUrl)[0].name : ''
    } else if (type === 'private') {
      publicDrivateFrom.value = {
        accidentId: rowData.accidentId,
        amount: rowData.amount ? rowData.amount : 0,
        name: rowData.name,
        accountNo: rowData.accountNo,
        id: rowData.id,
      }
    }
    state.formType = type
    state.isAdd = false
    state.publicDrivateDialog = true
  }

  // 获取数据
  const getDetails = async (id: string, type: string) => {
    state.accidentId = id
    // 获取下拉数据
    getFineDataList()
    getAccidentApprovalDetailsApi({ id: id })
      .then((res: any) => {
        if (type != 'no') {
          state.detailsFormData = res.data
        }
        state.qualityLossPrivateFeeApplyList = res.data.qualityLossPrivateFeeApplyList
        state.qualityLossPublicFeeApplyList = res.data.qualityLossPublicFeeApplyList
      })
      .catch(() => {
        state.qualityLossPrivateFeeApplyList = []
        state.qualityLossPublicFeeApplyList = []
      })
  }

  // 获取司机罚款类型
  const getFineDataList = async () => {
    fineSelectOptionApi({})
      .then((res: any) => {
        state.penaltyTypeList = res.data
      })
      .catch(() => {
        state.penaltyTypeList = []
      })
  }

  /**
   * 保存
   */
  const submitForm = async () => {
    const formParams = JSON.parse(JSON.stringify(publicDrivateFrom.value))
    var params = {}
    if (state.formType === 'public') {
      params = {
        accidentId: formParams.accidentId,
        accountName: formParams.accountName,
        accountNo: formParams.accountNo,
        bank: formParams.bank,
        invoiceNo: formParams.invoiceNo,
        invoiceUrl: formParams.fileList && formParams.fileList.length > 0 ? JSON.stringify(formParams.fileList) : '',
        taxAmount: formParams.taxAmount,
        taxedAmount: formParams.taxedAmount,
        untaxedAmount: formParams.untaxedAmount,
        id: formParams.id,
      }
    } else if (state.formType === 'private') {
      params = {
        id: formParams.id,
        accidentId: formParams.accidentId,
        accountNo: formParams.accountNo,
        name: formParams.name,
        amount: formParams.amount,
      }
    }
    let newDataApi
    if (state.isAdd) {
      if (state.formType === 'public') {
        newDataApi = addPublicFeeApplyApi
      } else if (state.formType === 'private') {
        newDataApi = addPrivateFeeApplyApi
      }
    } else {
      if (state.formType === 'public') {
        newDataApi = editPublicFeeApplyApi
      } else if (state.formType === 'private') {
        newDataApi = editPrivateFeeApplyApi
      }
    }
    if (newDataApi) {
      newDataApi(params)
        .then((res: any) => {
          if (res.code === 200) {
            ElMessage.success(res.message)
            // 再次获取详情接口渲染列表
            if (res.data) {
              getDetails(res.data[0].accidentId, 'no')
            }
            state.publicDrivateDialog = false
          }
        })
        .catch((err: any) => {})
    }
  }

  // 下载文件
  const downLoadFile = (row: any) => {
    let newRow = JSON.parse(row)
    window.open(newRow[0].url)
  }

  // 删除
  const deleteColumn = (row: any, index: number, type: string) => {
    let newDataApi
    if (type === 'public') {
      newDataApi = deletePublicFeeApplyApi
    } else if (type === 'private') {
      newDataApi = deletePrivateFeeApplyApi
    }
    let params = {
      accidentId: row.accidentId,
      id: row.id,
    }
    if (newDataApi) {
      newDataApi(params)
        .then((res: any) => {
          if (res.code === 200) {
            ElMessage.success(res.message)
            // 再次获取详情接口渲染列表
            getDetails(row.accidentId, 'no')
          }
        })
        .catch((err: any) => {})
    }
  }

  // 新增
  const addForm = (type: any) => {
    state.formType = type
    state.isAdd = true
    publicDrivateFrom.value = {
      accidentId: state.accidentId ? state.accidentId : '',
      id: '',
      invoiceNo: '',
      untaxedAmount: 0,
      taxAmount: 0,
      taxedAmount: 0,
      bank: '',
      accountName: '',
      accountNo: '',
      fileList: [],
      amount: 0,
      name: '',
    }
    state.publicDrivateDialog = true
  }

  /**
   * 关闭弹窗
   */
  const closeDialog = () => {
    emit('closeDialog')
  }

  //上传发票
  const uploadFile = async (file: any, fileList: any[]) => {
    if (file.status !== 'ready') return
    const params = {
      file: file.raw,
      businessLine: 204,
    }
    let res = await uploadFileApi('logistics/system/common/invoice/ocr', params) // 此处为自己的上传接口
    if (res.data) {
      publicDrivateFrom.value.fileList = res.data.uploads ? res.data.uploads : []
      publicDrivateFrom.value.invoiceNo = res.data.code ? res.data.code : '' // 发票号
      publicDrivateFrom.value.taxAmount = res.data.taxAmount ? res.data.taxAmount : 0 // 税额
      publicDrivateFrom.value.untaxedAmount = res.data.amount ? res.data.amount : 0 // 未税金额
      publicDrivateFrom.value.taxedAmount = res.data.totalAmount ? res.data.totalAmount : 0 // 含税金额
    }
  }

  const handleRemove = (file: any, fileList: any[]) => {
    //删除当前文件
    state.attachmentsUrl = ''
    state.attachments = ''
  }

  // 替换文件
  const handleExceed: UploadProps['onExceed'] = (files) => {
    uploadFileRef.value!.clearFiles()
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    uploadFileRef.value!.handleStart(file)
  }

  const closeAddDialog = () => {
    state.publicDrivateDialog = false
  }

  defineExpose({
    state, //状态
    getDetails, //获取质量损失
    closeDialog, //关闭弹窗
    paymentDialogVisible, //弹窗
  })
</script>

<style scoped>
  .left_m_30 {
    margin-left: 30px;
  }
  .left_m_20 {
    margin-left: 20px;
  }

  .textColor {
    color: #fca130;
  }
  .formClass {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    border-bottom: 1px solid #eee;
    padding: 8px 10px;
    margin: 20px 0;
  }
</style>
