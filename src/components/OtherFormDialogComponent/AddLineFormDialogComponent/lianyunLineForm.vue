<!--
 * @Author: llm
 * @Date: 2024-08-29 14:29:09
 * @LastEditors: llm
 * @LastEditTime: 2024-09-04 18:12:24
 * @Description:联运线路form表单
-->
<template>
  <div>
    <el-form ref="lianyunLineForm" :model="formData" label-width="120px" style="width: 100%">
      <el-form-item label="联运线路">
        <div style="width: 100%">
          <el-button type="primary" icon="Plus" size="small" circle @click="addTableItem" />
          <div>
            <el-text class="mx-1" type="warning">拖拽修改线路顺序，按照当前线路顺序来匹配执行顺序</el-text>
            <el-table :data="formData.tableData1" style="width: 100%" row-key="id" ref="dragTable">
              <!-- <el-table-column type="selection" width="55"></el-table-column> -->
              <el-table-column prop="name" label="线路名称"></el-table-column>
              <el-table-column prop="startName" label="出发地"></el-table-column>
              <el-table-column prop="endName" label="目的地"></el-table-column>
              <el-table-column prop="transportTypeName" label="运输类型"></el-table-column>
              <el-table-column label="操作" width="120px">
                <template #default="{ row, $index }">
                  <!-- 上移 -->
                  <!-- <el-button type="primary" icon="Arrow-up" size="small" circle v-if="$index === formData.tableData.length - 1" /> -->
                  <!-- 下移 -->
                  <!-- <el-button type="primary" icon="Arrow-down" size="small" circle v-if="$index < formData.tableData.length - 1" /> -->
                  <el-button type="danger" icon="Delete" size="small" circle @click="deleteTableItem(row)" />
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="60%" :draggable="true" :close-on-click-modal="false">
      <div>
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" style="width: 90%" :rules="rules">
          <el-form-item label="线路起点" prop="startCode">
            <!-- 级联选择器-省市区 -->
            <el-cascader
              style="width: 100%"
              v-model="queryParams.startCode"
              clearable
              filterable
              collapse-tags
              collapse-tags-tooltip
              :options="districtSelectOption"
              :props="{ multiple: false, checkStrictly: true }"
            />
          </el-form-item>
          <el-form-item label="线路终点" prop="endCode">
            <!-- 级联选择器-省市区 -->
            <el-cascader
              style="width: 100%"
              v-model="queryParams.endCode"
              clearable
              filterable
              collapse-tags
              collapse-tags-tooltip
              :options="districtSelectOption"
              :props="{ multiple: false, checkStrictly: true }"
            />
          </el-form-item>
          <el-form-item label="运输类型" prop="transportType">
            <el-select
              v-model="queryParams.transportType"
              style="width: 200px"
              value-key="value"
              :multiple="false"
              collapse-tags
              collapse-tags-tooltip
              placeholder="运输类型"
              filterable
            >
              <el-option v-for="i in transportTypeOptions" key="i.value" :label="i.label" :value="i.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button class="filter-item" type="primary" @click="handleQuery">
              <i-ep-search />
              搜索
            </el-button>
            <el-button @click="resetQuery">
              <i-ep-refresh />
              重置
            </el-button>
          </el-form-item>
        </el-form>
        <el-table
          :data="formData.tableData"
          style="width: 100%"
          row-key="id"
          v-loading="tableLoading"
          max-height="50vh"
          @selection-change="handleSelectionChange"
        >
          <!-- 复选框 -->
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="name" label="线路名称"></el-table-column>
          <el-table-column prop="startName" label="出发地"></el-table-column>
          <el-table-column prop="endName" label="目的地"></el-table-column>
          <el-table-column prop="transportTypeName" label="运输类型"></el-table-column>
        </el-table>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="createLine">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { getListAll } from '@/api/auth'
  import { postCustomerLineApi } from '@/api/customerCenter/customerBaseData'
  import { getDistrictSelectOptionApi } from '@/api/planManagement'
  import { transportTypeOptions } from '@/utils/selectOptions'
  import Sortable from 'sortablejs'
  const props = defineProps({
    requestUri: {
      type: String,
      default: '',
    },
    relationId: {
      //客户id
      type: String,
      default: '',
    },
  })
  const lianyunLineForm = ref()
  const dragTable = ref()
  const formData = ref({
    tableData: [] as any[],
    tableData1: [] as any[],
  })
  const districtSelectOption = ref()
  const queryFormRef = ref()
  const tableLoading = ref(false)
  const selectRows = ref<any[]>([])
  const rules = ref({
    startCode: [{ required: false, message: '请选择线路起点', trigger: 'blur' }],
    endCode: [{ required: false, message: '请选择线路终点', trigger: 'blur' }],
  })
  const queryParams = ref({
    startCode: '',
    endCode: '',
    transportType: '',
  })
  const dialog = ref({
    visible: false,
    title: '联运线路',
  })
  const addTableItem = () => {
    if (!props.relationId) {
      return ElMessage.error('请先选择客户')
    }
    dialog.value.visible = true
  }
  const deleteTableItem = (row: any) => {
    formData.value.tableData1 = formData.value.tableData1.filter((item: any) => item.id !== row.id)
  }
  const addCustomerLine = async () => {
    const data = await postCustomerLineApi({})
  }
  const createLine = () => {
    if (selectRows.value.length <= 1) {
      return ElMessage.error('联运线路至少需要2条线路')
    }
    formData.value.tableData1 = selectRows.value
    dialog.value.visible = false
  }
  //使用sortable实现表格拖拽排序
  const initSortable = () => {
    const el = dragTable.value?.$el.querySelector('.el-table__body-wrapper tbody')
    const sortable = new Sortable(el, {
      animation: 150,
      handle: '.el-table__row',
      onEnd: (evt: any) => {
        const { oldIndex, newIndex } = evt
        const oldData = formData.value.tableData1[oldIndex]
        formData.value.tableData1.splice(oldIndex, 1)
        formData.value.tableData1.splice(newIndex, 0, oldData)
      },
    })
  }
  const setSort = () => {
    nextTick(() => {
      initSortable()
    })
  }
  const handleQuery = async () => {
    setSort()
    //校验
    if (!queryFormRef.value) return
    await queryFormRef.value.validate(async (valid: any) => {
      if (valid) {
        const d = transportTypeOptions.map((item) => item.value)
        const params = {
          startCode: queryParams.value?.startCode?.[queryParams.value?.startCode.length - 1] || '',
          endCode: queryParams.value?.endCode?.[queryParams.value?.endCode.length - 1] || '',
          transportType: queryParams.value.transportType ? queryParams.value.transportType : d.join(','),
          relationId: props.relationId,
        }
        tableLoading.value = true
        const { data } = await getListAll(params, props.requestUri)
        formData.value.tableData = data
        tableLoading.value = false
      } else {
      }
    })
  }
  const resetQuery = () => {
    //重置表单
    queryParams.value = {
      startCode: '',
      endCode: '',
      transportType: '',
    }
    handleQuery()
  }
  const handleSelectionChange = (e: any[]) => {
    selectRows.value = e
  }
  const closeDialog = () => {
    //重置表单
    queryParams.value = {
      startCode: '',
      endCode: '',
      transportType: '',
    }
    formData.value.tableData = []
    dialog.value.visible = false
  }
  onMounted(async () => {
    const { data } = await getDistrictSelectOptionApi({})
    nextTick(() => {
      setSort()
    })
    districtSelectOption.value = data
  })
  defineExpose({
    formData,
  })
</script>
<style scoped lang="scss"></style>
