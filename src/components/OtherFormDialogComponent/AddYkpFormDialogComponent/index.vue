<!--
 * @Author: llm
 * @Date: 2024-09-24 16:36:04
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-03-11 11:51:47
 * @Description:
-->
<!--
 * @Author: llm
 * @Date: 2024-09-24 16:36:13
 * @LastEditors: llm
 * @LastEditTime: 2024-09-25 10:56:14
 * @Description: 新增移库计划弹窗
-->
<template>
  <div>
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" width="80%" :draggable="true" :close-on-click-modal="false" @close="closeDialog">
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form ref="formRef" :model="state.formData" :rules="rules" :inline="true" :disabled="state.origin === 'viewYkp'">
          <el-form-item label="移库计划名称" prop="name">
            <el-input v-model="state.formData.name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="基地" prop="baseId">
            <el-select v-model="state.formData.baseId" placeholder="单选" style="width: 180px" @change="selectBaseId">
              <el-option v-for="item in baseList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="执行班长" prop="leaderId">
            <el-select v-model="state.formData.leaderId" placeholder="单选(运备班长)" style="width: 180px" v-if="state.origin !== 'viewYkp'">
              <el-option v-for="item in leaderList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <el-input v-model="state.formData.leaderName" v-else></el-input>
          </el-form-item>
          <el-divider border-style="dashed"></el-divider>
          <div class="flex">
            <div class="flex-[0_0_100px]">
              <div class="m-[10px_0] text-center font-bold">移出</div>
              <el-form-item label="仓库" prop="warehouseId">
                <el-select
                  v-model="state.formData.warehouseId"
                  placeholder="单选"
                  style="width: 270px"
                  @change="selectWarehouseId"
                  v-if="state.origin !== 'viewYkp'"
                >
                  <el-option v-for="item in warehouseList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-input v-model="state.formData.warehouseName" v-else></el-input>
              </el-form-item>
              <el-form-item label="库区" prop="partitionId">
                <el-select
                  v-model="state.formData.partitionId"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :placeholder="state.origin !== 'viewYkp' ? '请选择(可多选)' : ''"
                  style="width: 270px"
                  @change="selectWareColumnId"
                  v-if="state.origin !== 'viewYkp'"
                >
                  <el-option v-for="item in warehouseAreaList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-input v-model="state.formData.partitionNames" v-else></el-input>
              </el-form-item>
              <el-form-item label="库列" prop="columnId">
                <el-select
                  v-model="state.formData.columnId"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :placeholder="state.origin !== 'viewYkp' ? '请选择(可多选)' : ''"
                  style="width: 270px"
                  v-if="state.origin !== 'viewYkp'"
                >
                  <el-option-group v-for="group in warehouseColumnList" :key="group.value" :label="group.label">
                    <el-option v-for="item in group.children" :key="item.value" :label="item.label" :value="item.value" />
                  </el-option-group>
                </el-select>
                <el-input v-model="state.formData.columnNames" v-else></el-input>
              </el-form-item>
              <el-form-item label="品牌" prop="brandId">
                <el-select
                  v-model="state.formData.brandId"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :placeholder="state.origin !== 'viewYkp' ? '请选择(可多选)' : ''"
                  style="width: 270px"
                  @change="selectBrandId"
                  v-if="state.origin !== 'viewYkp'"
                >
                  <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-input v-model="state.formData.brandNames" v-else></el-input>
              </el-form-item>
              <el-form-item label="车型" prop="vehicleModelId">
                <el-select
                  v-model="state.formData.vehicleModelId"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :placeholder="state.origin !== 'viewYkp' ? '请选择(可多选)' : ''"
                  style="width: 270px"
                  v-if="state.origin !== 'viewYkp'"
                >
                  <el-option v-for="item in vehicleModelList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-input v-model="state.formData.vehicleModelNames" v-else></el-input>
              </el-form-item>
              <el-form-item label="按库龄筛选" prop="stockAge">
                <el-input
                  type="number"
                  v-model="state.formData.stockAge"
                  min="0"
                  :placeholder="state.origin !== 'viewYkp' ? '查询库龄大于xx天的vin' : ''"
                  style="width: 270px"
                ></el-input>
              </el-form-item>
              <el-form-item label="按入库日期筛选" prop="inStoreTime">
                <el-date-picker
                  v-model="state.formData.inStoreTime"
                  @change="changeInstoreTime"
                  value-format="YYYY-MM-DD"
                  type="date"
                  :placeholder="state.origin !== 'viewYkp' ? '查询入库日期之前的vin' : ''"
                  :disabled-date="disabledDate"
                  style="width: 270px"
                />
              </el-form-item>
              <el-form-item label="移库数量" prop="moveTotal">
                <view>
                  <el-input type="number" v-model="state.formData.moveTotal" :placeholder="state.origin !== 'viewYkp' ? '请输入' : ''" style="width: 230px">
                    <template #append v-if="state.origin !== 'viewYkp'">
                      <el-button type="primary" size="small" @click="getMoveTotal">计算</el-button>
                    </template>
                  </el-input>
                </view>
              </el-form-item>
            </div>
            <el-divider border-style="dashed" direction="vertical"></el-divider>
            <div class="flex-1">
              <div class="m-[10px_0] text-center font-bold">移入</div>
              <el-table :data="state.formData.planInList" style="width: 100%" class="text-center">
                <el-table-column prop="warehouseParam" label="仓库" align="center">
                  <template #default="{ row, $index }">
                    <el-form-item
                      label="仓库"
                      :prop="`planInList.${$index}.warehouseParam`"
                      :rules="[{ required: true, message: '请选择仓库', trigger: 'change' }]"
                    >
                      <el-select
                        v-model="row.warehouseParam"
                        placeholder="单选"
                        @change="selectRowWarehouseId($event, $index, true)"
                        style="width: 160px"
                        v-if="state.origin !== 'viewYkp'"
                      >
                        <el-option v-for="item in warehouseList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                      </el-select>
                      <el-input v-model="row.warehouseName" v-else></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column prop="partitionParam" label="库区" align="center">
                  <template #default="{ row, $index }">
                    <el-form-item
                      label="库区"
                      :prop="`planInList.${$index}.partitionParam`"
                      :rules="[{ required: false, message: '请选择库区', trigger: 'change' }]"
                    >
                      <el-select
                        v-model="row.partitionParam"
                        multiple
                        collapse-tags
                        collapse-tags-tooltip
                        :placeholder="state.origin !== 'viewYkp' ? '请选择(可多选)' : ''"
                        style="width: 160px"
                        @change="selectRowWareColumnId($event, $index, true)"
                        v-if="state.origin !== 'viewYkp'"
                      >
                        <el-option :label="_item.label" :value="_item.value" v-for="(_item, _index) in row.partitionList" :key="_index"></el-option>
                      </el-select>
                      <el-input v-model="row.partitionNames" v-else></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column prop="columnParam" label="库列" align="center">
                  <template #default="{ row, $index }">
                    <el-form-item
                      label="库列"
                      :prop="`planInList.${$index}.columnParam`"
                      :rules="[{ required: false, message: '请选择库列', trigger: 'change' }]"
                    >
                      <el-select
                        v-model="row.columnParam"
                        multiple
                        collapse-tags
                        collapse-tags-tooltip
                        :placeholder="state.origin !== 'viewYkp' ? '请选择(可多选)' : ''"
                        style="width: 160px"
                        v-if="state.origin !== 'viewYkp'"
                      >
                        <el-option-group v-for="group in row.columnList" :key="group.value" :label="group.label">
                          <el-option v-for="item in group.children" :key="item.value" :label="item.label" :value="item.value" />
                        </el-option-group>
                      </el-select>
                      <el-input v-model="row.columnNames" v-else></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" align="center" fixed="right" v-if="state.origin !== 'viewYkp'">
                  <template #default="scope">
                    <el-button
                      type="primary"
                      icon="Plus"
                      size="small"
                      circle
                      @click="addTableItem"
                      v-if="scope.$index === state.formData.planInList.length - 1"
                    />
                    <el-button
                      type="danger"
                      icon="Delete"
                      size="small"
                      circle
                      @click="deleteTableItem(scope.row, scope.$index)"
                      v-if="state.formData.planInList.length > 1"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-form>
      </el-scrollbar>
      <template #footer v-if="state.origin !== 'viewYkp'">
        <div class="dialog-footer">
          <el-button @click="closeDialog()">取 消</el-button>
          <el-button type="primary" v-loading="submitLoading" @click="handleSubmit(formRef)">保 存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import {
    getExecuteLeaderSelectOptionsApi,
    getBaseBrandSelectOptionsApi,
    getBaseSelectOptionsApi,
    getWarehouseSelectOptionsByBaseIdApi,
    getWarePartitionColumnSelectOptionsByRelationIdApi,
    getWarePartitionSelectOptionsByRelationIdApi,
    getVehicleModelSelectOptionsApi,
    getWarehouseMoveInventoryPlanSelectOptionsApi,
    postWarehouseMoveInventoryPlanApi,
  } from '@/api/ykp'
  import type { ComponentSize, FormInstance, FormRules } from 'element-plus'

  import { postSafetyStandardTemplateAddApi, updateSafetyStandardTemplateApi } from '@/api/safetyManagement'
  import { PlanInListVO } from '@/api/ykp/type'
  const disabledDate = (time: Date) => {
    return time.getTime() > Date.now()
  }
  const dialogVisible = reactive<DialogOption>({
    visible: false,
  })
  const emit = defineEmits(['resetQuery', 'openInspectionDialog'])
  const submitLoading = ref(false)
  const formRef = ref()

  const rules = {
    name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    baseId: [{ required: true, message: '请选择基地', trigger: 'change' }],
    leaderId: [{ required: true, message: '请选择执行班长', trigger: 'change' }],
    warehouseId: [{ required: true, message: '请选择仓库', trigger: 'change' }],
    moveTotal: [{ required: true, message: '请输入移库数量', trigger: 'blur' }],
    'planInList.warehouseParam': [{ required: true, message: '请选择仓库', trigger: 'change' }],
    'planInList.partitionParam': [{ required: true, message: '请选择库区', trigger: 'change' }],
    'planInList.columnParam': [{ required: true, message: '请选择库列', trigger: 'change' }],
  }
  const result = ref('1')
  const state = reactive({
    origin: '',
    formData: {
      name: '', //移库计划名称
      moveTotal: '', //移库数量
      baseId: '', //基地
      brandId: [], //品牌
      vehicleModelId: [], //车型
      leaderId: '',
      warehouseId: '', //库
      partitionId: [], //区
      columnId: [], //列
      stockAge: '', //库龄
      inStoreTime: '', //入库日期
      planInList: [
        {
          columnParam: [],
          partitionParam: [],
          warehouseParam: '',
        },
      ],
    } as any,
  })
  watch(
    () => dialogVisible.visible,
    (val) => {
      if (val) {
        getBaseSelectOptionsList()
      }
    },
  )
  const baseList = ref<OptionType[]>([])
  const leaderList = ref<OptionType[]>([])
  const warehouseList = ref<OptionType[]>([])
  const warehouseAreaList = ref<OptionType[]>([])
  const warehouseColumnList = ref<OptionType[]>([])
  const brandList = ref<OptionType[]>([])
  const vehicleModelList = ref<OptionType[]>([])
  const changeInstoreTime = (e: string) => {
    state.formData.inStoreTime = e ?? ''
  }
  const selectBaseId = async (id: string) => {
    state.formData.warehouseId = ''
    state.formData.partitionId = []
    state.formData.columnId = []
    state.formData.leaderId = ''
    state.formData.brandId = []
    state.formData.vehicleModelId = []
    state.formData.planInList = [
      {
        columnParam: [],
        partitionParam: [],
        warehouseParam: '',
      },
    ]
    warehouseList.value = (await getWarehouseSelectOptionsByBaseIdApi({ baseId: id, simple: false })).data
    brandList.value = (await getBaseBrandSelectOptionsApi({ relationId: id, simple: false })).data
    leaderList.value = (await getExecuteLeaderSelectOptionsApi({ baseId: id, simple: false })).data
    //如果warehouseList.value.length === 1,默认选中
    if (warehouseList.value.length === 1) {
      state.formData.warehouseId = warehouseList.value[0].value
    }
    if (leaderList.value.length === 1) {
      state.formData.leaderId = leaderList.value[0].value
    }
    if (brandList.value.length === 1) {
      state.formData.brandId = [brandList.value[0].value]
      selectBrandId(state.formData.brandId)
    }
  }
  const selectWarehouseId = async (id: string) => {
    state.formData.partitionId = []
    state.formData.columnId = []
    const { data } = await getWarePartitionSelectOptionsByRelationIdApi({ relationId: id, simple: false })
    warehouseAreaList.value = data
    if (warehouseAreaList.value.length === 1) {
      state.formData.partitionId = [warehouseAreaList.value[0].value]
      selectWareColumnId([warehouseAreaList.value[0].value])
    }
  }
  const selectWareColumnId = async (id: Array<string>) => {
    state.formData.columnId = []
    const { data } = await getWarePartitionColumnSelectOptionsByRelationIdApi({ relationId: id.join(','), simple: false, tree: true })
    warehouseColumnList.value = data
    if (warehouseColumnList.value.length === 1) {
      state.formData.columnId = [warehouseColumnList.value[0].children![0].value]
    }
  }
  const selectBrandId = async (id: Array<string>) => {
    state.formData.vehicleModelId = []
    const brandId = id.join(',')
    const { data } = await getVehicleModelSelectOptionsApi({ brandId, simple: false })
    vehicleModelList.value = data
    if (vehicleModelList.value.length === 1) {
      state.formData.vehicleModelId = [vehicleModelList.value[0].value]
    }
  }
  const selectRowWarehouseId = async (e: string, index: number, reset: boolean = false) => {
    if (reset) {
      state.formData.planInList[index].partitionParam = []
      state.formData.planInList[index].columnParam = []
    }

    const { data } = await getWarePartitionSelectOptionsByRelationIdApi({ relationId: e, simple: false })
    state.formData.planInList[index].partitionList = data

    if (state.formData.planInList[index].partitionList.length === 1) {
      state.formData.planInList[index].partitionParam = [state.formData.planInList[index].partitionList[0].value]
      selectRowWareColumnId([state.formData.planInList[index].partitionList[0].value], index)
    }
  }
  const selectRowWareColumnId = async (e: Array<string>, index: number, reset: boolean = false) => {
    if (reset) {
      state.formData.planInList[index].columnParam = []
    }
    const { data } = await getWarePartitionColumnSelectOptionsByRelationIdApi({ relationId: e.join(','), simple: false, tree: true })
    state.formData.planInList[index].columnList = data
    if (state.formData.planInList[index].columnList.length === 1) {
      state.formData.planInList[index].columnParam = [state.formData.planInList[index].columnList[0].children![0].value]
    }
  }
  watch(
    () => dialogVisible.visible,
    async (val) => {
      if (val) {
        state.formData.planInList.forEach(async (item: any, index: number) => {
          await selectRowWarehouseId(item.warehouseParam, index)
          await selectRowWareColumnId(item.partitionParam, index)
        })
      }
    },
    { deep: true },
  )
  const getMoveTotal = async () => {
    const params = {
      warehouseId: state.formData.warehouseId,
      partitionId: state.formData.partitionId.join(','),
      columnId: state.formData.columnId.join(','),
      brandId: state.formData.brandId.join(','),
      vehicleModelId: state.formData.vehicleModelId.join(','),
      stockAge: state.formData.stockAge,
      inStoreTime: state.formData.inStoreTime,
    }
    const { data } = await getWarehouseMoveInventoryPlanSelectOptionsApi(params)
    state.formData.moveTotal = data
  }
  //获取基地下拉列表
  const getBaseSelectOptionsList = async () => {
    const { data } = await getBaseSelectOptionsApi({ simple: true })
    baseList.value = data
  }
  const handleSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        const params = {
          name: state.formData.name,
          leaderParam: state.formData.leaderId,
          baseParam: state.formData.baseId,
          warehouseParam: state.formData.warehouseId,
          partitionParam: state.formData.partitionId.join(','),
          columnParam: state.formData.columnId.join(','),
          brandParam: state.formData.brandId.join(','),
          vehicleModelParam: state.formData.vehicleModelId.join(','),
          stockAge: state.formData.stockAge,
          inStoreDatetime: state.formData.inStoreTime,
          moveTotal: state.formData.moveTotal,
          planInList: state.formData.planInList.map(
            ({
              columnParam,
              partitionParam,
              warehouseParam,
            }: {
              partitionList: Array<string>
              columnParam: Array<string>
              partitionParam: Array<string>
              warehouseParam: Array<string>
            }) => {
              return { warehouseParam, columnParam: columnParam.join(','), partitionParam: partitionParam.join(',') }
            },
          ),
        }
        submitLoading.value = true
        await postWarehouseMoveInventoryPlanApi(params)
          .then(() => {
            submitLoading.value = false
            dialogVisible.visible = false
            ElMessage.success('操作成功')
            closeDialog()
            emit('resetQuery')
          })
          .catch((error: any) => {
            submitLoading.value = false
          })
        //更新
        // if (state.formData.standardNo) {
        //   if (!state.formData.patrolPathList) {
        //     state.formData.patrolPathList = [];
        //   }
        //   await updateSafetyStandardTemplateApi(state.formData)
        //     .then(() => {
        //       submitLoading.value = false;
        //       dialogVisible.visible = false;
        //       ElMessage.success('操作成功');
        //       closeDialog();
        //       emit('resetQuery');
        //     })
        //     .catch((error: any) => {
        //       submitLoading.value = false;
        //     });
        // } else {
        //   await postWarehouseMoveInventoryPlanApi(params)
        //     .then(() => {
        //       submitLoading.value = false;
        //       dialogVisible.visible = false;
        //       ElMessage.success('操作成功');
        //       closeDialog();
        //       emit('resetQuery');
        //     })
        //     .catch((error: any) => {
        //       submitLoading.value = false;
        //     });
        // }
      } else {
      }
    })
  }
  const closeDialog = () => {
    //清空表单数据
    state.formData = {
      name: '', //移库计划名称
      moveTotal: '', //移库数量
      baseId: '', //基地
      brandId: [], //品牌
      vehicleModelId: [], //车型
      leaderId: '',
      warehouseId: '', //库
      partitionId: [], //区
      columnId: [], //列
      stockAge: '', //库龄
      inStoreTime: '', //入库日期
      planInList: [
        {
          columnParam: [],
          partitionParam: [],
          warehouseParam: '',
        },
      ],
    }
    formRef.value.resetFields()
    //清除所有下拉
    baseList.value = []
    brandList.value = []
    vehicleModelList.value = []
    leaderList.value = []
    warehouseList.value = []
    warehouseAreaList.value = []
    warehouseColumnList.value = []
    dialogVisible.visible = false
  }
  /**
   * 新增考核项
   */
  const addTableItem = () => {
    state.formData.planInList.push({
      columnParam: [],
      partitionParam: [],
      warehouseParam: '',
    })
  }
  /**
   * 删除考核项
   */
  const deleteTableItem = (row: any, index: number) => {
    state.formData.planInList.splice(index, 1)
  }
  const resetForm = () => {
    state.formData = {
      name: '', //移库计划名称
      moveTotal: '', //移库数量
      baseId: '', //基地
      brandId: [], //品牌
      vehicleModelId: [], //车型
      leaderId: '',
      warehouseId: '', //库
      partitionId: [], //区
      columnId: [], //列
      stockAge: '', //库龄
      inStoreTime: '', //入库日期
      patrolPathList: [],
      planInList: [
        {
          columnParam: [],
          partitionParam: [],
          warehouseParam: '',
        },
      ],
    }
  }
  defineExpose({
    dialogVisible,
    selectBaseId,
    selectWarehouseId,
    selectWareColumnId,
    state,
    resetForm,
  })
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    border-top-color: #e89e42;
    margin-bottom: 0;
  }

  :deep(.el-divider--vertical) {
    border-left: 1px dashed #e89e42;
    height: 420px;
  }

  :deep(.el-divider__text) {
    color: #e89e42;
  }
</style>
