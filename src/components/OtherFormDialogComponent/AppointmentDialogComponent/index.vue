<!--
 * @Author: llm
 * @Date: 2024-01-15 15:57:17
 * @LastEditors: llm
 * @LastEditTime: 2025-01-07 20:43:19
 * @Description: 公路发运-运单申请-预约信息查询弹窗
-->
<template>
  <div>
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="60%" :draggable="true" :close-on-click-modal="false">
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form ref="ruleFormRef" :inline="false" :model="state.formData" style="width: 93%; margin: 0 auto">
          <el-form-item label="车辆" prop="vehicleId" :rules="[{ required: true, message: '请选择预约车辆', trigger: 'change' }]">
            <el-select filterable v-model="state.formData!.vehicleId" style="width: 200px" placeholder="请选择车辆" @change="vehicleChange">
              <el-option v-for="(_item, _index) in state.formData!.vehicles" :key="_item.vehicleId" :label="_item.vehicleNo" :value="_item.vehicleId" />
            </el-select>
          </el-form-item>
          <el-form-item
            label="下级承运商"
            prop="levelCarrierIds"
            :rules="[{ required: false, message: '请选择承运商', trigger: 'change' }]"
            v-if="state.formData!.vehicleId"
          >
            <tree-selector :treeData="state.formData!.carriers" @update:selectedNodes="getSelectNodes($event, 'levelCarrierIds')" />
          </el-form-item>
          <el-form-item label="司机" prop="driverId" :rules="[{ required: true, message: '请选择司机', trigger: 'change' }]">
            <el-select filterable v-model="state.formData!.driverId" style="width: 200px" placeholder="请选择司机" @change="driverChange">
              <el-option v-for="(_item, _index) in state.formData!.drivers" :key="_item.driverId" :label="_item.driverName" :value="_item.driverId" />
            </el-select>
          </el-form-item>
          <el-form-item label="联系方式" v-if="state.formData!.driverMobile">
            <div>{{ state.formData!.driverMobile }}</div>
          </el-form-item>
          <el-form-item label="是否倒板" v-show="props.appointmentType === '3'">
            <el-radio-group v-model="state.formData!.invertPlate">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- <el-form-item label="倒板仓" v-if="state.formData!.invertPlate">
          <el-select filterable v-model="state.formData!.carrierWareId" style="width: 200px" placeholder="请选择倒板仓" @change="warehouseChange">
            <el-option v-for="(_item, _index) in currentVehicleWares" :key="_item.wareId" :label="_item.wareName" :value="_item.wareId" />
          </el-select>
        </el-form-item> -->
          <el-divider />
          <div v-for="(item, index) in state.formData!.wares" :key="index" style="margin-bottom: 10px">
            <el-form-item label="仓库">
              <div>{{ item.baseWareName }}</div>
            </el-form-item>
            <el-form-item label="日期" :prop="`wares.${index}.appointmentDate`" :rules="[{ required: true, message: '请选择预约日期', trigger: 'change' }]">
              <el-date-picker
                v-model="item.appointmentDate"
                type="date"
                placeholder="请选择预约日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleDateChange(item, index, $event)"
              />
            </el-form-item>
            <el-form-item
              label="预约时间段"
              :prop="`wares.${index}.appointmentId`"
              :rules="[{ required: true, message: '请选择预约时间段', trigger: 'change' }]"
            >
              <el-select
                filterable
                v-model="item.appointmentId"
                style="width: 200px"
                placeholder="请选择预约时间段"
                @change="handleAppointmentDateChange(item, index, $event)"
              >
                <el-option v-for="(_item, _index) in item.appointmentDateList" :key="_index" :label="_item.timeInterval" :value="_item.appointmentId" />
              </el-select>
            </el-form-item>
            <el-card shadow="never" class="time-card" style="width: 100%; padding-bottom: 0 !important" v-if="item.laneList && item.laneList.length > 0">
              <el-form-item style="width: 100%" :prop="`wares.${index}.laneId`" :rules="[{ required: true, message: '请选择预约车道', trigger: 'change' }]">
                <el-scrollbar max-height="150px" style="width: 100%; min-height: 40px">
                  <el-space wrap>
                    <div
                      class="lane-date"
                      :class="{ active: v.active }"
                      v-for="(v, i) in item.laneList"
                      @click="v.appointmentStatus ? '' : selectLane(v, index)"
                    >
                      {{ v.laneName }}
                      <span v-if="v.vinCount">(剩:{{ v.vinCount }})</span>
                    </div>
                  </el-space>
                </el-scrollbar>
              </el-form-item>
            </el-card>
            <el-card shadow="never" class="time-card" style="width: 100%; padding-bottom: 0 !important" v-else-if="item.laneList && item.laneList.length === 0">
              <div>当前时间段暂无可预约车道</div>
            </el-card>
            <div style="width: 100%">
              <TableComponent :tableData="item.orders" :tableConfig="tableConfig" />
            </div>
            <el-divider v-if="index !== state.formData!.wares!.length - 1" />
          </div>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancel(ruleFormRef)">取消</el-button>
          <el-button type="primary" @click="confirm(ruleFormRef)">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 合并运单号弹窗 -->
    <MergeShipmentNoDialogComponent ref="mergeShipmentNoDialogRef" :appointmentType="props.appointmentType" @closeDialog="closeMergeShipmentNoDialog" />
  </div>
</template>
<script setup lang="ts">
  import {
    appointmentIdsApi,
    appointmentSubmitApi,
    appointmentTaskListApi,
    getTreeOptionByVehicle,
    laneAppointmentByDateApi,
    laneAppointmentByDateTimeApi,
  } from '@/api/shipmentManagement'
  import {
    AppointmentsVO,
    ConfirmFormDataVO,
    StateVO,
    VehiclesVO,
    appointmentIdsRequest,
    lanesVO,
    vehicleWaresVO,
    AppointmentItemsVO,
    waresVO,
    MergeShipmentNoVO,
  } from '@/api/shipmentManagement/type'
  import MergeShipmentNoDialogComponent from '@/components/OtherFormDialogComponent/MergeShipmentNoDialogComponent/index.vue'
  import TreeSelector from '@/components/TreeSelector/index.vue'
  import { FormInstance } from 'element-plus'
  import { PropType } from 'vue'
  const emit = defineEmits(['closeDialog'])
  const ruleFormRef = ref<FormInstance>()

  const props = defineProps({
    /**
     * 行信息
     */
    tableRow: {
      type: Object,
      default: () => {
        return {}
      },
    },
    /**
     * 弹窗属性
     */
    dialog: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
    /**
     * 用来区分公路和铁水
     * 3:公路
     * 4:铁水
     */
    appointmentType: {
      type: String,
      default: '',
    },
  })
  const tableConfig = {
    tableItem: [
      {
        name: 'vin',
        label: 'VIN',
        listEnable: true,
      },
      {
        name: 'customerName',
        label: '客户名称',
        listEnable: true,
      },
    ],
  }
  /**
   * 关闭合并运单弹窗
   */
  const closeMergeShipmentNoDialog = () => {
    emit('closeDialog')
    mergeShipmentNoDialogRef.value.mergeShipmentNoVisible = false
  }
  /**
   * 合并运单弹窗
   */
  const mergeShipmentNoDialogRef = ref()
  const state = reactive<StateVO>({
    formData: {},
  })
  /**
   * 当前选中的车道
   */
  const currentLanes = ref<lanesVO[]>([])
  /**
   * 选中的车辆
   */
  const currentVehicle = ref<VehiclesVO>()
  /**
   * 选中的车辆绑定的倒板仓
   */
  const currentVehicleWares = ref<vehicleWaresVO[]>()
  const appointmentDateList = ref<AppointmentItemsVO[]>([])
  /**
   * @description: 选择预约时间段
   * @param {*} item 当前项
   * @param {*} index 当前索引
   * @param {*} event 值
   * @return {*}
   */
  function handleDateChange(item: appointmentIdsRequest, index: number, event: string): void {
    const params = {
      baseWareId: item.baseWareId,
      appointmentDate: item.appointmentDate,
    }
    item.appointmentDateList = []
    laneAppointmentByDateApi(params).then((res: { data: any }) => {
      const { data } = res
      item.appointmentDateList = data
      appointmentDateList.value = data
    })
  }
  //选择预约时间段
  function handleAppointmentDateChange(item: waresVO, index: number, appointmentId: string) {
    if (item.appointmentDateList.length > 0) {
      item.appointmentId = appointmentId
      const { workStart, workEnd } = item.appointmentDateList.find((v: { appointmentId: string }) => v.appointmentId === appointmentId)
      const params = {
        baseWareId: item.baseWareId,
        appointmentDate: item.appointmentDate,
        workStart: workStart,
        workEnd: workEnd,
      }
      laneAppointmentByDateTimeApi(params).then((res: { data: any }) => {
        const { data } = res
        item.laneList = data
      })
    }
  }
  /**
   * @description 选择司机
   * @param driverId
   */
  function driverChange(driverId: string) {
    const currentDriver = state.formData!.drivers!.filter((item) => item.driverId === driverId)
    state.formData!.driverMobile = currentDriver[0].mobile
    state.formData!.driverName = currentDriver[0].driverName
  }
  /**
   * @description 选择车辆
   * @param vehicleId
   */
  async function vehicleChange(vehicleId: string) {
    state.formData!.carriers = []
    const vehicle = state.formData!.vehicles!.filter((item) => item.vehicleId === vehicleId)
    currentVehicleWares.value = vehicle[0].wares
    state.formData!.vehicleNo = vehicle[0].vehicleNo
    state.formData!.invertPlate = vehicle[0].invertPlate
    state.formData!.carrierWareId = vehicle[0].warehouseId
    currentVehicle.value = vehicle[0]

    const params = {
      carrierId: props.tableRow.map((item: any) => item.carrierId).join(','),
      vehicleId,
      back: true,
    }
    const { data } = await getTreeOptionByVehicle(params)
    state.formData!.carriers = data
  }
  /**
   * 选择倒板仓
   */
  function warehouseChange(carrierWareId: string) {
    state.formData!.carrierWareId = carrierWareId
  }
  /**
   *
   */
  /**
   * @description: 选择预约车道
   * @param {*} item 当前项
   * @param {*} index 当前索引
   * @param {*} event 值
   * @return {*}
   */
  function handleLaneChange(item: appointmentIdsRequest, index: number, event: any): void {
    const lane = state.formData!.wares![index].lanes.filter((lane: lanesVO) => {
      return event.includes(lane.laneId)
    })
    currentLanes.value = lane
    state.formData!.wares![index].tempCurrentLanes = lane[0]
    state.formData!.wares![index].laneName = lane[0].laneName
    state.formData!.wares![index].appointmentId = '' //初始化预约时间段;
    const params = {
      appointmentDate: item.appointmentDate,
      laneId: item.laneId,
    }
    appointmentIdsApi(params).then((res) => {
      const { data } = res
      if (currentLanes.value.length > 0) {
        if (data.length > 0) {
          data.forEach((v: any) => {
            currentLanes.value![0].appointments.map((item) => {
              if (v === item.appointmentId) {
                item.appointmentStatus = 1
              } else {
                item.appointmentStatus = 0
              }
            })
          })
        } else {
          currentLanes.value![0].appointments.map((item) => {
            item.appointmentStatus = 0
          })
        }
      }
    })
  }

  /**
   * @description: 选择车道预约时间段
   * @param {*} item 当前选中项
   * @param {*} parentIndex 上级下标
   * @return {*}
   */
  function selectLane(item: AppointmentsVO, parentIndex: number): void {
    //过滤掉tempLaneDate中disabled=true的项， 将当前项的active=true,去掉其他项中的active，
    if (!item.disabled) {
      item.active = true
      state.formData!.wares![parentIndex].laneList!.map((_item: AppointmentsVO) => {
        if (_item.laneId !== item.laneId) {
          _item.active = false
        }
      })
      state.formData!.wares![parentIndex].laneId = item.laneId
      state.formData!.wares![parentIndex].laneName = item.laneName
    }
  }
  /**
   *
   * @param formEl 关闭弹窗
   */
  function cancel(formEl: FormInstance | undefined) {
    emit('closeDialog')
  }
  const confirm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        const laneItems = state.formData?.wares?.map((item) => {
          return {
            baseWareId: item.baseWareId,
            laneId: item.laneId,
            laneName: item.laneName,
            appointmentDate: item.appointmentDate,
            appointmentId: item.appointmentId,
            orderIds: item.orders?.map((order) => order.id),
          }
        })
        state.submitParams = {
          carrierId: props.tableRow[0].carrierId,
          warehouseId: props.tableRow[0]?.baseWareId,
          carrierWareId: state.formData?.carrierWareId,
          driverId: state.formData?.driverId,
          driverMobile: state.formData?.driverMobile,
          driverName: state.formData?.driverName,
          invertPlate: state.formData?.invertPlate,
          vehicleId: state.formData?.vehicleId,
          vehicleNo: state.formData?.vehicleNo,
          levelCarrierIds: state.formData?.levelCarrierIds,
          laneItems: laneItems,
        } as ConfirmFormDataVO
        const params = {
          vehicleInfo: state.formData?.vehicleId!,
          driverInfo: state.formData?.driverId!,
          invertPlate: state.formData?.invertPlate,
        }
        appointmentTaskListApi(params, props.tableRow[0].transportType).then((res: any) => {
          //如果没有合并运单，则直接提交
          if (res.data.length === 0) {
            appointmentSubmitApi(state.submitParams!, props.appointmentType).then((res: any) => {
              ElMessage.success(res.message)
              emit('closeDialog')
            })
          } else {
            // 合并运单
            mergeShipmentNoDialogRef.value.mergeShipmentNo = '' //初始化合并的运单
            mergeShipmentNoDialogRef.value.mergeShipmentNoVisible = true
            mergeShipmentNoDialogRef.value.mergeShipmentNoList = res.data
            mergeShipmentNoDialogRef.value.state.submitParams = state.submitParams
          }
        })
      } else {
      }
    })
  }
  interface SelectValueObjVO {
    selected: any
  }
  const getSelectNodes = (selectValueObj: SelectValueObjVO, name: string) => {
    //遍历selectValueObj对象，将所有的value组成数组赋值给formData.value[name]
    state.formData!.levelCarrierIds = selectValueObj.selected
  }
  defineExpose({
    state,
    handleLaneChange,
  })
</script>
<style scoped lang="scss">
  .lane-date {
    display: inline-block;
    width: 100px;
    height: 32px;
    text-align: center;
    border: 1px solid #ebeef5;
    background-color: #ffffff;
    cursor: pointer;
    border-radius: 4px;
    &.active {
      background-color: #1890ff;
      color: #ffffff;
    }
    &.disabled {
      background-color: #e3e3e3;
      //禁止点击
      cursor: not-allowed;
    }
  }
  .time-card {
    border-bottom: none;
    :deep(.el-card__body) {
      padding-bottom: 0;
    }
  }
</style>
