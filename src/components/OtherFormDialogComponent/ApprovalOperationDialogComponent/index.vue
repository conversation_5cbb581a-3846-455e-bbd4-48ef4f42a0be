<!--
 * @Author: llm
 * @Date: 2023-07-14 10:39:43
 * @LastEditors: llm
 * @LastEditTime: 2024-10-22 10:01:01
 * @Description:  下级审批弹窗操作
 *
-->
<template>
  <div>
    <el-dialog :draggable="true" v-model="dialogVisible.visible" :title="dialogVisible.title" width="60%" @close="closeDialog1">
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form ref="formRef" :model="formData" label-width="120px">
          <el-row :gutter="30">
            <el-col>
              <el-card class="box-card">
                <el-form-item label="换车位置/仓库" prop="warehousePlace" v-if="formData.body[0].warehousePlace">
                  <el-input
                    v-model="formData.body[0].warehousePlace"
                    :disabled="formData.applyStatus !== 4 && formData.applyStatus !== 7"
                    type="text"
                    clearable
                    :readonly="true"
                    placeholder="请输入仓库地址"
                    @click.native.stop="loctionAdress(0)"
                  />
                </el-form-item>
                <el-form-item label="换车位置/仓库" prop="warehouseId" v-else-if="formData.body[0].warehouseName">
                  <el-select
                    v-model="formData.body[0].warehouseId"
                    style="width: 200px"
                    :disabled="formData.applyStatus !== 4 && formData.applyStatus !== 7"
                    filterable
                    clearable
                    placeholder="模糊搜索"
                  >
                    <el-option v-for="item in JSON.parse(JSON.stringify(carriedWarehouseList))" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                </el-form-item>
                <el-form-item label="原车辆" prop="vehicleId">
                  <el-input v-model="formData.origin.vehicleNo" placeholder="原车辆" :disabled="true"></el-input>
                </el-form-item>
                <el-form-item label="替换车辆" prop="vehicleId">
                  <el-input v-model="formData.body[0].vehicleNo" placeholder="替换车辆" :disabled="true"></el-input>
                </el-form-item>
                <el-form-item label="原司机">
                  <el-input v-model="formData.origin.realName" placeholder="原司机" :disabled="true"></el-input>
                </el-form-item>
                <el-form-item label="替换司机">
                  <el-input v-model="formData.body[0].realName" placeholder="替换司机" :disabled="true"></el-input>
                </el-form-item>
                <!-- <el-form-item label="申请车辆图片">
                                    <div v-for="(_v, _i) in picConfig.oldConfig ?? []" :key="_i" style="display: inline-block; margin-right: 10px">
                                        <el-upload
                                                class="avatar-uploader"
                                                ref="uploadRef"
                                                :show-file-list="false"
                                                :auto-upload="false"
                                                :disabled="formData.applyStatus !== 4 && formData.applyStatus !== 7"
                                                :on-change="
                        (file) => {
                          uploadImage(file, _i, 'oldPictureList');
                        }
                      "
                                                :on-success="handleSuccess"
                                                :limit="1"
                                                :on-preview="handlePictureCardPreview"
                                                list-type="picture-card"
                                                action="#"
                                        >
                                            <img
                                                    v-if="formData.oldPictureList[_i].url"
                                                    @click="previewImage(formData.oldPictureList[_i].url)"
                                                    :src="formData.oldPictureList[_i].url"
                                                    class="avatar"
                                                    style="width: 100%; height: 100%"
                                                    alt=""/>
                                            <el-icon v-else>
                                                <Plus/>
                                            </el-icon>
                                        </el-upload>
                                        <div style="font-size: 24rpx; text-align: center">
                                            {{ _v.pictureRemark }}
                                        </div>
                                    </div>
                                </el-form-item>
                                <el-form-item label="替换车辆图片">
                                    <div v-for="(_v, _i) in picConfig.newConfig ?? []" :key="_i" style="display: inline-block; margin-right: 10px">
                                        <el-upload
                                                :disabled="formData.applyStatus !== 4 && formData.applyStatus !== 7"
                                                class="avatar-uploader"
                                                ref="uploadRef"
                                                :class="{
                        hide: formData.newPictureList && formData.newPictureList.length >= 1,
                      }"
                                                :on-success="handleSuccess"
                                                :limit="1"
                                                :auto-upload="false"
                                                :show-file-list="false"
                                                :on-change="
                        (file) => {
                          uploadImage(file, _i, 'newPictureList');
                        }
                      "
                                                :on-preview="handlePictureCardPreview"
                                                list-type="picture-card"
                                                action="#"
                                        >
                                            <img
                                                    v-if="formData.newPictureList[_i].url"
                                                    @click="previewImage(formData.newPictureList[_i].url)"
                                                    :src="formData.newPictureList[_i].url"
                                                    class="avatar"
                                                    style="width: 100%; height: 100%"
                                                    alt=""/>
                                            <el-icon v-else>
                                                <Plus/>
                                            </el-icon>
                                        </el-upload>
                                        <div style="font-size: 24rpx; text-align: center">
                                            {{ _v.pictureRemark }}
                                        </div>
                                    </div>
                                </el-form-item>
                                <el-form-item label="换车原因">
                                    <el-input :disabled="formData.applyStatus !== 4 && formData.applyStatus !== 7" clearable v-model="formData.applyReason" :rows="2" type="textarea"
                                              placeholder="请填写换车原因"/>
                                </el-form-item> -->
                <el-form-item label="驳回原因" v-if="formData.applyStatus !== 4 && formData.applyStatus !== 7">
                  <el-input v-model="formData.refuseReason" :rows="2" clearable type="textarea" placeholder="请填写驳回原因" />
                </el-form-item>
                <el-form-item label="驳回原因历史">
                  <div>
                    <div v-for="(item, index) in formData.refuseReasonList" :key="index">
                      {{ index + 1 }}、 日期：{{ item.createTime }}&nbsp;&nbsp; 原因：{{ item.refuseReason ? item.refuseReason : '无' }}
                    </div>
                  </div>
                </el-form-item>
              </el-card>
            </el-col>
          </el-row>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <!-- 如果form存在并且formUri有值则使用数据返回的按钮，否则使用默认按钮 -->
          <div v-if="btnMenu.meta?.form?.btns">
            <div v-for="(item, index) in btnMenu.meta?.form?.btns" :key="index" class="inline-block">
              <el-button
                class="mr-2"
                :color="item.background"
                v-loading="submitLoading"
                @click="item.label === '取消' ? closeDialog() : handleSubmit(formRef, item)"
                v-if="
                  !item.dependsOn ||
                  (item.dependsOn &&
                    item.dependsOn.every((depend) => {
                      let target = ''
                      if (depend.dependFrom === 'listData') {
                        target = selectTableColumn[0][depend.field!]
                      } else if (depend.dependFrom === 'responseData') {
                        target = formData[depend.field!]
                      }
                      if (!depend.operator) {
                        depend.operator = 'eq'
                      }
                      const when = depend.when!
                      return operatorCalculate(target, when, depend.operator)
                    }))
                "
              >
                {{ item.label }}
              </el-button>
            </div>
          </div>
          <div v-else>
            <el-button type="primary" @click="handleSubmit(formRef)">确定</el-button>
            <el-button @click="closeDialog()">取 消</el-button>
          </div>
        </div>
      </template>
      <map-search-dialog
        v-if="mapDialog.visible"
        :dialog="mapDialog"
        :pointData="mapData"
        @closeMapDialog="closeMapDialog"
        @submitLocation="submitLocation"
      ></map-search-dialog>
      <el-dialog :draggable="true" v-model="dialogFormVisible.visible" title="指定车辆" append-to-body width="500px">
        <el-form ref="formRef" :model="formData">
          <el-form-item label="指定车辆" prop="vehicleId">
            <el-select
              v-model="currentAssignVehicle.vehicleId"
              value-key="vehicleId"
              placeholder="请选择车辆"
              filterable
              clearable
              remote
              reserve-keyword
              :remote-method="remoteVehicleMethod"
              :loading="vehicleListLoading"
              @change="selectAssignApplyVehicle"
            >
              <el-option v-for="i in carriedVehicleList" :key="i.vehicleId" :label="i.vehicleNo" :value="i.vehicleId!" />
            </el-select>
          </el-form-item>
          <div v-if="currentAssignVehicleVinList.length > 0">
            <div>当前车辆下绑定的vin:</div>
            <div v-for="(item, index) in currentAssignVehicleVinList" :key="index">
              {{ item.vin }}
            </div>
          </div>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogFormVisible.visible = false">取消</el-button>
            <el-button type="primary" @click="selectAssignVehicle">提交</el-button>
          </span>
        </template>
      </el-dialog>
    </el-dialog>
    <!-- 图片查看弹窗 -->
    <pic-dialog-component ref="picDialogRef" :imageList="dialogImageUrl"></pic-dialog-component>
  </div>
</template>
<script setup lang="ts">
  // @ts-nocheck
  import MapSearchDialog from '@/components/MapSearchDialog/index.vue'
  import { DriverVO, VehicleVO, WarehouseVO } from '@/types/global'
  import { ExchangeVehicleParamsBodyVo, ExchangeVehicleParamsVo, VinInfoVO } from '@/api/planManagement/type'
  import PicDialogComponent from '@/components/PicDialogComponent/index.vue'
  import { carrierDriversApi, carrierVehiclesApi, carrierWarehouseApi, getPictureConfig, globalUpload, vehicleVinList } from '@/api/planManagement/index'
  import { genFileId } from 'element-plus'
  import { getBase64 } from '@/utils'
  import { operatorCalculate } from '@/utils/common'

  const emit = defineEmits([
    'closeDialog', //关闭弹窗
    'filpBoardResult', // 倒板结果
    'clearFormColumn', //清除表单
  ])
  const formRef = ref()
  const props = defineProps({
    /**
     * 弹窗属性
     */
    dialogVisible: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
    /**
     * 表单按钮组
     */
    btnMenu: {
      require: true,
      type: Object as PropType<MenuVO>,
      default: () => {
        return {}
      },
    },
  })
  const uploadRef = ref<UploadInstance>()
  const mapData = ref({})
  //车辆列表加载loading
  const vehicleListLoading = ref<boolean>(false)
  //司机列表加载loading
  const driverListLoading = ref<boolean>(false)
  //仓库列表加载loading
  const warehouseListLoading = ref<boolean>(false)
  //承运商车辆列表
  const carriedVehicleList = ref<VehicleVO[]>([])
  //承运商司机列表
  const carriedDriverList = ref<DriverVO[]>([])
  //承运商仓库列表
  const carriedWarehouseList = ref<WarehouseVO[]>([])
  // 当前选中需要分配的车辆
  const currentAssignVehicle = ref<VehicleVO>({})
  // 需要分配的车辆下绑定的 vin
  const currentAssignVehicleVinList = ref<VinInfoVO[]>([])
  const picConfig = reactive({
    newConfig: [],
    oldConfig: [],
  })
  const oldPictureList = ref([])
  const newPictureList = ref([])
  //预览图地址
  const dialogImageUrl = ref<string[]>()
  const picDialogRef = ref()

  const formData = ref<ExchangeVehicleParamsVo>({
    body: [
      {
        addressType: 1, //位置类型typeOption，提交时删掉
        // idList: [],
        vehicleId: '',
        driverId: '',
        warehouseId: '',
        warehousePlace: '',
        coordinate: null, //经纬度
      },
    ],
    operationType: 2, //操作类型1-倒板 2-换车
    source: 0, //申请来源：0-调度 1-司机
    shipmentNo: '', //shipmentNo运单号
    origin: {
      vehicleId: null,
      driverId: null,
      idList: [],
    },
    oldPictureList: [], //申请车辆图片
    newPictureList: [], //替换车辆图片
    applyReason: '', //换车原因
  })
  watch(
    () => props.dialogVisible.visible,
    (visible) => {
      if (visible) {
        remoteVehicleMethod('')
        remoteDriverMethod('')
        remoteWarehouseMethod('')
        getPictureConfigFun()
      }
    },
    {
      deep: true,
    },
  )
  const mapDialog = reactive<DialogOption>({
    visible: false,
  })
  const closeDialog1 = () => {
    formData.refuseReason = ''
  }

  /**
   * 选择位置
   * @param index 选中项下标
   */
  const loctionAdress = async (index: number) => {
    currentDataIndex.value = index
    mapDialog.visible = true //开启弹窗
    mapDialog.title = '选择位置'
    mapData.value = {
      address: formData.value.body![index].warehousePlace,
      coordinate: formData.value.body![index].coordinate,
    }
  }

  // 关闭子弹窗
  function closeMapDialog() {
    mapDialog.visible = false //关闭弹窗
  }

  // 子弹窗确认
  const submitLocation = (data: any) => {
    if (data) {
      formData.value.body![0].warehousePlace = data.address
      formData.value.body![0].coordinate = data.coordinate
      closeMapDialog()
    }
  }

  /**
   * 获取板车上所有vin
   */
  const getVehicleVinList = (vehicleId: string) => {
    return new Promise((resolve) => {
      vehicleVinList({ vehicleId }).then((res) => {
        resolve(res.data)
      })
    })
  }

  /**
   * 选择指定车辆，并查询所绑定的vin
   */
  const selectAssignApplyVehicle = async (vehicleId: string) => {
    let vinList = [] as VinInfoVO[]
    vinList = (await getVehicleVinList(vehicleId)) as VinInfoVO[]
    if (vinList.length > 0) {
      currentAssignVehicleVinList.value = vinList
    } else {
      currentAssignVehicleVinList.value = []
    }
  }
  /**
   * 数据
   */
  const data = ref<any>()
  /**
   * 提交加载动画
   */
  const submitLoading = ref(false)
  /**
   * 选择 车辆弹窗属性
   */
  const dialogFormVisible = reactive<DialogOption>({
    visible: false,
  })
  /**
   * 选中的vin
   */
  const selectVinsArr = ref<VinInfoVO[]>([])
  /**
   * 当前选中data数据中的某项下标
   */
  const currentDataIndex = ref<number>(0)
  /**
   * 提交
   */
  const handleSubmit = (formEl: { validate: any }, btnRequest?: BtnRequestVO) => {
    // formData.value.origin.driverId = formData.value.body[0]?.driverId;
    // formData.value.origin.vehicleId = formData.value.body[0]?.vehicleId;
    const params = JSON.parse(JSON.stringify(formData.value))
    params.body?.forEach((item: ExchangeVehicleParamsBodyVo) => {
      delete item.addressType
    })
    emit('handleSubmit', params, btnRequest)
  }
  /**
   * 关闭弹窗
   */
  const closeDialog = () => {
    // formData.value = {
    //   body: [
    //     {
    //       addressType: 1,
    //       idList: [],
    //     },
    //   ],
    //   operationType: 1, //操作类型1-倒板 2-换车
    //   source: 0, //申请来源：0-调度 1-司机
    //   origin: {
    //     vehicleId: null,
    //     driverId: null,
    //   },
    // };
    //清除表单
    emit('clearFormColumn')
    resetForm()

    emit('closeDialog')
  }
  //远程获取承运商车辆列表
  const remoteVehicleMethod = (query: string) => {
    // if (query) {
    vehicleListLoading.value = true
    carrierVehiclesApi({ vehicleNo: query }).then((res) => {
      carriedVehicleList.value = res.data as VehicleVO[]
    })
    // } else {
    //   // carriedVehicleList.value = [];
    // }
    vehicleListLoading.value = false
  }
  //远程获取承运商仓库列表
  const remoteDriverMethod = (query: string) => {
    // if (query) {
    driverListLoading.value = true
    carrierDriversApi({ name: query }).then((res) => {
      carriedDriverList.value = res.data as DriverVO[]
    })
    // } else {
    //   // carriedDriverList.value = [];
    // }
    driverListLoading.value = false
  }
  //远程获取承运商仓库列表
  const remoteWarehouseMethod = (query: string) => {
    // if (query) {
    warehouseListLoading.value = true
    carrierWarehouseApi({ name: query }).then((res) => {
      carriedWarehouseList.value = res.data as WarehouseVO[]
    })
    // } else {
    //   // carriedWarehouseList.value = [];
    // }
    warehouseListLoading.value = false
  }

  /**
   * 列表配置项
   */
  const tableConfig = reactive<TableConfig>({
    showHandleSelection: true,
    showSort: false,
    tableItem: [
      {
        name: 'vin',
        label: 'VIN',
        align: 'left',
        type: 'text',
        listEnable: true,
      },
    ],
  })

  //选择分配的车辆
  function selectAssignVehicle() {
    // 循环formData.value.body  判断item.vehicleId=currentAssignVehicle.value 是否存在
    const existArr: any = formData.value.body?.filter((item) => item.vehicleId === currentAssignVehicle.value.vehicleId)
    // 删除之前包含 selectVinsArr.value 的数据
    formData.value.body![currentDataIndex.value].vinItemList = formData.value.body![currentDataIndex.value].vinItemList!.filter(
      (item: any) => !selectVinsArr.value.includes(item),
    )
    formData.value.body![currentDataIndex.value].idList = formData.value.body![currentDataIndex.value].vinItemList!.map((item: any) => item.id)
    if (existArr?.length > 0) {
      // ElMessage.error("当前车辆已存在");
      formData.value.body!.forEach((item) => {
        if (item.vehicleId === currentAssignVehicle.value.vehicleId) {
          item.vinItemList = item.vinItemList!.concat(selectVinsArr.value)
          item.idList = item.vinItemList!.map((item: any) => item.id)
        }
      })
      dialogFormVisible.visible = false
    } else {
      formData.value.body!.push({
        addressType: 1,
        vehicleId: currentAssignVehicle.value.vehicleId,
        vinItemList: currentAssignVehicleVinList.value.concat(selectVinsArr.value),
      })
    }

    dialogFormVisible.visible = false
    //清空选中的 vin
    selectVinsArr.value = []
  }

  //获取图片上报配置
  const getPictureConfigFun = () => {
    getPictureConfig().then((res) => {
      const { data } = res
      picConfig.newConfig = data.newConfig
      picConfig.oldConfig = data.oldConfig
      formData.value.newPictureList = formData.value.newPictureList ?? []
      formData.value.oldPictureList = formData.value.oldPictureList ?? []
      if (formData.value.newPictureList.length <= 0) {
        data.newConfig.forEach((item) => {
          formData.value.newPictureList.push({
            id: item.id,
            remark: item.pictureRemark,
            ranks: item.ranks,
            url: '',
          })
          newPictureList.value.push([])
        })
      }
      if (formData.value.oldPictureList.length <= 0) {
        data.oldConfig.forEach((item, index) => {
          formData.value.oldPictureList.push({
            id: item.id,
            remark: item.pictureRemark,
            ranks: item.ranks,
            url: '',
          })
          oldPictureList.value.push([])
        })
      }
    })
  }
  /**
   * 图片预览
   */
  const previewImage = (url) => {
    dialogImageUrl.value = [url]
    picDialogRef.value.picDialogVisible = true
  }
  //上传图片
  const uploadImage = (file: any, index: number, keyName: string) => {
    getBase64(file.raw).then((res) => {
      if (file.status !== 'ready') return
      const params = {
        uploadData: res,
        businessLine: 204,
      }
      const tempArr = formData.value[keyName] ?? []

      globalUpload(params).then((res) => {
        formData.value[keyName][index].url = res.data
      })
    })
  }

  const handleSuccess = (response, file) => {
    // 上传成功以后
    uploadRef.value.clearFiles() // 先删除选择的文件
    nextTick(() => {
      // file里面的raw就是File类型，直接复制给组件
      uploadRef.value.handleStart(file.raw) // 重新赋值原来的文件
    })
  }
  /**
   * 表单重置
   */
  const resetForm = () => {
    formRef.value?.resetFields()
    formRef.value?.clearValidate()
    formData.value.id = undefined
    formData.value.enable = true
  }
  defineExpose({ data, formData, resetForm })
</script>
<style lang="scss" scoped>
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .box-card {
    margin-bottom: 20px;
  }

  .avatar-uploader .el-upload {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    transition: var(--el-transition-duration-fast);
  }

  .avatar-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
  }

  .el-icon.avatar-uploader-icon {
    width: 178px;
    height: 178px;
    font-size: 28px;
    color: #8c939d;
    text-align: center;
  }
</style>
