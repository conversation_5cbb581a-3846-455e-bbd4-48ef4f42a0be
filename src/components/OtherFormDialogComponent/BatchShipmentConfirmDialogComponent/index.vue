<!--
 * @Author: llm
 * @Date: 2024-01-15 15:57:17
 * @LastEditors: llm
 * @LastEditTime: 2024-02-20 11:56:31
 * @Description: 运单确认-批量录入
-->
<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="60%" :draggable="true" :close-on-click-modal="false">
    <el-scrollbar max-height="60vh" class="formClass">
      <el-form ref="ruleFormRef" label-width="120px" :inline="true" :model="state.batchShipmentInfo" style="width: 93%; margin: 0 auto">
        <el-row>
          <el-col :span="24">
            <el-form-item label="客户" style="width: 100%">
              <el-input v-model="state.batchShipmentInfo!.customerName" disabled style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="线路" style="width: 100%">
              <el-input v-model="state.batchShipmentInfo!.lineName" disabled style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="承运商" style="width: 100%">
              <el-input v-model="state.batchShipmentInfo!.carrierName" disabled style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="车辆" style="width: 100%">
              <el-input v-model="state.batchShipmentInfo!.vehicleNo" disabled style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="期望装车日期" style="width: 100%">
              <el-input v-model="state.batchShipmentInfo!.expectDateTime" disabled style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <div v-for="(item, index) in state.batchShipmentInfo!.baseWareItems" :key="index" style="margin-bottom: 10px">
          <el-form-item label="仓库">
            <el-input v-model="item.baseWareName" disabled style="width: 220px" />
          </el-form-item>
          <el-form-item label="车道" :prop="`baseWareItems.${index}.laneId`" :rules="[{ required: false, message: '请选择预约车道', trigger: 'change' }]">
            <el-select filterable v-model="item.laneId" style="width: 220px" placeholder="请选择车道" @change="handleLaneChange(item, index, $event)">
              <el-option v-for="(_item, _index) in item.lanes || []" :key="_index" :label="_item.laneName" :value="_item.laneId" />
            </el-select>
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item
                label="预约日期"
                :prop="`baseWareItems.${index}.confirmDate`"
                :rules="[{ required: true, message: '请选择装车日期', trigger: 'blur' }]"
              >
                <el-date-picker
                  v-model="item.confirmDate"
                  type="date"
                  placeholder="请选择装车日期"
                  style="width: 220px"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  @change="handleDateChange(item, index, $event)"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="预约时间段"
                :prop="`baseWareItems.${index}.confirmTime`"
                :rules="[{ required: true, message: '请选择装车时间段', trigger: 'blur' }]"
              >
                <el-time-picker
                  v-model="item.confirmTime"
                  is-range
                  format="HH:mm"
                  value-format="HH:mm"
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  clearable
                  style="width: 220px"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <div style="width: 100%">
            <TableComponent :tableData="item.orders" :tableConfig="tableConfig" />
          </div>
        </div>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel(ruleFormRef)">取消</el-button>
        <el-button type="primary" @click="confirm(ruleFormRef)">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
  import { appointmentIdsApi, appointmentSubmitApi, shipmentOrderConfirmSubmitApi } from '@/api/shipmentManagement'
  import {
    AppointmentsVO,
    BatchShipmentConfirmVO,
    ConfirmFormDataVO,
    StateVO,
    VehiclesVO,
    appointmentIdsRequest,
    baseWareItemsVO,
    highWayBatchShipmentConfirmVO,
    lanesVO,
    shipmentOrderConfirmSubmitItemsVO,
    shipmentOrderConfirmSubmitVO,
    vehicleWaresVO,
  } from '@/api/shipmentManagement/type'
  import { FormInstance } from 'element-plus'
  import { PropType } from 'vue'
  const emit = defineEmits(['closeDialog', 'shipmentOrderConfirmSubmit'])
  const ruleFormRef = ref<FormInstance>()
  const props = defineProps({
    /**
     * 行信息
     */
    tableRow: {
      type: Object,
      default: () => {
        return {}
      },
    },
    /**
     * 弹窗属性
     */
    dialog: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
  })
  const tableConfig = {
    tableItem: [
      {
        name: 'vin',
        label: '车架号',
        listEnable: true,
      },
    ],
  }
  const state = reactive<BatchShipmentConfirmVO>({
    formData: {
      items: [],
    },
    batchShipmentInfo: {},
  })
  /**
   * 当前选中的车道
   */
  const currentLanes = ref<lanesVO[]>([])
  /**
   * 选中的车辆
   */
  const currentVehicle = ref<VehiclesVO>()
  /**
   * 选中的车辆绑定的倒板仓
   */
  const currentVehicleWares = ref<vehicleWaresVO[]>()
  /**
   * @description: 选择预约时间段
   * @param {*} item 当前项
   * @param {*} index 当前索引
   * @param {*} event 值
   * @return {*}
   */
  function handleDateChange(item: appointmentIdsRequest, index: number, event: string): void {
    const params = {
      appointmentDate: item.appointmentDate,
      laneId: item.laneId,
    }
    appointmentIdsApi(params).then((res) => {
      const { data } = res
      if (currentLanes.value.length > 0) {
        if (data.length > 0) {
          data.forEach((v: any) => {
            currentLanes.value![0].appointments.map((item) => {
              if (v === item.appointmentId) {
                item.appointmentStatus = 1
              } else {
                item.appointmentStatus = 0
              }
            })
          })
        } else {
          currentLanes.value![0].appointments.map((item) => {
            item.appointmentStatus = 0
          })
        }
      }
    })
  }
  /**
   *
   */
  /**
   * @description: 选择预约车道
   * @param {*} item 当前项
   * @param {*} index 当前索引
   * @param {*} event 值
   * @return {*}
   */
  function handleLaneChange(item: appointmentIdsRequest, index: number, event: any): void {
    const lane = state.batchShipmentInfo!.baseWareItems![index].lanes.find((lane: lanesVO) => {
      return event.includes(lane.laneId)
    })
    state.batchShipmentInfo!.baseWareItems![index].laneName = lane!.laneName
  }
  /**
   *
   * @param formEl 关闭弹窗
   */
  function cancel(formEl: FormInstance | undefined) {
    emit('closeDialog')
  }
  // 提交表单
  const confirm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        const items = state.batchShipmentInfo?.baseWareItems!.map((item) => {
          return {
            confirmDate: item.confirmDate,
            confirmTime: item.confirmTime!.join('~'),
            laneId: item.laneId,
            laneName: item.laneName,
            orderIds: item.orders.map((orderId) => orderId.id),
          }
        })
        const params = {
          items,
        } as shipmentOrderConfirmSubmitVO
        emit('shipmentOrderConfirmSubmit', params)
      } else {
      }
    })
  }
  defineExpose({
    state,
    // handleLaneChange,
  })
</script>
<style scoped lang="scss">
  .lane-date {
    display: inline-block;
    width: 100px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border: 1px solid #ebeef5;
    background-color: #ffffff;
    cursor: pointer;
    border-radius: 4px;
    &.active {
      background-color: #1890ff;
      color: #ffffff;
    }
    &.disabled {
      background-color: #e3e3e3;
      //禁止点击
      cursor: not-allowed;
    }
  }
  .time-card {
    border-bottom: none;
    :deep(.el-card__body) {
      padding-bottom: 0;
    }
  }
</style>
