<!--
 * @Author: llm
 * @Date: 2024-09-24 16:36:04
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-03-11 11:52:19
 * @Description: 方案对比弹窗
-->
<template>
  <div>
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" width="80%" :draggable="true" :close-on-click-modal="false" @close="closeDialog">
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form ref="formRef" :model="formData" :inline="true" v-if="state.type === 'bottomSpanBtn'" style="margin: 0 4px">
          <el-form-item label="月份">
            <el-input v-model="formData.month" disabled style="width: 200px" />
          </el-form-item>
          <el-form-item label="方案版本" prop="ids">
            <el-select
              v-model="formData.ids"
              style="width: 200px"
              multiple
              collapse-tags
              collapse-tags-tooltip
              value-key="id"
              clearable
              placeholder="请选择方案版本"
              filterable
            >
              <el-option v-for="item in schemeListData" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="schemeComparison">开始对比</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="state.tableData" style="width: 100%" row-key="id" border ref="dragTable">
          <!-- <el-table-column type="selection" width="55"></el-table-column> -->
          <template v-if="state.type === 'topBtn'">
            <el-table-column prop="name" label="维度"></el-table-column>
            <el-table-column prop="totalNum" label="运输业预估总量(辆)"></el-table-column>
            <el-table-column prop="totalAmount" label="上家成本总金额(元)"></el-table-column>
            <el-table-column prop="otd" label="总OTD时长(天)"></el-table-column>
            <el-table-column prop="vehicleCost" label="总单车成本(元)"></el-table-column>
          </template>
          <template v-if="state.type === 'bottomBtn'">
            <el-table-column prop="month" label="月份"></el-table-column>
            <el-table-column prop="num" label="运输业务量(辆)"></el-table-column>
            <el-table-column prop="amount" label="上家成本金额(元)"></el-table-column>
            <el-table-column prop="otd" label="OTD时长(天)"></el-table-column>
            <el-table-column prop="vehicleCost" label="单车成本(元)"></el-table-column>
          </template>
          <template v-if="state.type === 'bottomSpanBtn'">
            <el-table-column prop="name" label="方案版本"></el-table-column>
            <el-table-column prop="totalNum" label="运输业务量(辆)"></el-table-column>
            <el-table-column prop="totalAmount" label="上家成本金额(元)"></el-table-column>
            <el-table-column prop="otd" label="OTD时长(天)"></el-table-column>
            <el-table-column prop="vehicleCost" label="单车成本(元)"></el-table-column>
          </template>
        </el-table>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog()">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { taskCrossCompare } from '@/api/transportPlan'
  const state = reactive({
    tableData: [],
    type: '',
  })
  const formData = ref<any>({})
  const newTaskId = ref()
  const schemeListData = ref<any>([]) //跨方案列表
  /**
   * 查看列表项详情弹窗
   */
  const dialogVisible = reactive<DialogOption>({
    visible: false,
    title: '月份对比(跨方案)',
  })

  // 方案对比
  const schemeComparison = () => {
    if (formData.value.ids.length > 0) {
      if (newTaskId.value) {
        let params = {
          taskId: newTaskId.value,
          month: formData.value.month,
          dataIdList: formData.value.ids,
        }
        taskCrossCompare(params)
          .then((res: any) => {
            if (res.code === 200 && res.data) {
              state.tableData = res.data
            } else {
              ElMessage.error(res.msg)
              state.tableData = []
            }
          })
          .catch(() => {})
      }
    } else {
      ElMessage.warning('请选择要对比的方案！')
      return
    }
  }

  /**
   * 关闭弹窗
   */
  function closeDialog() {
    dialogVisible.visible = false
  }

  defineExpose({
    dialogVisible,
    state,
    formData,
    schemeListData,
    newTaskId,
  })
</script>
<style lang="scss" scoped></style>
