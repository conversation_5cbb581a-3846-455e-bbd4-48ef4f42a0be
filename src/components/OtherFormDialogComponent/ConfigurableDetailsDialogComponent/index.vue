<!--
 * @Author: llm
 * @Date: 2024-03-19 16:18:41
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-07-30 18:40:20
 * @Description: 可配板弹窗
-->
<template>
  <el-dialog v-model="configurableDialogVisible" draggable title="可配板策略" width="500" :before-close="handleCloseMergeShipmentNoVisible">
    <el-scrollbar max-height="50vh">
      <div
        class="itemContent flex justify-between items-center"
        v-for="(item, index) in canList"
        :key="index"
        :style="{ background: index % 2 === 0 ? '#f0f0f0' : '#fff' }"
      >
        <div class="left flex justify-center items-center">
          <div class="vehicleType">{{ item.vehicleModel1 }}</div>
          <div class="textNumber">
            <span class="leftText b-color">{{ item.vehicleModel1Count }}</span>
            <span class="character c-color">*</span>
            <span class="rightText c-color">{{ item.count }}</span>
          </div>
        </div>
        <div class="right flex justify-center items-center" v-if="item.vehicleModel2 && item.vehicleModel2Count">
          <div class="vehicleType">{{ item.vehicleModel2 }}</div>
          <div class="textNumber">
            <span class="leftText b-color">{{ item.vehicleModel2Count }}</span>
            <span class="character c-color">*</span>
            <span class="rightText c-color">{{ item.count }}</span>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <el-button @click="handleCloseMergeShipmentNoVisible">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
  // 'closeDialog'
  const emit = defineEmits(['resetQuery'])
  const configurableDialogVisible = ref(false)
  const canList = ref<any>([])

  const handleCloseMergeShipmentNoVisible = () => {
    configurableDialogVisible.value = false
  }

  defineExpose({
    configurableDialogVisible,
    canList,
  })
</script>

<style scoped>
  .itemContent {
    /* border-bottom: 1px solid #e6e6e6; */
    margin-bottom: 10px;
  }

  .itemContent .left,
  .itemContent .right {
    padding: 4px 10px;
    width: 50%;
  }
  .itemContent .right {
    border-left: 1px solid #e6e6e6;
  }

  .itemContent .vehicleType {
    font-size: 18px;
    padding-right: 20px;
  }

  .itemContent .textNumber {
    font-size: 18px;
    text-align: center;
    position: relative;
  }
  .itemContent .character {
    font-size: 24px;
    position: absolute;
    top: 0px;
  }

  .leftText {
    padding-right: 2px;
  }

  .rightText {
    padding-left: 10px;
  }

  .b-color {
    color: #009ad6;
  }

  .c-color {
    color: #6950a1;
  }
</style>
