<template>
  <div>
    <div class="flex items-start justify-between">
      <div class="w-100% flex items-center justify-between">
        <div class="title">路线信息</div>
        <div class="flex items-center content">
          <div v-if="props.dispatchInfo.editShow || props.isEmptyDispatch">
            <el-button type="primary" plain icon="Edit" size="small" @click="editLineInfo" :disabled="isjinyong || disable">编辑线路</el-button>
            <el-divider direction="vertical" />
          </div>
          <el-text>总里程：{{ props.dispatchInfo.mileage || 0 }}km</el-text>
          <el-divider direction="vertical" />
          <el-text>重驶：{{ props.dispatchInfo.mileageFull || 0 }}km</el-text>
          <el-divider direction="vertical" />
          <el-text>空驶：{{ props.dispatchInfo.mileageEmpty || 0 }}km</el-text>
        </div>
      </div>
    </div>
    <el-scrollbar>
      <div class="min-h-100px position-relative">
        <div class="steps" v-if="props.dispatchLineData.length > 0">
          <div v-for="(item, index) in props.dispatchLineData" :key="index" class="flex items-center">
            <div class="flex flex-col items-center min-w-160px address">
              <div class="circle">
                <img src="@/assets/images/duigou.png" alt="" style="width: 15px" v-if="item.arrived" />
                <div v-else class="text-18px font-bold color-#326CE6">{{ Number(index) + 1 }}</div>
              </div>
              <el-text class="text-center min-w-60px">{{ item.area }}</el-text>
              <el-text class="text-center min-w-60px" v-if="item.startEnterTime">入：{{ item.startEnterTime || '-' }}</el-text>
              <el-text class="text-center min-w-60px" v-if="item.endEnterTime">出：{{ item.endEnterTime || '-' }}</el-text>
            </div>
            <div class="lines" v-if="item.mileage !== undefined">
              <div class="line" v-if="item.arrived" :class="item.dispatchType === '空载' ? 'bg-blue-6' : 'bg-red'"></div>
              <div class="line bg-gray" v-else></div>

              <div v-if="item.dispatchType === '重载'">
                <img src="@/assets/images/red_car.png" v-if="item.arrived" alt="" class="car" />
                <img src="@/assets/images/red_grey_car.png" v-else alt="" class="car" />
              </div>
              <div v-else>
                <img src="@/assets/images/blue_car.png" v-if="item.arrived" alt="" class="car" />
                <img src="@/assets/images/blue_grey_car.png" v-else alt="" class="car" />
              </div>
              <el-text class="distance">{{ item.mileage }}km</el-text>
            </div>
          </div>
        </div>
        <div v-else class="steps justify-center text-16px">暂无路线信息</div>
      </div>
    </el-scrollbar>
    <div class="flex justify-end" v-if="!isjinyong">
      <el-button
        v-if="props.dispatchInfo.dispatchStatus === '运输中' || props.dispatchInfo.dispatchStatus === '已完成'"
        type="primary"
        icon="Document"
        size="small"
        link
        @click="handleReportRecord"
        >点位上报记录</el-button
      >
    </div>
    <el-dialog
      :draggable="true"
      v-model="state.reportRecordDialogVisible.visible"
      :title="state.reportRecordDialogVisible.title"
      :fullscreen="isFullscreen"
      align-center
      destroy-on-close
      :width="state.reportRecordDialogVisible.dialogWidth ?? '80%'"
    >
      <template #header>
        <slot name="header">
          <div class="el-dialog__title" @dblclick="setFullscreen">{{ state.reportRecordDialogVisible.title }}</div>
        </slot>
        <button class="el-dialog__headerbtn" style="right: 30px" type="button" @click="setFullscreen">
          <el-icon color="#909399"><FullScreen /></el-icon>
        </button>
      </template>
      <el-scrollbar class="formClass" max-height="90vh">
        <BasePage1 />
      </el-scrollbar>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { getcurrentUserMenuColumnlist } from '@/utils/common'
  import BasePage1 from '@/views/Pages/basePage1.vue'
  import { useSideBarStore } from '@/store/modules/sideBar'
  import defaultSettings from '@/settings'
  const sideBarStore = useSideBarStore()
  const emit = defineEmits(['editLineInfo', 'showMenuDialog'])
  const props = defineProps({
    /**
     * 是否为空调度单，空调度单恒定展示线路
     */
    isEmptyDispatch: {
      type: Boolean,
      default: false,
    },
    dispatchInfo: {
      type: Object,
      default: () => {},
    },
    dispatchLineData: {
      type: Array as () => DispatchLineItem[],
      default: () => [] as DispatchLineItem[],
    },
    isjinyong: {
      type: Boolean,
      default: false,
    },
    disable: {
      type: Boolean,
      default: false,
    },
  })
  const isFullscreen = ref<any>(true)
  const state = reactive({
    reportRecordDialogVisible: {
      visible: false,
      title: '点位上报记录',
      dialogWidth: '80%',
    },
  })
  const editLineInfo = () => {
    emit('editLineInfo')
  }
  interface DispatchLineItem {
    area: string
    arrived: boolean
    mileage?: number
    dispatchType: string
    startEnterTime?: string
    endEnterTime?: string
    vinCount?: number
  }
  const handleReportRecord = async () => {
    if (props.disable) return
    const menu = {
      meta: {
        form: {
          title: '点位上报记录',
          menuId: '99030200004001',
          storeData: [
            {
              targetName: 'dispatchNo',
              value: props.dispatchInfo.dispatchNo,
            },
          ],
        },
      },
    }
    emit('showMenuDialog', menu)
  }
  const setFullscreen = () => {
    isFullscreen.value = !isFullscreen.value
  }
</script>
<style scoped lang="scss">
  .circle {
    width: 26px;
    height: 26px;
    border-radius: 50%;
    border: 2px solid #326ce6;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .title {
    font-size: 16px;
    font-weight: bold;
  }

  .content {
    font-size: 16px;
  }

  .address {
    position: relative;
    top: 10px;
  }

  .steps {
    display: flex;
    align-items: center;
    //横向滚动
    // overflow-x: scroll;
    min-height: 90px;
    // &::-webkit-scrollbar {
    //   display: none;
    // }

    .lines {
      margin: 0 5px;
      min-width: 120px;
      height: 40px;
      position: relative;

      .line {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        top: 50%;
        width: 100%;
        height: 2px;
      }

      .car {
        position: absolute;
        //居中
        left: 50%;
        transform: translateX(-50%);
        top: -3px;
        width: 68px;
        height: 24px;
      }

      .distance {
        position: absolute;
        //居中
        left: 50%;
        transform: translateX(-50%);
        top: 35px;
      }
    }
  }
</style>
