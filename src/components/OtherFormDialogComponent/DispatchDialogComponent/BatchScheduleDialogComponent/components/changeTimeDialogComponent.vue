<!--
 * @Author: llm
 * @Date: 2025-02-08 10:02:48
 * @LastEditors: llm
 * @LastEditTime: 2025-02-08 10:51:58
 * @Description: 变更时间弹窗
-->
<template>
  <!-- 编辑时间弹窗 -->
  <el-dialog
    :title="`${state.addressType === 'start' ? '变更装车时间' : '变更交车时间'}`"
    v-model="state.updateTimeDialog.visible"
    width="600px"
    :draggable="true"
    @close="closeUpdateTimeDialog"
  >
    <el-form ref="updateTimeFormRef" :model="state.updateTimeFormData" label-width="120px">
      <el-form-item
        :label="`${state.addressType === 'start' ? '装车时间' : '交车时间'}`"
        prop="time"
        :rules="[{ required: true, message: '请选择时间', trigger: 'change' }]"
      >
        <el-date-picker v-model="state.updateTimeFormData.time" type="datetime" placeholder="请选择时间" value-format="YYYY-MM-DD HH:mm:ss" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeUpdateTimeDialog">取消</el-button>
        <el-button type="primary" @click="confirmUpdateTimeDialog(updateTimeFormRef)">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { postOutFleetOrderDispatchAlterActualDropTimeApi, postOutFleetOrderDispatchAlterLoadTimeApi } from '@/api/businessManagement'
  import { getNowDate } from '@/utils'
  const emit = defineEmits(['resetQuery'])
  const state = reactive({
    addressType: '',
    updateTimeDialog: {
      visible: false,
    },
    updateTimeFormData: {
      time: '',
    },
    ids: [],
  })
  watch(
    () => state.updateTimeDialog.visible,
    (newVal) => {
      if (newVal) {
        state.updateTimeFormData.time = getNowDate()
      }
    },
  )
  const updateTimeFormRef = ref()
  const closeUpdateTimeDialog = () => {
    state.updateTimeDialog.visible = false
  }
  const confirmUpdateTimeDialog = (formRef: any) => {
    formRef.validate(async (valid: boolean) => {
      if (valid) {
        let params = {}
        if (state.addressType === 'start') {
          params = {
            ids: state.ids,
            loadTime: state.updateTimeFormData.time,
          }
          try {
            const { data } = await postOutFleetOrderDispatchAlterLoadTimeApi(params)
            ElMessage.success('操作成功')
          } catch (error) {
            console.log(error)
            return
          }
        } else {
          params = {
            ids: state.ids,
            actualDropTime: state.updateTimeFormData.time,
          }
          try {
            const { data } = await postOutFleetOrderDispatchAlterActualDropTimeApi(params)
            ElMessage.success('操作成功')
          } catch (error) {
            console.log(error)
            return
          }
        }
        //重置表单
        updateTimeFormRef.value.resetFields()
        closeUpdateTimeDialog()
        emit('resetQuery')
      }
    })
  }
  defineExpose({
    state,
  })
</script>
