<!--
 * @Author: llm
 * @Date: 2025-06-16 10:05:38
 * @LastEditors: llm
 * @LastEditTime: 2025-06-20 16:26:07
 * @Description: 
-->
<template>
  <el-dialog v-model="state.loadDetailDialogVisible" title="装载明细" width="80%" draggable>
    <SingleTable :table-data="state.loadDetailData" :table-config="state.tableConfig" height="300px" />
  </el-dialog>
</template>
<script setup lang="ts">
  import SingleTable from '@/components/VxeTableComponent/singleTable.vue'
  const state = reactive({
    loadDetailDialogVisible: false,
    loadDetailData: [],
    tableConfig: {
      showHandleSelection: false,
      tableItem: [
        {
          name: 'address',
          label: '地点',
        },
        {
          name: 'loadCount',
          label: '装载数',
        },
      ],
    } as TableConfig,
  })
  defineExpose({ state })
</script>
<style scoped lang="scss"></style>
