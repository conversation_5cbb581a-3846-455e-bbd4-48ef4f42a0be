<!--
 * @Author: llm
 * @Date: 2025-06-16 10:05:38
 * @LastEditors: llm
 * @LastEditTime: 2025-06-16 16:08:55
 * @Description: 
-->
<template>
  <el-dialog v-model="state.oilFeeDetailDialogVisible" title="油费详情" width="80%" draggable>
    <SingleTable :table-data="state.oilFeeDetailData" :table-config="state.tableConfig" height="300px" :show-footer="true" :footer-data="footerData" />
  </el-dialog>
</template>
<script setup lang="ts">
  import SingleTable from '@/components/VxeTableComponent/singleTable.vue'
  import { VxeTablePropTypes } from 'vxe-table'
  const state = reactive({
    oilFeeDetailDialogVisible: false,
    oilFeeDetailData: [],
    tableConfig: {
      showHandleSelection: false,
      tableItem: [
        {
          name: 'startName',
          label: '起点',
        },
        {
          name: 'endName',
          label: '终点',
        },
        {
          name: 'isFullName',
          label: '是否重驶',
        },
        {
          name: 'mileage',
          label: '里程',
        },
        {
          name: 'oilFeeDesc',
          label: '预估油费',
          width: 500,
        },
      ],
    } as TableConfig,
  })
  const footerData = ref<VxeTablePropTypes.FooterData>([])
  defineExpose({ state, footerData })
</script>
<style scoped lang="scss"></style>
