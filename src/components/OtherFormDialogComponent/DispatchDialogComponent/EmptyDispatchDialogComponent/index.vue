<!--
 * @Author: llm
 * @Date: 2024-12-17 11:28:45
 * @LastEditors: llm
 * @LastEditTime: 2025-06-26 14:12:51
 * @Description:
-->
<template>
  <div>
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="60%" :draggable="true" @close="closeDialog">
      <el-card>
        <LineInfoComponent
          :isEmptyDispatch="true"
          :dispatchLineData="state.dispatchLineData"
          :dispatchInfo="state.dispatchInfo"
          ref="lineInfoComponentRef"
          @editLineInfo="editLineInfo"
        ></LineInfoComponent>
      </el-card>
      <el-card class="mt-10px">
        <template #header="">
          <div class="my-header">
            <div class="title">整车信息</div>
            <el-button type="primary" size="small" @click="selectVehicle">选择车辆</el-button>
          </div>
        </template>
        <el-form ref="form" :model="state" label-width="80px">
          <vxe-table :data="state.currentChooseVehicles" max-height="400px" size="small" border class="mt-10px" style="width: 100%">
            <vxe-column type="seq" title="序号" width="60" align="center"></vxe-column>
            <vxe-column field="carrierName" title="承运商" width="160" align="center"></vxe-column>
            <vxe-column field="carrierType" title="承运商类型" width="160" align="center"></vxe-column>
            <vxe-column field="vehicleNo" title="车牌号" width="160" align="center"></vxe-column>
            <vxe-column field="driverOneName" title="司机" width="160" align="center"></vxe-column>
            <vxe-column field="driverOneMobile" title="司机电话" width="160" align="center"></vxe-column>
            <vxe-column field="dispatchCreateTime" title="调度单时间" width="240" align="center">
              <template #header>
                <div class="flex items-center">
                  <span class="w-80px">调度单时间</span>
                  <el-date-picker
                    v-model="state.dispatchCreateTime"
                    @change="handleCreateTimeChange"
                    type="datetime"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择调度单时间"
                    style="width: 140px"
                  ></el-date-picker>
                </div>
              </template>
              <template #default="{ row }">
                <el-date-picker
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  v-model="row.dispatchCreateTime"
                  type="datetime"
                  placeholder="请选择调度单时间"
                  style="width: 100%"
                ></el-date-picker>
              </template>
            </vxe-column>
            <vxe-column field="dispatchType" title="调度单类型" width="240" align="center">
              <template #header>
                <div class="flex items-center">
                  <el-form-item label="调度单类型" required label-width="90px" prop="dispatchType">
                    <el-select @change="handleDispatchTypeChange" v-model="state.dispatchType" placeholder="请选择" size="small" style="width: 120px">
                      <el-option v-for="item in state.dispatchOrderTypeOptions" :key="item.value" :label="item.label" :value="item.label"></el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </template>
              <template #default="{ row }">
                <el-select v-model="row.dispatchType" placeholder="请选择调度单类型">
                  <el-option v-for="item in state.dispatchOrderTypeOptions" :key="item.value" :label="item.label" :value="item.label"></el-option>
                </el-select>
              </template>
            </vxe-column>
            <vxe-column field="tripNumber" title="趟数" width="160" align="center">
              <template #header>
                <div class="flex items-center">
                  <el-form-item label="趟数" label-width="60px" required prop="tripNumber">
                    <el-input-number
                      @change="handleTripNumberChange"
                      controls-position="right"
                      :min="1"
                      :max="1000000"
                      v-model="state.tripNumber"
                      :step="1"
                      style="width: 80px"
                    ></el-input-number>
                  </el-form-item>
                </div>
              </template>
              <template #default="{ row }">
                <el-input-number controls-position="right" :min="1" :max="1000000" v-model="row.tripNumber" :step="1" style="width: 100%"></el-input-number>
              </template>
            </vxe-column>
            <vxe-column field="check" title="是否校验" width="100" align="center">
              <template #header>
                <div class="flex items-center">
                  <span class="w-60px">是否校验</span>
                  <el-checkbox @change="handleCheckAllChange" v-model="state.checkAll"></el-checkbox>
                </div>
              </template>
              <template #default="{ row }">
                <el-checkbox v-model="row.check"></el-checkbox>
              </template>
            </vxe-column>
            <vxe-column title="操作" width="100" align="center" fixed="right">
              <template #default="{ row }">
                <el-text class="cursor-pointer" type="danger" @click="handleDeleteVehicle(row)">删除</el-text>
              </template>
            </vxe-column>
          </vxe-table>
        </el-form>
      </el-card>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelVehicleInfo">取消</el-button>
          <el-button type="primary" @click="confirmVehicleInfo">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 整车信息 -->
    <VehicleInfoDialogComponent ref="vehicleInfoDialogRef" @chooseVehicleConfirm="chooseVehicleConfirm" />
    <!-- 线路调整弹窗 -->
    <LineAdjustmentDialogComponent
      ref="lineAdjustmentDialogComponentRef"
      @editLineInfo="editLineInfo"
      :dispatchLineData="state.dispatchLineData"
      @showScheduleDetail="showScheduleDetail"
    />
  </div>
</template>
<script setup lang="ts">
  import { getDispatchOrderTypeApi, getFleetOrderDispatchLineDetailApi, postFleetOrderDispatchCreateEmptyApi } from '@/api/businessManagement'
  import LineInfoComponent from '../BatchScheduleDialogComponent/components/LineInfoComponent.vue'
  import LineAdjustmentDialogComponent from '../LineAdjustmentDialogComponent/index.vue'
  import VehicleInfoDialogComponent from '../VehicleInfoDialogComponent/index.vue'
  import { VxeTable, VxeColumn } from 'vxe-table'
  const lineAdjustmentDialogComponentRef = ref()
  const emit = defineEmits(['editLineInfo', 'confirmDispatchSuccess'])
  const props = defineProps({
    dialog: {
      type: Object as PropType<DialogOption>,
      default: () => ({
        visible: false,
        title: '创建空调度单',
      }),
    },
  })
  watch(
    () => props.dialog.visible,
    (val) => {
      getDispatchOrderTypeOptions()
    },
  )
  const vehicleInfoDialogRef = ref()
  const lineInfoComponentRef = ref()
  const state = reactive({
    dispatchLineData: [],
    dispatchInfo: {} as any,
    vehicleInfoDialogVisible: false,
    currentChooseVehicles: [] as any[],
    dispatchOrderTypeOptions: [] as SelectOptions[],
    dispatchId: '',
    dispatchType: '正常',
    tripNumber: 1,
    checkAll: false,
    dispatchCreateTime: '',
  })
  const cancelVehicleInfo = () => {
    emit('confirmDispatchSuccess')
  }
  watch(
    () => state.currentChooseVehicles,
    (val) => {
      // 只对新添加的车辆设置默认值，保留已有车辆的修改
      state.currentChooseVehicles.forEach((item: any) => {
        if (!item.dispatchType) {
          item.dispatchType = state.dispatchType
        }
        if (!item.tripNumber) {
          item.tripNumber = state.tripNumber
        }
        if (!item.check) {
          item.check = state.checkAll
        }
        if (!item.dispatchCreateTime) {
          item.dispatchCreateTime = state.dispatchCreateTime
        }
      })
    },
  )
  const confirmVehicleInfo = async () => {
    const carrierList = state.currentChooseVehicles.map((item: any) => {
      return {
        carrierId: item.carrierId,
        carrierName: item.carrierName,
        vehicleNo: item.vehicleNo,
        vehicleWholeId: item.vehicleWholeId,
        carrierType: item.carrierType,
        tripNumber: item.tripNumber,
        dispatchType: item.dispatchType,
        check: item.check,
        dispatchCreateTime: item.dispatchCreateTime,
      }
    })
    const params = {
      mileage: state.dispatchInfo.mileage,
      mileageFull: state.dispatchInfo.mileageFull,
      mileageEmpty: state.dispatchInfo.mileageEmpty,
      carrierList,
      dispatchId: state.dispatchId,
    }
    await postFleetOrderDispatchCreateEmptyApi(params)
    ElMessage.success('创建成功')
    // state.vehicleInfoDialogVisible = false
    vehicleInfoDialogRef.value.state.vehicleInfoDialogVisible = false
    emit('confirmDispatchSuccess')
    //清除表单数据
    state.dispatchInfo = {}
    state.currentChooseVehicles = []
    state.dispatchId = ''
    state.dispatchLineData = []
  }
  //获取调度单类型下拉
  const getDispatchOrderTypeOptions = async () => {
    const { data } = await getDispatchOrderTypeApi({})
    state.dispatchOrderTypeOptions = data
  }
  const selectVehicle = () => {
    // state.vehicleInfoDialogVisible = true;
    vehicleInfoDialogRef.value.state.vehicleInfoDialogVisible = true
    vehicleInfoDialogRef.value.getFleetVehicleDispatchList(vehicleInfoDialogRef.value.state.vehicleQueryParams)
  }
  const chooseVehicleConfirm = (newVehicleTableData: any[], originalVehicleTableData: any[]) => {
    // 创建一个Set用于快速查找已存在的车辆ID
    const existingVehicleIds = new Set(state.currentChooseVehicles.map((vehicle) => vehicle.vehicleWholeId))

    // 过滤出不存在的新车辆
    const newVehicles = originalVehicleTableData.filter((vehicle) => !existingVehicleIds.has(vehicle.vehicleWholeId))

    // 将新车辆添加到现有列表中
    state.currentChooseVehicles = [...state.currentChooseVehicles, ...newVehicles]

    vehicleInfoDialogRef.value.state.vehicleInfoDialogVisible = false
  }

  const editLineInfo = () => {
    lineAdjustmentDialogComponentRef.value.state.isEmptyDispatch = true
    lineAdjustmentDialogComponentRef.value.state.lineAdjustmentDialogVisible = true
    if (state.dispatchLineData.length > 0) {
      lineAdjustmentDialogComponentRef.value.editLineInfo(state.dispatchLineData)
    }
  }
  const showScheduleDetail = async (dispatchId:string) => {
    state.dispatchId = dispatchId
    getFleetOrderDispatchLineDetailApi({ dispatchId }).then((res: any) => {
      const { data } = res
      const transformedData = transformData(data.lines)
      state.dispatchLineData = JSON.parse(JSON.stringify(transformedData))
      state.dispatchInfo = data
    })

    // getDispatchOrderTypeOptions();
  }
  function transformData(
    data: Array<{
      dispatchType: string
      endName: string
      mileage: number
      startName: string
      startFenceId: string
      endFenceId: string
      id: string
      startArrived: string
      endArrived: string
    }>,
  ) {
    const result: Array<{
      area: string
      dispatchType: string
      mileage?: number | string
      showLine: boolean
      startFenceId: string
      id: string
      arrived: string
    }> = []

    data.forEach((item, index) => {
      // 添加当前数据的起始区域
      result.push({
        id: item.id,
        area: item.startName,
        arrived: item.startArrived,
        startFenceId: item.startFenceId,
        dispatchType: item.dispatchType,
        mileage: item.mileage,
        showLine: true,
      })

      // 如果是最后一项，添加终点区域
      if (index === data.length - 1) {
        result.push({
          id: item.id,
          area: item.endName,
          dispatchType: item.dispatchType,
          showLine: false,
          startFenceId: item.endFenceId,
          arrived: item.endArrived,
        })
      }
    })
    return result
  }
  const handleDispatchTypeChange = (value: string) => {
    state.dispatchType = value
    // 遍历当前车辆信息，将调度单类型设置为当前选中的调度单类型
    state.currentChooseVehicles.forEach((item: any) => {
      item.dispatchType = value
    })
  }
  const handleCreateTimeChange = (value: string) => {
    state.dispatchCreateTime = value
    // 遍历当前车辆信息，将调度单时间设置为当前选中的调度单时间
    state.currentChooseVehicles.forEach((item: any) => {
      item.dispatchCreateTime = value
    })
  }
  const handleTripNumberChange = (cur: number | undefined, prev: number | undefined) => {
    if (cur !== undefined) {
      state.tripNumber = cur
      // 遍历当前车辆信息，将趟数设置为当前选中的趟数
      state.currentChooseVehicles.forEach((item: any) => {
        item.tripNumber = cur
      })
    }
  }
  const handleCheckAllChange = (value: boolean | string | number) => {
    state.checkAll = value as boolean
    // 遍历当前车辆信息，将是否校验设置为当前选中的是否校验
    state.currentChooseVehicles.forEach((item: any) => {
      item.check = value as boolean
    })
  }
  const closeDialog = () => {
    state.dispatchLineData = []
    state.dispatchInfo = {}
    state.currentChooseVehicles = []
    state.dispatchType = '正常'
    state.tripNumber = 1
    state.checkAll = false
    state.dispatchCreateTime = ''
    lineAdjustmentDialogComponentRef.value.state.currentDispatchId = ''
  }
  const handleDeleteVehicle = (row: any) => {
    state.currentChooseVehicles = state.currentChooseVehicles.filter((item: any) => item.vehicleWholeId !== row.vehicleWholeId)
  }
</script>

<style scoped lang="scss">
  .title {
    font-size: 18px;
    font-weight: bold;
  }

  .my-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 16px;
  }
  :deep(.el-form-item--small.el-form-item) {
    margin-bottom: 0px;
  }
</style>
