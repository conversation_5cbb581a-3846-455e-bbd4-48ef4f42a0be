<!--
 * @Author: llm
 * @Date: 2024-12-17 16:13:16
 * @LastEditors: llm
 * @LastEditTime: 2025-07-14 10:14:40
 * @Description:
-->
<template>
  <el-dialog title="线路调整" v-model="state.lineAdjustmentDialogVisible" width="600px" :close-on-click-modal="false" :draggable="true" @closed="closeDialog">
    <el-scrollbar max-height="60vh">
      <div class="flex">
        <div>
          <div v-for="(item, index) in state.lineAdjustmentLeftForm" :key="index" class="flex items-center mb-30px">
            <div class="line-num">{{ Number(index + 1) }}</div>
            <el-select
              v-model="state.lineAdjustmentLeftForm[index].startFenceId"
              style="width: 200px"
              class="ml-10px"
              remote-show-suffix
              clearable
              :multiple="false"
              filterable
              remote
              reserve-keyword
              placeholder="模糊搜索"
              @change="(e: any) => selectFence(e, item, index)"
              :remote-method="(query: string) => remoteSelectMethod(query, index, 'startFenceId')"
            >
              <el-option v-for="i in item.options || []" :key="i.value" :label="i.label" :value="i.value" />
            </el-select>
            <div class="ml-10px">
              <el-button type="primary" size="small" icon="Plus" circle @click="addLineAdjustment(index)"></el-button>
              <el-button
                type="danger"
                size="small"
                icon="Minus"
                circle
                @click="removeLineAdjustment(index)"
                v-if="state.lineAdjustmentLeftForm.length > 2"
              ></el-button>
            </div>
          </div>
        </div>
        <div class="mt-16px ml-20px">
          <div v-for="(item, index) in state.lineAdjustmentRightForm" :key="index" class="mb-2px">
            <div class="flex items-center">
              <div class="border-content mr-12px">
                <div class="border-line"></div>
              </div>
              <div class="flex items-center mr-10px cursor" @click="switchType(item)">
                <span class="color-red font-bold" v-if="item.dispatchType === '重载'">重</span>
                <span class="color-green-8 font-bold" v-else>空</span>
                <img src="@/assets/images/qiehuan.png" alt="" class="w-18px" />
              </div>
              <div class="w-130px flex items-center">
                <el-input v-model="item.mileage" placeholder="" class="mr-6px"></el-input>
                <span>km</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>

    <template #footer>
      <span class="dialog-footer">
        <el-button v-if="!state.isEmptyDispatch" type="primary" @click="replanRoute">重新路线规划</el-button>
        <el-button @click="lineAdjustmentCancel">取 消</el-button>
        <el-button type="primary" :disabled="submitLoading" @click="lineAdjustmentConfirm">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { getGeofenceListApi, postDispatchLineApi, postFleetRouteDispatchMileageApi, postDispatchLineReGenerateApi } from '@/api/businessManagement'
  const emit = defineEmits(['showScheduleDetail'])
  const props = defineProps({
    currentDispatchId: {
      type: String,
      default: '',
    },
    currentDispatchNo: {
      type: String,
      default: '',
    },
    // dispatchLineData: {
    //   type: Array,
    //   default: () => [],
    // },
    dispatchInfo: {
      type: Object,
      default: () => {},
    },
  })
  const submitLoading = ref(false)
  interface LineAdjustmentLeftFormVO {
    endFenceId: string
    sortNo?: number
    startFenceId: string
    options: SelectOptions[]
  }
  interface LineAdjustmentRightFormVO {
    dispatchType: string
    mileage: string
    highwayFee: string
    id: string
  }
  const state = reactive({
    isEmptyDispatch: false, //区分是否为空调度单，空调度单不展示重新规划路线按钮
    dispatchLineData: [],
    currentDispatchId: '',
    lineAdjustmentLeftForm: [{}, {}] as LineAdjustmentLeftFormVO[],
    lineAdjustmentRightForm: [
      {
        dispatchType: '重载',
        mileage: '',
      },
    ] as LineAdjustmentRightFormVO[],
    addressFuzzySearchParams: {
      label: 'name',
      dataSource: '围栏',
      value: 'id',
      fuzzy: true,
      keyword: '',
    },
    lineAdjustmentDialogVisible: false,
    status: 'add', //add:新增调度单  edit:编辑调度单
  })
  const closeDialog = () => {
    state.lineAdjustmentDialogVisible = false
    resetForm()
  }
  //远程模糊搜索下拉
  const remoteSelectMethod = async (query: string, index: number, key: string) => {
    if (query) {
      state.addressFuzzySearchParams.keyword = query
      const { data } = await getGeofenceListApi(state.addressFuzzySearchParams)
      state.lineAdjustmentLeftForm[index].options = data
    }
  }
  const selectFence = async (e: any, item: LineAdjustmentLeftFormVO, index: number) => {
    //如果选择的当前项的前一项存在并且前一项的startFenceId存在
    if (state.lineAdjustmentLeftForm[index - 1] && state.lineAdjustmentLeftForm[index - 1].startFenceId) {
      const endPoint = {
        coordinate: '',
        fenceId: item.startFenceId,
        name: item.options.find((fence: any) => fence.value === item.startFenceId)!.label,
      }
      const startPoint = {
        coordinate: '',
        fenceId: state.lineAdjustmentLeftForm[index - 1].startFenceId,
        name: state.lineAdjustmentLeftForm[index - 1].options.find((fence: any) => fence.value === state.lineAdjustmentLeftForm[index - 1].startFenceId)!.label,
      }
      const params = {
        endPoint,
        startPoint,
      }
      if (state.lineAdjustmentRightForm[index - 1]) {
        submitLoading.value = true
        state.lineAdjustmentRightForm[index - 1].mileage = '正在计算中...'
      }
      postFleetRouteDispatchMileageApi(params)
        .then((res: any) => {
          if (state.lineAdjustmentRightForm[index - 1]) {
            state.lineAdjustmentRightForm[index - 1].mileage = res.data.distanceKm
            state.lineAdjustmentRightForm[index - 1].highwayFee = res.data.highwayFee
          }
        })
        .finally(() => {
          submitLoading.value = false
        })
    }
    //如果选择的当前项的后一项存在并且后一项的startFenceId存在
    if (state.lineAdjustmentLeftForm[index + 1] && state.lineAdjustmentLeftForm[index + 1].startFenceId) {
      const endPoint = {
        coordinate: '',
        fenceId: state.lineAdjustmentLeftForm[index + 1].startFenceId,
        name: state.lineAdjustmentLeftForm[index + 1].options.find((fence: any) => fence.value === state.lineAdjustmentLeftForm[index + 1].startFenceId)!.label,
      }
      const startPoint = {
        coordinate: '',
        fenceId: item.startFenceId,
        name: item.options.find((fence: any) => fence.value === item.startFenceId)!.label,
      }
      const params = {
        endPoint,
        startPoint,
      }
      state.lineAdjustmentRightForm[index].mileage = '正在计算中...'
      submitLoading.value = true
      postFleetRouteDispatchMileageApi(params)
        .then((res: any) => {
          if (state.lineAdjustmentRightForm[index]) {
            state.lineAdjustmentRightForm[index].mileage = res.data.distanceKm
            state.lineAdjustmentRightForm[index].highwayFee = res.data.highwayFee
          }
        })
        .finally(() => {
          submitLoading.value = false
        })
    }
  }
  const addLineAdjustment = (index: number) => {
    state.lineAdjustmentLeftForm.splice(index + 1, 0, {
      endFenceId: '',
      startFenceId: '',
      options: [],
    })
    state.lineAdjustmentRightForm.splice(index, 0, {
      dispatchType: '重载',
      mileage: '',
      highwayFee: '',
      id: '',
    })
  }
  const removeLineAdjustment = (index: number) => {
    state.lineAdjustmentLeftForm.splice(index, 1)
    if (index === state.lineAdjustmentRightForm.length) {
      state.lineAdjustmentRightForm.splice(index - 1, 1)
    } else {
      state.lineAdjustmentRightForm.splice(index, 1)
    }
    //如果删除的是最后一项
    if (index !== state.lineAdjustmentLeftForm.length) {
      //如果删除的项上一项存在，且上一项的startFenceId存在
      if (state.lineAdjustmentLeftForm[index - 1] && state.lineAdjustmentLeftForm[index - 1].startFenceId) {
        const endPoint = {
          coordinate: '',
          fenceId: state.lineAdjustmentLeftForm[index - 1].startFenceId,
          name: state.lineAdjustmentLeftForm[index - 1].options.find((fence: any) => fence.value === state.lineAdjustmentLeftForm[index - 1].startFenceId)!
            .label,
        }
        const startPoint = {
          coordinate: '',
          fenceId: state.lineAdjustmentLeftForm[index].startFenceId,
          name: state.lineAdjustmentLeftForm[index].options?.find((fence: any) => fence.value === state.lineAdjustmentLeftForm[index].startFenceId)!.label,
        }
        const params = {
          endPoint,
          startPoint,
        }
        if (!params.startPoint.fenceId || !params.endPoint.fenceId) {
          state.lineAdjustmentRightForm[index - 1].mileage = ''
        } else {
          state.lineAdjustmentRightForm[index - 1].mileage = '正在计算中...'
          submitLoading.value = true
          postFleetRouteDispatchMileageApi(params)
            .then((res: any) => {
              state.lineAdjustmentRightForm[index - 1].mileage = res.data.distanceKm
              state.lineAdjustmentRightForm[index - 1].highwayFee = res.data.highwayFee
            })
            .finally(() => {
              submitLoading.value = false
            })
        }
      }
      //如果删除的项下一项存在，且下一项的startFenceId存在
      if (state.lineAdjustmentLeftForm[index + 1] && state.lineAdjustmentLeftForm[index + 1].startFenceId) {
        const endPoint = {
          coordinate: '',
          fenceId: state.lineAdjustmentLeftForm[index + 1].startFenceId,
          name: state.lineAdjustmentLeftForm[index + 1].options.find((fence: any) => fence.value === state.lineAdjustmentLeftForm[index + 1].startFenceId)!
            .label,
        }
        const startPoint = {
          coordinate: '',
          fenceId: state.lineAdjustmentLeftForm[index].startFenceId,
          name: state.lineAdjustmentLeftForm[index].options.find((fence: any) => fence.value === state.lineAdjustmentLeftForm[index].startFenceId)!.label,
        }
        const params = {
          endPoint,
          startPoint,
        }
        if (!params.startPoint.fenceId || !params.endPoint.fenceId) {
          state.lineAdjustmentRightForm[index].mileage = ''
        } else {
          state.lineAdjustmentRightForm[index].mileage = '正在计算中...'
          submitLoading.value = true
          postFleetRouteDispatchMileageApi(params)
            .then((res: any) => {
              state.lineAdjustmentRightForm[index].mileage = res.data.distanceKm
              state.lineAdjustmentRightForm[index].highwayFee = res.data.highwayFee
            })
            .finally(() => {
              submitLoading.value = false
            })
        }
      }
    }
  }
  const switchType = (item: LineAdjustmentRightFormVO) => {
    item.dispatchType = item.dispatchType === '重载' ? '空载' : '重载'
  }
  const lineAdjustmentCancel = () => {
    state.lineAdjustmentDialogVisible = false
  }
  const lineAdjustmentConfirm = async () => {
    //校验
    if (!state.lineAdjustmentRightForm.every((item) => item.mileage)) {
      ElMessage.error('请填写里程')
      return
    }
    let lines = state.lineAdjustmentRightForm.map((item: LineAdjustmentRightFormVO, index: number) => ({
      id: item.id,
      startFenceId: state.lineAdjustmentLeftForm[index].startFenceId,
      mileage: item.mileage,
      highwayFee: item.highwayFee,
      endFenceId: state.lineAdjustmentLeftForm[index + 1]?.startFenceId, // 使用可选链操作符防止数组越界
      dispatchType: item.dispatchType,
    }))
    const params = {
      dispatchId: props.currentDispatchId ? props.currentDispatchId : state.currentDispatchId,
      highwayFee: props.dispatchInfo?.highwayFee || 0,
      oilQuantity: props.dispatchInfo?.oilQuantity || 0,
      officePrice: props.dispatchInfo?.officePrice || 0,
      driverPrice: props.dispatchInfo?.driverPrice || 0,
      lines,
    }
    const { data } = await postDispatchLineApi(params)
    state.currentDispatchId = data

    ElMessage.success('操作成功')
    state.lineAdjustmentDialogVisible = false

    console.log(props.currentDispatchNo, 'props.currentDispatchNo')

    //清除表单
    resetForm()
    emit('showScheduleDetail', state.currentDispatchId ? state.currentDispatchId : props.currentDispatchId, props.currentDispatchNo, state.status, true)
  }
  const replanRoute = async () => {
    //校验
    if (!state.lineAdjustmentRightForm.every((item) => item.mileage)) {
      ElMessage.error('请填写里程')
      return
    }
    let lines = state.lineAdjustmentRightForm.map((item: LineAdjustmentRightFormVO, index: number) => ({
      id: item.id,
      startFenceId: state.lineAdjustmentLeftForm[index].startFenceId,
      mileage: item.mileage,
      highwayFee: item.highwayFee,
      endFenceId: state.lineAdjustmentLeftForm[index + 1]?.startFenceId, // 使用可选链操作符防止数组越界
      dispatchType: item.dispatchType,
    }))
    console.log(lines, 'lines')

    if (!lines.every((item) => item.startFenceId && item.endFenceId)) {
      ElMessage.error('请起点或终点围栏')
      return
    }

    const params = {
      dispatchId: props.currentDispatchId ? props.currentDispatchId : state.currentDispatchId,
      highwayFee: props.dispatchInfo?.highwayFee || 0,
      oilQuantity: props.dispatchInfo?.oilQuantity || 0,
      officePrice: props.dispatchInfo?.officePrice || 0,
      driverPrice: props.dispatchInfo?.driverPrice || 0,
      lines,
    }
    const { data } = await postDispatchLineReGenerateApi(params)
    ElMessage.success('重新路线规划成功')
    state.dispatchLineData = transformData(data.lines) as any
    console.log('%c [ state.dispatchLineData ] -> ', 'font-size:16px; background:#2afdde; color:#6effff;', state.dispatchLineData)
    editLineInfo(state.dispatchLineData)
    // state.currentDispatchId = data

    // ElMessage.success('操作成功')
    // state.lineAdjustmentDialogVisible = false
  }
  const resetForm = () => {
    //清除表单
    state.lineAdjustmentLeftForm = [
      {
        endFenceId: '',
        startFenceId: '',
        options: [],
      },
      {
        endFenceId: '',
        startFenceId: '',
        options: [],
      },
    ]
    state.lineAdjustmentRightForm = [
      {
        dispatchType: '重载',
        mileage: '',
        highwayFee: '',
        id: '',
      },
    ]
  }
  const editLineInfo = (dispatchLineData: any) => {
    state.dispatchLineData = dispatchLineData
    console.log('%c [ dispatchLineData ] -> ', 'font-size:16px; background:#dfaadb; color:#ffeeff;', dispatchLineData)
    // state.lineAdjustmentDialogVisible = true
    // const dispatchLineData = JSON.parse(JSON.stringify(dispatchLineData))
    state.lineAdjustmentLeftForm = dispatchLineData.map((item: any, index: number) => {
      return {
        startFenceId: item.startFenceId,
      }
    })
    console.log('%c [ state.lineAdjustmentLeftForm ] -> ', 'font-size:16px; background:#65962d; color:#a9da71;', state.lineAdjustmentLeftForm)
    state.lineAdjustmentRightForm = dispatchLineData.filter((item: any, index: number) => {
      if (item.showLine) {
        return {
          id: item.id,
          dispatchType: item.dispatchType,
          mileage: item.mileage,
        }
      }
    })
    dispatchLineData.map((item: any, index: number) => {
      remoteSelectMethod(item.area, index, 'startFenceId')
    })
  }
  function transformData(
    data: Array<{
      dispatchType: string
      endName: string
      mileage: number
      startName: string
      startFenceId: string
      endFenceId: string
      id: string
      startArrived: string
      endArrived: string
      highwayFee: number
      startEnterTime: string
      endEnterTime: string
      startLeaveTime: string
      endLeaveTime: string
    }>,
  ) {
    const result: Array<{
      area: string
      dispatchType: string
      mileage?: number | string
      showLine: boolean
      startFenceId: string
      id: string
      arrived: string
      highwayFee: number
      startEnterTime: string
      endEnterTime: string
      startLeaveTime?: string
      endLeaveTime?: string
    }> = []

    data.forEach((item, index) => {
      // 添加当前数据的起始区域
      result.push({
        id: item.id,
        area: item.startName,
        arrived: item.startArrived,
        startFenceId: item.startFenceId,
        dispatchType: item.dispatchType,
        mileage: item.mileage,
        highwayFee: item.highwayFee,
        showLine: true,
        startEnterTime: item.startEnterTime,
        endEnterTime: item.startLeaveTime,
      })

      // 如果是最后一项，添加终点区域
      if (index === data.length - 1) {
        result.push({
          id: item.id,
          area: item.endName,
          dispatchType: item.dispatchType,
          showLine: false,
          startFenceId: item.endFenceId,
          arrived: item.endArrived,
          highwayFee: item.highwayFee,
          startEnterTime: item.endEnterTime,
          endEnterTime: item.endLeaveTime,
        })
      }
    })
    return result
  }
  defineExpose({
    state,
    editLineInfo,
  })
</script>

<style scoped lang="scss">
  .line-num {
    width: 30px;
    height: 30px;
    border: 4px solid #326ce6;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #326ce6;
    font-size: 20px;
    font-weight: bold;
  }

  .border-content {
    width: 20px;
    overflow: hidden;
    //旋转180
    transform: rotate(180deg);
  }

  .border-line {
    border: 4px solid #326ce6;
    border-radius: 12px;
    width: 40px;
    height: 60px;
  }
</style>
