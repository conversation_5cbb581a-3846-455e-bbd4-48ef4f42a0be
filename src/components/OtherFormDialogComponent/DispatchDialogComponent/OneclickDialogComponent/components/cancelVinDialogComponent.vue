<template>
  <div>
    <el-dialog v-model="state.dialog.visible" :title="state.dialog.title" width="80%" :draggable="true" :close-on-click-modal="false">
      <div class="flex items-start">
        <div style="position: relative; width: 100%">
          <div class="query-container" :style="{ height: state.moreSearch ? 'auto' : '48px' }">
            <div class="query-box">
              <div class="query-container-left">
                <el-form ref="ruleFormRef" :inline="true" :model="state.queryParams" size="small">
                  <el-row :gutter="24">
                    <el-col :span="8">
                      <el-form-item label="VIN码" style="width: 100%" label-width="50px">
                        <el-input
                          v-model="state.queryParams!.vin"
                          style="width: 100%"
                          clearable
                          @click="openTextBatch()"
                          @clear="emptyTextBatch()"
                          placeholder="请输入VIN码，多个用,隔开"
                        />
                        <div v-show="showTextBatch" style="width: 100%; position: absolute; top: 0px; left: 0px; z-index: 9999">
                          <TextareaComponent
                            ref="TextareaRef"
                            @arraySent="handleArrayReceived"
                            :showTextBatch="showTextBatch"
                            :closeTextBatch="closeTextBatch"
                            targetField="请输入VIN码，多个用,隔开"
                          />
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="客户" style="width: 100%" label-width="50px">
                        <el-input v-model="state.queryParams!.customerName" style="width: 100%" clearable placeholder="请输入客户" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="起点" style="width: 100%" label-width="50px">
                        <el-input v-model="state.queryParams!.startArea" style="width: 100%" placeholder="请输入起点" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="24" v-if="state.moreSearch">
                    <el-col :span="8">
                      <el-form-item label="终点" style="width: 100%" label-width="50px">
                        <el-input v-model="state.queryParams!.endArea" style="width: 100%" placeholder="请输入终点" clearable />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="提车点名称" style="width: 100%" label-width="75px">
                        <el-input v-model="state.queryParams!.pickUpPointName" style="width: 100%" clearable placeholder="请输入提车点名称" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="提车点地址" style="width: 100%" label-width="75px">
                        <el-input v-model="state.queryParams!.pickUpPointAddress" style="width: 100%" clearable placeholder="请输入提车点地址" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="24" v-if="state.moreSearch">
                    <el-col :span="8">
                      <el-form-item label="交车点名称" style="width: 100%" label-width="75px">
                        <el-input v-model="state.queryParams!.dropUpPointName" style="width: 100%" clearable placeholder="请输入交车点名称" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="交车点地址" style="width: 100%" label-width="75px">
                        <el-input v-model="state.queryParams!.dropUpPointAddress" style="width: 100%" clearable placeholder="请输入交车点地址" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="线路" style="width: 100%" label-width="50px">
                        <el-input v-model="state.queryParams!.lineName" style="width: 100%" placeholder="请输入线路" clearable />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="24" v-if="state.moreSearch">
                    <el-col :span="8">
                      <el-form-item label="经销商名称" style="width: 100%" label-width="75px">
                        <el-input v-model="state.queryParams!.dealerName" style="width: 100%" clearable placeholder="请输入经销商名称" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="经销商地址" style="width: 100%" label-width="75px">
                        <el-input v-model="state.queryParams!.dealerAddress" style="width: 100%" clearable placeholder="请输入经销商地址" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="计划下达" style="width: 100%" label-width="65px">
                        <el-date-picker
                          v-model="state.queryParams!.orderIssueDatetime"
                          type="daterange"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          style="width: 100%"
                          format="YYYY-MM-DD"
                          value-format="YYYY-MM-DD"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
              <div class="query-container-right" style="width: 180px">
                <div style="display: flex; justify-content: start; align-items: center; padding-top: 1px">
                  <!-- icon="More" -->
                  <el-button size="small" round @click="moreClick">更多</el-button>
                  <el-button size="small" @click="searchConfirm" type="primary">查询</el-button>
                  <el-button size="small" @click="searchReset">重置</el-button>
                </div>
              </div>
            </div>
          </div>
          <div>
            <div class="mb-10px" style="margin-top: 60px"></div>
            <el-table
              size="small"
              max-height="45vh"
              :data="tableData"
              style="width: 100%"
              :border="true"
              @selection-change="handleSelectionChange"
              show-overflow-tooltip
            >
              <el-table-column type="selection" width="55" align="center"></el-table-column>
              <el-table-column
                align="center"
                :prop="item.name"
                :label="item.label"
                :width="item.width"
                v-for="(item, index) in tableConfig.tableItem"
                :key="index"
              ></el-table-column>
            </el-table>
            <pagination
              v-if="state.total >= state.queryParams.limit"
              v-model:limit="state.queryParams.limit"
              v-model:page="state.queryParams.page"
              v-model:total="state.total"
              @pagination="_pagination"
              style="margin-top: 15px"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { getOrderListApi, postOutFleetOrderDispatchJoinApi } from '@/api/businessManagement'
  import TextareaComponent from '@/components/TextcreaComponent/index.vue'
  import defaultSettings from '@/settings'
  import { debounce } from 'lodash'
  const emit = defineEmits(['refresh'])
  const showTextBatch = ref(false)
  const TextareaRef = ref()
  const ruleFormRef = ref()
  const tableData = ref([])
  const tableConfig = {
    tableItem: [
      {
        name: 'customerName',
        label: '客户名称',
        width: '120px',
      },
      {
        name: 'orderCode',
        label: '订单编号',
        width: '180px',
      },
      {
        name: 'vin',
        label: 'VIN',
        width: '120px',
      },
      {
        name: 'orderStatusName',
        label: '订单状态',
        width: '100px',
      },
      {
        name: 'lineName',
        label: '客户线路',
        width: '100px',
      },
      {
        name: 'startArea',
        label: '起点',
        width: '100px',
      },
      {
        name: 'endArea',
        label: '终点',
        width: '100px',
      },
      {
        name: 'pickUpPointName',
        label: '提车点',
        width: '120px',
      },
      {
        name: 'pickUpPointAddress',
        label: '提车地址',
        width: '120px',
      },
      {
        name: 'dropUpPointName',
        label: '交车点',
        width: '120px',
      },
      {
        name: 'dropUpPointAddress',
        label: '交车地址',
        width: '120px',
      },
      {
        name: 'orderIssueDatetime',
        label: '计划下达',
        width: '150px',
      },
      {
        name: 'actualDropTime',
        label: '订单交车',
        width: '150px',
      },
      {
        name: 'dealerName',
        label: '经销商',
        width: '120px',
      },
      {
        name: 'dealerAddress',
        label: '经销商地址',
        width: '120px',
      },
    ],
  }
  const props = defineProps({
    dispatchNo: {
      type: String,
      default: '',
    },
  })
  const state = reactive({
    total: 0,
    dialog: {
      visible: false,
      title: '',
    },
    moreSearch: false,
    queryParams: {
      vin: '',
      startArea: undefined,
      endArea: undefined,
      pickUpPointName: undefined,
      lineName: undefined,
      customerName: undefined,
      pickUpPointAddress: undefined,
      dropUpPointName: undefined,
      dropUpPointAddress: undefined,
      startProvince: undefined,
      endProvince: undefined,
      dealerName: undefined,
      dealerAddress: undefined,
      orderIssueDatetime: undefined,
      startOrderIssueDatetime: undefined,
      endOrderIssueDatetime: undefined,
      actualDropTime: undefined,
      page: 1,
      limit: defaultSettings.globalLimit,
      orderByClause: 'order_status,order_issue_datetime',
    },
    assignVehiclesFormData: {
      orderIds: [],
      dispatchNo: '',
    },
  })
  watch(
    () => state.dialog.visible,
    (newVal) => {
      if (newVal) {
        getOrderDispatchList()
      }
    },
    {
      immediate: true,
    },
  )
  const emptyTextBatch = () => {
    TextareaRef.value.list = []
  }
  //获取订单列表
  const getOrderDispatchList = async () => {
    const params = JSON.parse(JSON.stringify(state.queryParams))
    params.orderIssueDatetime = undefined
    const { data } = await getOrderListApi(params)
    state.total = data.total
    tableData.value = data.rows
  }
  const moreClick = () => {
    state.moreSearch = !state.moreSearch
  }
  const openTextBatch = () => {
    showTextBatch.value = true
  }
  const closeTextBatch = (array: any) => {
    showTextBatch.value = false
  }
  const handleArrayReceived = (array: any) => {
    state.queryParams!.vin = Object.values(array).join(',')
  }
  const searchConfirm = async () => {
    state.queryParams.page = 1
    state.queryParams.startOrderIssueDatetime = state.queryParams.orderIssueDatetime?.[0] ?? undefined
    state.queryParams.endOrderIssueDatetime = state.queryParams.orderIssueDatetime?.[1] ?? undefined
    state.moreSearch = false
    await getOrderDispatchList()
  }
  const searchReset = async () => {
    cancel()
    await getOrderDispatchList()
  }
  const cancel = () => {
    state.queryParams = {
      vin: '',
      startArea: undefined,
      endArea: undefined,
      pickUpPointName: undefined,
      lineName: undefined,
      customerName: undefined,
      pickUpPointAddress: undefined,
      dropUpPointName: undefined,
      dropUpPointAddress: undefined,
      startProvince: undefined,
      endProvince: undefined,
      dealerName: undefined,
      dealerAddress: undefined,
      orderIssueDatetime: undefined,
      startOrderIssueDatetime: undefined,
      endOrderIssueDatetime: undefined,
      actualDropTime: undefined,
      page: 1,
      limit: defaultSettings.globalLimit,
      orderByClause: 'order_status,order_issue_datetime',
    }
    state.moreSearch = false
    state.dialog.visible = false
  }
  const handleSelectionChange = (e: any) => {
    state.assignVehiclesFormData.orderIds = e.map((item: any) => item.id)
  }
  // 提交表单
  const confirm = debounce(async () => {
    state.assignVehiclesFormData.dispatchNo = props.dispatchNo
    console.log(state.assignVehiclesFormData)
    try {
      await postOutFleetOrderDispatchJoinApi(state.assignVehiclesFormData)
      ElMessage.success('添加成功')
      state.dialog.visible = false
      emit('refresh')
    } catch (error) {
      console.log(error)
    }
  }, 300)
  /**
   * 分页
   */
  const _pagination = async () => {
    await getOrderDispatchList()
  }
  defineExpose({
    state,
  })
</script>
<style scoped lang="scss">
  :deep(.el-divider--horizontal) {
    margin: 16px 0 !important;
  }

  .title {
    font-size: 18px;
    font-weight: bold;
  }

  .content {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .my-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 16px;
  }

  .query-container {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    padding: 10px;
    /* width: 100%; */
    z-index: 8;
    background: #fff;
    border: 1px solid #dedfe6;
    border-radius: 4px;
    /* overflow: hidden; */
  }

  .query-box {
    display: flex;
    align-items: start;
    justify-content: space-between;
  }

  .query-container-left {
    flex: 1;
  }

  .query-container-right {
    padding: 0 10px;
  }

  .query-container :deep(.el-form-item) {
    margin-bottom: 10px;
    border: 1px solid #dedfe6;
    border-radius: 2px;
  }

  .query-container :deep(.el-input__wrapper),
  .query-container :deep(.el-select__wrapper),
  .query-container :deep(.el-date-editor.el-input) {
    border-radius: 0px;
    box-shadow: none;
    min-height: 25px;
  }

  .query-container :deep(.el-form-item__label) {
    background: #f0f0f0;
    color: #333;
    height: auto;
    align-items: center;
  }

  .query-box :deep(.el-select__selection) {
    flex-wrap: nowrap !important;
    margin-right: 45px !important;
  }

  .query-box :deep(.el-select__selected-item) {
    flex-wrap: nowrap !important;
  }

  .query-box :deep(.el-select__input) {
    min-width: 40px !important;
    max-width: 40px !important;
  }

  .media-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding: 20px;
  }

  .video-item {
    border: 1px solid #eee;
    border-radius: 4px;
    overflow: hidden;
    transition: all 0.3s;

    &:hover {
      transform: scale(1.02);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }
  /* 视频缩略图样式 */
  .video-thumbnail {
    width: 50px;
    height: 50px;
    background-color: #f5f7fa;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      background-color: #e6f7ff;

      .play-icon {
        transform: scale(1.2);
        color: var(--el-color-primary);
      }
    }

    .play-icon {
      font-size: 24px;
      color: #909399;
      transition: all 0.3s;
    }
  }
</style>
