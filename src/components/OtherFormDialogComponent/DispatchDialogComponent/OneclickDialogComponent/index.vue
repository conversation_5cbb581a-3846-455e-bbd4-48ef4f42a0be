<!--
 * @Author: llm
 * @Date: 2024-01-15 15:57:17
 * @LastEditors: llm
 * @LastEditTime: 2025-07-16 11:31:07
 * @Description: 智能调度-一键换车
-->
<template>
  <div>
    <el-dialog v-model="dialog.visible" :close-on-click-modal="false" :draggable="true" :title="dialog.title" width="80%" @close="searchReset">
      <div class="flex items-start">
        <div class="w-100%" style="position: relative">
          <el-card width="100%">
            <div class="query-container" :style="{ height: 'auto' }">
              <div class="query-box">
                <div class="query-container-left">
                  <el-form ref="ruleFormRef" :inline="true" :model="state.queryParams" size="small">
                    <el-row :gutter="24">
                      <el-col :span="8">
                        <el-form-item label="VIN码" style="width: 100%" label-width="50px">
                          <el-input
                            v-model="state.queryParams!.vin"
                            style="width: 100%"
                            clearable
                            @click="openTextBatch()"
                            @clear="emptyTextBatch()"
                            placeholder="请输入VIN码，多个用,隔开"
                          />
                          <div v-show="showTextBatch" style="width: 100%; position: absolute; top: 0px; left: 0px; z-index: 9999">
                            <TextareaComponent
                              ref="TextareaRef"
                              @arraySent="handleArrayReceived"
                              :showTextBatch="showTextBatch"
                              :closeTextBatch="closeTextBatch"
                              targetField="请输入VIN码，多个用,隔开"
                            />
                          </div>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="客户" style="width: 100%" label-width="50px">
                          <el-input v-model="state.queryParams!.customerName" clearable placeholder="请输入客户" style="width: 100%" />
                        </el-form-item>
                      </el-col>

                      <el-col :span="8">
                        <el-form-item label="调度单号" label-width="75px" style="width: 100%">
                          <el-input v-model="state.queryParams!.dispatchNo" clearable placeholder="请输入调度单号" style="width: 100%" />
                        </el-form-item>
                        <!-- -->
                      </el-col>
                    </el-row>

                    <el-row :gutter="24">
                      <el-col :span="8">
                        <el-form-item label="车牌号" style="width: 100%" label-width="50px">
                          <el-input v-model="state.queryParams!.vehicleNo" clearable placeholder="请输入车牌号，批量用逗号分隔" style="width: 100%" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="起点" label-width="50px" style="width: 100%">
                          <el-input v-model="state.queryParams!.startArea" placeholder="请输入起点" style="width: 100%" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="终点" style="width: 100%" label-width="50px">
                          <el-input v-model="state.queryParams!.endArea" clearable placeholder="请输入终点" style="width: 100%" />
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row :gutter="24">
                      <el-col :span="8">
                        <el-form-item label="线路" style="width: 100%" label-width="50px">
                          <el-input v-model="state.queryParams!.lineName" clearable placeholder="请输入线路" style="width: 100%" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="提车点名称" style="width: 100%" label-width="75px">
                          <el-input v-model="state.queryParams!.pickUpPointName" clearable placeholder="请输入提车点名称" style="width: 100%" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="交车点名称" style="width: 100%" label-width="75px">
                          <el-input v-model="state.queryParams!.dropUpPointName" clearable placeholder="请输入交车点名称" style="width: 100%" />
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row :gutter="24" v-if="moreSearch">
                      <el-col :span="8">
                        <el-form-item label="经销商名称" style="width: 100%" label-width="75px">
                          <el-input v-model="state.queryParams!.dealerName" clearable placeholder="请输入经销商名称" style="width: 100%" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="经销商地址" style="width: 100%" label-width="75px">
                          <el-input v-model="state.queryParams!.dealerAddress" clearable placeholder="请输入经销商地址" style="width: 100%" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="计划下达" style="width: 100%" label-width="65px">
                          <el-date-picker
                            v-model="state.queryParams!.orderIssueDatetime"
                            type="daterange"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            style="width: 100%"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form>
                </div>
                <div class="query-container-right" style="width: 180px">
                  <div style="display: flex; justify-content: start; align-items: center; padding-top: 1px">
                    <!-- icon="More" -->
                    <el-button size="small" round @click="moreClick">更多</el-button>
                    <el-button size="small" @click="searchConfirm" type="primary">查询</el-button>
                    <el-button size="small" @click="searchReset">重置</el-button>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div class="mb-10px" style="margin-top: 130px">
                <el-button :disabled="state.assignVehiclesFormData.orderIds.length === 0" size="small" type="primary" @click="assignVehicles"
                  >更换车辆
                </el-button>
              </div>
              <vxe-single-table
                ref="tableRef"
                :loading="state.tableLoading"
                :table-config="tableConfig"
                :tableData="tableData"
                height="400px"
                @getSelectRowIds="handleSelectionChange"
              />
              <pagination
                v-if="state.total >= state.queryParams.limit"
                v-model:limit="state.queryParams.limit"
                v-model:page="state.queryParams.page"
                v-model:total="state.total"
                @pagination="_pagination"
                style="margin-top: 15px"
              />
            </div>
          </el-card>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialog.visible = false">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 整车信息 -->
    <VehicleInfoDialogComponent
      ref="vehicleInfoDialogRef"
      :assignVehiclesFormData="state.assignVehiclesFormData"
      selectStatus="radio"
      @chooseVehicleConfirm="chooseVehicleConfirm"
    />
    <!-- <VehicleInfoDialogComponent  :Vehiclename="state.assignVehiclesFormData.vehicleNo" selectStatus="radio" ref="vehicleInfoDialogRef" @chooseVehicleConfirm="chooseVehicleConfirm" /> -->
    <!-- 编辑调度 -->
    <el-dialog v-model="state.editScheduleDialogVisible" :fullscreen="true" :close-on-click-modal="false" :draggable="true" @close="editScheduleCancel">
      <template #header="{ close, titleId, titleClass }">
        <div class="my-header">
          <div class="flex items-center">
            <h4 :id="titleId" :class="titleClass">编辑调度(单号:{{ state.currentDispatchNo }})</h4>
            <el-tag v-if="state.dispatchInfo.dispatchStatus === '已撤回'" class="ml-20px" type="info">
              {{ state.dispatchInfo.dispatchStatus }}
            </el-tag>
            <el-tag type="primary" class="ml-20px" v-else>{{ state.dispatchInfo.dispatchStatus }}</el-tag>
          </div>

          <el-select
            @change="statusDispatchOrderTypeChange"
            v-model="state.dispatchInfo.dispatchType"
            placeholder="请选择调度状态"
            style="width: 150px"
            :disabled="state.dispatchInfo.dispatchStatus === '已完成'"
          >
            <el-option v-for="item in state.dispatchOrderTypeOptions" :key="item.value" :label="item.label" :value="item.label"></el-option>
          </el-select>
        </div>
      </template>
      <el-scrollbar max-height="85vh">
        <div>
          <el-card shadow="never" class="mb-10px">
            <div class="flex items-start">
              <div class="flex-[0_0_400px]">
                <div class="title mb-10px">调度信息</div>
                <el-row :gutter="0">
                  <el-col :span="12">
                    <el-text class="content">承运商：{{ state.dispatchInfo.carrierName || '-' }}</el-text>
                  </el-col>
                  <el-col :span="12">
                    <el-text class="content">司机：{{ state.dispatchInfo.driverName || '-' }}</el-text>
                  </el-col>
                  <el-col :span="12">
                    <el-text class="content">车牌号：{{ state.dispatchInfo.vehicleNo || '-' }}</el-text>
                  </el-col>
                  <el-col :span="12">
                    <el-text class="content">联系方式：{{ state.dispatchInfo.driverMobile || '-' }}</el-text>
                  </el-col>
                  <el-col :span="12">
                    <el-text class="content">挂车牌号：{{ state.dispatchInfo.trailerNo || '-' }}</el-text>
                  </el-col>
                  <el-col :span="12">
                    <el-text class="content">承运类型：{{ state.dispatchInfo.carrierType || '-' }}</el-text>
                  </el-col>
                  <el-col :span="24">
                    <el-text class="content">当前位置：{{ state.dispatchInfo.currentAddress || '-' }}</el-text>
                  </el-col>
                </el-row>
              </div>
              <div class="h-100px flex-[0_0_1px] bg-#c1c7d0 m-[0_20px]"></div>
              <div class="w-[calc(100%-450px)]">
                <LineInfoComponent
                  :isEmptyDispatch="false"
                  :disable="state.status === 'view'"
                  :dispatchLineData="state.dispatchLineData"
                  :dispatchInfo="state.dispatchInfo"
                  :isjinyong="state.isDispatch"
                  @editLineInfo="editLineInfo"
                  @showMenuDialog="showMenuDialog"
                />
              </div>
            </div>
          </el-card>
          <!-- 其他信息 -->
          <div class="mt-10px">
            <el-card shadow="never">
              <div class="flex items-center justify-between mb-10px">
                <div class="title">其他信息</div>
                <div class="flex items-center">
                  <el-text>
                    司机油价:
                    <el-text class="mx-1" type="primary">{{ state.dispatchInfo.driverPrice || 0 }}元/L</el-text>
                  </el-text>
                  <el-divider direction="vertical" />
                  <el-text>
                    当日油价:
                    <el-text class="mx-1" type="primary">{{ state.dispatchInfo.officePrice || 0 }}元/L</el-text>
                  </el-text>
                  <el-divider direction="vertical" />
                  <el-text>
                    预估高速费:
                    <el-text class="mx-1" type="primary">{{ state.dispatchInfo.highwayFee || 0 }}元</el-text>
                  </el-text>
                  <el-divider direction="vertical" />
                  <el-text>
                    预估油升数:
                    <el-text class="mx-1" type="primary">{{ state.dispatchInfo.oilQuantity }}L</el-text>
                  </el-text>
                </div>
              </div>
              <el-form ref="form" :disabled="state.isDispatch || state.status === 'view'" :inline="true" :model="state.formData">
                <el-form-item label="运输备注">
                  <el-input v-model="state.dispatchInfo.operationRemark" />
                </el-form-item>
                <el-form-item label="内部备注">
                  <el-input v-model="state.dispatchInfo.dispatchRemark" />
                </el-form-item>
                <el-form-item label="司机预估收入">
                  <div class="flex items-center cursor" @click="editIncome">
                    <div class="flex items-center text-#0E6EEE underline-#0E6EEE underline">
                      {{ state.dispatchInfo.driverEstimate || 0 }}元
                      <div class="ml-4px flex items-center">
                        <el-icon>
                          <Edit />
                        </el-icon>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
          <!-- tab -->
          <el-tabs v-model="activeName" type="card" class="mt-10px" @tab-click="handleClick">
            <el-tab-pane label="明细" name="first"></el-tab-pane>
            <el-tab-pane label="现金借支" name="second" v-if="state.addPermission.cash"></el-tab-pane>
            <el-tab-pane label="油费借支" name="third" v-if="state.addPermission.oil"></el-tab-pane>
            <el-tab-pane label="路桥费借支" name="fourth" v-if="state.addPermission.etc"></el-tab-pane>
          </el-tabs>
          <div v-if="activeName === 'first'">
            <div v-if="state.status === 'edit' && !state.isDispatch">
              <el-button
                @click="batchTransport"
                v-if="state.dispatchInfo.dispatchStatus === '已下达' || state.dispatchInfo.dispatchStatus === '运输中'"
                :disabled="state.currentDispatchOrderIds.length === 0"
              >
                批量装车
              </el-button>
              <el-button
                @click="batchDelivery"
                v-if="state.dispatchInfo.dispatchStatus === '已下达' || state.dispatchInfo.dispatchStatus === '运输中'"
                :disabled="state.currentDispatchOrderIds.length === 0"
              >
                批量交车
              </el-button>
              <el-button
                @click="batchChange"
                v-if="state.dispatchInfo.dispatchStatus === '已下达' || state.dispatchInfo.dispatchStatus === '运输中'"
                :disabled="state.currentDispatchOrderIds.length === 0"
              >
                变更调度单
              </el-button>
              <!-- <el-button type="primary" @click="batchTransfer">批量中转</el-button> -->
              <el-button v-if="state.dispatchInfo.cancelShow" :disabled="state.currentDispatchOrderIds.length === 0" type="danger" @click="batchRemove">
                VIN取消调度
              </el-button>
              <el-button :disabled="state.currentDispatchOrderIds.length === 0" type="primary" @click="updatePoint('start')">变更运输起点 </el-button>
              <el-button :disabled="state.currentDispatchOrderIds.length === 0" type="primary" @click="updatePoint('end')">变更运输终点 </el-button>
              <el-button
                type="primary"
                @click="updateTime('start', null, 2)"
                :disabled="state.currentDispatchOrderIds.length === 0"
                v-if="state.dispatchInfo.modifyLoadTimeBtnShow"
                >变更装车时间
              </el-button>
              <el-button
                type="primary"
                @click="updateTime('end', null, 2)"
                :disabled="state.currentDispatchOrderIds.length === 0"
                v-if="state.dispatchInfo.modifyDropTimeBtnShow"
                >变更交车时间
              </el-button>
              <CustomPrintButtonComponent
                :showPrint="state.scheduleTableData.length > 0"
                printId="99030200000000"
                :requestUri="requestUri"
                :currentRow="state.currentRow"
              />
              <el-button type="success" @click="addVin" v-if="state.dispatchInfo.cancelVinShow">添加VIN</el-button>
            </div>
            <el-table :border="true" :data="state.scheduleTableData" class="mt-10px" @selection-change="handleSelectionDispatchDetailChange">
              <el-table-column type="selection" width="40"></el-table-column>
              <!-- 序号 -->
              <el-table-column label="序号" type="index" width="50"></el-table-column>
              <template v-for="(_v, _i) in tableConfig.scheduleTableItem" :key="_i">
                <!-- 装车图片 -->
                <el-table-column label="装车图片" align="center" width="120" v-if="_v.name === 'loadPicList'">
                  <template #default="{ row }">
                    <div v-if="row.loadPicList && row.loadPicList.length > 0">
                      <template v-if="row.loadPicList.length === 1">
                        <el-image
                          :src="row.loadPicList[0].url"
                          :preview-src-list="[row.loadPicList[0].url]"
                          fit="cover"
                          :preview-teleported="true"
                          style="width: 80px; height: 80px; cursor: pointer"
                        />
                      </template>
                      <template v-else>
                        <el-link type="primary" @click="showMediaDialog(row.loadPicList, 'image')"> 查看({{ row.loadPicList.length }}) </el-link>
                      </template>
                    </div>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <!-- 装车视频 -->
                <el-table-column label="装车视频" align="center" width="120" v-else-if="_v.name === 'loadVideoList'">
                  <template #default="{ row }">
                    <div v-if="row.loadVideoList && row.loadVideoList.length > 0" class="flex items-center justify-center">
                      <template v-if="row.loadVideoList.length === 1">
                        <div class="video-thumbnail" @click="playVideo(row.loadVideoList[0].url)">
                          <el-icon class="play-icon">
                            <VideoPlay />
                          </el-icon>
                        </div>
                      </template>
                      <template v-else>
                        <el-link type="primary" @click="showMediaDialog(row.loadVideoList, 'video')"> 查看({{ row.loadVideoList.length }}) </el-link>
                      </template>
                    </div>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <!-- 交车图片 -->
                <el-table-column label="交车图片" align="center" width="120" v-else-if="_v.name === 'dropPicList'">
                  <template #default="{ row }">
                    <div v-if="row.dropPicList && row.dropPicList.length > 0">
                      <template v-if="row.dropPicList.length === 1">
                        <el-image
                          :src="row.dropPicList[0].url"
                          :preview-src-list="[row.dropPicList[0].url]"
                          fit="cover"
                          :preview-teleported="true"
                          style="width: 80px; height: 80px; cursor: pointer"
                        />
                      </template>
                      <template v-else>
                        <el-link type="primary" @click="showMediaDialog(row.dropPicList, 'image')"> 查看({{ row.dropPicList.length }}) </el-link>
                      </template>
                    </div>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <!-- 交车视频 -->
                <el-table-column label="交车视频" align="center" width="120" v-else-if="_v.name === 'dropVideoList'">
                  <template #default="{ row }">
                    <div v-if="row.dropVideoList && row.dropVideoList.length > 0" class="flex items-center justify-center">
                      <template v-if="row.dropVideoList.length === 1">
                        <div class="video-thumbnail" @click="playVideo(row.dropVideoList[0].url)">
                          <el-icon class="play-icon">
                            <VideoPlay />
                          </el-icon>
                        </div>
                      </template>
                      <template v-else>
                        <el-link type="primary" @click="showMediaDialog(row.dropVideoList, 'video')"> 查看({{ row.dropVideoList.length }}) </el-link>
                      </template>
                    </div>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column :prop="_v.name" :label="_v.label" v-else :width="_v.width ?? 'auto'"></el-table-column>
              </template>

              <el-table-column label="操作" fixed="right" align="center" v-if="state.status === 'edit'" width="120px">
                <template #default="{ row }">
                  <div>
                    <!-- 装车 -->
                    <div>
                      <el-button v-if="row.orderStatusName === '待装车'" link type="primary" @click="transport(row)"> 装车 </el-button>
                    </div>
                    <!-- 替换 -->
                    <div>
                      <el-button v-if="row.orderStatusName === '待装车'" link type="primary" @click="replaceOrder(row)"> 替换 </el-button>
                    </div>
                    <!-- 交车 -->
                    <div>
                      <el-button v-if="row.orderStatusName === '运输中'" link type="primary" @click="delivery(row)"> 交车 </el-button>
                    </div>
                    <!-- 撤回送达 -->
                    <div>
                      <el-button v-if="row.orderStatusName === '已送达' && !row.haveComputed" link type="primary" @click="withdrawDelivery(row)"
                        >撤回送达
                      </el-button>
                    </div>
                    <!-- 查看证明 调度单订单状态为【已送达】&& 调度单订单结算状态为【计算完成】-->
                    <div>
                      <el-button v-if="row.orderStatusName === '已送达' && row.haveComputed" link type="primary" @click="viewProof(row)">查看证明 </el-button>
                    </div>
                    <!-- 变更装车时间 -->
                    <div>
                      <el-button
                        type="primary"
                        @click="updateTime('start', row, 1)"
                        link
                        v-if="(row.orderStatusName === '运输中' || row.orderStatusName === '已送达') && row.modifyLoadTimeBtnShow"
                        >变更装车时间
                      </el-button>
                    </div>
                    <!-- 变更交车时间 -->
                    <div>
                      <el-button v-if="row.orderStatusName === '已送达' && row.modifyDropTimeBtnShow" link type="primary" @click="updateTime('end', row, 1)"
                        >变更交车时间
                      </el-button>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-if="activeName === 'second'">
            <div v-if="state.addPermission.cashAdd">
              <el-button :disabled="state.isDispatch" type="primary" @click="addCashBorrow"> 新增</el-button>
            </div>
            <el-table :data="state.cashBorrowTableData" style="width: 100%" :border="true" class="mt-10px">
              <el-table-column label="序号" type="index" width="50" align="center"></el-table-column>
              <el-table-column
                :label="_v.label"
                :prop="_v.name"
                v-for="(_v, _i) in tableConfig.cashBorrowTableItem"
                :key="_i"
                align="center"
                :width="_v.width ?? 'auto'"
              ></el-table-column>
              <el-table-column label="操作" width="90px" align="center">
                <template #default="{ row, $index }">
                  <div>
                    <el-button
                      @click="handleCashBorrowEdit(row, $index)"
                      type="primary"
                      link
                      v-if="row.auditStatusDesc === '审批驳回' || row.auditStatusDesc === '付款审批驳回' || state.status === 'add'"
                      >编辑
                    </el-button>
                  </div>
                  <div>
                    <el-button v-if="state.status === 'add'" link type="danger" @click="handleCashBorrowDelete(row)"> 删除 </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-if="activeName === 'third'">
            <div v-if="state.addPermission.oilAdd">
              <el-button :disabled="state.isDispatch" type="primary" @click="addOilBorrow(state.oilBorrowPreviewInfo)"> 新增 </el-button>
            </div>
            <el-table :data="state.oilBorrowTableData" style="width: 100%" :border="true" class="mt-10px">
              <el-table-column label="序号" type="index" width="50" align="center"></el-table-column>
              <el-table-column
                :label="_v.label"
                :prop="_v.name"
                v-for="(_v, _i) in tableConfig.oilBorrowTableItem"
                :key="_i"
                align="center"
                :show-overflow-tooltip="true"
                :width="_v.width ?? 'auto'"
              ></el-table-column>
              <el-table-column label="操作" width="90px" align="center" fixed="right">
                <template #default="{ row, $index }">
                  <div>
                    <el-button v-if="row.auditStatusDesc === '审批驳回' || state.status === 'add'" link type="primary" @click="handleOilBorrowEdit(row, $index)"
                      >编辑
                    </el-button>
                  </div>
                  <div>
                    <el-button v-if="state.status === 'add'" link type="danger" @click="handleOilBorrowDelete(row)"> 删除 </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-if="activeName === 'fourth'">
            <div v-if="state.addPermission.etcAdd">
              <el-button :disabled="state.isDispatch" type="primary" @click="addTollBorrow(state.tollBorrowPreviewInfo)"> 新增 </el-button>
            </div>
            <el-table :data="state.tollBorrowTableData" style="width: 100%" :border="true" class="mt-10px">
              <el-table-column label="序号" type="index" width="50" align="center"></el-table-column>
              <el-table-column
                :label="_v.label"
                :prop="_v.name"
                v-for="(_v, _i) in tableConfig.tollBorrowTableItem"
                :key="_i"
                align="center"
                :width="_v.width ?? 'auto'"
              ></el-table-column>
              <el-table-column label="操作" width="90px" align="center" fixed="right">
                <template #default="{ row, $index }">
                  <el-button v-if="row.auditStatusDesc === '审批驳回'" link type="primary" @click="handleTollBorrowEdit(row, $index)">编辑 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-scrollbar>
      <template #footer v-if="!state.isDispatch">
        <span class="dialog-footer">
          <el-button v-if="state.status === 'edit' && state.dispatchInfo.cancelShow" type="danger" @click="cancelDispatch">取消调度</el-button>
          <el-button v-if="state.status !== 'view'" @click="editScheduleCancel">取消</el-button>
          <el-button v-if="state.status !== 'view'" type="primary" @click="editScheduleConfirm">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 线路调整弹窗 -->
    <LineAdjustmentDialogComponent
      ref="lineAdjustmentDialogComponentRef"
      @editLineInfo="editLineInfo"
      @showScheduleDetail="showScheduleDetail"
      :currentDispatchId="state.currentDispatchId"
      :dispatchLineData="state.dispatchLineData"
      :currentDispatchNo="state.currentDispatchNo"
    />
    <!-- 替换订单 -->
    <el-dialog v-model="replaceOrderDialog.visible" :title="replaceOrderDialog.title" width="600px" @close="handleCancelReplaceOrder">
      <el-form label-width="80px" :model="state.replaceOrderFormData" :inline="true" ref="replaceOrderFormRef">
        <el-form-item :rules="[{ required: true, message: '请输入系统编号', trigger: 'blur' }]" label="系统编号" prop="orderCode">
          <el-input v-model="state.replaceOrderFormData.orderCode" placeholder="请输入系统编号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="handleCancelReplaceOrder">取 消</el-button>
          <el-button size="small" type="primary" @click="handleConfirmReplaceOrder(replaceOrderFormRef)">确 定 </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 指派车辆 -->
    <el-dialog v-model="state.assignVehiclesDialogVisible" :draggable="true" title="" width="1000px" @close="closeAssignVehiclesDialog">
      <div>
        <el-form ref="rulesSub" label-width="80px" :model="state.assignVehiclesFormData" :inline="true">
          <el-form-item label="更换车辆" prop="vehicleNo" style="width: 100%">
            <el-input v-model="state.assignVehiclesFormData.vehicleNo" disabled placeholder="请选择车辆" style="width: 240px; margin-right: 10px" />
            <el-button size="small" type="primary" @click="showChooseVehicle">选择车辆</el-button>
            <!-- <el-select v-model="state.assignVehiclesFormData.ownId" placeholder="请选择车辆" style="width: 220px" @change="ownIdChange">
              <el-option-group v-for="group in rightVehicleList" :key="group.carrierId" :label="group.carrierName">
                <el-option v-for="item in group.children" :key="item.id" :label="item.vehicleNo" :value="item.id" />
              </el-option-group>
            </el-select> -->
          </el-form-item>
          <el-form-item label="调度单" prop="dispatchNo">
            <el-scrollbar height="300px">
              <el-radio-group v-model="state.assignVehiclesFormData.dispatchNo" style="width: 100%" @change="radioChange">
                <el-radio :value="''" style="width: 100%">新建任务</el-radio>
                <el-radio v-for="(v, i) in RadioData" :key="i" :value="v.dispatchNo" style="width: 100%">
                  {{ v.taskContent }}
                </el-radio>
              </el-radio-group>
            </el-scrollbar>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <div class="text-right">
          <el-button @click="closeAssignVehiclesDialog">取 消</el-button>
          <el-button :loading="state.assignVehiclesLoading" type="primary" @click="confirmAssignVehiclesDialog()">确 定 </el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 其他信息调整弹窗 -->
    <el-dialog title="其他信息调整" v-model="state.otherInformationDialogVisible" width="60%" :draggable="true">
      <div>
        <el-card>
          <template #header>
            <div class="card-header">
              <span>补贴</span>
            </div>
          </template>
          <div>
            <el-table :data="state.dispatchFormData.items" :border="true">
              <el-table-column label="补贴名称" align="center">
                <template #default="{ row }">
                  <div>{{ row.item.itemName }}</div>
                </template>
              </el-table-column>
              <el-table-column label="关联线路" align="center">
                <template #default="{ row }">
                  <div>{{ row.item.startEnd }}</div>
                </template>
              </el-table-column>
              <el-table-column label="补贴公式" prop="subsidyFormula" align="center"></el-table-column>
              <el-table-column label="补贴金额" align="center">
                <template #default="{ row }">
                  <el-input v-model="row.item.amount" placeholder="请输入补贴金额"></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
        <el-card class="mt-10px mb-10px">
          <template #header>
            <div class="card-header">
              <span>其他调整</span>
            </div>
          </template>
          <div>
            <el-table :data="state.dispatchFormData.fees" :border="true">
              <el-table-column label="名目" align="center">
                <template #default="{ row }">
                  <el-input v-model="row.name" placeholder="请输入名目"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="金额" align="center">
                <template #default="{ row }">
                  <el-input v-model="row.amount" placeholder="请输入金额" type="number"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="备注" align="center">
                <template #default="{ row, index }">
                  <el-input v-model="row.remark" placeholder="请输入备注"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="90px" align="center">
                <template #default="{ row, $index }">
                  <el-button
                    @click="handleOtherInfoDelete(row, $index)"
                    circle
                    icon="Minus"
                    size="small"
                    type="danger"
                    v-if="state.dispatchFormData.fees.length > 1"
                  ></el-button>
                  <el-button circle icon="Plus" size="small" type="primary" @click="handleOtherInfoPlus(row, $index)"></el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
        <el-card>
          <el-form ref="form" :model="state.dispatchFormData" label-width="80px" :inline="true">
            <el-form-item label="运输备注">
              <el-input v-model="state.dispatchFormData.operationRemark" :rows="2" placeholder="请输入运输备注" type="textarea"></el-input>
            </el-form-item>
            <el-form-item label="内部备注" prop="remark">
              <el-input v-model="state.dispatchFormData.remark" :rows="2" placeholder="请输入内部备注" type="textarea"></el-input>
            </el-form-item>
          </el-form>
        </el-card>
        <div class="flex items-center justify-end mt-10px">
          <span class="mr-10px">合计：{{ state.dispatchFormDataTotal }}</span>
          <!-- <el-button size="small" @click="calculateTotal">合计计算 </el-button> -->
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelOtherInformation" :disabled="state.isDispatch">取消</el-button>
          <el-button type="primary" @click="confirmOtherInformation" :disabled="state.isDispatch || state.status === 'view'">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 批量变更调度单 -->
    <el-dialog v-model="state.batchChangeDispatchNoDialogVisible" :draggable="true" title="变更调度单" width="600px" @close="closeBatchChangeDispatchNoDialog">
      <el-form ref="batchChangeDispatchNoFormRef" :model="state.batchChangeDispatchNoFormData" label-width="120px">
        <el-form-item :rules="[{ required: true, message: '请输入调度单编号', trigger: 'blur' }]" label="调度单编号" prop="replaceDispatchNo">
          <el-input v-model="state.batchChangeDispatchNoFormData.replaceDispatchNo" placeholder="请输入调度单编号"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelBatchChangeDispatchNo">取消</el-button>
          <el-button type="primary" @click="confirmBatchChangeDispatchNo(batchChangeDispatchNoFormRef)">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog title="凭证" v-model="state.showProofDialog" width="600px" @close="closeProofDialog">
      <h3 v-if="!state.currentRowVideo" class="flex items-center justify-center h-200px">司机未上传凭证</h3>
      <video v-else :src="state.currentRowVideo" style="width: 100%" controls></video>
    </el-dialog>
    <!-- 现金借支弹窗 -->
    <el-dialog title="现金借支" v-model="state.cashBorrowDialogVisible" width="600px" @close="closeCashBorrowDialog">
      <el-form ref="cashBorrowFormRef" :model="state.cashBorrowFormData" label-width="80px" :inline="true">
        <el-descriptions title="历史借支" border>
          <el-descriptions-item label="已付借支">{{ state.cashBorrowPreviewInfo.paidLoanAmount || 0 }} </el-descriptions-item>
          <el-descriptions-item label="未付借支">{{ state.cashBorrowPreviewInfo.unpaidLoanAmount || 0 }} </el-descriptions-item>
        </el-descriptions>
        <el-row :gutter="0" class="mt-20px">
          <el-col :span="12">
            <el-form-item :rules="[{ required: true, message: '请输入借款金额', trigger: 'blur' }]" label="借款金额" prop="loanAmount">
              <el-input v-model="state.cashBorrowFormData.loanAmount" placeholder="请输入借款金额" style="width: 172px" type="number"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款方式" prop="paymentType">
              <el-select v-model="state.cashBorrowFormData.paymentType" placeholder="请选择付款方式" style="width: 172px">
                <el-option label="汇款" :value="2"></el-option>
                <el-option label="现金" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <template v-if="state.cashBorrowFormData.paymentType === 2">
            <el-col :span="12">
              <el-form-item label="银行卡号" prop="cardNumber">
                <el-input v-model="state.cashBorrowFormData.cardNumber" placeholder="请输入银行卡号" style="width: 172px"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户信息" prop="accountInfo">
                <el-input v-model="state.cashBorrowFormData.accountInfo" placeholder="请输入开户信息" style="width: 172px"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户银行" prop="accountBank">
                <el-input v-model="state.cashBorrowFormData.accountBank" placeholder="请输入开户银行" style="width: 172px"></el-input>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="12">
            <el-form-item label="可冲金额" prop="mayCollideAmount">
              <el-text size="large">{{ state.cashBorrowPreviewInfo.mayCollideAmount || 0 }}</el-text>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="冲账金额" prop="collideAmount">
              <el-input
                v-model="state.cashBorrowFormData.collideAmount"
                type="number"
                :max="state.cashBorrowPreviewInfo.mayCollideAmount"
                placeholder="请输入冲账金额"
                style="width: 172px"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="state.cashBorrowFormData.remark" type="textarea" placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeCashBorrowDialog">取消</el-button>
          <el-button type="primary" @click="confirmCashBorrowDialog(cashBorrowFormRef)">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 油费借支弹窗 -->
    <el-dialog title="油费借支" v-model="state.oilBorrowDialogVisible" width="60%" @close="closeOilBorrowDialog">
      <el-scrollbar max-height="65vh">
        <el-descriptions title="历史借支" border :column="4">
          <el-descriptions-item label="车牌号">{{ state.oilBorrowPreviewInfo.vehicleNo || '-' }}</el-descriptions-item>
          <el-descriptions-item label="司机信息"
            >{{ state.oilBorrowPreviewInfo.driverName || '-' }}（{{ state.oilBorrowPreviewInfo.driverMobile || '-' }}）
          </el-descriptions-item>
          <el-descriptions-item label="标准油箱升数">{{ state.oilBorrowPreviewInfo.standardOilTankCapacity || '-' }}L </el-descriptions-item>
          <el-descriptions-item label="预留油升数">{{ state.oilBorrowPreviewInfo.reserveOilCapacity || '-' }}L </el-descriptions-item>
        </el-descriptions>
        <el-form class="mt-20px" ref="oilBorrowFormRef" :model="state.oilBorrowFormData" label-width="120px">
          <el-row :gutter="0">
            <el-col :span="8">
              <el-form-item label="预估油升数" prop="estimatedOilCapacity">
                <el-input v-model="state.oilBorrowPreviewInfo.estimatedOilCapacity" :disabled="true" class="w-100%" placeholder="请输入预估油升数"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="已借金额" prop="totalPrice">
                <el-input v-model="state.oilBorrowPreviewInfo.totalPrice" :disabled="true" class="w-100%" placeholder="请输入已借金额"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="0">
            <el-col :span="8">
              <el-form-item label="基地油升数" prop="baseOilLiters">
                <el-input-number
                  :style="{ width: '100%' }"
                  v-model="state.oilBorrowFormData.baseOilLiters"
                  placeholder="请输入基地油升数"
                  :min="0"
                  :precision="2"
                >
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :rules="[{ required: true, message: '请输入基地油单价', trigger: 'blur' }]" label="基地油单价" prop="baseOilPrice">
                <el-input-number
                  :style="{ width: '100%' }"
                  v-model="state.oilBorrowFormData.baseOilPrice"
                  placeholder="请输入基地油单价"
                  :min="0"
                  :precision="2"
                >
                  <template #suffix>
                    <span>元/L</span>
                  </template>
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="基地油费" prop="baseFuelCost">
                <el-input-number v-model="state.baseFuelCost" :min="0" :precision="2" :style="{ width: '100%' }" placeholder="请输入基地油费">
                  <template #suffix>
                    <div class="flex items-center">
                      <span>元</span>
                      <el-tooltip effect="dark" content="基地油费=基地油升数*基地油单价" placement="bottom">
                        <el-icon :size="16" class="ml-5px" color="orange">
                          <Warning />
                        </el-icon>
                      </el-tooltip>
                    </div>
                  </template>
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="其他油升数" prop="otherOilLiters">
                <el-input-number
                  :style="{ width: '100%' }"
                  v-model="state.oilBorrowFormData.otherOilLiters"
                  placeholder="请输入其他油升数"
                  :min="0"
                  :precision="1"
                >
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :rules="[{ required: true, message: '请输入其他油单价', trigger: 'blur' }]" label="其他油单价" prop="otherOilPrice">
                <el-input-number
                  :style="{ width: '100%' }"
                  v-model="state.oilBorrowFormData.otherOilPrice"
                  placeholder="请输入其他油单价"
                  :min="0"
                  :precision="2"
                >
                  <template #suffix>
                    <span>元/L</span>
                  </template>
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="其他油总价" prop="otherFuelCost">
                <el-input-number
                  :disabled="true"
                  :style="{ width: '100%' }"
                  v-model="state.oilBorrowFormData.otherFuelCost"
                  placeholder="请输入其他油总价"
                  :min="0"
                  :precision="2"
                >
                  <template #suffix>
                    <span>元</span>
                  </template>
                </el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-card shadow="never" v-if="state.status === 'edit'">
            <template #header>
              <div class="card-header">
                <span>油卡信息</span>
              </div>
            </template>
            <el-table :data="state.oilBorrowPreviewInfo.oilItems" :border="true">
              <el-table-column label="类型" prop="oilCardTypeName" align="center"></el-table-column>
              <el-table-column label="卡号" prop="oilCardNumber" align="center"></el-table-column>
              <el-table-column label="余额" prop="cardBalance" align="center">
                <template #default="{ row }">
                  <el-text type="danger">{{ row.cardBalance || 0 }}</el-text>
                </template>
              </el-table-column>
              <el-table-column label="预估剩余油升数" prop="oilCardAmount" align="center">
                <template #default="{ row }">
                  <el-text v-if="state.oilBorrowFormData.otherOilPrice" type="danger"
                    >{{ (row.cardBalance / state.oilBorrowFormData.otherOilPrice).toFixed(2) }}
                  </el-text>
                  <el-text type="danger" v-else>--</el-text>
                </template>
              </el-table-column>
              <el-table-column label="充值金额" prop="rechargeAmount" align="center">
                <template #default="{ row }">
                  <el-input-number v-model="row.rechargeAmount" :min="0" :precision="2" :style="{ width: '100%' }" placeholder="请输入充值金额">
                    <template #suffix>
                      <span>元</span>
                    </template>
                  </el-input-number>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
          <el-row :gutter="0" class="mt-20px">
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="state.oilBorrowFormData.remark" type="textarea" placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-scrollbar>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeOilBorrowDialog">取消</el-button>
          <el-button type="primary" @click="confirmOilBorrowDialog(oilBorrowFormRef)">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 路桥费借支弹窗 -->
    <el-dialog v-model="state.tollBorrowDialogVisible" draggable title="路桥费借支" width="60%" @close="closeTollBorrowDialog">
      <el-form ref="tollBorrowFormRef" :model="state.tollBorrowFormData" label-width="100px">
        <el-row :gutter="0">
          <el-col :span="8">
            <el-form-item label="预估高速费" prop="highwayFee">
              <el-input v-model="state.dispatchInfo.highwayFee" :disabled="true" class="w-100%" placeholder="请输入预估高速费"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="已借金额" prop="totalPrice">
              <el-input v-model="state.tollBorrowPreviewInfo.totalPrice" :disabled="true" class="w-100%" placeholder=""></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span>ETC信息</span>
            </div>
          </template>
          <el-table :data="state.tollBorrowFormData.etcItems" :border="true">
            <el-table-column label="卡号" prop="etcCardNumber" align="center"></el-table-column>
            <el-table-column label="余额" prop="cardBalance" align="center">
              <template #default="{ row }">
                <el-text type="danger">{{ state.tollBorrowPreviewInfo.cardBalance || 0 }}</el-text>
              </template>
            </el-table-column>
            <el-table-column label="充值金额" prop="rechargeAmount" align="center">
              <template #default="{ row }">
                <el-input v-model="row.rechargeAmount" type="number" :min="0" placeholder="请输入充值金额"></el-input>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
        <el-row :gutter="0" class="mt-20px">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="state.tollBorrowFormData.remark" type="textarea" placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeTollBorrowDialog">取消</el-button>
          <el-button type="primary" @click="confirmTollBorrowDialog(tollBorrowFormRef)">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 变更起终点弹窗 -->
    <el-dialog
      :title="`${state.addressType === 'start' ? '编辑运输起点' : '编辑运输终点'}`"
      v-model="updateStartPointDialog.visible"
      width="600px"
      :draggable="true"
      @close="closeUpdateStartPointDialog"
    >
      <el-form ref="updatePointFormRef" :model="state.updatePointFormData" label-width="120px">
        <el-form-item
          :label="`${state.addressType === 'start' ? '运输起点' : '运输终点'}`"
          prop="fenceId"
          :rules="[{ required: true, message: '请输入' + (state.addressType === 'start' ? '运输起点' : '运输终点'), trigger: 'blur' }]"
        >
          <el-select
            v-model="state.updatePointFormData.fenceId"
            style="width: 100%"
            remote-show-suffix
            clearable
            filterable
            remote
            reserve-keyword
            :placeholder="`${state.addressType === 'start' ? '运输起点' : '运输终点'}`"
            :remote-method="(query: string) => remoteSelectMethod(query, 'fenceId')"
            :disabled="state.currentDispatchOrderIds.length === 0"
            @change="handleSelectFence"
          >
            <el-option v-for="i in geoFenceList" :key="i.id" :label="i.name" :value="i.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="state.updatePointFormData.address" readonly></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeUpdateStartPointDialog">取消</el-button>
          <el-button type="primary" @click="confirmUpdatePointDialog(updatePointFormRef)">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 编辑时间弹窗 -->
    <el-dialog
      :title="`${state.addressType === 'start' ? '装车时间' : '交车时间'}`"
      v-model="updateTimeDialog.visible"
      width="600px"
      :draggable="true"
      @close="closeUpdateTimeDialog"
    >
      <el-form ref="updateTimeFormRef" :model="state.updateTimeFormData" label-width="120px">
        <el-form-item
          :label="`${state.addressType === 'start' ? '装车时间' : '交车时间'}`"
          prop="time"
          :rules="[{ required: true, message: '请选择时间', trigger: 'change' }]"
        >
          <el-date-picker v-model="state.updateTimeFormData.time" placeholder="请选择时间" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeUpdateTimeDialog" :disabled="state.isDispatch">取消</el-button>
          <el-button :disabled="state.isDispatch" type="primary" @click="confirmUpdateTimeDialog(updateTimeFormRef)"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 媒体预览弹窗 -->
    <el-dialog v-model="mediaDialogVisible" :close-on-click-modal="false" :title="mediaDialogTitle" destroy-on-close width="80%">
      <div class="media-grid">
        <template v-if="mediaType === 'image'">
          <el-image
            v-for="(media, index) in currentMedia"
            :key="index"
            :src="media.url"
            :initial-index="index"
            :preview-src-list="currentMedia.map((m: any) => m.url)"
            fit="cover"
            :z-index="999"
            style="width: 100%; height: 200px"
          />
        </template>
        <template v-else>
          <div v-for="(media, index) in currentMedia" :key="index" class="video-item">
            <video :src="media.url" controls style="width: 100%; height: 200px" />
          </div>
        </template>
      </div>
    </el-dialog>
    <!-- 视频播放弹窗 -->
    <el-dialog v-model="videoDialogVisible" title="视频播放" width="60%" destroy-on-close>
      <video ref="videoPlayer" controls style="width: 100%; max-height: 70vh"></video>
    </el-dialog>
    <!-- 添加VIN弹窗 -->
    <CancelVinDialogComponent ref="cancelVinDialogComponent" :dispatchNo="state.currentDispatchNo" @refresh="refreshDetail" />
  </div>
</template>
<script setup lang="ts">
  import {
    deleteOutFleetLoanCashDraftApi,
    deleteOutFleetLoanOilFeeDraftApi,
    getDispatchFeeAdjustInfoApi,
    getdispatchListApi,
    getDispatchOrderTypeApi,
    getFleetOrderDispatchCashAddDetailApi,
    getFleetOrderDispatchCashAddDraftDetailApi,
    getFleetOrderDispatchCashAddDraftListApi,
    getFleetOrderDispatchCashAddPermissionApi,
    getFleetOrderDispatchCashAddPreviewApi,
    getFleetOrderDispatchClearApi,
    getFleetOrderDispatchEtcFeeDetailApi,
    getFleetOrderDispatchEtcFeePreviewApi,
    getFleetOrderDispatchGeofenceSearchApi,
    getFleetOrderDispatchLineDetailApi,
    getFleetOrderDispatchOilFeeDetailApi,
    getFleetOrderDispatchOilFeeDraftDetailApi,
    getFleetOrderDispatchOilFeeDraftListApi,
    getFleetOrderDispatchOilFeePreviewApi,
    getFleetOrderDispatchVinCancelDeliveryApi,
    getFleetOrderDispatchVinDetailApi,
    getOutFleetLoanCashDetailApi,
    getOutFleetLoanEtcFeeDetailApi,
    getOutFleetLoanOilFeeDetailApi,
    getvehicleWholeId,
    orderDispatchCancelApi,
    postAdjustDriverDispatchSubsidyApi,
    postFleetOrderDispatchBatchDeliveryApi,
    postFleetOrderDispatchBatchLoadingApi,
    postFleetOrderDispatchCashAddApi,
    postFleetOrderDispatchChangeApi,
    postFleetOrderDispatchEndAreaAlterApi,
    postFleetOrderDispatchEtcFeeAddApi,
    postFleetOrderDispatchOilFeeAddApi,
    postFleetOrderDispatchPublishApi,
    postFleetOrderDispatchRemoveVinApi,
    postFleetOrderDispatchStartAreaAlterApi,
    postFleetOrderDispatchVinCancelApi,
    postFleetOrderDispatchVinReplaceApi,
    postFleetOrderDispatchVinViewTypeAlterApi,
    postOutFleetLoanCashDraftEditApi,
    postOutFleetLoanCashEditApi,
    postOutFleetLoanEtcFeeEditApi,
    postOutFleetLoanOilFeeDraftEditApi,
    postOutFleetLoanOilFeeEditApi,
    postOutFleetOrderDispatchAlterActualDropTimeVinApi,
    postOutFleetOrderDispatchAlterLoadTimeVinApi,
    postvehicleWholeId,
    putFleetOrderDispatchApi,
  } from '@/api/businessManagement'
  import LineInfoComponent from './components/LineInfoComponent.vue'
  import VehicleInfoDialogComponent from '../VehicleInfoDialogComponent/index.vue'
  import LineAdjustmentDialogComponent from '../LineAdjustmentDialogComponent/index.vue'
  import CustomPrintButtonComponent from '@/components/CustomPrintButtonComponent/index.vue'
  import TextareaComponent from '@/components/TextcreaComponent/index.vue'
  import CancelVinDialogComponent from './components/cancelVinDialogComponent.vue'
  import VxeSingleTable from '@/components/VxeTableComponent/singleTable.vue'
  import { FormInstance, TabsPaneContext } from 'element-plus'
  import { PropType } from 'vue'
  import { useSmartDispatchStore } from '@/store/modules/smartDispatch'
  import { getNowDate } from '@/utils'
  import defaultSettings from '@/settings'
  import { debounce } from 'lodash'

  const smartDispatchStore = useSmartDispatchStore()
  const { smartDispatchRightVehicleList } = storeToRefs(smartDispatchStore)
  const emit = defineEmits(['confirmDispatchSuccess', 'closeDialog', 'resetQuery', 'showMenuDialog'])
  const ruleFormRef = ref<FormInstance>()
  const cashBorrowFormRef = ref<FormInstance>()
  const oilBorrowFormRef = ref<FormInstance>()
  const tollBorrowFormRef = ref<FormInstance>()
  const lineAdjustmentDialogComponentRef = ref()
  const updatePointFormRef = ref()
  const updateTimeFormRef = ref()
  const replaceOrderFormRef = ref<FormInstance>()
  const videoDialogVisible = ref(false)
  const videoPlayer = ref()

  const props = defineProps({
    /**
     * 弹窗属性
     */
    dialog: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
    requestUri: {
      require: true,
      type: String,
      default: '',
    },
  })
  const loading = ref(false)
  const tableConfig = {
    showHandleSelection: true,
    tableItem: [
      {
        name: 'customerName',
        label: '客户名称',
        width: '120px',
      },
      {
        name: 'vin',
        label: 'VIN',
        width: '120px',
      },

      {
        name: 'orderStatusName',
        label: '订单状态',
        width: '100px',
      },
      {
        name: 'dispatchNo',
        label: '调度单号',
        width: '100px',
      },
      {
        name: 'dispatchStatus',
        label: '调度状态',
        width: '100px',
      },
      {
        name: 'vehicleNo',
        label: '车牌号',
        width: '100px',
      },

      {
        name: 'startArea',
        label: '起点',
        width: '100px',
      },
      {
        name: 'endArea',
        label: '终点',
        width: '100px',
      },
      {
        name: 'lineName',
        label: '客户线路',
        width: '100px',
      },
      {
        name: 'pickUpPointName',
        label: '提车点',
        width: '120px',
      },
      {
        name: 'dropUpPointName',
        label: '交车点',
        width: '120px',
      },
      {
        name: 'pickUpPointAddress',
        label: '提车地址',
        width: '120px',
      },
      {
        name: 'orderCode',
        label: '订单编号',
        width: '180px',
      },

      {
        name: 'dropUpPointAddress',
        label: '交车地址',
        width: '120px',
      },
      {
        name: 'orderIssueDatetime',
        label: '计划下达',
        width: '150px',
      },
      {
        name: 'actualDropTime',
        label: '订单交车',
        width: '150px',
      },
      {
        name: 'dealerName',
        label: '经销商',
        width: '120px',
      },
      {
        name: 'dealerAddress',
        label: '经销商地址',
        width: '120px',
      },
    ],

    scheduleTableItem: [
      {
        name: 'customerName',
        label: '客户名称',
        width: '100px',
      },
      {
        name: 'vin',
        label: 'VIN',
        width: '120px',
      },
      {
        name: 'orderStatusName',
        label: '订单状态',
        width: '80px',
      },
      {
        name: 'customerLine',
        label: '客户线路',
        width: '120px',
      },
      {
        name: 'carrierLine',
        label: '运输线路',
        width: '120px',
      },
      {
        name: 'carrierStartFenceName',
        label: '运输起点',
        width: '120px',
      },
      {
        name: 'carrierEndFenceName',
        label: '运输终点',
        width: '120px',
      },
      {
        name: 'pickUpPointName',
        label: '提车点',
        width: '180px',
      },
      {
        name: 'pickUpPointAddress',
        label: '提车地址',
        width: '180px',
      },
      {
        name: 'dropUpPointName',
        label: '交车点',
        width: '180px',
      },
      {
        name: 'dropUpPointAddress',
        label: '交车地址',
        width: '180px',
      },
      {
        name: 'orderIssueDatetime',
        label: '计划下达',
        width: '150px',
      },
      {
        name: 'loadTime',
        label: '装车时间',
        width: '150px',
      },
      {
        name: 'loadPicList',
        label: '装车图片',
        width: '150px',
      },
      {
        name: 'loadVideoList',
        label: '装车视频',
        width: '150px',
      },
      {
        name: 'loadLocation',
        label: '实际装车地址',
        width: '150px',
      },
      {
        name: 'actualDropTime',
        label: '交车时间',
        width: '150px',
      },
      {
        name: 'dropPicList',
        label: '交车图片',
        width: '150px',
      },
      {
        name: 'dropVideoList',
        label: '交车视频',
        width: '150px',
      },
      {
        name: 'dropLocation',
        label: '实际交车地址',
        width: '150px',
      },
    ],
    otherInfoTableItem: [
      {
        name: 'customerName',
        label: '补贴名称',
      },
      {
        name: 'customerName',
        label: '关联线路',
      },
      {
        name: 'customerName',
        label: '补贴公式',
      },
      {
        name: 'customerName',
        label: '补贴金额',
      },
    ],
    cashBorrowTableItem: [
      {
        name: 'no',
        label: '借款单号',
      },
      {
        name: 'createTime',
        label: '借款日期',
      },
      {
        name: 'auditStatusDesc',
        label: '状态',
      },
      {
        name: 'typeDesc',
        label: '借支类型',
      },
      {
        name: 'loanAmount',
        label: '借款金额',
      },
      {
        name: 'collideAmount',
        label: '冲账金额',
      },
      {
        name: 'payableAmount',
        label: '应打款金额',
      },
      {
        name: 'paymentTypeDesc',
        label: '支付方式',
      },
      {
        name: 'cardNumber',
        label: '银行卡号',
      },
      {
        name: 'accountInfo',
        label: '开户信息',
      },
      {
        name: 'accountBank',
        label: '开户银行',
      },
      {
        name: 'remark',
        label: '备注',
        width: '240px',
      },
    ],
    oilBorrowTableItem: [
      {
        name: 'oilBillNumber',
        label: '油费单号',
      },
      {
        name: 'auditStatusDesc',
        label: '状态',
      },
      {
        name: 'baseOilPrice',
        label: '基地油价',
      },
      {
        name: 'baseOilLiters',
        label: '基地油升数',
      },
      {
        name: 'baseFuelCost',
        label: '基地油金额',
      },
      {
        name: 'sinopecAmount',
        label: '中石化金额',
      },
      {
        name: 'sinopecCardNumber',
        label: '中石化卡号',
      },
      {
        name: 'petroChinaAmount',
        label: '中石油金额',
      },
      {
        name: 'petroChinaCardNumber',
        label: '中石油卡号',
      },
      {
        name: 'wanjinOilAmount',
        label: '万金油金额',
      },
      {
        name: 'wanjinOilCardNumber',
        label: '万金油卡号',
      },
      {
        name: 'totalAmount',
        label: '总金额',
      },
      {
        name: 'remarks',
        label: '备注',
        width: '240px',
      },
    ],
    tollBorrowTableItem: [
      {
        name: 'feeNo',
        label: 'ETC费用单号',
      },
      {
        name: 'auditStatusDesc',
        label: '状态',
      },
      {
        name: 'amount',
        label: '金额',
      },
      {
        name: 'etcNumber',
        label: '卡号',
      },
      {
        name: 'remark',
        label: '备注',
        width: '240px',
      },
    ],
  }
  const replaceOrderDialog = reactive<DialogOption>({
    title: '替换订单',
    visible: false,
  })
  const updateStartPointDialog = reactive<DialogOption>({
    title: '编辑运输',
    visible: false,
  })
  const updateTimeDialog = reactive<DialogOption>({
    title: '',
    visible: false,
  })
  let RadioData: any = ref([])
  const geoFenceList = ref<any[]>([])
  const fuzzySelectLoading = ref(false)
  const activeName = ref('first')
  const batchChangeDispatchNoFormRef = ref()
  const handleClick = (tab: TabsPaneContext, event: Event) => {
    switch (tab.props.name) {
      case 'second':
        if (state.status === 'edit') {
          getFleetOrderDispatchCashAddDetailApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
            const { data } = res
            state.cashBorrowTableData = data
          })
        } else {
          getFleetOrderDispatchCashAddDraftListApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
            const { data } = res
            state.cashBorrowTableData = data.rows
          })
        }

        break
      case 'third':
        getFleetOrderDispatchOilFeePreview()
        if (state.status === 'edit') {
          getFleetOrderDispatchOilFeeDetailApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
            const { data } = res
            state.oilBorrowTableData = data
          })
        } else {
          getFleetOrderDispatchOilFeeDraftListApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
            const { data } = res
            state.oilBorrowTableData = data.rows
          })
        }
        break
      case 'fourth':
        getFleetOrderDispatchEtcFeePreview()
        getFleetOrderDispatchEtcFeeDetailApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
          const { data } = res
          state.tollBorrowTableData = data
        })
        break
    }
  }

  const ruleSub = ref<FormInstance>()
  const vehicleInfoDialogRef = ref()
  const tableData = ref<any[]>([])
  const state = reactive<any>({
    tableLoading: false, //表格加载状态
    isDispatch: '', ///是否禁用  whn-新增
    updateTimeFlag: 1, //1是当前行按钮，2是顶部变更时间按钮，3是顶部批量装车按钮,4是顶部批量交车按钮，5是行装车按钮，6是行交车按钮
    isOilBorrowEdit: false, //是否是油费借支编辑
    isCashBorrowEdit: false, //是否是现金借支编辑
    isTollBorrowEdit: false, //是否是路桥费借支编辑
    addressType: 'start', //默认是运输起点，start是运输起点，end是运输终点
    status: 'add', //默认是新增状态，add是新增，edit是编辑 add 隐藏明细下的操作按钮
    total: 0,
    addPermission: false, //是否展示借支新增按钮
    queryParams: {
      vin: undefined,
      pickUpPointName: undefined,
      lineName: undefined,
      customerName: undefined,
      pickUpPointAddress: undefined,
      dropUpPointName: undefined,
      dropUpPointAddress: undefined,
      startProvince: undefined,
      endProvince: undefined,
      dealerName: undefined,
      dealerAddress: undefined,
      orderIssueDatetime: undefined,
      startOrderIssueDatetime: undefined,
      endOrderIssueDatetime: undefined,
      actualDropTime: undefined,
      page: 1,
      limit: defaultSettings.globalLimit,
      orderByClause: 'order_status,order_issue_datetime',
    },
    updatePointFormData: {
      fenceId: '',
    },
    updateTimeFormData: {
      time: '',
    },
    assignVehiclesresult: {
      id: [], //列表的id
      createDispatchNo: true, //是否新建调度单（true:调度号不填，其余均填 false:调度单号填 其余字段不填）
      dispatchNo: '', //调度单号
      carrierId: '', //承运商id
      carrierName: '', //承运商名称
      carrierType: '', //承运商类型
      vehicleNo: '', //车辆
      vehicleWholeId: '', //整车id
    },
    assignVehiclesFormData: {
      carrierId: '',
      carrierName: '',
      createDispatchNo: true,
      dispatchNo: '',
      carrierType: '',
      vehicleNo: '',
      vehicleWholeId: '',
      orderIds: [],
      ids: [],
    },
    vehicleInfoDialogVisible: false,
    editScheduleDialogVisible: false,
    lineAdjustmentDialogVisible: false,
    otherInformationDialogVisible: false,
    batchChangeDispatchNoDialogVisible: false,
    formData: {
      items: [],
    },
    assignVehiclesForm: {
      id: '',
    },

    batchChangeDispatchNoFormData: {
      replaceDispatchNo: '',
    },
    currentChooseVehicles: [],
    scheduleTableData: [], //编辑调度弹窗中下方明细列表
    cashBorrowTableData: [], //现金借支列表
    oilBorrowTableData: [], //油费借支列表
    tollBorrowTableData: [], //路桥费借支列表
    vehicleTableData: [],
    rightTableData: [],
    currentSelectVehicles: [],
    rightVehicleDeleteOrderIds: [], //调度单中删除的订单ids
    otherInfoFormData: {
      otherInfoTableData: [{}],
      otherInfoSubsidyTableData: [{}],
    },
    vehicleTotal: 0,

    dispatchFormData: {
      operationRemark: '',
      dispatchRemark: '',
      fees: [],
      items: [],
    },
    dispatchFormDataTotal: 0,
    showDispatchBtns: false, //显示操作按钮
    currentChooseVehicleIds: '',
    assignVehiclesLoading: false,
    currentDispatchOrderIds: [],
    currentDispatchIds: [],
    currentDispatchId: '', //当前调度单id
    currentDispatchNo: '', //当前调度单号
    dispatchInfo: {}, //调度信息
    dispatchLineData: [], //调度线路信息
    dispatchOrderTypeOptions: [], //调度单类型下拉
    currentRowVideo: '',
    showProofDialog: false,
    replaceOrderFormData: {
      orderCode: '', //替换的订单id
      dispatchNo: '', //调度单号
      orderId: '', //订单id
    },
    cashBorrowDialogVisible: false,
    oilBorrowDialogVisible: false,
    tollBorrowDialogVisible: false,
    cashBorrowFormData: {
      loanAmount: 0,
      collideAmount: 0,
      paymentType: 2,
      remark: '',
      dispatchNo: '',
      cardNumber: '',
      accountInfo: '',
      accountBank: '',
    },
    oilBorrowFormData: {},
    tollBorrowFormData: {
      amount: '',
      remark: '',
      highwayFee: '',
      etcItems: [],
    },
    cashBorrowPreviewInfo: {},
    oilBorrowPreviewInfo: {},
    tollBorrowPreviewInfo: {},
    baseFuelCost: 0,
    otherTotalAmount: 0,
    currentRow: {},
  })

  const moreSearch = ref(false)
  const showTextBatch = ref(false)
  const TextareaRef = ref()
  const moreClick = () => {
    moreSearch.value = !moreSearch.value
  }

  const openTextBatch = () => {
    showTextBatch.value = true
  }

  const closeTextBatch = (array: any) => {
    showTextBatch.value = false
  }

  const handleArrayReceived = (array: any) => {
    state.queryParams!.vin = Object.values(array).join(',')
  }

  const emptyTextBatch = () => {
    TextareaRef.value.list = []
  }

  const rightVehicleList = ref<any[]>([])
  watch(
    () => props.dialog.visible,
    async (val) => {
      if (val) {
        await getOrderDispatchList()
        state.currentChooseVehicles = smartDispatchRightVehicleList.value
        vehicleInfoDialogRef.value.state.currentChooseVehicles = state.currentChooseVehicles
        vehicleInfoDialogRef.value.chooseVehicleConfirm(rightVehicleList.value, state.currentChooseVehicles)
        searchReset()
      }
    },
    { deep: true },
  )

  const cancelDispatch = () => {
    ElMessageBox.confirm('是否取消当前调度单?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      let params = {
        dispatchNo: state.currentDispatchNo,
      }
      orderDispatchCancelApi(params)
        .then((res: any) => {
          editScheduleCancel()
          state.editScheduleDialogVisible = false
          ElMessage.success('取消成功')
        })
        .catch((err: any) => {
          ElMessage.error(err.message)
        })
    })
  }

  // 更换车辆查询调度单
  const ownIdChange = (val: any) => {
    getvehicleWholeId({ vehicleWholeId: val }).then((res: any) => {
      state.assignVehiclesFormData.dispatchNo = ''
      RadioData.value = res.data
    })
  }

  const closeBatchChangeDispatchNoDialog = () => {
    //清除表单
    state.batchChangeDispatchNoFormData = {
      replaceDispatchNo: '',
    }
    state.batchChangeDispatchNoDialogVisible = false
  }
  const chooseVehicleConfirm = (newVehicleTableData: any[], originalVehicleTableData: any[]) => {
    if (originalVehicleTableData.length) {
      let { carrierName, carrierId, carrierType, vehicleWholeId, vehicleNo } = originalVehicleTableData[0]

      console.log(originalVehicleTableData[0])

      state.assignVehiclesFormData.carrierName = carrierName
      state.assignVehiclesFormData.carrierId = carrierId
      state.assignVehiclesFormData.carrierType = carrierType
      state.assignVehiclesFormData.vehicleWholeId = vehicleWholeId
      state.assignVehiclesFormData.vehicleNo = vehicleNo
    }

    rightVehicleList.value = newVehicleTableData
    state.currentChooseVehicles = originalVehicleTableData
    if (originalVehicleTableData.length > 0) {
      ownIdChange(originalVehicleTableData[0]?.vehicleWholeId)
    }

    vehicleInfoDialogRef.value.state.vehicleInfoDialogVisible = false
  }
  const radioChange = (val: any) => {
    if (val === '') {
      state.assignVehiclesFormData.createDispatchNo = true
    } else {
      state.assignVehiclesFormData.createDispatchNo = false
    }
    state.assignVehiclesFormData.dispatchNo = val
  }
  const calculateTotal = () => {
    let total = 0
    //将state.dispatchFormData.fees中的amount和state.dispatchFormData.items.item.amount累加
    state.dispatchFormData.fees.forEach((item: any) => {
      total += Number(item.amount)
    })
    //将state.dispatchFormData.items.item.amount累加
    state.dispatchFormData.items.forEach((_item: any) => {
      total += Number(_item.item.amount)
    })
    state.dispatchFormDataTotal = total
  }
  //监听state.dispatchFormData.fees中的amount变化，重新计算state.dispatchFormDataTotal
  watch(
    () => state.dispatchFormData,
    () => {
      calculateTotal()
    },
    { deep: true },
  )
  const editLineInfo = () => {
    lineAdjustmentDialogComponentRef.value.state.lineAdjustmentDialogVisible = true
    lineAdjustmentDialogComponentRef.value.state.isEmptyDispatch = false
    lineAdjustmentDialogComponentRef.value.editLineInfo(state.dispatchLineData)
    lineAdjustmentDialogComponentRef.value.state.status = state.status
  }
  //获取调度单类型下拉
  const getDispatchOrderTypeOptions = async () => {
    const { data } = await getDispatchOrderTypeApi({})
    state.dispatchOrderTypeOptions = data
  }

  /**
   * 分页
   */
  const _pagination = async () => {
    await getOrderDispatchList()
  }

  const showChooseVehicle = async () => {
    //如果rightVehicleList中的children中存在dispatchNo，则提示"当前车辆已存在调度单，请先清空调度单"
    // const dispatchNo = rightVehicleList.value.find((item: any) => item.children.some((child: any) => child.dispatchNo))
    vehicleInfoDialogRef.value.state.vehicleInfoDialogVisible = true
    vehicleInfoDialogRef.value.state.currentChooseVehicles = rightVehicleList.value.map((item: any) => item.children).flat()
    await vehicleInfoDialogRef.value.getFleetVehicleDispatchList(vehicleInfoDialogRef.value.state.vehicleQueryParams)
  }
  const searchReset = async () => {
    state.assignVehiclesFormData.orderIds = []
    TextareaRef.value.list = []
    state.queryParams = {
      vin: undefined,
      pickUpPointName: undefined,
      lineName: undefined,
      customerName: undefined,
      pickUpPointAddress: undefined,
      dropUpPointName: undefined,
      dropUpPointAddress: undefined,
      startProvince: undefined,
      endProvince: undefined,
      dealerName: undefined,
      dealerAddress: undefined,
      orderIssueDatetime: undefined,
      startOrderIssueDatetime: undefined,
      endOrderIssueDatetime: undefined,
      actualDropTime: undefined,
      page: 1,
      limit: defaultSettings.globalLimit,
      orderByClause: 'order_status,order_issue_datetime',
    }
    moreSearch.value = false
    await getOrderDispatchList()
    //关闭弹窗
    // emit('closeDialog')
  }
  //获取订单列表
  const getOrderDispatchList = async () => {
    try {
      state.tableLoading = true
      const params = JSON.parse(JSON.stringify(state.queryParams))
      params.orderIssueDatetime = undefined
      const { data } = await getdispatchListApi(Object.assign(params, { orderStatus: 2 }))
      state.total = data.total
      tableData.value = data.rows
      state.tableLoading = false
    } catch (err) {
      state.tableLoading = false
    }
  }
  // 提交表单
  const confirm = debounce(async () => {
    const dispatchNos = rightVehicleList.value
      .map((item: any) => item.children.map((child: any) => child.dispatchNo))
      .flat()
      .filter((dispatchNo: any) => dispatchNo !== undefined)
    const { data } = await postFleetOrderDispatchPublishApi({ dispatchNos })
    ElMessage.success('提交成功')
    smartDispatchStore.setSmartDispatchRightVehicleList([])
    emit('confirmDispatchSuccess')
  }, 300)

  const handleSelectionChange = (ids: Array<string>) => {
    state.assignVehiclesFormData.orderIds = ids
  }
  const handleSelectionRightVehicleOrderChange = (e: any) => {
    state.rightVehicleDeleteOrderIds = e.map((item: any) => item.orderId)
  }
  watch(
    () => state.editScheduleDialogVisible,
    (newVal) => {
      if (newVal) {
        activeName.value = 'first'
        //draft 草稿态
        getFleetOrderDispatchCashAddPermissionApi({
          dispatchNo: state.currentDispatchNo,
          draft: state.status === 'edit' ? 0 : 1,
        }).then((res: any) => {
          const { data } = res
          state.addPermission = data
        })
      }
    },
  )
  const editScheduleCancel = () => {
    if (state.status === 'edit') {
      emit('closeDialog')
    }
    state.editScheduleDialogVisible = false
  }
  const editScheduleConfirm = async () => {
    const params = {
      id: state.currentDispatchId,
      operationRemark: state.dispatchInfo.operationRemark,
      remark: state.dispatchInfo.dispatchRemark,
    }
    const { data } = await putFleetOrderDispatchApi(params)
    ElMessage.success('操作成功')
    state.editScheduleDialogVisible = false
    emit('confirmDispatchSuccess')
  }
  const cancelBatchChangeDispatchNo = () => {
    state.batchChangeDispatchNoDialogVisible = false
  }
  const confirmBatchChangeDispatchNo = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        const params = {
          orderIds: state.currentDispatchOrderIds,
          originalDispatchNo: state.currentDispatchNo,
          replaceDispatchNo: state.batchChangeDispatchNoFormData.replaceDispatchNo,
        }
        const { data } = await postFleetOrderDispatchChangeApi(params)
        ElMessage.success('变更成功')
        state.batchChangeDispatchNoDialogVisible = false
        //刷新列表
        await showScheduleDetail(state.currentDispatchId, state.currentDispatchNo, state.status)
      }
    })
  }
  const showScheduleDetail = async (dispatchId: string, dispatchNo: string, status: string = 'add') => {
    state.status = status
    state.currentDispatchId = dispatchId
    state.currentDispatchNo = dispatchNo
    // state.showDispatchBtns = false
    state.editScheduleDialogVisible = true
    getFleetOrderDispatchLineDetailApi({ dispatchId }).then((res: any) => {
      const { data } = res
      const transformedData = transformData(data.lines)

      state.dispatchLineData = JSON.parse(JSON.stringify(transformedData))
      state.dispatchInfo = data
    })

    getFleetOrderDispatchVinDetailApi({ id: dispatchId }).then((res: any) => {
      const { data } = res
      state.scheduleTableData = data
    })
    getDispatchOrderTypeOptions()
  }

  function transformData(
    data: Array<{
      dispatchType: string
      endName: string
      mileage: number
      startName: string
      startFenceId: string
      endFenceId: string
      id: string
      startArrived: string
      endArrived: string
      highwayFee: number
      startEnterTime: string
      endEnterTime: string
      startLeaveTime: string
      endLeaveTime: string
    }>,
  ) {
    const result: Array<{
      area: string
      dispatchType: string
      mileage?: number | string
      showLine: boolean
      startFenceId: string
      id: string
      arrived: string
      highwayFee: number
      startEnterTime: string
      endEnterTime: string
      startLeaveTime?: string
      endLeaveTime?: string
    }> = []

    data.forEach((item, index) => {
      // 添加当前数据的起始区域
      result.push({
        id: item.id,
        area: item.startName,
        arrived: item.startArrived,
        startFenceId: item.startFenceId,
        dispatchType: item.dispatchType,
        mileage: item.mileage,
        highwayFee: item.highwayFee,
        showLine: true,
        startEnterTime: item.startEnterTime,
        endEnterTime: item.startLeaveTime,
      })

      // 如果是最后一项，添加终点区域
      if (index === data.length - 1) {
        result.push({
          id: item.id,
          area: item.endName,
          dispatchType: item.dispatchType,
          showLine: false,
          startFenceId: item.endFenceId,
          arrived: item.endArrived,
          highwayFee: item.highwayFee,
          startEnterTime: item.endEnterTime,
          endEnterTime: item.endLeaveTime,
        })
      }
    })
    return result
  }

  const closeAssignVehiclesDialog = () => {
    state.assignVehiclesFormData.ownId = ''
    state.assignVehiclesDialogVisible = false
  }
  const confirmAssignVehiclesDialog = debounce(async () => {
    // const currentVehicle = state.currentChooseVehicles.find((item: any) => item.id === state.assignVehiclesFormData.ownId)
    // state.assignVehiclesFormData.vehicleNo = currentVehicle?.vehicleNo
    // state.assignVehiclesFormData.vehicleWholeId = currentVehicle?.vehicleWholeId
    // state.assignVehiclesFormData.carrierId = currentVehicle?.carrierId
    // state.assignVehiclesFormData.carrierName = currentVehicle?.carrierName
    // state.assignVehiclesFormData.carrierType = currentVehicle?.carrierType
    // state.assignVehiclesFormData.ownId = currentVehicle?.id
    // state.assignVehiclesFormData.dispatchNo = currentVehicle?.dispatchNo ? currentVehicle?.dispatchNo : undefined
    // state.assignVehiclesLoading = true
    // state.assignVehiclesFormData.ids = state.assignVehiclesFormData.oredrIds
    // delete state.assignVehiclesFormData.oredrIds

    if (state.assignVehiclesFormData.dispatchNo === '') {
      state.assignVehiclesFormData.createDispatchNo = true
    } else {
      state.assignVehiclesFormData.createDispatchNo = false
    }

    const data = Object.assign(state.assignVehiclesFormData, { ids: state.assignVehiclesFormData.orderIds })
    // delete data.orderIds
    if (!state.assignVehiclesFormData.vehicleNo) {
      ElMessage.error('请选择车辆')
    } else {
      // return
      await postvehicleWholeId(data)
        .then((res: any) => {
          const { data } = res
          ElMessage.success('操作成功')
          state.assignVehiclesFormData = {
            carrierId: '',
            carrierName: '',
            createDispatchNo: true,
            dispatchNo: '',
            carrierType: '',
            vehicleNo: '',
            vehicleWholeId: '',
            orderIds: [],
            ids: [],
          }
          emit('closeDialog')
          // state.currentChooseVehicles.forEach((item: any) => {
          //   if (item.id === data.ownId) {
          //     item.dispatchNo = data.dispatchNo
          //     item.dispatchId = data.dispatchId
          //     item.orderList = data.orderList
          //   }
          // })
          // vehicleInfoDialogRef.value.chooseVehicleConfirm(rightVehicleList.value, state.currentChooseVehicles)
          // smartDispatchStore.setSmartDispatchRightVehicleList(state.currentChooseVehicles)
        })
        .finally(() => {
          // state.assignVehiclesLoading = false
        })

      closeAssignVehiclesDialog()
    }
  }, 300)
  const assignVehicles = () => {
    RadioData.value = []
    state.assignVehiclesFormData.vehicleNo = ''
    if (state.assignVehiclesFormData.orderIds.length === 0) {
      ElMessage.error('请先勾选订单')
      return
    }

    state.assignVehiclesDialogVisible = true
  }
  const clearDispatch = (dispatchNo: string, currentVehicle: any) => {
    ElMessageBox.confirm('是否清空当前车辆调度单?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        await getFleetOrderDispatchClearApi({ dispatchNo })
        currentVehicle.dispatchNo = undefined
        currentVehicle.orderList = []
        vehicleInfoDialogRef.value.chooseVehicleConfirm(rightVehicleList.value, state.currentChooseVehicles)
        smartDispatchStore.setSmartDispatchRightVehicleList(state.currentChooseVehicles)
        ElMessage.success('操作成功')
      })
      .catch(() => {})
  }
  const deleteOrderIds = async (dispatchNo: string, currentVehicle: any) => {
    if (state.rightVehicleDeleteOrderIds.length === 0) {
      ElMessage.error('请先勾选订单')
      return
    }
    const params = {
      dispatchNo,
      orderIds: state.rightVehicleDeleteOrderIds,
    }
    const { data } = await postFleetOrderDispatchRemoveVinApi(params)
    currentVehicle.orderList = data.orderList
    // currentVehicle.dispatchNo = data.dispatchNo
    // currentVehicle.dispatchId = data.dispatchId
    vehicleInfoDialogRef.value.chooseVehicleConfirm(rightVehicleList.value, state.currentChooseVehicles)
    smartDispatchStore.setSmartDispatchRightVehicleList(state.currentChooseVehicles)
    ElMessage.success('删除成功')
  }
  const removeVehicle = async (currentVehicle: any) => {
    ElMessageBox.confirm('是否删除当前车辆?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        const index = state.currentChooseVehicles.findIndex((item: any) => item.id === currentVehicle.id)
        state.currentChooseVehicles.splice(index, 1)
        if (currentVehicle.dispatchNo) {
          await getFleetOrderDispatchClearApi({ dispatchNo: currentVehicle.dispatchNo })
        }
        vehicleInfoDialogRef.value.chooseVehicleConfirm(rightVehicleList.value, state.currentChooseVehicles)
        smartDispatchStore.setSmartDispatchRightVehicleList(state.currentChooseVehicles)
        ElMessage.success('删除成功')
      })
      .catch(() => {})
  }
  const removeCarrier = (currentCarrier: any) => {
    ElMessageBox.confirm('是否删除当前承运商?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        const index = rightVehicleList.value.findIndex((item: any) => item.carrierId === currentCarrier.carrierId)
        rightVehicleList.value.splice(index, 1)
      })
      .catch(() => {})
  }

  const searchConfirm = async () => {
    state.queryParams.page = 1
    if (state.queryParams.vehicleNo) {
      state.queryParams.vehicleNo = state.queryParams.vehicleNo.replace(/，/g, ',')
    }
    state.queryParams.startOrderIssueDatetime = state.queryParams.orderIssueDatetime?.[0] ?? undefined
    state.queryParams.endOrderIssueDatetime = state.queryParams.orderIssueDatetime?.[1] ?? undefined
    moreSearch.value = false
    await getOrderDispatchList()
  }

  const cancelOtherInformation = () => {
    state.otherInformationDialogVisible = false
  }
  const confirmOtherInformation = async () => {
    state.dispatchFormData.dispatchId = state.currentDispatchId
    await postAdjustDriverDispatchSubsidyApi(state.dispatchFormData)
    ElMessage.success('操作成功')
    await showScheduleDetail(state.currentDispatchId, state.currentDispatchNo, state.status)
    state.otherInformationDialogVisible = false
  }
  const editIncome = async () => {
    // if (state.status === 'view') return
    state.otherInformationDialogVisible = true
    const { data } = await getDispatchFeeAdjustInfoApi({ id: state.currentDispatchId })
    state.dispatchFormData = data
    if (state.dispatchFormData.fees.length === 0) {
      state.dispatchFormData.fees.push({
        amount: 0,
      })
    }
  }
  const handleOtherInfoDelete = (row: any, $index: number) => {
    state.dispatchFormData.fees.splice($index, 1)
  }
  const handleOtherInfoPlus = (row: any, $index: number) => {
    state.dispatchFormData.fees.splice($index + 1, 0, {
      amount: 0,
    })
  }
  //批量装车
  const batchTransport = async () => {
    state.addressType = 'start'
    updateTimeDialog.visible = true
    //获取当前时间，年月日时分秒
    state.updateTimeFormData.time = getNowDate()
    state.updateTimeFlag = 3
    // ElMessageBox.confirm('确定要批量装车吗?', '提示', {
    //   confirmButtonText: '确定',
    //   cancelButtonText: '取消',
    //   type: 'warning',
    // })
    //   .then(async () => {
    //     const params = {
    //       orderIds: state.currentDispatchOrderIds,
    //       dispatchNo: state.currentDispatchNo,
    //     }
    //     const { data } = await postFleetOrderDispatchBatchLoadingApi(params)
    //     ElMessage.success('批量装车成功')
    //     await showScheduleDetail(state.currentDispatchId, state.currentDispatchNo, state.status)
    //   })
    //   .catch(() => {})
  }
  //批量交车
  const batchDelivery = () => {
    state.addressType = 'end'
    updateTimeDialog.visible = true
    state.updateTimeFlag = 4
    state.updateTimeFormData.time = getNowDate()
    // ElMessageBox.confirm('确定要批量交车吗?', '提示', {
    //   confirmButtonText: '确定',
    //   cancelButtonText: '取消',
    //   type: 'warning',
    // })
    //   .then(async () => {
    //     const params = {
    //       orderIds: state.currentDispatchOrderIds,
    //       dispatchNo: state.currentDispatchNo,
    //     }
    //     const { data } = await postFleetOrderDispatchBatchDeliveryApi(params)
    //     ElMessage.success('批量交车成功')
    //     await showScheduleDetail(state.currentDispatchId, state.currentDispatchNo, state.status)
    //   })
    //   .catch(() => {})
  }
  const batchChange = () => {
    state.batchChangeDispatchNoDialogVisible = true
  }
  const batchTransfer = () => {}
  const batchRemove = () => {
    ElMessageBox.confirm('确定要取消当前VIN调度吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        const params = {
          orderIds: state.currentDispatchOrderIds,
          dispatchNo: state.currentDispatchNo,
        }
        const { data } = await postFleetOrderDispatchVinCancelApi(params)
        ElMessage.success('取消成功')
        await showScheduleDetail(state.currentDispatchId, state.currentDispatchNo, state.status)
      })
      .catch(() => {})
  }
  const statusDispatchOrderTypeChange = async (e: any) => {
    if (state.dispatchInfo.dispatchStatus === '已完成') {
      return
    }
    const params = {
      dispatchNo: state.currentDispatchNo,
      dispatchType: e,
    }
    const { data } = await postFleetOrderDispatchVinViewTypeAlterApi(params)
    ElMessage.success('操作成功')
    await showScheduleDetail(state.currentDispatchId, state.currentDispatchNo, state.status)
  }
  const handleSelectionDispatchDetailChange = (e: any[]) => {
    state.currentDispatchOrderIds = e.map((item: any) => item.orderId)
    state.currentDispatchIds = e.map((item: any) => item.id)
  }
  //查看凭证
  const viewProof = (row: any) => {
    state.showProofDialog = true
    state.currentRowVideo = row.dropImage
  }
  //装车
  const transport = async (row: any) => {
    state.addressType = 'start'
    updateTimeDialog.visible = true
    //获取当前时间，年月日时分秒
    state.updateTimeFormData.time = getNowDate()
    state.updateTimeFlag = 5
    state.currentRow = row

    // ElMessageBox.confirm('确定要装车吗?', '提示', {
    //   confirmButtonText: '确定',
    //   cancelButtonText: '取消',
    //   type: 'warning',
    // }).then(async () => {
    //   const params = {
    //     orderIds: [row.orderId],
    //     dispatchNo: state.currentDispatchNo,
    //   }
    //   const { data } = await postFleetOrderDispatchBatchLoadingApi(params)
    //   ElMessage.success('操作成功')
    //   await showScheduleDetail(state.currentDispatchId, state.currentDispatchNo, state.status)
    // })
  }
  //替换订单
  const replaceOrder = async (row: any) => {
    state.replaceOrderFormData.orderId = row.orderId
    state.replaceOrderFormData.dispatchNo = row.dispatchNo
    replaceOrderDialog.visible = true
  }
  //交车
  const delivery = async (row: any) => {
    state.addressType = 'end'
    updateTimeDialog.visible = true
    state.updateTimeFlag = 6
    state.updateTimeFormData.time = getNowDate()
    state.currentRow = row
    // ElMessageBox.confirm('确定要交车吗?', '提示', {
    //   confirmButtonText: '确定',
    //   cancelButtonText: '取消',
    //   type: 'warning',
    // }).then(async () => {
    //   const params = {
    //     orderIds: [row.orderId],
    //     dispatchNo: state.currentDispatchNo,
    //   }
    //   const { data } = await postFleetOrderDispatchBatchDeliveryApi(params)
    //   ElMessage.success('操作成功')
    //   await showScheduleDetail(state.currentDispatchId, state.currentDispatchNo, state.status)
    // })
  }
  const withdrawDelivery = async (row: any) => {
    ElMessageBox.confirm('确定要撤回交车吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      const { data } = await getFleetOrderDispatchVinCancelDeliveryApi({ id: row.id })
      ElMessage.success('操作成功')
      await showScheduleDetail(state.currentDispatchId, state.currentDispatchNo, state.status)
    })
  }
  const closeProofDialog = () => {
    state.showProofDialog = false
    state.currentRowVideo = ''
  }
  const handleCancelReplaceOrder = () => {
    replaceOrderDialog.visible = false
    //清除表单
    replaceOrderFormRef.value!.resetFields()
  }
  const handleConfirmReplaceOrder = async (formEl: FormInstance | undefined) => {
    //表单校验
    if (!formEl) return
    await formEl
      .validate(async (valid: any) => {
        if (valid) {
          await postFleetOrderDispatchVinReplaceApi(state.replaceOrderFormData)
          ElMessage.success('操作成功')
          replaceOrderDialog.visible = false
          await showScheduleDetail(state.currentDispatchId, state.currentDispatchNo, state.status)
          //清除表单
          replaceOrderFormRef.value!.resetFields()
        }
      })
      .catch((err) => {})
  }
  const addCashBorrow = () => {
    state.isCashBorrowEdit = false
    state.cashBorrowFormData = {}
    state.cashBorrowDialogVisible = true
  }
  const addOilBorrow = (info: any) => {
    state.isOilBorrowEdit = false
    state.oilBorrowFormData = {}
    state.oilBorrowDialogVisible = true
  }
  const addTollBorrow = (info: any) => {
    if (info.show) {
      state.isTollBorrowEdit = false
      state.tollBorrowFormData = {}
      state.tollBorrowDialogVisible = true
    } else {
      ElMessage.error('当前司机车辆没有关联的ETC卡信息，请先维护基础数据，或者进行现金借支')
    }
  }
  const closeCashBorrowDialog = () => {
    state.cashBorrowFormData = {
      loanAmount: 0,
      collideAmount: 0,
      paymentType: 1,
      remark: '',
      dispatchNo: '',
      cardNumber: '',
      accountInfo: '',
      accountBank: '',
    }
    state.cashBorrowPreviewInfo = {}
    state.cashBorrowDialogVisible = false
  }
  const confirmCashBorrowDialog = debounce((formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate(async (valid: any) => {
      if (valid) {
        //冲账金额不能大于可冲金额
        if (state.cashBorrowFormData.collideAmount > (state.cashBorrowPreviewInfo.mayCollideAmount || 0)) {
          ElMessage.error('冲账金额不能大于可冲金额')
          return
        }
        state.cashBorrowFormData.dispatchNo = state.currentDispatchNo
        if (state.cashBorrowFormData.id) {
          if (state.status === 'edit') {
            await postOutFleetLoanCashEditApi(state.cashBorrowFormData)
          } else {
            await postOutFleetLoanCashDraftEditApi(state.cashBorrowFormData)
          }
        } else {
          await postFleetOrderDispatchCashAddApi(state.cashBorrowFormData)
        }
        ElMessage.success('操作成功')
        state.cashBorrowDialogVisible = false
        if (state.status === 'edit') {
          getFleetOrderDispatchCashAddDetailApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
            const { data } = res
            state.cashBorrowTableData = data
          })
        } else {
          getFleetOrderDispatchCashAddDraftListApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
            const { data } = res
            state.cashBorrowTableData = data.rows
          })
        }
      }
    })
  }, 300)
  const handleCashBorrowEdit = (row: any, $index: number) => {
    state.cashBorrowDialogVisible = true
    state.isCashBorrowEdit = true
    if (state.status === 'edit') {
      getOutFleetLoanCashDetailApi({ id: row.id }).then((res: any) => {
        const { data } = res
        state.cashBorrowFormData = data
      })
    } else {
      getFleetOrderDispatchCashAddDraftDetailApi({ id: row.id }).then((res: any) => {
        const { data } = res
        state.cashBorrowFormData = data
      })
    }
  }
  const handleOilBorrowEdit = (row: any, $index: number) => {
    state.oilBorrowDialogVisible = true
    state.isOilBorrowEdit = true
    if (state.status === 'edit') {
      getOutFleetLoanOilFeeDetailApi({ id: row.id }).then((res: any) => {
        const { data } = res
        state.oilBorrowFormData.baseFuelCost = data.baseFuelCost
        state.oilBorrowFormData.baseOilLiters = data.baseOilLiters
        state.oilBorrowFormData.baseOilPrice = data.baseOilPrice
        state.oilBorrowFormData.dispatchNo = data.dispatchNo
        state.oilBorrowFormData.estimatedOilCapacity = data.estimatedOilCapacity
        state.oilBorrowFormData.otherFuelCost = data.otherFuelCost
        state.oilBorrowFormData.otherOilLiters = data.otherOilLiters
        state.oilBorrowFormData.otherOilPrice = data.otherOilPrice
        state.oilBorrowFormData.id = data.id
      })
    } else {
      getFleetOrderDispatchOilFeeDraftDetailApi({ id: row.id }).then((res: any) => {
        const { data } = res
        state.oilBorrowFormData.baseFuelCost = data.baseFuelCost
        state.oilBorrowFormData.baseOilLiters = data.baseOilLiters
        state.oilBorrowFormData.baseOilPrice = data.baseOilPrice
        state.oilBorrowFormData.dispatchNo = data.dispatchNo
        state.oilBorrowFormData.estimatedOilCapacity = data.estimatedOilCapacity
        state.oilBorrowFormData.otherFuelCost = data.otherFuelCost
        state.oilBorrowFormData.otherOilLiters = data.otherOilLiters
        state.oilBorrowFormData.otherOilPrice = data.otherOilPrice

        state.oilBorrowFormData.id = data.id
      })
    }
  }
  const handleTollBorrowEdit = (row: any, $index: number) => {
    state.tollBorrowDialogVisible = true
    state.isTollBorrowEdit = true
    getOutFleetLoanEtcFeeDetailApi({ id: row.id }).then((res: any) => {
      const { data } = res
      state.tollBorrowFormData.remark = data.remark
      state.tollBorrowFormData.id = data.id
      state.tollBorrowFormData.etcItems = [
        {
          etcCardNumber: data.etcNumber,
          cardBalance: data.cardBalance,
          rechargeAmount: data.rechargeAmount,
        },
      ]
    })
  }
  const closeOilBorrowDialog = () => {
    state.oilBorrowDialogVisible = false
  }
  const closeTollBorrowDialog = () => {
    state.tollBorrowDialogVisible = false
  }
  const confirmOilBorrowDialog = debounce((formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate(async (valid: any) => {
      if (valid) {
        state.oilBorrowFormData.dispatchNo = state.currentDispatchNo
        state.oilBorrowFormData.baseFuelCost = state.baseFuelCost
        if (state.oilBorrowFormData.id) {
          if (state.status === 'edit') {
            await postOutFleetLoanOilFeeEditApi(state.oilBorrowFormData)
          } else {
            await postOutFleetLoanOilFeeDraftEditApi(state.oilBorrowFormData)
          }
        } else {
          await postFleetOrderDispatchOilFeeAddApi(state.oilBorrowFormData)
        }
        ElMessage.success('操作成功')
        state.oilBorrowDialogVisible = false
        if (state.status === 'edit') {
          getFleetOrderDispatchOilFeeDetailApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
            const { data } = res
            state.oilBorrowTableData = data
          })
        } else {
          getFleetOrderDispatchOilFeeDraftListApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
            const { data } = res
            state.oilBorrowTableData = data.rows
          })
        }
      }
    })
  }, 300)
  const confirmTollBorrowDialog = debounce((formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate(async (valid: any) => {
      if (valid) {
        const params = {
          dispatchNo: state.currentDispatchNo,
          etcCardNumber: state.tollBorrowFormData.etcItems[0].etcCardNumber,
          rechargeAmount: state.tollBorrowFormData.etcItems[0].rechargeAmount,
          remark: state.tollBorrowFormData.remark,
          id: undefined,
        }
        if (state.tollBorrowFormData.id) {
          params.id = state.tollBorrowFormData.id
          await postOutFleetLoanEtcFeeEditApi(params)
        } else {
          await postFleetOrderDispatchEtcFeeAddApi(params)
        }
        ElMessage.success('操作成功')
        state.tollBorrowDialogVisible = false
        getFleetOrderDispatchEtcFeeDetailApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
          const { data } = res
          state.tollBorrowTableData = data
        })
      }
    })
  }, 300)
  watch(
    () => state.cashBorrowDialogVisible,
    (newVal) => {
      if (newVal) {
        if (!state.isCashBorrowEdit || state.status === 'add') {
          getFleetOrderDispatchCashAddPreview()
        }
      }
    },
  )
  watch(
    () => state.oilBorrowDialogVisible,
    (newVal) => {
      if (newVal) {
        if (!state.isOilBorrowEdit) {
          getFleetOrderDispatchOilFeePreview()
        }
      }
    },
  )
  watch(
    () => state.tollBorrowDialogVisible,
    (newVal) => {
      if (newVal) {
        if (!state.isTollBorrowEdit) {
          getFleetOrderDispatchEtcFeePreview()
        }
      }
    },
  )
  watch([() => state.oilBorrowFormData.baseOilLiters, () => state.oilBorrowFormData.baseOilPrice], () => {
    state.baseFuelCost = state.oilBorrowFormData.baseOilLiters * state.oilBorrowFormData.baseOilPrice || 0
  })
  watch([() => state.oilBorrowFormData.otherOilLiters, () => state.oilBorrowFormData.otherOilPrice], () => {
    state.oilBorrowFormData.otherFuelCost = state.oilBorrowFormData.otherOilLiters * state.oilBorrowFormData.otherOilPrice || 0
  })
  const getFleetOrderDispatchCashAddPreview = () => {
    getFleetOrderDispatchCashAddPreviewApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
      const { data } = res
      state.cashBorrowPreviewInfo = data
      state.cashBorrowFormData.cardNumber = data.cardNumber
      state.cashBorrowFormData.accountInfo = data.accountInfo
      state.cashBorrowFormData.accountBank = data.accountBank
    })
  }
  const getFleetOrderDispatchOilFeePreview = () => {
    getFleetOrderDispatchOilFeePreviewApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
      const { data } = res
      state.oilBorrowPreviewInfo = data
      state.oilBorrowFormData.oilItems = data.oilItems
      // state.oilBorrowFormData.baseOilLiters = data.baseOilLiters
      state.oilBorrowFormData.baseOilPrice = data.baseOilPrice
      // // state.oilBorrowFormData.otherOilLiters = data.otherOilLiters
      state.oilBorrowFormData.otherOilPrice = data.otherOilPrice
      // // state.oilBorrowFormData.remark = data.remark
      // state.oilBorrowFormData.oilItems = data.oilItems
      // state.oilBorrowFormData.estimatedOilCapacity = data.estimatedOilCapacity
    })
  }
  const getFleetOrderDispatchEtcFeePreview = () => {
    getFleetOrderDispatchEtcFeePreviewApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
      const { data } = res
      state.tollBorrowPreviewInfo = data
      state.tollBorrowFormData.etcItems = [
        {
          etcCardNumber: data.etcCardNumber,
          cardBalance: data.cardBalance,
          rechargeAmount: 0,
        },
      ]
      state.tollBorrowFormData.remark = data.remark
    })
  }
  const updatePoint = (type: string) => {
    state.addressType = type
    updateStartPointDialog.visible = true
  }
  const updateTime = async (type: string, row?: any, flag?: number) => {
    state.addressType = type
    state.updateTimeFlag = flag
    state.currentRow = row
    updateTimeDialog.visible = true
    //获取当前时间，年月日时分秒
    state.updateTimeFormData.time = getNowDate()
  }
  const closeUpdateStartPointDialog = () => {
    updatePointFormRef.value!.resetFields()
    updateStartPointDialog.visible = false
  }
  const closeUpdateTimeDialog = () => {
    updateTimeFormRef.value!.resetFields()
    updateTimeDialog.visible = false
  }
  const confirmUpdatePointDialog = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate(async (valid: any) => {
      if (valid) {
        const params = {
          dispatchNo: state.currentDispatchNo,
          address: state.updatePointFormData.address,
          fenceId: state.updatePointFormData.fenceId,
          fenceName: state.updatePointFormData.fenceName,
          ids: state.currentDispatchIds,
        }
        switch (state.addressType) {
          case 'start':
            await postFleetOrderDispatchStartAreaAlterApi(params)
            break
          case 'end':
            await postFleetOrderDispatchEndAreaAlterApi(params)
            break
        }
        ElMessage.success('操作成功')
        await showScheduleDetail(state.currentDispatchId, state.currentDispatchNo, state.status)
        updateStartPointDialog.visible = false
      }
    })
  }
  const confirmUpdateTimeDialog = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate(async (valid: any) => {
      if (valid) {
        let params = {}
        const ids = state.updateTimeFlag === 1 ? [state.currentRow.id] : state.currentDispatchIds
        if (state.updateTimeFlag === 1 || state.updateTimeFlag === 2) {
          if (state.addressType === 'start') {
            params = {
              ids,
              loadTime: state.updateTimeFormData.time,
            }
            const { data } = await postOutFleetOrderDispatchAlterLoadTimeVinApi(params)
            ElMessage.success('操作成功')
          } else {
            params = {
              ids,
              actualDropTime: state.updateTimeFormData.time,
            }
            const { data } = await postOutFleetOrderDispatchAlterActualDropTimeVinApi(params)
            ElMessage.success('操作成功')
          }
        } else if (state.updateTimeFlag === 3) {
          const params = {
            orderIds: state.currentDispatchOrderIds,
            dispatchNo: state.currentDispatchNo,
            loadTime: state.updateTimeFormData.time,
          }
          const { data } = await postFleetOrderDispatchBatchLoadingApi(params)
          ElMessage.success('批量装车成功')
        } else if (state.updateTimeFlag === 4) {
          const params = {
            orderIds: state.currentDispatchOrderIds,
            dispatchNo: state.currentDispatchNo,
            actualDropTime: state.updateTimeFormData.time,
          }
          const { data } = await postFleetOrderDispatchBatchDeliveryApi(params)
          ElMessage.success('批量交车成功')
        } else if (state.updateTimeFlag === 5) {
          const params = {
            loadTime: state.updateTimeFormData.time,
            orderIds: [state.currentRow.orderId],
            dispatchNo: state.currentDispatchNo,
          }
          const { data } = await postFleetOrderDispatchBatchLoadingApi(params)
          ElMessage.success('操作成功')
        } else if (state.updateTimeFlag === 6) {
          const params = {
            actualDropTime: state.updateTimeFormData.time,
            orderIds: [state.currentRow.orderId],
            dispatchNo: state.currentDispatchNo,
          }
          const { data } = await postFleetOrderDispatchBatchDeliveryApi(params)
          ElMessage.success('操作成功')
        }
        await showScheduleDetail(state.currentDispatchId, state.currentDispatchNo, state.status)
        updateTimeDialog.visible = false
      }
    })
  }
  //远程模糊搜索下拉
  const remoteSelectMethod = async (query: string, key: string) => {
    if (query) {
      fuzzySelectLoading.value = true
      const params = {
        name: query,
        page: 1,
        limit: defaultSettings.globalLimit,
      }
      const { data } = await getFleetOrderDispatchGeofenceSearchApi(params)
      geoFenceList.value = data.rows
      fuzzySelectLoading.value = false
    }
  }
  const handleSelectFence = (id: string) => {
    const row = geoFenceList.value.find((item: any) => item.id === id)
    state.updatePointFormData.address = row.address
    state.updatePointFormData.fenceId = row.id
    state.updatePointFormData.fenceName = row.name
  }
  const handleCashBorrowDelete = (row: any) => {
    ElMessageBox.confirm('确定删除该条记录吗？').then(async () => {
      await deleteOutFleetLoanCashDraftApi({ id: row.id })
      ElMessage.success('操作成功')
      //获取列表
      getFleetOrderDispatchCashAddDraftListApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
        const { data } = res
        state.cashBorrowTableData = data.rows
      })
    })
  }
  const handleOilBorrowDelete = (row: any) => {
    ElMessageBox.confirm('确定删除该条记录吗？').then(async () => {
      await deleteOutFleetLoanOilFeeDraftApi({ id: row.id })
      ElMessage.success('操作成功')
      //获取列表
      getFleetOrderDispatchOilFeeDraftListApi({ dispatchNo: state.currentDispatchNo }).then((res: any) => {
        const { data } = res
        state.oilBorrowTableData = data.rows
      })
    })
  }
  const showMenuDialog = (menu: MenuVO) => {
    emit('showMenuDialog', null, null, menu)
  }
  defineExpose({
    state,
    showScheduleDetail,
  })

  // 媒体预览相关
  const mediaDialogVisible = ref(false)
  const mediaDialogTitle = ref('')
  const currentMedia = ref<any[]>([])
  const mediaType = ref<'image' | 'video'>('image')

  const showMediaDialog = (media: any[], type: 'image' | 'video') => {
    currentMedia.value = media
    mediaType.value = type
    mediaDialogTitle.value = type === 'image' ? '图片预览' : '视频预览'
    mediaDialogVisible.value = true
  }

  // 播放视频
  const playVideo = (url: string) => {
    videoDialogVisible.value = true
    nextTick(() => {
      if (videoPlayer.value) {
        videoPlayer.value.src = url
        videoPlayer.value.play()
      }
    })
  }
  const cancelVinDialogComponent = ref()
  const addVin = () => {
    cancelVinDialogComponent.value.state.dialog.visible = true
    cancelVinDialogComponent.value.state.dialog.title = '添加VIN'
  }
  const refreshDetail = async () => {
    await showScheduleDetail(state.currentDispatchId, state.currentDispatchNo, state.status)
  }
</script>
<style scoped lang="scss">
  :deep(.el-divider--horizontal) {
    margin: 16px 0 !important;
  }

  .title {
    font-size: 18px;
    font-weight: bold;
  }

  .content {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .my-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 16px;
  }

  .query-container {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    padding: 10px;
    /* width: 100%; */
    z-index: 8;
    background: #fff;
    border: 1px solid #dedfe6;
    border-radius: 4px;
    /* overflow: hidden; */
  }

  .query-box {
    display: flex;
    align-items: start;
    justify-content: space-between;
  }

  .query-container-left {
    flex: 1;
  }

  .query-container-right {
    padding: 0 10px;
  }

  .query-container :deep(.el-form-item) {
    margin-bottom: 10px;
    border: 1px solid #dedfe6;
    border-radius: 2px;
  }

  .query-container :deep(.el-input__wrapper),
  .query-container :deep(.el-select__wrapper),
  .query-container :deep(.el-date-editor.el-input) {
    border-radius: 0px;
    box-shadow: none;
    min-height: 25px;
  }

  .query-container :deep(.el-form-item__label) {
    background: #f0f0f0;
    color: #333;
    height: auto;
    align-items: center;
  }

  .query-box :deep(.el-select__selection) {
    flex-wrap: nowrap !important;
    margin-right: 45px !important;
  }

  .query-box :deep(.el-select__selected-item) {
    flex-wrap: nowrap !important;
  }

  .query-box :deep(.el-select__input) {
    min-width: 40px !important;
    max-width: 40px !important;
  }

  .media-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding: 20px;
  }

  .video-item {
    border: 1px solid #eee;
    border-radius: 4px;
    overflow: hidden;
    transition: all 0.3s;

    &:hover {
      transform: scale(1.02);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }

  /* 视频缩略图样式 */
  .video-thumbnail {
    width: 50px;
    height: 50px;
    background-color: #f5f7fa;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      background-color: #e6f7ff;

      .play-icon {
        transform: scale(1.2);
        color: var(--el-color-primary);
      }
    }

    .play-icon {
      font-size: 24px;
      color: #909399;
      transition: all 0.3s;
    }
  }
</style>
