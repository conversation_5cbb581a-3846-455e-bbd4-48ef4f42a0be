<template>
  <el-dialog v-model="state.vehicleInfoDialogVisible" :close-on-click-modal="false" :draggable="true" :show-close="false" title="整车信息" width="80%">
    <el-card width="100%">
      <div class="query-container" :style="{ height: moreSearch ? 'auto' : '48px' }">
        <div class="query-box">
          <div class="query-container-left">
            <el-form ref="ruleFormRef" :inline="true" :model="state.vehicleQueryParams">
              <el-row :gutter="24">
                <el-col :span="6">
                  <el-form-item label="目的地" style="width: 100%" label-width="70px">
                    <el-input v-model="state.vehicleQueryParams!.targetArea" placeholder="请输入目的地" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="客户" style="width: 100%" label-width="70px">
                    <el-input v-model="state.vehicleQueryParams!.customerName" placeholder="请输入客户" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="承运商" style="width: 100%" label-width="70px">
                    <el-input v-model="state.vehicleQueryParams!.carrierName" placeholder="请输入承运商" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="车牌号" style="width: 100%" label-width="70px">
                    <!-- <el-input v-model="state.vehicleQueryParams!.vehicleNo" style="width: 100%" placeholder="请输入车牌号" /> -->
                    <el-input
                      v-model="state.vehicleQueryParams!.vehicleNo"
                      style="width: 100%"
                      clearable
                      @click="openTextBatch()"
                      @clear="emptyTextBatch()"
                      placeholder="请输入车牌号，多个用,隔开"
                    />
                    <div v-show="showTextBatch" style="width: 100%; position: absolute; top: 0px; left: 0px; z-index: 9999">
                      <TextareaComponent
                        ref="TextareaRef"
                        @arraySent="handleArrayReceived"
                        :showTextBatch="showTextBatch"
                        :closeTextBatch="closeTextBatch"
                        targetField="请输入车牌号，多个用,隔开"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24" v-if="moreSearch">
                <el-col :span="6">
                  <el-form-item label="车辆状态" style="width: 100%" label-width="70px">
                    <el-input v-model="state.vehicleQueryParams!.vehicleStatus" placeholder="请输入车辆状态" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="装载位数" style="width: 100%" label-width="70px">
                    <el-input v-model="state.vehicleQueryParams!.loadTotalVin" placeholder="请输入装载位数" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="停留天数" style="width: 100%" label-width="70px">
                    <el-input v-model="state.vehicleQueryParams!.stayDays" placeholder="请输入停留天数" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="预达时间" style="width: 100%" label-width="70px">
                    <el-date-picker
                      v-model="state.vehicleQueryParams.targetEstimateTime"
                      type="daterange"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      placeholder="请选择预达时间"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      clearable
                      :shortcuts="ShortcutsList"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24" v-if="moreSearch">
                <el-col :span="6">
                  <el-form-item label="司机名称" style="width: 100%" label-width="70px">
                    <el-input v-model="state.vehicleQueryParams!.driverOneName" placeholder="模糊搜索" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="司机联系方式" style="width: 100%" label-width="90px">
                    <el-input v-model="state.vehicleQueryParams!.driverOneMobile" placeholder="模糊搜索" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="承运商类型" style="width: 100%" label-width="90px">
                    <el-select v-model="state.vehicleQueryParams!.carrierType" placeholder="请选择承运商类型" style="width: 100%">
                      <el-option v-for="(item, index) in state.carrierTypeList" :key="index" :label="item.name" :value="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div class="query-container-right" style="width: 180px">
            <div style="display: flex; justify-content: start; align-items: center; padding-top: 1px">
              <el-button size="small" round @click="moreClick">更多</el-button>
              <el-button @click="searchVehicleConfirm" type="primary">筛选</el-button>
              <el-button @click="searchVehicleReset">重置</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="mt-10px">
        <vxe-single-table
          ref="tableRef"
          :selectStatus="selectStatus"
          :table-config="tableConfig"
          :tableData="state.vehicleTableData"
          height="500px"
          @getSelectRowIds="handleSelectionChange"
          @radioChangeEvent="handleSelectionChange"
        />
        <pagination
          v-if="state.vehicleTotal >= state.vehicleQueryParams.limit"
          v-model:limit="state.vehicleQueryParams.limit"
          v-model:page="state.vehicleQueryParams.page"
          v-model:total="state.vehicleTotal"
          @pagination="_pagination"
        />
      </div>
    </el-card>

    <template #footer>
      <el-button @click="cancelVehicleInfoDialog">关 闭</el-button>
      <el-button type="primary" @click="chooseVehicleConfirm">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { getCommonOptionDistributeSelectApi, getFleetVehicleDispatchListApi } from '@/api/businessManagement'
  import defaultSettings from '@/settings'
  import { ShortcutsList } from '@/utils/index'
  import type { VxeColumnPropTypes } from 'vxe-table'
  import TextareaComponent from '@/components/TextcreaComponent/index.vue'
  import VxeSingleTable from '@/components/VxeTableComponent/singleTable.vue'
import { log } from 'console'

  const emit = defineEmits(['cancelVehicleInfoDialog', 'chooseVehicleConfirm', 'currentChooseVehicles'])
  const tableRef = ref<any>(null)
  const props = defineProps({
    dialog: {
      type: Boolean,
      default: false,
    },
    selectStatus: {
      type: String as PropType<VxeColumnPropTypes.Type>,
      default: 'checkbox',
    },
    assignVehiclesFormData: {
      type: Object,
      default: () => {},
    },
  })
  const state = reactive({
    tableLoading: false, //表格加载状态
    carrierTypeList: [] as SelectVO[],
    tableSelect: [] as any[], //左侧表格选中的行
    tableHTSelect: [] as any[], //左侧表格选中的行
    currentChooseVehicles: [],
    currentVehicles: [], //上页面右侧的车辆
    vehicleQueryParams: {
      targetArea: '',
      currentArea: '',
      customerName: '',
      carrierName: '',
      riskWarning:'',
      vehicleNo: '',
      vehicleStatus: '',
      loadTotalVin: '',
      stayDays: '',
      targetEstimateTime: [] as any[],
      page: 1,
      limit: defaultSettings.globalLimit,
      driverOneName: '',
      driverOneMobile: '',
      carrierType: '',
    },
    vehicleTableData: [],
    vehicleTotal: 0,
    vehicleInfoDialogVisible: false,
  })
  const tableConfig = reactive<TableConfig>({
    showHandleSelection: true,
    tableItem: [
      {
        name: 'carrierName',
        label: '承运商',
        width: '100px',
      },
      {
        name: 'carrierType',
        label: '承运商类型',
        width: '120px',
      },
      {
        name: 'vehicleNo',
        label: '车牌号',
        width: '100px',
      },
      {
        name: 'plateColor',
        label: '车牌颜色',
        width: '100px',
      },
      {
        name: 'vehicleStatus',
        label: '车辆状态',
        width: '100px',
      },
      {
        name: 'driverOneName',
        label: '司机',
        width: '100px',
      },
      {
        name: 'driverOneMobile',
        label: '联系方式',
        width: '100px',
      },
      {
        name: 'loadTotalVin',
        label: '装载位数',
        width: '100px',
      },
      {
        name: 'currentArea',
        label: '当前车辆位置',
        width: '150px',
      },
      {
        name: 'originDispatchNo',
        label: '当前调度单',
        width: '180px',
      },
      {
        name: 'targetArea',
        label: '目的地',
        width: '150px',
      },
      {
        name: 'targetEstimateTime',
        label: '目的地预达',
        width: '150px',
      },
      {
        name: 'stayDays',
        label: '停留天数',
        width: '100px',
      },
      {
        name: 'reportStatus',
        label: '报到状态',
        width: '100px',
      },
      {
        name: 'reportStartTime',
        label: '报到时间',
        width: '120px',
      },
      {
        name: 'reportGeofenceName',
        label: '报到围栏',
        width: '100px',
      },
      {
        name: 'reportBaseName',
        label: '报到基地',
        width: '100px',
      },
      {
        name: 'reportWaitTime',
        label: '等待时长',
        width: '100px',
      },
    ],
  })
  watch(
    () => state.vehicleInfoDialogVisible,
    (newVal) => {
      if (newVal) {
        getCarrierTypeList()
      }
    },
  )

  //全选改全不选
  const selectAll = () => {
    tableRef.value.clearSelection()
  }
  // 选中的行
  const handleSelectionChange = (ids: Array<string>, val: any[]) => {
    handleSelectionVehicleChange(val)
  }
  const getCarrierTypeList = async () => {
    const res = await getCommonOptionDistributeSelectApi({
      selectType: 'fleetBelong',
      simple: true,
      value: 'name',
      needAll: false,
    })
    const { data } = res
    state.carrierTypeList = data as SelectVO[]
  }
  const searchVehicleConfirm = async () => {
    state.vehicleQueryParams.page = 1
    const params = {
      ...state.vehicleQueryParams,
      targetEstimateTime: undefined,
      startTargetEstimateTime: state.vehicleQueryParams.targetEstimateTime[0],
      endTargetEstimateTime: state.vehicleQueryParams.targetEstimateTime[1],
    }
    moreSearch.value = false
    await getFleetVehicleDispatchList(params)
  }
  const getFleetVehicleDispatchList = async (params: any) => {
    try {
      state.tableLoading = true
      const { data } = await getFleetVehicleDispatchListApi(params)
      if (props.assignVehiclesFormData) {
        const currentVehicle = data.rows.find((item: any) => item.vehicleNo === props.assignVehiclesFormData.vehicleNo)
        tableRef.value.setSelectRow(currentVehicle)
      }
      //将车辆上的所有历史调度单号清空，防止选择的车辆把历史订单号带过去
      data.rows.map((item: any) => {
        item.originDispatchNo = item.dispatchNo
        // item.dispatchNo = ''
      })
      state.vehicleTableData = data.rows

      console.log(state.vehicleTableData, ' state.vehicleTableData')

      const _currentChooseVehicles = JSON.parse(JSON.stringify(state.currentChooseVehicles))
      //过滤出state.currentChooseVehicles在state.vehicleTableData中的车辆
      state.currentVehicles = state.vehicleTableData.filter((item: any) => _currentChooseVehicles.some((chooseItem: any) => chooseItem.id === item.id))
      //勾选currentVehicles
      // state.currentVehicles.forEach((item: any) => {
      //   setTimeout(() => {
      //     tableRef.value.toggleRowSelection(item, true)
      //   })
      // })
      state.vehicleTotal = data.total
      state.tableLoading = false
    } catch (err) {
      console.log(err, '2133')
      state.tableLoading = false
    }
  }
  const handleSelectionVehicleChange = (e: any) => {
    console.log('handleSelectionVehicleChange', e)
    //清除当前选择车辆的dispatchNo
    e.forEach((item: any) => {
      item.dispatchNo = ''
    })
    state.currentChooseVehicles = e
    // emit('currentChooseVehicles', state.currentChooseVehicles)
  }
  /**
   * 分页
   */
  const _pagination = async () => {
    await getFleetVehicleDispatchList(state.vehicleQueryParams)
  }

  const searchVehicleReset = async () => {
    state.vehicleQueryParams = {
      targetArea: '',
      currentArea: '',
      customerName: '',
      carrierName: '',
      riskWarning:'',
      vehicleNo: '',
      vehicleStatus: '',
      loadTotalVin: '',
      stayDays: '',
      targetEstimateTime: [],
      page: 1,
      limit: defaultSettings.globalLimit,
      driverOneName: '',
      driverOneMobile: '',
      carrierType: '',
    }
    moreSearch.value = false
    await getFleetVehicleDispatchList(state.vehicleQueryParams)
  }
  const cancelVehicleInfoDialog = () => {
    //遍历state.currentChooseVehicles，根据carrierId相同项重组数据
    state.currentChooseVehicles = JSON.parse(JSON.stringify(state.currentVehicles))

    console.log(state.currentChooseVehicles,'state.currentChooseVehicles');
    
    const newVehicleTableData = state.currentChooseVehicles.reduce(
      (
        acc: Array<{
          carrierId: string
          carrierName: string
          carrierType: string
          riskWarning:string
          children: Array<{ id: string; carrierId: string; carrierName: string }>
        }>,
        item: { id: string; carrierId: string; riskWarning: string; carrierName: string; carrierType: string; orderList: any[] },
      ) => {
        const carrierId = item.carrierId
        const carrierName = item.carrierName
        const carrierType = item.carrierType
        const riskWarning = item.riskWarning
        const groupIndex = acc.findIndex((group) => group.carrierId === carrierId)
        if (groupIndex !== -1) {
          acc[groupIndex].children.push(item)
        } else {
          acc.push({ carrierId, carrierName, carrierType,riskWarning, children: [item] })
        }
        return acc
      },
      [],
    )
    emit('chooseVehicleConfirm', newVehicleTableData, state.currentChooseVehicles)
  }
  const chooseVehicleConfirm = () => {
    //遍历state.currentChooseVehicles，根据carrierId相同项重组数据
    console.log(state.currentChooseVehicles,'state.currentChooseVehicles');
    const newVehicleTableData = state.currentChooseVehicles.reduce(
      (
        acc: Array<{
          carrierId: string
          carrierName: string
          carrierType: string
          riskWarning:string
          children: Array<{ id: string; carrierId: string; carrierName: string }>
        }>,
        item: { id: string; carrierId: string; riskWarning: string; carrierName: string; carrierType: string; orderList: any[] },
      ) => {
        const carrierId = item.carrierId
        const carrierName = item.carrierName
        const carrierType = item.carrierType
        const riskWarning = item.riskWarning
        const groupIndex = acc.findIndex((group) => group.carrierId === carrierId)
        if (groupIndex !== -1) {
          acc[groupIndex].children.push(item)
        } else {
          acc.push({ carrierId, carrierName, carrierType,riskWarning, children: [item] })
        }
        return acc
      },
      [],
    )
    emit('chooseVehicleConfirm', newVehicleTableData, state.currentChooseVehicles)
  }

  const showTextBatch = ref(false)
  const TextareaRef = ref()
  const openTextBatch = () => {
    showTextBatch.value = true
  }

  const closeTextBatch = (array: any) => {
    showTextBatch.value = false
  }

  const handleArrayReceived = (array: any) => {
    state.vehicleQueryParams!.vehicleNo = Object.values(array).join(',')
  }

  const emptyTextBatch = () => {
    TextareaRef.value.list = []
  }
  const moreSearch = ref(false)
  const moreClick = () => {
    moreSearch.value = !moreSearch.value
  }
  defineExpose({
    state,
    getFleetVehicleDispatchList,
    chooseVehicleConfirm,
  })
</script>
<style scoped lang="scss">
  :deep(.el-divider--horizontal) {
    margin: 16px 0 !important;
  }

  .title {
    font-size: 18px;
    font-weight: bold;
  }

  .content {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .my-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 16px;
  }

  .query-container {
    top: 0px;
    left: 0px;
    right: 0px;
    padding: 10px 10px 0;
    /* width: 100%; */
    z-index: 8;
    background: #fff;
    border: 1px solid #dedfe6;
    border-radius: 4px;
    /* overflow: hidden; */
  }

  .query-box {
    display: flex;
    align-items: start;
    justify-content: space-between;
  }

  .query-container-left {
    flex: 1;
  }

  .query-container-right {
    padding: 0 20px;
  }

  .query-container :deep(.el-form-item) {
    margin-bottom: 10px;
    border: 1px solid #dedfe6;
    border-radius: 2px;
  }

  .query-container :deep(.el-input__wrapper),
  .query-container :deep(.el-select__wrapper),
  .query-container :deep(.el-date-editor.el-input) {
    border-radius: 0px;
    box-shadow: none;
    min-height: 25px;
  }

  .query-container :deep(.el-form-item__label) {
    background: #f0f0f0;
    color: #333;
    height: auto;
    align-items: center;
  }

  .query-box :deep(.el-select__selection) {
    flex-wrap: nowrap !important;
    margin-right: 45px !important;
  }

  .query-box :deep(.el-select__selected-item) {
    flex-wrap: nowrap !important;
  }

  .query-box :deep(.el-select__input) {
    min-width: 40px !important;
    max-width: 40px !important;
  }

  ::v-deep .seltAllbtnDis .cell {
    visibility: hidden;
  }
</style>
