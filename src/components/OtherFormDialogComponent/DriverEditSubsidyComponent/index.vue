<!--
 * @Author: llm
 * @Date: 2024-12-06 14:55:42
 * @LastEditors: llm
 * @LastEditTime: 2025-04-07 15:28:21
 * @Description: 修改费用
-->
<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="60%" :draggable="true" :close-on-click-modal="false" @close="cancel()">
    <el-scrollbar max-height="60vh" class="formClass">
      <el-table :data="state.tableData" :span-method="objectSpanMethod" border style="width: 100%">
        <el-table-column prop="startFenceName" label="关联路线">
          <template #default="scope">
            {{ scope.row.startFenceName + ' - ' + scope.row.endFenceName }}
          </template>
        </el-table-column>
        <el-table-column prop="dispatchType" label="类型" />
        <el-table-column prop="mileage" label="结算里程" />
        <el-table-column prop="adjustMileage" label="修改后结算里程">
          <template #default="scope">
            <el-input-number v-model="scope.row.adjustMileage" size="small" :precision="2" @change="handleChange(scope.row)" />
          </template>
        </el-table-column>
      </el-table>
      <el-form label-width="80px" :inline="true" :model="state.formData" style="margin-top: 20px">
        
        <el-row>
          <el-col :span="6">
            <el-form-item label="调整补贴" prop="adjustAmount">
              <el-input-number v-model="state.formData.adjustAmount" size="default" :precision="2" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="扣款" prop="deduction">
              <el-input-number v-model="state.formData.deduction" :min="0" size="default" :precision="2" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="state.formData.remark" style="width: 360px" placeholder="请填写备注" show-word-limit type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel()">取消</el-button>
        <el-button type="primary" @click="confirm()">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
  import { updateDispatchSubsidyApi, getDispatchSubsidyApi } from '@/api/salaryFormulaApi/index'
  const emit = defineEmits(['closeDialog', 'subsidyConfirmSubmit'])
  const state = reactive<any>({
    formData: {
      items: [],
      adjustAmount: 0,
      deduction: 0,
      remark: '',
    },
    tableData: [],
  })

  const dispatchId = ref('')
  const props = defineProps({
    /**
     * 弹窗属性
     */
    dialog: {
      require: true,
      type: Object as any,
      default: () => {
        return {
          visible: false,
          title: '修改费用',
        }
      },
    },
  })

  // 修改费用
  const handleChange = (data: any) => {
    if (state.formData.items.length > 0) {
      state.formData.items.map((_item: { dispatchLineId: any; items: any[] }) => {
        if (_item.dispatchLineId === data.dispatchLineId) {
          if (_item.items && _item.items.length > 0) {
            _item.items.map((_subItem: { adjustMileage: any; dispatchType: any }) => {
              if (_subItem.dispatchType === data.dispatchType) {
                _subItem.adjustMileage = data.adjustMileage
              }
            })
          }
        }
      })
    }
  }

  // 合并数据
  const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: any) => {
    if (columnIndex === 0) {
      // 第一列需要合并
      if (row.rowType === 'first') {
        return { rowspan: 2, colspan: 1 }
      } else {
        return { rowspan: 0, colspan: 0 }
      }
    }
  }

  function cancel() {
    state.formData.deduction = 0
    emit('closeDialog')
  }

  // 提交表单
  const confirm = () => {
    const params = {
      ...state.formData,
      dispatchId: dispatchId.value,
    }
    updateDispatchSubsidyApi(params).then((response) => {
      ElMessage.success('修改成功')
      emit('subsidyConfirmSubmit')
      cancel()
    })
    //
  }

  // 获取数据
  const getDetail = async (id: string) => {
    const { data } = await getDispatchSubsidyApi(id, {})
    state.tableData = data.items
    const result: any[] = []
    data.items.forEach((item: any) => {
      item.items.forEach((subItem: any, index: any) => {
        result.push({
          ...item,
          ...subItem,
          // 添加分组标识
          groupId: item.dispatchLineId,
          rowType: index === 0 ? 'first' : 'follow',
        })
      })
    })
    state.tableData = result

    state.formData.adjustAmount = data.adjustSubsidy ? data.adjustSubsidy : 0
    state.formData.deduction = data.deduction ? data.deduction : 0
    state.formData.remark = data.remark
    if (data.items) {
      state.formData.items = []
      data.items.map((item: any) => {
        if (item.dispatchLineId) {
          state.formData.items.push({
            dispatchLineId: item.dispatchLineId,
            adjustMileage: item.adjustMileage,
            items: item.items,
          })
        }
      })
    }
  }
  defineExpose({
    dispatchId,
    getDetail,
  })
</script>
