<!--
 * @Author: llm
 * @Date: 2025-02-21 11:11:17
 * @LastEditors: llm
 * @LastEditTime: 2025-04-01 10:27:28
 * @Description:
-->
<template>
  <div>
    <el-dialog
      @close="closeDialog"
      v-model="state.dialogVisible.visible"
      :close-on-click-modal="false"
      draggable
      lock-scroll
      :title="state.dialogVisible.title"
      @closed="closeDialog"
      width="80%"
    >
      <el-scrollbar height="60vh" v-loading="state.loading">
        <div class="h-100%" v-if="state.items && state.items.length > 0">
          <el-form ref="formRef" :model="state.formData">
            <el-row :gutter="10">
              <el-col :span="item.colSpan" v-for="(item, index) in state.items" :key="index">
                <el-row :gutter="10" v-if="item.children">
                  <el-col :span="child.colSpan" v-for="(child, childIndex) in item.children" :key="childIndex">
                    <div class="mb-10px">
                      <el-card shadow="never" body-style="padding:0">
                        <div class="p-5px bg-#f5f7fa">
                          <el-text class="mx-1" v-if="child.title">{{ child.title }}</el-text>
                        </div>
                        <div v-if="child.type === 'object'">
                          <el-row :gutter="0">
                            <template v-for="(_item, _index) in child.dataColumn" :key="_index">
                              <el-col :span="_item.form?.colSpan || 24" v-if="_item.form?.canShow">
                                <el-form-item :label="_item.form?.label" :label-width="_item.form?.labelWidth || '80px'">
                                  <div class="px-10px text-ellipsis overflow-hidden whitespace-nowrap">
                                    <template v-if="_item.form?.canEdit">
                                      <el-input
                                        style="width: 100%"
                                        placeholder="请输入"
                                        :type="_item.form?.type"
                                        v-model="state.formData[_item.form?.name!]"
                                        size="small"
                                        v-if="_item.type === 'text'"
                                      >
                                        <template #append v-if="_item.form?.unit">
                                          <span>{{ _item.form?.unit }}</span>
                                        </template>
                                      </el-input>
                                    </template>
                                    <div v-else>
                                      <template v-if="_item.type === 'viewPictures'">
                                        <el-image
                                          v-for="(item, index) in state.formData[_item.form?.name!] || []"
                                          :key="index"
                                          style="width: 100px; height: 100px"
                                          :src="item.url"
                                          :zoom-rate="1.2"
                                          :max-scale="7"
                                          :min-scale="0.2"
                                          :preview-src-list="
                                            state.formData[_item.form?.name!] ? state.formData[_item.form?.name!].map((item: UploadUserFile) => item.url) : []
                                          "
                                          show-progress
                                          :initial-index="index"
                                          fit="cover"
                                        />
                                      </template>

                                      <div v-else class="text-ellipsis overflow-hidden whitespace-nowrap">
                                        <el-tooltip effect="dark" :content="child.object[_item.form?.name!]" placement="bottom">
                                          <span>{{ child.object[_item.form?.name!] || '-' }}</span>
                                          <span v-if="_item?.form?.unit">{{ _item.form?.unit }}</span>
                                        </el-tooltip>
                                      </div>
                                    </div>
                                  </div>
                                </el-form-item>
                              </el-col>
                            </template>
                          </el-row>
                        </div>
                      </el-card>
                    </div>
                  </el-col>
                </el-row>
                <div v-else class="mb-10px h-100%">
                  <el-card shadow="never" style="height: calc(100% - 10px)" body-style="padding:0">
                    <div class="p-5px bg-#f5f7fa">
                      <el-text class="mx-1 my-1" v-if="item.title">{{ item.title }}</el-text>
                    </div>
                    <div v-if="item.type === 'object'">
                      <el-row :gutter="0" style="width: 100%">
                        <el-col :span="_item.form?.colSpan || 24" v-for="(_item, _index) in item.dataColumn" :key="_index">
                          <el-form-item v-if="_item.form?.canShow" :label="_item.form?.label" :label-width="_item.form?.labelWidth || '80px'">
                            <div class="w-100% px-10px text-ellipsis overflow-hidden whitespace-nowrap">
                              <div class="w-100% text-ellipsis overflow-hidden whitespace-nowrap" v-if="item.object">
                                <!-- 可编辑状态 -->
                                <template v-if="_item.form?.canEdit">
                                  <!-- 文本输入框 -->
                                  <el-input
                                    v-if="_item.type === 'text'"
                                    v-model="state.formData[_item.form?.name!]"
                                    :type="_item.form?.type"
                                    placeholder="请输入"
                                    size="small"
                                    style="width: 100%"
                                  >
                                    <template v-if="_item.form?.unit" #append>
                                      <span>{{ _item.form?.unit }}</span>
                                    </template>
                                  </el-input>
                                  <!-- 图片上传 -->
                                  <UploadImageComponent
                                    v-else-if="_item.type === 'imageThumbnail'"
                                    :ref="`uploadImageRef_${_item.form?.name}`"
                                    :limit="9"
                                    :multiple="true"
                                    @fileData="(fileList: any[]) => handleImageUpload(fileList, _item.form?.name!)"
                                  />
                                  <!-- 其他类型显示文本 -->
                                  <el-text v-else>
                                    {{ item.object[_item.form?.name!] || '-' }}
                                    <span v-if="_item?.form?.unit">{{ _item.form?.unit }}</span>
                                  </el-text>
                                </template>
                                <!-- 只读状态 -->
                                <template v-else>
                                  <!-- 图片缩略图显示 -->
                                  <template v-if="_item.type === 'imageThumbnail'">
                                    <el-image
                                      v-for="(v, i) in item.object[_item.form?.name!] || []"
                                      :key="i"
                                      :initial-index="i"
                                      :max-scale="7"
                                      :min-scale="0.2"
                                      :preview-src-list="
                                        item.object[_item.form?.name!] ? item.object[_item.form?.name!].map((item: UploadUserFile) => item.url) : []
                                      "
                                      :src="v.url"
                                      :zoom-rate="1.2"
                                      fit="cover"
                                      show-progress
                                      style="width: 100px; height: 100px; padding: 5px"
                                    />
                                  </template>
                                  <!-- 视频缩略图 -->
                                  <template v-else-if="_item.type === 'videoThumbnail'">
                                    <el-link v-if="item.object[_item.form?.name!]" type="primary" @click="showVideo(item, _item.form?.name!)">查看 </el-link>
                                  </template>
                                  <!-- 操作链接 -->
                                  <el-link v-else-if="_item.type === 'operation'" type="primary" @click="linkTo(item, _item)"
                                    >{{ item.object[_item.form?.name!] || '-' }}
                                  </el-link>
                                  <!-- 默认文本显示 -->
                                  <el-text v-else>
                                    {{ item.object[_item.form?.name!] || '-' }}
                                    <span v-if="_item?.form?.unit">{{ _item.form?.unit }}</span>
                                  </el-text>
                                </template>
                              </div>
                              <div v-else>
                                <el-input
                                  v-if="_item.form?.canEdit"
                                  v-model="state.formData[_item.form?.name!]"
                                  :type="_item.form?.type"
                                  placeholder="请输入"
                                  size="small"
                                  style="width: 100%"
                                >
                                  <template v-if="_item.form?.unit" #append>
                                    <span>{{ _item.form?.unit }}</span>
                                  </template>
                                </el-input>
                              </div>
                            </div>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                    <div v-else-if="item.type === 'auditFlow'" class="pt-10px">
                      <el-scrollbar height="50vh">
                        <div class="p-10px">
                          <el-timeline style="width: 100%">
                            <el-timeline-item color="#0bbd87" :hide-timestamp="true" placement="top" v-for="(_item, _index) in item.list" :key="_index">
                              <el-card shadow="never" body-style="padding: 10px">
                                <div v-if="_item.operationDesc === '开始'" class="flex flex-col items-start justify-between">
                                  <h4>申请人 （{{ _item.userName }}） 发起</h4>
                                  <el-divider />
                                  <p>发起时间：{{ _item.operationTime }}</p>
                                </div>
                                <div v-else-if="_item.operationDesc === '结束'" class="flex flex-col items-start justify-between">
                                  <div class="flex justify-between items-center w-100%">
                                    <div>
                                      <h4>{{ _item.userName }}</h4>
                                    </div>
                                    <div class="flex items-center">
                                      <el-icon color="var(--el-color-success)"><SuccessFilled /></el-icon>
                                      <el-text size="small">完成时间：{{ _item.operationTime }}</el-text>
                                    </div>
                                  </div>
                                  <el-divider />
                                  <p v-if="_item.remark">审批意见：{{ _item.remark }}</p>
                                </div>
                                <div v-else class="flex flex-col items-start justify-between">
                                  <div class="flex justify-between items-center w-100%">
                                    <div>
                                      <h4>{{ _item.userName }}</h4>
                                    </div>
                                    <div class="flex items-center">
                                      <el-icon color="var(--el-color-success)"><SuccessFilled /></el-icon>
                                      <el-text size="small">完成时间：{{ _item.operationTime }}</el-text>
                                    </div>
                                  </div>
                                  <el-divider />
                                  <p>审批意见：{{ _item.remark || '-' }}</p>
                                </div>
                              </el-card>
                            </el-timeline-item>
                          </el-timeline>
                        </div>
                      </el-scrollbar>
                    </div>
                    <div v-else-if="item.type === 'list'">
                      <el-table
                        :data="item.list! || []"
                        style="width: 100%"
                        :border="true"
                        size="small"
                        show-overflow-tooltip
                        :header-cell-style="{ backgroundColor: '#f5f7fa', textAlign: 'center' }"
                      >
                        <el-table-column :prop="_item.name" :label="_item.label" v-for="(_item, _index) in item.dataColumn" :key="_index">
                          <template #default="scope" v-if="_item.type === 'imageThumbnail'">
                            <el-link
                              type="primary"
                              v-if="scope.row[_item.form!.name!] && scope.row[_item.form!.name!].length > 1"
                              @click="showPic(scope.row, _item.form!.name!)"
                              >查看</el-link
                            >
                            <div v-else>
                              <el-image
                                style="width: 50px; height: 50px"
                                v-for="(v, i) in scope.row[_item.form!.name!] ?? []"
                                :key="i"
                                :src="v.url"
                                :zoom-rate="1.2"
                                :max-scale="7"
                                :min-scale="0.2"
                                :preview-src-list="scope.row[_item.form!.name!].map((_item: any) => _item.url) ?? []"
                                fit="cover"
                              />
                            </div>
                          </template>
                          <template #default="scope" v-else-if="_item.type === 'videoThumbnail'">
                            <el-link type="primary" @click="showVideo(scope.row, _item.form!.name!)" v-if="scope.row[_item.form!.name!]">查看</el-link>
                          </template>
                          <template #default="{ row }" v-else>
                            <el-text>{{ row[_item.name!] || '-' }}</el-text>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-card>
                </div>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div v-else class="h-100% flex items-center justify-center">
          <el-empty description="暂无数据" />
        </div>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <!-- 如果form存在并且formUri有值则使用数据返回的按钮，否则使用默认按钮 -->
          <div v-if="state.btns && state.btns.length > 0">
            <div v-for="(item, index) in state.btns" :key="index" class="inline-block">
              <el-button
                class="mr-2"
                :color="item.background"
                v-loading="submitLoading"
                :data-uri="item.uri"
                @click="item.label === '取消' || item.label === '关闭' ? closeDialog() : handleSubmit(formRef, item)"
                v-if="
                  !item.dependsOn ||
                  (item.dependsOn &&
                    item.dependsOn.every((depend: DependsOn) => {
                      //遍历item.dependsOn->depend，如果depend.field==='listData'的话selectTableColumn[0][depend.field!] === depend.when并且depend.field==='responseData'的话formData[depend.field!] === depend.when再显示
                      var target = ''
                      if (depend.dependFrom === 'listData') {
                        target = props.selectTableColumn[0][depend.field!]
                      } else if (depend.dependFrom === 'responseData') {
                        target = state.formData[depend.field!]
                      }
                      if (!depend.operator) {
                        depend.operator = 'eq'
                      }
                      var when = depend.when!
                      return operatorCalculate(target, when, depend.operator)
                    }))
                "
              >
                {{ item.label }}
              </el-button>
            </div>
          </div>
          <!-- <div v-else>
            <el-button type="primary" @click="handleSubmit(formRef)">确定</el-button>
            <el-button @click="closeDialog()">取 消</el-button>
          </div> -->
        </div>
      </template>
    </el-dialog>
    <!-- 查看照片 -->
    <PicDialogComponent ref="picDialogComponent" :image-list="imageList" />
    <!-- 查看视频 -->
    <VideoDialogComponent ref="videoDialogComponent" :video-list="videoList" />
  </div>
</template>

<script setup lang="ts">
  const { proxy }: any = getCurrentInstance()
  import { DynamicDetailVO, Item } from '@/api/Global/types'
  import { globalRequestApi, globalRequestUrlApi } from '@/api/planManagement'
  import { composeRequestParams, operatorCalculate } from '@/utils/common'
  import { useFormStore } from '@/store/modules/form'
  import { UploadUserFile, ElMessage } from 'element-plus'
  import PicDialogComponent from '@/components/PicDialogComponent/index.vue'
  import VideoDialogComponent from '@/components/VideoDialogComponent/index.vue'
  import UploadImageComponent from '@/components/UploadImageComponent/index.vue'
  import { SpanMethodProps } from '@/types/global'
  const emit = defineEmits(['handleSubmit', 'viewGenerateSubsidyStatement', 'viewRepairGenerateStatement', 'viewGenerateStatement', 'viewGenerateSett'])
  const formStore = useFormStore()
  const formRef = ref()
  const props = defineProps({
    /**
     * 表单按钮组
     */
    btnMenu: {
      required: true,
      type: Object as PropType<MenuVO>,
      default: () => ({}),
    },
    selectTableColumn: {
      required: true,
      type: Array as PropType<TableItem[]>,
      default: () => [],
    },
    currentRow: {
      required: true,
      type: Object as PropType<any>,
      default: () => ({}),
    },
    requestUri: {
      required: true,
      type: String as PropType<string>,
      default: '',
    },
    refreshMenuCount: {
      required: true,
      type: Boolean,
      default: false,
    },
  })
  /**
   * 提交加载动画
   */
  const submitLoading = ref(false)
  const state = reactive({
    dialogVisible: {
      visible: false,
      title: '',
      width: '50%',
    },
    loading: false,
    items: [] as TableItem[],
    formData: {} as any,
    btns: [] as BtnRequestVO[],
  })
  const getDetail = async (queryParams: any, row: any, menu: MenuVO) => {
    if (menu.meta?.form?.formUri) {
      //定义传递的参数
      let params = {} as any
      const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
      menu.meta!.form!.params?.map((_item) => {
        composeRequestParams(params, _item, menu, storeDataParams, row, queryParams)
      })
      state.loading = true
      if (menu.meta?.form?.method === 'get' || menu.meta?.form?.method === 'GET') {
        globalRequestUrlApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri)
          .then(async (res) => {
            const detail: DynamicDetailVO = res.data
            const { title, items, btns } = detail
            state.dialogVisible.title = title
            //重组items,遍历items，根据sortNo进行排序
            const newItems = items.sort((a, b) => a.sortNo - b.sortNo)
            //遍历newItems，如果children存在，则递归遍历children，根据sortNo进行排序
            const sortedItems = newItems.map((item) => {
              if (item.children) {
                item.children = item.children.sort((a, b) => a.sortNo - b.sortNo)
              }
              return item
            })
            state.items = sortedItems
            state.btns = btns
            // 初始化表单数据
            initializeFormData(sortedItems)
            state.loading = false
          })
          .catch(() => {
            state.loading = false
          })
      } else {
        globalRequestApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri)
          .then(async (res) => {
            state.loading = false
          })
          .catch(() => {
            state.loading = false
          })
      }
    }
  }
  const getColumnDetail = async (queryParams: any, row: any, column: TableItem) => {
    state.loading = true
    if (column.jump?.method === 'get' || column.jump?.method === 'GET') {
      globalRequestUrlApi(queryParams, column.jump?.method!, column.jump?.formUri!)
        .then(async (res) => {
          const detail: DynamicDetailVO = res.data
          const { title, items, btns } = detail
          state.dialogVisible.title = title
          //重组items,遍历items，根据sortNo进行排序
          const newItems = items.sort((a, b) => a.sortNo - b.sortNo)
          //遍历newItems，如果children存在，则递归遍历children，根据sortNo进行排序
          const sortedItems = newItems.map((item) => {
            if (item.children) {
              item.children = item.children.sort((a, b) => a.sortNo - b.sortNo)
            }
            return item
          })
          state.items = sortedItems
          state.btns = btns
          // 初始化表单数据
          initializeFormData(sortedItems)
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    } else {
      globalRequestApi(queryParams, column.jump?.method!, column.jump?.formUri!)
        .then(async (res) => {
          state.loading = false
        })
        .catch(() => {
          state.loading = false
        })
    }
  }
  /**
   * 关闭弹窗
   */
  const closeDialog = () => {
    //清除表单
    formRef.value?.resetFields()
    // 清除图片上传
    clearImageUploads()
    state.formData = {}
    state.dialogVisible.visible = false
  }
  /**
   * 获取所有图片上传组件的最新数据
   */
  const getLatestImageData = () => {
    // 遍历所有项目，获取图片上传字段的最新数据
    state.items.forEach((item) => {
      if (item.children) {
        item.children.forEach((child) => {
          if (child.dataColumn) {
            child.dataColumn.forEach((_item: any) => {
              if (_item.type === 'imageThumbnail' && _item.form?.name) {
                // 获取对应的上传组件引用
                const uploadRef = proxy.$refs[`uploadImageRef_${_item.form.name}`]
                let componentRef = uploadRef
                if (Array.isArray(uploadRef) && uploadRef.length > 0) {
                  componentRef = uploadRef[0]
                }
                // 如果组件存在且有uploadImageList，更新表单数据
                if (componentRef && componentRef.uploadImageList !== undefined) {
                  state.formData[_item.form.name] = componentRef.uploadImageList
                }
              }
            })
          }
        })
      } else if (item.dataColumn) {
        item.dataColumn.forEach((_item: any) => {
          if (_item.type === 'imageThumbnail' && _item.form?.name) {
            // 获取对应的上传组件引用
            const uploadRef = proxy.$refs[`uploadImageRef_${_item.form.name}`]
            let componentRef = uploadRef
            if (Array.isArray(uploadRef) && uploadRef.length > 0) {
              componentRef = uploadRef[0]
            }
            // 如果组件存在且有uploadImageList，更新表单数据
            if (componentRef && componentRef.uploadImageList !== undefined) {
              state.formData[_item.form.name] = componentRef.uploadImageList
            }
          }
        })
      }
    })
  }

  /**
   * 提交表单
   */
  const handleSubmit = async (formEl: { validate: any }, btnRequest?: BtnRequestVO) => {
    if (!formEl) return
    await formEl.validate((valid: any) => {
      if (valid) {
        // 获取最新的图片数据
        getLatestImageData()
        const formParams = JSON.parse(JSON.stringify(state.formData))
        //如果存在按钮，则获取当前点击按钮中的params
        if (formStore.storeFormBtns.length > 0) {
          formStore.storeFormBtns.forEach((item: BtnRequestVO) => {
            if (item.label === btnRequest?.label) {
              if (item.params) {
                const params: any = {}
                item.params?.map((_item) => {
                  composeRequestParams(params, item, null, null, props.currentRow, null, null)
                })
                Object.assign(formParams, params)
              }
            }
          })
        }
        if (btnRequest?.params) {
          const params: any = {}
          btnRequest.params?.map((_item) => {
            composeRequestParams(params, _item, null, null, props.currentRow, null, null)
          })
          Object.assign(formParams, params)
        }
        //如果按钮下存在请求操作，则优先使用按钮上的
        emit('handleSubmit', formParams, btnRequest ? btnRequest : props.requestUri, props.refreshMenuCount)
      }
    })
  }
  const imageList = ref([])
  const videoList = ref([])
  const picDialogComponent = ref()
  const videoDialogComponent = ref()
  const showPic = (row: any, name: string) => {
    if (row[name]) {
      imageList.value = row[name] ? row[name].map((item: any) => item.url) : []
      picDialogComponent.value.picDialogVisible = true
    } else {
      ElMessage.warning('当前未上传证件')
    }
  }

  const showVideo = (row: any, name: string) => {
    if (row[name]) {
      const videoList = row[name] ? row[name].map((item: any) => item.url) : []
      videoDialogComponent.value.videoDialogVisible = true
      videoDialogComponent.value.videoList = videoList
    } else {
      ElMessage.warning('当前未上传视频')
    }
  }
  const closed = () => {
    //清除表单
    formRef.value?.resetFields()
    state.formData = {}
  }
  // 合并单元格规则
  interface MergeRule {
    rowIndex: number
    columnIndex: number
    rowspan: number
    colspan: number
  }
  const mergeRules = ref<MergeRule[]>([
    { rowIndex: 0, columnIndex: 0, rowspan: 2, colspan: 1 },
    { rowIndex: 2, columnIndex: 0, rowspan: 2, colspan: 1 },
  ])
  const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: SpanMethodProps) => {
    const rule = mergeRules.value.find((rule) => rule.rowIndex === rowIndex && rule.columnIndex === columnIndex)
    if (rule) {
      return {
        rowspan: rule.rowspan,
        colspan: rule.colspan,
      }
    }
    return {
      rowspan: 1,
      colspan: 1,
    }
  }
  const linkTo = (item: any, _item: any) => {
    switch (_item.jump.jumpType) {
      case 'viewGenerateSubsidyStatement':
        emit('viewGenerateSubsidyStatement', { id: item.object.id })
        break
      case 'viewRepairGenerateStatement':
        emit('viewRepairGenerateStatement', {
          id: item.object.id,
          supplierId: item.object.supplierId,
          supplierName: item.object.supplierName,
          no: item.object.no,
        })
        break
      case 'viewGenerateStatement':
        emit('viewGenerateStatement', {
          id: item.object.id,
          customerId: item.object.customerId,
        })
        break
      case 'viewGenerateSett':
        emit('viewGenerateSett', {
          id: item.object.id,
        })
        break
      default:
        break
    }
  }

  /**
   * 初始化表单数据
   */
  const initializeFormData = (items: TableItem[]) => {
    // 遍历所有项目，将原始数据复制到 formData 中
    items.forEach((item) => {
      if (item.children) {
        // 如果有子项，递归处理
        item.children.forEach((child) => {
          if (child.dataColumn) {
            child.dataColumn.forEach((_item: any) => {
              if (_item.form?.name && child.object && child.object[_item.form.name] !== undefined) {
                state.formData[_item.form.name] = child.object[_item.form.name]
                // 如果是图片类型，需要在下一个tick设置上传组件的初始值
                if (_item.type === 'imageThumbnail') {
                  nextTick(() => {
                    setUploadImageList(_item.form.name, child.object[_item.form.name] || [])
                  })
                }
              }
            })
          }
        })
      } else if (item.dataColumn) {
        // 处理直接的数据列
        item.dataColumn.forEach((_item: any) => {
          if (_item.form?.name && item.object && item.object[_item.form.name] !== undefined) {
            state.formData[_item.form.name] = item.object[_item.form.name]
            // 如果是图片类型，需要在下一个tick设置上传组件的初始值
            if (_item.type === 'imageThumbnail') {
              nextTick(() => {
                setUploadImageList(_item.form.name, item.object[_item.form.name] || [])
              })
            }
          }
        })
      }
    })
  }

  /**
   * 处理图片上传
   */
  const handleImageUpload = (fileList: UploadUserFile[], fieldName: string) => {
    // UploadImageComponent 已经处理了文件验证和上传
    // 直接更新表单数据
    state.formData[fieldName] = fileList
  }

  /**
   * 设置上传图片列表
   */
  const setUploadImageList = (fieldName: string, list: UploadUserFile[]) => {
    const uploadRef = proxy.$refs[`uploadImageRef_${fieldName}`]
    // 处理组件引用可能是数组的情况
    let componentRef = uploadRef
    if (Array.isArray(uploadRef) && uploadRef.length > 0) {
      componentRef = uploadRef[0]
    }
    if (componentRef && componentRef.uploadImageList !== undefined) {
      componentRef.uploadImageList = list
    }
  }
  /**
   * 清除所有图片上传组件
   */
  const clearImageUploads = () => {
    console.log('开始清除图片上传组件', state.items)
    // 遍历所有项目，找到图片上传字段并清除
    state.items.forEach((item) => {
      if (item.children) {
        item.children.forEach((child) => {
          if (child.dataColumn) {
            child.dataColumn.forEach((_item: any) => {
              if (_item.type === 'imageThumbnail' && _item.form?.name) {
                // 清除表单数据
                state.formData[_item.form.name] = []
                // 清除上传组件
                setUploadImageList(_item.form.name, [])
              }
            })
          }
        })
      } else if (item.dataColumn) {
        item.dataColumn.forEach((_item: any) => {
          if (_item.type === 'imageThumbnail' && _item.form?.name) {
            // 清除表单数据
            state.formData[_item.form.name] = []
            // 清除上传组件
            setUploadImageList(_item.form.name, [])
          }
        })
      }
    })
  }

  defineExpose({
    closeDialog,
    getDetail,
    getColumnDetail,
    clearImageUploads,
    setUploadImageList,
    getLatestImageData,
    state,
  })
</script>

<style scoped lang="scss">
  .el-divider--horizontal {
    margin: 10px 0 20px;
  }
  :deep(.el-form-item__label) {
    background-color: #f5f7fa;
    padding-left: 5px;
  }
  :deep(.el-form-item) {
    margin-bottom: 0;
    border: 1px solid #dcdfe6;
    margin-left: -1px;
    margin-bottom: -1px;
    border-right: none;
  }
  // 单元格样式
  :deep(.el-table__cell) {
    position: static !important; // 解决el-image 和 el-table冲突层级冲突问题
  }
</style>
