<!--
 * @Author: llm
 * @Date: 2025-02-12 15:10:40
 * @LastEditors: llm
 * @LastEditTime: 2025-02-27 16:35:40
 * @Description: 编辑工资单
-->
<template>
  <div>
    <el-dialog
      :title="state.dialogVisible.title"
      v-model="state.dialogVisible.visible"
      :close-on-click-modal="false"
      :draggable="true"
      width="80%"
      @close="handleClose"
    >
      <el-scrollbar max-height="60vh">
        <el-form ref="formRef" :model="state.formData" :rules="rules" label-width="80px">
          <el-space direction="vertical" alignment="flex-start" style="width: 100%" fill>
            <el-descriptions title="" :column="6" border>
              <el-descriptions-item label="驾驶员" :width="140">
                <el-text size="large">{{ state.userInfo.driverName }}</el-text>
              </el-descriptions-item>
              <el-descriptions-item label="实发工资" :width="140">
                <el-text size="large" type="danger">{{ Number(state.userInfo.actualAmount) + Number(otherTotalIncome) }}</el-text>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions title="" :column="6" border>
              <!-- 固定工资 -->
              <el-descriptions-item label="固定工资" :width="140">
                <el-text size="large" type="warning">{{ state.userInfo.fixedIncome }}</el-text>
              </el-descriptions-item>
              <!-- 变动工资 -->
              <el-descriptions-item label="变动工资" :width="140">
                <el-text size="large" type="warning">{{ state.userInfo.variableIncome }}</el-text>
              </el-descriptions-item>
              <!-- 自动工资 -->
              <el-descriptions-item label="自动工资" :width="140">
                <el-text size="large" type="warning">{{ state.userInfo.autoIncome }}</el-text>
              </el-descriptions-item>
              <el-descriptions-item label="保证金" :width="140">
                <el-text size="large" type="warning">{{ state.userInfo.earnestSetAmount }}</el-text>
              </el-descriptions-item>
              <el-descriptions-item label="备用金" :width="140">
                <el-text size="large" type="warning">{{ state.userInfo.impressAmount }}</el-text>
              </el-descriptions-item>
              <el-descriptions-item label="现金挂账" :width="140">
                <el-text size="large" type="warning">{{ state.userInfo.cashChargeAmount }}</el-text>
              </el-descriptions-item>
              <el-descriptions-item label="备注" :width="140">
                <el-form-item label="" prop="remark" label-width="0">
                  <el-input placeholder="请填写备注" :maxLength="20" v-model="state.formData.remark" type="textarea"></el-input>
                </el-form-item>
              </el-descriptions-item>
            </el-descriptions>
            <el-card shadow="never">
              <template #header>
                <el-text size="large">其他款项：</el-text>
                <el-text size="large" type="warning">{{ otherTotalIncome }}</el-text>
              </template>
              <el-table :data="state.formData.otherItemList" style="width: 100%" border>
                <el-table-column prop="name" label="款项名称">
                  <template #default="{ row, $index }">
                    <el-form-item label="" :prop="`otherItemList.${$index}.name`" label-width="0" :rules="{ required: true, message: '请选择款项名称' }">
                      <el-input v-model="row.name" placeholder="请输入" clearable></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column prop="amount" label="金额">
                  <template #default="{ row, $index }">
                    <el-form-item label="" :prop="`otherItemList.${$index}.amount`" label-width="0" :rules="{ required: true, message: '请选择维修类别' }">
                      <el-input v-model="row.amount" placeholder="请输入" type="number" :min="1" clearable></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column prop="remark" label="备注">
                  <template #default="{ row, $index }">
                    <el-form-item label="" :prop="`otherItemList.${$index}.remark`" label-width="0" :rules="{ required: false, message: '请选择零件使用' }">
                      <el-input v-model="row.remark" placeholder="请输入" clearable></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="操作" width="100px" fixed="right">
                  <template #default="scope">
                    <!-- 新增 -->
                    <el-button type="primary" size="small" icon="Plus" circle @click="handleAdd(scope.$index)"></el-button>
                    <!-- 删除 -->
                    <el-button
                      type="danger"
                      size="small"
                      icon="Delete"
                      circle
                      @click="handleDelete(scope.$index)"
                      v-if="state.formData.otherItemList.length > 1"
                    ></el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-space>
        </el-form>
      </el-scrollbar>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { putOutFleetAdministrationStaffSalaryManagementApi } from '@/api/administrativeManagement'

  const formRef = ref()
  const emit = defineEmits(['resetQuery'])
  const state = reactive({
    dialogVisible: {
      visible: false,
      title: '',
    },
    userInfo: {
      id: '',
      driverName: '', // 驾驶员
      actualAmount: '', // 实发工资
      fixedIncome: '', // 固定收入
      variableIncome: '', // 变动收入
      earnestSetAmount: '', // 保障金
      impressAmount: '', // 备用金
      cashChargeAmount: '', // 现金挂账
      autoIncome: '',
    },
    formData: {
      remark: '',
      otherItemList: [
        {
          name: '',
          amount: '',
          remark: '',
        },
      ],
    },
    tableData: [{}],
  })
  watch(
    () => state.userInfo,
    (newVal: any) => {
      state.formData.otherItemList = newVal.otherItemList?.length === 0 ? [{ name: '', amount: '', remark: '' }] : newVal.otherItemList
    },
    {
      deep: true,
    },
  )
  const rules = {}
  const otherTotalIncome = computed(() => {
    return state.formData.otherItemList.reduce((total, item) => Number(total) + Number(item.amount), 0)
  })
  function handleClose() {
    formRef.value.resetFields()
    state.dialogVisible.visible = false
    state.formData.otherItemList = [
      {
        name: '',
        amount: '',
        remark: '',
      },
    ]
  }

  function handleSubmit() {
    formRef.value.validate(async (valid: any) => {
      if (valid) {
        const params = {
          otherTotalIncome: otherTotalIncome.value,
          ...state.formData,
        }
        const { data } = await putOutFleetAdministrationStaffSalaryManagementApi(state.userInfo.id, params)
        ElMessage.success('更新成功')
        handleClose()
        emit('resetQuery')
      }
    })
  }

  function handleAdd(index: number) {
    //在指定位置新增
    state.formData.otherItemList.splice(index + 1, 0, {
      name: '',
      amount: '',
      remark: '',
    })
  }
  function handleDelete(index: number) {
    state.formData.otherItemList.splice(index, 1)
  }
  defineExpose({
    state,
  })
</script>

<style scoped>
  .dialog-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
</style>
