<!--
 * @Author: llm
 * @Date: 2025-05-24 09:50:34
 * @LastEditors: llm
 * @LastEditTime: 2025-05-27 10:16:39
 * @Description: 
-->
<script lang="ts" setup>
  import { getFleetOrderDispatchEtcFeePreviewApi } from '@/api/businessManagement'
  import {
    getCommonOptionBusinessSelectApi,
    getOutFleetLoanEtcFeeDetailApi,
    postOutFleetLoanEtcFeeAddApi,
    postOutFleetLoanEtcFeeEditApi,
  } from '@/api/financialManagement'
  import { getNowDateYMD } from '@/utils'
  import { FormInstance } from 'element-plus'
  import { debounce } from 'lodash'
  const tollBorrowFormRef = ref<FormInstance>()
  const emit = defineEmits(['refresh'])

  const state = reactive({
    tollBorrowDialogVisible: false,
    dispatchCreateDate: '',
    rules: {
      dispatchNo: [{ required: true, message: '请选择调度单号', trigger: 'change' }],
      applyDate: [{ required: true, message: '请选择借款日期', trigger: 'change' }],
    },
    tollBorrowFormData: {
      etcItems: [
        {
          etcCardNumber: '',
          cardBalance: '',
          rechargeAmount: '',
        },
      ],
    } as any,
    tollBorrowPreviewInfo: {} as any,
    currentDispatchNo: '',
    status: '',
    tollBorrowTableData: [] as any[],
    dispatchList: [] as any[],
    loading: false,
    dispatchInfo: {}, //调度信息
  })
  watch(
    () => state.tollBorrowDialogVisible,
    () => {
      //applyDate 默认当天
      if (state.tollBorrowDialogVisible && state.status === 'add') {
        state.tollBorrowFormData.applyDate = getNowDateYMD()
      }
    },
  )
  const closeTollBorrowDialog = () => {
    state.tollBorrowDialogVisible = false
    state.tollBorrowFormData = {
      etcItems: [
        {
          etcCardNumber: '',
          cardBalance: '',
          rechargeAmount: '',
        },
      ],
    }
    state.tollBorrowPreviewInfo = {}
    state.dispatchList = []
    state.tollBorrowTableData = []
    state.status = 'add'
    state.currentDispatchNo = ''
    // 清空表单验证
    tollBorrowFormRef.value?.clearValidate()
  }
  const confirmTollBorrowDialog = debounce((formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate(async (valid: any) => {
      if (valid) {
        //校验充值金额必填
        for (const item of state.tollBorrowFormData.etcItems) {
          if (!item.rechargeAmount) {
            ElMessage.error('充值金额必填')
            return
          }
        }
        const params = {
          dispatchNo: state.tollBorrowFormData.dispatchNo,
          etcCardNumber: state.tollBorrowFormData.etcItems[0].etcCardNumber,
          rechargeAmount: state.tollBorrowFormData.etcItems[0].rechargeAmount,
          remark: state.tollBorrowFormData.remark,
          applyDate: state.tollBorrowFormData.applyDate,
          id: undefined,
        }
        if (state.tollBorrowFormData.id) {
          params.id = state.tollBorrowFormData.id
          await postOutFleetLoanEtcFeeEditApi(params)
        } else {
          await postOutFleetLoanEtcFeeAddApi(params)
        }
        ElMessage.success('操作成功')
        state.tollBorrowDialogVisible = false
        emit('refresh')
      }
    })
  }, 300)
  const getDetail = (id: string) => {
    state.loading = true
    getOutFleetLoanEtcFeeDetailApi({ id })
      .then((res: any) => {
        const { data } = res
        state.tollBorrowFormData.remark = data.remark
        state.tollBorrowFormData.id = data.id
        state.tollBorrowFormData.etcItems[0].rechargeAmount = data.amount
        state.tollBorrowFormData.applyDate = data.applyDate
        state.tollBorrowFormData.dispatchNo = data.dispatchNo
        getDispatchInfo(data.dispatchNo)
      })
      .finally(() => {
        state.loading = false
      })
  }
  const getDispatchList = async (query: string) => {
    if (!query) return
    const params = {
      label: 'name',
      dataSource: '未结算调度单',
      value: 'code',
      fuzzy: true,
      carrierType:"自有",
      keyword: query,
      selfFleet: true,
      extra: true,
    }
    const { data } = await getCommonOptionBusinessSelectApi(params)
    state.dispatchList = data as any[]
  }
  const getDispatchInfo = async (e: string) => {
    if (!e) return
    console.log(e)
    getFleetOrderDispatchEtcFeePreviewApi({ dispatchNo: e }).then((res: any) => {
      const { data } = res
      if (data.etcCardNumber) {
        state.tollBorrowPreviewInfo = data
        state.tollBorrowFormData.etcItems[0].etcCardNumber = data.etcCardNumber
        state.tollBorrowFormData.etcItems[0].cardBalance = data.cardBalance
        state.dispatchCreateDate = data.dispatchCreateDate
      } else {
        ElMessage.error('当前司机车辆没有关联的ETC卡信息，请先维护基础数据，或者进行现金借支')
        state.tollBorrowFormData.dispatchNo = ''
        //清空表单验证
        tollBorrowFormRef.value?.clearValidate()
      }
    })
  }
  defineExpose({
    getDetail,
    state,
  })
</script>
<template>
  <el-dialog title="路桥费借支" draggable v-model="state.tollBorrowDialogVisible" width="60%" @close="closeTollBorrowDialog">
    <el-form ref="tollBorrowFormRef" :rules="state.rules" :model="state.tollBorrowFormData" label-width="100px">
      <el-row>
        <el-col>
          <el-form-item label="调度单号" prop="dispatchNo">
            <!-- <el-input placeholder="请填写" :maxLength="20" @blur="getDispatchList" v-model="state.formData.vin"></el-input> -->
            <el-select
              popper-class="my-selects"
              :fit-input-width="true"
              v-model="state.tollBorrowFormData.dispatchNo"
              filterable
              remote
              reserve-keyword
              remote-show-suffix
              :remote-method="getDispatchList"
              :disabled="state.status === 'edit'"
              placeholder="请选择"
              clearable
              @change="getDispatchInfo"
            >
              <el-option v-for="item in state.dispatchList" :key="item.id" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-descriptions title="" border :column="2" class="mb-20px">
        <el-descriptions-item :width="140" label="车牌号">{{ state.tollBorrowPreviewInfo.vehicleNo || '-' }}</el-descriptions-item>
        <el-descriptions-item :width="140" label="司机信息"
          >{{ state.tollBorrowPreviewInfo.driverName || '-' }}（{{ state.tollBorrowPreviewInfo.driverMobile || '-' }}）</el-descriptions-item
        >
        <el-descriptions-item :width="140" label="运输动态">
          <span v-html="state.tollBorrowPreviewInfo.showDispatchLine"></span>
        </el-descriptions-item>
        <el-descriptions-item :width="140" label="里程">{{ state.tollBorrowPreviewInfo.mileage || '-' }}</el-descriptions-item>
        <el-descriptions-item :width="140" label="预估高速费">{{ state.tollBorrowPreviewInfo.highwayFee || '-' }}</el-descriptions-item>
        <el-descriptions-item :width="140" label="已借金额">{{ state.tollBorrowPreviewInfo.totalPrice || '-' }}</el-descriptions-item>
      </el-descriptions>
      <el-row :gutter="0">
        <el-col :span="8">
          <el-form-item label="借款日期" prop="applyDate">
            <el-date-picker
              v-model="state.tollBorrowFormData.applyDate"
              type="date"
              placeholder="请选择借款日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabledDate="
                (time: any) => time > new Date() || time <= new Date(state.dispatchCreateDate).setDate(new Date(state.dispatchCreateDate).getDate() - 1)
              "
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <span>ETC信息</span>
          </div>
        </template>
        <el-table :data="state.tollBorrowFormData.etcItems" :border="true">
          <el-table-column label="卡号" prop="etcCardNumber" align="center"></el-table-column>
          <el-table-column label="余额" prop="cardBalance" align="center">
            <template #default="{ row }">
              <el-text type="danger">{{ state.tollBorrowPreviewInfo.cardBalance || 0 }}</el-text>
            </template>
          </el-table-column>
          <el-table-column label="充值金额" prop="rechargeAmount" align="center">
            <template #header>
              <span class="color-red">*</span>
              <span>充值金额</span>
            </template>
            <template #default="{ row }">
              <el-input v-model="row.rechargeAmount" type="number" :min="0" placeholder="请输入充值金额"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <el-row :gutter="0" class="mt-20px">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="state.tollBorrowFormData.remark" type="textarea" placeholder="请输入备注"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeTollBorrowDialog">取消</el-button>
        <el-button type="primary" @click="confirmTollBorrowDialog(tollBorrowFormRef)">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>

.my-selects .el-select-dropdown__item {
  box-sizing: border-box;
  color: var(--el-text-color-regular);
  cursor: pointer;
  font-size: var(--el-font-size-base);
  line-height: 20px !important;
  height: auto !important;
  overflow: auto !important;
  padding: 10px 32px 10px 20px !important;
  position: relative;
  
  /* 文本溢出处理 - 兼容性优化 */
  text-overflow: clip !important;
  
  /* 换行处理 - 兼容性优化 */
  white-space: normal !important; /* wrap 不是标准值，改为 normal */
  word-wrap: break-word !important; /* 添加 word-wrap 兼容老版本浏览器 */
  word-break: break-all !important;
  
  /* 添加 Webkit 前缀支持 */
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

/* 为老版本 IE 添加回退样式 */
.my-selects .el-select-dropdown__item {
  *zoom: 1; /* IE6/7 触发 hasLayout */
}

/* 针对不支持 CSS 变量的浏览器添加回退值 */
.my-selects .el-select-dropdown__item {
  color: #606266; /* 回退颜色值 */
  font-size: 14px; /* 回退字体大小 */
}

.my-selects .el-select-dropdown__item {
  color: var(--el-text-color-regular, #606266);
  font-size: var(--el-font-size-base, 14px);
}
</style>
