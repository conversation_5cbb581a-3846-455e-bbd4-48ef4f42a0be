<!--
 * @Author: llm
 * @Date: 2024-04-26 15:10:04
 * @LastEditors: llm
 * @LastEditTime: 2024-05-23 10:42:23
 * @Description: 考核项目弹窗
-->
<template>
  <div>
    <el-dialog
      v-model="examineScoreDialogVisible.visible"
      :title="examineScoreDialogVisible.title"
      width="80%"
      :draggable="true"
      :close-on-click-modal="false"
      @closed="closeDialog"
    >
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form ref="formRef" :model="state.formData" label-width="120px" :inline="true" :rules="rules">
          <el-form-item label="考核项目名称" prop="examineName">
            <el-input v-model="state.formData.examineName" disabled></el-input>
          </el-form-item>
          <el-table class="mb-20px" :data="state.formData.majorItemList" show-summary :summary-method="getSummaries">
            <el-table-column label="序号" type="index" width="55" align="center" />
            <el-table-column prop="category" label="考核类别" width="180" align="center">
              <template #default="scope">
                <el-select v-model="scope.row.category" disabled placeholder="请选择考核类别" @change="changeCategory($event, scope.row)">
                  <el-option :label="item.label" :value="item.value" v-for="(item, index) in categoryOptions" :key="index"></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="majorItem" label="考核大项" width="180" align="center">
              <template #default="scope">
                <el-select v-model="scope.row.majorItem" disabled placeholder="请选择考核类别" v-if="scope.row.category == 1">
                  <el-option :label="item" :value="item" v-for="(item, index) in majorOptions" :key="index"></el-option>
                </el-select>
                <el-input v-model="scope.row.majorItem" disabled placeholder="例:安全" v-else></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="majorItemWeight" label="大项权重" width="180" align="center">
              <template #default="scope">
                <el-input disabled v-model="scope.row.majorItemWeight" placeholder="自动计算">
                  <template #append>%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="subItem" label="考核分项" width="180" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0]">
                  <el-input disabled v-model="item.subItem" :placeholder="scope.row.category == 1 ? '例:≤80' : '请输入考核分项'"></el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="standard" label="考核标准" width="180" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0]">
                  <el-input disabled v-model="item.standard" :placeholder="scope.row.category == 1 ? '例:-2' : '请输入考核标准'"></el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="assessMark" label="标准分数" width="160" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0]">
                  <el-input
                    disabled
                    v-model="item.assessMark"
                    :placeholder="scope.row.category == 1 ? '例:30' : '请输入标准分数'"
                    @blur="changeSubItemWeight(scope.row, scope.$index, index)"
                    type="number"
                  ></el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="score" label="打分" width="160" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0]">
                  <el-input v-model="item.score" placeholder="例:30"></el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="deductPoints" label="扣分说明" width="160" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0]">
                  <el-input v-model="item.deductPoints" placeholder="请输入"></el-input>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-form-item label="附加分说明" prop="objectionPeriod" label-width="120px">
            <div>{{ state.formData.extraPointStandard }}</div>
          </el-form-item>
          <el-form-item label="附加分" prop="extraPointScore" v-if="state.formData.extraPoint">
            <el-input v-model="state.formData.extraPointScore" :placeholder="'附加分最高为' + state.formData.extraPoint"></el-input>
          </el-form-item>
          <div>
            <el-form-item label="分数材料" prop="attachment">
              <el-upload
                ref="uploadFileRef"
                action="#"
                :auto-upload="false"
                :limit="1"
                :on-change="
                  (file: any, fileList: any[]) => {
                    uploadFile(file, fileList)
                  }
                "
                :on-exceed="handleExceed"
                :disabled="false"
              >
                <template #trigger>
                  <el-button type="primary">点击上传</el-button>
                </template>
              </el-upload>
            </el-form-item>
          </div>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog()">取 消</el-button>
          <el-button type="primary" v-loading="submitLoading" @click="handleSubmit(formRef)">提 交</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { ExamineProjectVO, MajorItemList } from '@/api/supplierManagement/type'
  import {
    examineListAddApi,
    examineListUpdateApi,
    examineTemplateCategorySelectOptionsApi,
    examineListDetailApi,
    examineTemplateMajorSelectOptionsApi,
    examineTemplateUseTargetSelectOptionsApi,
    examineTemplateOptionsApi,
    examineListMarkApi,
  } from '@/api/supplierManagement'
  import { FormInstance, UploadProps, UploadRawFile, dayjs, genFileId } from 'element-plus'
  import QuarterPicker from '@/components/QuarterPicker/index.vue'
  import { uploadFileApi } from '@/api/auth'

  const quarterValue = ref()
  const formRef = ref()
  const rules = reactive({
    examineName: [{ required: true, message: '请输入考核项目名称', trigger: 'blur' }],
    templateNo: [{ required: true, message: '请选择考核模板', trigger: 'change' }],
    useTarget: [{ required: true, message: '请选择考核对象', trigger: 'change' }],
    examineType: [{ required: true, message: '请选择考核类型', trigger: 'change' }],
    startTime: [{ required: true, message: '请选择考核时间范围', trigger: 'change' }],
    objectionPeriod: [{ required: true, message: '请输入考核结果异议期', trigger: 'change' }],
  })
  //获取当前的第一个季度
  const range = ref({
    quarter: '',
    // quarter: new Date().getFullYear() + '01',
  })

  function disabledQuarter(date: any) {
    return date > dayjs(new Date().getFullYear() + 2 + '01', 'YYYYMM') || date < dayjs(new Date().getFullYear() - 1 + '01', 'YYYYMM')
  }

  function changeDateRange(date: any) {
    state.formData.startTime = date + '-01'
    state.formData.endTime = date + '-12'
  }

  function handleChangeQuarter(date: any) {
    //change事件
    //quarterValue.value.year 为年（yyyy）
    //quarterValue.value.quarter 为季度（1,2,3,4）
    //根据季度获取对应的月份区间(1-3，4-6，7-9，10-12)
    let year = quarterValue.value.year
    let quarter = quarterValue.value.quarter
    switch (quarter) {
      case 1:
        state.formData.startTime = `${year}-01`
        state.formData.endTime = `${year}-03`
        break
      case 2:
        state.formData.startTime = `${year}-04`
        state.formData.endTime = `${year}-06`
        break
      case 3:
        state.formData.startTime = `${year}-07`
        state.formData.endTime = `${year}-09`
        break
      case 4:
        state.formData.startTime = `${year}-10`
        state.formData.endTime = `${year}-12`
        break
      default:
        break
    }
  }

  const examineScoreDialogVisible = reactive<DialogOption>({
    visible: false,
  })
  const emit = defineEmits(['resetQuery'])
  const submitLoading = ref(false)
  const state = reactive({
    examineType: [
      { label: '月度', value: 1 },
      { label: '季度', value: 2 },
      { label: '年度', value: 3 },
    ],
    dateRange: '',
    dateMonth: '',
    formData: {
      examineName: '',
      templateName: '',
      templateNo: '',
      useTarget: 0,
      examineType: 1,
      systemType: 'JIAOY',
      startTime: '',
      endTime: '',
      objectionPeriod: '',
      extraPoint: '',
      extraPointStandard: '',
      extraPointScore: '',
      attachment: '',
      majorItemList: [
        {
          category: 0,
          subItemList: [
            {
              subItem: '',
              standard: '',
              assessMark: 0,
              subItemWeight: 0,
              subItemSort: 0,
            },
          ],
          majorItemWeight: 0,
          majorItem: '',
          serialNumber: 0,
        },
      ],
    } as ExamineProjectVO,
  })
  watch(
    () => examineScoreDialogVisible.visible,
    (val) => {
      if (val) {
        examineTemplateOptions()
        examineTemplateUseTargetSelectOptions()
        examineTemplateCategorySelectOptions()
      }
    },
  )
  const templateOptions = ref<OptionType[]>([])
  const useTargetOptions = ref<OptionType[]>([])
  const categoryOptions = ref<OptionType[]>([])
  const majorOptions = ref<string[]>([])
  //查看模板详情
  const examineTemplateDetail = async (row: ExamineProjectVO) => {
    const { data } = await examineListDetailApi({ id: row.id })
    //如果state.formData与data存在相同key则覆盖

    // 截取row.startTime后两位,如果=01则为第一季度，否则如果=04则为第二季度，否则如果=07则为第三季度，否则如果 =10则为第四季度
    //如果为第一季度,则range.range = row.startTime.slice(0, 4) + '01',否则如果为第二季度,则range.range = row.startTime.slice(0, 4) + '04',否则如果为第三季度,则range.range = row.startTime.slice(0, 4) + '07',否则如果为第四季度,则range.quarter = row.startTime.slice(0, 4) + '10'
    if (data.startTime.slice(5, 7) === '01') {
      range.value.quarter = data.startTime.slice(0, 4) + '01'
    } else if (data.startTime.slice(5, 7) === '04') {
      range.value.quarter = data.startTime.slice(0, 4) + '04'
    } else if (data.startTime.slice(5, 7) === '07') {
      range.value.quarter = data.startTime.slice(0, 4) + '07'
    } else if (data.startTime.slice(5, 7) === '10') {
      range.value.quarter = data.startTime.slice(0, 4) + '10'
    }
    Object.assign(state.formData, data)
  }
  const examineTemplateOptions = async () => {
    const { data } = await examineTemplateOptionsApi()
    templateOptions.value = data as OptionType[]
  }
  const examineTemplateUseTargetSelectOptions = async () => {
    const { data } = await examineTemplateUseTargetSelectOptionsApi()
    useTargetOptions.value = data as OptionType[]
  }
  const examineTemplateCategorySelectOptions = async () => {
    const { data } = await examineTemplateCategorySelectOptionsApi()
    categoryOptions.value = data as OptionType[]
  }
  const examineTemplateMajorSelectOptions = async (e: any) => {
    const params = {
      useTarget: e,
    }
    const { data } = await examineTemplateMajorSelectOptionsApi(params)
    majorOptions.value = data as any[]
  }
  //选择考核类别
  const changeCategory = (e: any, row: any) => {
    examineTemplateMajorSelectOptions(e)
    //清除当前行所有数据
    row.subItemList = [
      {
        subItem: '',
        standard: '',
        assessMark: 0,
        subItemWeight: 0,
        subItemSort: 0,
      },
    ]
    row.majorItemWeight = 0
  }
  const changeSubItemWeight = (row: MajorItemList, index: number, subIndex: number) => {
    //每行的大项权重 = 每行中每一小项subItemList中assessMark的和
    row.majorItemWeight = row.subItemList
      .map((item) => item.assessMark)
      .reduce((prev, next) => {
        return Number(prev) + Number(next)
      })
    row.subItemList.map((item) => {
      item.subItemWeight = item.assessMark
    })
  }
  const handleSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl
      .validate(async (valid, fields) => {
        if (valid) {
          submitLoading.value = true
          await examineListMarkApi(state.formData)
            .then(() => {
              submitLoading.value = false
              examineScoreDialogVisible.visible = false
              ElMessage.success('操作成功')
              closeDialog()
              emit('resetQuery')
            })
            .catch(() => {
              submitLoading.value = false
            })
        } else {
        }
      })
      .catch(() => {
        submitLoading.value = false
      })
  }
  const changeExamineType = () => {
    if (state.formData.examineType === 1) {
      state.dateRange = 'month'
    } else if (state.formData.examineType === 2) {
      state.dateRange = 'quarter'
    } else if (state.formData.examineType === 3) {
      state.dateRange = 'year'
    }
  }
  const changeTemplateNo = (e: any) => {
    //state.formData.templateName = templateOptions中value等于e的label
    state.formData.templateName = templateOptions.value.find((item) => item.value === e)!.label
  }
  const getSummaries = (param: any) => {
    const { columns, data } = param
    const sums: any[] = []
    var arr = ['majorItemWeight', 'assessMark', 'subItemWeight']
    columns.forEach((column: { property: string }, index: number) => {
      if (index === 0) {
        sums[index] = '合计'
        return
      }
      const values = data.map((item: { [x: string]: any }) => Number(item[column.property]))
      if (column.property === 'assessMark') {
        //对state.formData.majorItemList进行遍历,遍历subItemList中的assessMark进行求和
        let totalAssessMark = 0
        state.formData.majorItemList.forEach((item) => {
          item.subItemList.forEach((subItem) => {
            totalAssessMark += Number(subItem.assessMark)
          })
        })
        sums[index] = totalAssessMark
      } else if (column.property === 'subItemWeight') {
        //对state.formData.majorItemList进行遍历,遍历subItemList中的subItemWeight进行求和
        let totalSubItemWeight = 0
        state.formData.majorItemList.forEach((item) => {
          item.subItemList.forEach((subItem) => {
            totalSubItemWeight += Number(subItem.subItemWeight)
          })
        })
        sums[index] = totalSubItemWeight + '%'
      } else {
        if (arr.indexOf(column.property) !== -1) {
          let sum = 0
          state.formData.majorItemList.forEach((item: any) => {
            sum += Number(item[column.property])
          })
          sums[index] = sum + '%'
        } else {
          sums[index] = ''
        }
      }
    })
    return sums
  }
  const closeDialog = () => {
    //清空表单数据
    formRef.value.resetFields()
    ;(state.formData.majorItemList = [
      {
        category: 0,
        subItemList: [
          {
            subItem: '',
            standard: '',
            assessMark: 0,
            subItemWeight: 0,
            subItemSort: 0,
          },
        ],
        majorItemWeight: 0,
        majorItem: '',
        serialNumber: 0,
      },
    ]),
      (examineScoreDialogVisible.visible = false)
  }
  /**
   * 新增考核项
   */
  const addTableItem = () => {
    state.formData.majorItemList.push({
      subItemList: [
        {
          subItem: '',
          standard: '',
          assessMark: 0,
          subItemWeight: 0,
          subItemSort: 0,
        },
      ],
      category: 0,
      majorItemWeight: 0,
      majorItem: '',
      serialNumber: 0,
    })
  }
  /**
   * 新增考核子项
   */
  const addSubItem = (row: any, rowIndex: number, index: number) => {
    state.formData.majorItemList[rowIndex].subItemList.push({
      subItem: '',
      standard: '',
      assessMark: 0,
      subItemWeight: 0,
      subItemSort: 0,
    })
  }
  /**
   * 删除考核子项
   */
  const deleteSubItem = (row: any, rowIndex: number, index: number) => {
    state.formData.majorItemList[rowIndex].subItemList.splice(index, 1)
    //重新计算权重
    changeSubItemWeight(row, rowIndex, index)
  }
  /**
   * 上移考核子项
   */
  const upSubItem = (row: any, rowIndex: number, subItemIndex: number) => {
    if (subItemIndex > 0) {
      let temp = state.formData.majorItemList[rowIndex].subItemList[subItemIndex]
      state.formData.majorItemList[rowIndex].subItemList[subItemIndex] = state.formData.majorItemList[rowIndex].subItemList[subItemIndex - 1]
      state.formData.majorItemList[rowIndex].subItemList[subItemIndex - 1] = temp
    }
  }
  /**
   * 下移考核子项
   */
  const downSubItem = (row: any, rowIndex: number, index: number) => {
    if (index < state.formData.majorItemList[rowIndex].subItemList.length - 1) {
      let temp = state.formData.majorItemList[rowIndex].subItemList[index]
      state.formData.majorItemList[rowIndex].subItemList[index] = state.formData.majorItemList[rowIndex].subItemList[index + 1]
      state.formData.majorItemList[rowIndex].subItemList[index + 1] = temp
    }
  }
  //上传文件
  const uploadFile = (file: any, fileList: any[]) => {
    if (file.status !== 'ready') return
    const params = {
      file: file.raw,
      businessLine: 204,
    }
    uploadFileApi('tms/system/file/upload', params).then((res) => {
      const { data } = res
      state.formData.attachment = data
    })
  }
  const uploadFileRef = ref()
  const handleExceed: UploadProps['onExceed'] = (files) => {
    uploadFileRef.value.clearFiles()
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    uploadFileRef.value.handleStart(file)
  }
  defineExpose({
    examineScoreDialogVisible,
    examineTemplateDetail,
  })
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    border-top-color: #e89e42;
  }

  :deep(.el-divider__text) {
    color: #e89e42;
  }
</style>
