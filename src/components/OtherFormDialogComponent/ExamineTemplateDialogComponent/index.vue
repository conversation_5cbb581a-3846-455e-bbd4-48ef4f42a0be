<!--
 * @Author: llm
 * @Date: 2024-04-26 15:10:04
 * @LastEditors: llm
 * @LastEditTime: 2024-12-26 10:43:25
 * @Description: 考核模板弹窗
-->
<template>
  <div>
    <el-dialog
      v-model="examineTemplateDialogVisible.visible"
      :title="examineTemplateDialogVisible.title"
      width="80%"
      :draggable="true"
      :close-on-click-modal="false"
      @close="closeDialog"
    >
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form ref="formRef" :model="state.formData" :rules="rules" label-width="100px" :inline="true">
          <el-form-item label="模板名称" prop="templateName">
            <el-input v-model="state.formData.templateName" placeholder="请输入模板名称"></el-input>
          </el-form-item>
          <el-form-item label="适用对象" prop="useTarget" class="w-[260px]">
            <el-select v-model="state.formData.useTarget">
              <el-option :label="item.label" :value="item.value" v-for="(item, index) in useTargetOptions" :key="index"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="desc">
            <el-input v-model="state.formData.desc" placeholder="请输入备注"></el-input>
          </el-form-item>
          <el-divider border-style="dashed">设置考核模板</el-divider>
          <el-table :data="state.formData.majorItemList" show-summary :summary-method="getSummaries">
            <el-table-column label="序号" type="index" width="55" align="center" />
            <el-table-column width="90px">
              <template #default="scope">
                <el-button
                  type="primary"
                  icon="Plus"
                  size="small"
                  circle
                  @click="addTableItem"
                  v-if="scope.$index === state.formData.majorItemList.length - 1"
                />
                <el-button
                  type="danger"
                  icon="Delete"
                  size="small"
                  circle
                  @click="deleteTableItem(scope.row, scope.$index)"
                  v-if="state.formData.majorItemList.length > 1"
                />
              </template>
            </el-table-column>
            <el-table-column prop="category" label="考核类别" width="180" align="center">
              <template #default="scope">
                <el-select v-model="scope.row.category" placeholder="请选择考核类别" @change="changeCategory($event, scope.row)">
                  <el-option :label="item.label" :value="item.value" v-for="(item, index) in categoryOptions" :key="index"></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="majorItem" label="考核大项" width="180" align="center">
              <template #default="scope">
                <el-select v-model="scope.row.majorItem" placeholder="请选择考核类别" v-if="scope.row.category == 1">
                  <el-option :label="item" :value="item" v-for="(item, index) in majorOptions" :key="index"></el-option>
                </el-select>
                <el-input v-model="scope.row.majorItem" placeholder="例:安全" v-else></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="majorItemWeight" label="大项权重" width="180" align="center">
              <template #default="scope">
                <el-input disabled v-model="scope.row.majorItemWeight" placeholder="自动计算">
                  <template #append>%</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="subItem" label="考核分项" width="180" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0]">
                  <el-input v-model="item.subItem" :placeholder="scope.row.category == 1 ? '例:≤80' : '请输入考核分项'">
                    <template #append v-if="scope.row.category == 1">%</template>
                  </el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="standard" label="考核标准" width="180" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0]">
                  <el-input v-model="item.standard" :placeholder="scope.row.category == 1 ? '例:-2' : '请输入考核标准'"></el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="assessMark" label="标准分数" width="160" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0]">
                  <el-input
                    v-model="item.assessMark"
                    :placeholder="scope.row.category == 1 ? '例:30' : '请输入标准分数'"
                    @blur="changeSubItemWeight(scope.row, scope.$index, index)"
                    type="number"
                  ></el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="subItemWeight" label="分项权重" width="160" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0]">
                  <el-input disabled v-model="item.subItemWeight" placeholder="自动计算">
                    <template #append>%</template>
                  </el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0] h-42px flex justify-center items-center">
                  <el-button
                    type="danger"
                    icon="Delete"
                    circle
                    size="small"
                    @click="deleteSubItem(scope.row, scope.$index, index)"
                    v-if="state.formData.majorItemList[scope.$index].subItemList.length > 1"
                  />
                  <el-button
                    type="primary"
                    icon="Plus"
                    circle
                    size="small"
                    @click="addSubItem(scope.row, scope.$index, index)"
                    v-if="index === state.formData.majorItemList[scope.$index].subItemList.length - 1 && scope.row.category != 1"
                  />
                  <el-button
                    type="primary"
                    icon="Top"
                    circle
                    size="small"
                    @click="upSubItem(scope.row, scope.$index, index)"
                    v-if="scope.row.category != 1 && index > 0"
                  />
                  <el-button
                    type="primary"
                    icon="Bottom"
                    circle
                    size="small"
                    @click="downSubItem(scope.row, scope.$index, index)"
                    v-if="scope.row.category != 1 && index < scope.row.subItemList.length - 1"
                  />
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog()">取 消</el-button>
          <el-button type="primary" v-loading="submitLoading" @click="handleSubmit(formRef)">保 存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { ExamineTemplateVO, MajorItemList } from '@/api/supplierManagement/type'
  import {
    examineTemplateAddApi,
    examineTemplateUpdateApi,
    examineTemplateCategorySelectOptionsApi,
    examineTemplateDetailApi,
    examineTemplateMajorSelectOptionsApi,
    examineTemplateUseTargetSelectOptionsApi,
  } from '@/api/supplierManagement'
  import type { ComponentSize, FormInstance, FormRules } from 'element-plus'

  const examineTemplateDialogVisible = reactive<DialogOption>({
    visible: false,
  })
  const emit = defineEmits(['resetQuery'])
  const submitLoading = ref(false)
  const formRef = ref()
  const rules = {
    templateName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  }
  const state = reactive({
    formData: {
      templateName: '',
      useTarget: 0,
      desc: '',
      systemType: 'JIAOY',
      majorItemList: [
        {
          category: 0,
          subItemList: [
            {
              subItem: '',
              standard: '',
              assessMark: 0,
              subItemWeight: 0,
              subItemSort: 0,
            },
          ],
          majorItemWeight: 0,
          majorItem: '',
          serialNumber: 0,
        },
      ],
    } as ExamineTemplateVO,
  })
  watch(
    () => examineTemplateDialogVisible.visible,
    (val) => {
      if (val) {
        examineTemplateUseTargetSelectOptions()
        examineTemplateCategorySelectOptions()
      }
    },
  )
  const useTargetOptions = ref<OptionType[]>([])
  const categoryOptions = ref<OptionType[]>([])
  const majorOptions = ref<string[]>([])
  //查看模板详情
  const examineTemplateDetail = async (row: ExamineTemplateVO) => {
    const { data } = await examineTemplateDetailApi({ id: row.id })
    //如果state.formData与data存在相同key则覆盖
    Object.assign(state.formData, data)
  }
  const examineTemplateUseTargetSelectOptions = async () => {
    const { data } = await examineTemplateUseTargetSelectOptionsApi()
    useTargetOptions.value = data as OptionType[]
  }
  const examineTemplateCategorySelectOptions = async () => {
    const { data } = await examineTemplateCategorySelectOptionsApi()
    categoryOptions.value = data as OptionType[]
  }
  const examineTemplateMajorSelectOptions = async (e: any) => {
    const params = {
      useTarget: e,
    }
    const { data } = await examineTemplateMajorSelectOptionsApi(params)
    majorOptions.value = data as any[]
  }
  //选择考核类别
  const changeCategory = (e: any, row: any) => {
    row.majorItem = ''
    examineTemplateMajorSelectOptions(state.formData.useTarget)
    //清除当前行所有数据
    row.subItemList = [
      {
        subItem: '',
        standard: '',
        assessMark: 0,
        subItemWeight: 0,
        subItemSort: 0,
      },
    ]
    row.majorItemWeight = 0
  }
  const changeSubItemWeight = (row: MajorItemList, index: number, subIndex: number) => {
    //每行的大项权重 = 每行中每一小项subItemList中assessMark的和
    row.majorItemWeight = row.subItemList
      .map((item) => item.assessMark)
      .reduce((prev, next) => {
        return Number(prev) + Number(next)
      })
    row.subItemList.map((item) => {
      item.subItemWeight = item.assessMark
    })
  }
  const handleSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        submitLoading.value = true
        //遍历表单数据,将subItemSort赋值为数组所在位置的下标,serialNumber赋值为数组所在位置的下标
        state.formData.majorItemList.forEach((item, index) => {
          item.subItemList.forEach((subItem, subIndex) => {
            subItem.subItemSort = subIndex
          })
          item.serialNumber = index
        })
        // 求和标准分数的总和不等于100,则不能提交
        let totalAssessMark = 0
        state.formData.majorItemList.forEach((item) => {
          item.subItemList.forEach((subItem) => {
            totalAssessMark += Number(subItem.assessMark)
          })
        })
        if (totalAssessMark !== 100) {
          ElMessage.error('标准分数总和必须等于100')
          submitLoading.value = false
          return
        }
        try {
          //如果
          if (state.formData.id) {
            await examineTemplateUpdateApi(state.formData)
              .then(() => {
                submitLoading.value = false
                examineTemplateDialogVisible.visible = false
                ElMessage.success('操作成功')
                closeDialog()
                emit('resetQuery')
              })
              .catch((error: any) => {
                submitLoading.value = false
              })
          } else {
            await examineTemplateAddApi(state.formData)
              .then(() => {
                submitLoading.value = false
                examineTemplateDialogVisible.visible = false
                ElMessage.success('操作成功')
                closeDialog()
                emit('resetQuery')
              })
              .catch((error: any) => {
                submitLoading.value = false
              })
          }
        } catch {
          submitLoading.value = false
        }
      } else {
      }
    })
  }

  const getSummaries = (param: any) => {
    const { columns, data } = param
    const sums: any[] = []
    var arr = ['majorItemWeight', 'assessMark', 'subItemWeight']

    columns.forEach((column: { property: string }, index: number) => {
      if (index === 0) {
        sums[index] = '合计'
        return
      }
      const values = data.map((item: { [x: string]: any }) => Number(item[column.property]))
      if (column.property === 'assessMark') {
        //对state.formData.majorItemList进行遍历,遍历subItemList中的assessMark进行求和
        let totalAssessMark = 0
        state.formData.majorItemList.forEach((item) => {
          item.subItemList.forEach((subItem) => {
            totalAssessMark += Number(subItem.assessMark)
          })
        })
        sums[index] = totalAssessMark
      } else if (column.property === 'subItemWeight') {
        //对state.formData.majorItemList进行遍历,遍历subItemList中的subItemWeight进行求和
        let totalSubItemWeight = 0
        state.formData.majorItemList.forEach((item) => {
          item.subItemList.forEach((subItem) => {
            totalSubItemWeight += Number(subItem.subItemWeight)
          })
        })
        sums[index] = totalSubItemWeight + '%'
      } else {
        if (arr.indexOf(column.property) !== -1) {
          let sum = 0
          state.formData.majorItemList.forEach((item: any) => {
            sum += Number(item[column.property])
          })
          sums[index] = sum + '%'
        } else {
          sums[index] = ''
        }
      }
    })
    return sums
  }
  const closeDialog = () => {
    //清空表单数据
    state.formData.templateName = ''
    state.formData.useTarget = 0
    state.formData.desc = ''
    state.formData.systemType = 'JIAOY'
    state.formData.majorItemList = [
      {
        category: 0,
        subItemList: [
          {
            subItem: '',
            standard: '',
            assessMark: 0,
            subItemWeight: 0,
            subItemSort: 0,
          },
        ],
        majorItemWeight: 0,
        majorItem: '',
        serialNumber: 0,
      },
    ]
    formRef.value.resetFields()
    examineTemplateDialogVisible.visible = false
  }
  /**
   * 新增考核项
   */
  const addTableItem = () => {
    state.formData.majorItemList.push({
      subItemList: [
        {
          subItem: '',
          standard: '',
          assessMark: 0,
          subItemWeight: 0,
          subItemSort: 0,
        },
      ],
      category: 0,
      majorItemWeight: 0,
      majorItem: '',
      serialNumber: 0,
    })
  }
  /**
   * 删除考核项
   */
  const deleteTableItem = (row: any, index: number) => {
    state.formData.majorItemList.splice(index, 1)
  }
  /**
   * 新增考核子项
   */
  const addSubItem = (row: any, rowIndex: number, index: number) => {
    state.formData.majorItemList[rowIndex].subItemList.push({
      subItem: '',
      standard: '',
      assessMark: 0,
      subItemWeight: 0,
      subItemSort: 0,
    })
  }
  /**
   * 删除考核子项
   */
  const deleteSubItem = (row: any, rowIndex: number, index: number) => {
    state.formData.majorItemList[rowIndex].subItemList.splice(index, 1)
    //重新计算权重
    changeSubItemWeight(row, rowIndex, index)
  }
  /**
   * 上移考核子项
   */
  const upSubItem = (row: any, rowIndex: number, subItemIndex: number) => {
    if (subItemIndex > 0) {
      let temp = state.formData.majorItemList[rowIndex].subItemList[subItemIndex]
      state.formData.majorItemList[rowIndex].subItemList[subItemIndex] = state.formData.majorItemList[rowIndex].subItemList[subItemIndex - 1]
      state.formData.majorItemList[rowIndex].subItemList[subItemIndex - 1] = temp
    }
  }
  /**
   * 下移考核子项
   */
  const downSubItem = (row: any, rowIndex: number, index: number) => {
    if (index < state.formData.majorItemList[rowIndex].subItemList.length - 1) {
      let temp = state.formData.majorItemList[rowIndex].subItemList[index]
      state.formData.majorItemList[rowIndex].subItemList[index] = state.formData.majorItemList[rowIndex].subItemList[index + 1]
      state.formData.majorItemList[rowIndex].subItemList[index + 1] = temp
    }
  }
  defineExpose({
    examineTemplateDialogVisible,
    examineTemplateDetail,
    state,
  })
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    border-top-color: #e89e42;
  }

  :deep(.el-divider__text) {
    color: #e89e42;
  }
</style>
