<!--
 * @Author: llm
 * @Date: 2025-02-12 15:10:40
 * @LastEditors: llm
 * @LastEditTime: 2025-06-20 17:05:30
 * @Description: 车队维修弹窗
-->
<template>
  <div>
    <el-dialog
      :title="state.dialogVisible.title"
      v-model="state.dialogVisible.visible"
      :close-on-click-modal="false"
      :draggable="true"
      width="80%"
      @close="handleClose"
    >
      <el-scrollbar max-height="60vh">
        <el-form
          v-loading="state.loading"
          ref="formRef"
          :model="state.formData"
          :rules="rules"
          :disabled="state.origin === 'adjustCost' || state.isView"
          label-width="80px"
          :inline-message="true"
          size="small"
        >
          <el-descriptions title="" :column="3" size="small" border>
            <el-descriptions-item label="维修单位">
              <template #label>
                <div class="cell-item">
                  <span class="text-red-500">*</span>
                  <span>维修单位</span>
                </div>
              </template>
              <el-form-item label="" prop="supplierId" label-width="0" :rules="{ required: true, message: '请选择维修单位', trigger: 'change' }">
                <el-select
                  filterable
                  clearable
                  v-model="state.formData.supplierId"
                  placeholder="请选择维修单位"
                  style="width: 100%"
                  @change="handleSupplierChange"
                >
                  <el-option v-for="item in state.supplierList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="维修基地">
              <template #label>
                <div class="cell-item">
                  <span class="text-red-500">*</span>
                  <span>维修基地</span>
                </div>
              </template>
              <el-form-item label="" prop="baseName" label-width="0" :rules="{ required: true, message: '请选择维修基地', trigger: 'change' }">
                <el-tree-select
                  :props="props"
                  v-model="state.formData.baseId"
                  :data="state.baseNameList"
                  clearable
                  filterable
                  :check-strictly="true"
                  placeholder="请选择维修基地"
                  style="width: 100%"
                  @node-click="handleBaseNameChange"
                >
                </el-tree-select>
              </el-form-item>
            </el-descriptions-item>
            <!-- 维修车辆 -->
            <el-descriptions-item label="维修车辆">
              <template #label>
                <div class="cell-item">
                  <span class="text-red-500">*</span>
                  <span>维修车辆</span>
                </div>
              </template>
              <el-form-item label="" prop="vehicleNo" label-width="0" :rules="{ required: true, message: '请选择维修车辆', trigger: 'change' }">
                <!-- <el-select
                  v-model="state.formData.vehicleNo"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="模糊搜索"
                  remote-show-suffix
                  :remote-method="handleVehicleChange"
                  :loading="state.vehicleLoading"
                  style="width: 100%"
                  @change="handleVehicleChange1"
                >
                  <el-option v-for="item in state.repairVehicleList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select> -->
                <el-tree-select filterable @change="handleVehicleChange1" v-model="state.formData.vehicleNo" :data="state.repairVehicleList" :render-after-expand="false" style="width: 100%" />
              </el-form-item>
            </el-descriptions-item>
            <!-- 车辆里程 -->
            <el-descriptions-item label="车辆里程">
              <template #label>
                <div class="cell-item">
                  <span class="text-red-500">*</span>
                  <span>车辆里程</span>
                </div>
              </template>
              <el-form-item label="" prop="vehicleMileage" label-width="0" :rules="{ required: true, message: '请输入车辆里程', trigger: 'blur' }">
                <el-input-number :min="0" v-model="state.formData.vehicleMileage" :step="1" style="width: 100%;"></el-input-number>
              </el-form-item>
            </el-descriptions-item>
            <!-- 委托人 -->
            <el-descriptions-item label="委托人">
              <el-form-item label="" prop="entrustUser" label-width="0">
                <el-input v-model="state.formData.entrustUser" placeholder="请输入委托人" clearable></el-input>
              </el-form-item>
            </el-descriptions-item>
            <!-- 联系电话 -->
            <el-descriptions-item label="联系电话">
              <el-form-item label="" prop="contact" label-width="0">
                <el-input v-model="state.formData.contact" placeholder="请输入联系电话" type="tel" clearable></el-input>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
          <el-card class="mt-10px mb-10px" shadow="never">
            <template #header>
              <el-text size="large">维修项目总计：{{ state.formData.itemList.length }}件</el-text>
            </template>
            <el-table :data="state.formData.itemList" style="width: 100%" border>
              <el-table-column prop="projectId" label="维修项目" width="220px">
                <template #header>
                  <div class="cell-item">
                    <span class="text-red-500">*</span>
                    <span>维修项目</span>
                  </div>
                </template>
                <template #default="{ row, $index }">
                  <el-form-item label="" :prop="`itemList.${$index}.projectItemId`" label-width="0" :rules="{ required: true, message: '请选择维修项目' }">
                    <el-select filterable v-model="row.projectItemId" placeholder="请选择" @change="handleProjectChange($index)">
                      <el-option v-for="item in state.repairProjectList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                    <el-text type="warning" size="small" v-if="row.maintenanceDate"
                      >最近维修：{{ row.maintenanceDate }} &nbsp;{{ row.vehicleMileage }}KM</el-text
                    >
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="maintenanceType" label="维修类别" width="180px">
                <template #header>
                  <div class="cell-item">
                    <span class="text-red-500">*</span>
                    <span>维修类别</span>
                  </div>
                </template>
                <template #default="{ row, $index }">
                  <el-form-item label="" :prop="`itemList.${$index}.maintenanceType`" label-width="0" :rules="{ required: true, message: '请选择维修类别' }">
                    <el-select filterable clearable v-model="row.maintenanceType" placeholder="请选择维修类别" @change="handleProjectChange($index)">
                      <el-option v-for="item in state.maintenanceTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="partsUse" label="零件使用" width="180px">
                <template #header>
                  <div class="cell-item">
                    <span class="text-red-500">*</span>
                    <span>零件使用</span>
                  </div>
                </template>
                <template #default="{ row, $index }">
                  <el-form-item label="" :prop="`itemList.${$index}.partsUse`" label-width="0" :rules="{ required: true, message: '请选择零件使用' }">
                    <el-radio-group v-model="row.partsUse" @change="handleProjectChange($index)">
                      <el-radio :value="item.value" v-for="item in state.partsUseList" :key="item.value"> {{ item.label }} </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="packageNumber" label="零件数量" width="120px">
                <template #header>
                  <div class="cell-item">
                    <span class="text-red-500">*</span>
                    <span>零件数量</span>
                  </div>
                </template>
                <template #default="{ row, $index }">
                  <el-form-item label="" :prop="`itemList.${$index}.packageNumber`" label-width="0" :rules="{ required: true, message: '请输入零件件数' }">
                    <el-input-number v-model="row.packageNumber" :min="1" :step="1" clearable @change="handleProjectChange($index)"></el-input-number>
                  </el-form-item>
                </template>
              </el-table-column>
              <!-- 单位（件/升） -->
              <el-table-column prop="unit" label="单位(件/升)" width="120px">
                <template #default="{ row, $index }">
                  <el-form-item label="" :prop="`itemList.${$index}.unit`" label-width="0">
                    <el-select v-model="row.unit" placeholder="请选择单位" filterable clearable>
                      <el-option label="件" value="件" />
                      <el-option label="升" value="升" />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="partPrice" label="零件单价" width="120px">
                <template #default="{ row, $index }">
                  <el-form-item label="" :prop="`itemList.${$index}.partPrice`" label-width="0">
                    <el-input :disabled="true" v-model="row.partPrice" placeholder="--" type="number" :min="1" clearable></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="laborPrice" label="人工费" width="120px">
                <template #default="{ row, $index }">
                  <el-form-item label="" :prop="`itemList.${$index}.laborPrice`" label-width="0">
                    <el-input :disabled="true" v-model="row.laborPrice" placeholder="--" type="number" :min="1" clearable></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="维修费用">
                <template #default="{ row, $index }">
                  <span class="text-red-500 font-bold">{{ row.estimatedCost || '--' }}</span
                  ><span>元</span>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="历史" width="120px">
                <template #default="{ row, $index }">
                  <span>{{ row.historyMaintenanceCost || '--' }}元</span>
                  <span class="text-blue-500 font-bold">&nbsp;{{ row.maintenanceCount || '--' }}</span
                  ><span>次</span>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="操作" width="100px" fixed="right" v-if="state.origin !== 'adjustCost'">
                <template #default="scope">
                  <!-- 新增 -->
                  <el-button type="primary" size="small" icon="Plus" circle @click="handleAdd(scope.$index)"></el-button>
                  <!-- 删除 -->
                  <el-button
                    type="danger"
                    size="small"
                    icon="Delete"
                    circle
                    @click="handleDelete(scope.$index)"
                    v-if="state.formData.itemList.length > 1"
                  ></el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
          <el-descriptions title="" :column="3" size="small" border v-if="state.origin === 'adjustCost' || state.isView">
            <el-descriptions-item label="调整费用">
              <div class="font-bold flex items-center justify-between">
                <el-form ref="adjustCostFormRef" :model="state.adjustCostForm" :rules="rules" label-width="80px">
                  <el-form-item label="" prop="adjustCost" label-width="0" :rules="{ required: true, message: '请输入调整费用' }">
                    <el-input-number
                      v-model="state.adjustCostForm.adjustCost"
                      placeholder="请输入调整费用"
                      clearable
                      class="w-240px"
                      :disabled="state.isView"
                    ></el-input-number>
                    <!-- <el-link type="primary" @click="handleAdjustCost" class="ml-10px">确定</el-link> -->
                    <div class="flex items-center ml-20px">
                      <el-icon color="#f3d19e"><QuestionFilled /></el-icon>
                      <span class="color-red text-12px">正值为公司向供应商支付费用，负值为扣除供应商费用</span>
                    </div>
                  </el-form-item>
                </el-form>
              </div>
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions title="" :column="3" size="small" border class="mt-10px">
            <el-descriptions-item :label="state.formData.sourceType === '车队' ? '预估费用' : '实际费用'">
              <span class="text-red-500 font-bold">{{ state.formData.totalEstimatedCost || '--' }}</span
              ><span>元</span>
            </el-descriptions-item>
            <el-descriptions-item label="现金付款">
              <span class="text-red-500 font-bold">{{ state.formData.totalCashPayment || '--' }}</span
              ><span>元</span>
            </el-descriptions-item>
            <el-descriptions-item label="转账未税付款">
              <span class="text-red-500 font-bold">{{ state.formData.totalTransferWithoutTaxPayment || '--' }}</span
              ><span>元</span>
            </el-descriptions-item>
          </el-descriptions>
          <el-form-item label="备注" prop="remark" class="mt-10px">
            <el-input v-model="state.formData.remark" placeholder="请输入备注" type="textarea" clearable></el-input>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <div slot="footer" class="dialog-footer">
        <template v-if="!state.isView">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="submitAdjustCost" v-if="state.origin === 'adjustCost'">提交</el-button>
          <el-button type="primary" @click="submitReport" v-else>提交</el-button>
        </template>
        <template v-else>
          <el-button @click="handleClose" type="primary">关闭</el-button>
        </template>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import {
    getDepartmentTreeOptionApi,
    getMaintenanceSubmissionEditDetailApi,
    getMaintenanceTypeSelectApi,
    getPartsUseSelectOptionApi,
    getRepairProjectSelectOptionApi,
    getSupplierSelectOptionApi,
    getVehicleSelectOptionApi,
    putMaintenanceSubmissionEditApi,
    postMaintenanceSubmissionSaveApi,
    postProjectCostApi,
    postAdjustCostChangeApi,
    postAdjustCostChangeSubmitApi,
    selectvehicleOption,
  } from '@/api/fleetManagement'
  import { FormDataVO, ItemListVO } from '@/api/fleetManagement/type'
  import { useAppStore } from '@/store/modules/app'

  const appStore = useAppStore()
  const formRef = ref()
  const adjustCostFormRef = ref()
  const emit = defineEmits(['resetQuery'])
  const props = {
    label: 'label',
    children: 'children',
    isLeaf: 'isLeaf',
  }
  const state = reactive({
    isView: false,
    loading: false,
    id: '',
    adjustCostForm: {
      adjustCost: 0,
    },
    origin: '', //adjustCost 展示调整费用
    vehicleLoading: false,
    dialogVisible: {
      visible: false,
      title: '',
    },
    reportData: {
      name: '',
      description: '',
      date: '',
    },
    baseNameList: [] as SelectOptions[],
    maintenanceTypeList: [] as SelectOptions[],
    supplierList: [] as SelectOptions[],
    repairProjectList: [] as SelectOptions[],
    partsUseList: [] as SelectOptions[],
    repairVehicleList: [] as any[],
    formData: {
      adjustCost: undefined,
      id: undefined,
      removeItemIds: [],
      sourceType: '',
      vehicleMileage: 0,
      baseName: '',
      baseId: '',
      supplierId: '',
      supplierName: '',
      vehicleNo: '',
      entrustUser: '',
      contact: '',
      itemList: [
        {
          projectId: '',
          packageNumber: 1,
          maintenanceType: '',
          partsUse: '',
          partPrice: '',
          laborPrice: '',
          estimatedCost: '',
          cashPayment: '',
          transferWithoutTaxPayment: '',
          historyMaintenanceCost: '',
          maintenanceCount: 0,
          projectName: '',
          projectItemId: '',
          maintenanceDate: '',
          vehicleMileage: 0,
        },
      ],
      totalEstimatedCost: '',
      totalCashPayment: '',
      totalTransferWithoutTaxPayment: '',
      remark: '',
    } as FormDataVO,
    tableData: [{}],
  })
  const rules = {
    name: [{ required: true, message: '请输入维修单位', trigger: 'blur' }],
    description: [{ required: true, message: '请输入维修单位', trigger: 'blur' }],
    date: [{ required: true, message: '请输入维修单位', trigger: 'blur' }],
  }

  function handleClose() {
    resetFormData()
    state.origin = ''
    // state.repairVehicleList = []
  }

  const selectvehicleOptionApi = async () => {
    const res = await selectvehicleOption({})
    console.log('res: ', res)
    state.repairVehicleList = res.data as SelectOptions[]
  }

  function submitReport() {
    formRef.value.validate(async (valid: any) => {
      if (valid) {
        if (state.origin === 'adjustCost') {
          //表单校验
          adjustCostFormRef.value.validate(async (valid: any) => {})
        } else {
          if (state.formData.id) {
            const { data } = await putMaintenanceSubmissionEditApi(state.formData)
            ElMessage.success('更新成功')
          } else {
            const { data } = await postMaintenanceSubmissionSaveApi(state.formData)
            ElMessage.success('新增成功')
          }
        }

        resetFormData()
      }
    })
  }
  const handleBaseNameChange = (node: any) => {
    state.formData.baseName = node.label
  }
  const resetFormData = () => {
    state.dialogVisible.visible = false
    // 清空表单
    formRef.value.resetFields()
    if (state.origin === 'adjustCost') {
      adjustCostFormRef.value.resetFields()
    }
    state.formData = {
      id: undefined,
      removeItemIds: [],
      sourceType: '',
      vehicleMileage: 0,
      baseName: '',
      baseId: '',
      supplierId: '',
      supplierName: '',
      vehicleNo: '',
      entrustUser: '',
      contact: '',
      itemList: [
        {
          projectId: '',
          packageNumber: 1,
          maintenanceType: '',
          partsUse: '',
          partPrice: '',
          laborPrice: '',
          estimatedCost: '',
          cashPayment: '',
          transferWithoutTaxPayment: '',
          historyMaintenanceCost: '',
          maintenanceCount: 0,
          projectName: '',
          projectItemId: '',
          maintenanceDate: '',
          vehicleMileage: 0,
        },
      ],
      totalEstimatedCost: '',
      totalCashPayment: '',
      totalTransferWithoutTaxPayment: '',
      remark: '',
    }
    emit('resetQuery')
  }
  function submitAdjustCost() {
    adjustCostFormRef.value.validate(async (valid: any) => {
      if (valid) {
        const params = {
          id: state.formData.id,
          adjustCost: state.adjustCostForm.adjustCost,
          actualMaintenanceCost: state.formData.totalEstimatedCost,
          cashPayment: state.formData.totalCashPayment,
          transferWithoutTaxPayment: state.formData.totalTransferWithoutTaxPayment,
        }
        const { data } = await postAdjustCostChangeSubmitApi(params)
        ElMessage.success('调整费用成功')
        resetFormData()
      }
    })
  }
  function handleAdd(index: number) {
    state.formData.itemList.splice(index + 1, 0, {
      projectId: '',
      packageNumber: 1,
      maintenanceType: '',
      partsUse: '',
      partPrice: '',
      laborPrice: '',
      estimatedCost: '',
      cashPayment: '',
      transferWithoutTaxPayment: '',
      historyMaintenanceCost: '',
      projectName: '',
      maintenanceCount: 0,
      projectItemId: '',
      maintenanceDate: '',
      vehicleMileage: 0,
    })
    state.formData.itemList[index + 1].partsUse = state.partsUseList[0].value
    state.formData.itemList[index + 1].maintenanceType = state.maintenanceTypeList[0].value
  }
  async function handleDelete(index: number) {
    if (!state.formData.removeItemIds) {
      state.formData.removeItemIds = []
    }
    //如果当前删除的行存在itemId,将删除行的itemId添加到removeItemIds中
    if (state.formData.itemList[index].itemId) {
      state.formData.removeItemIds.push(state.formData.itemList[index].itemId!)
    }
    state.formData.itemList.splice(index, 1)
    await calculateCost()
  }

  watch(
    () => state.dialogVisible.visible,
    async (newVal) => {
      if (newVal) {
        state.loading = true
        await getMaintenanceTypeSelect()
        await selectvehicleOptionApi()
        await getDepartmentTreeOption()
        await getSupplierSelectOption()
        await getRepairProjectSelectOption()
        await getPartsUseSelectOption()
        if (state.formData.id) {
          await getDetail(state.formData.id)
        }
        state.loading = false
      }
    },
  )
  const getDetail = async (id: string) => {
    const { data } = await getMaintenanceSubmissionEditDetailApi({ id })
    state.formData = data as FormDataVO
    console.log('state.formData: ', state.formData)
    state.formData.itemList.map((item) => {
      if (item.partsUse === '自有') {
        item.partPrice = ''
      }
    })

    state.adjustCostForm.adjustCost = Number(state.formData.adjustCost) || 0
  }
  const getMaintenanceTypeSelect = async () => {
    const { data } = await getMaintenanceTypeSelectApi({})
    state.maintenanceTypeList = data as SelectOptions[]
    state.formData.itemList.map((item) => {
      item.maintenanceType = state.maintenanceTypeList[0].value
    })
  }
  const getDepartmentTreeOption = async () => {
    const { data } = await getDepartmentTreeOptionApi({})
    state.baseNameList = data as SelectOptions[]
  }
  const getSupplierSelectOption = async () => {
    const { data } = await getSupplierSelectOptionApi({})
    state.supplierList = data as SelectOptions[]
  }

  const getVehicleSelectOption = async (keyword: string) => {
    console.log('keyword: ', keyword)

    const { data } = await getVehicleSelectOptionApi({
      dataSource: '车队-车辆',
      label: 'vehicleNo,plateColor',
      value: 'vehicleNo,plateColor',
      carrierType: '自有',
      keyword,
      labelSeparate: '-',
    })
    state.repairVehicleList = data as SelectOptions[]
    state.vehicleLoading = false
  }
  const getRepairProjectSelectOption = async () => {
    const { data } = await getRepairProjectSelectOptionApi({
      dataSource: '维修项目',
      label: 'name',
      value: 'id',
    })
    state.repairProjectList = data as SelectOptions[]
  }
  const getPartsUseSelectOption = async () => {
    const { data } = await getPartsUseSelectOptionApi({})
    state.partsUseList = data as SelectOptions[]
    state.formData.itemList.map((item) => {
      item.partsUse = state.partsUseList[0].value
    })
  }
  // 维修车辆选择
  const handleVehicleChange = async (value: any) => {
    if (!value) return
    state.vehicleLoading = true
    getVehicleSelectOption(value)
    // /logistics/api/out/fleet/insurance/select/vehicleOption
  }
  const handleVehicleChange1 = async (value: any) => {
    console.log('value: ', value)

    state.formData.vehicleNo = value
    await calculateCost()
  }

  const handleProjectChange = async (index: number) => {
    if (!state.formData.supplierId) {
      ElMessage.warning('请选择维修单位')
      state.formData.itemList[index].projectItemId = ''
      return
    }
    if (!state.formData.vehicleNo) {
      ElMessage.warning('请选择维修车辆')
      state.formData.itemList[index].projectItemId = ''
      return
    }
    //如果遍历state.formData.itemList中的存在projectId = '',则return
    if (state.formData.itemList.some((item) => item.projectItemId === '')) {
      return
    }

    state.formData.itemList[index].projectName = state.repairProjectList.find(
      (item: SelectOptions) => item.value === state.formData.itemList[index].projectItemId,
    )!?.label
    await calculateCost(index)
  }
  const calculateCost = async (index?: number) => {
    const params = {
      supplierId: state.formData.supplierId,
      supplierName: state.formData.supplierName,
      vehicleNo: state.formData.vehicleNo,
      itemList: state.formData.itemList.map((item) => {
        return {
          projectItemId: item.projectItemId,
          packageNumber: item.packageNumber,
          maintenanceType: item.maintenanceType,
          partsUse: item.partsUse,
          projectName: item.projectName,
        }
      }),
    }
    if (!params.itemList[0].projectItemId) {
      return
    }
    try {
      const { data } = await postProjectCostApi(params)
      state.formData.itemList.forEach((item, index) => {
        if (item.partsUse === '自有') {
          item.partPrice = ''
        } else {
          console.log(data.itemList[index],'data.itemList[index]');
          
          item.partPrice = data.itemList[index].partPrice
        }
        item.laborPrice = data.itemList[index].laborPrice
        item.estimatedCost = data.itemList[index].estimatedCost
        item.historyMaintenanceCost = data.itemList[index].historyMaintenanceCost
        item.maintenanceCount = data.itemList[index].maintenanceCount
        item.cashPayment = data.itemList[index].cashPayment
        item.transferWithoutTaxPayment = data.itemList[index].transferWithoutTaxPayment
        item.projectItemId = data.itemList[index].projectItemId
        item.projectId = data.itemList[index].projectId
        item.maintenanceDate = data.itemList[index].maintenanceDate
        item.vehicleMileage = data.itemList[index].vehicleMileage
      })

      state.formData.totalEstimatedCost = data.totalEstimatedCost
      state.formData.totalCashPayment = data.totalCashPayment
      state.formData.totalTransferWithoutTaxPayment = data.totalTransferWithoutTaxPayment
    } catch (error) {
      if (index !== undefined) {
        state.formData.itemList[index] = {
          projectId: '',
          packageNumber: 1,
          maintenanceType: '',
          partsUse: '',
          partPrice: '',
          laborPrice: '',
          estimatedCost: '',
          cashPayment: '',
          transferWithoutTaxPayment: '',
          historyMaintenanceCost: '',
          maintenanceCount: 0,
          projectName: '',
          projectItemId: '',
          maintenanceDate: '',
          vehicleMileage: 0,
        }
        state.formData.itemList[index].partsUse = state.partsUseList[0].value
        state.formData.itemList[index].maintenanceType = state.maintenanceTypeList[0].value
      }
    }
  }
  const handleSupplierChange = async (value: any) => {
    state.formData.supplierName = state.supplierList.find((item: SelectOptions) => item.value === value)!.label
    if (!state.formData.vehicleNo) {
      ElMessage.warning('请选择维修车辆')
      return
    }
    //如果遍历state.formData.itemList中的存在projectId = '',则return
    if (state.formData.itemList.some((item) => item.projectItemId === '')) {
      return
    }
    await calculateCost()
  }
  watch(
    () => state.adjustCostForm.adjustCost,
    (newVal) => {
      if (newVal) {
        //查看不调用
        if (!state.isView) {
          handleAdjustCost()
        }
      }
    },
  )
  const handleAdjustCost = async () => {
    //校验表单
    adjustCostFormRef.value.validate(async (valid: any) => {
      if (valid) {
        const params = {
          id: state.formData.id,
          adjustCost: state.adjustCostForm.adjustCost,
        }
        const { data } = await postAdjustCostChangeApi(params)
        state.formData.totalEstimatedCost = data.actualMaintenanceCost
        state.formData.totalCashPayment = data.cashPayment
        state.formData.totalTransferWithoutTaxPayment = data.transferWithoutTaxPayment
      }
    })
  }
  defineExpose({
    state,
    getDetail,
  })
</script>

<style scoped>
  .dialog-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
</style>
