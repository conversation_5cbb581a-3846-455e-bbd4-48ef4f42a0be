<!--
 * @Author: llm
 * @Date: 2023-07-14 10:39:43
 * @LastEditors: llm
 * @LastEditTime: 2024-05-13 16:57:51
 * @Description: 倒板
 *
-->
<template>
  <el-drawer
    v-model="dialogVisible.visible"
    title="倒板调度"
    :size="formData.body?.length === 1 ? '30%' : formData.body?.length === 2 ? '50%' : '80%'"
    direction="rtl"
  >
    <el-scrollbar max-height="100vh" class="formClass">
      <el-form ref="formRef" :model="formData" label-width="100px">
        <el-row :gutter="30">
          <el-col v-for="(v, i) in formData.body" :key="i" :span="formData.body?.length === 1 ? 24 : formData.body?.length === 2 ? 12 : 8">
            <el-card class="box-card">
              <el-form-item label="选择换车位置" prop="addressType">
                <el-radio-group v-model="v.addressType" @change="selectAddressType($event, i)">
                  <el-radio :label="1">自选位置</el-radio>
                  <el-radio :label="2">仓库</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="倒板位置" prop="warehousePlace" v-if="v.addressType === 1">
                <el-input
                  v-model="v.warehousePlace"
                  type="text"
                  clearable
                  :readonly="true"
                  placeholder="请输入仓库地址"
                  @click.native.stop="loctionAdress(i)"
                />
              </el-form-item>
              <el-form-item label="请选择仓库" prop="warehouseId" v-else-if="v.addressType === 2">
                <el-select v-model="v.warehouseId" filterable clearable placeholder="模糊搜索" style="width: 200px">
                  <el-option v-for="item in JSON.parse(JSON.stringify(carriedWarehouseList))" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>

              <el-form-item label="倒板车辆" prop="vehicleId">
                <el-select
                  v-model="v.vehicleId"
                  filterable
                  clearable
                  placeholder="模糊搜索"
                  @change="selectApplyVehicle"
                  @click="setCurrentIndex(i)"
                  style="width: 200px"
                >
                  <el-option
                    v-for="item in JSON.parse(JSON.stringify(carriedVehicleList))"
                    :key="item.vehicleId"
                    :label="item.vehicleNo"
                    :value="item.vehicleId"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="倒板司机" prop="driverId">
                <el-select v-model="v.driverId" filterable clearable placeholder="模糊搜索" style="width: 200px">
                  <el-option v-for="item in JSON.parse(JSON.stringify(carriedDriverList))" :key="item.id" :label="item.realName" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item label="倒板车辆vin" prop="vin" v-if="v.vehicleId">
                <div style="width: 100%; text-align: right">
                  <el-button type="primary" size="small" @click="daoban(i)">倒板</el-button>
                </div>

                <TableComponent :tableData="v.idList" :tableConfig="tableConfig" @batchDelete="selectVins" v-if="v.idList!.length > 0" />
                <el-text class="mx-1" v-else>当前车辆下未绑定任何vin</el-text>
              </el-form-item>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" v-loading="submitLoading" @click="handleSubmit()">确 定</el-button>
        <el-button @click="closeDialog()">取 消</el-button>
      </div>
    </template>
    <map-search-dialog
      v-if="mapDialog.visible"
      :dialog="mapDialog"
      :pointData="mapData"
      @closeMapDialog="closeMapDialog"
      @submitLocation="submitLocation"
    ></map-search-dialog>
    <el-dialog :draggable="true" v-model="dialogFormVisible.visible" title="指定车辆" append-to-body width="500px">
      <el-form ref="formRef" :model="formData">
        <el-form-item label="指定车辆" prop="vehicleId">
          <el-select
            style="width: 200px"
            v-model="currentAssignVehicle.vehicleId"
            value-key="vehicleId"
            placeholder="请选择车辆"
            filterable
            clearable
            remote
            reserve-keyword
            :remote-method="remoteVehicleMethod"
            :loading="vehicleListLoading"
            @change="selectAssignApplyVehicle"
          >
            <el-option v-for="i in carriedVehicleList" :key="i.vehicleId" :label="i.vehicleNo" :value="i.vehicleId!" />
          </el-select>
        </el-form-item>
        <div v-if="currentAssignVehicleVinList.length > 0">
          <div>当前车辆下绑定的vin:</div>
          <div v-for="(item, index) in currentAssignVehicleVinList" :key="index">
            {{ item.vin }}
          </div>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogFormVisible.visible = false">取消</el-button>
          <el-button type="primary" @click="selectAssignVehicle">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </el-drawer>
</template>
<script setup lang="ts">
  // @ts-nocheck
  import MapSearchDialog from '@/components/MapSearchDialog/index.vue'
  import { DriverVO, VehicleVO, WarehouseVO } from '@/types/global'
  import { ExchangeVehicleParamsVo, ExchangeVehicleParamsBodyVo, VinInfoVO } from '@/api/planManagement/type'
  import { carrierVehiclesApi, carrierDriversApi, carrierWarehouseApi, vehicleVinList, transferVehicle } from '@/api/planManagement/index'
  const emit = defineEmits([
    'closeDialog', //关闭弹窗
    'flipBoardResult', // 倒板结果
  ])
  const formRef = ref()
  const props = defineProps({
    /**
     * 弹窗属性
     */
    dialogVisible: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
    vehicleFlipBoardParams: {
      require: true,
      type: Object as PropType<any>,
      default: () => {
        return {}
      },
    },
  })
  const mapData = ref({})
  //车辆列表加载loading
  const vehicleListLoading = ref<boolean>(false)
  //司机列表加载loading
  const driverListLoading = ref<boolean>(false)
  //仓库列表加载loading
  const warehouseListLoading = ref<boolean>(false)
  //承运商车辆列表
  const carriedVehicleList = ref<VehicleVO[]>([])
  //承运商司机列表
  const carriedDriverList = ref<DriverVO[]>([])
  //承运商仓库列表
  const carriedWarehouseList = ref<WarehouseVO[]>([])
  // 当前选中需要分配的车辆
  const currentAssignVehicle = ref<VehicleVO>({})
  // 需要分配的车辆下绑定的 vin
  const currentAssignVehicleVinList = ref<VinInfoVO[]>([])

  const formData = ref<ExchangeVehicleParamsVo>({
    body: [
      {
        addressType: 1,
        idList: [],
      },
    ],
    operationType: 1, //操作类型1-倒板 2-换车
    source: 0, //申请来源：0-调度 1-司机
    origin: {
      vehicleId: null,
      driverId: null,
    },
  })
  watch(
    () => props.dialogVisible.visible,
    (visible) => {
      if (visible) {
        remoteVehicleMethod('')
        remoteDriverMethod('')
        remoteWarehouseMethod('')
      }
    },
    {
      deep: true,
    },
  )
  const mapDialog = reactive<DialogOption>({
    visible: false,
  })
  const setCurrentIndex = (index: number) => {
    currentDataIndex.value = index
  }
  /**
   * 选择vin
   */
  const selectVins = (arr: any[]) => {
    selectVinsArr.value = arr
  }
  /**
   * 选择位置
   * @param index 选中项下标
   */
  const loctionAdress = async (index: number) => {
    currentDataIndex.value = index
    mapDialog.visible = true //开启弹窗
    mapDialog.title = '选择位置'
    mapData.value = {
      address: formData.value.body![index].warehousePlace,
      coordinate: formData.value.body![index].coordinate,
    }
  }
  // 关闭子弹窗
  function closeMapDialog() {
    mapDialog.visible = false //关闭弹窗
  }

  // 子弹窗确认
  const submitLocation = (data: any) => {
    if (data) {
      formData.value.body![currentDataIndex.value].warehousePlace = data.address
      formData.value.body![currentDataIndex.value].coordinate = data.coordinate
      closeMapDialog()
    }
  }
  //切换换车地
  const selectAddressType = (e: any, index: number) => {
    //循环 formData.value.body，如果e===1 初始化formData.value.body 中所有项中的warehousePlace='',如果e===2 初始化formData.value.body warehouseId 和coordinate=''
    if (e === 2) {
      formData.value.body[index].warehousePlace = ''
    }
    if (e === 1) {
      formData.value.body[index].warehouseId = ''
      formData.value.body[index].warehouseName = ''
      formData.value.body[index].coordinate = ''
    }
  }
  /**
   * 获取板车上所有vin
   */
  const getVehicleVinList = (vehicleId: string) => {
    const params = {
      vehicleId,
      transportType: props.vehicleFlipBoardParams.transportType,
      shipmentType: props.vehicleFlipBoardParams.shipmentType,
    }
    return new Promise((resolve) => {
      vehicleVinList(params).then((res) => {
        resolve(res.data)
      })
    })
  }
  /**
   * 选择申请的车牌号，并查询所绑定的vin
   */
  const selectApplyVehicle = async (vehicleId: string) => {
    const vehicleList = (await getVehicleVinList(vehicleId)) as VehicleVO[]
    if (vehicleList.length > 0) {
      formData.value.body![currentDataIndex.value].idList = vehicleList as any[]
    } else {
      formData.value.body![currentDataIndex.value].idList = []
    }
  }
  /**
   * 选择指定车辆，并查询所绑定的vin
   */
  const selectAssignApplyVehicle = async (vehicleId: string) => {
    let vinList = [] as VinInfoVO[]
    vinList = (await getVehicleVinList(vehicleId)) as VinInfoVO[]
    if (vinList.length > 0) {
      currentAssignVehicleVinList.value = vinList
    } else {
      currentAssignVehicleVinList.value = []
    }
  }
  /**
   * 数据
   */
  const data = ref<any>()
  /**
   * 提交加载动画
   */
  const submitLoading = ref(false)
  /**
   * 选择 车辆弹窗属性
   */
  const dialogFormVisible = reactive<DialogOption>({
    visible: false,
  })
  /**
   * 选中的vin
   */
  const selectVinsArr = ref<VinInfoVO[]>([])
  /**
   * 当前选中data数据中的某项下标
   */
  const currentDataIndex = ref<number>(0)
  /**
   * 提交
   */
  const handleSubmit = () => {
    formData.value.origin.driverId = formData.value.body[0]?.driverId
    formData.value.origin.vehicleId = formData.value.body[0]?.vehicleId
    const params = JSON.parse(JSON.stringify(formData.value))
    params.body?.forEach((item: ExchangeVehicleParamsBodyVo, index: number) => {
      delete item.addressType
      let vinListTemp: string[] = []
      item.idList!.forEach((v: any) => {
        vinListTemp.push(v.id)
      })
      params.body![index].idList = vinListTemp
    })
    emit('flipBoardResult', params)
  }
  /**
   * 关闭弹窗
   */
  const closeDialog = () => {
    formData.value = {
      body: [
        {
          addressType: 1,
          idList: [],
        },
      ],
      operationType: 1, //操作类型1-倒板 2-换车
      source: 0, //申请来源：0-调度 1-司机
      origin: {
        vehicleId: null,
        driverId: null,
      },
    }
    emit('closeDialog')
  }
  //远程获取承运商车辆列表
  const remoteVehicleMethod = (query: string) => {
    // if (query) {
    vehicleListLoading.value = true
    carrierVehiclesApi({ vehicleNo: query }).then((res) => {
      carriedVehicleList.value = res.data as VehicleVO[]
    })
    // } else {
    //   // carriedVehicleList.value = [];
    // }
    vehicleListLoading.value = false
  }
  //远程获取承运商仓库列表
  const remoteDriverMethod = (query: string) => {
    // if (query) {
    driverListLoading.value = true
    carrierDriversApi({ name: query }).then((res) => {
      carriedDriverList.value = res.data as DriverVO[]
    })
    // } else {
    //   // carriedDriverList.value = [];
    // }
    driverListLoading.value = false
  }
  //远程获取承运商仓库列表
  const remoteWarehouseMethod = (query: string) => {
    // if (query) {
    warehouseListLoading.value = true
    carrierWarehouseApi({ name: query }).then((res) => {
      carriedWarehouseList.value = res.data as WarehouseVO[]
    })
    // } else {
    //   // carriedWarehouseList.value = [];
    // }
    warehouseListLoading.value = false
  }
  /**
   * 将选中的 vin  分配给指定车辆
   * @param item 当前选中的data中的某个item
   * @param index 当前选中的data中的某个item的下标
   * @param _index 当前选中的data中的某个item的承运商下标
   */
  const assignVehicles = (item: any, index: number, _index: number) => {
    dialogFormVisible.visible = true
    currentDataIndex.value = index
  }
  /**
   * 列表配置项
   */
  const tableConfig = reactive<TableConfig>({
    showHandleSelection: true,
    showSort: false,
    tableItem: [
      {
        name: 'vin',
        label: 'VIN',
        align: 'left',
        type: 'text',
        listEnable: true,
      },
    ],
  })

  //调换 vin 位置
  const daoban = (i: number) => {
    if (selectVinsArr.value.length > 0) {
      currentDataIndex.value = i
      dialogFormVisible.visible = true
    } else {
      ElMessage.warning('请先勾选需要倒板的vin')
    }
  }
  //选择分配的车辆
  function selectAssignVehicle() {
    // 循环formData.value.body  判断item.vehicleId=currentAssignVehicle.value 是否存在
    const existArr: any = formData.value.body?.filter((item) => item.vehicleId === currentAssignVehicle.value.vehicleId)
    // 删除之前包含 selectVinsArr.value 的数据
    formData.value.body![currentDataIndex.value].idList = formData.value.body![currentDataIndex.value].idList!.filter(
      (item: any) => !selectVinsArr.value.includes(item),
    )
    if (existArr?.length > 0) {
      ElMessage.error('当前车辆已存在')
      formData.value.body!.forEach((item) => {
        if (item.vehicleId === currentAssignVehicle.value.vehicleId) {
          item.idList = item.idList!.concat(selectVinsArr.value)
        }
      })
      dialogFormVisible.visible = false
    } else {
      formData.value.body!.push({
        addressType: 1,
        vehicleId: currentAssignVehicle.value.vehicleId,
        idList: currentAssignVehicleVinList.value.concat(selectVinsArr.value),
      })
    }
    dialogFormVisible.visible = false
    //清空选中的 vin
    selectVinsArr.value = []
  }
  defineExpose({ data, formData })
</script>
<style lang="scss" scoped>
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .box-card {
    margin-bottom: 20px;
  }
</style>
