<template>
  <div style="margin-left: 20px; width: 45%; margin-bottom: 10px; display: flex">
    <div style="width: 95%">
      <el-form ref="fuelcostFromRef" :model="allState" style="width: 100%">
        <el-descriptions class="margin-top" :column="2" size="small" :border="true" title="油费结算">
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">应付油升数</div>
            </template>
            <div style="display: flex; justify-content: space-between">
              {{ allState.shouldPaymentLiters }}
              <div>升</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">应付油费</div>
            </template>
            <div style="display: flex; justify-content: space-between">
              {{ allState.shouldPaymentOilFee }}
              <div>元</div>
            </div>
          </el-descriptions-item>
          <el-icon color="#F59A23" size="20">
            <QuestionFilled />
          </el-icon>

          <el-descriptions-item>
            <template #label>
              <div class="cell-item">基地油升数</div>
            </template>
            <div style="display: flex; justify-content: space-between">
              {{ allState.baseLiters }}
              <div>升</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">基地油费</div>
            </template>
            <div style="display: flex; justify-content: space-between">
              {{ allState.baseOilFee }}
              <div>元</div>
            </div>
          </el-descriptions-item>
          <el-icon color="#F59A23" size="20">
            <QuestionFilled />
          </el-icon>
          <!-- <el-descriptions-item>
            <template #label>
              <div class="cell-item">借支油升数</div>
            </template>
            <div style="display: flex; justify-content: space-between">
              {{ allState.oilLoanLiters }}
              <div>升</div>
            </div>
          </el-descriptions-item>  -->
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">油费补贴</div>
            </template>
            <div style="display: flex; justify-content: space-between">
              {{ allState.oilFeeSubsidy || 0 }}
              <div>元</div>
              <el-icon class="cursor-pointer" color="#F59A23" size="20" @click="clickFour">
                <QuestionFilled />
              </el-icon>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">借支油费</div>
            </template>
            <div style="display: flex; justify-content: space-between">
              {{ allState.loanOilFee }}
              <div>元</div>
            </div>
          </el-descriptions-item>
          <el-icon color="#F59A23" size="20">
            <QuestionFilled />
          </el-icon>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">油费结余</div>
            </template>
            <div class="buttonTogg">
              <el-button v-if="state.isToggle == 1" :disabled="allState.isvist ? true : false" color="#F59A23" @click="toggleTabone">
                <el-tooltip class="box-item" content="（应付油升数-基地油升数）*司机油价-借支油费+油费补贴" effect="light" placement="bottom">
                  <span style="color: #fff">切换到升数结余计算</span>
                </el-tooltip>
              </el-button>
              <div class="butPri">{{ state.isToggle == 1 ? allState.oilFeeBalanceByFee : allState.oilFeeBalanceByLiters }} 元</div>
              <el-button v-if="state.isToggle == 0" :disabled="allState.isvist ? true : false" color="#F59A23" @click="toggleTab">
                <el-tooltip class="box-item" content="应付油费-基地油费-借支油费+油费补贴" effect="light" placement="bottom">
                  <span style="color: #fff">切换到油费结余计算</span>
                </el-tooltip>
              </el-button>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
      <!-- 油费支付 -->
      <el-form ref="fuelcostFromRef" :model="allState">
        <el-descriptions class="margin-top" :column="1" size="small" :border="true" label-width="100" title="油费支付">
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">油费付款金额</div>
            </template>
            <!-- <el-input-number
              v-model="allState.oilFeeAmount"
              style="width: 100%"
              :min="min"
              :disabled="allState.isvist ? true : false"
              :precision="2"
              @change="editIilFee"
            >
              <template #suffix>
                <span>元</span>
              </template>
            </el-input-number> -->
            <el-input-number
              v-model="allState.oilFeeAmount"
              :min="dynamicMin"
              :max="dynamicMax"
              :precision="2"
              :disabled="allState.isvist ? true : false"
              @change="handleAmountChange"
              style="width: 100%"
            >
              <template #suffix>
                <span>元</span>
              </template>
            </el-input-number>
            <template></template>
          </el-descriptions-item>

          <el-descriptions-item>
            <template #label>
              <!-- “本次油费冲账”是指用户在本次结算扣掉司机油费挂账的费用，输入数值大于等于0小于等于历史油费挂账 -->
              <div class="cell-item">本次油费冲账</div>
            </template>
            <el-input-number
              v-model="allState.oilFeeBalanceAmount"
              :min="0"
              :max="allState.oilFeeHistoryChargeAmount"
              style="width: 100%"
              :disabled="allState.isvist ? true : false"
              :precision="2"
              @change="editAmount"
            >
              <template #suffix>
                <span>元</span>
              </template>
            </el-input-number>
          </el-descriptions-item>
          <el-icon color="#F59A23" size="20">
            <QuestionFilled />
          </el-icon>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">本次油费挂账</div>
            </template>

            <el-input-number
              v-model="allState.oilFeeChargeAmount"
              :min="0"
              :precision="2"
              :disabled="allState.isvist ? true : false"
              :max="state.isToggle == 1 ? allState.oilFeeBalanceByFee : allState.oilFeeBalanceByLiters"
              style="width: 100%"
              @change="editPosting"
            >
              <template #suffix>
                <span>元</span>
              </template>
            </el-input-number>
            <!-- <div style="display: flex; justify-content: space-between">
              {{ allState.oilFeeChargeAmount }}
              <div>元</div>
            </div> -->
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">历史挂账余额</div>
            </template>
            <div style="display: flex; justify-content: space-between">
              {{ allState.oilFeeHistoryChargeAmount }}
              <div>元</div>
            </div>
          </el-descriptions-item>

          <el-descriptions-item>
            <template #label>
              <div class="cell-item">累计挂账余额</div>
            </template>
            <div style="display: flex; justify-content: space-between">
              {{ oilTotal }}
              <div>元</div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </div>
    <div class="cancelClass">
      <div style="height: 36px">
        <el-icon color="#F59A23" size="20" class="el-icons" style="margin-top: 5px" @click="clickone">
          <QuestionFilled />
        </el-icon>
      </div>
      <div style="height: 36px">
        <el-icon color="#F59A23" size="20" class="el-icons" style="margin-top: 5px" @click="clicktwo">
          <QuestionFilled />
        </el-icon>
      </div>

      <div style="height: 36px">
        <el-icon color="#F59A23" size="20" class="el-icons" style="margin-top: 5px" @click="clickthree">
          <QuestionFilled />
        </el-icon>
      </div>
    </div>

    <!-- 表格弹框 -->
    <el-dialog v-model="dialogVisible" :title="state.title" width="80vw" :before-close="handleClose">
      <el-table :data="tableData" :summary-method="getSummariesde" border max-height="50vh" show-summary style="width: 100%">
        <el-table-column align="center" label="序号" type="index" width="60" fixed="left"></el-table-column>
        <el-table-column v-for="item in toggeleTitle" :label="item.label" :prop="item.name" :key="item.id" />
      </el-table>
      <!-- <div class="demo-pagination-block" style="margin-top: 10px">
        <el-pagination
          v-model:current-page="currentPage4"
          v-model:page-size="pageSize4"
          :page-sizes="[100, 200, 300, 400]"
          :size="size"
          :disabled="disabled"
          layout="total, sizes, prev, pager, next, jumper"
          :total="400"
        />
      </div> -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 报销明细传值 -->
    <ModelTable ref="modelAmountRef" :driver-id-data="props.driverId" :expense-items-child="[]" :month="props.month" :visible="dialogVisibleds" />
  </div>
</template>

<script setup lang="ts">
  import bus from '@/utils/bus'
  import { shouldPaymentOilFeeDetailApi, baseOilFeeDetailApi, loanOilFeeDetailApi, postOilFeeSubsidyDetailApi } from '@/api/financialManagement'
  import ModelTable from '@/components/OtherFormDialogComponent/ModelTable/index.vue'
  const modelAmountRef = ref()
  const dialogVisibleds = ref(false)
  const state = ref<any>({
    questionMark: 1, // 3个问号的切换
    isToggle: 0, // 0为切换到油费结余计算 1为切换到升数结余计算
    amountFuelPayment: 0, //油费付款金额
    title: '应付油费明细', // 基地油明细 油费借支明细
    oilFeePayType: '1',
  })
  const allState = reactive<any>({
    ids: [],
    settlementNo: '',
    isvist: '',
    oilFeeSubsidy: '0', //油费补贴
    shouldPaymentLiters: '0', //应付油升数
    shouldPaymentOilFee: '0', //应付油费
    baseLiters: '0', //基地油升数
    baseOilFee: '0', //基地油费
    oilLoanLiters: '0', //借支油升数
    loanOilFee: '0', //借支油费
    oilFeeBalanceByLiters: '0', //油费结余(根据油升数)
    oilFeeBalanceByFee: '0', //油费结余(根据费用)
    oilFeeAmount: '0', //油费付款金额
    oilFeeBalanceAmount: '0', //本次油费冲账
    oilFeeChargeAmount: '0', //本次油费挂账
    oilFeeHistoryChargeAmount: '0', //历史挂账金额
    totalOilFeeChargeAmount: '0', //累计挂账金额
  })
  const toggeleTitle = ref<any>({})
  const emit = defineEmits(['changeNum', 'changeTol', 'changeFuel'])
  const oldDate = ref<any>()
  const oldDateFun = () => {
    oldDate.value = JSON.parse(JSON.stringify(toRaw(allState)))
    let num = Number(allState.oilFeeAmount) - Number(allState.oilFeeBalanceAmount)
    emit('changeFuel', num)
    console.log(oldDate.value, 'oldDate.value')
  }

  const props = defineProps<{
    month: string
    driverId: string
  }>()
  const tableConfig = [
    {
      name: 'dispatchNo',
      label: '调度单号',
    },
    {
      name: 'departureDate',
      label: '发车日期',
    },
    {
      name: 'settleMileageFull',
      label: '结算满载里程',
    },
    {
      name: 'settleMileageEmpty',
      label: '结算空载里程',
    },
    {
      name: 'fullOilCost',
      label: '重驶油耗',
    },
    {
      name: 'emptyOilCost',
      label: '空驶油耗',
    },
    {
      name: 'shouldPaymentLiters',
      label: '应付油升数',
    },
    {
      name: 'companyOilPrice',
      label: '公司油价',
    },
    {
      name: 'shouldPaymentOilFee',
      label: '应付油费',
    },
  ]
  const tableConfige = [
    {
      name: 'vehicleNo',
      label: '车牌号',
    },
    {
      name: 'refuelTime',
      label: '加油日期',
    },
    {
      name: 'dispatchNo',
      label: '调度单号',
    },
    {
      name: 'liters',
      label: '加油升数',
    },
    {
      name: 'oilPrice',
      label: '司机油价',
    },
    {
      name: 'oilMoney',
      label: '加油金额',
    },
  ]
  const tableConfigo = [
    {
      name: 'oilBillNumber',
      label: '油费单号',
    },
    {
      name: 'auditStatus',
      label: '状态',
    },
    {
      name: 'dispatchNo',
      label: '调度单号',
    },
    {
      name: 'sinopecAmount',
      label: '中石化',
    },
    {
      name: 'petroChinaAmount',
      label: '中石油',
    },
    {
      name: 'wanjinOilAmount',
      label: '万金油',
    },
    {
      name: 'totalAmount',
      label: '总金额',
    },
  ]
  const tableData = ref<any>()
  const dialogVisible = ref(false)

  // 切换油费结余计算时，油费支付还原
  const toggleTab = () => {
    state.value.isToggle = 1
    state.value.oilFeePayType = 2
    allState.oilFeeSubsidy = oldDate.value.oilFeeSubsidy
    allState.oilFeeChargeAmount = 0
    allState.oilFeeBalanceAmount = 0
    allState.oilFeeAmount = allState.oilFeeBalanceByFee
    allState.oilFeeHistoryChargeAmount = oldDate.value.oilFeeHistoryChargeAmount
    allState.totalOilFeeChargeAmount = oldDate.value.totalOilFeeChargeAmount
    let num = Number(allState.oilFeeAmount) - Number(allState.oilFeeBalanceAmount)
    emit('changeFuel', num)
  }
  const toggleTabone = () => {
    state.value.isToggle = 0
    state.value.oilFeePayType = 1
    allState.oilFeeSubsidy = oldDate.value.oilFeeSubsidy
    allState.oilFeeChargeAmount = 0
    allState.oilFeeBalanceAmount = 0
    allState.oilFeeAmount = allState.oilFeeBalanceByLiters
    allState.oilFeeHistoryChargeAmount = oldDate.value.oilFeeHistoryChargeAmount
    allState.totalOilFeeChargeAmount = oldDate.value.totalOilFeeChargeAmount
    let num = Number(allState.oilFeeAmount) - Number(allState.oilFeeBalanceAmount)
    emit('changeFuel', num)
  }
  const clickone = async () => {
    if (allState.ids.length <= 0) {
      return ElMessage.warning('请选择结算月份！')
    }

    state.value.title = '应付油费明细'
    toggeleTitle.value = tableConfig
    dialogVisible.value = true
    let { data } = await shouldPaymentOilFeeDetailApi({
      ids: allState.ids,
    })
    tableData.value = data.items
  }
  const clicktwo = async () => {
    if (allState.ids.length <= 0) {
      return ElMessage.warning('请选择结算月份！')
    }
    state.value.title = '基地油明细'
    toggeleTitle.value = tableConfige
    dialogVisible.value = true
    let { data } = await baseOilFeeDetailApi({
      ids: allState.ids,
      settlementNo: allState.settlementNo,
    })
    tableData.value = data.items
  }
  // 本次油费挂帐更改
  const editPosting = () => {
    if (state.value.isToggle == 1) {
      allState.oilFeeAmount = (allState.oilFeeBalanceByFee - allState.oilFeeChargeAmount).toFixed(2)
    } else {
      allState.oilFeeAmount = (allState.oilFeeBalanceByLiters - allState.oilFeeChargeAmount).toFixed(2)
    }
    console.log(allState.oilFeeAmount, allState.oilFeeBalanceAmount, 'allState.oilFeeAmount')

    emit('changeNum', allState.oilFeeAmount)
    emit('changeTol', allState.oilFeeBalanceAmount)
    let num = Number(allState.oilFeeAmount) - Number(allState.oilFeeBalanceAmount)
    emit('changeFuel', num)
  }

  const clickthree = async () => {
    if (allState.ids.length <= 0) {
      return ElMessage.warning('请选择结算月份！')
    }
    state.value.title = '油费借支明细'
    toggeleTitle.value = tableConfigo
    dialogVisible.value = true
    let { data } = await loanOilFeeDetailApi({
      ids: allState.ids,
      settlementNo: allState.settlementNo,
    })
    tableData.value = data.items
  }
  const clickFour = async () => {
    if (allState.ids.length <= 0) {
      return ElMessage.warning('请选择结算月份！')
    }
    let params = {}
    if (allState.settlementNo) {
      params = {
        // driverId: props.driverId,
        // month: props.month,
        settlementNo: allState.settlementNo,
        onlyView: allState.isvist ? true : undefined,
      }
    } else {
      params = {
        // driverId: props.driverId,
        // month: props.month,
        ids: allState.ids,
        onlyView: allState.isvist ? true : undefined,
      }
    }
    let { data } = await postOilFeeSubsidyDetailApi(params)
    modelAmountRef.value?.getbalfun(null, null, null, null, null, data)
    modelAmountRef.value.visible = true
    modelAmountRef.value.title = '油费补贴明细'
    modelAmountRef.value.gridList = [
      { name: '调度单号', property: 'dispatchNo' },
      { name: '加油升数', property: 'liters' },
      { name: '加油总价', property: 'amount' },
      { name: '公司油价', property: 'companyOilPrice' },
      { name: '公司油费', property: 'companyAmount' },
      { name: '油费补贴', property: 'subsidy' },
    ]
  }
  // 合计-计算
  const getSummariesde = (param: any) => {
    if (tableData.value.length === 0) {
      return []
    }
    const { columns, data } = param
    const sums: any[] = []
    var arr = [
      'shouldPaymentOilFee',
      'settleMileageEmpty',
      'settleMileageFull',
      'shouldPaymentLiters',
      'petroChinaAmount',
      'sinopecAmount',
      'totalAmount',
      'wanjinOilAmount',
      'liters',
      'oilMoney',
    ]
    columns.forEach((column: { property: string }, index: number) => {
      if (index === 0) {
        sums[index] = '合计'
        return
      }
      if (arr.includes(column.property)) {
        sums[index] = data.reduce((sum: number, row: any) => {
          const value = Number(row[column.property])
          if (!isNaN(value)) {
            return sum + value
          } else {
            return sum
          }
        }, 0)
        if (column.property === 'loadCount') {
          sums[index] = Math.round(sums[index])
        } else {
          sums[index] = sums[index].toFixed(2)
        }
        sums[index] += ' '
      } else {
        sums[index] = ''
      }
    })
    return sums
  }
  const min = ref<any>()
  const max = ref<any>()
  const editIilFee = () => {
    if (allState.oilFeeBalanceByFee > 0) {
      min.value = 0
    } else {
      min.value = allState.oilFeeBalanceByFee
    }
    // max.value = allState.oilFeeBalanceByFee

    if (allState.oilFeeBalanceByLiters > 0) {
      min.value = 0
    } else {
      max.value = allState.oilFeeBalanceByFee
    }

    // 本次油费挂账联动到油费付款金额（即等于油费结余金额-油费付款金额）；
    if (state.value.isToggle == 0) {
      allState.oilFeeChargeAmount = (allState.oilFeeBalanceByFee - allState.oilFeeAmount + allState.oilFeeSubsidy).toFixed(2)
    } else {
      //根据升数
      allState.oilFeeChargeAmount = (allState.oilFeeBalanceByLiters - allState.oilFeeAmount + allState.oilFeeSubsidy).toFixed(2)
    }
  }
  const editAmount = () => {
    allState.totalOilFeeChargeAmount = (allState.oilFeeHistoryChargeAmount - allState.oilFeeBalanceAmount - allState.oilFeeChargeAmount).toFixed(2)
    emit('changeNum', allState.oilFeeAmount)
    emit('changeTol', allState.oilFeeBalanceAmount)
    let num = Number(allState.oilFeeAmount) - Number(allState.oilFeeBalanceAmount)
    emit('changeFuel', num)
  }
  const handleClose = (done: () => void) => {
    done()
  }
  // 根据结算模式获取当前基准值
  const currentBalance = computed(() => {
    return state.value.isToggle === 1 ? allState.oilFeeBalanceByFee : allState.oilFeeBalanceByLiters
  })

  // 动态计算输入范围
  const dynamicMin = computed(() => {
    return currentBalance.value > 0 ? 0 : currentBalance.value
  })

  const dynamicMax = computed(() => {
    return currentBalance.value > 0 ? currentBalance.value : 0
  })

  // 输入变化处理
  const handleAmountChange = (value: any) => {
    console.log('numfffff')

    // 自动修正越界值
    if (value > dynamicMax.value) {
      allState.oilFeeAmount = dynamicMax.value
    } else if (value < dynamicMin.value) {
      allState.oilFeeAmount = dynamicMin.value
    }
    // 更新挂账金额
    allState.oilFeeChargeAmount = (currentBalance.value - allState.oilFeeAmount).toFixed(2)
    // 更改累计挂账金额
    allState.totalOilFeeChargeAmount = (allState.oilFeeHistoryChargeAmount - allState.oilFeeBalanceAmount - allState.oilFeeChargeAmount).toFixed(2)
    emit('changeNum', allState.oilFeeAmount)
    emit('changeTol', allState.oilFeeBalanceAmount)
    let num = Number(allState.oilFeeAmount) - Number(allState.oilFeeBalanceAmount)
    emit('changeFuel', num)
  }
  // 重置
  const resetToggleState = () => {
    state.value.isToggle = 0
    state.value.oilFeePayType = 1
  }
  // 监听结算模式切换
  watch(
    () => state.value.isToggle,
    () => {
      // 重置金额时保持范围有效性
      allState.oilFeeAmount = Math.max(dynamicMin.value, Math.min(allState.oilFeeAmount, dynamicMax.value))
      emit('changeNum', allState.oilFeeAmount)
      emit('changeTol', allState.oilFeeBalanceAmount)
      let num = Number(allState.oilFeeAmount) - Number(allState.oilFeeBalanceAmount)
      emit('changeFuel', num)
    },
  )
  // <div class="butPri">{{ state.isToggle == 1 ? allState.oilFeeBalanceByFee : allState.oilFeeBalanceByLiters }} 元</div>

  const oilTotal = computed(() => {
    // 本次油费挂账联动到油费付款金额（即等于油费结余金额-油费付款金额）；
    if (state.value.isToggle == 1) {
      allState.oilFeeChargeAmount = (allState.oilFeeBalanceByFee - allState.oilFeeAmount).toFixed(2)
    } else {
      //根据升数
      allState.oilFeeChargeAmount = (allState.oilFeeBalanceByLiters - allState.oilFeeAmount).toFixed(2)
    }
    return (allState.oilFeeHistoryChargeAmount - allState.oilFeeBalanceAmount - allState.oilFeeChargeAmount).toFixed(2)
  })
  defineExpose({
    allState,
    state,
    tableData,
    oldDateFun,
    resetToggleState,
  })
</script>

<style lang="scss" scoped>
  .buttonTogg {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .butPri {
    font-weight: bold;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .cancelClass {
    display: flex;
    flex-direction: column;
    margin-top: 30px;
    margin-left: 5px;
  }
  .el-icons:hover {
    cursor: pointer;
  }
</style>
