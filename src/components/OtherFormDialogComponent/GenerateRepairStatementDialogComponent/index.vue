<!--
 * @Author: llm
 * @Date: 2024-12-27 19:37:32
 * @LastEditors: llm
 * @LastEditTime: 2025-02-15 16:44:16
 * @Description:
-->
<template>
  <div>
    <el-dialog :lock-scroll="true" :draggable="true" v-model="state.dialogVisible.visible" :title="state.dialogVisible.title" width="80%" @close="closeDialog">
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form ref="formRef" :inline="true" :model="state.searchParams" :disabled="state.isView">
          <el-form-item label="维修单位" prop="supplierId" :rules="[{ required: true, message: '请选择维修单位', trigger: 'change' }]">
            <el-select
              :disabled="state.id ? true : false"
              filterable
              clearable
              style="width: 180px"
              v-model="state.searchParams.supplierId"
              @change="handleSupplierChange"
              placeholder="请选择"
            >
              <el-option v-for="item in state.supplierList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="批量维修单号" prop="maintenanceNo">
            <el-input type="textarea" v-model="state.searchParams.maintenanceNo" placeholder="请输入" />
          </el-form-item>
          <!-- 交车时间 -->
          <el-form-item label="交车时间" prop="deliveryTime">
            <el-date-picker
              v-model="state.searchParams.deliveryTime"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="请选择"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search(formRef)">查询</el-button>
            <el-button @click="resetForm(formRef)">重置</el-button>
          </el-form-item>
        </el-form>
        <el-form ref="statisticsFromRef" :model="state.statistics" class="mb-10px" :disabled="state.isView">
          <el-descriptions class="margin-top" :column="4" size="small" :border="true">
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">计税合计费用</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.totalTaxedPayment || 0 }}</span>
                元
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">现金付款</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.cashPayment || 0 }}</span>
                元
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">转账未税付款</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.transferUntaxedPayment || 0 }}</span>
                元
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">税金</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.taxes || 0 }}</span>
                元
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">转账计税付款</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.transferTaxedPayment || 0 }}</span>
                元
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
        <el-button class="mb-10px" type="primary" icon="Plus" @click="addMaintenance" size="small" v-if="state.id && !state.isView">添加维修单</el-button>
        <el-table
          ref="tableRef"
          @select="handleSelectChange"
          @select-all="handleSelectAllChange"
          show-overflow-tooltip
          :border="true"
          :data="state.tableData"
          v-loading="state.loading"
        >
          <el-table-column type="selection" width="65" fixed="left" v-if="!state.isView"></el-table-column>
          <!-- 序号 -->
          <el-table-column label="序号" type="index" width="60" fixed="left"></el-table-column>
          <el-table-column v-for="item in tableConfig.tableItem" :key="item.name" :label="item.label" :prop="item.name"></el-table-column>
        </el-table>
      </el-scrollbar>
      <template #footer v-if="!state.isView">
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="submitForm(statisticsFromRef)">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog title="添加维修单" width="80%" v-model="state.dispatchDialogVisible" @close="onCloseDispatchDialogVisible">
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form :inline="true" :model="state.searchParams" :disabled="state.isView">
          <el-form-item label="维修单位" prop="supplierId" :rules="[{ required: true, message: '请选择维修商', trigger: 'change' }]">
            <el-select :disabled="state.id" filterable clearable style="width: 180px" v-model="state.searchParams.supplierId" placeholder="请选择">
              <el-option v-for="item in state.supplierList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="维修单号" prop="main">
            <el-input type="textarea" v-model="state.searchParams.maintenanceNo" placeholder="请输入" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search1">查询</el-button>
          </el-form-item>
        </el-form>
        <el-table
          ref="tableRef1"
          @selection-change="handleSelectionChange"
          show-overflow-tooltip
          :border="true"
          :data="state.tableData1"
          v-loading="state.loading1"
        >
          <el-table-column type="selection" width="65"></el-table-column>
          <!-- 序号 -->
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column v-for="item in tableConfig.tableItem" :key="item.name" :label="item.label" :prop="item.name"></el-table-column>
        </el-table>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="onCloseDispatchDialogVisible">取 消</el-button>
          <el-button size="small" type="primary" @click="handleOkDispatch">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import {
    getFleetOutFleetSupplierSelectOptionApi,
    getFleetSettlementSupplierBillAllApi,
    postFleetSettlementRepairAddBillApi,
    postFleetSettlementRepairManagementBindChangeApi,
    postFleetSettlementRepairManagementInfoApi,
    postFleetSettlementRepairReconciliationManagementSummaryApi,
  } from '@/api/financialManagement'
  import { SearchParamsVO } from '@/api/financialManagement/types'
  import { FormInstance } from 'element-plus'
  const emit = defineEmits(['confirmStatementSuccess'])
  const formRef = ref()
  const statisticsFromRef = ref()
  const tableRef = ref()
  const tableRef1 = ref()
  const spacer = h(ElDivider, { direction: 'vertical' })
  const state = reactive<any>({
    loading: false,
    loading1: false,
    dispatchDialogVisible: false,
    dialogVisible: {
      visible: false,
      title: '新增驾驶员结算单',
    },
    formData: {},
    searchParams: {
      supplierId: '',
      supplierName: '',
      maintenanceNo: '',
      deliveryTime: [],
      customerId: '',
      vin: '',
      shortVin: '',
      orderIssueDatetime: [],
      predictDeliveryDatetime: [],
    } as SearchParamsVO,
    ids: [] as string[],
    id: '',
    isView: false, //是否是查看
    tableData: [],
    tableData1: [],
    driverList: [] as SelectOptions[],
    statistics: {},
    totalSummary: {},
    selectDispatchList: [],
  })
  const tableConfig = {
    tableItem: [
      {
        name: 'no',
        label: '维修单号',
      },
      {
        name: 'supplierName',
        label: '维修单位',
      },
      {
        name: 'baseName',
        label: '维修基地',
      },
      {
        name: 'vehicleNo',
        label: '维修车辆',
      },
      {
        name: 'entrustUser',
        label: '委托人',
      },
      {
        name: 'contact',
        label: '联系电话',
      },
      {
        name: 'maintenanceStatus',
        label: '维修状态',
      },
      {
        name: 'deliveryTime',
        label: '交车日期',
      },
      {
        name: 'estimatedCost',
        label: '实际维修费用',
      },
      {
        name: 'cashPayment',
        label: '现金付款',
      },
      {
        name: 'transferWithoutTaxPayment',
        label: '转账未税付款',
      },
      {
        name: 'taxes',
        label: '税金',
      },
      {
        name: 'totalTaxedPayment',
        label: '计税合计费用',
      },
      {
        name: 'settlementStatusDesc',
        label: '结算状态',
      },
    ],
  }
  watch(
    () => state.dialogVisible.visible,
    (val) => {
      if (val) {
        getFleetOutFleetBusinessSupplierSelectOption()
      }
    },
  )
  //添加vin
  const addMaintenance = () => {
    state.dispatchDialogVisible = true
  }
  const onCloseDispatchDialogVisible = () => {
    state.tableData1 = []
    state.selectDispatchList = []
    state.searchParams.dispatchNo = ''
    state.searchParams.vin = ''
    state.searchParams.shortVin = ''
    state.dispatchDialogVisible = false
  }
  const handleOkDispatch = () => {
    //如果state.selectDispatchList中的id在state.tableData中不存在，则将state.selectDispatchList合并到state.tableData中,
    state.tableData.push(...state.selectDispatchList.filter((item: any) => !state.tableData.some((tableItem: any) => tableItem.id === item.id)))
    //将state.tableData中的所有项全选
    state.tableData.forEach((item: any) => {
      setTimeout(() => {
        tableRef.value.toggleRowSelection(item, true)
      })
    })
    handleSelectChange(state.tableData)
    onCloseDispatchDialogVisible()
  }
  //获取结算单详情
  const postFleetSettlementRepairManagementCompute = async (row: { id: string; supplierId: string; supplierName: string; no: string }) => {
    state.loading = true
    const { data } = await postFleetSettlementRepairManagementInfoApi({ reconciliationId: row.id, no: row.no })
    state.id = row.id
    state.searchParams.supplierId = row.supplierId
    state.searchParams.supplierName = row.supplierName
    state.statistics = data.statistics
    state.tableData = data.rows
    state.ids = data.rows.map((item: any) => item.id)
    data.rows.forEach((item: any) => {
      setTimeout(() => {
        tableRef.value.toggleRowSelection(item, true)
      })
    })
    state.loading = false
  }
  //获取维修供应商下拉
  const getFleetOutFleetBusinessSupplierSelectOption = async () => {
    const params = {
      enable: true,
    }
    const { data } = await getFleetOutFleetSupplierSelectOptionApi(params)
    state.supplierList = data
  }
  const closeDialog = () => {
    //清空表单
    state.ids = []
    state.totalSummary = {}
    state.statistics = {}
    state.tableData = []
    tableRef.value.clearSelection()
    formRef.value ? formRef.value.resetFields() : ''
    statisticsFromRef.value ? statisticsFromRef.value.resetFields() : ''
    state.dialogVisible.visible = false
  }
  const search = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid) => {
      if (valid) {
        const params = {
          supplierId: state.searchParams.supplierId,
          maintenanceNo: state.searchParams.maintenanceNo,
          startDeliveryTime: state.searchParams.deliveryTime ? state.searchParams.deliveryTime[0] : undefined,
          endDeliveryTime: state.searchParams.deliveryTime ? state.searchParams.deliveryTime[1] : undefined,
        }
        try {
          state.loading = true
          const { data } = await getFleetSettlementSupplierBillAllApi(params)
          const { rows, statistics, totalSummary } = data
          state.tableData = rows
          state.ids = rows.map((item: any) => item.id)
          state.statistics = statistics
          state.totalSummary = totalSummary
          state.tableData.forEach((item: any) => {
            setTimeout(() => {
              tableRef.value.toggleRowSelection(item, true)
            })
          })
          state.loading = false
        } catch (e) {
          console.log(e)
          state.loading = false
        }
      } else {
        console.log('error submit!')
      }
    })
  }
  const handleSupplierChange = (val: any) => {
    const customer = state.supplierList.find((item: any) => item.value === val)
    state.searchParams.customerName = customer.label
    // state.searchParams.customerName = val
  }
  const search1 = async () => {
    const params = {
      supplierId: state.searchParams.supplierId,
      maintenanceNo: state.searchParams.maintenanceNo,
    }
    const { data } = await getFleetSettlementSupplierBillAllApi(params)
    const { rows, statistics, totalSummary } = data
    state.tableData1 = rows
    // state.ids = rows.map((item: any) => item.id);
    // state.statistics = statistics;
    // state.totalSummary = totalSummary;
    state.tableData1.forEach((item: any) => {
      setTimeout(() => {
        tableRef1.value.toggleRowSelection(item, true)
      })
    })
  }
  const handleSelectChange = async (val: any) => {
    const ids = val.map((item: any) => item.id)
    const { data } = await postFleetSettlementRepairReconciliationManagementSummaryApi({ computeIds: ids })
    state.ids = ids
    state.statistics = {
      // vinCount: data.vinCount,
      // totalUntaxedIncome: data.totalUntaxedIncome,
      totalTaxedPayment: data.totalTaxedPayment,
      cashPayment: data.cashPayment,
      taxes: data.taxes,
      transferUntaxedPayment: data.transferUntaxedPayment,
      transferTaxedPayment: data.transferTaxedPayment,
    }
  }
  const handleSelectionChange = (val: any) => {
    state.selectDispatchList = val
  }
  const handleSelectAllChange = async (val: any) => {
    await handleSelectChange(val)
  }
  const submitForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate(async (valid) => {
      if (valid) {
        try {
          if (state.id) {
            const params = {
              computeIds: state.ids,
              id: state.id,
            }
            await postFleetSettlementRepairManagementBindChangeApi(params)
            state.id = ''
            ElMessage.success('修改成功')
          } else {
            const params = {
              supplierId: state.searchParams.supplierId,
              computeIds: state.ids,
            }
            await postFleetSettlementRepairAddBillApi(params)
            ElMessage.success('生成成功')
          }
          closeDialog()
          emit('confirmStatementSuccess')
        } catch (e) {
          console.log(e)
        }
      } else {
        console.log('error submit!')
      }
    })
  }
  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
  }
  defineExpose({
    state,
    postFleetSettlementRepairManagementCompute,
  })
</script>
<style scoped lang="scss"></style>
