<!--
 * @Author: llm
 * @Date: 2024-12-27 19:37:32
 * @LastEditors: llm
 * @LastEditTime: 2025-07-18 09:58:11
 * @Description:  司机补贴-补贴对账
-->
<template>
  <div>
    <div>
      <el-dialog
        :lock-scroll="true"
        :draggable="true"
        v-model="state.dialogVisible.visible"
        :title="state.dialogVisible.title"
        width="90%"
        :close-on-click-modal="false"
        @close="closeDialog"
      >
        <el-scrollbar max-height="60vh" class="formClass">
          <el-form ref="formRef" :inline="true" :model="state.searchParams" :disabled="state.isView">
            <el-form-item :rules="[{ required: true, message: '请选择驾驶员', trigger: 'change' }]" label="驾驶员" prop="driverId">
              <el-select
                v-if="!state.isView"
                :disabled="state.id ? true : false"
                filterable
                clearable
                style="width: 180px"
                v-model="state.searchParams.driverId"
                placeholder="请选择"
                @change="toggID"
              >
                <el-option v-for="item in state.driverList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <el-input v-model="state.searchParams.driverName" placeholder="请输入" v-else></el-input>
            </el-form-item>
            <template v-if="!state.id">
              <el-form-item label="批量调度单" prop="dispatchNo">
                <el-input v-model="state.searchParams.dispatchNo" placeholder="请输入" type="textarea" />
              </el-form-item>
              <!-- 调度时间 -->
              <el-form-item label="调度时间" prop="dispachTime">
                <el-date-picker
                  v-model="state.searchParams.dispachTime"
                  type="datetimerange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择"
                  :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                  :show-footer="false"
                />
              </el-form-item>
              <!-- 发车时间 -->
              <el-form-item label="发车时间" prop="departureDate">
                <el-date-picker
                  v-model="state.searchParams.departureDate"
                  type="datetimerange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择"
                  :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                  :show-footer="false"
                />
              </el-form-item>
              <!-- 送达时间 -->
              <el-form-item label="送达时间" prop="actualDropTime">
                <el-date-picker
                  v-model="state.searchParams.actualDropTime"
                  type="datetimerange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择"
                  :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                  :show-footer="false"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="search(formRef)">查询</el-button>
              </el-form-item>
            </template>
          </el-form>
          <!-- 油数据 -->
          <div style="display: flex">
            <div style="width: 55%">
              <el-form ref="statisticsFromRef" :model="state.statistics" class="mb-10px" :disabled="state.isView">
                <el-descriptions class="margin-top" :column="2" size="small" :border="true" label-width="150">
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item"><span class="color-red">*</span>结算月份</div>
                    </template>
                    <el-form-item :rules="[{ required: true, message: '请选择', trigger: 'change' }]" class="m-0!" label="" prop="month">
                      <el-date-picker
                        :disabled="state.id ? true : false"
                        v-model="state.statistics.month"
                        type="month"
                        format="YYYY-MM"
                        value-format="YYYY-MM"
                        placeholder="请选择结算月份"
                        @change="changeMonth"
                      />
                    </el-form-item>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">应付金额</div>
                    </template>
                    <div class="font-bold">
                      <span class="color-red">{{ state.statistics.shouldPayAmount || 0 }}</span>
                      元
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">补贴合计金额</div>
                    </template>
                    <div class="font-bold">
                      <span>{{ state.statistics.totalSubsidy || 0 }}</span>
                      元
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">实报金额</div>
                    </template>
                    <div class="font-bold">
                      <span>{{ state.statistics.actualExpenseAmount || 0 }}</span>
                      元
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">借支金额</div>
                    </template>
                    <div class="font-bold">
                      <span class="color-red" style="text-decoration: underline; cursor: pointer" @click="openDialogAmount">{{
                        state.statistics.borrowingAmount || 0
                      }}</span>
                      元
                    </div>
                    <ModelAmountBorrowed v-model:data="loanCashItemsChild" v-model:toggId="hostID" v-model:visibled="dialogVisibledsAmount" />
                  </el-descriptions-item>
                  <!-- 后面开发 -->
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">报销明细</div>
                    </template>
                    <div class="itembox">
                      <div class="itempirce" v-for="item in state.statistics.expenseItems" :key="item.feeType" style="margin-right: 10px">
                        {{ item.feeType }}:
                        <div class="pirce" @click="openDialog(item, item.feeType)">{{ item.amount || 0 }}</div>
                        元;
                      </div>
                    </div>
                  </el-descriptions-item>

                  <el-descriptions-item :span="24">
                    <template #label>
                      <div class="font-bold">挂账信息</div>
                    </template>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">历史挂账金额</div>
                    </template>
                    <div class="font-bold">
                      <span style="text-decoration: underline; cursor: pointer" @click="openDialogAmountde">{{ state.statistics.chargeAmount || 0 }}</span>
                      元
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">本次冲账金额</div>
                    </template>
                    <div class="font-bold">
                      <el-input-number v-model="state.statistics.balanceAmount" :precision="2" :style="{ width: '180px' }" @change="editall">
                        <template #suffix>
                          <span>元</span>
                        </template>
                      </el-input-number>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">保证金缴费</div>
                    </template>
                    <div class="font-bold flex items-center">
                      <el-input-number
                        :style="{ width: '180px' }"
                        v-model="state.statistics.earnestAmount"
                        :disabled="!state.earnestAmountCanEdit"
                        :precision="2"
                        @change="editalls"
                      >
                        <template #suffix>
                          <span>元</span>
                        </template>
                      </el-input-number>
                      <el-tooltip :raw-content="true" effect="light" placement="top">
                        <el-icon color="#f3d19e" style="margin-left: 10px">
                          <QuestionFilled />
                        </el-icon>
                        <template #content>
                          <div>限定保证金：{{ state.statistics.earnestSetAmount }}元</div>
                          <div>已缴金额：{{ state.statistics.earnestPaidAmount }}元</div>
                        </template>
                      </el-tooltip>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">高速费</div>
                    </template>

                    <div class="flexs">
                      <div class="font-bold">
                        <span class="color-red" style="text-decoration: underline; cursor: pointer; color: red" @click="openDialogEtcFee">{{
                          state.statistics.etcFeeInfo?.etcFee || 0
                        }}</span>
                        元
                      </div>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">交车费</div>
                    </template>

                    <div class="flexs">
                      <div class="font-bold">
                        <span class="color-red" style="text-decoration: underline; cursor: pointer; color: red" @click="openDialogPay">{{
                          state.statistics?.dropUpFee || 0
                        }}</span>
                        元
                      </div>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">在途费用</div>
                    </template>

                    <div class="flexs">
                      <div class="font-bold">
                        <span class="color-red" style="text-decoration: underline; cursor: pointer; color: red" @click="openDialogWay">{{
                          state.statistics?.intransitFee || 0
                        }}</span>
                        元
                      </div>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">在途维修</div>
                    </template>

                    <div class="flexs">
                      <div class="font-bold">
                        <span class="color-red" style="text-decoration: underline; cursor: pointer; color: red" @click="openDialogRepair">{{
                          state.statistics?.repairFee || 0
                        }}</span>
                        元
                      </div>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">其他收入</div>
                    </template>

                    <div class="flexs">
                      <div class="font-bold">
                        <span class="color-red" style="text-decoration: underline; cursor: pointer; color: red" @click="openDialogOther">{{
                          state.statistics?.otherIncome || 0
                        }}</span>
                        元
                      </div>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item :span="24">
                    <template #label>
                      <div class="cell-item">备用金支付</div>
                    </template>
                    <div class="font-bold flex items-center">
                      <el-input-number v-model="state.statistics.imprestAmount" :precision="2" :style="{ width: '180px' }" @change="editallse">
                        <template #suffix>
                          <span>元</span>
                        </template>
                      </el-input-number>
                    </div>
                  </el-descriptions-item>

                  <el-descriptions-item :span="24">
                    <template #label>
                      <div class="cell-item">其他金额</div>
                    </template>
                    <div class="font-bold flex items-center">
                      <div class="font-bold flex">
                        <el-input-number v-model="state.statistics.otherAmount" :precision="2" :style="{ width: '180px' }" @change="editallsed">
                          <template #suffix>
                            <span>元</span>
                          </template>
                        </el-input-number>
                      </div>
                      <div class="flex items-center ml-10px">
                        <el-icon color="#f3d19e">
                          <QuestionFilled />
                        </el-icon>
                        <span class="color-red text-12px">正值为公司扣驾驶员金额，负值为公司奖励驾驶员</span>
                      </div>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item :span="1">
                    <template #label>
                      <div class="cell-item">劳务结算</div>
                    </template>
                    <span class="color-red">{{ isNaN(Labour as any) ? 0.0 : Labour }}</span
                    >元
                  </el-descriptions-item>
                  <el-descriptions-item :span="1">
                    <template #label>
                      <div class="cell-item">现金结算</div>
                    </template>
                    <span class="color-red">{{ cash || 0.0 }}</span
                    >元
                  </el-descriptions-item>
                  <el-descriptions-item :span="2">
                    <template #label>
                      <div class="cell-item">油费结算</div>
                    </template>
                    <span class="color-red">{{ Fuel || 0.0 }}</span
                    >元
                  </el-descriptions-item>

                  <el-descriptions-item :span="24">
                    <template #label>
                      <div class="cell-item">实际结算金额</div>
                    </template>
                    <div class="font-bold">
                      <!-- state.statistics.actualPayAmount 总的 = 应付金额 - 借支金额 - 历史挂账金额 - 保证金缴费 + 备用金支付 - 其他金额 -->
                      <span class="color-red">{{
                        !flag && state.statistics.actualPayAmount !== null ? state.statistics.actualPayAmount : Actualsettlement
                      }}</span>
                      元
                    </div>
                  </el-descriptions-item>
                  <!-- :max="totalActualAmount" -->
                  <el-descriptions-item :span="24">
                    <template #label>
                      <div class="cell-item">结算方式拆分</div>
                    </template>
                    <div class="font-bold font-paly">
                      <div class="items-play">
                        <span style="margin-right: 10px">现金支付</span>
                        <el-input-number v-model="cashCount" :min="0" :precision="2" :style="{ width: '180px' }" @change="cashCancelAmount">
                          <template #suffix>
                            <span>元</span>
                          </template>
                        </el-input-number>
                      </div>
                      <div class="items-play">
                        <span style="margin-right: 10px">油支付</span>
                        <el-input-number v-model="oilCount" :min="0" :precision="2" :style="{ width: '180px' }" @change="oilCancelAmount">
                          <template #suffix>元</template>
                        </el-input-number>
                      </div>
                    </div>
                  </el-descriptions-item>

                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">备注</div>
                    </template>
                    <div class="font-bold flex items-center">
                      <el-input type="text" placeholder="请输入" v-model="state.statistics.remark"></el-input>
                    </div>
                  </el-descriptions-item>
                </el-descriptions>
              </el-form>
            </div>
            <FuelCostSettlementComponent
              ref="fuelCostSettlement"
              :driverId="state.searchParams.driverId"
              :month="state.statistics.month"
              @changeNum="changeNum"
              @changeTol="changeTol"
              @changeFuel="changeFuel"
            />
          </div>
          <div class="flex items-center mb-10px">
            <el-button v-if="state.id && !state.isView" class="mr-10px" icon="Plus" size="small" type="primary" @click="addDispatchNo">添加调度单 </el-button>
            <CancelTableColumnCheckedDialogComponent
              v-if="!state.isView"
              ref="cancelTableColumnCheckedDialogRef"
              btnText="批量释放调度单"
              :targetField="{ name: 'batchInput', label: '批量调度单', message: '请输入批量调度单', max: 100 }"
              @submitCancelTableColumnCheckedList="submitCancelTableColumnCheckedList"
            />
          </div>

          <el-table
            ref="tableRef"
            @select="handleSelectChange"
            @select-all="handleSelectAllChange"
            show-overflow-tooltip
            show-summary
            :border="true"
            :data="state.tableData"
            :summary-method="getSummariesTable"
            v-loading="state.loading"
          >
            <el-table-column type="selection" width="65" fixed="left" v-if="!state.isView"></el-table-column>
            <!-- 序号 -->
            <!-- <el-table-column align="center" label="序号" type="index" width="60" fixed="left"></el-table-column> -->
            <el-table-column
              :render-header="renderHeader"
              v-for="item in tableConfig.tableItem"
              :key="item.name"
              :label="item.label"
              :prop="item.name"
              :width="item.width"
            >
              <template #default="{ row }">
                <!-- <el-button key="primary" type="primary" text v-if="item.name == 'dispatchNo'" @click="linkTo(row)"> {{ row[item.name] }} </el-button> -->
                <div v-if="item.name == 'dispatchNo'" class="dispatch-no-link" style="color: #3f97fd" @click="linkTo(row)">{{ row[item.name] }}</div>
                <div
                  v-if="item.name == 'showDispatchLine'"
                  :style="{ width: '100%', display: 'flex', 'justify-content': item.align }"
                  v-html="row[item.name]"
                ></div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="{ row }">
                <el-button v-if="!state.isView" key="primary" text type="primary" @click="editCostDate(row)"> 修改费用 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-scrollbar>
        <template #footer v-if="!state.isView">
          <span class="dialog-footer">
            <!-- <el-button type="warning" style="width: 100px">重新计算</el-button> -->
            <el-button @click="closeDialog">取消</el-button>
            <el-button type="primary" @click="submitForm(statisticsFromRef)">确定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>

    <el-dialog v-model="state.dispatchDialogVisible" draggable title="添加调度单" width="80%" @close="onCloseDispatchDialogVisible">
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form :inline="true" :model="state.searchParams" :disabled="state.isView">
          <el-form-item :rules="[{ required: true, message: '请选择驾驶员', trigger: 'change' }]" label="驾驶员" prop="driverId">
            <el-select v-model="state.searchParams.driverId" :disabled="state.id" clearable filterable placeholder="请选择" style="width: 180px">
              <el-option v-for="item in state.driverList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="批量调度单" prop="dispatchNo">
            <el-input v-model="state.searchParams.dispatchNo" placeholder="请输入" type="textarea" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search1">查询</el-button>
          </el-form-item>
        </el-form>
        <el-table
          ref="tableRef1"
          @selection-change="handleSelectionChange"
          show-overflow-tooltip
          show-summary
          :summary-method="getSummaries"
          :border="true"
          :data="state.tableData1"
          v-loading="state.loading1"
        >
          <el-table-column type="selection" width="65"></el-table-column>
          <!-- 序号 -->
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column :render-header="renderHeader" v-for="item in tableConfig.tableItem" :key="item.name" :label="item.label" :prop="item.name">
            <template #default="{ row }">
              <div
                v-if="item.name == 'showDispatchLine'"
                style="width: 100%; display: flex; align-items: center; justify-content: left"
                v-html="row[item.name]"
              ></div>
            </template>
          </el-table-column>
        </el-table>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="onCloseDispatchDialogVisible">取 消</el-button>
          <el-button size="small" type="primary" @click="handleOkDispatch">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 修改费用 -->
    <el-dialog v-model="dialogVisibledis" title="修改费用" width="500" :before-close="handleClose">
      <div style="display: flex; align-items: center">
        <div>
          <span>调整补贴</span>
          <el-input-number v-model="editCost.adjustSubsidies" :precision="2" style="margin-left: 20px" />
        </div>
        <div style="margin-left: 20px">
          扣款（元）
          <el-input-number v-model="editCost.deduction" :precision="2" style="margin-left: 20px" />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="okcost"> 确认</el-button>
          <el-button @click="dialogVisibledis = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 油费结算 FuelCostSettlementComponent-->

    <!-- 不存在的调度单提示 -->
    <el-dialog v-model="nonExistentDialogVisible" title="以下调度单不存在，已被过滤" width="500">
      <div class="nonexistent-dispatch-list">
        <ul>
          <li v-for="(item, index) in nonExistentDialogItems" :key="index">{{ item }}</li>
        </ul>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleConfirmNonExistent">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 报销明细传值 -->
    <ModelTable
      ref="modelAmountRef"
      :visible="dialogVisibleds"
      :expenseItemsChild="parentMessage"
      :driverIdData="driverIdChild"
      :month="state.statistics.month"
    />
  </div>
</template>
<script setup lang="ts">
  import {
    expenseForModificationApi,
    getFleetOrderSettlementDetailViewDriverDetailPageApi,
    getFleetOrderSettlementGetSettlementInfoApi,
    getFleetOutFleetDriverSelectOptionApi,
    postFleetOrderSettlementDetailViewDriverDetailSummaryApi,
    postFleetOrderSettlementGenerateSettlementApi,
    postFleetOrderSettlementUpdateSettlementApi,
  } from '@/api/financialManagement'
  import { renderHeader } from '@/utils/index'
  import { FormInstance } from 'element-plus'
  import ModelTable from '@/components/OtherFormDialogComponent/ModelTable/index.vue'
  import ModelAmountBorrowed from '@/components/OtherFormDialogComponent/ModelAmountBorrowed/index.vue'
  import FuelCostSettlementComponent from '@/components/OtherFormDialogComponent/FuelCostSettlementComponent/index.vue'
  import bus from '@/utils/bus'
  const emit = defineEmits(['confirmSettlementSuccess', 'edit-dispatch'])

  const formRef = ref()
  const statisticsFromRef = ref()
  const tableRef = ref()
  const tableRef1 = ref()
  const cancelTableColumnCheckedDialogRef = ref()
  let flag = ref(false)
  const state = reactive<any>({
    loading: false,
    loading1: false,
    dispatchDialogVisible: false,
    dialogVisible: {
      visible: false,
      title: '新增驾驶员结算单',
    },
    formData: {},
    searchParams: {
      driverName: '',
      driverId: '',
      dispatchNo: '',
      dispachTime: '',
      departureDate: '',
      actualDropTime: '',
    },
    ids: [] as string[],
    id: '',
    isView: false, //是否是查看
    tableData: [],
    tableData1: [],
    driverList: [] as SelectOptions[],
    statistics: {},
    totalSummary: {},
    selectDispatchList: [],
    loanCashItems: [] as any,
    earnestAmountCanEdit: false, //是否可以编辑保证金
  })
  // 添加报销明细数组
  const detailedArray = ref<any>([])
  // 修改费用弹框
  const editCost = ref<any>({
    adjustSubsidies: 0,
    deduction: 0,
    dispatchId: 0,
  })
  const dialogVisibledis = ref<any>(false)
  const handleClose = (done: () => void) => {
    done()
  }
  // 修改费用详情
  const editCostDate = (item: any) => {
    editCost.value.adjustSubsidies = item.adjustSubsidy
    editCost.value.deduction = item.deduction
    editCost.value.dispatchId = item.id
    dialogVisibledis.value = true
  }
  const okcost = async () => {
    let params = {
      adjustAmount: editCost.value.adjustSubsidies,
      deduction: editCost.value.deduction,
      dispatchId: editCost.value.dispatchId,
    }
    await expenseForModificationApi(params)
    dialogVisibledis.value = false
    if (state.id) {
      getFleetOrderSettlementGetSettlementInfo(state.id)
    } else {
      await search(formRef.value)
    }
  }
  const Fuel = ref('0')

  const changeFuel = (num: number) => {
    Fuel.value = num.toFixed(2)
  }

  // kkkkkkk
  const Labour = computed(() => {
    // if(allState)

    if (typeof state.statistics.actualExpenseAmount === 'undefined') {
      state.statistics.actualExpenseAmount = 0
    }
    if (typeof state.statistics.borrowingAmount === 'undefined') {
      state.statistics.borrowingAmount = 0
    }
    if (typeof state.statistics.totalSubsidy === 'undefined') {
      state.statistics.totalSubsidy = 0
    }
    if (typeof state.statistics.balanceAmount === 'undefined') {
      state.statistics.balanceAmount = 0
    }
    if (typeof state.statistics.earnestAmount === 'undefined') {
      state.statistics.earnestAmount = 0
    }
    if (typeof state.statistics.imprestAmount === 'undefined') {
      state.statistics.imprestAmount = 0
    }
    if (typeof state.statistics.otherAmount === 'undefined') {
      state.statistics.otherAmount = 0
    }
    if (typeof state.statistics.otherIncome === 'undefined') {
      state.statistics.otherIncome = 0
    }

    let Num = 0
    if (state.statistics.actualExpenseAmount - state.statistics.borrowingAmount >= 0) {
      Num = Number(
        (
          state.statistics.totalSubsidy -
          state.statistics.balanceAmount -
          state.statistics.earnestAmount +
          state.statistics.imprestAmount -
          state.statistics.otherAmount
        ).toFixed(2),
      )
    } else {
      Num = Number(
        (
          state.statistics.totalSubsidy -
          state.statistics.balanceAmount -
          state.statistics.earnestAmount +
          state.statistics.imprestAmount -
          state.statistics.otherAmount +
          (state.statistics.actualExpenseAmount - state.statistics.borrowingAmount)
        ).toFixed(2),
      )
    }
    if (isNaN(Num)) {
      Num = 0
    }
    return Num
  })

  const cash = computed(() => {
    // if(allState)
    if (state.statistics.actualExpenseAmount - state.statistics.borrowingAmount >= 0) {
      console.log(state.statistics.otherIncome, 'state.statistics.otherIncome')

      return (state.statistics.actualExpenseAmount + state.statistics.otherIncome - state.statistics.borrowingAmount).toFixed(2)
    } else {
      return (0).toFixed(2)
    }
  })

  const Actualsettlement = computed(() => {
    return Number((Number(Labour.value) + Number(cash.value) + Number(Fuel.value)).toFixed(2))
  })

  const linkTo = async (row: any) => {
    // const textToCopy = row.dispatchNo
    // // 复制单号
    // navigator.clipboard.writeText(textToCopy)
    // ElMessage.success('调度单号已复制')
    let isDispatch = true
    let data = { ...row, isDispatch }
    await nextTick()
    emit('edit-dispatch', data, '', '') // 按参数顺序传递
  }
  // 定义数据 chargeItem
  const chargeItem = ref<any>()

  // 定义两个变量 现金支付 油支付
  const playState = reactive<any>({
    cashPayment: 0,
    oliPayment: 0,
  })

  const tableConfig = {
    tableItem: [
      {
        name: 'dispatchNo',
        label: '调度单号',
        width: 180,
        align: 'center',
      },
      {
        name: 'loadCount',
        label: '装载台数',
        width: 100,
        align: 'center',
      },
      {
        name: 'startFenceName',
        label: '起点',
        width: 100,
        align: 'center',
      },
      {
        name: 'endFenceName',
        label: '终点',
        width: 100,
        align: 'center',
      },
      {
        name: 'showDispatchLine',
        label: '运输状态',
        width: 180,
        align: 'left',
      },
      {
        name: 'dispatchTime',
        label: '调度时间',
        width: 180,
        align: 'left',
      },
      {
        name: 'departureDate',
        label: '发车时间',
        width: 140,
        align: 'center',
      },
      {
        name: 'actualDropTime',
        label: '送达时间',
        width: 140,
        align: 'center',
      },
      // {
      //   name: 'mileageFull',
      //   label: '满载里程',
      // },
      // {
      //   name: 'mileageEmpty',
      //   label: '空载里程',
      // },
      {
        name: 'settleMileageFull',
        label: '结算满载里程',
        width: 100,
        align: 'center',
      },
      {
        name: 'settleMileageEmpty',
        label: '结算空载里程',
        width: 100,
        align: 'center',
      },
      {
        name: 'settleMileage',
        label: '结算里程合计',
        width: 100,
        align: 'center',
      },
      {
        name: 'subsidy',
        label: '调度补贴（元）',
        width: 100,
        align: 'center',
      },
      {
        name: 'deduction',
        label: '扣款（元）',
        width: 100,
        align: 'center',
      },
      {
        name: 'adjustSubsidy',
        label: '调整补贴（元）',
        width: 100,
        align: 'center',
      },
      {
        name: 'totalSubsidy',
        label: '补贴合计（元）',
        width: 100,
        align: 'center',
      },
      {
        name: 'otherIncome',
        label: '其他收入（元）',
        width: 100,
        align: 'center',
      },
      {
        name: 'borrowingAmount',
        label: '借支（元）',
        width: 100,
        align: 'center',
      },
      {
        name: 'oilFee',
        label: '油费（元）',
        width: 100,
        align: 'center',
      },
    ],
  }
  watch(
    () => state.dialogVisible.visible,
    async (val) => {
      if (val) {
        await getFleetOutFleetDriverSelectOption()
        // flag.value = true
      }
    },
  )

  watch(
    () => state.dialogVisible.title,
    (val) => {
      if (val === '新增驾驶员结算单') {
        flag.value = true
      } else {
        flag.value = false
      }

      console.log(flag.value, 'flag.value')
    },
    { immediate: true },
  )
  // 报销明细+借支金额+更改bug （司机结算，切换驾驶员点击查询的时候，上方的应付，借支等应该跟着切换变化）
  const toggID = () => {
    state.ids = []
    state.tableData = []
    state.statistics = {
      month: '',
      shouldPayAmount: 0,
      totalSubsidy: 0,
      borrowingAmount: 0,
      actualExpenseAmount: 0,
      otherIncome: 0,
      chargeAmount: 0,
      balanceAmount: 0,
      earnestAmount: 0,
      imprestAmount: 0,
      otherAmount: 0,
      actualPayAmount: 0,
      expenseItems: [],
      loanCashItems: [],
      remark: '',
      earnestSetAmount: 0,
      earnestPaidAmount: 0,
    }
    state.searchParams.dispatchNo = ''
    state.searchParams.departureDate = ''
    state.searchParams.dispachTime = ''
    state.searchParams.actualDropTime = ''
    state.searchParams.driverName = ''
    cashCount.value = 0
    oilCount.value = 0
    changeFuel(0)
    clearDetal()
  }

  const clearDetal = () => {
    fuelCostSettlement.value.allState.ids = []
    fuelCostSettlement.value.tableData = []
    fuelCostSettlement.value.allState.oilFeeSubsidy = 0
    fuelCostSettlement.value.allState.shouldPaymentLiters = 0
    fuelCostSettlement.value.allState.shouldPaymentOilFee = 0
    fuelCostSettlement.value.allState.baseLiters = 0
    fuelCostSettlement.value.allState.baseOilFee = 0
    fuelCostSettlement.value.allState.oilLoanLiters = 0
    fuelCostSettlement.value.allState.loanOilFee = 0
    fuelCostSettlement.value.allState.oilFeeBalanceByLiters = 0
    fuelCostSettlement.value.allState.oilFeeBalanceByFee = 0
    fuelCostSettlement.value.allState.oilFeeAmount = 0
    fuelCostSettlement.value.allState.oilFeeBalanceAmount = 0
    fuelCostSettlement.value.allState.oilFeeChargeAmount = 0
    fuelCostSettlement.value.allState.oilFeeHistoryChargeAmount = 0
    fuelCostSettlement.value.allState.totalOilFeeChargeAmount = 0
  }

  // 油费结算数据
  const fuelCostSettlement = ref()
  const passValue = (row: any, ids: any) => {
    console.log(row, 'row')

    fuelCostSettlement.value.allState.ids = ids
    if (row == null) {
      fuelCostSettlement.value.allState.shouldPaymentLiters = 0
      fuelCostSettlement.value.allState.shouldPaymentOilFee = 0
      fuelCostSettlement.value.allState.oilFeeSubsidy = 0
      fuelCostSettlement.value.allState.baseLiters = 0
      fuelCostSettlement.value.allState.baseOilFee = 0
      fuelCostSettlement.value.allState.oilLoanLiters = 0
      fuelCostSettlement.value.allState.loanOilFee = 0
      fuelCostSettlement.value.allState.oilFeeBalanceByLiters = 0
      fuelCostSettlement.value.allState.oilFeeBalanceByFee = 0
      fuelCostSettlement.value.allState.oilFeeAmount = 0
      fuelCostSettlement.value.allState.oilFeeBalanceAmount = 0
      fuelCostSettlement.value.allState.oilFeeChargeAmount = 0
      fuelCostSettlement.value.allState.oilFeeHistoryChargeAmount = 0
      fuelCostSettlement.value.allState.totalOilFeeChargeAmount = 0
    } else {
      fuelCostSettlement.value.allState.shouldPaymentLiters = row.shouldPaymentLiters
      fuelCostSettlement.value.allState.shouldPaymentOilFee = row.shouldPaymentOilFee
      fuelCostSettlement.value.allState.oilFeeSubsidy = row.oilFeeSubsidy
      fuelCostSettlement.value.allState.baseLiters = row.baseLiters
      fuelCostSettlement.value.allState.baseOilFee = row.baseOilFee
      fuelCostSettlement.value.allState.oilLoanLiters = row.oilLoanLiters
      fuelCostSettlement.value.allState.loanOilFee = row.loanOilFee
      fuelCostSettlement.value.allState.oilFeeBalanceByLiters = row.oilFeeBalanceByLiters
      fuelCostSettlement.value.allState.oilFeeBalanceByFee = row.oilFeeBalanceByFee
      fuelCostSettlement.value.allState.oilFeeAmount = row.oilFeeAmount
      fuelCostSettlement.value.allState.oilFeeBalanceAmount = row.oilFeeBalanceAmount
      fuelCostSettlement.value.allState.oilFeeChargeAmount = row.oilFeeChargeAmount
      fuelCostSettlement.value.allState.oilFeeHistoryChargeAmount = row.oilFeeHistoryChargeAmount
      fuelCostSettlement.value.allState.totalOilFeeChargeAmount = row.totalOilFeeChargeAmount
    }
    fuelCostSettlement.value.oldDateFun()
  }

  const changeMonth = async (date: any) => {
    state.statistics.month = date
    state.statistics.etcFeeInfo = {}
    state.statistics.dropUpItems = []
    state.statistics.intransitItems = []
    state.statistics.dropUpFee = 0
    state.statistics.otherIncome = 0
    state.statistics.otherIncomeItems = []
    state.statistics.intransitFee = 0
    await postFleetOrderSettlementDetailViewDriverDetailSummaryApi({ ids: state.ids, month: state.statistics.month })
      .then((res: any) => {
        if (res.code === 200) {
          let data = res.data
          // fuelCostSettlement.value.allState = data.oilFeeInfo
          passValue(data.oilFeeInfo, state.ids)
          oilFeeAmountFater.value = fuelCostSettlement.value.allState.oilFeeAmount
          oilFeeBalanceAmountFater.value = fuelCostSettlement.value.allState.oilFeeBalanceAmount
          detailedArray.value = data.expenses

          state.statistics.expenseItems = data.expenseItems
          state.statistics.loanCashItems = data.loanCashItems
          if (state.ids.length > 0) {
            chargeItem.value = [data.chargeItem]
            // 合计选中的数据
            let newShouldPayAmount = 0
            let newTotalSubsidy = 0
            let newBorrowingAmount = 0
            const keys = Object.keys(data)
            if (keys.includes('shouldPayAmount')) {
              const value = Number(data.shouldPayAmount)
              if (!isNaN(value)) {
                newShouldPayAmount += value
              }
            }
            if (keys.includes('totalSubsidy')) {
              const value = Number(data.totalSubsidy)
              if (!isNaN(value)) {
                newTotalSubsidy += value
              }
            }
            if (keys.includes('borrowingAmount')) {
              const value = Number(data.borrowingAmount)
              if (!isNaN(value)) {
                newBorrowingAmount += value
              }
            }
            state.statistics.shouldPayAmount = newShouldPayAmount.toFixed(2) //应付金额
            state.statistics.totalSubsidy = newTotalSubsidy.toFixed(2) // 补贴合计
            state.statistics.borrowingAmount = newBorrowingAmount.toFixed(2) //借支
          }
          state.earnestAmountCanEdit = data.earnestAmountCanEdit
          state.statistics.actualExpenseAmount = data.actualExpenseAmount
          state.statistics.etcFeeInfo = data.etcFeeInfo
          state.statistics.otherIncome = data.otherIncome
          state.statistics.otherIncomeItems = data.otherIncomeItems
          state.statistics.dropUpFee = data.dropUpFee
          state.statistics.dropUpItems = data.dropUpItems
          state.statistics.intransitFee = data.intransitFee
          state.statistics.intransitItems = data.intransitItems
          state.statistics.repairFee = data.repairFee
          state.statistics.repairItems = data.repairItems
          state.statistics.chargeAmount = data.chargeAmount
          state.statistics.balanceAmount = data.balanceAmount
          state.statistics.earnestAmount = data.earnestAmount * 1
          state.statistics.earnestSetAmount = data.earnestSetAmount
          state.statistics.earnestPaidAmount = data.earnestPaidAmount
          //     nextTick(async () => {
          //   const Actualsettlements = (await ActualsettlementNum()) as number

          //   console.log(cashRatio.value * (Actualsettlements / 100), oilRatio.value, 'cashRatio,oiggglRatio')

          //   cashCount.value = ((Actualsettlements * cashRatio.value) / 100 || 0).toFixed(2)
          //   //油费支付 = 实际结算金额 * 油费比例
          //   oilCount.value = ((Actualsettlements * oilRatio.value) / 100 || 0).toFixed(2)
          //   console.log(cashCount.value, oilCount.value, Actualsettlements, 'cashRatio,oiggglRatio')
          // })
          // state.statistics.imprestAmount = data.imprestAmount
          // state.statistics.otherAmount = data.otherAmount
          // state.statistics.actualPayAmount = data.actualPayAmount
          getCount()
        } else {
          state.statistics.month = ''
        }
      })
      .catch((err: any) => {
        state.statistics.month = ''
      })
  }

  //添加调度单
  const addDispatchNo = () => {
    state.dispatchDialogVisible = true
  }
  const onCloseDispatchDialogVisible = () => {
    state.tableData1 = []
    state.selectDispatchList = []
    state.searchParams.dispatchNo = ''
    state.searchParams.driverName = ''
    state.searchParams.dispachTime = ''
    state.dispatchDialogVisible = false
    cashCount.value = 0
    oilCount.value = 0
  }
  const handleOkDispatch = () => {
    //如果state.selectDispatchList中的id在state.tableData中不存在，则将state.selectDispatchList合并到state.tableData中,
    state.tableData.push(...state.selectDispatchList.filter((item: any) => !state.tableData.some((tableItem: any) => tableItem.id === item.id)))
    //将state.tableData中的所有项全选
    state.tableData.forEach((item: any) => {
      item.isSelected = true
      setTimeout(() => {
        tableRef.value.toggleRowSelection(item, true)
      })
    })
    handleSelectChange(state.tableData)
    onCloseDispatchDialogVisible()
  }
  //获取结算单详情
  const getFleetOrderSettlementGetSettlementInfo = async (id: string) => {
    state.loading = true

    try {
      state.id = id
      let params = {}
      if (state.isView) {
        params = {
          id,
          detail: true,
        }
      } else {
        params = {
          id,
        }
      }
      const { data } = await getFleetOrderSettlementGetSettlementInfoApi(params)
      state.searchParams.driverId = data.settlement.driverId
      state.searchParams.driverName = data.settlement.driverName

      await nextTick()
      detailedArray.value = data.settlement.expenses
      state.statistics = {
        month: data.settlement.month,
        shouldPayAmount: data.settlement.shouldPayAmount,
        totalSubsidy: data.settlement.totalSubsidy,
        actualExpenseAmount: data.settlement.actualExpenseAmount,
        borrowingAmount: data.settlement.borrowingAmount,
        chargeAmount: data.settlement.chargeAmount,
        balanceAmount: data.settlement.balanceAmount,
        earnestAmount: data.settlement.earnestAmount,
        imprestAmount: data.settlement.imprestAmount,
        otherAmount: data.settlement.otherAmount,
        actualPayAmount: data.settlement.actualPayAmount,
        remark: data.settlement.remark,
        expenseItems: data.settlement.expenseItems,
        loanCashItems: data.settlement.loanCashItems,
        oilRatio: data.settlement.oilRatio, // 油支付
        cashRatio: data.settlement.cashRatio, // 现金支付
        expenses: data.settlement.expenses,
        earnestSetAmount: data.settlement.earnestSetAmount,
        earnestPaidAmount: data.settlement.earnestPaidAmount,
        etcFeeInfo: data.settlement.etcFeeInfo,
        otherIncome: data.settlement.otherIncome,
        otherIncomeItems: data.settlement.otherIncomeItems,
        dropUpFee: data.settlement.dropUpFee,
        dropUpItems: data.settlement.dropUpItems,
        intransitFee: data.settlement.intransitFee,
        intransitItems: data.settlement.intransitItems,
        repairFee: data.settlement.repairFee,
        repairItems: data.settlement.repairItems,
      }
      state.earnestAmountCanEdit = data.settlement.earnestAmountCanEdit
      chargeItem.value = data.settlement.chargeItem ? [data.settlement.chargeItem] : null
      // cashCount.value = data.settlement.cashPaymentAmount
      // oilCount.value = data.settlement.oilPaymentAmount

      cashCount.value = data.settlement.cashPaymentAmount
      oilCount.value = data.settlement.oilPaymentAmount
      console.log(data.settlement.cashPaymentAmount, data.settlement.oilPaymentAmount, '213213213')
      // nextTick(async () => {
      //   const Actualsettlements = (await ActualsettlementNum()) as number
      //   cashRatio.value = data.settlement.cashRatio ? data.settlement.cashRatio : 0
      //   oilRatio.value = data.settlement.oilRatio ? data.settlement.oilRatio : 0

      //   cashCount.value = ((Actualsettlements * cashRatio.value) / 100 || 0).toFixed(2)
      //   //油费支付 = 实际结算金额 * 油费比例
      //   oilCount.value = ((Actualsettlements * oilRatio.value) / 100 || 0).toFixed(2)
      //   console.log(cashCount.value, oilCount.value, Actualsettlements, 'cashRatio,oilRatio')
      // })

      state.tableData = data.details
      state.ids = data.details.map((item: any) => item.id)
      fuelCostSettlement.value.allState.settlementNo = data.settlement.no
      fuelCostSettlement.value.allState.isvist = state.isView

      if (data.settlement.oilFeeInfo == null) {
        fuelCostSettlement.value.state.isToggle = 0
        fuelCostSettlement.value.state.oilFeePayType = 1
      } else if (data.settlement.oilFeeInfo.oilFeePayType == null || data.settlement.oilFeeInfo == null) {
        fuelCostSettlement.value.state.isToggle = 0
        fuelCostSettlement.value.state.oilFeePayType = 1
      } else if (data.settlement.oilFeeInfo.oilFeePayType == 1) {
        fuelCostSettlement.value.state.isToggle = 0
        fuelCostSettlement.value.state.oilFeePayType = data.settlement.oilFeeInfo.oilFeePayType
      } else {
        fuelCostSettlement.value.state.isToggle = 1
        fuelCostSettlement.value.state.oilFeePayType = data.settlement.oilFeeInfo.oilFeePayType
      }

      // if (data.settlement.oilFeeInfo.oilFeePayType == null) {
      //   fuelCostSettlement.value.state.isToggle = 0
      //   fuelCostSettlement.value.state.oilFeePayType = 1
      // } else {
      //   fuelCostSettlement.value.state.isToggle = 1
      //   fuelCostSettlement.value.state.oilFeePayType = data.settlement.oilFeeInfo.oilFeePayType
      // }

      passValue(data.settlement.oilFeeInfo, state.ids)
      oilFeeAmountFater.value = fuelCostSettlement.value.allState.oilFeeAmount
      oilFeeBalanceAmountFater.value = fuelCostSettlement.value.allState.oilFeeBalanceAmount
      data.details.forEach((item: any) => {
        item.isSelected = true
        nextTick(() => {
          tableRef.value.toggleRowSelection(item, true)
        })
      })
      state.loading = false
      // getCount()
    } catch (error) {
      state.loading = false
    }
  }
  // 计算合计
  const getSummaries = (param: any) => {
    if (state.tableData.length === 0) {
      return []
    }
    const { columns, data } = param
    const sums: any[] = []
    var arr = [
      'loadCount',
      'mileageFull',
      'mileageEmpty',
      'adjustMileageFull',
      'adjustMileageEmpty',
      'totalMileage',
      'subsidy',
      'adjustSubsidy',
      'totalSubsidy',
      'borrowingAmount',
    ]

    columns.forEach((column: { property: string }, index: number) => {
      if (index === 0) {
        sums[index] = '合计'
        return
      }
      if (arr.includes(column.property)) {
        sums[index] = data.reduce((sum: number, row: any) => {
          const value = Number(row[column.property])
          if (!isNaN(value)) {
            return sum + value
          } else {
            return sum
          }
        }, 0)
        if (column.property === 'loadCount') {
          sums[index] = Math.round(sums[index])
        } else {
          sums[index] = sums[index].toFixed(2)
        }
        sums[index] += ' '
      } else {
        sums[index] = ''
      }
    })
    return sums
  }

  // 计算合计
  const getSummariesTable = (param: any) => {
    if (state.tableData.length === 0) {
      return []
    }
    const { columns, data } = param

    const sums: any[] = []
    var arr = [
      'loadCount',
      'mileageFull',
      'mileageEmpty',
      'adjustMileageFull',
      'adjustMileageEmpty',
      'totalMileage',
      'subsidy',
      'adjustSubsidy',
      'totalSubsidy',
      'borrowingAmount',
      'deduction',
      'settleMileageEmpty',
      'settleMileageFull',
      'settleMileage',
      'otherIncome',
      'oilFee',
    ]
    columns.forEach((column: { property: string }, index: number) => {
      if (index === 0) {
        sums[index] = '合计'
        return
      }
      if (arr.includes(column.property)) {
        // 合计选中的数据
        let total = 0
        state.tableData.forEach((item: any) => {
          if (state.ids.includes(item.id)) {
            const value = Number(item[column.property])
            if (!isNaN(value)) {
              total += value
            }
          }
        })
        if (column.property === 'loadCount') {
          sums[index] = Math.round(total)
        } else {
          sums[index] = total.toFixed(2)
        }
        sums[index] += ' '
      } else {
        sums[index] = ''
      }
    })
    return sums
  }

  //获取驾驶员下拉
  const getFleetOutFleetDriverSelectOption = async () => {
    const { data } = await getFleetOutFleetDriverSelectOptionApi({})
    state.driverList = data
  }
  const closeDialog = () => {
    flag.value = false
    state.statistics.etcFeeInfo = {}
    state.statistics.dropUpItems = []
    state.statistics.intransitItems = []
    state.statistics.dropUpFee = 0
    state.statistics.intransitFee = 0
    // //清空表单
    state.ids = []
    state.totalSummary = {}
    state.tableData = []
    state.statistics = {}
    tableRef.value.clearSelection()
    formRef.value.resetFields()
    statisticsFromRef.value.resetFields()
    state.dialogVisible.visible = false
    state.id = ''
    state.isView = false
    fuelCostSettlement.value.allState.ids = ''
    fuelCostSettlement.value.allState.settlementNo = ''
    fuelCostSettlement.value.allState.isvist = false
    fuelCostSettlement.value.resetToggleState()
    cashCount.value = 0
    //油费支付 = 实际结算金额 * 油费比例
    oilCount.value = 0
    clearDetal()
    changeFuel(0)
  }

  const cashRatio = ref(0)
  const oilRatio = ref(0)
  // 搜索
  const search = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid) => {
      if (valid) {
        const params = {
          driverId: state.searchParams.driverId,
          dispatchNo: state.searchParams.dispatchNo,
          startDepartureDate: state.searchParams.departureDate ? state.searchParams.departureDate[0] : '',
          startDispatchTime: state.searchParams.dispachTime ? state.searchParams.dispachTime[0] : '',
          endDispatchTime: state.searchParams.dispachTime ? state.searchParams.dispachTime[1] : '',
          endDepartureDate: state.searchParams.departureDate ? state.searchParams.departureDate[1] : '',
          startActualDropTime: state.searchParams.actualDropTime ? state.searchParams.actualDropTime[0] : '',
          endActualDropTime: state.searchParams.actualDropTime ? state.searchParams.actualDropTime[1] : '',
        }

        try {
          state.loading = true
          const { data } = await getFleetOrderSettlementDetailViewDriverDetailPageApi(params)
          const { rows, statistics, totalSummary } = data
          state.statistics.actualPayAmount = statistics.actualPayAmount
          state.tableData = rows
          state.ids = rows.map((item: any) => item.id)
          nextTick(async () => {
            const Actualsettlements = (await ActualsettlementNum()) as number
            cashRatio.value = rows[0].cashRatio ? rows[0].cashRatio : 0
            oilRatio.value = rows[0].oilRatio ? rows[0].oilRatio : 0

            cashCount.value = ((Actualsettlements * cashRatio.value) / 100 || 0).toFixed(2)
            //油费支付 = 实际结算金额 * 油费比例
            oilCount.value = ((Actualsettlements * oilRatio.value) / 100 || 0).toFixed(2)
            console.log(cashCount.value, oilCount.value, Actualsettlements, 'cashRatio,oilRatio')
          })
          // state.statistics = statistics
          state.statistics.shouldPayAmount = statistics.shouldPayAmount?.toFixed(2)
          state.statistics.totalSubsidy = statistics.totalSubsidy?.toFixed(2)
          state.statistics.borrowingAmount = statistics.borrowingAmount?.toFixed(2)
          state.statistics.chargeAmount = statistics.chargeAmount?.toFixed(2)
          state.statistics.balanceAmount = statistics.balanceAmount?.toFixed(2)
          state.statistics.earnestAmount = statistics.earnestAmount
          state.statistics.earnestPaidAmount = statistics.earnestPaidAmount
          state.statistics.earnestSetAmount = statistics.earnestSetAmount
          state.earnestAmountCanEdit = statistics.earnestAmountCanEdit
          state.statistics.etcFeeInfo = statistics.etcFeeInfo
          state.statistics.otherIncome = statistics.otherIncome
          state.statistics.otherIncomeItems = statistics.otherIncomeItems
          state.statistics.dropUpFee = statistics.dropUpFee
          state.statistics.dropUpItems = statistics.dropUpItems
          state.statistics.intransitFee = statistics.intransitFee
          state.statistics.intransitItems = statistics.intransitItems
          state.statistics.repairFee = statistics.repairFee
          state.statistics.repairItems = statistics.repairItems
          state.totalSummary = totalSummary
          state.tableData.forEach((item: any) => {
            item.isSelected = true
            setTimeout(() => {
              tableRef.value.toggleRowSelection(item, true)
            })
          })
          // passValue(data.oilFeeInfo, state.ids)
          fuelCostSettlement.value.resetToggleState()
          state.loading = false
          if (state.statistics.month) {
            await changeMonth(state.statistics.month)
          }
        } catch (e) {
          state.loading = false
        }
      } else {
      }
    })
  }
  const search1 = async () => {
    const params = {
      driverId: state.searchParams.driverId,
      dispatchNo: state.searchParams.dispatchNo,
    }
    const { data } = await getFleetOrderSettlementDetailViewDriverDetailPageApi(params)
    const { rows, statistics } = data
    state.tableData1 = rows
    // state.ids = rows.map((item: any) => item.id)
    // state.statistics = statistics
    state.tableData1.forEach((item: any) => {
      item.isSelected = true
      setTimeout(() => {
        tableRef1.value.toggleRowSelection(item, true)
      })
    })
  }
  // 表格选中的切换
  const handleSelectChange = async (val: any) => {
    const ids = val.map((item: any) => item.id)
    state.ids = ids
    state.totalSummary = {}
    if (ids.length === 0) {
      state.statistics.shouldPayAmount = 0 //应付金额
      state.statistics.totalSubsidy = 0 // 补贴合计
      state.statistics.borrowingAmount = 0 //借支
      cashCount.value = 0
      oilCount.value = 0
      state.statistics.balanceAmount = 0
      oilFeeBalanceAmountFater.value = 0
      oilFeeAmountFater.value = 0
      state.statistics.etcFeeInfo = {}
      state.statistics.dropUpItems = []
      state.statistics.intransitItems = []
      state.statistics.dropUpFee = 0
      state.statistics.otherIncome = 0
      state.statistics.otherIncomeItems = []
      state.statistics.intransitFee = 0
      clearDetal()
      passValue(null, [])
      return ElMessage.warning('请选择调度单！')
    }
    state.statistics.etcFeeInfo = {}
    state.statistics.dropUpItems = []
    state.statistics.intransitItems = []
    state.statistics.dropUpFee = 0
    state.statistics.otherIncome = 0
    state.statistics.otherIncomeItems = []
    state.statistics.intransitFee = 0
    const { data } = await postFleetOrderSettlementDetailViewDriverDetailSummaryApi({
      ids: ids,
      month: state.statistics.month,
    })
    if (ids.length > 0) {
      chargeItem.value = [data.chargeItem]
      detailedArray.value = data.expenses
      // 合计选中的数据
      let newShouldPayAmount = 0
      let newTotalSubsidy = 0
      let newBorrowingAmount = 0
      const keys = Object.keys(data)
      if (keys.includes('shouldPayAmount')) {
        const value = Number(data.shouldPayAmount)
        if (!isNaN(value)) {
          newShouldPayAmount += value
        }
      }
      if (keys.includes('totalSubsidy')) {
        const value = Number(data.totalSubsidy)
        if (!isNaN(value)) {
          newTotalSubsidy += value
        }
      }
      if (keys.includes('borrowingAmount')) {
        const value = Number(data.borrowingAmount)
        if (!isNaN(value)) {
          newBorrowingAmount += value
        }
      }
      state.statistics.loanCashItems = data.loanCashItems
      state.statistics.expenseItems = data.expenseItems
      state.statistics.etcFeeInfo = data.etcFeeInfo
      state.statistics.otherIncome = data.otherIncome
      state.statistics.otherIncomeItems = data.otherIncomeItems
      state.statistics.dropUpFee = data.dropUpFee
      state.statistics.dropUpItems = data.dropUpItems
      state.statistics.intransitFee = data.intransitFee
      state.statistics.intransitItems = data.intransitItems
      state.statistics.repairFee = data.repairFee
      state.statistics.repairItems = data.repairItems
      state.statistics.shouldPayAmount = newShouldPayAmount.toFixed(2) //应付金额
      state.statistics.totalSubsidy = newTotalSubsidy.toFixed(2) // 补贴合计
      state.statistics.borrowingAmount = newBorrowingAmount.toFixed(2) //借支
      passValue(data.oilFeeInfo, state.ids)
      getCount()
    }
    state.statistics.actualExpenseAmount = data.actualExpenseAmount
    state.statistics.otherIncome = data.otherIncome
    state.statistics.chargeAmount = data.chargeAmount
    state.statistics.balanceAmount = data.balanceAmount
    state.statistics.earnestSetAmount = data.earnestSetAmount
    state.statistics.earnestPaidAmount = data.earnestPaidAmount
    state.statistics.earnestAmount = data.earnestAmount
    // state.statistics.imprestAmount = data.imprestAmount
    // state.statistics.otherAmount = data.otherAmount
    state.statistics.actualPayAmount = data.actualPayAmount
    state.statistics.remark = data.remark
  }
  const handleSelectionChange = (val: any) => {
    state.selectDispatchList = val
  }
  const handleSelectAllChange = async (val: any) => {
    getCount()
    await handleSelectChange(val)
  }
  const submitForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    if (state.ids.length === 0) {
      return ElMessage.error('请至少选择一项')
    }
    formEl.validate(async (valid) => {
      if (valid) {
        const params = {
          actualExpenseAmount: state.statistics.actualExpenseAmount,
          otherIncome: state.statistics.otherIncome,
          actualPayAmount: Number((Number(Labour.value) + Number(cash.value) + Number(Fuel.value)).toFixed(2)),
          balanceAmount: state.statistics.balanceAmount,
          borrowingAmount: state.statistics.borrowingAmount,
          chargeAmount: state.statistics.chargeAmount,
          earnestAmount: state.statistics.earnestAmount,
          imprestAmount: state.statistics.imprestAmount,
          month: state.statistics.month,
          otherAmount: state.statistics.otherAmount,
          shouldPayAmount: state.statistics.shouldPayAmount,
          remark: state.statistics.remark,
          ids: state.ids,
          id: state.id,
          cashPaymentAmount: cashCount.value,
          oilPaymentAmount: oilCount.value,
          oilFeeInfo: fuelCostSettlement.value.allState,
          oilFeePayType: fuelCostSettlement.value.state.oilFeePayType,
          laborSettlementAmount: Number(Number(Labour.value).toFixed(2)),
          cashSettlementAmount: Number(Number(cash.value).toFixed(2)),
          oilSettlementAmount: Number(Number(Fuel.value).toFixed(2)),
        }
        try {
          if (state.id) {
            await postFleetOrderSettlementUpdateSettlementApi(params)
            state.id = ''
          } else {
            await postFleetOrderSettlementGenerateSettlementApi(params)
          }
          ElMessage.success('生成成功')
          closeDialog()
          emit('confirmSettlementSuccess')
        } catch (e) {}
      } else {
      }
    })
  }
  // 借支金额
  const dialogVisibledsAmount = ref(false)
  const loanCashItemsChild = ref<any>(null)
  const hostID = ref<any>(0) // 0 为借支金额 1 为历史挂账 2 为高速费
  const openDialogAmount = async () => {
    hostID.value = 0
    dialogVisibledsAmount.value = true
    loanCashItemsChild.value = state.statistics.loanCashItems
  }
  const openDialogAmountde = async () => {
    hostID.value = 1
    dialogVisibledsAmount.value = true
    loanCashItemsChild.value = chargeItem.value
  }
  const openDialogEtcFee = async () => {
    hostID.value = 2
    dialogVisibledsAmount.value = true
    loanCashItemsChild.value = state.statistics.etcFeeInfo?.items || []
  }

  const openDialogPay = async () => {
    hostID.value = 3
    dialogVisibledsAmount.value = true
    loanCashItemsChild.value = state.statistics?.dropUpItems || []
  }
  const openDialogRepair = async () => {
    hostID.value = 5
    dialogVisibledsAmount.value = true
    loanCashItemsChild.value = state.statistics?.repairItems || []
  }
  const openDialogOther = async () => {
    hostID.value = 6
    dialogVisibledsAmount.value = true
    loanCashItemsChild.value = state.statistics?.otherIncomeItems || []
    console.log(state.statistics, 'state.statistics?.otherIncomeItems')
  }
  const openDialogWay = async () => {
    hostID.value = 4
    dialogVisibledsAmount.value = true
    loanCashItemsChild.value = state.statistics?.intransitItems || []
  }

  // 报销明细
  const dialogVisibleds = ref(false)
  const parentMessage = ref<any>(null)
  const driverIdChild = ref<any>(null)
  const modelAmountRef = ref()
  const openDialog = async (item: any, feeType: any) => {
    let dataArray = detailedArray.value[feeType]
    let type = '' as any
    if (state.dialogVisible.title == '查看驾驶员结算单') {
      type = 1
    } else {
      type = 0
    }
    parentMessage.value = item
    driverIdChild.value = state.searchParams.driverId
    // dialogVisibleds.value = true
    modelAmountRef.value.visible = true
    modelAmountRef.value.title = '报销明细'
    modelAmountRef.value.gridList = [
      { name: '费用单号', property: 'no' },
      { name: '审批状态', property: 'auditStatusDesc' },
      { name: '调度单号', property: 'dispatchNo' },
      { name: '费用类型', property: 'feeType' },
      { name: '车队', property: 'fleetName' },
      { name: '申请人', property: 'reportUserName' },
      { name: '金额', property: 'amount' },
      { name: '报销比例', property: 'expenseRatioInfo' },
      { name: '实报金额', property: 'actualAmount' },
      { name: '凭证', property: 'imageUrls' },
      { name: '创建人', property: 'createUserName' },
      { name: '创建时间', property: 'createTime' },
    ]
    // 编辑有settlementNo  生成结算单为null
    modelAmountRef.value?.getbalfun(item.feeType, state.searchParams.driverId, state.statistics.month, state.tableData[0].settlementNo, type, dataArray)
  }
  const editall = (value: any) => {
    getCount()
  }
  const editalls = (value: any) => {
    getCount()
  }
  const editallse = (value: any) => {
    getCount()
  }
  const editallsed = (value: any) => {
    getCount()
  }
  // 现金支付、油支付
  const cashCount = ref<any>(0)
  const oilCount = ref<any>(0)
  const getCount = () => {
    console.log('1232132323')

    flag.value = true
    // const num = (
    //   (state.statistics.shouldPayAmount ? state.statistics.shouldPayAmount : 0) -
    //   (state.statistics.borrowingAmount ? state.statistics.borrowingAmount : 0) -
    //   (state.statistics.balanceAmount ? state.statistics.balanceAmount : 0) -
    //   (state.statistics.earnestAmount ? state.statistics.earnestAmount : 0) -
    //   (state.statistics.otherAmount ? state.statistics.otherAmount : 0) +
    //   (state.statistics.imprestAmount ? state.statistics.imprestAmount : 0) +
    //   (oilFeeAmountFater.value ? oilFeeAmountFater.value : 0) -
    //   (oilFeeBalanceAmountFater.value ? oilFeeBalanceAmountFater.value : 0)
    // ).toFixed(2) as any
    //现金支付 =  实际结算金额 * 现金比例
    // cashCount.value = state.statistics.cashRatio ? (num * state.statistics.cashRatio) / 100 || 0 : (num * playState.cashPayment) / 100 || 0

    let cashRatios = state.statistics.cashRatio ? state.statistics.cashRatio : cashRatio.value
    let oilRatios = state.statistics.oilRatio ? state.statistics.oilRatio : oilRatio.value

    nextTick(async () => {
      const Actualsettlements = (await ActualsettlementNum()) as any

      cashCount.value = ((Actualsettlements * cashRatios) / 100 || 0).toFixed(2)
      //油费支付 = 实际结算金额 * 油费比例
      oilCount.value = ((Actualsettlements * oilRatios) / 100 || 0).toFixed(2)

      console.log(cashCount.value, oilCount.value, cashRatio.value, 'cashRatios')
    })
  }

  const ActualsettlementNum = () => {
    return new Promise((resolve) => {
      let Actualsettlements = 0
      if (flag.value) {
        Actualsettlements = Number(Actualsettlement.value)
      } else {
        Actualsettlements = state.statistics.actualPayAmount
      }
      resolve(Number(Actualsettlements))
    })
  }
  const oilFeeAmountFater = ref<any>()
  const oilFeeBalanceAmountFater = ref<any>()
  // 获取子组件的数据
  const changeNum = (value: any) => {
    oilFeeAmountFater.value = value
    getCount()
  }
  const changeTol = (value: any) => {
    oilFeeBalanceAmountFater.value = value
    getCount()
  }

  const totalActualAmount = computed(() => {
    oilFeeAmountFater.value = fuelCostSettlement.value?.allState.oilFeeAmount
    oilFeeBalanceAmountFater.value = fuelCostSettlement.value?.allState.oilFeeBalanceAmount
    const shouldPay = Number(state.statistics.shouldPayAmount) || 0
    const borrowing = Number(state.statistics.borrowingAmount) || 0
    const balance = Number(state.statistics.balanceAmount) || 0
    const earnest = Number(state.statistics.earnestAmount) || 0
    const other = Number(state.statistics.otherAmount) || 0
    const imprest = Number(state.statistics.imprestAmount) || 0
    const oilFeeAmounts = Number(oilFeeAmountFater.value) || 0
    const oilFeeBalanceAmounts = Number(oilFeeBalanceAmountFater.value) || 0
    const total = shouldPay - borrowing - balance - earnest - other + imprest + oilFeeAmounts - oilFeeBalanceAmounts
    return parseFloat(total.toFixed(2)) // 确保两位小数并转换为数值
  })

  let isUpdating = false // 防止更新循环
  // 监听现金支付的变化
  // watch(cashCount, (newVal: any) => {
  //   if (isUpdating) return
  //   isUpdating = true
  //   const newCash = parseFloat(newVal)
  //   if (!isNaN(newCash)) {
  //     const remaining = totalActualAmount.value - newCash
  //     oilCount.value = Math.max(0, parseFloat(remaining.toFixed(2))) // 不小于0
  //   }
  //   isUpdating = false
  // })
  // watch(oilCount, (newVal: any) => {
  //   if (isUpdating) return
  //   isUpdating = true
  //   const newCash = parseFloat(newVal)
  //   if (!isNaN(newCash)) {
  //     const remaining = totalActualAmount.value - newCash
  //     cashCount.value = Math.max(0, parseFloat(remaining.toFixed(2))) // 不小于0
  //   }
  //   isUpdating = false
  // })
  const cashCancelAmount = async (val: any) => {
    if (cashCount.value == null) {
      cashCount.value = 1
    }
    let Actualsettlements = Number(Actualsettlement.value)
    oilCount.value = Actualsettlements - cashCount.value
  }
  const oilCancelAmount = async (val: any) => {
    if (oilCount.value == null) {
      oilCount.value = 1
    }
    let Actualsettlements = Number(Actualsettlement.value)
    cashCount.value = Actualsettlements - oilCount.value
  }
  //获取需要释放的调度单
  const submitCancelTableColumnCheckedList = (str: string) => {
    // 将字符串转换为数组
    const list = str.split(',')

    // 检查不存在的调度单
    const nonExistentDispatchNos = list
      .filter((dispatchNo) => !state.tableData.some((item: any) => item.dispatchNo === dispatchNo))
      .filter((item) => item.trim() !== '') // 过滤掉空白项

    // 保存待处理的调度单列表
    pendingDispatchList.value = [...list]

    // 如果有不存在的调度单，显示对话框
    if (nonExistentDispatchNos.length > 0) {
      nonExistentDialogItems.value = nonExistentDispatchNos
      nonExistentDialogVisible.value = true
    } else {
      // 如果没有不存在的调度单，直接处理
      handleConfirmNonExistent()
    }
  }
  // 非存在的调度单对话框控制
  const nonExistentDialogVisible = ref(false)
  const nonExistentDialogItems = ref<string[]>([])
  const pendingDispatchList = ref<string[]>([])

  // 确认按钮处理函数
  const handleConfirmNonExistent = () => {
    nonExistentDialogVisible.value = false

    // 处理存在的调度单
    const list = pendingDispatchList.value
    state.tableData.forEach((item: any) => {
      if (list.includes(item.dispatchNo)) {
        item.isSelected = false
        tableRef.value.toggleRowSelection(item, false)
      }
    })
    //触发handleSelectChange方法，参数是已选中的数据
    const selectedTableData = state.tableData.filter((item: any) => item.isSelected)
    handleSelectChange(selectedTableData)

    // 清空待处理数据
    pendingDispatchList.value = []

    // 关闭CancelTableColumnCheckedDialogComponent
    if (cancelTableColumnCheckedDialogRef.value && cancelTableColumnCheckedDialogRef.value.state && cancelTableColumnCheckedDialogRef.value.state.dialog) {
      cancelTableColumnCheckedDialogRef.value.state.dialog.visible = false
      cancelTableColumnCheckedDialogRef.value.state.formData.batchInput = ''
    }
  }

  defineExpose({
    state,
    getFleetOrderSettlementGetSettlementInfo,
  })
</script>
<style scoped lang="scss">
  .font-paly {
    display: flex;
  }

  .items-play {
    margin-right: 20px;
  }

  .flexs {
    width: 100%;
    // height: 100px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .flexitem {
      width: 33%;
      display: flex;
      justify-content: space-around;
      align-items: center;
      .flexitem-label {
        width: 100px;
        background-color: #f2f2f2;
        color: #3d21f3;
        text-align: center;
        padding: 5px 0;
        font-size: 12px;
        font-weight: 600;
      }
      .flexitem-price {
        width: 70%;
        padding: 5px 0;
        text-align: center;
        font-size: 16px;
        font-weight: 600;
        span {
          color: #f00;
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
  }

  /* 添加深度选择器穿透element样式 */
  :deep(.dispatch-no-link) {
    color: #3f97fd;
    cursor: pointer; /* 始终显示手型光标 */
    transition: all 0.3s ease; /* 添加过渡动画 */

    /* 悬停效果 */
    &:hover {
      text-decoration: underline;
      text-underline-offset: 3px; /* 下划线偏移量 */
      text-decoration-thickness: 1px; /* 下划线粗细 */
      color: #2d7fd3; /* 悬停颜色变化 */
    }

    /* 禁用默认按钮悬停效果 */
    &.nohover:hover {
      background-color: transparent !important;
    }
  }

  .itembox {
    display: grid;
    grid-gap: 10px;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    .itempirce {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 10px;
      .pirce {
        color: red;
        text-decoration: underline;
        font-weight: 600;
        cursor: pointer;
      }
    }
  }
</style>
