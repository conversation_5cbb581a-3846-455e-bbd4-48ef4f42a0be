<!--
 * @Author: llm
 * @Date: 2024-12-27 19:37:32
 * @LastEditors: llm
 * @LastEditTime: 2025-05-26 14:32:47
 * @Description:
-->
<template>
  <div>
    <el-dialog :lock-scroll="true" :draggable="true" v-model="state.dialogVisible.visible" :title="state.dialogVisible.title" width="80%" @close="closeDialog">
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form ref="formRef" :inline="true" :model="state.searchParams" :disabled="state.isView" label-width="100px">
          <el-row :gutter="15">
            <!-- 客户名称 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-form-item
                label-width="80px"
                label="客户名称"
                prop="customerId"
                class="inputDeep"
                :rules="[{ required: true, message: '请选择客户', trigger: 'change' }]"
              >
                <el-select
                  :disabled="state.id ? true : false"
                  filterable
                  clearable
                  style="width: 100%"
                  v-model="state.searchParams.customerId"
                  @change="handleCustomerChange"
                  placeholder="请选择"
                >
                  <el-option v-for="item in state.customerList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 批量客户单号 -->
            <!-- <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-if="!state.id">
              <el-form-item label="客户单号" prop="vin">
                <el-input type="textarea" :rows="1" style="width: 100%" v-model="state.searchParams.customerOrderNo" clearable placeholder="请输入" />
              </el-form-item>
            </el-col> -->

            <!-- 对账期 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-if="!state.id">
              <el-form-item label="对账期" prop="reconciliationMonth" class="inputDeep" label-width="70px">
                <el-date-picker
                  style="width: 100%"
                  value-format="YYYY-MM"
                  v-model="state.searchParams.reconciliationMonth"
                  type="month"
                  clearable
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col>

            <!-- 收款期 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-if="!state.id">
              <el-form-item label="收款期" prop="collectionPeriod" class="inputDeep" label-width="70px">
                <el-select filterable clearable style="width: 100%" v-model="state.searchParams.collectionPeriod" placeholder="请选择">
                  <template #label="{ label, value }">
                    <span>N+</span>
                    <span style="font-weight: bold">{{ value }}</span>
                  </template>
                  <el-option v-for="item in state.collectionPeriodList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 批量VIN码 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-if="!state.id">
              <el-form-item label-width="80px" class="inputDeep" label="批量VIN码" prop="vin">
                <div style="position: relative; width: 100%; height: 100%">
                  <el-input
                    style="width: 100%"
                    v-model="state.searchParams.vin"
                    type="textBatch"
                    :rows="1"
                    clearable
                    placeholder="批量搜索"
                    @click="openTextBatch('vin')"
                    @clear="emptyTextBatch(vinBatchRef)"
                  />
                  <div style="width: 100%; position: absolute; top: 0px; left: 0px; z-index: 9999">
                    <VINbatch
                      ref="vinBatchRef"
                      @arraySent="handleArrayReceived"
                      :showTextBatch="showTextBatch"
                      :targetField="{ name: 'vin', label: '批量VIN码', message: '请输入批量VIN码', max: 100 }"
                      :closeTextBatch="closeTextBatch"
                      :initialValue="currentBatchValue"
                    />
                  </div>
                </div>
              </el-form-item>
            </el-col>

            <!-- 批量六位VIN码 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-if="!state.id">
              <el-form-item label-width="100px" class="inputDeep" label="批量六位VIN码" prop="shortVin">
                <div style="position: relative; width: 100%; height: 100%">
                  <el-input
                    style="width: 100%"
                    v-model="state.searchParams.shortVin"
                    type="textBatch"
                    :rows="1"
                    clearable
                    placeholder="批量搜索"
                    @click="openShortVinBatch"
                    @clear="emptyTextBatch(shortVinBatchRef)"
                  />
                  <div style="width: 100%; position: absolute; top: 0px; left: 0px; z-index: 9999">
                    <VINbatch
                      ref="shortVinBatchRef"
                      @arraySent="handleArrayReceived"
                      :showTextBatch="showShortVinBatch"
                      :targetField="{ name: 'shortVin', label: '批量六位VIN码', message: '请输入批量六位VIN码', max: 100 }"
                      :closeTextBatch="closeShortVinBatch"
                      :initialValue="currentShortVinBatchValue"
                    />
                  </div>
                </div>
              </el-form-item>
            </el-col>

            <!-- 计划下达时间 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-if="!state.id">
              <el-form-item label="计划下达时间" prop="orderIssueDatetime" class="inputDeep">
                <el-date-picker
                  v-model="state.searchParams.orderIssueDatetime"
                  type="daterange"
                  style="width: 100%"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col>

            <!-- 要求交车日期 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-if="!state.id">
              <el-form-item label="要求交车日期" prop="predictDeliveryDatetime" class="inputDeep">
                <el-date-picker
                  v-model="state.searchParams.predictDeliveryDatetime"
                  type="daterange"
                  style="width: 100%"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col>

            <!-- 发运日期 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-if="!state.id">
              <el-form-item label="发运日期" prop="loadTime" class="inputDeep" label-width="80px">
                <el-date-picker
                  v-model="state.searchParams.loadTime"
                  type="daterange"
                  style="width: 100%"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col>
            <!-- 送达日期 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-if="!state.id">
              <el-form-item label="送达日期" prop="actualDropTime" class="inputDeep" label-width="80px">
                <el-date-picker
                  v-model="state.searchParams.actualDropTime"
                  type="daterange"
                  style="width: 100%"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择"
                />
              </el-form-item>
            </el-col>

            <!-- 品牌 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-if="!state.id">
              <el-form-item label="品牌" prop="brandName" class="inputDeep" label-width="50px">
                <el-input v-model="state.searchParams.brandName" style="width: 100%" placeholder="请输入" />
              </el-form-item>
            </el-col>

            <!-- 车型 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-if="!state.id">
              <el-form-item label="车型" prop="modelName" class="inputDeep" label-width="50px">
                <el-input v-model="state.searchParams.modelName" style="width: 100%" placeholder="请输入" />
              </el-form-item>
            </el-col>

            <!-- 线路 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-if="!state.id">
              <el-form-item label="线路" prop="lineName" class="inputDeep" label-width="50px">
                <el-input v-model="state.searchParams.lineName" style="width: 100%" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <!-- 客户单号 -->
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-if="!state.id">
              <el-form-item label="客户单号" prop="customerOrderNo" class="inputDeep" label-width="80px">
                <div style="position: relative; width: 100%; height: 100%">
                  <el-input
                    type="textBatch"
                    :rows="1"
                    style="width: 100%"
                    clearable
                    v-model="state.searchParams.customerOrderNo"
                    placeholder="请输入"
                    @click="openOrderVinBatch"
                    @clear="emptyTextBatch(orderVinBatchRef)"
                  />
                  <div style="width: 100%; position: absolute; top: 0px; left: 0px; z-index: 9999">
                    <VINbatch
                      ref="orderVinBatchRef"
                      @arraySent="handleArrayReceived"
                      :showTextBatch="showOrderVinBatch"
                      :targetField="{ name: 'customerOrderNo', label: '客户单号', message: '请输入客户单号', max: 100 }"
                      :closeTextBatch="closeOrderVinBatch"
                      :initialValue="currentOrderVinBatchValue"
                    />
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-form-item v-if="!state.id">
                <el-button type="primary" style="margin-left: 20px" @click="search(formRef)">查询</el-button>
                <el-button @click="resetForm(formRef)" style="margin-left: 10px">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form ref="statisticsFromRef" :model="state.statistics" class="mb-10px" :disabled="state.isView">
          <el-descriptions class="margin-top" :column="4" size="small" :border="true">
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">VIN台数</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.vinCount || 0 }}</span>
                台
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">含税收入合计</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.taxInclusiveIncome || 0 }}</span>

                元
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">未税应收合计</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.totalUntaxedIncome || 0 }}</span>
                元
              </div>
            </el-descriptions-item>
          </el-descriptions>
          <el-form label-width="auto" style="margin-top: 20px; margin-left: -60px; display: flex; flex-wrap: wrap">
            <el-form-item label="客户补贴" style="margin-left: 60px" prop="bonusAmountTotal">
              <el-input style="width: 200px" v-model="state.statistics.bonusAmountTotal" :readonly="true" placeholder="请选择">
                <template #append>
                  <el-button type="primary" @click="selectCustomerSubsidy">选择补贴项</el-button>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="补贴方式" style="margin-left: 60px">
              <el-select v-model="state.searchParams.bonusType" placeholder="请选择" style="width: 200px" @change="handleBonusTypeChange">
                <el-option v-for="item in state.selectSubsidy" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="客户扣款" style="margin-left: 60px" prop="deductionAmountTotal">
              <el-input style="width: 200px" v-model="state.statistics.deductionAmountTotal" :readonly="true" placeholder="请选择">
                <template #append>
                  <el-button type="primary" @click="selectCustomerDeduction">选择扣款项</el-button>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="扣款方式" style="margin-left: 60px">
              <el-select v-model="state.searchParams.deductionType" placeholder="请选择" style="width: 200px" @change="handleDeductionTypeChange">
                <el-option v-for="item in state.selectDeduction" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-form>
          <div style="max-width: 1200px">
            <el-alert
              title="1.根据系统配置的客户补贴/扣款是否参与对账来判断规则2是否生效；2.“补贴方式/扣款方式”选择后系统自动在计划收款方式中增加或者减扣，注意“收款方式金额 ≥ 客户扣款金额”"
              type="warning"
              :closable="false"
              show-icon
              style="border: 1px solid #f9c37d; margin-bottom: 10px"
            />
          </div>
          <el-descriptions class="mt-20px" title="计划收款信息" :column="3" size="small" :border="true">
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">现金</div>
              </template>
              <el-space wrap :spacer="spacer">
                <div class="font-bold">
                  金额：
                  <span>{{ state.statistics.cashPayment || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  税金
                  <span>{{ state.statistics.cashTax || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  价税合计
                  <span>{{ state.statistics.cashPayment || 0 }}</span>
                  元
                </div>
              </el-space>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">万金油</div>
              </template>
              <el-space wrap :spacer="spacer">
                <div class="font-bold">
                  金额：
                  <span>{{ state.statistics.wanjinOilPayment || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  税金
                  <span>{{ state.statistics.wanjinOilTax || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  价税合计
                  <span>{{ state.statistics.wanjinOilPayment || 0 }}</span>
                  元
                </div>
              </el-space>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">迪链</div>
              </template>
              <el-space wrap :spacer="spacer">
                <div class="font-bold">
                  金额：
                  <span>{{ state.statistics.diChainUnTax || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  税金
                  <span>{{ state.statistics.diChainTax || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  价税合计
                  <span>{{ state.statistics.diChainPayment || 0 }}</span>
                  元
                </div>
              </el-space>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">承兑</div>
              </template>
              <el-space wrap :spacer="spacer">
                <div class="font-bold">
                  金额：
                  <span>{{ state.statistics.acceptanceUnTax || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  税金
                  <span>{{ state.statistics.acceptanceTax || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  价税合计
                  <span>{{ state.statistics.acceptancePayment || 0 }}</span>
                  元
                </div>
              </el-space>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">转账</div>
              </template>
              <el-space wrap :spacer="spacer">
                <div class="font-bold">
                  金额：
                  <span>{{ state.statistics.transferUnTax || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  税金
                  <span>{{ state.statistics.transferTax || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  价税合计
                  <span>{{ state.statistics.transferPayments || 0 }}</span>
                  元
                </div>
              </el-space>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
        <el-button class="mb-10px" type="primary" icon="Plus" @click="addVin" size="small" v-if="state.id && !state.isView">添加VIN</el-button>
        <el-table
          ref="tableRef"
          @select="handleSelectChange"
          @select-all="handleSelectAllChange"
          show-overflow-tooltip
          :border="true"
          :data="state.tableData"
          v-loading="state.loading"
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ textAlign: 'center' }"
        >
          <el-table-column type="selection" width="65" fixed="left" v-if="!state.isView"></el-table-column>
          <!-- 序号 -->
          <el-table-column label="序号" type="index" width="60" fixed="left"></el-table-column>
          <el-table-column v-for="item in tableConfig.tableItem" :key="item.name" :label="item.label" :prop="item.name"></el-table-column>
        </el-table>
      </el-scrollbar>
      <template #footer v-if="!state.isView">
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="submitForm(statisticsFromRef)">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog title="添加VIN" width="80%" v-model="state.dispatchDialogVisible" @close="onCloseDispatchDialogVisible">
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form :inline="true" :model="state.searchParams" :disabled="state.isView">
          <el-form-item label="客户名称" prop="customerId" :rules="[{ required: true, message: '请选择客户', trigger: 'change' }]">
            <el-select :disabled="state.id" filterable clearable style="width: 100%" v-model="state.searchParams.customerId" placeholder="请选择">
              <el-option v-for="item in state.customerList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="六位VIN号" prop="shortVin">
            <el-input type="textarea" :rows="1" v-model="state.searchParams.shortVin" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="批量六位VIN码" prop="vin">
            <el-input type="textarea" :rows="1" v-model="state.searchParams.vin" placeholder="请输入" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search1">查询</el-button>
          </el-form-item>
        </el-form>
        <el-table
          ref="tableRef1"
          @selection-change="handleSelectionChange"
          show-overflow-tooltip
          :border="true"
          :data="state.tableData1"
          v-loading="state.loading1"
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ textAlign: 'center' }"
        >
          <el-table-column type="selection" width="65" align="center" header-align="center"></el-table-column>
          <!-- 序号 -->
          <el-table-column header-align="center" label="序号" type="index" width="60" align="center"></el-table-column>
          <el-table-column
            header-align="center"
            v-for="item in tableConfig.tableItem"
            :key="item.name"
            :label="item.label"
            :prop="item.name"
            align="center"
          ></el-table-column>
        </el-table>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="onCloseDispatchDialogVisible">取 消</el-button>
          <el-button size="small" type="primary" @click="handleOkDispatch">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog title="选择扣款项" v-model="state.customerDeductionDialogVisible" width="80%" @closed="closeCustomerDeductionDialog">
      <el-table
        ref="tableRef2"
        @selection-change="handleSelectionChange1"
        show-overflow-tooltip
        :border="true"
        :data="state.customerDeductionList"
        v-loading="state.loading2"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ textAlign: 'center' }"
      >
        <!-- 序号 -->
        <el-table-column header-align="center" label="序号" type="selection" width="60" align="center"></el-table-column>
        <el-table-column
          header-align="center"
          v-for="item in tableConfig.tableItem1"
          :key="item.name"
          :label="item.label"
          :prop="item.name"
          align="center"
        ></el-table-column>
      </el-table>
      <template #footer>
        <span>
          <el-button @click="state.customerDeductionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleOkCustomerDeduction">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog title="选择补贴项" v-model="state.customerSubsidyDialogVisible" width="80%" @closed="closeCustomerDeductionDialog">
      <el-table
        ref="tableRef3"
        @selection-change="handleSelectionChange2"
        show-overflow-tooltip
        :border="true"
        :data="state.customerSubsidyList"
        v-loading="state.loading2"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ textAlign: 'center' }"
      >
        <!-- 序号 -->
        <el-table-column header-align="center" label="序号" type="selection" width="60" align="center"></el-table-column>
        <el-table-column
          header-align="center"
          v-for="item in tableConfig.tableItem2"
          :key="item.name"
          :label="item.label"
          :prop="item.name"
          align="center"
        ></el-table-column>
      </el-table>
      <template #footer>
        <span>
          <el-button @click="state.customerSubsidyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleOkCustomerSubsidy">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import {
    getFleetOutFleetBusinessCustomerSelectOptionApi,
    postFleetOrderSettlementDetailViewDriverDetailSummaryApi,
    getFleetOrderSettlementDetailViewDriverDetailPageApi,
    postFleetOrderSettlementGenerateSettlementApi,
    getFleetOrderSettlementGetSettlementInfoApi,
    postFleetOrderSettlementUpdateSettlementApi,
    getFleetSettlementCustomerReconciliationManagementComputeAllApi,
    postFleetSettlementCustomerReconciliationManagementAddBillApi,
    postFleetSettlementCustomerReconciliationManagementBindChangeApi,
    postFleetSettlementCustomerReconciliationManagementComputeApi,
    postFleetSettlementCustomerReconciliationManagementSummaryApi,
    getFleetSettlementCustomerDeductionPageApi,
    getFleetSettlementCustomerReconciliationManagementOptionsReconciliationDateApi,
    getDistributeSelectReceivePaymentMethodselectApi,
  } from '@/api/financialManagement'
  import { SearchParamsVO } from '@/api/financialManagement/types'
  import { FormInstance } from 'element-plus'
  import VINbatch from '@/components/TopQueryGroupComponent/components/VINbatch.vue'
  const emit = defineEmits(['confirmStatementSuccess'])
  const formRef = ref()
  const statisticsFromRef = ref()
  const tableRef = ref()
  const tableRef1 = ref()
  const tableRef2 = ref()
  const tableRef3 = ref()
  const showTextBatch = ref(false)
  const showShortVinBatch = ref(false)
  const showOrderVinBatch = ref(false)
  const currentBatchField = ref('')
  const currentBatchValue = ref('')
  const currentShortVinBatchField = ref('')
  const currentOrderVinBatchField = ref('')
  const currentShortVinBatchValue = ref('')
  const currentOrderVinBatchValue = ref('')
  const shortVinBatchRef = ref()
  const vinBatchRef = ref()
  const orderVinBatchRef = ref()

  const spacer = h(ElDivider, { direction: 'vertical' })
  const state = reactive<any>({
    reconciliationMonthList: [],
    collectionPeriodList: [],
    loading: false,
    loading1: false,
    dispatchDialogVisible: false,
    customerDeductionDialogVisible: false,
    customerSubsidyDialogVisible: false,
    customerDeductionList: [],
    customerSubsidyList: [],
    dialogVisible: {
      visible: false,
      title: '新增驾驶员结算单',
    },
    formData: {},
    searchParams: {
      customerId: '',
      customerName: '',
      vin: '',
      shortVin: '',
      orderIssueDatetime: [],
      predictDeliveryDatetime: [],
      brandName: '',
      modelName: '',
      loadTime: [],
      actualDropTime: [],
      lineName: '',
      deductionAmountTotal: '',
      bonusAmountTotal: '',
      deductionIds: [],
      bonusIds: [],
      deductionType: '2',
      bonusType: '2',
      // 客户单号批量查询
      customerOrderNo: '',
    } as SearchParamsVO,
    ids: [] as string[],
    id: '',
    isView: false, //是否是查看
    tableData: [],
    tableData1: [],
    driverList: [] as SelectOptions[],
    statistics: {},
    totalSummary: {},
    selectDispatchList: [],
    selectCustomerDeductionList: [],
    selectSubsidy: [], //补贴
    selectDeduction: [], //扣款
  })
  const tableConfig = {
    tableItem: [
      {
        name: 'vin',
        label: 'VIN码',
      },
      {
        name: 'shortVin',
        label: 'VIN后六位码',
      },
      {
        name: 'customerOrderNo',
        label: '客户单号',
      },
      {
        name: 'orderRemark',
        label: '订单备注',
      },
      {
        name: 'reorderStatusDesc',
        label: '回单状态',
      },
      {
        name: 'startArea',
        label: '起点',
      },
      {
        name: 'endArea',
        label: '终点',
      },
      {
        name: 'brandName',
        label: '品牌',
      },
      {
        name: 'modelName',
        label: '车型',
      },
      {
        name: 'lineName',
        label: '线路',
      },
      {
        name: 'lineMileage',
        label: '里程',
      },
      {
        name: 'dropUpPointName',
        label: '交车点名称',
      },
      {
        name: 'dropUpPointAddress',
        label: '交车地址',
      },
      {
        name: 'orderIssueDatetime',
        label: '计划下达日期',
      },
      {
        name: 'predictDeliveryDatetime',
        label: '要求交车日期',
      },
      {
        name: 'loadTime',
        label: '实际发车时间',
      },
      {
        name: 'actualDropTime',
        label: '实际交车日期',
      },
      {
        name: 'childCompanyName',
        label: '所属分/子公司',
      },
      {
        name: 'settlementType',
        label: '结算类型',
      },
      {
        name: 'settlementMethod',
        label: '结算方式',
      },
      {
        name: 'price',
        label: '单价',
      },
      {
        name: 'prepaidFreight',
        label: '运费',
      },
      {
        name: 'realFreight',
        label: '实际运费',
      },
      {
        name: 'freightIncome',
        label: '运输收入',
      },
      {
        name: 'freightSubsidy',
        label: '补贴收入',
      },
      {
        name: 'freightFeeDifference',
        label: '客户运费差',
      },
      {
        name: 'customerIncomeTotal',
        label: '收入合计',
      },
      {
        name: 'outcomeUnTaxed',
        label: '未税收入合计',
      },
      {
        name: 'cashAmount',
        label: '现金收款',
      },
      {
        name: 'wjyAmount',
        label: '万金油收款',
      },
      {
        name: 'dlAmount',
        label: '迪链收款',
      },
      {
        name: 'acceptAmount',
        label: '承兑收款',
      },
      {
        name: 'transferAmount',
        label: '转账收款',
      },
      {
        name: 'outcomeTaxed',
        label: '税金',
      },
      {
        name: 'outcomeIncludeTaxed',
        label: '含税收入',
      },
    ],
    tableItem1: [
      {
        name: 'deductionNo',
        label: '客户扣款单号',
      },
      {
        name: 'vin',
        label: 'vin',
      },
      {
        name: 'customerName',
        label: '客户名称',
      },
      {
        name: 'deductionAmount',
        label: '扣款金额',
      },
      {
        name: 'timeOfDeduction',
        label: '发生扣款时间',
      },
      {
        name: 'reasonForDeduction',
        label: '扣款原因',
      },
    ],
    tableItem2: [
      {
        name: 'deductionNo',
        label: '客户补贴单号',
      },
      {
        name: 'vin',
        label: 'vin',
      },
      {
        name: 'customerName',
        label: '客户名称',
      },
      {
        name: 'deductionAmount',
        label: '补贴金额',
      },
      {
        name: 'timeOfDeduction',
        label: '发生补贴时间',
      },
      {
        name: 'reasonForDeduction',
        label: '补贴原因',
      },
    ],
  }
  function openTextBatch(name: any) {
    currentBatchField.value = name
    currentBatchValue.value = state.searchParams[name] || '' // 保存当前值
    showTextBatch.value = true
  }

  const emptyTextBatch = (fieldRef: any) => {
    if (fieldRef?.list && fieldRef.list.length > 0) {
      fieldRef.list = []
    }
  }

  const handleArrayReceived = (array: any, targetField: { name: string | number }) => {
    state.searchParams[targetField.name] = Object.values(array).join(',')
    currentBatchValue.value = state.searchParams[targetField.name]
  }
  const closeTextBatch = () => {
    showTextBatch.value = false
  }
  const closeShortVinBatch = () => {
    showShortVinBatch.value = false
  }
  function openShortVinBatch() {
    currentShortVinBatchField.value = 'shortVin'
    currentShortVinBatchValue.value = state.searchParams.shortVin || '' // 保存当前值
    showShortVinBatch.value = true
  }
  function openOrderVinBatch() {
    currentOrderVinBatchField.value = 'customerOrderNo'
    currentOrderVinBatchValue.value = state.searchParams.customerOrderNo || '' // 保存当前值
    showOrderVinBatch.value = true
  }
  const closeOrderVinBatch = () => {
    showOrderVinBatch.value = false
  }
  watch(
    () => state.dialogVisible.visible,
    (val) => {
      if (val) {
        getFleetOutFleetBusinessCustomerSelectOption()
        getDistributeSelectReceivePaymentMethodselect()
      }
    },
  )
  //添加vin
  const addVin = () => {
    state.dispatchDialogVisible = true
  }
  const onCloseDispatchDialogVisible = () => {
    state.tableData1 = []
    state.selectDispatchList = []
    state.searchParams.dispatchNo = ''
    state.searchParams.vin = ''
    state.searchParams.shortVin = ''
    state.dispatchDialogVisible = false
  }
  const handleOkDispatch = () => {
    state.tableData.push(...state.selectDispatchList.filter((item: any) => !state.tableData.some((tableItem: any) => tableItem.id === item.id)))
    state.tableData.forEach((item: any) => {
      setTimeout(() => {
        tableRef.value.toggleRowSelection(item, true)
      })
    })
    handleSelectChange(state.tableData)
    onCloseDispatchDialogVisible()
  }
  //获取结算单详情
  const postFleetSettlementCustomerReconciliationManagementCompute = async (row: {
    collectionPeriod: any
    reconciliationMonth: any
    id: string
    customerId: string
    customerName: string
  }) => {
    state.loading = true
    const { data } = await postFleetSettlementCustomerReconciliationManagementComputeApi({ reconciliationId: row.id, customerId: row.customerId })
    state.id = row.id
    state.searchParams.customerId = row.customerId
    state.searchParams.customerName = row.customerName
    state.statistics = data.statistics
    state.tableData = data.rows
    state.searchParams.deductionIds = data.statistics.deductionIds
    state.searchParams.bonusIds = data.statistics.bonusIds
    state.ids = data.rows.map((item: any) => item.id)

    if (state.statistics.deductionType) {
      state.searchParams.deductionType = String(state.statistics.deductionType)
    } else {
      state.searchParams.deductionType = '2'
    }
    if (state.statistics.bonusType) {
      state.searchParams.bonusType = String(state.statistics.bonusType)
    } else {
      state.searchParams.bonusType = '2'
    }

    data.rows.forEach((item: any) => {
      setTimeout(() => {
        tableRef.value.toggleRowSelection(item, true)
      })
    })
    state.loading = false
  }
  //获取客户下拉
  const getFleetOutFleetBusinessCustomerSelectOption = async () => {
    const params = {
      selectType: 'customerName',
      simple: true,
    }
    const { data } = await getFleetOutFleetBusinessCustomerSelectOptionApi(params)
    state.customerList = data
  }
  //获取奖惩下拉数据
  const getDistributeSelectReceivePaymentMethodselect = async () => {
    const { data } = await getDistributeSelectReceivePaymentMethodselectApi()
    state.selectSubsidy = data
    state.selectDeduction = data
  }
  const closeDialog = () => {
    //清空表单
    state.ids = []
    state.totalSummary = {}
    state.statistics = {}
    state.tableData = []
    state.collectionPeriodList = []

    state.searchParams.deductionAmountTotal = ''
    state.searchParams.bonusAmountTotal = ''
    state.searchParams.deductionIds = []
    state.searchParams.deductionType = '2'
    state.searchParams.bonusIds = []
    state.searchParams.bonusType = '2'

    tableRef.value.clearSelection()
    formRef.value ? formRef.value.resetFields() : ''
    statisticsFromRef.value ? statisticsFromRef.value.resetFields() : ''
    state.dialogVisible.visible = false
  }
  const search = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid) => {
      if (valid) {
        const params = {
          customerId: state.searchParams.customerId,
          vin: state.searchParams.vin,
          shortVin: state.searchParams.shortVin,
          startOrderIssueDatetime: state.searchParams.orderIssueDatetime ? state.searchParams.orderIssueDatetime[0] : undefined,
          endOrderIssueDatetime: state.searchParams.orderIssueDatetime ? state.searchParams.orderIssueDatetime[1] : undefined,
          startPredictDeliveryDatetime: state.searchParams.predictDeliveryDatetime ? state.searchParams.predictDeliveryDatetime[0] : undefined,
          endPredictDeliveryDatetime: state.searchParams.predictDeliveryDatetime ? state.searchParams.predictDeliveryDatetime[1] : undefined,
          startLoadTime: state.searchParams.loadTime ? state.searchParams.loadTime[0] : undefined,
          endLoadTime: state.searchParams.loadTime ? state.searchParams.loadTime[1] : undefined,
          startActualDropTime: state.searchParams.actualDropTime ? state.searchParams.actualDropTime[0] : undefined,
          endActualDropTime: state.searchParams.actualDropTime ? state.searchParams.actualDropTime[1] : undefined,
          customerName: state.searchParams.customerName,
          brandName: state.searchParams.brandName,
          modelName: state.searchParams.modelName,
          lineName: state.searchParams.lineName,
          deductionIds: state.searchParams.deductionIds,
          bonusIds: state.searchParams.bonusIds,
          reconciliationMonth: state.searchParams.reconciliationMonth,
          collectionPeriod: state.searchParams.collectionPeriod,
          customerOrderNo: state.searchParams.customerOrderNo,
        }
        try {
          state.loading = true
          const { data } = await getFleetSettlementCustomerReconciliationManagementComputeAllApi(params)
          const { rows, statistics, totalSummary } = data
          state.tableData = rows
          state.ids = rows.map((item: any) => item.id)
          state.statistics = statistics
          state.totalSummary = totalSummary
          state.tableData.forEach((item: any) => {
            setTimeout(() => {
              tableRef.value.toggleRowSelection(item, true)
            })
          })
          state.loading = false
        } catch (e) {
          state.loading = false
        }
      } else {
      }
    })
  }

  const handleBonusTypeChange = async () => {
    await getSummary()
  }

  const handleDeductionTypeChange = async () => {
    await getSummary()
  }

  const handleCustomerChange = async (val: any) => {
    const customer = state.customerList.find((item: any) => item.value === val)
    state.searchParams.customerName = customer.label
    const { data } = await getFleetSettlementCustomerReconciliationManagementOptionsReconciliationDateApi({
      customerId: val,
    })
    state.reconciliationMonthList = data.reconciliationMonth
    state.collectionPeriodList = data.collectionPeriod
  }
  const search1 = async () => {
    const params = {
      customerId: state.searchParams.customerId,
      vin: state.searchParams.vin,
      shortVin: state.searchParams.shortVin,
      startLoadTime: state.searchParams.loadTime ? state.searchParams.loadTime[0] : undefined,
      endLoadTime: state.searchParams.loadTime ? state.searchParams.loadTime[1] : undefined,
      customerName: state.searchParams.customerName,
      brandName: state.searchParams.brandName,
      modelName: state.searchParams.modelName,
      settlementNo: state.searchParams.settlementNo,
      qualityLoss: state.searchParams.qualityLoss === '-1' ? '' : state.searchParams.qualityLoss,
      dealStatus: state.searchParams.dealStatus === '-1' ? '' : state.searchParams.dealStatus,
      reconciliationMonth: state.searchParams.reconciliationMonth,
      collectionPeriod: state.searchParams.collectionPeriod,
    }

    const { data } = await getFleetSettlementCustomerReconciliationManagementComputeAllApi(params)
    const { rows, statistics, totalSummary } = data
    state.tableData1 = rows
    // state.ids = rows.map((item: any) => item.id);
    // state.statistics = statistics;
    // state.totalSummary = totalSummary;
    state.tableData1.forEach((item: any) => {
      setTimeout(() => {
        tableRef1.value.toggleRowSelection(item, true)
      })
    })
  }
  const handleSelectChange = async (val: any) => {
    state.ids = val.map((item: any) => item.id)
    getSummary()
  }
  const getSummary = async () => {
    const { data } = await postFleetSettlementCustomerReconciliationManagementSummaryApi({
      customerId: state.searchParams.customerId,
      computeIds: state.ids,
      deductionIds: state.searchParams.deductionIds,
      bonusIds: state.searchParams.bonusIds,
      deductionType: state.searchParams.deductionType,
      bonusType: state.searchParams.bonusType,
    })
    state.statistics = data
    state.statistics = {
      vinCount: data.vinCount,
      totalUntaxedIncome: data.totalUntaxedIncome,
      taxes: data.taxes,
      taxInclusiveIncome: data.taxInclusiveIncome,
      cashPayment: data.cashPayment,
      cashTax: data.cashTax,
      wanjinOilPayment: data.wanjinOilPayment,
      wanjinOilTax: data.wanjinOilTax,
      diChainUnTax: data.diChainUnTax,
      diChainTax: data.diChainTax,
      diChainPayment: data.diChainPayment,
      acceptanceUnTax: data.acceptanceUnTax,
      acceptanceTax: data.acceptanceTax,
      acceptancePayment: data.acceptancePayment,
      transferUnTax: data.transferUnTax,
      transferTax: data.transferTax,
      transferPayments: data.transferPayments,
      deductionAmountTotal: data.deductionAmountTotal,
      bonusAmountTotal: data.bonusAmountTotal,
    }
  }
  const handleSelectionChange = (val: any) => {
    state.selectDispatchList = val
  }
  const handleSelectionChange1 = (val: any) => {
    state.selectCustomerDeductionList = val
  }
  const handleSelectionChange2 = (val: any) => {
    state.selectCustomerSubsidyList = val
  }
  const handleSelectAllChange = async (val: any) => {
    await handleSelectChange(val)
  }
  const submitForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate(async (valid) => {
      if (valid) {
        try {
          if (state.id) {
            const params = {
              computeIds: state.ids,
              id: state.id,
              customerId: state.searchParams.customerId,
              deductionIds: state.searchParams.deductionIds,
              deductionType: state.searchParams.deductionType,
              bonusType: state.searchParams.bonusType,
              bonusIds: state.searchParams.bonusIds,
            }
            await postFleetSettlementCustomerReconciliationManagementBindChangeApi(params)
            state.id = ''
            ElMessage.success('修改成功')
          } else {
            const params = {
              customerId: state.searchParams.customerId,
              customerName: state.searchParams.customerName,
              computeIds: state.ids,
              deductionIds: state.searchParams.deductionIds,
              deductionType: state.searchParams.deductionType,
              bonusType: state.searchParams.bonusType,
              bonusIds: state.searchParams.bonusIds,
            }

            await postFleetSettlementCustomerReconciliationManagementAddBillApi(params)
            ElMessage.success('生成成功')
          }
          closeDialog()
          emit('confirmStatementSuccess')
        } catch (e) {}
      } else {
      }
    })
  }
  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
  }
  const closeCustomerDeductionDialog = () => {
    state.customerDeductionList = []
    state.customerDeductionDialogVisible = false
  }
  const handleOkCustomerSubsidy = () => {
    state.customerSubsidyDialogVisible = false
    state.searchParams.bonusAmountTotal = state.selectCustomerSubsidyList.reduce((acc: number, item: any) => acc + item.deductionAmount, 0)
    state.searchParams.bonusIds = state.selectCustomerSubsidyList.map((item: any) => item.id)
    getSummary()
  }

  const handleOkCustomerDeduction = () => {
    state.customerDeductionDialogVisible = false
    state.searchParams.deductionAmountTotal = state.selectCustomerDeductionList.reduce((acc: number, item: any) => acc + item.deductionAmount, 0)
    state.searchParams.deductionIds = state.selectCustomerDeductionList.map((item: any) => item.id)
    getSummary()
  }
  const selectCustomerDeduction = async () => {
    if (!state.searchParams.customerId) {
      ElMessage.warning('请先选择客户')
      return
    }
    state.customerDeductionDialogVisible = true
    const { data } = await getFleetSettlementCustomerDeductionPageApi({
      page: 1,
      limit: 10000,
      customerId: state.searchParams.customerId,
      type: '客户扣款',
      reconciliationId: state.id ? state.id : null,
      billState: '未对账',
    })
    state.customerDeductionList = data.rows
    nextTick(() => {
      const deductionIds = state.statistics.deductionIds || state.searchParams.deductionIds || []
      state.customerDeductionList.forEach((item: { id: any }) => {
        if (deductionIds.includes(item.id)) {
          tableRef2.value.toggleRowSelection(item, true)
        }
      })
    })
  }

  const selectCustomerSubsidy = async () => {
    if (!state.searchParams.customerId) {
      ElMessage.warning('请先选择客户')
      return
    }
    state.customerSubsidyDialogVisible = true
    const { data } = await getFleetSettlementCustomerDeductionPageApi({
      page: 1,
      limit: 10000,
      customerId: state.searchParams.customerId,
      type: '客户补贴',
      reconciliationId: state.id ? state.id : null,
    })
    state.customerSubsidyList = data.rows
    nextTick(() => {
      const bonusIds = state.statistics.bonusIds || state.searchParams.bonusIds || []

      state.customerSubsidyList.forEach((item: { id: any }) => {
        if (bonusIds.includes(item.id)) {
          tableRef3.value.toggleRowSelection(item, true)
        }
      })
    })
  }

  defineExpose({
    state,
    postFleetSettlementCustomerReconciliationManagementCompute,
  })
</script>
<style scoped lang="scss">
  .inputDeep {
    width: 100%;
    background: #f0f0f0;
    border: 1px solid #dbdbdb;
    display: flex;
    align-items: center;
    border-radius: 2px;
    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;
      border-radius: 0;
      cursor: default;
      .el-input__inner {
        cursor: default !important;
      }
    }
    :deep(.el-textarea__inner) {
      box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;
      border-radius: 0;
      cursor: default;
    }
    :deep(.el-select__wrapper) {
      box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;
      border-radius: 0;
    }
  }
</style>
