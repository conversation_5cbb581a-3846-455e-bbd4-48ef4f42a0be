<!--
 * @Author: llm
 * @Date: 2024-12-27 19:37:32
 * @LastEditors: llm
 * @LastEditTime: 2025-05-30 14:48:57
 * @Description:
-->
<template>
  <div>
    <el-dialog :lock-scroll="true" :draggable="true" v-model="state.dialogVisible.visible" :title="state.dialogVisible.title" width="80%" @close="closeDialog">
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form ref="formRef" :inline="true" :model="state.searchParams" :disabled="state.isView" label-width="100px">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-form-item
                label-width="70px"
                class="inputDeep"
                label="承运商"
                prop="carrierId"
                :rules="[{ required: true, message: '请选择承运商', trigger: 'change' }]"
              >
                <el-select
                  :disabled="state.id ? true : false"
                  filterable
                  clearable
                  style="width: 100%"
                  v-model="state.searchParams.carrierId"
                  placeholder="请选择"
                  @change="selectcarrierId"
                >
                  <el-option v-for="item in state.carrierList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-form-item label-width="80px" class="inputDeep" label="批量VIN码" prop="vin">
                <div style="position: relative; width: 100%; height: 100%">
                  <el-input
                    style="width: 100%"
                    v-model="state.searchParams.vin"
                    type="textBatch"
                    :rows="1"
                    clearable
                    placeholder="批量搜索"
                    @click="openTextBatch('vin')"
                    @clear="emptyTextBatch(vinBatchRef)"
                  />
                  <div style="width: 100%; position: absolute; top: 0px; left: 0px; z-index: 9999">
                    <VINbatch
                      ref="vinBatchRef"
                      @arraySent="handleArrayReceived"
                      :showTextBatch="showTextBatch"
                      :targetField="{ name: 'vin', label: '批量VIN码', message: '请输入批量VIN码', max: 100 }"
                      :closeTextBatch="closeTextBatch"
                      :initialValue="currentBatchValue"
                    />
                  </div>
                </div>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-form-item label-width="100px" class="inputDeep" label="批量六位VIN码" prop="shortVin">
                <div style="position: relative; width: 100%; height: 100%">
                  <el-input
                    style="width: 100%"
                    v-model="state.searchParams.shortVin"
                    type="textBatch"
                    :rows="1"
                    clearable
                    placeholder="批量搜索"
                    @click="openShortVinBatch"
                    @clear="emptyTextBatch(shortVinBatchRef)"
                  />
                  <div style="width: 100%; position: absolute; top: 0px; left: 0px; z-index: 9999">
                    <VINbatch
                      ref="shortVinBatchRef"
                      @arraySent="handleArrayReceived"
                      :showTextBatch="showShortVinBatch"
                      :targetField="{ name: 'shortVin', label: '批量六位VIN码', message: '请输入批量六位VIN码', max: 100 }"
                      :closeTextBatch="closeShortVinBatch"
                      :initialValue="currentShortVinBatchValue"
                    />
                  </div>
                </div>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-form-item label-width="100px" class="inputDeep" label="计划下达时间" prop="orderIssueDatetime">
                <el-date-picker
                  v-model="state.searchParams.orderIssueDatetime"
                  type="datetimerange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 200px"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-form-item label-width="100px" class="inputDeep" label="要求交车日期" prop="predictDeliveryDatetime">
                <el-date-picker
                  v-model="state.searchParams.predictDeliveryDatetime"
                  type="datetimerange"
                  start-placeholder="开始日期"
                  style="width: 100%"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-form-item label-width="70px" class="inputDeep" label="发运日期" prop="loadTime">
                <el-date-picker
                  v-model="state.searchParams.loadTime"
                  type="daterange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择"
                  clearable
                />
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-form-item label-width="50px" class="inputDeep" label="客户" prop="customerName">
                <el-input clearable v-model="state.searchParams.customerName" placeholder="请输入" style="width: 100%" />
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-form-item label-width="50px" class="inputDeep" label="品牌" prop="brandName">
                <el-input clearable v-model="state.searchParams.brandName" placeholder="请输入" style="width: 100%" />
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-form-item label-width="50px" class="inputDeep" label="车型" prop="modelName">
                <el-input clearable v-model="state.searchParams.modelName" placeholder="请输入" style="width: 100%" />
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-form-item label-width="80px" class="inputDeep" label="是否质损" prop="qualityLoss">
                <el-select v-model="state.searchParams.qualityLoss" placeholder="请选择" style="width: 100%" clearable filterable>
                  <el-option label="不限" value="-1" />
                  <el-option label="是" value="是" />
                  <el-option label="否" value="否" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-form-item class="inputDeep" label="质损处理结果" prop="dealStatus">
                <el-select v-model="state.searchParams.dealStatus" placeholder="请选择" style="width: 100%" clearable filterable>
                  <el-option label="不限" value="-1" />
                  <el-option label="已完成" value="已完成" />
                  <el-option label="未完成" value="未完成" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-form-item>
                <el-button type="primary" style="margin-left: 20px" @click="search(formRef, 'search')">查询</el-button>
                <!-- 重置 -->
                <el-button @click="resetForm(formRef)" style="margin-left: 10px">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form ref="statisticsFromRef" :model="state.statistics" class="mb-10px" :disabled="state.isView">
          <el-descriptions class="margin-top" :title="`应付合计：${state.statistics.outcomeIncludeTaxed || 0}元`" :column="4" size="small" :border="true">
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">VIN台数</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.totalVin || 0 }}</span>
                台
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">未税支出合计</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.outcomeUnTaxed || 0 }}</span>
                元
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">税金合计</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.outcomeTaxed || 0 }}</span>
                元
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">计税支出合计</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.outcomeIncludeTaxed || 0 }}</span>
                元
              </div>
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions class="mt-20px" :title="`应扣合计：${state.statistics.deductionAmount || 0}元`" :column="4" size="small" :border="true">
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">现金借支</div>
              </template>
              <div class="flex items-center">
                <el-radio-group v-model="state.statistics.deductionTypeName">
                  <el-radio :label="item.label" :value="item.label" v-for="item in typeOptions" :key="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
                <div class="flex items-center ml-20px">
                  <el-text class="ml-20px">{{ state.statistics.deductionAmount || 0 }}元</el-text>
                  <el-button type="primary" link size="small" class="ml-20px" @click="getFleetOrderCarrierCostSummaryCashLoanSelectOption">选择</el-button>
                </div>
              </div>
            </el-descriptions-item>
          </el-descriptions>
          <!-- 补贴合计 -->
          <el-descriptions class="mt-20px" :title="`补贴合计：${state.statistics.carrierSubsidyAmount || 0}元`" :column="4" size="small" :border="true">
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">外协补贴</div>
              </template>
              <div class="flex items-center">
                <el-radio-group v-model="state.statistics.carrierSubsidyTypeName" @change="transformtion">
                  <el-radio :label="item.label" :value="item.label" v-for="item in typeOptions" :key="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
                <div class="flex items-center ml-20px">
                  <el-text class="ml-20px">{{ state.statistics.carrierSubsidyAmount || 0 }}元</el-text>
                  <el-button type="primary" link size="small" class="ml-20px" @click="getFleetOrderCarrierCostSummaryCashLoanSelectOptioned('补贴')"
                    >选择</el-button
                  >
                </div>
              </div>
            </el-descriptions-item>
          </el-descriptions>
          <!-- 扣款合计 -->
          <el-descriptions class="mt-20px" :title="`扣款合计：${state.statistics.carrierDeductionAmount || 0}元`" :column="4" size="small" :border="true">
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">外协扣款</div>
              </template>
              <div class="flex items-center">
                <el-radio-group v-model="state.statistics.carrierDeductionTypeName" @change="transformtion">
                  <el-radio :label="item.label" :value="item.label" v-for="item in typeOptions" :key="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
                <div class="flex items-center ml-20px">
                  <el-text class="ml-20px">{{ state.statistics.carrierDeductionAmount || 0 }}元</el-text>
                  <el-button type="primary" link size="small" class="ml-20px" @click="getFleetOrderCarrierCostSummaryCashLoanSelectOptioned('扣款')"
                    >选择</el-button
                  >
                </div>
              </div>
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions class="mt-20px" title="挂账信息" :column="2" size="small" :border="true">
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">历史挂账金额</div>
              </template>
              <div class="font-bold">
                <span>{{ state.statistics.chargeAmount || 0 }}</span>
                元
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">本次冲账金额</div>
              </template>
              <div class="flex items-center justify-between">
                <el-radio-group v-model="state.statistics.hedgingName">
                  <el-radio :label="item.label" :value="item.label" v-for="item in typeOptions" :key="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
                <div class="font-bold flex items-center justify-between">
                  <div class="font-bold flex items-center">
                    <el-input placeholder="请输入" v-model="state.statistics.hedgingAmount" type="number" class="w-60px"></el-input>
                    &nbsp;元
                    <el-button type="primary" link size="small" @click="search(formRef, 'resetCashLoan')">重算</el-button>
                  </div>
                  <div class="flex items-center">
                    <el-icon color="#f3d19e">
                      <QuestionFilled />
                    </el-icon>
                    <span class="color-red text-12px">正值为公司扣驾驶员金额，负值为公司奖励驾驶员</span>
                  </div>
                </div>
              </div>
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions class="mt-20px" title="计划付款信息" :column="3" size="small" :border="true">
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">现金</div>
              </template>
              <el-space wrap :spacer="spacer">
                <div class="font-bold">
                  金额：
                  <span>{{ state.statistics.cashAmount || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  税金
                  <span>{{ state.statistics.cashTaxedAmount || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  价税合计
                  <span>{{ state.statistics.cashTotalAmount || 0 }}</span>
                  元
                </div>
              </el-space>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">万金油</div>
              </template>
              <el-space wrap :spacer="spacer">
                <div class="font-bold">
                  金额：
                  <span>{{ state.statistics.wjyAmount || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  税金
                  <span>{{ state.statistics.wjyTaxedAmount || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  价税合计
                  <span>{{ state.statistics.wjyTotalAmount || 0 }}</span>
                  元
                </div>
              </el-space>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">迪链</div>
              </template>
              <el-space wrap :spacer="spacer">
                <div class="font-bold">
                  金额：
                  <span>{{ state.statistics.dlAmount || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  税金
                  <span>{{ state.statistics.dlTaxedAmount || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  价税合计
                  <span>{{ state.statistics.dlTotalAmount || 0 }}</span>
                  元
                </div>
              </el-space>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">承兑</div>
              </template>
              <el-space wrap :spacer="spacer">
                <div class="font-bold">
                  金额：
                  <span>{{ state.statistics.acceptAmount || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  税金
                  <span>{{ state.statistics.acceptTaxedAmount || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  价税合计
                  <span>{{ state.statistics.acceptTotalAmount || 0 }}</span>
                  元
                </div>
              </el-space>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">转账</div>
              </template>
              <el-space wrap :spacer="spacer">
                <div class="font-bold">
                  金额：
                  <span>{{ state.statistics.transferAmount || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  税金
                  <span>{{ state.statistics.transferTaxedAmount || 0 }}</span>
                  元
                </div>
                <div class="font-bold">
                  价税合计
                  <span>{{ state.statistics.transferTotalAmount || 0 }}</span>
                  元
                </div>
              </el-space>
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions class="mt-20px" :column="3" size="small" :border="true">
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">实付合计</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.finalOutcomeUnTaxed || 0 }}</span>
                元
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">税金</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.finalOutcomeTaxed || 0 }}</span>
                元
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">实付含税支出</div>
              </template>
              <div class="font-bold">
                <span class="color-red">{{ state.statistics.finalOutcomeIncludeTaxed || 0 }}</span>
                元
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
        <el-button class="mb-10px" type="primary" icon="Plus" @click="addVin" size="small" v-if="state.id && !state.isView">添加VIN</el-button>
        <!-- <el-table
          ref="tableRef"
          @select="handleSelectChange"
          @select-all="handleSelectAllChange"
          show-overflow-tooltip
          :border="true"
          :data="state.tableData"
          v-loading="state.loading"
        >
          <el-table-column type="selection" width="65"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column v-for="item in tableConfig.tableItem" :key="item.name" :label="item.label" :prop="item.name"></el-table-column>
        </el-table> -->
        <TableComponent
          ref="tableComponentRef"
          :loading="state.loading"
          :tableConfig="tableConfig"
          :tableData="state.tableData"
          rowKey="id"
          @handleSelectRows="handleSelectChange"
        />
      </el-scrollbar>
      <template #footer v-if="!state.isView">
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="submitForm(statisticsFromRef)">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog draggable title="添加VIN" width="80%" v-model="state.dispatchDialogVisible" @close="onCloseDispatchDialogVisible">
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form :inline="true" :model="state.searchParams" :disabled="state.isView">
          <el-form-item label="承运商" prop="carrierId" :rules="[{ required: true, message: '请选择承运商', trigger: 'change' }]">
            <el-select :disabled="state.id" filterable clearable style="width: 180px" v-model="state.searchParams.carrierId" placeholder="请选择">
              <el-option v-for="item in state.carrierList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="六位VIN号" prop="shortVin">
            <el-input type="textarea" v-model="state.searchParams.shortVin" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="批量六位VIN码" prop="vin">
            <el-input type="textarea" v-model="state.searchParams.vin" placeholder="请输入" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search1">查询</el-button>
          </el-form-item>
        </el-form>
        <!-- <el-table
          ref="tableComponentRef1.value.dataTableRef"
          @selection-change="handleSelectionChange"
          show-overflow-tooltip
          :border="true"
          :data="state.tableData1"
          v-loading="state.loading1"
        >
          <el-table-column type="selection" width="65"></el-table-column>
          <el-table-column label="序号" type="index" width="60"></el-table-column>
          <el-table-column v-for="item in tableConfig.tableItem" :key="item.name" :label="item.label" :prop="item.name"></el-table-column>
        </el-table> -->
        <TableComponent
          ref="tableComponentRef1"
          :loading="state.loading1"
          :tableConfig="tableConfig"
          :tableData="state.tableData1"
          rowKey="id"
          @handleSelectionChange="handleSelectChange1"
        />
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="onCloseDispatchDialogVisible">取 消</el-button>
          <el-button size="small" type="primary" @click="handleOkDispatch">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="state.cashLoanDialogVisible" title="借支明细" width="500px" @close="closeCashLoanDialog">
      <el-scrollbar max-height="200px" v-loading="state.cashLoadIdSetLoading">
        <el-checkbox-group size="default" v-model="state.statistics.cashLoadIdSet" v-if="state.cashLoanList.length > 0">
          <div v-for="item in state.cashLoanList" :key="item.value">
            <el-checkbox :label="item.label" :value="item.value"></el-checkbox>
          </div>
        </el-checkbox-group>
        <div v-else class="flex items-center justify-center h-full">暂无借支明细</div>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeCashLoanDialog">取消</el-button>
          <el-button type="primary" size="small" @click="confirmCashLoan">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 补贴 -->
    <el-dialog v-model="state.subsidyVisible" title="奖惩明细" width="400px" @close="closeCashLoanDialog">
      <el-scrollbar max-height="200px" v-loading="state.cashLoadIdSetLoading">
        <el-input v-model="state.content" style="width: 100%" placeholder="请输入奖惩原因，VIN搜索" @change="inputBlur" v-if="state.subsidyList.length > 0" />
        <div v-if="state.allowanceType == '补贴'">
          <el-checkbox-group size="default" v-model="state.statistics.carrierSubsidyIdSet" v-if="state.subsidyList.length > 0">
            <div v-for="item in state.subsidyList" :key="item.value">
              <el-checkbox :label="item.label" :value="item.value"></el-checkbox>
            </div>
          </el-checkbox-group>
          <div v-else class="flex items-center justify-center h-full">暂无奖惩明细</div>
        </div>
        <div v-if="state.allowanceType == '扣款'">
          <el-checkbox-group size="default" v-model="state.statistics.carrierDeductionIdSet" v-if="state.subsidyList.length > 0">
            <div v-for="item in state.subsidyList" :key="item.value">
              <el-checkbox :label="item.label" :value="item.value"></el-checkbox>
            </div>
          </el-checkbox-group>
          <div v-else class="flex items-center justify-center h-full">暂无奖惩明细</div>
        </div>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeCashLoanDialoged">取消</el-button>
          <el-button type="primary" size="small" @click="confirmCashLoaned">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import {
    getFleetCarrierSelectOptionApi,
    getFleetOrderCarrierCostComputeSummaryApi,
    postFleetOrderCarrierCostSummaryApi,
    putFleetOrderCarrierCostSummaryApi,
    getFleetOrderCarrierCostComputeSummaryDetailApi,
    getFleetOrderCarrierCostSummaryCashLoanSelectOptionApi,
    outFleetSettlementCarrierDeductionApi,
  } from '@/api/financialManagement'
  import { getcurrentUserMenuColumnlist } from '@/utils/common'
  import { FormInstance } from 'element-plus'
  import VINbatch from '@/components/TopQueryGroupComponent/components/VINbatch.vue'
  const showTextBatch = ref(false)
  const showShortVinBatch = ref(false)
  const vinBatchRef = ref()
  const shortVinBatchRef = ref()
  const currentBatchField = ref('')
  const currentShortVinBatchField = ref('')
  const currentBatchValue = ref('')
  const currentShortVinBatchValue = ref('')
  function openTextBatch(name: any) {
    currentBatchField.value = name
    currentBatchValue.value = state.searchParams[name] || '' // 保存当前值
    showTextBatch.value = true
  }
  function openShortVinBatch() {
    currentShortVinBatchField.value = 'shortVin'
    currentShortVinBatchValue.value = state.searchParams.shortVin || '' // 保存当前值
    showShortVinBatch.value = true
  }

  const handleArrayReceived = (array: any, targetField: { name: string | number }) => {
    state.searchParams[targetField.name] = Object.values(array).join(',')
    currentBatchValue.value = state.searchParams[targetField.name]
  }

  const emptyTextBatch = (fieldRef: any) => {
    if (fieldRef?.list && fieldRef.list.length > 0) {
      fieldRef.list = []
    }
  }
  const closeTextBatch = () => {
    showTextBatch.value = false
  }
  const closeShortVinBatch = () => {
    showShortVinBatch.value = false
  }
  interface TableItem {
    name: string
    label: string
    [key: string]: any // Allow additional properties
  }
  const emit = defineEmits(['confirmStatementSuccess'])
  const formRef = ref()
  const statisticsFromRef = ref()
  const tableComponentRef1 = ref()
  const tableComponentRef = ref()
  const spacer = h(ElDivider, { direction: 'vertical' })
  const state = reactive<any>({
    cashLoanDialogVisible: false,
    subsidyVisible: false,
    content: '',
    cashLoadIdSetLoading: false,
    cashLoanList: [],
    subsidyList: [],
    allowanceType: '',
    loading: false,
    loading1: false,
    dispatchDialogVisible: false,
    dialogVisible: {
      visible: false,
      title: '新增驾驶员结算单',
    },
    formData: {},
    searchParams: {
      carrierId: '',
      vin: '',
      shortVin: '',
      orderIssueDatetime: '',
      predictDeliveryDatetime: '',
      customerName: '',
      brandName: '',
      modelName: '',
      // settlementNo: '',
      qualityLoss: '-1',
      dealStatus: '-1',
      loadTime: '',
    },
    ids: [] as string[],
    id: '',
    isView: false, //是否是查看
    tableData: [],
    tableData1: [],
    carrierList: [] as SelectOptions[],
    statistics: {
      hedgingName: '现金',
      hedgingAmount: 0,
      cashAmount: 0,
      cashTaxedAmount: 0,
      wjyAmount: 0,
      wjyTaxedAmount: 0,
      dlAmount: 0,
      dlTaxedAmount: 0,
      acceptAmount: 0,
      acceptTaxedAmount: 0,
      transferAmount: 0,
      transferTaxedAmount: 0,
      cashLoadIdSet: [] as string[],
      deductionTypeName: '现金',
      carrierSubsidyTypeName: '现金', //补贴
      carrierDeductionTypeName: '现金', //扣款
      deductionAmount: 0, //应扣合计
      carrierSubsidyAmount: 0, //补贴合计
      carrierDeductionAmount: 0, //扣款合计
      carrierDeductionIdSet: [] as string[], //外协扣款idList
      carrierSubsidyIdSet: [] as string[], //外协补贴idList
    },
    originalCashAmount: 0,
    originalWjyAmount: 0,
    originalDlAmount: 0,
    originalAcceptAmount: 0,
    originalTransferAmount: 0,

    totalSummary: {},
    selectDispatchList: [],
    backupSubsidyIds: [] as string[], // 补贴ID备份
    backupDeductionIds: [] as string[], // 扣款ID备份
  })
  const typeOptions = [
    {
      label: '现金',
      value: '现金',
    },
    {
      label: '万金油',
      value: '万金油',
    },
    {
      label: '迪链',
      value: '迪链',
    },
    {
      label: '承兑',
      value: '承兑',
    },
    {
      label: '转账',
      value: '转账',
    },
  ]
  const tableConfig: { tableItem: { name: string; label: string }[] } = reactive({
    tableItem: [],
    showHandleSelection: true,
  })
  watch(
    () => state.dialogVisible.visible,
    async (val) => {
      if (val) {
        getFleetOrderCarrierCostComputeDriverSelectOption()
        // 动态设置菜单数据列
        const { children, meta }: { children: MenuVO[]; meta: MetaVO } = await getcurrentUserMenuColumnlist('99080502000000')
        tableConfig.tableItem = (meta.dataColumn || []) as { name: string; label: string }[]
      }
    },
  )
  // selectcarrierId 切换承运商时，清空 补贴与扣款下拉与选择的值
  const selectcarrierId = () => {
    state.statistics.deductionAmount = 0
    state.statistics.carrierSubsidyAmount = 0
    state.statistics.carrierDeductionAmount = 0
    state.statistics.carrierDeductionIdSet = []
    state.statistics.carrierSubsidyIdSet = []
    state.statistics.cashLoadIdSet = []
  }

  //添加vin
  const addVin = () => {
    state.dispatchDialogVisible = true
  }
  const onCloseDispatchDialogVisible = () => {
    state.tableData1 = []
    state.selectDispatchList = []
    state.searchParams.dispatchNo = ''
    state.dispatchDialogVisible = false
  }
  const getFleetOrderCarrierCostSummaryCashLoanSelectOption = async () => {
    if (!state.searchParams.carrierId) {
      ElMessage.warning('请选择承运商')
      return
    }
    state.cashLoanDialogVisible = true
    state.cashLoadIdSetLoading = true
    const params = {
      carrierId: state.searchParams.carrierId,
      id: state.id,
    }
    try {
      const { data } = await getFleetOrderCarrierCostSummaryCashLoanSelectOptionApi(params)
      state.cashLoanList = data
    } catch (e) {
      state.cashLoadIdSetLoading = false
    } finally {
      state.cashLoadIdSetLoading = false
    }
  }

  // 补贴选择
  const getFleetOrderCarrierCostSummaryCashLoanSelectOptioned = async (type: String) => {
    if (!state.searchParams.carrierId) {
      ElMessage.warning('请选择承运商')
      return
    }
    state.backupSubsidyIds = [...state.statistics.carrierSubsidyIdSet]
    state.backupDeductionIds = [...state.statistics.carrierDeductionIdSet]

    state.cashLoadIdSetLoading = true
    state.subsidyVisible = true
    state.allowanceType = type
    const params = {
      content: state.content,
      type: type,
      carrierId: state.searchParams.carrierId,
      costSummaryId: state.id ? state.id : '',
    }
    try {
      const { data } = await outFleetSettlementCarrierDeductionApi(params)
      state.subsidyList = data
    } catch (e) {
      state.cashLoadIdSetLoading = false
    } finally {
      state.cashLoadIdSetLoading = false
    }
  }

  const inputBlur = async (value: String) => {
    const params = {
      content: state.content,
      type: state.allowanceType,
      carrierId: state.searchParams.carrierId,
      costSummaryId: state.id ? state.id : '',
    }
    const { data } = await outFleetSettlementCarrierDeductionApi(params)
    state.subsidyList = data
  }

  const handleOkDispatch = () => {
    //如果state.selectDispatchList中的id在state.tableData中不存在，则将state.selectDispatchList合并到state.tableData中,
    state.tableData.push(...state.selectDispatchList.filter((item: any) => !state.tableData.some((tableItem: any) => tableItem.id === item.id)))
    //将state.tableData中的所有项全选
    state.tableData.forEach((item: any) => {
      setTimeout(() => {
        tableComponentRef.value.dataTableRef.toggleRowSelection(item, true)
      })
    })
    handleSelectChange(state.tableData)
    onCloseDispatchDialogVisible()
  }
  //获取结算单详情
  const getFleetOrderCarrierCostComputeSummaryDetail = async (row: { id: string }) => {
    state.loading = true
    const { data } = await getFleetOrderCarrierCostComputeSummaryDetailApi({ id: row.id })
    state.id = row.id
    for (const key in data) {
      state.statistics[key] = data[key]
    }
    handleHedgingNameChange(data.hedgingName)
    state.searchParams.carrierId = data.carrierId
    state.tableData = data.list
    state.ids = data.list.map((item: any) => item.id)
    data.list.forEach((item: any) => {
      setTimeout(() => {
        tableComponentRef.value.dataTableRef.toggleRowSelection(item, true)
      })
    })
    handleSelectChange(state.tableData)
    state.loading = false
  }
  //获取承运商下拉
  const getFleetOrderCarrierCostComputeDriverSelectOption = async () => {
    const { data } = await getFleetCarrierSelectOptionApi({})
    state.carrierList = data
  }
  const closeDialog = () => {
    //清空表单
    state.ids = []
    state.totalSummary = {}
    state.tableData = []
    state.statistics = {
      hedgingName: '现金',
      hedgingAmount: 0,
      cashAmount: 0,
      cashTaxedAmount: 0,
      wjyAmount: 0,
      wjyTaxedAmount: 0,
      dlAmount: 0,
      dlTaxedAmount: 0,
      acceptAmount: 0,
      acceptTaxedAmount: 0,
      transferAmount: 0,
      transferTaxedAmount: 0,
      cashLoadIdSet: [] as string[],
      deductionTypeName: '现金',
      deductionAmount: 0, //应扣合计
      carrierSubsidyTypeName: '现金', //补贴
      carrierDeductionTypeName: '现金', //扣款
      carrierSubsidyAmount: 0, //补贴合计
      carrierDeductionAmount: 0, //扣款合计
      carrierDeductionIdSet: [], //外协扣款idList
      carrierSubsidyIdSet: [], //外协补贴idList
    }
    tableComponentRef.value.dataTableRef.clearSelection()
    state.formData.id = undefined
    formRef.value.resetFields()
    statisticsFromRef.value.resetFields()
    state.dialogVisible.visible = false
  }
  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
    // search(formRef.value, 'search')
  }
  const search = async (formEl: FormInstance | undefined, type: string) => {
    if (!formEl) return
    await formEl.validate(async (valid) => {
      if (valid) {
        const params = {
          carrierId: state.searchParams.carrierId,
          vin: state.searchParams.vin,
          shortVin: state.searchParams.shortVin,
          startOrderIssueDatetime: state.searchParams.orderIssueDatetime ? state.searchParams.orderIssueDatetime[0] : undefined,
          endOrderIssueDatetime: state.searchParams.orderIssueDatetime ? state.searchParams.orderIssueDatetime[1] : undefined,
          startPredictDeliveryDatetime: state.searchParams.predictDeliveryDatetime ? state.searchParams.predictDeliveryDatetime[0] : undefined,
          endPredictDeliveryDatetime: state.searchParams.predictDeliveryDatetime ? state.searchParams.predictDeliveryDatetime[1] : undefined,
          startLoadTime: state.searchParams.loadTime ? state.searchParams.loadTime[0] : undefined,
          endLoadTime: state.searchParams.loadTime ? state.searchParams.loadTime[1] : undefined,
          customerName: state.searchParams.customerName,
          brandName: state.searchParams.brandName,
          modelName: state.searchParams.modelName,
          hedgingName: state.statistics.hedgingName,
          hedgingAmount: state.statistics.hedgingAmount,
          // settlementNo: state.searchParams.settlementNo,
          id: type === 'resetCashLoan' ? state.ids.join(',') : undefined,
          qualityLoss: state.searchParams.qualityLoss === '-1' ? '' : state.searchParams.qualityLoss,
          dealStatus: state.searchParams.dealStatus === '-1' ? '' : state.searchParams.dealStatus,
          carrierCostSummaryId: state.id ? state.id : undefined,
          deductionAmount: state.statistics.deductionAmount,
          deductionTypeName: state.statistics.deductionTypeName,
          carrierSubsidyAmount: state.statistics.carrierSubsidyAmount,
          carrierSubsidyTypeName: state.statistics.carrierSubsidyTypeName,
          carrierDeductionAmount: state.statistics.carrierDeductionAmount,
          carrierDeductionTypeName: state.statistics.carrierDeductionTypeName,
        }
        try {
          state.loading = true
          const { data } = await getFleetOrderCarrierCostComputeSummaryApi(params)
          const { rows, statistics, totalSummary } = data
          state.tableData = rows
          state.ids = rows.map((item: any) => item.id)
          for (const key in statistics) {
            state.statistics[key] = statistics[key]
          }
          state.originalCashAmount = state.statistics.cashAmount
          state.originalWjyAmount = state.statistics.wjyAmount
          state.originalDlAmount = state.statistics.dlAmount
          state.originalAcceptAmount = state.statistics.acceptAmount
          state.originalTransferAmount = state.statistics.transferAmount
          state.totalSummary = totalSummary
          state.tableData.forEach((item: any) => {
            setTimeout(() => {
              tableComponentRef.value.dataTableRef.toggleRowSelection(item, true)
            })
          })
          state.loading = false
        } catch (e) {
          state.loading = false
        }
      } else {
      }
    })
  }

  const search1 = async () => {
    const params = {
      carrierId: state.searchParams.carrierId,
      vin: state.searchParams.vin,
      shortVin: state.searchParams.shortVin,
      carrierCostSummaryId: state.id ? state.id : undefined,
      deductionAmount: state.statistics.deductionAmount,
      deductionTypeName: state.statistics.deductionTypeName,
    }
    const { data } = await getFleetOrderCarrierCostComputeSummaryApi(params)
    const { rows, statistics, totalSummary } = data
    state.tableData1 = rows
    state.ids = rows.map((item: any) => item.id)
    // for (const key in statistics) {
    //   state.statistics[key] = statistics[key]
    // }
    // state.totalSummary = totalSummary
    state.tableData1.forEach((item: any) => {
      setTimeout(() => {
        tableComponentRef1.value.dataTableRef.toggleRowSelection(item, true)
      })
    })
  }
  const currentSelectRow = ref<any>([])
  const handleSelectChange = async (val: any) => {
    currentSelectRow.value = val
    const ids = val.map((item: any) => item.id)
    state.ids = ids
    if (ids.length === 0) {
      for (const key in state.statistics) {
        if (
          key === 'hedgingName' ||
          key === 'hedgingAmount' ||
          key === 'deductionTypeName' ||
          key === 'cashLoadIdSet' ||
          key === 'chargeAmount' ||
          key === 'deductionAmount' ||
          key === 'carrierSubsidyAmount' ||
          key === 'carrierDeductionAmount' ||
          key === 'carrierDeductionIdSet' ||
          key === 'carrierSubsidyIdSet' ||
          key === 'carrierSubsidyTypeName' ||
          key === 'carrierDeductionTypeName'
        ) {
          continue
        }
        state.statistics[key] = 0
      }
      return
    }
    const params = {
      carrierId: state.searchParams.carrierId,
      // vin: state.searchParams.vin,
      // shortVin: state.searchParams.shortVin,
      startOrderIssueDatetime: state.searchParams.orderIssueDatetime[0],
      endOrderIssueDatetime: state.searchParams.orderIssueDatetime[1],
      startPredictDeliveryDatetime: state.searchParams.predictDeliveryDatetime[0],
      endPredictDeliveryDatetime: state.searchParams.predictDeliveryDatetime[1],
      hedgingAmount: state.statistics.hedgingAmount,
      hedgingName: state.statistics.hedgingName,
      id: ids.join(','),
      carrierCostSummaryId: state.id ? state.id : undefined,
      deductionAmount: state.statistics.deductionAmount,
      deductionTypeName: state.statistics.deductionTypeName,
      carrierSubsidyAmount: state.statistics.carrierSubsidyAmount,
      carrierSubsidyTypeName: state.statistics.carrierSubsidyTypeName,
      carrierDeductionAmount: state.statistics.carrierDeductionAmount,
      carrierDeductionTypeName: state.statistics.carrierDeductionTypeName,
    }
    const { data } = await getFleetOrderCarrierCostComputeSummaryApi(params)
    state.ids = ids
    for (const key in data.statistics) {
      state.statistics[key] = data.statistics[key]
    }
  }
  const handleSelectChange1 = (val: any) => {
    state.selectDispatchList = val
  }
  const handleSelectionChange = (val: any) => {
    state.selectDispatchList = val
  }
  const submitForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate(async (valid) => {
      if (valid) {
        try {
          if (state.id) {
            const params = {
              hedgingAmount: state.statistics.hedgingAmount,
              hedgingName: state.statistics.hedgingName,
              carrierId: state.searchParams.carrierId,
              computeIdSet: state.ids,
              id: state.id,
              deductionTypeName: state.statistics.deductionTypeName,
              cashLoadIdSet: state.statistics.cashLoadIdSet,
              deductionAmount: state.statistics.deductionAmount,
              carrierDeductionIdSet: state.statistics.carrierDeductionIdSet, //外协扣款idList
              carrierSubsidyIdSet: state.statistics.carrierSubsidyIdSet, //外协补贴idList
              carrierSubsidyAmount: state.statistics.carrierSubsidyAmount,
              carrierSubsidyTypeName: state.statistics.carrierSubsidyTypeName,
              carrierDeductionAmount: state.statistics.carrierDeductionAmount,
              carrierDeductionTypeName: state.statistics.carrierDeductionTypeName,
            }

            await putFleetOrderCarrierCostSummaryApi(params)
            state.id = ''
            ElMessage.success('修改成功')
          } else {
            const params = {
              hedgingAmount: state.statistics.hedgingAmount,
              hedgingName: state.statistics.hedgingName,
              carrierId: state.searchParams.carrierId,
              computeIdSet: state.ids,
              deductionTypeName: state.statistics.deductionTypeName,
              cashLoadIdSet: state.statistics.cashLoadIdSet,
              deductionAmount: state.statistics.deductionAmount,
              carrierDeductionIdSet: state.statistics.carrierDeductionIdSet, //外协扣款idList
              carrierSubsidyIdSet: state.statistics.carrierSubsidyIdSet, //外协补贴idList
              carrierSubsidyAmount: state.statistics.carrierSubsidyAmount,
              carrierSubsidyTypeName: state.statistics.carrierSubsidyTypeName,
              carrierDeductionAmount: state.statistics.carrierDeductionAmount,
              carrierDeductionTypeName: state.statistics.carrierDeductionTypeName,
            }

            await postFleetOrderCarrierCostSummaryApi(params)
            ElMessage.success('生成成功')
          }
          closeDialog()
          emit('confirmStatementSuccess')
        } catch (e) {}
      } else {
      }
    })
  }
  const handleHedgingAmountChange = (val: any) => {
    switch (state.statistics.hedgingName) {
      case '现金':
        state.statistics.cashAmount = (Number(state.originalCashAmount) - Number(val)).toFixed(2)
        break
      case '万金油':
        state.statistics.wjyAmount = (Number(state.originalWjyAmount) - Number(val)).toFixed(2)
        break
      case '迪链':
        state.statistics.dlAmount = (Number(state.originalDlAmount) - Number(val)).toFixed(2)
        break
      case '承兑':
        state.statistics.acceptAmount = (Number(state.originalAcceptAmount) - Number(val)).toFixed(2)
        break
      case '转账':
        state.statistics.transferAmount = (Number(state.originalTransferAmount) - Number(val)).toFixed(2)
        break
    }
  }
  const cashTotalAmount = computed(() => {
    return (Number(state.statistics.cashAmount || 0) + Number(state.statistics.cashTaxedAmount || 0)).toFixed(2) || 0
  })
  const wjyTotalAmount = computed(() => {
    return (Number(state.statistics.wjyAmount || 0) + Number(state.statistics.wjyTaxedAmount || 0)).toFixed(2) || 0
  })
  const dlTotalAmount = computed(() => {
    return (Number(state.statistics.dlAmount || 0) + Number(state.statistics.dlTaxedAmount || 0)).toFixed(2) || 0
  })
  const acceptTotalAmount = computed(() => {
    return (Number(state.statistics.acceptAmount || 0) + Number(state.statistics.acceptTaxedAmount || 0)).toFixed(2) || 0
  })
  const transferTotalAmount = computed(() => {
    return (Number(state.statistics.transferAmount || 0) + Number(state.statistics.transferTaxedAmount || 0)).toFixed(2) || 0
  })
  const handleHedgingNameChange = (val: any) => {
    handleHedgingAmountChange(state.statistics.hedgingAmount)
    //重置其他金额
    switch (val) {
      case '现金':
        state.statistics.wjyAmount = state.originalWjyAmount
        state.statistics.dlAmount = state.originalDlAmount
        state.statistics.acceptAmount = state.originalAcceptAmount
        state.statistics.transferAmount = state.originalTransferAmount
        break
      case '万金油':
        state.statistics.cashAmount = state.originalCashAmount
        state.statistics.dlAmount = state.originalDlAmount
        state.statistics.acceptAmount = state.originalAcceptAmount
        state.statistics.transferAmount = state.originalTransferAmount
        break
      case '迪链':
        state.statistics.cashAmount = state.originalCashAmount
        state.statistics.wjyAmount = state.originalWjyAmount
        state.statistics.acceptAmount = state.originalAcceptAmount
        state.statistics.transferAmount = state.originalTransferAmount
        break
      case '承兑':
        state.statistics.cashAmount = state.originalCashAmount
        state.statistics.wjyAmount = state.originalWjyAmount
        state.statistics.dlAmount = state.originalDlAmount
        state.statistics.transferAmount = state.originalTransferAmount
        break
      case '转账':
        state.statistics.cashAmount = state.originalCashAmount
        state.statistics.wjyAmount = state.originalWjyAmount
        state.statistics.dlAmount = state.originalDlAmount
        state.statistics.acceptAmount = state.originalAcceptAmount
        break
    }
  }
  const closeCashLoanDialog = () => {
    state.cashLoadIdSetLoading = false
    state.cashLoanDialogVisible = false
    state.subsidyVisible = false
    state.cashLoanList = []
    state.subsidyList = []
  }
  const closeCashLoanDialoged = () => {
    // 根据类型恢复对应字段
    if (state.allowanceType === '补贴') {
      state.statistics.carrierSubsidyIdSet = [...state.backupSubsidyIds]
    } else if (state.allowanceType === '扣款') {
      state.statistics.carrierDeductionIdSet = [...state.backupDeductionIds]
    }
    // 清空备份
    state.backupSubsidyIds = []
    state.backupDeductionIds = []
    state.cashLoadIdSetLoading = false
    state.cashLoanDialogVisible = false
    state.subsidyVisible = false
    state.cashLoanList = []
    state.subsidyList = []
  }
  const confirmCashLoan = () => {
    const selectCashLoanList = state.cashLoanList.filter((item: any) => state.statistics.cashLoadIdSet.includes(item.value))
    state.statistics.deductionAmount = selectCashLoanList.reduce((total: number, item: any) => total + Number(item.desc), 0)
    closeCashLoanDialog()
  }
  // 补贴确定
  const confirmCashLoaned = () => {
    if (state.allowanceType == '补贴') {
      const selectCashLoanList = state.subsidyList.filter((item: any) => state.statistics.carrierSubsidyIdSet.includes(item.value))
      state.statistics.carrierSubsidyAmount = selectCashLoanList.reduce((total: number, item: any) => total + Number(item.desc), 0)
    } else {
      const selectCashLoanList = state.subsidyList.filter((item: any) => state.statistics.carrierDeductionIdSet.includes(item.value))
      state.statistics.carrierDeductionAmount = selectCashLoanList.reduce((total: number, item: any) => total + Number(item.desc), 0)
    }
    // 查找对应的数据
    const result = state.ids
      .map((id: any) => {
        return state.tableData.find((item: any) => item.id === id)
      })
      .filter((item: any) => item !== undefined) // 过滤掉未找到的项
    closeCashLoanDialog()
    handleSelectChange(result)
  }
  const transformtion = () => {
    handleSelectChange(currentSelectRow.value)
  }
  defineExpose({
    state,
    tableConfig,
    getFleetOrderCarrierCostComputeSummaryDetail,
  })
</script>
<style scoped lang="scss">
  .inputDeep {
    width: 100%;
    background: #f0f0f0;
    border: 1px solid #dbdbdb;
    display: flex;
    align-items: center;
    border-radius: 2px;
    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;
      border-radius: 0;
      cursor: default;
      .el-input__inner {
        cursor: default !important;
      }
    }
    :deep(.el-textarea__inner) {
      box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;
      border-radius: 0;
      cursor: default;
    }
    :deep(.el-select__wrapper) {
      box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;
      border-radius: 0;
    }
  }
</style>
