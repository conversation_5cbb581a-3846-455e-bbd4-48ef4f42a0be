<template>
  <el-dialog :close-on-click-modal="false" :title="dialog.title" v-model="dialog.visible" width="836px" :before-close="handleClose">
    <div class="images">
      <img src="@/assets/bian_zu.png" alt="" />
    </div>
    <div class="bottom-list">
      <div class="div-item">
        <div class="text">本次待导入：</div>
        <div class="data-list">{{ state.data.totalCount }}条</div>
      </div>
      <div class="div-item">
        <div class="text">校验通过：</div>
        <div class="data-list">{{ state.data.successCount }}条</div>
      </div>
      <div class="div-item">
        <div class="text">校验失败：</div>
        <div class="data-list">{{ state.data.failCount }}条</div>
      </div>
      <div class="div-item">
        <div class="text">下载文件：</div>
        <el-text class="mx-1 cursor-pointer" type="primary" @click="excelClickDown">{{ state.data.url }}</el-text>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
  const props = defineProps({
    dialog: {
      type: Object as PropType<DialogOption>,
      default: () => ({
        visible: false,
        title: '导入数据校验反馈',
      }),
    },
  })
  const state = reactive({
    data: {
      successCount: 0,
      failCount: 0,
      totalCount: 0,
      successMessage: '',
      failMessage: '',
      totalMessage: '',
      url: '',
    },
  })
  // fileDownload
  const excelClickDown = () => {
    window.open(state.data.url)
  }
  const handleClose = (done: () => void) => {
    done()
  }

  defineExpose({
    state,
  })
</script>
<style scoped>
  /* 如果使用全局CSS */
  .cursor-pointer {
    cursor: pointer;
  }
  /* 悬停下划线（可选） */
  .cursor-pointer:hover {
    text-decoration: underline;
  }
  .images {
    width: 100%;
    height: 204px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .bottom-list {
    width: 785px;
    height: 185px;
    background: #f2f6fa;
    border-radius: 15px;
    margin: auto;
    padding: 20px;
    display: flex;
    flex-wrap: wrap;
  }
  .div-item {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .text {
    width: 140px;
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 400;
    color: #999999;
    line-height: 30px;
    text-align: right;
    font-style: normal;
  }
  .data-list {
    width: 110px;
    background: #ebf6ff;
    border-radius: 8px;
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 400;
    color: #1b94fa;
    line-height: 33px;
    text-align: left;
    font-style: normal;
    text-align: center;
  }
</style>
