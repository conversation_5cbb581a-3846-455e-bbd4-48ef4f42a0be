<!--
 * @Author: llm
 * @Date: 2025-03-13 12:26:30
 * @LastEditors: llm
 * @LastEditTime: 2025-06-23 10:34:26
 * @Description: 点检标准
-->
<template>
  <div>
    <el-dialog
      v-model="state.dialogVisible.visible"
      :title="state.dialogVisible.title"
      width="80%"
      :draggable="true"
      :close-on-click-modal="false"
      @close="closeDialog"
    >
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form ref="formRef" :model="state.formData" :rules="rules" label-width="100px" :inline="true">
          <el-form-item label="标准名称" prop="standardName">
            <el-input v-model="state.formData.standardName" placeholder="请输入标准名称"></el-input>
          </el-form-item>
          <el-form-item label="点检对象" prop="checkObject" class="w-[260px]">
            <el-select v-model="state.formData.checkObject" @change="handleCheckObjectChange">
              <el-option :label="item.label" :value="item.value" v-for="(item, index) in distributeOptions" :key="index"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="点检节点" prop="checkNodeList" class="w-[260px]">
            <el-select v-model="state.formData.checkNodeList" multiple>
              <el-option :label="item.label" :value="item.value" v-for="(item, index) in spotCheckNodeOptions" :key="index"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="state.formData.remark" placeholder="请输入备注"></el-input>
          </el-form-item>
          <el-divider border-style="dashed">设置点检标准</el-divider>
          <el-table :data="state.formData.cfgs">
            <el-table-column label="序号" type="index" width="55" align="center" />
            <el-table-column width="90px">
              <template #default="scope">
                <div class="flex justify-center items-center">
                  <el-button type="primary" icon="Plus" size="small" circle @click="addTableItem" v-if="scope.$index === state.formData.cfgs.length - 1" />
                  <el-button
                    type="danger"
                    icon="Delete"
                    size="small"
                    circle
                    @click="deleteTableItem(scope.row, scope.$index)"
                    v-if="state.formData.cfgs.length > 1"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="checkItem"
              label="检查项目"
              width="180"
              align="center"
              :rules="{ required: true, message: '请输入检查项目', trigger: 'blur' }"
            >
              <template #default="scope">
                <el-input v-model="scope.row.checkItem" placeholder="请输入检查项目"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="content" label="检查内容" width="160" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.content" :key="index" class="p-[5px_0]">
                  <el-input v-model="item.checkContent" placeholder="请输入检查内容"></el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="needMultimedia" label="需要视频/图片" width="180" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.content" :key="index" class="p-[5px_0] w-[100%]">
                  <el-select v-model="item.needMultimedia" placeholder="请选择">
                    <el-option :label="item.label" :value="item.value" v-for="(item, index) in commonDistributeOptions" :key="index"></el-option>
                  </el-select>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="sampleImageList" label="示例图片" align="center">
              <template #default="scope">
                <template v-for="(item, index) in scope.row.content" :key="index" class="p-[5px_0]">
                  <template v-if="item.needMultimedia === '1'">
                    <div v-if="item.sampleImageList && item.sampleImageList.length > 1">
                      <el-link type="primary" @click="showPic(item, 'sampleImageList')">查看</el-link>
                    </div>
                    <template v-else>
                      <el-image
                        style="width: 50px; height: 50px"
                        v-for="(_item, _index) in item.sampleImageList ?? []"
                        :key="_index"
                        :src="_item.url"
                        :zoom-rate="1.2"
                        :max-scale="7"
                        :min-scale="0.2"
                        :preview-teleported="true"
                        :preview-src-list="item.sampleImageList.map((_item: any) => _item.url) ?? []"
                        fit="cover"
                      />
                    </template>
                  </template>
                  <div v-else>-</div>
                </template>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.content" :key="index" class="p-[5px_0] h-42px flex justify-center items-center">
                  <el-button
                    type="primary"
                    icon="Upload"
                    circle
                    size="small"
                    @click="uploadImageItem(scope.row, scope.$index, index)"
                    v-if="item.needMultimedia === '1'"
                  />
                  <el-button
                    type="danger"
                    icon="Delete"
                    circle
                    size="small"
                    @click="deleteSubItem(scope.row, scope.$index, index)"
                    v-if="state.formData.cfgs[scope.$index].content.length > 1"
                  />
                  <el-button
                    type="primary"
                    icon="Plus"
                    circle
                    size="small"
                    @click="addSubItem(scope.row, scope.$index, index)"
                    v-if="index === state.formData.cfgs[scope.$index].content.length - 1"
                  />
                  <el-button type="primary" icon="Top" circle size="small" @click="upSubItem(scope.row, scope.$index, index)" v-if="index > 0" />
                  <el-button
                    type="primary"
                    icon="Bottom"
                    circle
                    size="small"
                    @click="downSubItem(scope.row, scope.$index, index)"
                    v-if="index < state.formData.cfgs[scope.$index].content.length - 1"
                  />
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog()">取 消</el-button>
          <el-button type="primary" v-loading="submitLoading" @click="handleSubmit(formRef)">保 存</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 查看照片 -->
    <PicDialogComponent ref="picDialogComponent" :image-list="imageList" />
    <el-dialog v-model="state.uploadImageVisible.visible" :title="state.uploadImageVisible.title" width="600px" @closed="closeUploadImageDialog">
      <UploadImageComponent ref="uploadImageRef" :limit="2" :multiple="true" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeUploadImageDialog()">取消</el-button>
          <el-button type="primary" @click="submitUploadImage">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { ExamineTemplateVO, MajorItemList } from '@/api/supplierManagement/type'
  import PicDialogComponent from '@/components/PicDialogComponent/index.vue'
  import UploadImageComponent from '@/components/UploadImageComponent/index.vue'
  import { examineTemplateDetailApi } from '@/api/supplierManagement'
  import type { FormInstance, FormRules, UploadUserFile } from 'element-plus'
  import { InspectionConfigVO } from '@/api/systemConfig/types'
  import {
    getDistributeSelectOptionsApi,
    getSpotCheckConfigurationDetailApi,
    getSpotCheckNodeOptionsApi,
    postSpotCheckConfigurationApi,
    putSpotCheckConfigurationApi,
  } from '@/api/systemConfig'
  import { debounce } from 'lodash'
  const uploadImageRef = ref()
  const emit = defineEmits(['refresh'])
  const submitLoading = ref(false)
  const formRef = ref()
  const rules = {
    standardName: [{ required: true, message: '请输入标准名称', trigger: 'blur' }],
    checkObject: [{ required: true, message: '请选择点检对象', trigger: 'change' }],
    checkNodeList: [{ required: true, message: '请选择点检节点', trigger: 'change' }],
  }
  const state = reactive({
    origin: 'add', //add:新增,edit:编辑,copy:复制
    dialogVisible: {
      visible: false,
      title: '',
    },
    uploadImageVisible: {
      visible: false,
      title: '上传示例图片',
    },
    formData: {
      id: undefined, //id
      standardName: '', //标准名称
      checkObject: '', //点检对象
      remark: '', //备注
      checkNodeList: [], //点检节点
      cfgs: [
        {
          checkItem: '', //检查项目
          content: [
            {
              checkContent: '', //检查内容
              needMultimedia: '0', //需要视频/图片
              sampleImageList: [], //示例图片
            },
          ],
        },
      ], //设置点检标准
    } as InspectionConfigVO,
    currentRowIndex: 0, //行索引
    currentChildRowIndex: 0, //子行索引
  })
  watch(
    () => state.dialogVisible.visible,
    (val) => {
      if (val) {
        getDistributeSelectOptions('spotCheckObject')
        getDistributeSelectOptions('multimediaType')
      }
    },
  )
  const distributeOptions = ref<OptionType[]>([])
  const spotCheckNodeOptions = ref<OptionType[]>([])
  const commonDistributeOptions = ref<OptionType[]>([])
  //查看模板详情
  const examineTemplateDetail = async (row: ExamineTemplateVO) => {
    const { data } = await examineTemplateDetailApi({ id: row.id })
    //如果state.formData与data存在相同key则覆盖
    Object.assign(state.formData, data)
  }
  const getDistributeSelectOptions = async (selectType: string) => {
    const { data } = await getDistributeSelectOptionsApi({ selectType })
    if (selectType === 'spotCheckObject') {
      distributeOptions.value = data as OptionType[]
    } else if (selectType === 'multimediaType') {
      commonDistributeOptions.value = data as OptionType[]
    }
  }
  const handleCheckObjectChange = (val: string) => {
    //清空点检节点
    state.formData.checkNodeList = []
    getSpotCheckNodeOptions(val)
  }
  const getSpotCheckNodeOptions = async (checkObject: string) => {
    const { data } = await getSpotCheckNodeOptionsApi({ checkObject })
    spotCheckNodeOptions.value = data as OptionType[]
  }
  const handleSubmit = debounce(async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        submitLoading.value = true
        try {
          //如果
          if (state.formData.id) {
            await putSpotCheckConfigurationApi(state.formData)
              .then(() => {
                submitLoading.value = false
                state.dialogVisible.visible = false
                ElMessage.success('操作成功')
                closeDialog()
                emit('refresh')
              })
              .catch((error: any) => {
                submitLoading.value = false
              })
          } else {
            await postSpotCheckConfigurationApi(state.formData)
              .then(() => {
                submitLoading.value = false
                state.dialogVisible.visible = false
                ElMessage.success('操作成功')
                closeDialog()
                emit('refresh')
              })
              .catch((error: any) => {
                submitLoading.value = false
              })
          }
        } catch {
          submitLoading.value = false
        }
      } else {
      }
    })
  }, 1000)
  const getDetail = async (id: string) => {
    const { data } = await getSpotCheckConfigurationDetailApi(id)
    state.formData = data as InspectionConfigVO
    if (state.origin === 'copy') {
      state.formData.standardName = ''
      state.formData.checkObject = ''
      state.formData.checkNodeList = []
      state.formData.remark = ''
      state.formData.id = undefined
    }
  }
  const closeDialog = () => {
    //清空表单数据
    state.formData.standardName = ''
    state.formData.checkObject = ''
    state.formData.remark = ''
    state.formData.checkNodeList = []
    state.formData.cfgs = [
      {
        checkItem: '',
        content: [
          {
            checkContent: '',
            needMultimedia: '0',
            sampleImageList: [],
          },
        ],
      },
    ]
    formRef.value.resetFields()
    state.dialogVisible.visible = false
  }
  /**
   * 新增考核项
   */
  const addTableItem = () => {
    state.formData.cfgs.push({
      checkItem: '',
      content: [
        {
          checkContent: '',
          needMultimedia: '0',
          sampleImageList: [],
        },
      ],
    })
  }
  /**
   * 删除项
   */
  const deleteTableItem = (row: any, index: number) => {
    state.formData.cfgs.splice(index, 1)
  }
  /**
   * 新增子项
   */
  const addSubItem = (row: any, rowIndex: number, index: number) => {
    state.formData.cfgs[rowIndex].content.push({
      checkContent: '',
      needMultimedia: '0',
      sampleImageList: [],
    })
  }
  /**
   * 删除子项
   */
  const deleteSubItem = (row: any, rowIndex: number, index: number) => {
    state.formData.cfgs[rowIndex].content.splice(index, 1)
  }
  /**
   * 上传图片
   */
  const uploadImageItem = (row: any, rowIndex: number, index: number) => {
    state.currentRowIndex = rowIndex
    state.currentChildRowIndex = index
    state.uploadImageVisible.visible = true
    if (row.content[index].sampleImageList) {
      setUploadImageList(row.content[index].sampleImageList)
    }
  }
  // 获取上传图片列表
  const getUploadImageList = () => {
    return uploadImageRef.value.uploadImageList
  }

  //设置上传图片列表
  const setUploadImageList = (list: UploadUserFile[]) => {
    uploadImageRef.value.uploadImageList = list
  }
  const closeUploadImageDialog = () => {
    state.uploadImageVisible.visible = false
    setUploadImageList([])
  }
  const submitUploadImage = () => {
    state.formData.cfgs[state.currentRowIndex].content[state.currentChildRowIndex].sampleImageList = getUploadImageList()
    closeUploadImageDialog()
  }
  /**
   * 上移子项
   */
  const upSubItem = (row: any, rowIndex: number, subItemIndex: number) => {
    if (subItemIndex > 0) {
      let temp = state.formData.cfgs[rowIndex].content[subItemIndex]
      state.formData.cfgs[rowIndex].content[subItemIndex] = state.formData.cfgs[rowIndex].content[subItemIndex - 1]
      state.formData.cfgs[rowIndex].content[subItemIndex - 1] = temp
    }
  }
  /**
   * 下移子项
   */
  const downSubItem = (row: any, rowIndex: number, index: number) => {
    if (index < state.formData.cfgs[rowIndex].content.length - 1) {
      let temp = state.formData.cfgs[rowIndex].content[index]
      state.formData.cfgs[rowIndex].content[index] = state.formData.cfgs[rowIndex].content[index + 1]
      state.formData.cfgs[rowIndex].content[index + 1] = temp
    }
  }
  const imageList = ref([])
  const picDialogComponent = ref()
  const showPic = (row: any, name: string) => {
    if (row[name]) {
      imageList.value = row[name] ? row[name].map((item: any) => item.url) : []
      picDialogComponent.value.picDialogVisible = true
    } else {
      ElMessage.warning('当前未上传证件')
    }
  }
  defineExpose({
    examineTemplateDetail,
    state,
    getDetail,
  })
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    border-top-color: #e89e42;
  }

  :deep(.el-divider__text) {
    color: #e89e42;
  }
  :deep(.el-table .cell) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
</style>
