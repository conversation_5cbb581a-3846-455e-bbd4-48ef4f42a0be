<!--
 * @Author: llm
 * @Date: 2024-01-15 15:57:17
 * @LastEditors: llm
 * @LastEditTime: 2024-09-25 15:39:14
 * @Description: 公路发运-运单申请-车道预约
-->
<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="60%" :draggable="true" :close-on-click-modal="false" @close="cancel(ruleFormRef)">
    <el-scrollbar max-height="60vh" class="formClass">
      <el-form ref="ruleFormRef" :inline="true" :model="state.formData" style="width: 93%; margin: 0 auto">
        <el-form-item label="供应商" prop="carrierId" :rules="[{ required: true, message: '请选择供应商', trigger: 'change' }]">
          <el-select filterable v-model="state.formData!.carrierId" style="width: 200px" placeholder="请选择供应商" @change="carrierChange">
            <el-option v-for="(_item, _index) in state.carriers.carriers" :key="_item.carrierId" :label="_item.carrierName" :value="_item.carrierId" />
          </el-select>
        </el-form-item>
        <el-form-item label="车辆" prop="vehicleId" :rules="[{ required: true, message: '请选择车辆', trigger: 'change' }]">
          <el-select filterable v-model="state.formData!.vehicleId" style="width: 200px" placeholder="请选择车辆" @change="vehicleChange">
            <el-option v-for="(_item, _index) in currentCarrierVehicles" :key="_item.vehicleId" :label="_item.vehicleNo" :value="_item.vehicleId" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="运单号" prop="shipmentNo" :rules="[{ required: true, message: '请选择运单号', trigger: 'change' }]">
          <el-select v-model="state.formData!.shipmentNo" style="width: 100%" placeholder="请选择运单号" @change="shipmentChange">
            <el-option v-for="(_item, _index) in currentCarrierVehicleShipments" :key="_index" :label="_item" :value="_item" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="司机" v-show="state.formData!.driverName" prop="driverName">
          <el-input v-model="state.formData!.driverName" disabled></el-input>
        </el-form-item>
        <el-form-item label="联系方式" v-show="state.formData!.driverMobile" prop="driverMobile">
          <el-input v-model="state.formData!.driverMobile" disabled></el-input>
        </el-form-item>
        <el-form-item label="编辑VIN" style="width: 100%" prop="vins">
          <el-input v-model="state.formData!.vins" :rows="3" type="textarea" placeholder='输入vin，多个用","分隔' />
        </el-form-item>
        <div class="text-right">
          <el-button type="primary" @click="addVin">添加</el-button>
          <el-button type="danger" @click="removeVin">移除</el-button>
        </div>
        <div v-for="(item, index) in state.formData!.wares" :key="index" class="mb-[10px]">
          <el-form-item label="仓库" prop="baseWareName">
            <div>{{ item.baseWareName }}</div>
          </el-form-item>
          <el-form-item label="日期" :prop="`wares.${index}.appointmentDate`" :rules="[{ required: true, message: '请选择预约日期', trigger: 'blur' }]">
            <el-date-picker
              v-model="item.appointmentDate"
              type="date"
              placeholder="请选择预约日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange(item, index, $event)"
            />
          </el-form-item>
          <!-- <el-form-item label="车道" :prop="`wares.${index}.laneId`" :rules="[{ required: true, message: '请选择预约车道', trigger: 'change' }]">
            <el-select filterable v-model="item.laneId" style="width: 200px" placeholder="请选择车道" @change="handleLaneChange(item, index, $event)" :prop="`wares.${index}.laneId`">
              <el-option v-for="(_item, _index) in item.lanes" :key="_index" :label="_item.laneName" :value="_item.laneId" />
            </el-select>
          </el-form-item> -->
          <el-form-item label="预约时间段" :prop="`wares.${index}.appointmentId`" :rules="[{ required: true, message: '请选择预约时间段', trigger: 'change' }]">
            <el-select
              filterable
              v-model="item.appointmentId"
              style="width: 200px"
              placeholder="请选择预约时间段"
              @change="handleAppointmentDateChange(item, index, $event, item.laneId)"
            >
              <el-option v-for="(_item, _index) in item.appointmentDateList" :key="_index" :label="_item.timeInterval" :value="_item.appointmentId" />
            </el-select>
          </el-form-item>
          <el-card shadow="never" class="time-card" style="width: 100%; padding-bottom: 0 !important" v-if="item.laneList && item.laneList.length > 0">
            <el-form-item style="width: 100%" :prop="`wares.${index}.laneId`" :rules="[{ required: true, message: '请选择预约车道', trigger: 'change' }]">
              <el-scrollbar max-height="150px" style="width: 100%; min-height: 40px">
                <el-space wrap>
                  <div class="lane-date" :class="{ active: v.active }" v-for="(v, i) in item.laneList" @click="v.appointmentStatus ? '' : selectLane(v, index)">
                    {{ v.laneName }}
                    <span v-if="v.vinCount">(剩:{{ v.vinCount }})</span>
                  </div>
                </el-space>
              </el-scrollbar>
            </el-form-item>
          </el-card>
          <el-card shadow="never" class="time-card" style="width: 100%; padding-bottom: 0 !important" v-else-if="item.laneList && item.laneList.length === 0">
            <div>当前时间段暂无可预约车道</div>
          </el-card>
          <div style="width: 100%">
            <TableComponent :tableData="item.orders" :tableConfig="tableConfig" />
          </div>
          <el-divider v-if="index !== state.formData!.wares!.length - 1" />
        </div>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel(ruleFormRef)">取消</el-button>
        <el-button type="primary" @click="confirm(ruleFormRef)">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
  import {
    appointmentIdsApi,
    appointmentInfoByVehicleApi,
    appointmentSubmitApi,
    laneAppointmentSubmitApi,
    laneAppointmentUpdateVinApi,
    laneAppointmentByDateApi,
    laneAppointmentByDateTimeApi,
  } from '@/api/shipmentManagement'
  import {
    AppointmentsVO,
    CarriersVO,
    CarriersVehiclesVO,
    ConfirmFormDataVO,
    StateLaneReservationVO,
    StateVO,
    VehiclesVO,
    appointmentIdsRequest,
    laneAppointmentUpdateVinRequestVO,
    laneItemsVO,
    lanesVO,
    shipmentInfoVO,
    vehicleWaresVO,
    waresVO,
    AppointmentItemsVO,
  } from '@/api/shipmentManagement/type'
  import { FormInstance } from 'element-plus'
  import { PropType } from 'vue'
  const emit = defineEmits(['closeDialog'])
  const ruleFormRef = ref<FormInstance>()
  const props = defineProps({
    /**
     * 行信息
     */
    tableRow: {
      type: Object,
      default: () => {
        return {}
      },
    },
    /**
     * 弹窗属性
     */
    dialog: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
    // 车道预约类型 3:公路 4:铁水
    laneAppointmentType: {
      require: true,
      type: String,
      default: '',
    },
  })
  const tableConfig = {
    tableItem: [
      {
        name: 'vin',
        label: 'VIN',
        listEnable: true,
      },
    ],
  }
  const state = reactive<StateLaneReservationVO>({
    formData: {},
    carriers: {}, //三级联动
  })
  //监听state.formData.wares变化
  watch(
    () => state.formData!.wares,
    (newVal, oldVal) => {
      if (newVal!.length > 0) {
        newVal!.forEach((item: waresVO, index: number) => {
          //之前预约过的日期
          item.selectedAppointmentId = item.appointmentId
          item.selectedAppointmentLaneId = item.laneId
          if (item.appointmentDate) {
            const params = {
              baseWareId: item.baseWareId,
              appointmentDate: item.appointmentDate,
              appointmentId: item.appointmentId,
            }
            laneAppointmentByDateApi(params).then((res: { data: any }) => {
              const { data } = res
              item.appointmentDateList = data
              if (item.appointmentDateList && item.appointmentDateList.length > 0 && item.appointmentId) {
                handleAppointmentDateChange(item, index, item.appointmentId, item.laneId)
              }
            })
          }
        })
      }
    },
  )
  /**
   * 当前选中的车道
   */
  const currentLanes = ref<lanesVO[]>([])
  /**
   * 当前承运商
   */
  const currentCarriers = ref<CarriersVO[]>()
  /**
   * 当前供应商下的车辆列表
   */
  const currentCarrierVehicles = ref<CarriersVehiclesVO[]>()
  const appointmentDateList = ref<AppointmentItemsVO[]>([])
  /**
   * 当前供应商下的车辆下的运单列表
   */
  // const currentCarrierVehicleShipments = ref<string[]>();
  /**
   * @description: 选择预约时间段
   * @param {*} item 当前项
   * @param {*} index 当前索引
   * @param {*} event 值
   * @return {*}
   */
  function handleDateChange(item: appointmentIdsRequest, index: number, event: string): void {
    const params = {
      baseWareId: item.baseWareId,
      appointmentDate: item.appointmentDate,
    }
    laneAppointmentByDateApi(params).then((res: { data: any }) => {
      const { data } = res
      item.appointmentDateList = data
      appointmentDateList.value = data
    })
  }
  //选择预约时间段
  function handleAppointmentDateChange(item: waresVO, index: number, appointmentId: string, laneId?: string) {
    item.laneId = ''
    item.laneName = ''
    if (item.appointmentDateList.length > 0) {
      item.appointmentId = appointmentId
      const { workStart, workEnd } = item.appointmentDateList.find((v: { appointmentId: string }) => v.appointmentId === appointmentId)
      const params = {
        baseWareId: item.baseWareId,
        appointmentDate: item.appointmentDate,
        workStart: workStart,
        workEnd: workEnd,
        laneId: item.selectedAppointmentId === item.appointmentId ? item.selectedAppointmentLaneId : undefined,
      }
      laneAppointmentByDateTimeApi(params).then((res: { data: any }) => {
        const { data } = res
        data.map((_item: AppointmentsVO) => {
          // 如果当前时间段是自己预约的，默认选中
          if (_item.laneId === item.selectedAppointmentLaneId && item.selectedAppointmentId === appointmentId) {
            item.laneName = _item.laneName
            item.laneId = _item.laneId
            _item.active = true
          } else {
            _item.active = false
          }
        })
        item.laneList = data
      })
    }
  }
  /**
   * @description 选择供应商
   * @param carrierId
   */
  function carrierChange(carrierId: string) {
    currentCarrierVehicles.value = []
    state.formData!.vehicleId = ''
    currentCarriers.value = state.carriers!.carriers!.filter((item) => item.carrierId === carrierId)
    currentCarrierVehicles.value = currentCarriers.value[0].vehicles
  }
  /**
   * @description 选择车辆
   * @param vehicleId
   */
  function vehicleChange(vehicleId: string) {
    // const currentVehicle = currentCarrierVehicles.value!.filter(item => item.vehicleId === vehicleId);
    // currentCarrierVehicleShipments.value = currentVehicle[0].shipmentNos;
    appointmentInfoByVehicleApi({ vehicleId }).then((res) => {
      const { data } = res
      if (data) {
        state.formData!.driverMobile = data.driverMobile || ''
        state.formData!.driverName = data.driverName || ''
        state.formData!.wares = data.wares
        // 付默认值给已预约的日期和车道
        if (data.wares.length > 0) {
          data.wares.forEach((_item: waresVO, _index: number) => {
            if (_item.lanes && _item.lanes.length > 0) {
              _item.lanes.forEach((v: lanesVO, i: number) => {
                if (v.appointmentDate) {
                  state.formData!.wares![_index].appointmentDate = v.appointmentDate
                  state.formData!.wares![_index].laneId = v.laneId
                  const params = {
                    laneId: v.laneId,
                    appointmentDate: v.appointmentDate,
                  }
                  handleLaneChange(params, _index, v.laneId)
                }
              })
            }
          })
        }
      } else {
        state.formData!.driverMobile = ''
        state.formData!.driverName = ''
        state.formData!.wares = []
      }
    })
  }
  /**
   * @description: 添加vin
   * @return {*}
   */
  function addVin(): void {
    const params = {
      vehicleId: state.formData!.vehicleId,
      vinUpdateType: 1,
      vins: state.formData!.vins?.split(','),
    }
    laneAppointmentUpdateVinApi(params).then((res: any) => {
      ElMessage.success('添加成功')
      vehicleChange(state.formData!.vehicleId!)
    })
  }
  /**
   * @description: 移除vin
   * @return {*}
   */
  function removeVin(): void {
    const params = {
      vehicleId: state.formData!.vehicleId,
      vinUpdateType: 2,
      vins: state.formData!.vins?.split(','),
    }
    laneAppointmentUpdateVinApi(params).then((res: any) => {
      ElMessage.success('移除成功')
      vehicleChange(state.formData!.vehicleId!)
    })
  }
  /**
   *
   */
  /**
   * @description: 选择预约车道
   * @param {*} item 当前项
   * @param {*} index 当前索引
   * @param {*} event 值
   * @return {*}
   */
  function handleLaneChange(item: appointmentIdsRequest, index: number, event: any): void {
    const lane = state.formData!.wares![index].lanes.filter((lane: lanesVO) => {
      return event.includes(lane.laneId)
    })
    currentLanes.value = lane
    state.formData!.wares![index].tempCurrentLanes = lane[0]
    state.formData!.wares![index].laneName = lane[0].laneName
    state.formData!.wares![index].appointmentId = '' //初始化预约时间段;
    const params = {
      appointmentDate: item.appointmentDate,
      laneId: item.laneId,
    }
    appointmentIdsApi(params).then((res) => {
      const { data } = res
      if (currentLanes.value.length > 0) {
        currentLanes.value![0].appointments.map((item) => {
          //如果当前时间段是自己预约的
          if (item.appointmentStatus === 1) {
            item.active = true
            state.formData!.wares![index].appointmentId = item.appointmentId
          }
          if (data.length > 0) {
            data.forEach((v: any) => {
              //如果是其他人预约的
              if (v === item.appointmentId && item.appointmentStatus != 1) {
                item.disabled = true
                item.active = false
              }
            })
          } else {
            item.disabled = false
          }
        })
      }
    })
  }
  /**
   * @description: 选择车道预约时间段
   * @param {*} item 当前选中项
   * @param {*} parentIndex 上级下标
   * @return {*}
   */
  function selectLane(item: AppointmentsVO, parentIndex: number): void {
    //过滤掉tempLaneDate中disabled=true的项， 将当前项的active=true,去掉其他项中的active，
    if (!item.disabled) {
      item.active = true
      state.formData!.wares![parentIndex].laneList!.map((_item: AppointmentsVO) => {
        if (_item.laneId !== item.laneId) {
          _item.active = false
        } else {
          _item.active = true
        }
      })
      state.formData!.wares![parentIndex].laneId = item.laneId
      state.formData!.wares![parentIndex].laneName = item.laneName
    }
  }
  /**
   *
   * @param formEl 关闭弹窗
   */
  function cancel(formEl: FormInstance | undefined) {
    //清除表单数据
    formEl!.resetFields()
    state.formData!.wares = []
    emit('closeDialog')
  }
  const confirm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        const laneItems = state.formData?.wares?.map((item) => {
          return {
            baseWareId: item.baseWareId,
            laneId: item.laneId,
            laneName: item.laneName,
            appointmentDate: item.appointmentDate,
            appointmentId: item.appointmentId,
            orderIds: item.orders?.map((order) => order.id),
          } as laneItemsVO
        })
        const params = {
          carrierId: state.formData?.carrierId,
          // shipmentNo: state.formData?.shipmentNo,
          laneItems: laneItems,
        }
        laneAppointmentSubmitApi(params, props.laneAppointmentType).then((res: any) => {
          ElMessage.success('预约成功')
          //清除表单
          formEl.resetFields()
          state.formData!.wares = []
          emit('closeDialog')
        })
      } else {
      }
    })
  }
  defineExpose({
    state,
    handleLaneChange,
    carrierChange,
  })
</script>
<style scoped lang="scss">
  .lane-date {
    position: relative;
    display: inline-block;
    height: 40px;
    line-height: 40px;
    padding: 0 8px;
    text-align: center;
    border: 1px solid #ebeef5;
    background-color: #ffffff;
    cursor: pointer;
    border-radius: 4px;
    &.active {
      background-color: #1890ff;
      color: #ffffff;
    }
    &.disabled {
      background-color: #e3e3e3;
      //禁止点击
      cursor: not-allowed;
    }
    .vin-tag {
      position: absolute;
      right: 0;
      top: -50%;
      transform: transitionY(-50%);
    }
  }
  .time-card {
    border-bottom: none;
    :deep(.el-card__body) {
      padding-bottom: 0;
    }
  }
</style>
