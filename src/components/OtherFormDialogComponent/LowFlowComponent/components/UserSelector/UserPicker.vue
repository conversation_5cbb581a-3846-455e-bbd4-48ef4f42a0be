<script setup lang="ts">
  import { useVModel } from '@vueuse/core'
  import type { TreeNodeData } from 'element-plus/es/components/tree/src/tree.type'
  import type { TreeInstance } from 'element-plus'
  import { getUserSelectOptionApi } from '@/api/GlobalMenu'
  import { UserInfo } from './UserTag.vue'

  export type ModelValueType = UserInfo | UserInfo[] | null | undefined

  export interface UserDropdownProps {
    modelValue: ModelValueType
    multiple?: boolean
  }

  const treeProps = {
    label: 'userName',
    children: 'children',
    isLeaf: 'leaf',
    class: (org: TreeNodeData) => renderClass(org),
  }

  export interface Org {
    value: string
    type: 'user' | 'dept'
    avatar?: string
    name: string
    leaf: boolean
  }

  const $props = withDefaults(defineProps<UserDropdownProps>(), {
    multiple: false,
  })

  // const emit = defineEmits(["userOrgOptions"]);

  const $emits = defineEmits<{
    (e: 'update:modelValue', modelValue: ModelValueType): void
    (e: 'userOrgOptions', value: any): void
  }>()

  const value = useVModel($props, 'modelValue', $emits)

  const userOptions = ref<UserInfo[]>([])
  const userOrgOptions = ref<UserInfo[]>([])
  const orgTreeRef = ref<TreeInstance>()
  const expandedKeys = ref<string[]>([])

  const renderClass = (org: TreeNodeData): string | { [key: string]: boolean } => {
    const val = userOptions.value.find((e) => e.userId === org.userId)
    if (val) {
      return 'is-active'
    } else {
      return ''
    }
  }

  const onNodeClick = (data: UserInfo) => {
    // if (data.type !== 'user') return;
    if ($props.multiple) {
      const index = userOptions.value.findIndex((e) => e.userId === data.userId)
      if (index === -1) {
        userOptions.value.push(data)
        userOptions.value.sort((a, b) => a.userId!.localeCompare(b.userId!))
      } else {
        userOptions.value.splice(index, 1)
      }
    } else {
      const index = userOptions.value.findIndex((e) => e.userId === data.userId)
      if (index === -1) {
        userOptions.value = [data]
      } else {
        userOptions.value.splice(index, 1)
      }
    }
  }
  const dialogVisible = ref(false)
  const queryForm = reactive({
    name: null,
  })

  watch(
    () => queryForm.name,
    (val) => {
      orgTreeRef.value?.filter(val)
    },
  )
  const filterNode = (value: string, data: TreeNodeData): boolean => {
    if (!value) return true
    return data.userName.includes(value)
  }
  const open = () => {
    dialogVisible.value = true
  }
  const onOpen = async () => {
    await getUserSelectOptionList()
    let userInfo: UserInfo[] = []
    if (Array.isArray(value.value)) {
      userInfo.push(...value.value)
    } else if (value.value) {
      userInfo.push(value.value)
    }
    if (userInfo.length > 0) {
      userOptions.value = userOrgOptions.value.filter((item) => userInfo.some((user) => user.userId === item.userId))
      userOptions.value.sort((a, b) => a.userId!.localeCompare(b.userId!))
    } else {
      userOptions.value = []
    }
  }
  const getUserSelectOptionList = async () => {
    const { data } = await getUserSelectOptionApi({})
    userOrgOptions.value = data.map((item: { value: any; label: any }) => {
      return {
        userId: item.value,
        userName: item.label,
      }
    })
    $emits('userOrgOptions', userOrgOptions.value)
  }
  const handelConfirm = () => {
    if ($props.multiple) {
      value.value = userOptions.value
    } else {
      if (userOptions.value.length > 0) {
        value.value = userOptions.value[0]
      } else {
        value.value = null
      }
    }
    dialogVisible.value = false
  }
  defineExpose({
    open,
  })
</script>

<template>
  <el-dialog v-model="dialogVisible" align-center draggable title="选择用户" width="30%" @close="queryForm.name = null" @open="onOpen">
    <el-card shadow="never" class="org-card">
      <template #header>
        <el-input v-model="queryForm.name" placeholder="输入关键字进行查询" :style="{ width: '100%' }" suffix-icon="search" clearable></el-input>
      </template>
      <el-scrollbar tag="div" class="org-tree">
        <el-tree
          ref="orgTreeRef"
          node-key="userId"
          :data="userOrgOptions"
          :default-expanded-keys="expandedKeys"
          :props="treeProps"
          :filter-node-method="filterNode"
          @node-click="onNodeClick"
        >
          <template #default="{ data }">
            <div class="flex flex-1 flex-items-center flex-justify-between">
              <div class="flex-center">
                <!-- <el-avatar v-if="data.type === 'user'" :size="25" :src="data.avatar">
                  {{ data.name.charAt(0) }}
                </el-avatar> -->
                <el-icon :size="16">
                  <Avatar />
                </el-icon>
                &nbsp;{{ data.userName }}
              </div>
              <el-icon class="is-selected">
                <Check />
              </el-icon>
            </div>
          </template>
        </el-tree>
      </el-scrollbar>
    </el-card>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handelConfirm">确认</el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
  .el-tree {
    --el-tree-node-content-height: 40px;

    .el-tree-node__content {
      border-radius: 0px;
      margin: 2px 0;
    }

    .is-active {
      color: var(--el-color-primary);

      .is-selected {
        display: block;
      }
    }
  }

  .el-card {
    background-color: transparent;

    :deep(.el-card__header) {
      padding: 10px !important;
    }

    :deep(.el-card__body) {
      padding: 0 !important;
    }
  }

  .org-tree {
    height: 270px;
    padding: 5px 8px;
  }

  .is-selected {
    display: none;
    padding-right: 15px;
  }
</style>
