<!--
 * @Author: llm
 * @Date: 2025-01-04 10:37:55
 * @LastEditors: llm
 * @LastEditTime: 2025-01-07 18:12:15
 * @Description:
-->
<script setup lang="ts">
  // import { getByUsername } from '@@/api/modules/user';
  import { componentSizeMap, useFormSize } from 'element-plus'

  export interface UserTagProps {
    dataList: any[]
    type?: 'success' | 'info' | 'warning' | 'danger'
    closable?: boolean
    userInfo: UserInfo
  }

  const $props = withDefaults(defineProps<UserTagProps>(), {
    closable: false,
    type: 'info',
    userInfo: () => ({
      userId: undefined,
      userName: undefined,
    }),
  })
  const $emits = defineEmits<{
    (e: 'close', userInfo: UserInfo): void
  }>()

  export interface UserInfo {
    userId: string | undefined
    userName: string | undefined
  }

  onMounted(() => {
    if (!$props.userInfo?.userName) {
      throw new Error('userName is required')
    }
  })
  const formSize = useFormSize()
  const getComponentSize = computed(() => {
    return componentSizeMap[formSize.value || 'default'] - 12
  })
  const onClose = () => {
    $emits('close', $props.userInfo)
  }
</script>
<template>
  <el-tag round :closable="$props.closable" :type="type" effect="light" @close="onClose">
    <div class="flex-center" style="gap: 4px; grid-gap: 4px">
      <!-- <el-avatar :size="getComponentSize" :src="userInfo.avatar">
        {{ (userInfo.label || userName).charAt(0) }}
      </el-avatar> -->
      <span>{{ $props.userInfo.userName || '-' }}</span>
    </div>
  </el-tag>
</template>

<style scoped lang="scss">
  :deep(.el-tag) {
    .el-tag__content:only-child {
      margin-right: 4px;
    }
  }
</style>
