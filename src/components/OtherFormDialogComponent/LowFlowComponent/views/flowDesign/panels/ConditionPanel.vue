<!--
 * @Author: 周宗文
 * @Date: 2025-01-08 10:11:49
 * @LastEditors: 周宗文
 * @LastEditTime: 2025-01-08 11:15:37
 * @Description:
-->
<script setup lang="ts">
  import type { ConditionNode } from '../nodes/type'
  import type { Ref } from 'vue'
  import type { Field } from '@@/components/Render/type'

  const { fields } = inject<{ fields: Ref<Field[]> }>('flowDesign', { fields: ref([]) })
  defineProps<{
    activeData: ConditionNode
  }>()

  const operatorOptions = [
    {
      value: 'lt',
      label: '<',
    },
    {
      value: 'le',
      label: '<=',
    },
  ]
</script>

<template>
  <div style="display: flex; align-items: center; justify-content: space-between; max-width: 600px">
    <el-input-number style="width: 150px" v-model="activeData.condition.leftValue" size="default" :min="0" :precision="2" />
    <el-select v-model="activeData.condition.leftOp" style="width: 75px" placeholder="符号">
      <el-option v-for="(item, index) in operatorOptions" :key="index" :label="item.label" :value="item.value"></el-option>
    </el-select>
    <el-tag style="width: 50px; font-weight: 600" type="info" effect="dark" :hit="false" size="large">金额</el-tag>
    <el-select v-model="activeData.condition.rightOp" style="width: 75px" placeholder="符号">
      <el-option v-for="(item, index) in operatorOptions" :key="index" :label="item.label" :value="item.value"></el-option>
    </el-select>
    <el-input-number style="width: 150px" v-model="activeData.condition.rightValue" size="default" :min="0" :precision="2" />
  </div>
</template>

<style scoped lang="scss"></style>
