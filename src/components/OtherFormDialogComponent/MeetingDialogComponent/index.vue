<!--
 * @Author: llm
 * @Date: 2024-06-14 09:53:21
 * @LastEditors: llm
 * @LastEditTime: 2024-06-19 10:25:24
 * @Description:会议纪要弹窗
-->
<template>
  <el-drawer v-model="meetingVisible" direction="rtl" title="会议纪要" size="50%" :before-close="handleCloseMeetingVisible" :close-on-click-modal="false">
    <el-scrollbar height="100vh">
      <el-form @input="resetTimer" :model="form" :rules="rules" ref="formRef" label-position="right" label-width="90px" style="width: 95%" :disabled="readonly">
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item prop="theme" label="会议主题">
              <el-input v-model="form.theme" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item prop="emcee" label="主持人">
              <el-input v-model="form.emcee" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="department" label="参会部门">
              <el-input v-model="form.department" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item prop="conferee" label="参会人员">
              <el-input v-model="form.conferee" type="textarea" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item prop="place" label="会议地点">
              <el-input v-model="form.place" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item prop="sendMails" label="抄送">
              <el-input v-model="form.sendMails" type="textarea" placeholder="请输入需要抄送的邮箱,多个用','隔开" :clearable="true"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item prop="attachmentsUrl" label="选择文件">
              <el-upload
                :file-list="form.attachmentsUrl ? [{ name: form.attachments, url: form.attachmentsUrl }] : []"
                ref="uploadFileRef"
                action="#"
                :auto-upload="false"
                :limit="1"
                :on-change="
                  (file: any, fileList: any[]) => {
                    uploadFile(file, fileList)
                  }
                "
                :on-remove="
                  (file: any, fileList: any[]) => {
                    handleRemove(file, fileList)
                  }
                "
                :on-exceed="handleExceed"
              >
                <template #trigger>
                  <el-button type="primary">点击上传</el-button>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item prop="content" label="会议内容">
              <div style="width: 100%">
                <quill-editor class="editor" contentType="html" ref="quillEditorRef" :content="form.content" :options="editorOption" :readOnly="readonly" />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="confirmForm(formRef, false)" v-if="!readonly">会议存档</el-button>
        <el-button type="primary" @click="confirmForm(formRef, true)" v-if="!readonly">保存快照并存储</el-button>
        <el-button type="primary" @click="handleCloseMeetingVisible">返回</el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
  import { reactive, ref, watchEffect } from 'vue'
  import { FormInstance, UploadProps, UploadRawFile, genFileId } from 'element-plus'
  //局部引入
  import { QuillEditor, Quill } from '@vueup/vue-quill'
  import { postMeetingDraftSave, getMeetingDraftDetail, postMeetingDraftRecord } from '@/api/meetingCenter'
  import ImageUploader from 'quill-image-uploader'
  import '@vueup/vue-quill/dist/vue-quill.snow.css'
  import { uploadFileApi } from '@/api/auth'
  Quill.register('modules/imageUploader', ImageUploader)
  const emit = defineEmits(['resetQuery'])
  const meetingVisible = ref(false)
  const handleCloseMeetingVisible = async () => {
    // 重置表单
    resetForm()
    meetingVisible.value = false
  }
  let timer = ref()
  const urlParams = ref<any>({
    reportFormsType: 1, //会议类型 1:业务报表 2:财务报表
  }) //请求地址上的参数
  const formRef = ref<FormInstance>()
  const quillEditorRef = ref()
  const editorOption = {
    theme: 'snow',
    placeholder: '请输入正文',
    modules: {
      imageUploader: {
        upload: async (file: any) => {
          const params = {
            file: file,
            businessLine: 204,
          }
          let res = await uploadFileApi('tms/system/file/upload', params) // 此处为自己的上传接口
          return res.data // 此处替换为接口返回的文件路径
        },
      },
      toolbar: [
        ['bold', 'italic', 'underline', 'strike'], // 加粗 斜体 下划线 删除线
        ['blockquote', 'code-block'], // 引用  代码块
        [{ header: 1 }, { header: 2 }], // 1、2 级标题
        [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
        [{ script: 'sub' }, { script: 'super' }], // 上标/下标
        [{ indent: '-1' }, { indent: '+1' }], // 缩进
        [{ direction: 'rtl' }], // 文本方向
        [{ size: ['small', false, 'large', 'huge'] }], // 字体大小
        [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
        [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
        [{ font: [] }], // 字体种类
        [{ align: [] }], // 对齐方式
        ['clean'], // 清除文本格式
        ['link', 'image', 'video'], // 链接、图片、视频
      ],
    },
  }

  const form = ref({
    theme: '',
    emcee: '',
    department: '',
    conferee: '',
    place: '',
    sendMails: '',
    content: '',
    attachmentsUrl: '',
    attachments: '',
    snapshot: false, //true-保存快照并存档接口  false-会议存档接口
  })
  const rules = reactive({
    theme: [{ required: true, message: '请输入会议主题', trigger: 'blur' }],
    emcee: [{ required: true, message: '请输入主持人', trigger: 'blur' }],
    department: [{ required: true, message: '请输入参会部门', trigger: 'blur' }],
    conferee: [{ required: true, message: '请输入参会人员', trigger: 'blur' }],
  })
  /**
   * Submits the form if it is valid, otherwise logs the error fields.
   *
   * @param {FormInstance | undefined} formEl - The form instance to validate.
   * @param {boolean} snapshot - true-保存快照并存档接口  false-会议存档接口
   * @return {Promise<void>} A promise that resolves when the form is submitted or rejected.
   */
  const submitForm = async (formEl: FormInstance | undefined = formRef.value, snapshot: boolean = false) => {
    return new Promise<void>(async (resolve, reject) => {
      await formEl!.validate(async (valid, fields) => {
        if (valid) {
          form.value.snapshot = snapshot
          // 获取编辑器的内容
          const editor = quillEditorRef.value.editor
          form.value.content = editor.innerHTML
          let url = '/tms/meeting/draft/save'
          //将urlParams对象中的键值对拼到url上例如/tms/meeting/draft/save?key1=value1&key2=value2
          if (Object.keys(urlParams.value).length > 0) {
            url += '?'
            for (const key in urlParams.value) {
              if (urlParams.value.hasOwnProperty(key)) {
                url += `${key}=${urlParams.value[key]}&`
              }
            }
            url = url.substring(0, url.length - 1)
          }
          await postMeetingDraftSave(form.value, url)
          resolve()
        } else {
        }
      })
    })
  }
  //提交会议纪要
  const confirmForm = async (formEl: FormInstance | undefined = formRef.value, snapshot: boolean = false) => {
    if (!formEl) return
    await submitForm(formEl, snapshot)
    // 重置表单
    resetForm()
    ElMessage.success('保存成功')
    meetingVisible.value = false
  }
  //临时保存会议纪要
  const saveTempMeeting = async () => {
    // 获取编辑器的内容
    if (quillEditorRef.value) {
      const editor = quillEditorRef.value.editor
      form.value.content = editor.innerHTML
      const params = {
        ...form.value,
        summaryStatus: 0,
      }
      await postMeetingDraftRecord(params)
    }
  }
  // 重置表单
  const resetForm = () => {
    formRef.value!.resetFields()
    formRef.value!.clearValidate()
    form.value = {
      theme: '',
      emcee: '',
      department: '',
      conferee: '',
      place: '',
      sendMails: '',
      content: '',
      attachmentsUrl: '',
      attachments: '',
      snapshot: false, //true-保存快照并存档接口  false-会议存档接口
    }
  }
  const readonly = ref(false) //是否只读
  const meetingId = ref()
  //获取会议纪要详情
  const getMeetingDetail = async (id: string) => {
    let url = 'tms/meeting/draft/detail'
    if (id) {
      meetingId.value = id
      readonly.value = true
      url += `/${id}`
    } else {
      meetingId.value = undefined
      readonly.value = false
    }
    const { data } = await getMeetingDraftDetail({}, url)
    interface FormValue {
      // 定义 form.value 的类型
      [key: string]: any
    }
    //覆盖form的值,过滤掉不存在的key
    const formValue: FormValue = form.value
    Object.keys(formValue).forEach((key: string) => {
      if (data.hasOwnProperty(key as keyof typeof form.value)) {
        ;(form.value as any)[key] = data[key]
      }
    })
    quillEditorRef.value.setContents(data.content)
  }
  //上传文件
  const uploadFile = async (file: any, fileList: any[]) => {
    if (file.status !== 'ready') return
    const params = {
      file: file.raw,
      businessLine: 204,
    }
    let res = await uploadFileApi('tms/system/file/upload', params) // 此处为自己的上传接口
    form.value.attachmentsUrl = res.data
    form.value.attachments = file.name
  }
  const uploadFileRef = ref()
  const handleExceed: UploadProps['onExceed'] = (files) => {
    uploadFileRef.value[0]!.clearFiles()
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    uploadFileRef.value[0]!.handleStart(file)
  }
  const handleRemove = (file: any, fileList: any[]) => {
    //删除当前文件
    form.value.attachmentsUrl = ''
    form.value.attachments = ''
  }
  const resetTimer = () => {
    clearTimeout(timer.value)
    timer.value = setTimeout(saveTempMeeting, 5000) // 10秒后自动保存
  }
  watchEffect(() => {
    if (quillEditorRef.value) {
      if (!meetingId.value) {
        resetTimer()
      }
    }
  })
  defineExpose({
    meetingVisible,
    form,
    urlParams,
    getMeetingDetail,
  })
</script>
<style>
  .editor {
    line-height: normal !important;
    height: 300px;
  }
  .ql-snow .ql-tooltip[data-mode='link']::before {
    content: '请输入链接地址:';
  }
  .ql-snow .ql-tooltip.ql-editing a.ql-action::after {
    border-right: 0px;
    content: '保存';
    padding-right: 0px;
  }

  .ql-snow .ql-tooltip[data-mode='video']::before {
    content: '请输入视频地址:';
  }

  .ql-snow .ql-picker.ql-size .ql-picker-label::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item::before {
    content: '14px';
  }
  .ql-snow .ql-picker.ql-size .ql-picker-label[data-value='small']::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item[data-value='small']::before {
    content: '10px';
  }
  .ql-snow .ql-picker.ql-size .ql-picker-label[data-value='large']::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item[data-value='large']::before {
    content: '18px';
  }
  .ql-snow .ql-picker.ql-size .ql-picker-label[data-value='huge']::before,
  .ql-snow .ql-picker.ql-size .ql-picker-item[data-value='huge']::before {
    content: '32px';
  }

  .ql-snow .ql-picker.ql-header .ql-picker-label::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item::before {
    content: '文本';
  }
  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='1']::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='1']::before {
    content: '标题1';
  }
  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='2']::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='2']::before {
    content: '标题2';
  }
  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='3']::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='3']::before {
    content: '标题3';
  }
  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='4']::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='4']::before {
    content: '标题4';
  }
  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='5']::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='5']::before {
    content: '标题5';
  }
  .ql-snow .ql-picker.ql-header .ql-picker-label[data-value='6']::before,
  .ql-snow .ql-picker.ql-header .ql-picker-item[data-value='6']::before {
    content: '标题6';
  }

  .ql-snow .ql-picker.ql-font .ql-picker-label::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item::before {
    content: '标准字体';
  }
  .ql-snow .ql-picker.ql-font .ql-picker-label[data-value='serif']::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item[data-value='serif']::before {
    content: '衬线字体';
  }
  .ql-snow .ql-picker.ql-font .ql-picker-label[data-value='monospace']::before,
  .ql-snow .ql-picker.ql-font .ql-picker-item[data-value='monospace']::before {
    content: '等宽字体';
  }
</style>
