<!--
 * @Author: llm
 * @Date: 2024-03-19 16:18:41
 * @LastEditors: llm
 * @LastEditTime: 2024-03-20 10:43:51
 * @Description: 合并运单弹窗
-->
<template>
  <el-dialog :draggable="true" v-model="mergeShipmentNoVisible" title="运单拼单" width="500" :before-close="handleCloseMergeShipmentNoVisible">
    <span>当前车辆司机存在未完成的运单，勾选后可进行拼单操作</span>
    <div>
      <el-radio-group v-model="mergeShipmentNo" class="ml-4">
        <el-radio :label="item.shipmentNo" size="large" v-for="(item, index) in mergeShipmentNoList" :key="index" border class="mt-[12px]">{{
          item.taskName
        }}</el-radio>
      </el-radio-group>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="mergeShipmentNoFun">确认拼单</el-button>
        <el-button type="primary" @click="createShipmentNoFun">创建新运单</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
  import { globalRequestApi } from '@/api/planManagement'
  import { appointmentSubmitApi } from '@/api/shipmentManagement'
  import { MergeShipmentNoVO, StateVO } from '@/api/shipmentManagement/type'
  const emit = defineEmits(['closeDialog'])
  const mergeShipmentNoVisible = ref(false)
  const props = defineProps({
    /**
     * 弹窗属性
     */
    dialog: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
    /**
     * 用来区分公路和铁水
     * 3:公路
     * 4:铁水
     */
    appointmentType: {
      type: String,
      default: '',
    },
  })
  const state = reactive<StateVO>({
    formData: {},
    submitParams: {
      vehicleId: '',
      driverId: '',
      driverMobile: '',
      driverName: '',
      invertPlate: false,
      vehicleNo: '',
      carrierWareId: '',
      laneItems: [],
      warehouseId: '',
      carrierId: '',
      levelCarrierIds: [],
    },
  })

  /**
   * 合并的运单号
   */
  const mergeShipmentNo = ref<string>('')
  /**
   * 请求信息
   */
  const requestInfo = reactive({
    methods: 'POST',
    uri: '',
  })
  /**
   * 已存在运单列表
   */
  const mergeShipmentNoList = ref<MergeShipmentNoVO[]>()
  const handleCloseMergeShipmentNoVisible = () => {
    mergeShipmentNoVisible.value = false
  }
  //合并运单
  const mergeShipmentNoFun = () => {
    if (mergeShipmentNo.value) {
      if (!requestInfo.uri) {
        requestInfo.uri = `tms/company/order/shipment/lane/appointment/${props.appointmentType}/appointmentSubmit`
      }
      state.submitParams!.shipmentNo = mergeShipmentNo.value
      globalRequestApi(state.submitParams!, requestInfo.methods, requestInfo.uri!).then((res: any) => {
        ElMessage.success('合并成功')
        emit('closeDialog')
        mergeShipmentNoVisible.value = false
        state.submitParams = null
      })
    } else {
      ElMessage.warning('请选择要合并的运单')
    }
  }
  //创建新运单
  const createShipmentNoFun = () => {
    state.submitParams!.shipmentNo = ''
    if (!requestInfo.uri) {
      requestInfo.uri = `tms/company/order/shipment/lane/appointment/${props.appointmentType}/appointmentSubmit`
    }
    globalRequestApi(state.submitParams!, requestInfo.methods, requestInfo.uri!).then((res: any) => {
      ElMessage.success('预约成功')
      emit('closeDialog')
    })
  }
  defineExpose({
    mergeShipmentNoList,
    mergeShipmentNoVisible,
    mergeShipmentNo,
    state,
    requestInfo,
  })
</script>
