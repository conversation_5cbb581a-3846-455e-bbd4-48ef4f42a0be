<template>
  <el-dialog v-model="visible" :title="title" width="80%">
    <el-table :data="data" border max-height="600px" class="el_table">
      <el-table-column align="center" label="序号" width="60">
        <template #default="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>

      <el-table-column align="center" v-for="item in columns" :key="item.property" :property="item.property" :label="item.name">
        <template #default="{ row }">
          <span>{{ row[item.property] ?? '--' }}</span>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script lang="ts" setup>
  // 使用 defineProps 定义 props
  const props = defineProps({
    visibled: {
      type: Boolean,
      required: true,
    },
    data: {
      type: Array,
      required: true,
    },
    toggId: {
      type: Number,
      required: true,
    },
  })
  const title = computed(() => {
    switch (props.toggId) {
      case 0:
        return '借支金额'
      case 1:
        return '历史挂账金额'
      case 2:
        return '高速费'
      case 3:
        return '交车费'
      case 4:
        return '在途费用'
      case 5:
        return '在途维修'
      case 6:
        return '其他支付'
      default:
        return ''
    }
  })

  const emit = defineEmits(['update:visibled', 'update:data', 'update:toggId'])
  // 监听 visible 的本地变化
  const visible = computed({
    get: () => props.visibled,
    set: (val) => emit('update:visibled', val),
  })

  const data = computed({
    get: () => props.data,
    set: (val) => emit('update:data', val),
  })
  const toggId = computed({
    get: () => props.data,
    set: (val) => emit('update:toggId', val),
  })

  const gridList = [
    {
      name: '借款单号',
      property: 'no',
    },
    {
      name: '借支类型',
      property: 'typeDesc',
    },
    {
      name: '事故单号',
      property: 'accidentNo',
    },
    {
      name: '审批状态',
      property: 'auditStatusDesc',
    },
    {
      name: '调度单号',
      property: 'dispatchNo',
    },
    {
      name: '结算单号',
      property: 'settleNo',
    },
    {
      name: '结算状态',
      property: 'auditStatusDesc',
    },
    {
      name: '车牌号',
      property: 'vehicleNo',
    },
    {
      name: '司机',
      property: 'driverName',
    },
    {
      name: '承运商',
      property: 'carrierName',
    },
    {
      name: '承运商类型',
      property: 'carrierType',
    },
    {
      name: '借支金额',
      property: 'loanAmount',
    },
    {
      name: '支付方式',
      property: 'paymentTypeDesc',
    },
    {
      name: '银行卡号',
      property: 'cardNumber',
    },
    {
      name: '备注',
      property: 'remark',
    },
    {
      name: '申请人',
      property: 'createUserName',
    },
    {
      name: '申请时间',
      property: 'createTime',
    },
    {
      name: '付款人',
      property: 'modifyUserName',
    },
    {
      name: '付款时间',
      property: 'paymentTime',
    },
    {
      name: '付款备注',
      property: 'remark',
    },
  ]
  const dataColumn = [
    { name: '挂账日期', property: 'chargeDate' },
    { name: '驾驶员', property: 'driverName' },
    { name: '挂账类型', property: 'type' },
    { name: '挂账增加金额', property: 'addAmount' },
    { name: '挂账减少金额', property: 'reduceAmount' },
    { name: '结余金额', property: 'balance' },
    { name: '调度撤销挂账', property: 'dispatchWithdrawAmount' },
    { name: '关联单据', property: 'relationNo' },
    { name: '最后操作人', property: 'createUserName' },
    { name: '最后操作', property: 'createTime' },
  ]

  const dataColumnEtcFee = [
    { name: '费用单号', property: 'no' },
    { name: '调度单号', property: 'dispatchNo' },
    { name: '审批状态', property: 'auditStatus' },
    { name: '入口', property: 'entranceName' },
    { name: '进入时间', property: 'inHighwayDatetime' },
    { name: '出口', property: 'leaveName' },
    { name: '离开时间', property: 'outHighwayDatetime' },
    { name: '高速费', property: 'amount' },
  ]

  const Paythefare = [
    { name: '费用单号', property: 'no' },
    { name: '审批状态', property: 'auditStatusName' },
    { name: '调度单号', property: 'dispatchNo' },
    { name: '交车地点', property: 'dropUpPointAddress' },
    { name: '交车数量', property: 'dropUpCount' },
    { name: '金额', property: 'amount' },
  ]

  const Ontheway = [
    { name: '费用单号', property: 'no' },
    { name: '审批状态', property: 'auditStatusDesc' },
    { name: '调度单号', property: 'dispatchNo' },
    { name: '费用类型', property: 'chargeItemDesc' },
    { name: '金额', property: 'amount' },
  ]

  const dataColumnRepair = [
    { name: '调度单号', property: 'dispatchNo' },
    { name: '费用单号', property: 'no' },
    { name: '维修日期', property: 'reportDate' },
    { name: '车牌号', property: 'vehicleNo' },
    { name: '司机', property: 'driverName' },
    { name: '总维修费', property: 'amount' },
  ]

  const dataColumnOther = [
    { name: '调度单号', property: 'dispatchNo' },
    { name: '费用类型', property: 'name' },
    { name: '金额', property: 'amount' },
  ]

  const columns = computed(() => {
    switch (props.toggId) {
      case 0:
        return gridList
      case 1:
        return dataColumn
      case 2:
        return dataColumnEtcFee
      case 3:
        return Paythefare
      case 4:
        return Ontheway
      case 5:
        return dataColumnRepair
      case 6:
        return dataColumnOther
      default:
        return []
    }
  })
</script>

<style scoped>
  .el_table {
    text-align: center;
  }
</style>
