<template>
  <el-dialog v-model="visible" :title="title" width="80vw">
    <el-table border :data="dataList" height="50vh" show-overflow-tooltip>
      <el-table-column align="center" label="序号" width="60">
        <template #default="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column v-for="item in gridList" :key="item.property" :label="item.name" :property="item.property" align="center">
        <template #default="{ row }">
          <template v-if="item.property === 'imageUrls'">
            <!-- 处理凭证列 -->
            <div v-if="row.imageUrlList?.length === 1">
              <el-image
                style="width: 50px; height: 50px; cursor: pointer"
                :src="row.imageUrlList[0].url"
                :preview-src-list="row.imageUrlList.map((img: any) => img.url)"
                fit="cover"
                hide-on-click-modal
                :preview-teleported="true"
              />
            </div>
            <div v-else-if="row.imageUrlList?.length > 1">
              <!-- 查看更多图片 -->
              <el-link type="primary" @click="showPic(row.imageUrlList)">查看</el-link>
            </div>
          </template>
          <template v-else>
            <!-- 其他列直接显示数据 -->
            {{ row[item.property] }}
          </template>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
  <!-- 查看图片 -->
  <PicDialogComponent ref="picDialogComponent" :image-list="imageList" />
</template>

<script lang="ts" setup>
  import { globalRequestApi } from '@/api/planManagement'
  import PicDialogComponent from '@/components/PicDialogComponent/index.vue' //查看照片组件
  // 使用 defineProps 定义 props
  const props = defineProps({
    // visible: {
    //   type: Boolean,
    //   required: true,
    // },
    expenseItemsChild: {
      required: true,
    },
    driverIdData: {
      required: true,
    },
    month: {
      required: true,
    },
  })
  const visible = ref(false)
  // const emit = defineEmits(['update:visible', 'update:expenseItemsChild', 'update:driverIdData', 'update:month'])
  // 监听 visible 的本地变化
  // const visible = computed({
  //   get: () => props.visible,
  //   set: (val) => emit('update:visible', val),
  // })
  // // 监听 expenseItemsChild 的本地变化
  // const expenseItemsChild = computed({
  //   get: () => props.expenseItemsChild,
  //   set: (val) => emit('update:expenseItemsChild', val),
  // })
  // // 监听 expenseItemsChild 的本地变化
  // const driverIdData = computed({
  //   get: () => props.driverIdData,
  //   set: (val) => emit('update:driverIdData', val),
  // })
  // // 监听 expenseItemsChild 的本地变化
  // const month = computed({
  //   get: () => props.month,
  //   set: (val) => emit('update:month', val),
  // })
  const title = ref('')
  const gridList = ref<any[]>([])
  let dataList = ref([]) as any

  const getbalfun = async (feeType: any, id: any, month: any, settlementNo: any, type: String, dataArray: any) => {
    dataList.value = dataArray
    console.log(dataList.value, 'dataList.value')
    return
    let expenseDetailParam = {
      driverId: id,
      feeType: feeType,
      month: month,
      settlementNo: settlementNo,
      type: type,
    }
    try {
      const res = await globalRequestApi(expenseDetailParam, 'post', '/logistics/api/out/fleet/order/settlementDetailView/expense/detail')
      dataList.value = res.data.map((item: any) => {
        try {
          return {
            ...item,
            imageUrlList: JSON.parse(item.imageUrls), // 解析JSON字符串
          }
        } catch (error) {
          return { ...item, imageUrlList: [] } // 解析失败设为空数组
        }
      })
    } catch (error) {
      console.error('获取数据失败:', error)
    }
  }

  //查看图片组件
  const picDialogComponent = ref<{
    picDialogVisible: boolean
  }>({ picDialogVisible: false })
  //图片地址
  const imageList = ref<string[]>([])
  /**
   * 展示图片
   * @param row 当前行数据
   * @param name 图片字段名
   */
  const showPic = (row: any) => {
    if (row) {
      imageList.value = []
      row.map((item: any) => {
        imageList.value.push(item.url)
      })
      picDialogComponent.value.picDialogVisible = true
    }
  }

  const getPreviewList = (images: any) => {
    return images.map((item: any) => item.url)
  }

  defineExpose({
    getbalfun,
    visible,
    gridList,
    title,
  })
</script>

<style scoped>
  .imagestyle {
    width: 100px;
    height: 100px;
    z-index: 9999 !important;
  }

  :deep(.el-image-viewer__canvas) {
    width: 100vw;
    height: 100vh;
    position: absolute !important;
    position: none !important;
  }
</style>
