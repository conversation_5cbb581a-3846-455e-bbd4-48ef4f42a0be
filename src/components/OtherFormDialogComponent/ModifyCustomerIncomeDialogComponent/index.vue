<!--
 * @Author: llm
 * @Date: 2024-12-27 19:37:32
 * @LastEditors: llm
 * @LastEditTime: 2025-04-09 09:58:38
 * @Description:
-->
<template>
  <div>
    <el-dialog
      :lock-scroll="true"
      :draggable="true"
      v-model="state.dialogVisible.visible"
      :title="state.dialogVisible.title"
      :close-on-click-modal="false"
      width="500px"
      @close="closeDialog"
    >
      <el-form ref="formRef" :model="state.formData" label-width="90px">
        <el-descriptions class="margin-top mb-20px" :column="2" size="small" :border="true">
          <el-descriptions-item width="100px">
            <template #label>
              <div class="cell-item">{{ state.type === 'customer' ? '收款' : '付款' }}合计</div>
            </template>
            <div class="font-bold">
              <span class="text-red-500">{{ incomeTotal || '-' }} </span>
              <span class="ml-10px">元</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item width="100px">
            <template #label>
              <div class="cell-item">未税{{ state.type === 'customer' ? '收入' : '支出' }}合计</div>
            </template>
            <div class="font-bold">
              <span>{{ state.outcomeUnTaxed || '-' }} </span>
              <span class="ml-10px">元</span>
            </div>
          </el-descriptions-item>
        </el-descriptions>
        <el-form-item :label="'现金' + (state.type === 'customer' ? '收款' : '付款')" prop="cashAmount">
          <div class="flex items-center w-100%">
            <el-input-number v-model="state.formData.cashAmount" class="w-240px" :min="0" />
            <span class="ml-10px">元</span>
            <div class="ml-20px">占比：{{ new BN(state.formDataRatio.cashRatio).times(100).toString() || '-' }}%</div>
          </div>
        </el-form-item>
        <el-form-item :label="'万金油' + (state.type === 'customer' ? '收款' : '付款')" prop="wjyAmount">
          <div class="flex items-center w-100%">
            <el-input-number v-model="state.formData.wjyAmount" class="w-240px" :min="0" />
            <span class="ml-10px">元</span>
            <div class="ml-20px">占比：{{ new BN(state.formDataRatio.wjyRatio).times(100).toString() || '-' }}%</div>
          </div>
        </el-form-item>
        <el-form-item :label="'迪链' + (state.type === 'customer' ? '收款' : '付款')" prop="dlAmount">
          <div class="flex items-center w-100%">
            <el-input-number v-model="state.formData.dlAmount" class="w-240px" :min="0" />
            <span class="ml-10px">元</span>
            <div class="ml-20px">占比：{{ new BN(state.formDataRatio.dlRatio).times(100).toString() || '-' }}%</div>
          </div>
        </el-form-item>
        <el-form-item :label="'承兑' + (state.type === 'customer' ? '收款' : '付款')" prop="acceptAmount">
          <div class="flex items-center w-100%">
            <el-input-number v-model="state.formData.acceptAmount" class="w-240px" :min="0" />
            <span class="ml-10px">元</span>
            <div class="ml-20px">占比：{{ new BN(state.formDataRatio.acceptRatio).times(100).toString() || '-' }}%</div>
          </div>
        </el-form-item>
        <el-form-item :label="'转账' + (state.type === 'customer' ? '收款' : '付款')" prop="transferAmount">
          <div class="flex items-center w-100%">
            <el-input-number v-model="state.formData.transferAmount" class="w-240px" :min="0" />
            <span class="ml-10px">元</span>
            <div class="ml-20px">占比：{{ new BN(state.formDataRatio.transferRatio).times(100).toString() || '-' }}%</div>
          </div>
        </el-form-item>
      </el-form>
      <div class="mt-20px" v-show="compareIncomeTotal">
        <div class="flex items-center" v-show="compareIncomeTotal === 1">
          <el-icon color="#F56C6C" class="mr-5px"><WarningFilled /></el-icon>
          <el-text type="danger">{{ state.type === 'customer' ? '收款' : '付款' }}合计不能大于收入合计</el-text>
        </div>
        <div class="flex items-center" v-show="compareIncomeTotal === 2">
          <el-icon color="#E6A23C" class="mr-5px"><WarningFilled /></el-icon>
          <el-text type="warning">{{ state.type === 'customer' ? '收款' : '付款' }}合计不能小于收入合计</el-text>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="submitForm(formRef)">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import { FormInstance } from 'element-plus'
  import { debounce } from 'lodash'
  import BigNumber from 'bignumber.js'
  import { globalRequestApi } from '@/api/planManagement'
  const emit = defineEmits(['refresh'])
  const formRef = ref<FormInstance>()
  const state = reactive<any>({
    type: '', //customer:客户结算，carrier:外协结算
    uri: '',
    loading: false,
    dialogVisible: {
      visible: false,
      title: '',
    },
    formData: {
      id: undefined,
      cashAmount: 0,
      wjyAmount: 0,
      dlAmount: 0,
      acceptAmount: 0,
      transferAmount: 0,
    },
    formDataRatio: {
      cashRatio: '',
      wjyRatio: '',
      dlRatio: '',
      acceptRatio: '',
      transferRatio: '',
    },
    outcomeUnTaxed: '',
  })
  const title = computed(() => {
    return state.type === 'customer' ? '收款' : '付款'
  })
  const closeDialog = () => {
    //清空表单
    formRef.value?.resetFields()
    state.formData = {}
    state.formDataRatio.cashRatio = ''
    state.formDataRatio.wjyRatio = ''
    state.formDataRatio.dlRatio = ''
    state.formDataRatio.acceptRatio = ''
    state.formDataRatio.transferRatio = ''
    state.outcomeUnTaxed = ''
    state.dialogVisible.visible = false
  }
  const BN = BigNumber.clone({ DECIMAL_PLACES: 4, ROUNDING_MODE: BigNumber.ROUND_HALF_UP })
  const incomeTotal = computed(() => {
    return new BigNumber(state.formData.cashAmount || 0).plus(
      new BigNumber(state.formData.wjyAmount || 0)
        .plus(
          new BigNumber(state.formData.dlAmount || 0).plus(
            new BigNumber(state.formData.acceptAmount || 0).plus(new BigNumber(state.formData.transferAmount || 0)),
          ),
        )
        .toString(),
    )
  })
  //监听state.formData,根据输入的收款金额，计算对应的占比
  watch(
    () => state.formData,
    () => {
      const total = new BN(incomeTotal.value)
      if (total.isZero()) {
        state.formDataRatio.cashRatio = '0'
        state.formDataRatio.wjyRatio = '0'
        state.formDataRatio.dlRatio = '0'
        state.formDataRatio.acceptRatio = '0'
        state.formDataRatio.transferRatio = '0'
      } else {
        state.formDataRatio.cashRatio = new BN(state.formData.cashAmount || 0).div(total).toString()
        state.formDataRatio.wjyRatio = new BN(state.formData.wjyAmount || 0).div(total).toString()
        state.formDataRatio.dlRatio = new BN(state.formData.dlAmount || 0).div(total).toString()
        state.formDataRatio.acceptRatio = new BN(state.formData.acceptAmount || 0).div(total).toString()
        state.formDataRatio.transferRatio = new BN(state.formData.transferAmount || 0).div(total).toString()
      }
    },
    {
      deep: true,
    },
  )

  // 未税收入合计和收款合计对比，如果收款合计大于未税收入合计，则提示收款合计不能大于未税收入合计
  const compareIncomeTotal = computed(() => {
    if (incomeTotal.value > state.outcomeUnTaxed) {
      return 1
    } else if (incomeTotal.value < state.outcomeUnTaxed) {
      return 2
    } else {
      return 0
    }
  })
  const formatNumber = (value: number | string) => {
    return Number(value).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
  }

  const submitForm = debounce(async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        if (compareIncomeTotal.value) {
          ElMessage({
            message: '收款合计不等于收入合计',
            type: 'error',
          })
          return
        }
        const params = {
          id: state.formData.id,
          cashAmount: state.formData.cashAmount,
          wjyAmount: state.formData.wjyAmount,
          dlAmount: state.formData.dlAmount,
          acceptAmount: state.formData.acceptAmount,
          transferAmount: state.formData.transferAmount,
          cashRatio: state.formDataRatio.cashRatio * 100,
          wjyRatio: state.formDataRatio.wjyRatio * 100,
          dlRatio: state.formDataRatio.dlRatio * 100,
          acceptRatio: state.formDataRatio.acceptRatio * 100,
          transferRatio: state.formDataRatio.transferRatio * 100,
        }
        if (state.uri) {
          await globalRequestApi(params, 'post', state.uri)
          ElMessage({
            message: '修改成功',
            type: 'success',
          })
          closeDialog()
          emit('refresh')
        }
      } else {
      }
    })
  }, 300)

  defineExpose({
    state,
  })
</script>
<style scoped lang="scss"></style>
