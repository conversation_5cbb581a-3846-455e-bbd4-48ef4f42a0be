<!--
 * @Author: llm
 * @Date: 2024-12-27 19:37:32
 * @LastEditors: llm
 * @LastEditTime: 2025-03-28 16:57:06
 * @Description:
-->
<template>
  <div>
    <el-dialog :lock-scroll="true" :draggable="true" v-model="state.dialogVisible" title="发票管理" width="80%" @close="closeDialog">
      <el-form ref="formRef1" :inline="true" :model="state.searchParams">
        <el-descriptions class="margin-top" :column="3" size="small" :border="true">
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">客户名称</div>
            </template>
            <div class="font-bold">
              <span>{{ state.customerName }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">对账单号</div>
            </template>
            <div class="font-bold">
              <span>{{ state.billNo }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">对账人</div>
            </template>
            <div class="font-bold">
              <span>{{ state.reconciler }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">应开票金额</div>
            </template>
            <div class="font-bold">
              <span>{{ state.invoiceAmountRequired }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">已开票金额</div>
            </template>
            <div class="font-bold">
              <span>{{ state.invoicedAmount }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">未开票金额</div>
            </template>
            <div class="font-bold">
              <span>{{ state.notInvoicedMount }}</span>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
      <el-form v-model="state.dialogVisible">
        <br />
        <el-row>
          <el-col :span="12">
            <span><strong>已开发票</strong></span>
          </el-col>
          <el-col :span="12">
            <el-row class="row-bg mb-10px" justify="end">
              <el-button size="small" type="primary" @click="addInvoice">+新增发票</el-button>
            </el-row>
          </el-col>
        </el-row>
        <el-scrollbar max-height="30vh" class="formClass">
          <el-table ref="bindDataRef" show-overflow-tooltip :border="true" :data="state.bindedData" v-loading="state.loading">
            <!-- 序号 -->
            <el-table-column align="center" label="序号" type="index" width="60"></el-table-column>
            <el-table-column label="发票号码" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.invoiceNumber }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="开票日期" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.invoiceDate }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="开票金额(计税)" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.invoiceAmount }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="发票PDF" align="center">
              <template #default="{ row }">
                <div style="display: inline-block">
                  <el-link type="primary" @click="downloadFile(row.fileList[0].url)">
                    {{ row.fileList !== null ? row.fileList[0].name : '' }}
                  </el-link>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="备注" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.remarks }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="开票人" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.invoiceUsername }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-button size="small" type="primary" :disabled="state.loading" @click="unbindInvoice(scope.$index)">解绑</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-scrollbar>
      </el-form>

      <el-form v-model="state.dialogVisible">
        <br />
        <el-row>
          <el-col :span="12">
            <span><strong>可绑定发票</strong></span>
          </el-col>
        </el-row>
        <br />
        <el-scrollbar max-height="30vh" class="formClass">
          <el-table ref="unBindDataRef" show-overflow-tooltip :border="true" :data="state.unbindedData" v-loading="state.loading" height="200">
            <!-- 序号 -->
            <el-table-column label="序号" align="center" type="index" width="60"></el-table-column>
            <el-table-column label="发票号码" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.invoiceNumber }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="开票日期" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.invoiceDate }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="开票金额(计税)" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.invoiceAmount }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="发票PDF" align="center">
              <template #default="{ row }">
                <div style="display: inline-block">
                  <el-link type="primary" @click="downloadFile(row.fileList[0].url)">
                    {{ row.fileList !== null ? row.fileList[0].name : '' }}
                  </el-link>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="备注" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.remarks }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="开票人" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.invoiceUsername }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-button size="small" type="primary" :disabled="state.loading" @click="bindInvoice(scope.$index)">绑定</el-button>
                  <!-- <el-button type="primary"@click="submitForm(tableRef2)">绑定</el-button> -->
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-scrollbar>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="closeDialog">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 新增发票弹窗 -->
    <el-dialog title="新增发票" width="30%" v-model="state.addInvoice">
      <el-form
        ref="ruleFormRef"
        style="max-width: 600px"
        :model="ruleForm"
        :rules="rules"
        label-width="auto"
        class="demo-ruleForm"
        :size="formSize"
        status-icon
      >
        <el-form-item label="对账单号">
          <el-input v-model="state.billNo" :disabled="true" />
        </el-form-item>
        <el-form-item label="未开票金额">
          <el-input v-model="state.notInvoicedMount" :disabled="true" />
        </el-form-item>
        <el-form-item prop="fileList" label="PDF">
          <el-upload
            :file-list="ruleForm.fileList ?? []"
            ref="uploadFileRef"
            action="#"
            :auto-upload="false"
            :limit="1"
            :on-change="handleChange"
            :on-remove="
              (file: any, fileList: any[]) => {
                handleRemove(file, fileList)
              }
            "
            :on-exceed="handleExceed"
          >
            <template #trigger>
              <el-button size="small" type="primary">点击上传</el-button>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="购买方信息" prop="buyerName">
          <el-input v-model="ruleForm.buyerName" />
        </el-form-item>
        <el-form-item label="销售方信息" prop="sellerName">
          <el-input v-model="ruleForm.sellerName" />
        </el-form-item>
        <el-form-item label="发票号码" prop="invoiceNumber">
          <el-input v-model="ruleForm.invoiceNumber" />
        </el-form-item>
        <el-form-item label="发票金额" prop="invoiceAmount">
          <el-input v-model="ruleForm.invoiceAmount" type="number" />
        </el-form-item>
        <el-form-item label="开票日期" prop="invoiceDate">
          <el-date-picker
            v-model="ruleForm.invoiceDate"
            @change="changeInstoreTime"
            value-format="YYYY-MM-DD"
            type="date"
            prop="invoiceDate"
            :disabled-date="disabledDate"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注" prop="rmarks">
          <el-input v-model="ruleForm.remarks" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onCloseAddInvoice">取 消</el-button>
          <el-button type="primary" @click="submitForm(ruleFormRef)">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import {
    postFleetSettlementCustomerInvoicesManagementAllApi,
    postFleetSettlementCustomerInvoicesManagementBindApi,
    postFleetSettlementCustomerInvoicesManagementUnBindApi,
    postFleetSettlementCustomerInvoicesManagementAddInvoiceApi,
    getFleetSettlementCustomerInvoicesManagementByIdApi,
  } from '@/api/financialManagement'
  import { uploadFileApi } from '@/api/auth'
  import { ComponentSize, FormRules, FormInstance, UploadProps, UploadRawFile, genFileId, UploadUserFile } from 'element-plus'
  const emit = defineEmits(['confirmStatementSuccess'])
  const locationOptions = ['Home', 'Company', 'School']
  interface RuleForm {
    billNo: string
    region: string
    invoiceNumber: string
    invoiceAmount: string
    invoiceDate: string
    fileList: UploadUserFile[]
    invoiceUsername: string
    remarks: string
    sellerName: string
    buyerName: string
  }
  const formSize = ref<ComponentSize>('default')
  const ruleFormRef = ref<FormInstance>()
  const ruleForm = reactive<RuleForm>({
    billNo: '',
    region: '',
    invoiceNumber: '',
    invoiceAmount: '',
    invoiceDate: '',
    fileList: [],
    invoiceUsername: '',
    remarks: '',
    buyerName: '',
    sellerName: '',
  })
  const handleChange: UploadProps['onChange'] = (file, files) => {
    if (beforeUpload(file) == true) {
      uploadFile(file, files)
    } else {
      uploadFileRef.value!.clearFiles()
      ruleForm.fileList = []
      ruleForm.invoiceAmount = ''
      ruleForm.invoiceNumber = ''
      ruleForm.invoiceUsername = ''
      ruleForm.invoiceDate = ''
      ruleForm.remarks = ''
      ElMessage.error('文件格式不正确,请上传pdf文件')
    }
  }
  const beforeUpload = (file: any) => {
    let fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
    // 判断文件名的类型，允许多种就判断多个
    if (fileType == 'pdf') {
      return true
    } else {
      return false
    }
  }
  //下载文件
  const downloadFile = (item: string) => {
    window.open(item)
  }
  const changeInstoreTime = (e: string) => {
    state.formData.inStoreTime = e ?? ''
  }
  const disabledDate = (time: Date) => {
    return time.getTime() > Date.now()
  }

  const bindDataRef = ref()
  const unBindDataRef = ref()
  const uploadFileRef = ref()
  const state = reactive<any>({
    loading: false,
    addInvoice: false,
    dialogVisible: false,
    formData: {},
    ids: [] as bigint[],
    id: '',
    bindedData: [],
    unbindedData: [],
    customerId: '',
  })
  const rules = reactive<FormRules<RuleForm>>({
    invoiceNumber: [{ required: true, message: '请填写发票号码', trigger: 'blur' }],
    invoiceAmount: [{ required: true, message: '请填写发票金额', trigger: 'blur' }],
    invoiceDate: [{ required: true, message: '请选择开票日期', trigger: 'blur' }],
    fileList: [{ required: true, message: '请选上传文件', trigger: 'blur' }],
    buyerName: [{ required: true, message: '请填写购买方信息', trigger: 'blur' }],
    sellerName: [{ required: true, message: '请填写销售方信息', trigger: 'blur' }],
  })

  //上传发票
  const uploadFile = async (file: any, fileList: any[]) => {
    if (file.status !== 'ready') return
    const params = {
      file: file.raw,
      businessLine: 204,
    }
    let res = await uploadFileApi('logistics/system/common/invoice/ocr', params) // 此处为自己的上传接口
    ruleForm.invoiceAmount = res.data.totalAmount
    ruleForm.invoiceNumber = res.data.number
    ruleForm.invoiceDate = res.data.dateDesc
    ruleForm.invoiceUsername = res.data.drawer
    ruleForm.fileList = res.data.uploads
    ruleForm.buyerName = res.data.buyerName
    ruleForm.sellerName = res.data.sellerName
  }
  const handleRemove = (file: any, fileList: any[]) => {
    //删除当前文件
    state.attachmentsUrl = ''
    state.attachments = ''
  }
  const handleExceed: UploadProps['onExceed'] = (files) => {
    uploadFileRef.value!.clearFiles()
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    uploadFileRef.value!.handleStart(file)
  }
  //新增发票
  const addInvoice = () => {
    state.addInvoice = true
  }
  const onCloseAddInvoice = () => {
    state.invoiceAmount = ''
    state.invoiceNumber = ''
    state.invoiceUsername = ''
    state.fileList = ''
    state.remarks = ''
    state.invoiceDate = ''
    ruleForm.fileList = []
    ruleForm.invoiceAmount = ''
    ruleForm.invoiceNumber = ''
    ruleForm.invoiceUsername = ''
    ruleForm.invoiceDate = ''
    ruleForm.remarks = ''
    ruleForm.buyerName = ''
    ruleForm.sellerName = ''
    state.addInvoice = false
    state.loading = true
    flushIndex()
  }
  //获取已绑定发票详情
  const postFleetSettlementCustomerInvoicesManagementAll = async (row: {
    id: string
    reconciliationId: string
    customerId: string
    customerName: string
    billNo: string
    reconciler: string
    invoiceAmountRequired: string
    invoicedAmount: string
    notInvoicedMount: string
  }) => {
    state.loading = true
    const { data } = await postFleetSettlementCustomerInvoicesManagementAllApi({ reconciliationId: row.reconciliationId, bindingState: 1 })
    state.customerName = row.customerName
    state.billNo = row.billNo
    state.reconciler = row.reconciler
    state.invoiceAmountRequired = row.invoiceAmountRequired
    state.invoicedAmount = row.invoicedAmount
    state.notInvoicedMount = row.notInvoicedMount
    state.reconciliationId = row.reconciliationId
    state.id = row.id
    state.customerId = row.customerId
    state.bindedData = data
    // debugger
    state.loading = false
  }
  //获取可绑定发票详情
  const postFleetSettlementCustomerInvoicesManagementAvalable = async (row: {
    reconciliationId: string
    customerName: string
    billNo: string
    reconciler: string
    invoiceAmountRequired: string
    invoicedAmount: string
    notInvoicedMount: string
  }) => {
    state.loading = true
    const { data } = await postFleetSettlementCustomerInvoicesManagementAllApi({ bindingState: 0 })
    state.unbindedData = data
    // debugger
    state.loading = false
  }
  const closeDialog = () => {
    //清空表单
    state.ids = []
    state.totalSummary = {}
    state.bindedData = []
    state.unbindedData = []
    state.dialogVisible = false
    state.addInvoice = false
    emit('confirmStatementSuccess')
  }

  const bindInvoice = async (index: number) => {
    try {
      state.loading = true
      const ids = [state.unbindedData[index].id]
      const id = state.id
      const reconciliation = state.reconciliationId
      const params = {
        invoiceIdList: ids,
        reconciliationId: reconciliation,
        id: id,
      }
      const res = await postFleetSettlementCustomerInvoicesManagementBindApi(params)
      state.id = res.data
      flushIndex()
      ElMessage.success('绑定成功')
    } catch (e) {
      flushIndex()
    }
  }

  const unbindInvoice = async (index: number) => {
    try {
      state.loading = true
      const ids = [state.bindedData[index].id]
      const id = state.id
      const reconciliation = state.reconciliationId
      const params = {
        invoiceIdList: ids,
        reconciliationId: reconciliation,
        id: id,
      }
      await postFleetSettlementCustomerInvoicesManagementUnBindApi(params)
      flushIndex()
      ElMessage.success('解绑成功')
    } catch (e) {
      flushIndex()
    }
  }
  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        const params = {
          id: state.id,
          reconciliationId: state.reconciliationId,
          newInvoice: {
            customerId: state.customerId,
            fileList: ruleForm.fileList,
            invoiceNumber: ruleForm.invoiceNumber,
            invoiceAmount: ruleForm.invoiceAmount,
            invoiceDate: ruleForm.invoiceDate,
            remarks: ruleForm.remarks,
            invoiceUsername: ruleForm.invoiceUsername,
            buyerName: ruleForm.buyerName,
            sellerName: ruleForm.sellerName,
          },
        }
        const res = await postFleetSettlementCustomerInvoicesManagementAddInvoiceApi(params)
        state.id = res.data
        onCloseAddInvoice()
        flushIndex()
      } else {
      }
    })
  }
  //刷新页面
  const flushIndex = async () => {
    const reconciliation = state.reconciliationId
    try {
      const { data } = await postFleetSettlementCustomerInvoicesManagementAllApi({ reconciliationId: reconciliation, bindingState: 1 })
      state.bindedData = data
    } catch (e) {
      state.loading = false
    }
    try {
      const { data } = await postFleetSettlementCustomerInvoicesManagementAllApi({ bindingState: 0 })
      state.unbindedData = data
    } catch (e) {
      state.loading = false
    }
    try {
      if (state.id != null) {
        const { data } = await getFleetSettlementCustomerInvoicesManagementByIdApi({ id: state.id })
        state.invoiceAmountRequired = data.invoiceAmountRequired
        state.invoicedAmount = data.invoicedAmount
        state.notInvoicedMount = data.notInvoicedMount
      }
    } catch (e) {
      state.loading = false
    }
    state.loading = false
  }
  defineExpose({
    state,
    postFleetSettlementCustomerInvoicesManagementAll,
    postFleetSettlementCustomerInvoicesManagementAvalable,
  })
</script>
<style scoped lang="scss"></style>
