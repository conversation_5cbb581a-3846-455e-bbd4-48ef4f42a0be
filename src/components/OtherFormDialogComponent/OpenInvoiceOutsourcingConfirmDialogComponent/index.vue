<!--
 * @Author: llm
 * @Date: 2024-12-27 19:37:32
 * @LastEditors: llm
 * @LastEditTime: 2025-04-01 20:21:33
 * @Description:
-->
<template>
  <div>
    <el-dialog :lock-scroll="true" :draggable="true" v-model="state.dialogVisible" title="发票管理" width="80%" @close="closeDialog">
      <el-form ref="formRef1" :inline="true" :model="state.searchParams">
        <el-descriptions class="margin-top" :column="3" size="small" :border="true">
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">承运商</div>
            </template>
            <div class="font-bold">
              <span>{{ state.carrierName }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">结算单号</div>
            </template>
            <div class="font-bold">
              <span>{{ state.settlementNo }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">结算人</div>
            </template>
            <div class="font-bold">
              <span>{{ state.operatorName }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">计税支出</div>
            </template>
            <div class="font-bold">
              <span>{{ state.outcomeIncludeTaxed }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">已开票金额</div>
            </template>
            <div class="font-bold">
              <span>{{ state.haveInvoiceAmount }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">未开票金额</div>
            </template>
            <div class="font-bold">
              <span>{{ state.haveNotInvoicedAmount }}</span>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
      <el-form v-model="state.dialogVisible">
        <br />
        <el-row>
          <el-col :span="12">
            <span><strong>已开发票</strong></span>
          </el-col>
          <el-col :span="12">
            <el-row class="row-bg mb-10px" justify="end">
              <el-button size="small" type="primary" @click="addInvoice">+新增发票</el-button>
            </el-row>
          </el-col>
        </el-row>
        <el-scrollbar max-height="30vh" class="formClass">
          <el-table ref="bindDataRef" show-overflow-tooltip :border="true" :data="state.boundList" v-loading="state.loading">
            <!-- 序号 -->
            <el-table-column align="center" label="序号" type="index" width="60"></el-table-column>
            <el-table-column label="发票号码" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.invoiceNo }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="开票日期" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.doneDate }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="开票金额（计税）" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.invoiceAmount }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="发票PDF" align="center">
              <template #default="{ row }">
                <div style="display: inline-block">
                  <el-link type="primary" @click="downloadFile(invoicePdf(row.invoicePdfUrl))">
                    {{ invoiceName(row.invoicePdfUrl) }}
                  </el-link>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="备注" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.remark }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="开票人" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.operatorName }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-button size="small" type="primary" :disabled="state.loading" @click="unbindInvoice(scope.$index)">解绑</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-scrollbar>
      </el-form>

      <el-form v-model="state.dialogVisible">
        <br />
        <el-row>
          <el-col :span="12">
            <span><strong>可绑定发票</strong></span>
          </el-col>
        </el-row>
        <br />
        <el-scrollbar max-height="30vh" class="formClass">
          <el-table ref="unBindDataRef" show-overflow-tooltip :border="true" :data="state.unBoundList" v-loading="state.loading" height="200">
            <!-- 序号 -->
            <!-- 序号 -->
            <el-table-column align="center" label="序号" type="index" width="60"></el-table-column>
            <el-table-column label="发票号码" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.invoiceNo }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="开票日期" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.doneDate }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="开票金额（计税）" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.invoiceAmount }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="发票PDF" align="center">
              <template #default="{ row }">
                <div style="display: inline-block">
                  <el-link type="primary" @click="downloadFile(invoicePdf(row.invoicePdfUrl))">
                    {{ invoiceName(row.invoicePdfUrl) }}
                  </el-link>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="备注" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.remark }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="开票人" align="center">
              <template #default="{ row }">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-text>{{ row.operatorName }}</el-text>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <div style="display: flex; align-items: center; justify-content: center; width: 100%">
                  <el-button size="small" type="primary" :disabled="state.loading" @click="bindInvoice(scope.$index)">绑定</el-button>
                  <!-- <el-button type="primary"@click="submitForm(tableRef2)">绑定</el-button> -->
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-scrollbar>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 新增发票弹窗 -->
    <el-dialog title="新增发票" width="30%" v-model="state.addInvoice" @close="onCloseAddInvoice">
      <el-form
        ref="ruleFormRef"
        style="max-width: 600px"
        :model="ruleForm"
        :rules="rules"
        label-width="auto"
        class="demo-ruleForm"
        :size="formSize"
        status-icon
      >
        <el-form-item label="结算单号">
          <el-input v-model="state.settlementNo" :disabled="true" />
        </el-form-item>
        <el-form-item label="未开票金额">
          <el-input v-model="state.haveNotInvoicedAmount" :disabled="true" />
        </el-form-item>
        <el-form-item prop="invoicePdfList" label="PDF">
          <el-upload
            :file-list="ruleForm.invoicePdfList ?? []"
            ref="uploadFileRef"
            action="#"
            :auto-upload="false"
            :limit="1"
            :on-change="
              (file: any, fileList: any[]) => {
                uploadFile(file, fileList)
              }
            "
            :on-remove="
              (file: any, fileList: any[]) => {
                handleRemove(file, fileList)
              }
            "
            :on-exceed="handleExceed"
          >
            <template #trigger>
              <el-button size="small" type="primary">点击上传</el-button>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="购买方信息" prop="buyerName">
          <el-input v-model="ruleForm.buyerName" />
        </el-form-item>
        <el-form-item label="销售方信息" prop="sellerName">
          <el-input v-model="ruleForm.sellerName" />
        </el-form-item>
        <el-form-item label="发票号码" prop="invoiceNo">
          <el-input v-model="ruleForm.invoiceNo" />
        </el-form-item>
        <el-form-item label="发票金额" prop="invoiceAmount">
          <el-input-number v-model="ruleForm.invoiceAmount" :precision="2" :min="0" />
        </el-form-item>
        <el-form-item label="开票日期" prop="doneDate">
          <el-date-picker
            v-model="ruleForm.doneDate"
            @change="changeInstoreTime"
            value-format="YYYY-MM-DD"
            type="date"
            prop="invoiceDate"
            :disabled-date="disabledDate"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注" prop="rmarks">
          <el-input v-model="ruleForm.remark" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onCloseAddInvoice">取 消</el-button>
          <el-button type="primary" @click="submitForm(ruleFormRef)">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import {
    postOutsourcingFleetSettlementCustomerInvoicesManagementAllApi,
    postOutsourcingFleetSettlementCustomerInvoicesManagementUnBindApi,
    postOutsourcingFleetSettlementCustomerInvoicesManagementBindApi,
    postOutsourcingFleetSettlementCustomerInvoicesManagementAddInvoiceApi,
  } from '@/api/financialManagement'
  import { uploadFileApi } from '@/api/auth'
  import { ComponentSize, FormRules, FormInstance, UploadProps, UploadRawFile, genFileId, UploadUserFile } from 'element-plus'
  interface RuleForm {
    billNo: string
    region: string
    invoiceNo: string
    invoiceAmount: number
    doneDate: string
    invoicePdfList: UploadUserFile[]
    operatorName: string
    remark: string
    buyerName: string
    sellerName: string
  }
  const formSize = ref<ComponentSize>('default')
  const ruleFormRef = ref<FormInstance>()
  const ruleForm = reactive<RuleForm>({
    billNo: '',
    region: '',
    invoiceNo: '',
    invoiceAmount: 0,
    doneDate: '',
    invoicePdfList: [],
    operatorName: '',
    remark: '',
    buyerName: '',
    sellerName: '',
  })
  //下载文件
  const downloadFile = (item: string) => {
    window.open(item)
  }
  const invoicePdf = (jsonString: string) => {
    try {
      if (jsonString != null) {
        const list = JSON.parse(jsonString)
        if (list.length >= 0) {
          if (list[0].url != null) {
            return list[0].url
          } else {
            return ''
          }
        }
      }
      return ''
    } catch {
      return undefined
    }
  }
  const invoiceName = (jsonString: string) => {
    try {
      if (jsonString != null) {
        const list = JSON.parse(jsonString)
        if (list.length >= 0) {
          if (list[0].name != null) {
            return list[0].name
          } else {
            return ''
          }
        }
      }
      return ''
    } catch {
      return undefined
    }
  }
  const changeInstoreTime = (e: string) => {
    state.formData.inStoreTime = e ?? ''
  }
  const disabledDate = (time: Date) => {
    return time.getTime() > Date.now()
  }
  const bindDataRef = ref()
  const unBindDataRef = ref()
  const uploadFileRef = ref()
  const state = reactive<any>({
    loading: false,
    addInvoice: false,
    dialogVisible: false,
    formData: {},
    ids: [] as bigint[],
    id: '',
    boundList: [],
    unBoundList: [],
    customerId: '',
    settlementNo: '',
    operatorName: '',
    haveInvoiceAmount: '',
    haveNotInvoicedAmount: '',
    carrierName: '',
    outcomeIncludeTaxed: '',
    attachmentsUrl: '',
    carrierId: '',
  })
  const rules = reactive<FormRules<RuleForm>>({
    invoiceNo: [{ required: true, message: '请填写发票号码', trigger: 'blur' }],
    invoiceAmount: [{ required: true, message: '请填写发票金额', trigger: 'blur' }],
    doneDate: [{ required: true, message: '请选择开票日期', trigger: 'blur' }],
    invoicePdfList: [{ required: true, message: '请选上传文件', trigger: 'blur' }],
    buyerName: [{ required: true, message: '请填写购买方信息', trigger: 'blur' }],
    sellerName: [{ required: true, message: '请填写销售方信息', trigger: 'blur' }],
  })

  //上传发票
  const uploadFile = async (file: any, fileList: any[]) => {
    if (file.status !== 'ready') return
    const params = {
      file: file.raw,
      businessLine: 204,
    }
    let res = await uploadFileApi('logistics/system/common/invoice/ocr', params) // 此处为自己的上传接口
    ruleForm.invoiceAmount = res.data.totalAmount
    ruleForm.invoiceNo = res.data.number
    ruleForm.doneDate = res.data.dateDesc
    ruleForm.operatorName = res.data.drawer
    ruleForm.invoicePdfList = res.data.uploads
    ruleForm.buyerName = res.data.buyerName
    ruleForm.sellerName = res.data.sellerName
  }
  const handleRemove = (file: any, fileList: any[]) => {
    //删除当前文件
    // state.value.attachmentsUrl = ''
    // state.value.attachments = ''
    ruleForm.invoicePdfList = []
  }
  const handleExceed: UploadProps['onExceed'] = (files) => {
    uploadFileRef.value!.clearFiles()
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    uploadFileRef.value!.handleStart(file)
  }
  //新增发票
  const addInvoice = () => {
    state.addInvoice = true
  }
  const onCloseAddInvoice = () => {
    state.invoiceAmount = ''
    state.invoiceNumber = ''
    state.invoiceUsername = ''
    state.fileList = ''
    state.remarks = ''
    state.invoiceDate = ''
    ruleForm.billNo = ''
    ruleForm.region = ''
    ruleForm.invoiceNo = ''
    ruleForm.invoiceAmount = 0
    ruleForm.doneDate = ''
    ruleForm.invoicePdfList = []
    ruleForm.operatorName = ''
    ruleForm.remark = ''
    ruleForm.buyerName = ''
    ruleForm.sellerName = ''
    state.addInvoice = false
    state.loading = true
    flushIndex()
  }
  //发票管理详情页
  const postOutsourcingFleetSettlementCustomerInvoicesManagementAll = async (row: { id: string }) => {
    state.loading = true
    const { data } = await postOutsourcingFleetSettlementCustomerInvoicesManagementAllApi({ id: row.id })
    state.openInvoiceId = row.id
    state.carrierId = data.carrierId
    state.carrierName = data.carrierName
    state.operatorName = data.operatorName
    state.settlementNo = data.settlementNo
    state.haveInvoiceAmount = data.haveInvoiceAmount
    state.haveNotInvoicedAmount = data.haveNotInvoicedAmount
    state.outcomeIncludeTaxed = data.outcomeIncludeTaxed
    state.boundList = data.boundList
    state.unBoundList = data.unBoundList
    state.loading = false
  }
  const closeDialog = () => {
    //清空表单
    state.boundList = []
    state.unBoundList = []
    state.dialogVisible = false
    state.addInvoice = false
  }

  const bindInvoice = async (index: number) => {
    try {
      state.loading = true
      const params = {
        id: state.unBoundList[index].id,
        openInvoiceId: state.openInvoiceId,
      }
      const res = await postOutsourcingFleetSettlementCustomerInvoicesManagementBindApi(params)
      flushIndex()
      ElMessage.success('绑定成功')
    } catch (e) {
      flushIndex()
      console.log(e)
    }
  }

  const unbindInvoice = async (index: number) => {
    try {
      state.loading = true
      const params = {
        id: state.boundList[index].id,
        openInvoiceId: state.openInvoiceId,
      }
      await postOutsourcingFleetSettlementCustomerInvoicesManagementUnBindApi(params)
      flushIndex()
      ElMessage.success('解绑成功')
    } catch (e) {
      flushIndex()
      console.log(e)
    }
  }
  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        const params = {
          carrierId: state.carrierId,
          invoicePdfList: ruleForm.invoicePdfList,
          invoiceNo: ruleForm.invoiceNo,
          invoiceAmount: ruleForm.invoiceAmount,
          doneDate: ruleForm.doneDate,
          operatorName: ruleForm.operatorName,
          remark: ruleForm.remark,
          openInvoiceId: state.openInvoiceId,
          buyerName: ruleForm.buyerName,
          sellerName: ruleForm.sellerName,
        }
        await postOutsourcingFleetSettlementCustomerInvoicesManagementAddInvoiceApi(params)
        onCloseAddInvoice()
      } else {
        console.log('error submit!', fields)
      }
    })
  }
  //刷新页面
  const flushIndex = async () => {
    try {
      const { data } = await postOutsourcingFleetSettlementCustomerInvoicesManagementAllApi({ id: state.openInvoiceId })
      state.haveInvoiceAmount = data.haveInvoiceAmount
      state.haveNotInvoicedAmount = data.haveNotInvoicedAmount
      state.boundList = data.boundList
      state.unBoundList = data.unBoundList
    } catch (e) {
      state.loading = false
      console.log(e)
    }
    state.loading = false
  }
  defineExpose({
    state,
    postOutsourcingFleetSettlementCustomerInvoicesManagementAll,
  })
</script>
<style scoped lang="scss"></style>
