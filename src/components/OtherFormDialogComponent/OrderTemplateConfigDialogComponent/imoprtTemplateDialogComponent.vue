<!--
 * @Author: llm
 * @Date: 2025-02-26 17:10:56
 * @LastEditors: llm
 * @LastEditTime: 2025-06-26 10:26:50
 * @Description:
-->
<template>
  <el-dialog
    v-model="state.dialogVisible.visible"
    :title="state.dialogVisible.title"
    width="80%"
    :draggable="true"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-scrollbar max-height="55vh">
      <el-form ref="ruleFormRef" label-width="120px" :model="state.formData" style="width: 100%">
        <el-row>
          <el-col :span="24">
            <el-form-item label="模版名称" prop="name" :rules="{ required: true, message: '请输入模版名称', trigger: 'blur' }">
              <el-input v-model="state.formData.name" style="width: 180px" placeholder="请输入模版名称" />
            </el-form-item>
            <el-form-item label="客户模版" prop="titles" :rules="{ required: true, message: '请上传客户模版', trigger: 'blur' }">
              <upload-file-component
                ref="uploadFileRef"
                :show-file-list="false"
                btnText="点击上传"
                :multiple="false"
                :limit="1"
                :title="state.formData.title"
                tip="支持扩展名：.xlsx .xls"
                accept=".xlsx,.xls"
                @fileData="getFileData"
                @removeFile="removeFile"
              />
            </el-form-item>
            <el-form-item label="导入模版设置" prop="remark">
              <el-table v-loading="state.tableLoading" :data="state.formData.templateFieldList" border stripe style="width: 100%">
                <el-table-column prop="systemField" label="系统模板字段">
                  <template #default="scope">
                    <el-text :type="scope.row.required ? 'danger' : ''">{{ scope.row.systemField }}</el-text>
                  </template>
                </el-table-column>
                <el-table-column prop="customerField" label="客户模板字段">
                  <template #default="scope">
                    <el-select v-model="scope.row.customerField" style="width: 180px" placeholder="请选择客户模板字段">
                      <el-option v-for="(item, index) in state.formData.titles" :key="index" :label="item" :value="item" />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="defaultValue" label="默认值">
                  <template #default="scope">
                    <el-input v-model="scope.row.defaultValue" :disabled="scope.row.required" style="width: 180px" placeholder="请输入默认值" />
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <div class="flex justify-end mt-20px">
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="addTemplate(ruleFormRef)">确定</el-button>
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
  import { getSystemDefaultTemplateField, getTemplateDetailApi, postAddTemplate, putUpdateTemplate } from '@/api/outboundDispatchManagement'
  import UploadFileComponent from '@/components/UploadFileComponent/index.vue'
  const ruleFormRef = ref<any>()
  const emit = defineEmits(['refreshList'])
  const state = reactive({
    id: '',
    tableLoading: false,
    dialogVisible: {
      visible: false,
      title: '',
    },
    formData: {
      title: '',
      name: '',
      titles: [],
      templateFieldList: [] as any[],
    },
    customerTemplateData: [],
  })
  watch(
    () => state.dialogVisible.visible,
    async (newVal) => {
      if (newVal) {
        state.tableLoading = true
        if (state.id) {
          await getTemplateDetail(state.id)
        } else {
          const { data } = await getSystemDefaultTemplateField({})
          data.forEach((item: any) => {
            if (!item.defaultValue && item.required) {
              item.defaultValue = ' '
            }
            return item
          })
          state.formData.templateFieldList = data as any[]
        }
        state.tableLoading = false
      }
    },
  )
  const getFileData = (data: any, file: any) => {
    state.formData.title = file.name
    ElMessage.success('上传成功')
    state.formData.titles = data
    state.formData.templateFieldList = state.formData.templateFieldList.map((item) => ({
      ...item,
      customerField: '',
    }))
  }
  const addTemplate = (ruleFormRef: any) => {
    ruleFormRef.validate(async (valid: boolean) => {
      if (valid) {
        const params = {
          ...state.formData,
          templateFieldList: state.formData.templateFieldList.map((item) => ({
            ...item,
            defaultValue: item.defaultValue?.trim(),
          })),
        } as any
        if (params.id) {
          await putUpdateTemplate(params)
          ElMessage.success('编辑模版成功')
        } else {
          await postAddTemplate(params)
          ElMessage.success('创建模版成功')
        }
        closeDialog()
        emit('refreshList')
      } else {
      }
    })
  }
  const getTemplateDetail = async (id: string) => {
    const { data } = await getTemplateDetailApi({ id })
    state.formData = data as any
  }
  const editTemplate = (row: any) => {}
  const deleteTemplate = (row: any) => {}
  const closeDialog = () => {
    state.formData.templateFieldList = []
    state.formData.titles = []
    state.formData.name = ''
    state.id = ''
    state.dialogVisible.visible = false
    state.formData.title = ''
  }
  const openDialog = () => {
    state.dialogVisible.visible = true
  }
  const removeFile = (fileList: any[]) => {
    state.customerTemplateData = []
  }
  defineExpose({
    state,
    openDialog,
    getTemplateDetail,
  })
</script>
