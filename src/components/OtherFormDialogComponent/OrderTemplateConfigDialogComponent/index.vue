<!--
 * @Author: llm
 * @Date: 2025-02-26 17:10:56
 * @LastEditors: llm
 * @LastEditTime: 2025-06-26 10:18:17
 * @Description: 
-->
<template>
  <div>
    <el-dialog
      v-model="state.dialogVisible.visible"
      :title="state.dialogVisible.title"
      width="80%"
      :draggable="true"
      :close-on-click-modal="false"
      @close="closeDialog"
    >
      <el-button type="primary" @click="addTemplate">创建模版</el-button>
      <el-table v-loading="state.tableLoading" :data="state.tableData" style="width: 100%" border class="mt-20px">
        <el-table-column prop="name" label="模版名称" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="createUserName" label="创建人" />
        <el-table-column prop="remark" label="操作">
          <template #default="scope">
            <el-button size="small" link type="primary" @click="editTemplate(scope.row)">编辑</el-button>
            <el-button size="small" link type="danger" @click="deleteTemplate(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="flex justify-end mt-20px">
        <el-button @click="closeDialog" type="primary">关闭</el-button>
      </div>
    </el-dialog>
    <ImportTemplateDialogComponent ref="importTemplateDialogComponentRef" @refreshList="refreshList" />
  </div>
</template>
<script setup lang="ts">
  import { deleteTemplateApi, getTemplatePageApi } from '@/api/outboundDispatchManagement'
  import ImportTemplateDialogComponent from './imoprtTemplateDialogComponent.vue'
  const importTemplateDialogComponentRef = ref()
  const state = reactive({
    dialogVisible: {
      visible: false,
      title: '',
    },
    tableLoading: false,
    tableData: [],
  })
  watch(
    () => state.dialogVisible.visible,
    async (newVal) => {
      if (newVal) {
        await getTemplateList()
      }
    },
  )
  const addTemplate = () => {
    importTemplateDialogComponentRef.value?.openDialog()
    importTemplateDialogComponentRef.value!.state.dialogVisible.title = '创建模版'
  }
  const editTemplate = (row: any) => {
    importTemplateDialogComponentRef.value?.openDialog()
    importTemplateDialogComponentRef.value!.state.dialogVisible.title = '编辑模版'
    importTemplateDialogComponentRef.value!.state.id = row.id
  }
  const deleteTemplate = (row: any) => {
    ElMessageBox.confirm('确定删除该模版吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      await deleteTemplateApi(row.id)
      ElMessage.success('删除模版成功')
      refreshList()
    })
  }
  const closeDialog = () => {
    state.dialogVisible.visible = false
    state.tableData = []
  }
  const refreshList = async () => {
    await getTemplateList()
  }
  const getTemplateList = async () => {
    state.tableLoading = true
    try {
      const { data } = await getTemplatePageApi({
        pageNum: 1,
        pageSize: 1000,
      })
      state.tableData = data.rows
    } catch (error) {
    } finally {
      state.tableLoading = false
    }
  }
  defineExpose({
    state,
  })
</script>
