<!--
 * @Author: llm
 * @Date: 2024-12-06 14:55:42
 * @LastEditors: llm
 * @LastEditTime: 2025-07-05 15:00:27
 * @Description:
-->
<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="80%" :draggable="true" :close-on-click-modal="false" @close="cancel(ruleFormRef)">
    <div class="flex">
      <el-form ref="ruleFormRef" label-width="120px" :inline="true" :model="state.formData" style="width: 340px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="审批名称" prop="name" :rules="{ required: true, message: '请输入审批名称', trigger: 'blur' }">
              <el-input v-model="state.formData.name" style="width: 180px" placeholder="请输入审批名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应用流程" prop="auditType" :rules="{ required: true, message: '请选择应用流程', trigger: 'change' }">
              <el-select filterable v-model="state.formData!.auditType" style="width: 180px" placeholder="请选择应用流程" @change="selectData">
                <el-option v-for="(item, index) in state.distributeTypeOptions || []" :key="index" :label="item.label" :value="item.value!" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="应用场景" prop="scenarios" :rules="{ required: true, message: '请选择应用场景', trigger: 'change' }">
              <el-select filterable v-model="state.formData!.scenarios" style="width: 180px" placeholder="请选择应用场景" @change="getSelectType">
                <el-option v-for="(item, index) in state.feesTypeSelectOptions || []" :key="index" :label="item.label" :value="item.value!" />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 新增车队非必填 -->
          <el-col :span="24" v-if="state.formData!.scenarios == '报销' || state.formData!.scenarios == '报销付款'">
            <el-form-item :rules="{ required: true, message: '请选择车队', trigger: 'change' }" label="车队" prop="fleetInfo">
              <!-- 编辑模式：单选，新增模式：多选 -->
              <el-select
                filterable
                :multiple="!state.id"
                v-model="state.formData!.fleetInfo"
                style="width: 180px"
                placeholder="请选择车队"
                clearable
                @change="handleFleetChange"
              >
                <el-option v-for="(item, index) in state.FleetTypeOptions || []" :key="index" :label="item.label" :value="item.value!" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input style="width: 180px" :rows="2" type="textarea" v-model="state.formData.remark" placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="width: 100%; max-height: 70vh; overflow: auto; position: relative">
        <FlowDesign ref="processRef" :process="process" />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel(ruleFormRef)">取消</el-button>
        <el-button type="primary" :loading="btnLoading" @click="confirm(ruleFormRef)">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
  import { FormInstance } from 'element-plus'
  import {
    getOutFleetFeesAuditSettingApi,
    distributeSelectApi,
    postOutFleetFeesAuditSettingApi,
    putOutFleetFeesAuditSettingApi,
    getBusinessSelectApi,
  } from '@/api/GlobalMenu/index'
  const emit = defineEmits(['closeDialog', 'outFleetFeesAuditSettingConfirmSubmit'])
  import FlowDesign from '@@/views/flowDesign/index.vue'
  import type { EndNode, FlowNode, StartNode } from '@@/views/flowDesign/nodes/type'

  const btnLoading = ref(false)
  // 流程节点
  const process = ref<FlowNode>({
    id: 'root',
    pid: undefined,
    type: 'start',
    name: '流程开始',
    executionListeners: [],
    formProperties: [],
    next: {
      id: 'end',
      pid: 'root',
      type: 'end',
      name: '流程结束',
      executionListeners: [],
      next: undefined,
    } as EndNode,
  } as StartNode)
  const ruleFormRef = ref<FormInstance>()
  const processRef = ref<any>() // 明确声明 processRef 的类型

  const state = reactive<any>({
    formData: {
      name: '',
      auditType: '',
      scenarios: '',
      remark: '',
      fleetInfo: ['全部'] as any,
    } as FormDataVO,
    feesTypeSelectOptions: '' as string,
    distributeTypeOptions: [] as SelectOptions[],
    FleetTypeOptions: [] as SelectOptions[],
    id: '',
  })
  interface FormDataVO {
    name: string
    auditType: ''
    scenarios: string | null
    remark: ''
    fleetInfo: Array<string> | string
  }
  const props = defineProps({
    /**
     * 弹窗属性
     */
    dialog: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
    processData: {
      type: Object,
      default: () => {
        return {}
      },
    },
  })
  watch(
    () => props.dialog.visible,
    async (val) => {
      if (val) {
        getDistributeTypeOptions()
      }
    },
  )

  const { proxy }: any = getCurrentInstance()

  //获取审批类型下拉
  const getDistributeTypeOptions = async () => {
    let params = {
      selectType: 'flowAuditType',
      needAll: false,
      value: 'name',
    }
    const { data } = await distributeSelectApi(params)
    state.distributeTypeOptions = data
  }
  //获取应用场景下拉
  const getFeesTypeSelectOptionList = async (params: any) => {
    const { data } = await distributeSelectApi(params)
    state.feesTypeSelectOptions = data
    getBusinessSelect()
  }
  const getSelectType = () => {
    //清空车队
    if (state.formData!.scenarios == '报销' || state.formData!.scenarios == '报销付款') {
      state.formData.fleetInfo = ['全部']
      getBusinessSelect()
    } else {
      state.formData.fleetInfo = ''
    }
  }
  // 获取车队下拉
  const getBusinessSelect = async () => {
    const { data } = await getBusinessSelectApi({
      dataSource: '车队-承运商',
      carrierType: '自有',
      value: 'id,name',
      label: 'name',
    })
    // 只在新增模式下添加"全部"选项，编辑模式下不添加
    if (!state.id) {
      data.unshift({
        value: '全部',
        label: '全部',
      })
    }
    state.FleetTypeOptions = data
  }

  // 处理车队选择变化
  const handleFleetChange = (value: any) => {
    // 只在新增模式下处理（多选模式）
    if (!state.id && Array.isArray(value)) {
      // 如果选择了"全部"
      if (value.includes('全部')) {
        // 如果同时选择了其他选项，只保留"全部"
        if (value.length > 1) {
          state.formData.fleetInfo = ['全部']
        }
      } else {
        // 如果选择了其他选项，确保"全部"不在选择中
        state.formData.fleetInfo = value.filter((item: any) => item !== '全部')
      }
    }
  }

  // 选择应用流程
  const selectData = async (value: any) => {
    if (value) {
      let params = {
        selectType: 'flowAuditScene',
        selectSubType: value,
        needAll: true,
        value: 'name',
      }
      getFeesTypeSelectOptionList(params)
      state.formData.scenarios = []
    }
  }

  /**
   *
   * @param formEl 关闭弹窗
   */
  function cancel(formEl: FormInstance | undefined) {
    //清空表单数据
    formEl!.resetFields()
    emit('closeDialog')
  }

  // 提交表单
  const confirm = async (formEl: FormInstance | undefined) => {
    btnLoading.value = true
    if (!formEl) {
      return
    } else {
      btnLoading.value = false
    }
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        if (processRef.value) {
          // 在调用之前，先检查 processRef.value 是否为 null
          processRef.value
            .validateFun()
            .then(async (result: any) => {
              // 校验成功，处理成功逻辑
              // 处理车队信息：编辑模式下是字符串，新增模式下是数组
              const fleetInfoStr = state.id
                ? state.formData.fleetInfo // 编辑模式：单个值
                : Array.isArray(state.formData.fleetInfo)
                  ? state.formData.fleetInfo.join(',') // 新增模式：数组转字符串
                  : state.formData.fleetInfo

              const params = {
                ...state.formData,
                process: process.value,
                scenarios: Array.isArray(state.formData.scenarios) ? state.formData.scenarios[0] : [state.formData.scenarios],
                fleetInfo: fleetInfoStr,
              }
              try {
                let res: any
                if (state.id) {
                  // 编辑
                  res = await putOutFleetFeesAuditSettingApi(params, state.id)
                } else {
                  // 新增
                  res = await postOutFleetFeesAuditSettingApi(params)
                }
                if (res.code === 200) {
                  ElMessage.success(res.message)
                  setTimeout(() => {
                    emit('outFleetFeesAuditSettingConfirmSubmit')
                  }, 10)
                } else {
                  ElMessage.error(res.message)
                }
              } finally {
                btnLoading.value = false
              }
            })
            .catch((errors: any) => {
              // // 校验失败，处理错误信息
              errors.forEach((error: any) => {
                ElMessage.error(error.name + '：' + error.message) // 显示每个错误消息
              })
              return
            })
        }
      } else {
        btnLoading.value = false
      }
    })
  }

  const getDetail = async (type: string, _data: any) => {
    let data = _data ? JSON.parse(JSON.stringify(_data)) : ''
    if (type === 'edit') {
      state.id = data.id
      console.log(data, 'data')

      selectData(data.auditType)
      process.value = data.process
      state.formData.name = data.name
      state.formData.auditType = data.auditType
      state.formData.scenarios = data.scenarios
      state.formData.remark = data.remark
      if (Array.isArray(state.formData.scenarios)) {
        state.formData.scenarios = state.formData.scenarios[0] || null // 取第一个值（按业务需求调整）
      } else {
        state.formData.scenarios = state.formData.scenarios
      }
      // 编辑模式下车队信息为单选，取第一个值
      const fleetArray = data.fleetInfo?.split(',') || []
      state.formData.fleetInfo = fleetArray.length > 0 ? fleetArray[0] : ''
    } else if (type == 'add') {
      state.id = ''
      state.formData = {
        name: '',
        auditType: '',
        scenarios: [],
        remark: '',
        fleetInfo: ['全部'], // 新增模式下车队信息为数组
      }
      process.value = {
        id: 'root',
        pid: undefined,
        type: 'start',
        name: '流程开始',
        executionListeners: [],
        formProperties: [],
        next: {
          id: 'end',
          pid: 'root',
          type: 'end',
          name: '流程结束',
          executionListeners: [],
          next: undefined,
        } as EndNode,
      } as StartNode
      //清除表单
      ruleFormRef.value ? ruleFormRef.value!.resetFields() : null
    } else if (type === 'copy') {
      state.id = ''
      state.formData.name = ''
      state.formData.auditType = ''
      state.formData.scenarios = []
      state.formData.remark = ''
      state.formData.fleetInfo = ['全部'] // 复制模式下车队信息为数组
      selectData('')
      process.value = data.process
    }
  }

  defineExpose({
    ruleFormRef,
    getDetail,
  })
</script>
