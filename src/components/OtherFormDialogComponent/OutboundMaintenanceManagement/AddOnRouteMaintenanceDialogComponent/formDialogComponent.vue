<!--
 * @Author: llm
 * @Date: 2025-06-19 11:15:25
 * @LastEditors: llm
 * @LastEditTime: 2025-06-24 17:07:56
 * @Description: 
-->
<template>
  <el-dialog
    v-model="state.dialogVisible.visible"
    :title="state.dialogVisible.title"
    width="500px"
    :close-on-click-modal="false"
    @close="handleCancel"
    draggable
  >
    <el-scrollbar style="height: 260px; padding: 20px 10px">
      <el-form ref="formRef" :rules="state.rules" :model="state.form" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="维修项目" prop="itemName">
              <el-select v-model="state.form.itemName" placeholder="请选择维修项目" :disabled="isEdit">
                <el-option v-for="item in repairItemList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="维修费" prop="amount">
              <el-input v-model="state.form.amount" type="number" min="0" placeholder="请输入维修费" :disabled="isEdit" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="state.form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
  import { FormInstance } from 'element-plus'
  const props = defineProps<{
    repairItemList: SelectOptions[]
    isEdit: boolean
  }>()
  const validateMaintenanceFee = (rule: any, value: any, callback: any) => {
    if (value < 0) {
      callback(new Error('维修费不能小于0'))
    } else {
      callback()
    }
  }
  const state = reactive({
    dialogVisible: {
      visible: false,
      title: '',
    },
    form: {
      idd: undefined as string | undefined,
      itemName: '',
      amount: '',
      remark: '',
    },
    rules: {
      itemName: [{ required: true, message: '请选择维修项目', trigger: 'change' }],
      amount: [
        { required: true, message: '请输入维修费', trigger: 'blur' },
        { validator: validateMaintenanceFee, trigger: 'blur' },
      ],
    },
  })
  const formRef = ref<FormInstance>()
  const emit = defineEmits(['addRepairItem'])

  const handleCancel = () => {
    state.form = {
      idd: undefined as string | undefined,
      itemName: '',
      amount: '',
      remark: '',
    }
    formRef.value?.resetFields()
    state.dialogVisible.visible = false
  }
  const handleSubmit = () => {
    formRef.value?.validate((valid) => {
      if (valid) {
        //如果state.form.id不存在，则根据时间戳创建id
        if (!state.form.idd) {
          state.form.idd = Date.now().toString()
        }
        emit('addRepairItem', state.form)
        handleCancel()
      }
    })
  }
  defineExpose({
    state,
    handleCancel,
  })
</script>
