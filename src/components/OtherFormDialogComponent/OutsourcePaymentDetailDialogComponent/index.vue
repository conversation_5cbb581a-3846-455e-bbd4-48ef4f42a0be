<!--
 * @Author: llm
 * @Date: 2025-02-04 10:40:46
 * @LastEditors: llm
 * @LastEditTime: 2025-02-17 11:23:57
 * @Description: 外协结算-付款管理-已支付金额明细
-->
<template>
  <div>
    <el-dialog v-model="state.dialogVisible.visible" :title="state.dialogVisible.title" width="800px" @close="handleClose">
      <el-scrollbar max-height="65vh">
        <div>
          <div class="font-bold text-lg mb-10px">
            已付款金额：<span class="text-red-500">{{ state.havePaymentAmount || 0 }}</span
            >元
          </div>
          <el-table max-height="50vh" :data="state.tableData" style="width: 100%" border>
            <!-- 序号 -->
            <el-table-column fixed="left" align="center" type="index" width="70">
              <template #header>
                <div style="display: flex; align-items: center">序号</div>
              </template>
              <template #default="scope">
                <div style="display: flex; align-items: center">{{ scope.$index + 1 }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="paymentAmount" label="付款金额" />
            <el-table-column prop="paymentDate" label="付款时间" />
            <el-table-column prop="paymentMethod" label="付款方式" />
            <el-table-column prop="invoiceUrlsList" label="付款凭证">
              <template #default="scope">
                <el-image
                  style="width: 50px; height: 50px"
                  v-for="url in scope.row.invoiceUrlsList ?? []"
                  :src="url.url"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="scope.row.invoiceUrlsList?.map((item: any) => item.url) ?? []"
                  :preview-teleported="true"
                  fit="cover"
                />
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" />
          </el-table>
        </div>
      </el-scrollbar>
      <template #footer>
        <div style="display: flex; justify-content: flex-end">
          <el-button type="primary" @click="handleClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import { postOutFleetOrderCarrierCostPaymentPartAllApi } from '@/api/financialManagement'

  const state = reactive({
    havePaymentAmount: 0, //打款金额
    id: '',
    dialogVisible: {
      visible: false,
      title: '打款明细',
    },
    tableData: [] as any[],
  })
  const getDetail = async (id: string) => {
    state.id = id
    const { data } = await postOutFleetOrderCarrierCostPaymentPartAllApi({ paymentId: id })
    state.tableData = data as any[]
  }
  const handleClose = () => {
    state.dialogVisible.visible = false
    state.id = ''
  }
  defineExpose({
    state,
    getDetail,
  })
</script>
