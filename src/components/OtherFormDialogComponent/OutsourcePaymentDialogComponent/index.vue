<!--
 * @Author: llm
 * @Date: 2025-02-04 10:40:46
 * @LastEditors: llm
 * @LastEditTime: 2025-04-11 10:54:58
 * @Description: 外协结算-付款管理-付款信息
-->
<template>
  <div>
    <el-dialog :close-on-click-modal="false" v-model="state.dialogVisible.visible" :title="state.dialogVisible.title" width="800px" @close="handleClose">
      <el-scrollbar height="70vh">
        <div>
          <el-card>
            <el-form :model="state.formData" label-width="120px">
              <el-form-item label="结算单号" prop="settlementNo">
                <el-input v-model="state.formData.settlementNo" :disabled="true" style="width: 200px" />
              </el-form-item>
              <el-row>
                <el-col :span="7">
                  <el-form-item label="现金付款合计" prop="cashOutcomeIncludeTaxed">
                    <el-input v-model="state.formData.cashOutcomeIncludeTaxed" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="已付款金额" prop="cashHavePaymentAmount">
                    <el-input v-model="state.formData.cashHavePaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="未付款金额" prop="cashHaveNotPaymentAmount">
                    <el-input v-model="state.formData.cashHaveNotPaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item label="" prop="" label-width="20">
                    <el-link
                      :type="state.paymentType === 1 ? 'info' : 'primary'"
                      :disabled="state.paymentType === 1"
                      @click="handlePayment(1)"
                      :underline="false"
                      >打款</el-link
                    >
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="7">
                  <el-form-item label="万金油付款合计" prop="wjyOutcomeIncludeTaxed">
                    <el-input v-model="state.formData.wjyOutcomeIncludeTaxed" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="已付款金额" prop="wjyHavePaymentAmount">
                    <el-input v-model="state.formData.wjyHavePaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="未付款金额" prop="wjyHaveNotPaymentAmount">
                    <el-input v-model="state.formData.wjyHaveNotPaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item label="" prop="paymentAmount" label-width="20">
                    <el-link
                      :type="state.paymentType === 2 ? 'info' : 'primary'"
                      :disabled="state.paymentType === 2"
                      @click="handlePayment(2)"
                      :underline="false"
                      >打款</el-link
                    >
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="7">
                  <el-form-item label="开票付款合计" prop="invoiceOutcomeIncludeTaxed">
                    <el-input v-model="state.formData.invoiceOutcomeIncludeTaxed" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="已付款金额" prop="invoiceHavePaymentAmount">
                    <el-input v-model="state.formData.invoiceHavePaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="未付款金额" prop="invoiceHaveNotPaymentAmount">
                    <el-input v-model="state.formData.invoiceHaveNotPaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item label="" prop="paymentAmount" label-width="20">
                    <el-link
                      :type="state.paymentType === 3 ? 'info' : 'primary'"
                      :disabled="state.paymentType === 3"
                      @click="handlePayment(3)"
                      :underline="false"
                      >打款</el-link
                    >
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-card>
          <el-card
            :header="state.paymentType === 1 ? '现金打款' : state.paymentType === 2 ? '万金油打款' : '开票打款'"
            style="margin-top: 10px"
            v-if="state.paymentType === 1 || state.paymentType === 2 || state.paymentType === 3"
          >
            <el-form
              ref="paymentCashFormRef"
              :rules="rules"
              :model="state.paymentCashFormData"
              label-width="120px"
              v-if="state.paymentType === 1 || state.paymentType === 2"
            >
              <el-form-item label="打款金额" prop="paymentAmount">
                <el-input v-model="state.paymentCashFormData.paymentAmount" type="number" autocomplete="off" />
              </el-form-item>
              <el-form-item label="打款时间" prop="paymentDate">
                <el-date-picker v-model="state.paymentCashFormData.paymentDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD" autocomplete="off" />
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input v-model="state.paymentCashFormData.remark" type="textarea" autocomplete="off" />
              </el-form-item>
              <el-form-item label="上传打款凭证" prop="invoiceUrls">
                <UploadImageComponent ref="uploadImageRef" tip="支持图片格式" />
              </el-form-item>
            </el-form>
            <el-form ref="paymentInvoiceFormRef" :rules="rules" :model="state.paymentInvoiceFormData" label-width="120px" v-if="state.paymentType === 3">
              <el-form-item label="关联发票" prop="invoiceId">
                <el-select v-model="state.paymentInvoiceFormData.invoiceId" placeholder="请选择关联发票">
                  <el-option v-for="item in state.invoiceList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="打款方式" prop="paymentMethod">
                <el-select v-model="state.paymentInvoiceFormData.paymentMethod" placeholder="请选择打款方式">
                  <el-option v-for="item in paymentTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="打款金额" prop="paymentAmount">
                <el-input v-model="state.paymentInvoiceFormData.paymentAmount" type="number" autocomplete="off" />
              </el-form-item>
              <el-form-item label="打款时间" prop="paymentDate">
                <el-date-picker
                  v-model="state.paymentInvoiceFormData.paymentDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  autocomplete="off"
                />
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input v-model="state.paymentInvoiceFormData.remark" type="textarea" autocomplete="off" />
              </el-form-item>
              <el-form-item label="上传打款凭证" prop="invoiceUrls">
                <UploadImageComponent ref="uploadImageRef" tip="支持图片格式" />
              </el-form-item>
            </el-form>
            <div style="text-align: center">
              <el-button type="primary" @click="handleSave">保存</el-button>
            </div>
          </el-card>
        </div>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import {
    getOutFleetOrderCarrierCostPaymentDetailApi,
    getOutFleetOrderCarrierCostPaymentInvoiceListApi,
    postOutFleetOrderCarrierCostPaymentPartApi,
  } from '@/api/financialManagement'
  import UploadImageComponent from '@/components/UploadImageComponent/index.vue'
  import { paymentTypeOptions } from '@/utils/selectOptions'
  import { UploadUserFile } from 'element-plus'
  const uploadImageRef = ref()
  const paymentCashFormRef = ref()
  const paymentInvoiceFormRef = ref()
  const emit = defineEmits(['closeDialog'])
  const state = reactive({
    noPaymentAmount: '', //未付款金额
    invoiceList: [] as any[], //发票列表
    paymentType: 1,
    dialogVisible: {
      visible: false,
      title: '编辑打款信息',
    },
    origin: 'add',
    formData: {
      settlementNo: '',
      cashOutcomeIncludeTaxed: '', //现金付款合计
      cashHavePaymentAmount: '', //现金已经付款合计
      cashHaveNotPaymentAmount: '', //现金暂未付款合计
      wjyOutcomeIncludeTaxed: '', //万金油付款合计
      wjyHavePaymentAmount: '', //万金油已经付款合计
      wjyHaveNotPaymentAmount: '', //万金油暂未付款合计
      invoiceOutcomeIncludeTaxed: '', //开票付款合计
      invoiceHavePaymentAmount: '', //开票已经付款合计
      invoiceHaveNotPaymentAmount: '', //开票暂未付款合计
    },
    id: '',
    paymentCashFormData: {
      paymentAmount: '',
      paymentDate: '',
      remark: '',
      paymentMethod: '现金打款',
      paymentId: '',
      invoiceUrlsList: [],
    },
    paymentInvoiceFormData: {
      paymentAmount: '',
      paymentDate: '',
      remark: '',
      paymentMethod: '',
      paymentId: '',
      invoiceUrlsList: [],
      invoiceId: '',
    },
  })
  const validatePaymentAmount = (rule: any, value: any, callback: any) => {
    let notPaymentAmount = 0
    if (state.paymentType === 1) {
      notPaymentAmount = Number(state.formData.cashHaveNotPaymentAmount)
    } else if (state.paymentType === 2) {
      notPaymentAmount = Number(state.formData.wjyHaveNotPaymentAmount)
    } else if (state.paymentType === 3) {
      notPaymentAmount = Number(state.formData.invoiceHaveNotPaymentAmount)
    }
    if (value === '') {
      callback(new Error('请输入打款金额'))
    } else if (value > notPaymentAmount) {
      callback(new Error('打款金额不能大于未付款金额'))
    } else {
      callback()
    }
  }
  const rules = {
    paymentAmount: [
      { required: true, message: '请输入打款金额', trigger: 'blur' },
      { validator: validatePaymentAmount, trigger: 'blur' },
    ],
    paymentDate: [{ required: true, message: '请选择打款时间', trigger: 'change' }],
    paymentMethod: [{ required: true, message: '请选择打款方式', trigger: 'change' }],
    invoiceId: [{ required: true, message: '请选择关联发票', trigger: 'change' }],
  }
  const getInvoiceList = async (id: string) => {
    const { data } = await getOutFleetOrderCarrierCostPaymentInvoiceListApi({ id: state.id })
    state.invoiceList = data as SelectVO[]
  }
  // 获取上传图片列表
  const getUploadImageList = () => {
    return uploadImageRef.value.uploadImageList
  }
  //设置上传图片列表
  const setUploadImageList = (list: UploadUserFile[]) => {
    uploadImageRef.value.uploadImageList = list
  }
  const getDetail = async (id: string) => {
    state.id = id
    const { data } = await getOutFleetOrderCarrierCostPaymentDetailApi({ id })
    state.formData = data as any
  }
  const handlePayment = (type: number) => {
    if (paymentCashFormRef.value) {
      paymentCashFormRef.value.resetFields()
    }
    if (paymentInvoiceFormRef.value) {
      paymentInvoiceFormRef.value.resetFields()
    }
    state.paymentCashFormData.invoiceUrlsList = []
    state.paymentInvoiceFormData.invoiceUrlsList = []
    state.paymentType = type
    switch (type) {
      case 1:
        state.paymentCashFormData.paymentMethod = '现金打款'
        break
      case 2:
        state.paymentCashFormData.paymentMethod = '万金油打款'
        break
      case 3:
        getInvoiceList(state.id)
        break
    }
  }
  const handleSave = () => {
    if (state.paymentType === 1 || state.paymentType === 2) {
      state.paymentCashFormData.invoiceUrlsList = getUploadImageList()
      state.paymentCashFormData.paymentId = state.id
      paymentCashFormRef.value.validate(async (valid: boolean) => {
        if (valid) {
          try {
            // 保存
            const { data } = await postOutFleetOrderCarrierCostPaymentPartApi(state.paymentCashFormData)
            ElMessage.success('保存成功')
            //重置表单
            paymentCashFormRef.value.resetFields()
            setUploadImageList([])
            getDetail(state.id)
          } catch (error) {}
        } else {
          return false
        }
      })
    } else {
      state.paymentInvoiceFormData.invoiceUrlsList = getUploadImageList()
      state.paymentInvoiceFormData.paymentId = state.id
      paymentInvoiceFormRef.value.validate(async (valid: boolean) => {
        if (valid) {
          try {
            const { data } = await postOutFleetOrderCarrierCostPaymentPartApi(state.paymentInvoiceFormData)
            ElMessage.success('保存成功')
            //重置表单
            paymentInvoiceFormRef.value.resetFields()
            setUploadImageList([])
            getDetail(state.id)
          } catch (error) {}
        }
      })
    }
  }
  const handleClose = () => {
    if (state.paymentType === 3) {
      paymentInvoiceFormRef.value.resetFields()
    } else {
      paymentCashFormRef.value.resetFields()
    }
    state.paymentType = 1
    state.paymentCashFormData.invoiceUrlsList = []
    state.paymentInvoiceFormData.invoiceUrlsList = []
    setUploadImageList([])
    emit('closeDialog')
  }
  defineExpose({
    state,
    getDetail,
  })
</script>
