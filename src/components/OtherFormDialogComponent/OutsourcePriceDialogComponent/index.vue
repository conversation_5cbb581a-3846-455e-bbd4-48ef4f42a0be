<!--
 * @Author: llm
 * @Date: 2025-02-04 10:40:46
 * @LastEditors: llm
 * @LastEditTime: 2025-06-03 18:28:11
 * @Description: 外协管理-外协合同-阶梯价格
-->
<template>
  <div>
    <el-dialog :close-on-click-modal="false" v-model="state.dialogVisible.visible" :title="state.dialogVisible.title" width="800px" @close="handleClose">
      <el-scrollbar max-height="50vh">
        <div>
          <!-- <el-card> -->
          <el-form ref="ModelForm" label-width="0" :model="formList" :rules="rules">
            <el-text size="large">阶梯价计算规则</el-text>
            <el-form-item label="阶梯价计算方式" class="mt-10px">
              <el-radio-group v-model="ladderPriceCalculationType" @change="TableChange">
                <el-radio v-for="item in ladderPriceCalculationTypeList" :key="item.value" :value="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- ······························ -->
            <el-row style="width: 100%" :gutter="20">
              <el-col :span="2">
                <div class="text-center">
                  <el-text>{{ title.t1 }}</el-text>
                </div>
              </el-col>
              <el-col :span="5">
                <div class="text-center">
                  <el-text>{{ title.t2 }}</el-text>
                </div>
              </el-col>
              <el-col :span="5">
                <div class="text-center">
                  <el-text>{{ title.t3 }}</el-text>
                </div>
              </el-col>
              <el-col :span="5">
                <div class="text-center">
                  <el-text>{{ title.t4 }}</el-text>
                </div>
              </el-col>
              <el-col :span="5">
                <div class="text-center">
                  <el-text>{{ title.t5 }}</el-text>
                </div>
              </el-col>
              <el-col :span="2">
                <div class="text-center">
                  <el-text>{{ title.t6 }}</el-text>
                </div>
              </el-col>
            </el-row>
            <el-row style="margin-top: 10px; width: 100%" :gutter="20">
              <el-col :span="2">
                <div class="text-center">
                  <el-text>开始</el-text>
                </div>
              </el-col>
              <el-col :span="5">
                <el-form-item :prop="'index[0].minKm'" :rules="rules['index[0].minKm']">
                  <el-input v-model="formList.index[0].minKm" placeholder="" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item :prop="'index[0].maxKm'" :rules="rules['index[0].maxKm']">
                  <el-input v-model="formList.index[0].maxKm" placeholder=""></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item :prop="'index[0].price'" :rules="rules['index[0].price']">
                  <el-input v-model="formList.index[0].price" placeholder="" :disabled="isStartSectionPriceDisabled"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item :prop="'index[0].unitPrice'" :rules="rules['index[0].unitPrice']">
                  <el-input v-model="formList.index[0].unitPrice" placeholder="" :disabled="isStartSectionUnitPriceDisabled"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="2">
                <div class="flex items-center">
                  <el-button v-if="formListAdd.length === 0" type="primary" circle style="color: white" @click="addFirst" icon="Plus" size="small"></el-button>
                </div>
              </el-col>
            </el-row>
            <!-- ·········额外添加·················· -->
            <el-form ref="form" :model="formListAdd" :rules="rules" label-width="0">
              <template v-for="(item, index) in formListAdd" :key="index">
                <el-row style="width: 100%" :gutter="20">
                  <el-col :span="2">
                    <div class="text-center">
                      <el-text>区间{{ index + 1 }}</el-text>
                    </div>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item :prop="`${index}.minKm`" :rules="getRulemin(index)">
                      <el-input v-model="item.minKm" placeholder=""></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <!--   rules="getRule(index)" 动态生成规则 -->
                    <el-form-item :prop="`${index}.maxKm`" :rules="getRule(index)">
                      <el-input v-model="item.maxKm" placeholder=""></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item :prop="`${index}.price`" :rules="rules[`index[1].price`]">
                      <el-input v-model="item.price" placeholder="" :disabled="isOtherSectionPriceDisabled"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item :prop="`${index}.unitPrice`" :rules="rules[`index[1].unitPrice`]">
                      <el-input v-model="item.unitPrice" placeholder="" :disabled="isOtherSectionUnitPriceDisabled"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="2">
                    <div class="flex items-center">
                      <el-button type="danger" circle style="color: white" @click="del(index)" icon="Minus" size="small"></el-button>
                      <el-button
                        type="primary"
                        circle
                        style="color: white"
                        v-if="index === formListAdd.length - 1"
                        @click="add(index)"
                        icon="Plus"
                        size="small"
                      ></el-button>
                    </div>
                  </el-col>
                </el-row>
              </template>
            </el-form>

            <el-row style="width: 100%" :gutter="20">
              <el-col :span="2">
                <div class="text-center">
                  <el-text>结束</el-text>
                </div>
              </el-col>
              <el-col :span="5">
                <el-form-item :prop="`index.${formList.index.length - 1}.minKm`" :rules="getRuleminKm(formListAdd.length - 1)">
                  <el-input v-model="formList.index[formList.index.length - 1].minKm" placeholder=""></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item :prop="`index.${formList.index.length - 1}.maxKm`" :rules="rules[`index.${formList.index.length - 1}.maxKm`]">
                  <el-input v-model="formList.index[formList.index.length - 1].maxKm" placeholder="" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item :prop="`index.${formList.index.length - 1}.price`" :rules="rules[`index.${formList.index.length - 1}.price`]">
                  <el-input v-model="formList.index[formList.index.length - 1].price" placeholder="" :disabled="isOtherSectionPriceDisabled"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item :prop="`index.${formList.index.length - 1}.unitPrice`" :rules="rules[`index.${formList.index.length - 1}.unitPrice`]">
                  <el-input v-model="formList.index[formList.index.length - 1].unitPrice" placeholder="" :disabled="isOtherSectionUnitPriceDisabled"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row style="width: 100%" :gutter="20">
              <el-col :span="2">
                <div class="text-center">
                  <el-text>备注</el-text>
                </div></el-col
              >
              <el-col :span="20">
                <el-form-item label="">
                  <el-input v-model="formList.remark" type="textarea" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="ok">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import { postOutsourcePriceApi, getOutsourcePriceEchoApi, getLadderPriceRuleOptionApi } from '@/api/auth'
  import { number } from 'echarts'
  import { ElMessage } from 'element-plus'
  import type { FormItemRule } from 'element-plus'

  interface ValidationRule extends FormItemRule {
    validator?: (rule: any, value: string | number, callback: (error?: Error) => void) => void
  }

  const state = reactive({
    dialogVisible: {
      visible: false,
      title: '阶梯价格',
    },
    formData: {
      itemList: [],
    },
  })

  interface LadderPriceSection {
    minKm: string
    maxKm: string
    price: string
    unitPrice: string
    str: string
    title: string
    minRegion: string
    maxRegion: string
  }
  const row = ref<any>({})
  const ladderPriceCalculationType = ref<string>('3')
  const emit = defineEmits(['refresh'])
  watch(
    () => state.dialogVisible.visible,
    (newVal) => {
      if (newVal) {
        console.log(row.value, 'newVal---------')
        formList.value.remark = row.value.remark
        ladderPriceCalculationType.value = row.value.ladderPriceCalculationType || '3' // 默认选择里程累进计算
      }
    },
    { immediate: true },
  )

  const ladderPriceCalculationTypeList = ref<any>([])

  const showButton = ref<boolean>(false)
  const title = ref<any>({
    t1: '',
    t2: '开始里程',
    t3: '结束里程(含)',
    t4: '起步价(元)',
    t5: '单价(元/台/公里)',
    t6: '',
  })
  const formList = ref<{
    index: LadderPriceSection[]
    contractId: string
    contractDetailId: string
    remark: string
  }>({
    index: [
      { minKm: '', maxKm: '', price: '', unitPrice: '', str: 'km', title: '开始', minRegion: '', maxRegion: '<=' },
      { minKm: '', maxKm: '', price: '', unitPrice: '', str: 'km', title: '中间', minRegion: '<', maxRegion: '<=' },
      { minKm: '', maxKm: '', price: '', unitPrice: '', str: 'km', title: '结束', minRegion: '>', maxRegion: '' },
    ],
    remark: '',
    contractId: '',
    contractDetailId: '',
  })
  const formListAdd = ref<LadderPriceSection[]>([])

  const Validate = ref<any>([])

  // 验证函数
  const validateMinKm = (rule: any, value: string | number, callback: (error?: Error) => void) => {
    if (Number(value) < 0) {
      Validate.value[0] = false
      callback(new Error('里程不能小于0'))
    } else {
      Validate.value[0] = true
      callback()
    }
  }

  const validateMaxKm = (rule: any, value: string | number, callback: (error?: Error) => void) => {
    const minKm = formList.value.index[0].minKm
    if (Number(value) <= Number(minKm)) {
      Validate.value[1] = false
      callback(new Error('结束里程不能小于等于开始里程'))
    } else {
      Validate.value[1] = true
      callback()
    }
  }

  const validateMaxKms = async (rule: any, value: string | number, callback: (error?: Error) => void, index: number) => {
    // const minKm = formList.value.index[0].minKm
    await nextTick()
    const minKm = formListAdd.value[index].minKm
    // console.log('88888',formList.value.index[index].maxKm );
    if (Number(value) < Number(minKm)) {
      Validate.value[2] = false
      callback(new Error('结束里程不能小于开始里程'))
    } else {
      Validate.value[2] = true
      callback()
    }
  }

  const validateMinKms = async (rule: any, value: string | number, callback: (error?: Error) => void, index: number) => {
    // const minKm = formList.value.index[0].minKm
    await nextTick()
    console.log(formListAdd.value, '[[[[]]]]', value)
    let minKm = ''
    if (!formListAdd.value.length) {
      minKm = formList.value.index[0].maxKm
    } else {
      minKm = formListAdd.value[index].maxKm
    }

    if (Number(value) < Number(minKm)) {
      Validate.value[3] = false
      callback(new Error('里程区间不能有重叠'))
    } else if (Number(value) > Number(minKm)) {
      Validate.value[3] = false
      callback(new Error('请补全缺失里程区间'))
    } else {
      Validate.value[3] = true
      callback()
    }
  }

  const validateMin = async (rule: any, value: string | number, callback: (error?: Error) => void, index: number) => {
    // const minKm = formList.value.index[0].minKm
    await nextTick()

    let minKm = ''
    if (index === 0) {
      minKm = formList.value.index[index].maxKm
    } else {
      minKm = formListAdd.value[index - 1].maxKm
    }

    console.log(minKm, '[[[[1111]]]]', value)
    if (Number(value) < Number(minKm)) {
      Validate.value[4] = false
      callback(new Error('请补全缺失里程区间！'))
    } else {
      Validate.value[4] = true
      callback()
    }
  }

  const validatePrice = (rule: any, value: string | number, callback: (error?: Error) => void) => {
    if (Number(value) < 0) {
      callback(new Error('价格不能小于0'))
    } else if (!/^\d+(\.\d{1,2})?$/.test(String(value))) {
      callback(new Error('价格最多保留两位小数'))
    } else {
      callback()
    }
  }

  const validateUnitPrice = (rule: any, value: string | number, callback: (error?: Error) => void) => {
    if (Number(value) < 0) {
      callback(new Error('单价不能小于0'))
    } else if (!/^\d+(\.\d{1,2})?$/.test(String(value))) {
      callback(new Error('单价最多保留两位小数'))
    } else {
      callback()
    }
  }

  const rules = {
    'index[0].minKm': [
      { required: true, message: '请输入里程', trigger: ['blur'] },
      { validator: validateMinKm, trigger: 'blur' },
    ],
    'index[0].maxKm': [
      { required: true, message: '请输入里程', trigger: 'blur' },
      { validator: validateMaxKm, trigger: 'blur' },
    ],
    'index[0].price': [
      { required: true, message: '请输入价格', trigger: 'blur' },
      {
        type: 'number',
        transform(value) {
          return Number(value)
        },
        message: '价格必须为数字',
      },
      { validator: validatePrice, trigger: 'blur' },
    ],
    'index[0].unitPrice': [
      { required: true, message: '请输入单价', trigger: 'blur' },
      {
        type: 'number',
        transform(value) {
          return Number(value)
        },
        message: '单价必须为数字',
      },
      { validator: validateUnitPrice, trigger: 'blur' },
    ],
    'index[1].minKm': [
      { required: true, message: '请输入里程', trigger: ['blur'] },
      { validator: validateMinKm, trigger: 'blur' },
    ],
    'index[1].maxKm': [
      { required: true, message: '请输入里程', trigger: 'blur' },
      { validator: validateMaxKm, trigger: 'blur' },
    ],
    'index[1].price': [
      { required: true, message: '请输入价格', trigger: 'blur' },
      {
        type: 'number',
        transform(value) {
          return Number(value)
        },
        message: '价格必须为数字',
      },
      { validator: validatePrice, trigger: 'blur' },
    ],
    'index[1].unitPrice': [
      { required: true, message: '请输入单价', trigger: 'blur' },
      {
        type: 'number',
        transform(value) {
          return Number(value)
        },
        message: '单价必须为数字',
      },
      { validator: validateUnitPrice, trigger: 'blur' },
    ],
  } as { [key: string]: ValidationRule[] }

  const getRule = (index: number) => {
    return [
      { required: true, message: '请输入里程', trigger: 'blur' },
      {
        validator: (rule: any, value: string | number, callback: (error?: Error) => void) => {
          validateMaxKms(rule, value, callback, index) // 传递索引
        },
        trigger: 'blur',
      },
    ]
  }

  const getRuleminKm = (index: number) => {
    return [
      { required: true, message: '请输入里程', trigger: 'blur' },
      {
        validator: (rule: any, value: string | number, callback: (error?: Error) => void) => {
          validateMinKms(rule, value, callback, index) // 传递索引
        },
        trigger: 'blur',
      },
    ]
  }

  const getRulemin = (index: number) => {
    return [
      { required: true, message: '请输入里程', trigger: 'blur' },
      {
        validator: (rule: any, value: string | number, callback: (error?: Error) => void) => {
          validateMin(rule, value, callback, index) // 传递索引
        },
        trigger: 'blur',
      },
    ]
  }

  watch(
    () => state.dialogVisible.visible,
    (newVal) => {
      if (newVal) {
        getLadderPriceRuleOption()
      }
    },
  )
  const addFirst = () => {
    formListAdd.value.push({
      minKm: formList.value.index[0].maxKm || '',
      maxKm: '',
      price: '',
      unitPrice: '',
      str: 'km',
      title: '区间' + (formListAdd.value.length + 1),
      minRegion: '<',
      maxRegion: '<=',
    })
  }
  const add = (index: number) => {
    formListAdd.value.splice(index + 1, 0, {
      minKm: formListAdd.value[index].maxKm || '',
      maxKm: '',
      price: '',
      unitPrice: '',
      str: 'km',
      title: '区间' + (formListAdd.value.length + 1),
      minRegion: '<',
      maxRegion: '<=',
    })
  }
  const del = (index: number) => {
    formListAdd.value.splice(index, 1)
    // 重新设置区间序号
    formListAdd.value.forEach((item: LadderPriceSection, idx: number) => {
      item.str = 'km'
    })
  }
  const initializeFormData = () => {
    formList.value = {
      index: [
        { minKm: '', maxKm: '', price: '', unitPrice: '', str: 'km', title: '开始', minRegion: '', maxRegion: '<=' },
        { minKm: '', maxKm: '', price: '', unitPrice: '', str: 'km', title: '结束', minRegion: '', maxRegion: '>' },
      ],
      contractId: '',
      contractDetailId: '',
      remark: '',
    }
    formListAdd.value = [] // 清空额外的区间
    ladderPriceCalculationType.value = '3' // 默认选择里程累进计算
  }

  const ModelForm = ref<any>(null)
  const getLadderPriceRuleOption = async () => {
    nextTick(() => {
      ModelForm.value.clearValidate()
    })
    const { data } = await getLadderPriceRuleOptionApi({})
    ladderPriceCalculationTypeList.value = data
  }

  // 获取数据
  const getDetail = async (id: string) => {
    try {
      const res = await getOutsourcePriceEchoApi(id)
      showButton.value = res.data.length > 0
      if (res.data.length !== 0) {
        if (res.data.length > 0) {
          // 处理开始区间
          if (isStartSectionPriceDisabled.value && res.data[0].price) {
            res.data[0].unitPrice = res.data[0].price
            res.data[0].price = ''
          }
          formList.value.index[0] = res.data[0]

          // 处理中间区间
          for (let i = 1; i < res.data.length - 1; i++) {
            if (isOtherSectionPriceDisabled.value && res.data[i].price) {
              res.data[i].unitPrice = res.data[i].price
              res.data[i].price = ''
            }
            formListAdd.value[i - 1] = res.data[i]
          }

          // 处理结束区间
          if (res.data.length > 1) {
            const lastIndex = res.data.length - 1
            if (isOtherSectionPriceDisabled.value && res.data[lastIndex].price) {
              res.data[lastIndex].unitPrice = res.data[lastIndex].price
              res.data[lastIndex].price = ''
            }
            formList.value.index[formList.value.index.length - 1] = res.data[lastIndex]
          }
        }
      }
    } catch (error) {
      ElMessage.error('获取阶梯价格数据失败')
    }
  }

  const ok = async () => {
    let is = Validate.value.every((item: boolean) => item === true)

    if (!is) {
      return ElMessage.error('请检查表单数据是否可通过验证单价不可为负数')
    }
    if (!(await validateForm())) {
      return
    }

    if (!checkMileageOverlap()) {
      return
    }

    // 合并所有区间数据
    const allSections = [formList.value.index[0], ...formListAdd.value, formList.value.index[formList.value.index.length - 1]]

    console.log(allSections, 'allSections')

    let isValid = allSections.every((item) => Number(item.unitPrice) > 0 || Number(item.price) > 0)

    if (!isValid) {
      ElMessage.error('请检查所有区间的价格和单价是否大于0')
      return
    }

    await nextTick()
    const currentResult = allSections
      .map(
        (item: { minRegion: any; title: string; minKm: any; maxRegion: any; maxKm: any; str: any; price: any; unitPrice: any }, index: number, self: any) => {
          let range = ''
          // if (item.minRegion && item.minKm && item.maxRegion && item.maxKm) {
          //   range = `${item.minKm}${item.minRegion}${item.str}${item.maxRegion}${item.maxKm}`
          // } else if (item.minRegion && item.minKm) {
          //      range = `${item.str}${item.minRegion}${item.minKm}`
          // } else if (item.maxRegion && item.maxKm) {
          //     range = `${item.str}${item.maxRegion}${item.maxKm}`
          // }

          console.log(item, 'item')

          if (item.title === '开始') {
            let str = item.minRegion != null ? item.minRegion : ''
            range = `${item.str}${item.minRegion != '' && item.minRegion != null ? item.minRegion : item.maxRegion}${item.maxKm}`
          } else if (item.title === '结束' || self.length - 1 === index) {
            range = `${item.str}${item.minRegion != '' && item.minRegion != null ? item.minRegion : item.maxRegion}${item.minKm}`
          } else {
            range = `${item.minKm}${item.minRegion}${item.str}${item.maxRegion}${item.maxKm}`
          }
          range = range.replace(/null/g, '') // 替换掉undefined
          // 根据是否禁用price来决定使用price还是unitPrice
          const priceValue =
            isStartSectionPriceDisabled.value && index === 0 ? item.unitPrice : isOtherSectionPriceDisabled.value && index > 0 ? item.unitPrice : item.price
          return `${range}~@#${priceValue || ''}`
        },
      )
      .join('$*%')

    console.log(currentResult, 'currentResult')

    try {
      await postOutsourcePriceApi({
        contractId: formList.value.contractId,
        contractDetailId: formList.value.contractDetailId,
        remark: formList.value.remark,
        ladderPricingFormula: currentResult,
        ladderPriceCalculationType: ladderPriceCalculationType.value,
      })

      ElMessage.success('阶梯价格设置成功')
      state.dialogVisible.visible = false
      emit('refresh')
    } catch (error) {
      ElMessage.error('阶梯价格设置失败')
    }
  }

  const cancel = () => {
    state.dialogVisible.visible = false
    initializeFormData()
  }

  const TableChange = (val: any) => {
    ModelForm.value.clearValidate()
  }

  const handleClose = () => {
    row.value = null
    state.dialogVisible.visible = false
    initializeFormData()
  }
  watch(
    () => ladderPriceCalculationType.value,
    (newVal) => {
      //如果price 的disabled= true,则 将price 赋值给unitPrice，并清空price，如果unitPrice 的disabled= true,则 将unitPrice 赋值给price，并清空unitPrice
      if (isStartSectionPriceDisabled.value) {
        formList.value.index[0].unitPrice = formList.value.index[0].price
        formList.value.index[0].price = ''
      }
      if (isStartSectionUnitPriceDisabled.value) {
        formList.value.index[0].price = formList.value.index[0].unitPrice
        formList.value.index[0].unitPrice = ''
      }
    },
  )
  // 计算属性：控制起步价和单价的输入
  const isStartSectionPriceDisabled = computed(() => {
    return ['1', '3'].includes(ladderPriceCalculationType.value)
  })

  const isStartSectionUnitPriceDisabled = computed(() => {
    return ['0', '2'].includes(ladderPriceCalculationType.value)
  })

  const isOtherSectionPriceDisabled = computed(() => {
    return true // 除开始区间外的其他区间起步价都禁用
  })

  const isOtherSectionUnitPriceDisabled = computed(() => {
    return false // 其他区间的单价都可以输入
  })

  // 监听计算方式变化
  // watch(
  //   () => ladderPriceCalculationType.value,
  //   (newVal) => {
  //     // 重置价格和单价
  //     formList.value.index[0].price = ''
  //     formList.value.index[0].unitPrice = ''
  //     formListAdd.value.forEach((item) => {
  //       item.price = ''
  //       item.unitPrice = ''
  //     })
  //     formList.value.index[formList.value.index.length - 1].price = ''
  //     formList.value.index[formList.value.index.length - 1].unitPrice = ''
  //   },
  // )

  // 监听里程变化，检查区间重叠
  watch([() => formList.value.index[0].maxKm, ...formListAdd.value.map((_, index) => () => formListAdd.value[index].maxKm)], (newVals) => {
    checkMileageOverlap()
  })

  // 检查里程区间重叠
  const checkMileageOverlap = () => {
    const sections = [formList.value.index[0], ...formListAdd.value, formList.value.index[formList.value.index.length - 1]]

    for (let i = 0; i < sections.length - 1; i++) {
      const current = sections[i]
      const next = sections[i + 1]

      if (current.maxKm && next.minKm && Number(current.maxKm) > Number(next.minKm)) {
        ElMessage.error('里程区间不能有重叠')
        return false
      }

      if (i < sections.length - 2 && current.maxKm && next.minKm && Number(current.maxKm) < Number(next.minKm)) {
        ElMessage.warning('请补全缺失里程区间！')
        return false
      }
    }
    return true
  }

  // 提交前的表单验证
  const validateForm = async () => {
    // 验证所有区间的里程和价格
    const sections = [formList.value.index[0], ...formListAdd.value, formList.value.index[formList.value.index.length - 1]]

    for (let i = 0; i < sections.length; i++) {
      const section = sections[i]

      // 验证里程
      if (i === 0) {
        if (!section.maxKm) {
          ElMessage.error('请输入开始区间的结束里程')
          return false
        }
      } else if (i === sections.length - 1) {
        if (!section.minKm) {
          ElMessage.error('请输入结束区间的开始里程')
          return false
        }
      } else {
        if (!section.minKm || !section.maxKm) {
          ElMessage.error(`请输入第${i}个区间的完整里程信息`)
          return false
        }
      }

      // 验证价格
      if (i === 0) {
        if (isStartSectionPriceDisabled.value && !section.unitPrice) {
          ElMessage.error('请输入开始区间的单价')
          return false
        }
        if (isStartSectionUnitPriceDisabled.value && !section.price) {
          ElMessage.error('请输入开始区间的起步价')
          return false
        }
      } else if (i === sections.length - 1) {
        if (!section.unitPrice) {
          ElMessage.error('请输入结束区间的单价')
          return false
        }
      } else {
        if (!section.unitPrice) {
          ElMessage.error(`请输入第${i}个区间的单价`)
          return false
        }
      }
    }

    return true
  }

  // 监听里程变化，自动填充下一区间的开始里程
  watch(
    () => formList.value.index[0].maxKm,
    (newVal) => {
      if (formListAdd.value.length > 0) {
        formListAdd.value[0].minKm = newVal
      }
    },
  )

  watch(
    () => formListAdd.value.map((item) => item.maxKm),
    (newVals) => {
      newVals.forEach((val, index) => {
        if (index < formListAdd.value.length - 1) {
          formListAdd.value[index + 1].minKm = val
        }
        if (index === formListAdd.value.length - 1) {
          formList.value.index[formList.value.index.length - 1].minKm = val
        }
      })
    },
    { deep: true },
  )

  defineExpose({
    state,
    formList,
    getDetail,
    row,
  })
</script>
