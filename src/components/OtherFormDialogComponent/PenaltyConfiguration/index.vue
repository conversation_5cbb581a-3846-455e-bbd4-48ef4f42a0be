<template>
  <!-- 新增编辑 补贴 弹窗 -->
  <el-dialog
    draggable
    :close-on-click-modal="false"
    :lock-scroll="true"
    v-model="state.dialogVisible.visible"
    :title="state.dialogVisible.title"
    width="80%"
    @close="closeDialog"
  >
    <el-form ref="formDataRef" :inline="true" :model="state.formData" :rules="state.rules">
      <el-row>
        <el-col :span="24">
          <el-form-item label="罚款类型：" prop="type">
            <el-input v-model="state.formData.type" placeholder="请输入罚款类型" clearable style="width: 240px"></el-input>
          </el-form-item>
          <el-form-item label="计算类型：" prop="formulaType">
            <el-select v-model="state.formData.formulaType" placeholder="请选择计算类型" clearable @change="changeFormula" style="width: 240px">
              <el-option v-for="item in state.subsidyTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="state.subsidyNameList.length" :span="24">
          <el-card>
            <el-row>
              <el-col :span="24">
                <div style="padding: 6px 0">
                  <span>参与计算的编号与单位:</span>
                  <!-- 补贴 -->
                  <span v-for="item in state.factorsTable" :key="item.id">
                    <el-tag style="margin: 0px 5px 5px; font-weight: 600; font-size: 14px">{{ item.code + ' - ' + item.name + '/' + item.unit }}</el-tag>
                  </span>
                </div>
              </el-col>
            </el-row>
            <el-tag style="margin-bottom: 5px" type="warning">说明: 等于 ==，小于 <，小于等于 <=，大于 >，大于等于 >=</el-tag>
            <div v-for="(item, index) in state.subsidyNameList" :key="index">
              <div class="addInputValue">
                <span v-if="state.formData.formulaType == 3 || state.formData.formulaType == 0">
                  <span v-if="state.formData.formulaType == 3">计算条件{{ index + 1 }}：</span>
                  <span v-else>计算条件：</span>
                  <el-input v-model="item.formula" placeholder="请输入计算条件" clearable style="width: 230px; margin-right: 10px"></el-input>
                </span>
                <span v-if="state.formData.formulaType == 2 || state.formData.formulaType == 3">
                  <span>罚款金额：</span>
                  <el-input v-model="item.stationValue" placeholder="请输入罚款金额(数值)" clearable style="width: 230px; margin-right: 10px"></el-input>
                </span>

                <el-button v-if="index === state.subsidyNameList.length - 1 && state.formData.formulaType === 3" text type="primary" @click="addItem()">
                  添加
                </el-button>
                <el-button v-if="state.subsidyNameList.length > 1" style="color: red" text type="primary" @click="delColumn(index)">删除</el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-form>
    <!-- 按钮 -->
    <template #footer>
      <div class="submit-btn">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="confirmForm(formDataRef)">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { addFineConfigApi, editFineConfigApi } from '@/api/GlobalMenu/index'
  const emit = defineEmits(['confirmSettlementSuccess'])
  const formDataRef = ref()
  const stationStr = ref([] as any)

  const state = reactive<any>({
    dialogVisible: {
      visible: false,
      title: '新增罚款信息',
    },
    id: '',
    formData: {
      type: '',
      formulaType: 3,
      formula: '',
    },
    // 公式类型选择
    subsidyNameList: [
      {
        formula: '',
        stationValue: '',
      },
    ],
    subsidyTypeList: [
      {
        value: 0,
        label: '按代入公式',
      },
      {
        value: 2,
        label: '按固定值',
      },
      {
        value: 3,
        label: '按条件代入公式',
      },
    ], //计算类型列表
    factorsTable: [
      {
        id: 1,
        code: 'f',
        name: '损失金额',
        unit: '元',
      },
      {
        id: 2,
        code: 'i',
        name: '保险金额',
        unit: '元',
      },
    ], //示例
    rules: {
      type: [{ required: true, message: '请输入罚款类型', trigger: 'blur' }],
      formulaType: [{ required: true, message: '请选择计算类型', trigger: 'change' }],
    },
  })

  // 项 新增编辑提交
  // 应用字符为~@#
  // 分割字符为$*%
  // 且关系字符为^&!
  // 或者关系符为*|#
  const confirmForm = async (formEl: { validate: any }) => {
    if (!formEl) return
    await formEl.validate((valid: any) => {
      if (valid) {
        if (state.subsidyNameList.length > 0) {
          stationStr.value = []
          state.subsidyNameList.map((item: any) => {
            if (state.formData.formulaType == 3) {
              if (item.formula && item.stationValue) {
                stationStr.value.push(item.formula + '~@#' + item.stationValue)
              }
              if (!item.formula || !item.stationValue) {
                ElMessage.error('请填写计算条件及金额')
                throw new Error()
              }
            } else {
              if (item.stationValue) {
                stationStr.value.push(item.stationValue)
              }
              if (!item.stationValue) {
                ElMessage.error('请填写计算金额')
                throw new Error()
              }
            }
          })
          if (stationStr.value.length > 0) {
            state.formData.formula = stationStr.value.join('$*%')
          } else {
            state.formData.formula = ''
          }
        }
        let params = {
          ...state.formData,
        }
        if (state.id) {
          editFineConfigApi(params, state.id).then((res: any) => {
            if (res.code == 200) {
              ElMessage.success(res.message)
              closeDialog()
              emit('confirmSettlementSuccess')
            }
          })
        } else {
          addFineConfigApi(params)
            .then((res: any) => {
              if (res.code == 200) {
                ElMessage.success(res.message)
                closeDialog()
                emit('confirmSettlementSuccess')
              }
            })
            .catch(() => {})
        }
      }
    })
  }

  //添加项
  const addItem = () => {
    state.subsidyNameList.push({
      formula: '',
      stationValue: '',
    })
  }
  //删除项
  const delColumn = (index: number) => {
    state.subsidyNameList.splice(index, 1)
  }

  // 重置
  const resetForm = () => {
    state.formData.type = '' // 类型
    state.formData.formulaType = 3 // 公式类型id
    state.subsidyNameList = [
      {
        formula: '',
        stationValue: '',
      },
    ]
  }

  // 补贴项选择公式类型
  const changeFormula = (value: any) => {
    state.subsidyNameList = [
      {
        formula: '',
        stationValue: '',
      },
    ]
  }

  // 编辑
  const editItem = (itemData: any) => {
    state.subsidyNameList = []
    state.id = itemData.id //id
    state.formData.type = itemData.type // 类型
    state.formData.formulaType = itemData.formulaType // 公式类型id

    if (itemData.formula) {
      var subsidyListData = itemData.formula.split('$*%')
      if (subsidyListData.length > 0) {
        subsidyListData.map((item: any) => {
          let newStationValue = item.split('~@#')
          if (itemData.formulaType == 3) {
            state.subsidyNameList.push({
              formula: newStationValue[0],
              stationValue: newStationValue[1],
            })
          } else {
            if (newStationValue.length >= 1) {
              state.subsidyNameList.push({
                formula: '',
                stationValue: newStationValue[0],
              })
            }
          }
        })
      }
    } else {
      state.subsidyNameList = [
        {
          formula: '',
          stationValue: '',
        },
      ]
    }
  }

  const closeDialog = () => {
    state.dialogVisible.visible = false
  }

  defineExpose({
    state,
    resetForm,
    editItem,
  })
</script>

<style scoped>
  .addInputValue {
    padding: 4px 0;
  }
</style>
