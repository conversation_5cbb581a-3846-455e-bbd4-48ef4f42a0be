<!--
 * @Author: llm
 * @Date: 2024-09-24 16:36:04
 * @LastEditors: llm
 * @LastEditTime: 2025-03-26 18:05:16
 * @Description: 调整约束条件
-->
<template>
  <div>
    <el-dialog
      v-model="dialogVisible.visible"
      :title="dialogVisible.title"
      width="85%"
      :draggable="true"
      :close-on-click-modal="false"
      destroy-on-close
      @close="closeDialog"
    >
      <el-scrollbar max-height="70vh" class="formClass">
        <el-form ref="formRef" :model="state.formData" :rules="rules" :inline="true" style="margin: 0 20px">
          <el-form-item label="基地" prop="startBase">
            <el-input v-model="state.formData.startBase" :disabled="state.origin === 'viewPlanLimit' ? true : false" placeholder="请输入品牌"></el-input>
          </el-form-item>
          <el-form-item label="运输业务预估总量" prop="expectNum">
            <el-input v-model="state.formData.expectNum" :disabled="state.origin === 'viewPlanLimit' ? true : false" placeholder="请输入预估总量"></el-input>
          </el-form-item>
          <el-form-item label="时间区间" prop="month">
            <el-date-picker
              v-model="state.formData.month"
              type="monthrange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="请选择时间"
              format="YYYY-MM"
              value-format="YYYY-MM"
              clearable
              :disabled="state.origin === 'viewPlanLimit' ? true : false"
            />
          </el-form-item>
          <el-form-item v-if="state.origin === 'provinceLimit'">
            <el-button type="primary" @click="setConstraint(formRef)">设置约束条件</el-button>
          </el-form-item>
          <el-divider content-position="center" border-style="dashed" class="custom-divider">总体约束条件</el-divider>
          <el-form-item label="公路占比">
            <el-input-number
              v-model="state.formData.roadRatio"
              :min="0"
              :max="100"
              placeholder="请输入"
              :disabled="state.origin === 'viewPlanLimit' ? true : false"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="公路占比偏差比例">
            <el-input-number
              v-model="state.formData.roadOffsetRatio"
              placeholder="请输入"
              :disabled="state.origin === 'viewPlanLimit' ? true : false"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="铁路占比">
            <el-input-number
              v-model="state.formData.railwayRatio"
              :min="0"
              :max="100"
              placeholder="请输入"
              :disabled="state.origin === 'viewPlanLimit' ? true : false"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="铁路占比偏差比例">
            <el-input-number
              v-model="state.formData.railwayOffsetRatio"
              placeholder="请输入"
              :disabled="state.origin === 'viewPlanLimit' ? true : false"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="水路占比">
            <el-input-number
              v-model="state.formData.waterwayRatio"
              :min="0"
              :max="100"
              placeholder="请输入"
              :disabled="state.origin === 'viewPlanLimit' ? true : false"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="水路占比偏差比例">
            <el-input-number
              v-model="state.formData.waterwayOffsetRatio"
              placeholder="请输入"
              :disabled="state.origin === 'viewPlanLimit' ? true : false"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="新增港站数量约束">
            <el-input-number
              v-model="state.formData.newPortUpperLimit"
              :min="0"
              :max="100000000"
              :precision="0"
              placeholder="请输入正整数"
              :disabled="state.origin === 'viewPlanLimit' ? true : false"
              style="min-width: 180px"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="总OTD时效约束">
            <el-input-number
              v-model="state.formData.otdUpperLimit"
              :min="0"
              :max="100000000"
              :precision="0"
              placeholder="请输入正整数"
              :disabled="state.origin === 'viewPlanLimit' ? true : false"
              style="min-width: 180px"
            ></el-input-number>
          </el-form-item>
        </el-form>
        <baseTabPage ref="baseTabPageRef" :currentId="state.currentId" :isShowBtnGroup="state.showBtnGroup" :newMenuId="newMenuId"></baseTabPage>
      </el-scrollbar>
      <template #footer v-if="state.origin === 'provinceLimit'">
        <div class="dialog-footer">
          <el-button @click="closeDialog()">取 消</el-button>
          <el-button type="primary" v-loading="submitLoading" @click="handleSubmit()">保 存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { FormInstance, TransferDataItem } from 'element-plus'
  import baseTabPage from '@/views/Pages/baseTabPage.vue'
  import { getTransportConfig, getHistoryTransportConfig, getMonthSetLimitationApi, saveSetUpPlanApi } from '@/api/transportPlan'
  // 状态管理依赖
  import { useFormStore } from '@/store/modules/form.js'
  import { useSideBarStore } from '@/store/modules/sideBar'
  const formStore = useFormStore()
  const sideBarStore = useSideBarStore()
  const { routerParams, defaultTableIds, mergeFormData, storeFormParams } = storeToRefs(formStore)
  const { proxy }: any = getCurrentInstance()
  //优先使用列表中的携带的参数，再使用地址栏参数
  const routeParams =
    proxy.$sideBarStore.$state.btnMenuQuery && JSON.stringify(proxy.$sideBarStore.$state.btnMenuQuery) != '{}'
      ? proxy.$sideBarStore.$state.btnMenuQuery
      : routerParams.value
  const rules = {
    startBase: [{ required: true, message: '请输入基地名称', trigger: 'blur' }],
    expectNum: [{ required: true, message: '请输入运输业务预估总量', trigger: 'blur' }],
    month: [{ required: true, message: '请选择时间区间', trigger: 'change' }],
  }
  const state = reactive({
    formData: {
      id: '',
      month: '', //月份
      startBase: '', //基地
      expectNum: null, //业务总量
      roadRatio: null, //公路占比
      roadOffsetRatio: null, //比例偏差
      railwayRatio: null, //铁路占比
      railwayOffsetRatio: null, //比例偏差
      waterwayRatio: null, //水路占比
      waterwayOffsetRatio: null, //比例偏差
      newPortUpperLimit: null, //新港站数
      otdUpperLimit: null, //otd上限
    } as any,
    origin: '',
    showBtnGroup: true,
    currentId: '', //当前行的id
  })
  /**
   * 查看列表项详情弹窗
   */
  const dialogVisible = reactive<DialogOption>({
    visible: false,
    title: '详情',
  })
  const formRef = ref()
  const currentTab = ref<MenuVO>()
  const baseTabPageRef = ref()
  const newMenuId = ref('')
  /**
   * 下载加载动画
   */
  const submitLoading = ref<boolean>(false)
  /**
   * 加载动画
   */
  const dialogLoading = ref<boolean>(false)

  /**
   * 约束设置查询条件
   */
  const transportQueryParams = reactive<any>({})
  // 点击设置约束条件按钮
  const setConstraint = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        for (const key in routeParams) {
          if (Object.prototype.hasOwnProperty.call(routeParams, key)) {
            //如果属性中包含range字符串，则把首字母大写，然后拆分2个，分别拼上start 和 end ,并删除原有属性
            if (key!.indexOf('Range') !== -1 && (routeParams[key] as any)) {
              const startName = 'start' + key.charAt(0).toUpperCase() + key.slice(1)
              const endName = 'end' + key.charAt(0).toUpperCase() + key.slice(1)
              transportQueryParams[startName] = routeParams[key]![0]
              transportQueryParams[endName] = routeParams[key]![1]
              delete transportQueryParams[key]
            } else {
              transportQueryParams[key] = routeParams[key]
            }
          }
        }
        let params = {
          ...transportQueryParams,
          startMonth: state.formData.month[0],
          endMonth: state.formData.month[1],
        }
        getMonthSetLimitationApi(params)
          .then((res) => {
            baseTabPageRef.value.changeTab(baseTabPageRef.value.currentTab!)
          })
          .catch((err) => {})
      }
    })
  }

  const getConfigInfo = () => {
    dialogLoading.value = true
    for (const key in routeParams) {
      if (Object.prototype.hasOwnProperty.call(routeParams, key)) {
        //如果属性中包含range字符串，则把首字母大写，然后拆分2个，分别拼上start 和 end ,并删除原有属性
        if (key!.indexOf('Range') !== -1 && (routeParams[key] as any)) {
          const startName = 'start' + key.charAt(0).toUpperCase() + key.slice(1)
          const endName = 'end' + key.charAt(0).toUpperCase() + key.slice(1)
          transportQueryParams[startName] = routeParams[key]![0]
          transportQueryParams[endName] = routeParams[key]![1]
          delete transportQueryParams[key]
        } else {
          transportQueryParams[key] = routeParams[key]
        }
      }
    }
    //如果storeFormParams有值，则合并到formData中
    for (const key in sideBarStore.$state.storeDialogFormParams) {
      if (Object.prototype.hasOwnProperty.call(sideBarStore.$state.storeDialogFormParams, key)) {
        transportQueryParams[key] = sideBarStore.$state.storeDialogFormParams[key]
      }
    }
    let configApi
    if (state.origin === 'viewPlanLimit') {
      configApi = getHistoryTransportConfig({
        dataId: state.currentId,
      })
    } else {
      configApi = getTransportConfig(transportQueryParams)
    }
    configApi
      .then((res) => {
        const { data } = res
        state.formData.id = data.id //id
        state.formData.startBase = data.startBase //基地
        state.formData.expectNum = data.expectNum //业务总量
        state.formData.roadRatio = data.roadRatio //公路占比
        state.formData.roadOffsetRatio = data.roadOffsetRatio //比例偏差
        state.formData.railwayRatio = data.railwayRatio //铁路占比
        state.formData.railwayOffsetRatio = data.railwayOffsetRatio //比例偏差
        state.formData.waterwayRatio = data.waterwayRatio //水路占比
        state.formData.waterwayOffsetRatio = data.waterwayOffsetRatio //比例偏差
        state.formData.newPortUpperLimit = data.newPortUpperLimit //新港站数
        state.formData.otdUpperLimit = data.otdUpperLimit //otd上限
        dialogLoading.value = false
        // 查看
        if (state.origin === 'viewPlanLimit') {
          // 判断如果有时间的话 就展示下方列表
          if (data.startMonth && data.startMonth) {
            state.formData.month = [data.startMonth, data.endMonth] //月份
          } else {
            state.formData.month = ''
          }
        } else {
          // 修改
          // 判断如果有时间的话 就展示下方列表
          if (data.startMonth && data.startMonth) {
            state.formData.month = [data.startMonth, data.endMonth] //月份
          } else {
            state.formData.month = ''
          }
        }
      })
      .catch((err) => {
        dialogLoading.value = false
      })
  }

  // 保存
  const handleSubmit = () => {
    let params = {
      dataId: transportQueryParams?.taskId,
      startMonth: state.formData.month[0],
      endMonth: state.formData.month[1],
      ...state.formData,
    }
    let newParams = JSON.parse(JSON.stringify(params))
    if (newParams.month) {
      delete newParams.month
    }
    if (newParams.expectNum) {
      newParams.expectNum = Number(newParams.expectNum)
    }
    if (state.formData.id) {
      saveSetUpPlanApi(state.formData.id, newParams)
        .then((res) => {
          ElMessage.success('操作成功')
          closeDialog()
        })
        .catch((err) => {})
    }
  }

  /**
   * 关闭弹窗
   */
  function closeDialog() {
    dialogVisible.visible = false
    //如果transportQueryParams有值，则清除已经从storeDialogFormParams中合并的值 然后重新存到storeDialogFormParams
    for (const key in transportQueryParams) {
      if (key in sideBarStore.$state.storeDialogFormParams) {
        delete transportQueryParams[key]
      }
    }
    sideBarStore.$state.storeDialogFormParams = transportQueryParams
  }

  watch(
    () => dialogVisible.visible,
    async (val) => {
      if (val) {
        // 获取配置信息
        getConfigInfo()
      }
    },
    { deep: true },
  )

  defineExpose({
    dialogVisible,
    state,
    newMenuId,
  })
</script>
<style lang="scss" scoped>
  .custom-divider :deep(.el-divider__text) {
    color: #f47920; /* 文本 */
  }
  .custom-divider {
    border-color: #f47920; /* 分隔线 */
  }
  .formClass :deep(.el-form-item) {
    margin-right: 20px;
  }
</style>
