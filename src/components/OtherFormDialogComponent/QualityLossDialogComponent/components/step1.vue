<!--
 * @Author: llm
 * @Date: 2025-01-11 17:47:56
 * @LastEditors: llm
 * @LastEditTime: 2025-07-18 09:50:41
 * @Description:
-->
<template>
  <view>
    <el-card shadow="never" v-loading="state.loading">
      <template #header>
        <div>质损车信息</div>
      </template>
      <el-form ref="step1FormRef" :model="state.formData" :rules="rules" label-width="120px">
        <el-row :gutter="0">
          <el-col :span="8">
            <el-form-item label="质损VIN" prop="vin">
              <!-- <el-input placeholder="请填写" :maxLength="20" @blur="getDispatchList" v-model="state.formData.vin"></el-input> -->
              <el-select
                v-model="state.formData.vin"
                filterable
                remote
                reserve-keyword
                remote-show-suffix
                :remote-method="getOrderVinList"
                placeholder="请选择"
                clearable
                @change="getDispatchList"
                :disabled="props.currentRow.dealStatus === '已完成' || props.origin === 'edit'"
              >
                <el-option v-for="item in state.orderVinList" :key="item.id" :label="item.vin" :value="item.vin"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="state.formData.accidentNo ? 8 : 16">
            <el-form-item label="关联调度单" prop="dispatchNo">
              <el-select
                :empty-values="[null, undefined]"
                :disabled="props.currentRow.dealStatus === '已完成' || props.origin === 'edit'"
                clearable
                placeholder="请选择调度单"
                v-model="state.formData.dispatchNo"
                @change="getDispatchInfo"
                filterable
              >
                <el-option :label="item.label" :value="item.value" v-for="item in state.dispatchList"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="state.formData.accidentNo">
            <el-form-item label="关联事故" prop="accidentNo">
              <el-input placeholder="" :maxLength="20" v-model="state.formData.accidentNo" :disabled="true"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="基地" prop="baseId">
              <el-tree-select
                :disabled="props.currentRow.dealStatus === '已完成' || props.origin === 'edit'"
                v-model="state.formData.baseId"
                default-expand-all
                filterable
                style="width: 100%"
                :multiple="false"
                :empty-values="[null, undefined]"
                placeholder=""
                :data="state.baseList"
                :check-strictly="true"
                :props="{ value: 'value', label: 'label' }"
                clearable
                @change="handleBaseChange"
              >
                <template #default="{ data }">{{ data.label }}</template>
              </el-tree-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户" prop="customerName">
              <el-input
                :disabled="props.currentRow.dealStatus === '已完成' || props.origin === 'edit'"
                placeholder=""
                :maxLength="20"
                v-model="state.formData.customerName"
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 品牌 -->
          <el-col :span="8">
            <el-form-item label="品牌" prop="brandName">
              <el-input placeholder="" :disabled="props.currentRow.dealStatus === '已完成'" :maxLength="20" v-model="state.formData.brandName"></el-input>
            </el-form-item>
          </el-col>
          <!-- 车型 -->
          <el-col :span="8">
            <el-form-item label="车型" prop="vehicleModelName">
              <el-input
                placeholder=""
                :disabled="props.currentRow.dealStatus === '已完成'"
                :maxLength="20"
                v-model="state.formData.vehicleModelName"
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 经销商 -->
          <el-col :span="8">
            <el-form-item label="经销商" prop="dealerName">
              <el-input placeholder="" :disabled="props.currentRow.dealStatus === '已完成'" :maxLength="20" v-model="state.formData.dealerName"></el-input>
            </el-form-item>
          </el-col>
          <!-- 经销商联系人 -->
          <el-col :span="8">
            <el-form-item label="经销商联系人" prop="dealerContacts">
              <el-input placeholder="" :disabled="props.currentRow.dealStatus === '已完成'" :maxLength="20" v-model="state.formData.dealerContacts"></el-input>
            </el-form-item>
          </el-col>
          <!-- 经销商联系方式 -->
          <el-col :span="8">
            <el-form-item label="经销商联系方式" prop="dealerMobile">
              <el-input placeholder="" :disabled="props.currentRow.dealStatus === '已完成'" :maxLength="20" v-model="state.formData.dealerMobile"></el-input>
            </el-form-item>
          </el-col>
          <!-- 承运商 -->
          <el-col :span="8">
            <el-form-item label="承运商" prop="carrierInfo">
              <!-- <el-select placeholder="请选择承运商" v-model="state.formData.carrierInfo" filterable @change="getCarrier">
                <el-option-group v-for="group in state.carrierList" :key="group.label" :label="group.label">
                  <el-option v-for="item in group.children" :key="item.value" :label="item.label" :value="item.value" />
                </el-option-group>
              </el-select> -->
              <el-tree-select
                :disabled="props.currentRow.dealStatus === '已完成' || props.origin === 'edit'"
                v-model="state.formData.carrierInfo"
                default-expand-all
                filterable
                style="width: 100%"
                :multiple="false"
                :empty-values="[null, undefined]"
                placeholder=""
                :data="state.carrierList"
                :check-strictly="false"
                :props="{ value: 'value', label: 'label' }"
                clearable
              >
                <template #default="{ data }">{{ data.label }}</template>
              </el-tree-select>
            </el-form-item>
          </el-col>
          <!-- 车牌号 -->
          <el-col :span="8" v-if="state.formData.carrierInfo">
            <el-form-item label="车牌号" prop="vehicleNo">
              <el-select
                :disabled="props.currentRow.dealStatus === '已完成' || props.origin === 'edit'"
                placeholder="请选择车牌号"
                remote-show-suffix
                clearable
                :empty-values="[null, undefined]"
                v-model="state.formData.vehicleNo"
                reserve-keyword
                filterable
                :remote-method="getPlateNumberList"
                :remote="true"
                :loading="state.plateNumberLoading"
              >
                <el-option :label="item.label" :value="item.value" v-for="item in state.plateNumberList" :key="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="state.formData.carrierInfo">
            <el-form-item label="车牌颜色" prop="plateColor">
              <el-select
                :disabled="props.currentRow.dealStatus === '已完成' || props.origin === 'edit'"
                placeholder=""
                clearable
                :empty-values="[null, undefined]"
                v-model="state.formData.plateColor"
                filterable
              >
                <el-option :label="item.label" :value="item.value" v-for="item in state.plateColorList" :key="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 司机信息 -->
          <el-col :span="8" v-if="state.formData.carrierInfo">
            <el-form-item label="司机信息" prop="driverInfo">
              <el-tree-select
                :disabled="
                  props.currentRow.dealStatus === '已完成' || props.origin === 'edit' || (props.origin === 'add' && state.dispatchInfo?.carrierType === '外协')
                "
                v-model="state.formData.driverInfo"
                default-expand-all
                filterable
                :empty-values="[null, undefined]"
                style="width: 100%"
                :multiple="false"
                placeholder=""
                :data="state.driverList"
                :check-strictly="false"
                :props="{ value: 'value', label: 'label' }"
                clearable
              >
                <template #default="{ data }">{{ data.label }}</template>
              </el-tree-select>
            </el-form-item>
          </el-col>
          <!-- 司机责任 -->
          <el-col :span="8">
            <el-form-item label="司机责任" prop="responsibilityType">
              <el-select
                :disabled="props.currentRow.dealStatus === '已完成' || props.origin === 'edit'"
                clearable
                placeholder=""
                v-model="state.formData.responsibilityType"
              >
                <el-option label="全责" value="全责"></el-option>
                <el-option label="无责" value="无责"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 安全员 -->
          <el-col :span="8">
            <el-form-item label="安全员" prop="safetyOfficer">
              <el-input
                :disabled="props.currentRow.dealStatus === '已完成' || props.origin === 'edit'"
                placeholder=""
                :maxLength="20"
                v-model="state.formData.safetyOfficer"
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 发生时间 -->
          <el-col :span="8">
            <el-form-item label="发生时间" prop="happenTime">
              <el-date-picker
                :disabled="props.currentRow.dealStatus === '已完成' || props.origin === 'edit'"
                :empty-values="[null, undefined]"
                type="datetime"
                v-model="state.formData.happenTime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
          <!-- 发生地点 -->
          <el-col :span="8">
            <el-form-item label="发生地点" prop="happenAddress">
              <el-input
                :empty-values="[null, undefined]"
                :disabled="props.currentRow.dealStatus === '已完成' || props.origin === 'edit'"
                placeholder="请填写"
                :maxLength="20"
                v-model="state.formData.happenAddress"
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 质损类型 -->
          <el-col :span="6">
            <el-form-item label="质损类型" prop="type">
              <el-select :disabled="props.currentRow.dealStatus === '已完成'" placeholder="请选择质损类型" multiple v-model="state.formData.type" filterable>
                <el-option :label="item.label" :value="item.value" v-for="item in state.dealTypeList"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 质损部位 -->
          <el-col :span="6">
            <el-form-item label="质损部位" prop="damagedArea">
              <el-select
                :disabled="props.currentRow.dealStatus === '已完成'"
                placeholder="请选择质损部位"
                multiple
                v-model="state.formData.damagedArea"
                filterable
              >
                <el-option :label="item.label" :value="item.value" v-for="item in state.damagedAreaList"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 质损原因 -->
          <el-col :span="6">
            <el-form-item label="质损原因" prop="qualityDamageReasons">
              <el-select :disabled="props.currentRow.dealStatus === '已完成'" placeholder="" v-model="state.formData.qualityDamageReasons" filterable clearable>
                <el-option :label="item.label" :value="item.value" v-for="item in state.qualityDamageReasonsList"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 处理模式 -->
          <el-col :span="6">
            <el-form-item label="处理模式" prop="dealType">
              <el-select :disabled="props.currentRow.dealStatus === '已完成'" clearable placeholder="" v-model="state.formData.dealType" filterable>
                <el-option :label="item.label" :value="item.value" v-for="item in state.dealModeList"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 是否买断 -->
          <el-col :span="6">
            <el-form-item label="是否买断" prop="isBuyout">
              <el-checkbox :disabled="props.currentRow.dealStatus === '已完成'" v-model="state.formData.buyUp" :value="true"></el-checkbox>
            </el-form-item>
          </el-col>
          <!-- 是否出口车 -->
          <el-col :span="6">
            <el-form-item label="是否出口车" prop="exportCar">
              <el-checkbox
                true-value="是"
                false-value="否"
                :disabled="props.currentRow.dealStatus === '已完成'"
                v-model="state.formData.exportCar"
              ></el-checkbox>
            </el-form-item>
          </el-col>
          <!-- 是否取消装车 -->
          <el-col :span="6">
            <el-form-item label="是否取消装车" prop="cancelLoad">
              <el-checkbox :disabled="props.currentRow.dealStatus === '已完成'" v-model="state.formData.cancelLoad"></el-checkbox>
            </el-form-item>
          </el-col>
          <!-- 质损金额 -->
          <el-col :span="12">
            <el-row :gutter="0">
              <el-col :span="12">
                <el-form-item label="质损金额" prop="amount">
                  <el-input-number
                    :disabled="props.currentRow.dealStatus === '已完成'"
                    placeholder="请填写"
                    :precision="2"
                    :maxLength="20"
                    type="number"
                    v-model="state.formData.amount"
                  ></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="" prop="" label-width="20">
                  <el-radio-group :disabled="props.currentRow.dealStatus === '已完成'" v-model="state.formData.chargeType">
                    <el-radio label="预计金额" value="预计金额"></el-radio>
                    <el-radio label="实际金额" value="实际金额"></el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>

          <!-- 维修费用 -->
          <el-col :span="6">
            <el-form-item label="维修费用" prop="maintenanceFee">
              <el-input-number
                :disabled="props.currentRow.dealStatus === '已完成'"
                placeholder="请填写"
                :precision="2"
                :maxLength="20"
                type="number"
                v-model="state.formData.maintenanceFee"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <!-- 降价费用 -->
          <el-col :span="6">
            <el-form-item label="降价费用" prop="reducePrice">
              <el-input-number
                :disabled="props.currentRow.dealStatus === '已完成'"
                placeholder="请填写"
                :precision="2"
                :maxLength="20"
                type="number"
                v-model="state.formData.reducePrice"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <!-- 问题描述 -->
          <el-col :span="24">
            <el-form-item label="问题描述" prop="problemDesc">
              <el-input
                :disabled="props.currentRow.dealStatus === '已完成'"
                placeholder="请填写"
                :maxLength="20"
                v-model="state.formData.problemDesc"
                type="textarea"
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 问题图片 -->
          <el-col :span="12">
            <el-form-item label="问题图片" prop="picsList">
              <UploadImageComponent
                :disabled="props.currentRow.dealStatus === '已完成'"
                ref="uploadImageRef"
                tip="支持扩展名.png.jpeg.jpg，可多张上传最多20张"
                :limit="20"
                :multiple="true"
              />
            </el-form-item>
          </el-col>
          <!-- 问题视频 -->
          <el-col :span="12">
            <el-form-item label="问题视频" prop="video">
              <el-upload
                ref="uploadVideoRef"
                accept="video/*"
                :auto-upload="false"
                :on-change="
                  (file: any, fileList: any[]) => {
                    uploadVideo(file, fileList)
                  }
                "
                :multiple="false"
                list-type="text"
                action="#"
                :show-file-list="false"
              >
                <el-button
                  :disabled="props.currentRow.dealStatus === '已完成' || state.videoLoading"
                  type="primary"
                  :loading="state.videoLoading"
                  v-if="state.formData.videosList.length < 3 && !computedDisabledState"
                  >{{ state.videoLoading ? '上传中...' : '上传视频' }}</el-button
                >
                <template #tip>
                  <div>
                    <el-tag type="warning">
                      <div class="flex items-center">
                        <el-icon>
                          <Warning />
                        </el-icon>
                        支持扩展名.MP4，可多张上传最多3个，单个视频大小不超过20MB
                      </div>
                    </el-tag>
                  </div>
                </template>
              </el-upload>
            </el-form-item>
            <div style="display: flex; flex-wrap: wrap; gap: 10px">
              <div
                v-for="(item, index) in state.formData.videosList"
                :key="item.url"
                style="padding: 10px; border: 1px solid #ccc; border-radius: 5px; display: flex; flex-direction: column; align-items: center"
                class="w-30%"
              >
                <video :src="item.url" controls style="width: 100%; height: auto"></video>
                <el-text v-if="!computedDisabledState" type="danger" @click="handleVideoRemove(index)" class="mt-10px cursor-pointer">删除</el-text>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <template #footer> </template>
    </el-card>
  </view>
</template>

<script setup lang="ts">
  import {
    getFleetOrderDispatchCarrierSelectOptionApi,
    getFleetOrderDispatchDealTypeSelectOptionApi,
    getFleetOrderDispatchDriverSelectOptionApi,
    getFleetOrderDispatchPlateNumberSelectOptionApi,
    getFleetOrderDispatchSelectOptionByVinApi,
    getOutFleetOrderDispatchVinViewByDispatchNoApi,
    getOutFleetOrderPageApi,
    getQualityLossQualityDamageReasonsSelectOptionApi,
    getDepartmentTreeOptionApi,
  } from '@/api/businessManagement'
  import { QualityLossVO } from '@/api/businessManagement/type'
  import { getOutFleetVehicleAccidentPageApi } from '@/api/financialManagement'
  import { globalUploadV3Api, globalUploadVideoV2Api } from '@/api/Global'
  import UploadImageComponent from '@/components/UploadImageComponent/index.vue'
  import defaultSettings from '@/settings'
  import { UploadUserFile } from 'element-plus'
  import { ElMessage } from 'element-plus'
  import { plateColorOptions } from '@/utils/selectOptions'
  const uploadImageRef = ref()
  const step1FormRef = ref()
  const props = defineProps({
    currentRow: {
      type: Object as PropType<CurrentRowVO>,
      required: true,
    },
    origin: {
      type: String,
      required: true,
    },
  })
  interface CurrentRowVO {
    /**
     * 审批状态
     */
    auditStatus: string
    /**
     * 完成情况
     */
    dealStatus: string
  }
  const computedDisabledState = computed(() => {
    if (JSON.stringify(props.currentRow) !== '{}') {
      return props.currentRow.dealStatus === '已完成'
    } else {
      return false
    }
  })
  const state = reactive({
    baseList: [] as SelectOptions[],
    dispatchInfo: {} as any,
    loading: false,
    plateNumberLoading: false,
    orderVinList: [] as any[],
    accidentList: [] as any[],
    dispatchList: [] as SelectOptions[],
    carrierList: [] as SelectOptions[],
    driverList: [] as SelectOptions[],
    dealTypeList: [] as SelectOptions[],
    damagedAreaList: [] as SelectOptions[],
    dealModeList: [] as SelectOptions[],
    plateNumberList: [] as SelectOptions[],
    plateColorList: plateColorOptions,
    qualityDamageReasonsList: [] as SelectOptions[],
    videoLoading: false,
    formData: {
      baseId: '',
      baseName: '',
      accidentNo: '',
      amount: 0,
      brandName: '',
      buyUp: '',
      carrierInfo: '',
      carrierName: '',
      carrierType: '',
      chargeType: '预计金额',
      createTime: '',
      createUserName: '',
      customerName: '',
      damagedArea: [],
      dealStatus: '',
      dealTime: '',
      dealType: '',
      dealUserName: '',
      dealerContacts: '',
      dealerMobile: '',
      dealerName: '',
      deptId: 0,
      dispatchNo: '',
      driverAdvancePaymentAmount: '',
      driverChargeAmount: '',
      driverId: 0,
      driverInfo: '',
      driverName: '',
      driverType: '',
      happenAddress: '',
      happenTime: '',
      maintenanceFee: 0,
      modifyTime: '',
      modifyUserName: '',
      no: '',
      picsList: [],
      problemDesc: '',
      reducePrice: 0,
      remark: '',
      responsibilityType: '',
      safetyOfficer: '',
      type: [],
      vehicleModelName: '',
      vehicleNo: '',
      plateColor: '',
      videosList: [],
      vin: '',
      qualityDamageReasons: '',
      exportCar: '否',
      cancelLoad: false,
    } as QualityLossVO,
  })
  watch(
    () => state.formData,
    (newVal) => {
      if (!newVal.videosList) {
        state.formData.videosList = []
      }
    },
    {
      deep: true,
    },
  )
  onMounted(() => {
    getCarrierList()
    getDriverList()
    getDealTypeList()
    getDamagedAreaList()
    getDealModeList()
    getQualityDamageReasonsList()
    getBaseList()
  })
  const getOrderVinList = async (query: string) => {
    if (!query) return
    const { data } = await getOutFleetOrderPageApi({ page: 1, limit: defaultSettings.globalLimit, vin: query })
    state.orderVinList = data.rows
  }
  const getAccidentList = async (query: string) => {
    if (!query) return
    const { data } = await getOutFleetVehicleAccidentPageApi({ page: 1, limit: defaultSettings.globalLimit, no: query })
    state.accidentList = data.rows
  }
  const getDispatchList = async (vin: string) => {
    if (!vin) return
    state.formData.dispatchNo = ''
    const res = await getFleetOrderDispatchSelectOptionByVinApi({ vin })
    state.dispatchList = res.data
  }
  const getCarrierList = async () => {
    const res = await getFleetOrderDispatchCarrierSelectOptionApi({})
    state.carrierList = res.data
  }
  const getDriverList = async () => {
    const res = await getFleetOrderDispatchDriverSelectOptionApi({})
    state.driverList = res.data
  }
  const getDealTypeList = async () => {
    const res = await getFleetOrderDispatchDealTypeSelectOptionApi({ type: '质损类型' })
    state.dealTypeList = res.data
  }
  const getDamagedAreaList = async () => {
    const res = await getFleetOrderDispatchDealTypeSelectOptionApi({ type: '质损部位' })
    state.damagedAreaList = res.data
  }
  const getDealModeList = async () => {
    const res = await getFleetOrderDispatchDealTypeSelectOptionApi({ type: '处理模式' })
    state.dealModeList = res.data
  }
  const getQualityDamageReasonsList = async () => {
    const res = await getQualityLossQualityDamageReasonsSelectOptionApi({})
    state.qualityDamageReasonsList = res.data
  }
  const getPlateNumberList = async (query: string) => {
    if (!query) return
    const params = {
      keyword: query,
      label: 'name',
      value: 'name',
      dataSource: '车队-车辆',
      fleetId: state.formData.carrierInfo,
    }
    const res = await getFleetOrderDispatchPlateNumberSelectOptionApi(params)
    state.plateNumberList = res.data
  }
  const getCarrier = async (e: string) => {
    const currentCarrier = state.carrierList.flatMap((item) => item.children || []).find((_item) => _item.value === e)
    state.plateNumberList = []
    if (currentCarrier) {
      state.formData.carrierName = currentCarrier.label
    } else {
      console.error('Carrier not found for value:', e)
      state.formData.carrierName = '' // Handle the case where the carrier is not found
    }
  }
  const rules = ref({
    vin: [{ required: true, message: '请填写质损VIN', trigger: 'blur' }],
    happenTime: [{ required: true, message: '请选择发生时间', trigger: 'change' }],
    happenAddress: [{ required: true, message: '请填写发生地点', trigger: 'blur' }],
    type: [{ required: true, message: '请选择质损类型', trigger: 'change' }],
    damagedArea: [{ required: true, message: '请选择质损部位', trigger: 'change' }],
    problemDesc: [{ required: true, message: '请填写问题描述', trigger: 'blur' }],
  })

  const uploadVideo = (file: any, fileList: any[]) => {
    if (file.status !== 'ready') return

    // Add file size check (20MB limit)
    const maxSize = 20 * 1024 * 1024 // 20MB in bytes
    if (file.raw.size > maxSize) {
      ElMessage.error('视频大小不能超过20MB')
      return
    }

    const params = {
      file: file.raw,
      businessLine: 204,
    }
    state.videoLoading = true
    globalUploadVideoV2Api(params)
      .then((res: any) => {
        state.formData.videosList.push(res.data.uploads[0])
        state.videoLoading = false
      })
      .catch(() => {
        state.videoLoading = false
      })
  }

  const handleVideoRemove = (index: number) => {
    state.formData.videosList.splice(index, 1)
  }
  // 获取上传图片列表
  const getUploadImageList = () => {
    return uploadImageRef.value.uploadImageList
  }
  //设置上传图片列表
  const setUploadImageList = (list: UploadUserFile[]) => {
    uploadImageRef.value.uploadImageList = list
  }
  const getDriverInfo = (e: string) => {
    const driverInfo = state.driverList.flatMap((item) => item.children || []).find((_item) => _item.value === e)
    if (driverInfo) {
      state.formData.driverName = driverInfo.label
    }
  }
  const getDispatchInfo = async (e: string) => {
    if (!e) return
    const { data } = await getOutFleetOrderDispatchVinViewByDispatchNoApi({ no: e })
    state.dispatchInfo = data
    state.formData.customerName = data.customerName
    state.formData.brandName = data.brandName
    state.formData.vehicleModelName = data.modelName
    state.formData.carrierInfo = data.carrierInfo
    state.formData.vehicleNo = data.vehicleNo
    state.formData.dealerName = data.dealerName
    state.formData.plateColor = data.plateColor
    state.plateNumberList = []
    if (data.driverInfo) {
      const driverInfo = state.driverList.flatMap((item) => item.children || []).find((_item) => _item.value === data.driverInfo)
      if (driverInfo) {
        state.formData.driverInfo = driverInfo.value
      } else {
        state.formData.driverInfo = ''
      }
    } else {
      state.formData.driverInfo = ''
    }
  }

  // 获取基地下拉数据
  const getBaseList = async () => {
    try {
      const res = await getDepartmentTreeOptionApi({ enable: true })
      state.baseList = res.data as any[]
    } catch (error) {
      console.error('获取基地数据失败', error)
    }
  }

  // 处理基地选择变化
  const handleBaseChange = (value: string) => {
    if (!value) {
      state.formData.baseName = ''
      state.formData.baseId = ''
      return
    }

    // 查找所选基地项
    const findNode = (list: any[]): any => {
      for (const item of list) {
        if (item.value === value) {
          return item
        }
        if (item.children && item.children.length) {
          const found = findNode(item.children)
          if (found) return found
        }
      }
      return null
    }

    const selectedNode = findNode(state.baseList)
    if (selectedNode) {
      state.formData.baseId = value
      state.formData.baseName = selectedNode.label
    }
  }

  defineExpose({
    step1FormRef,
    state,
    getUploadImageList,
    getDispatchList,
    setUploadImageList,
  })
</script>
