<!--
 * @Author: llm
 * @Date: 2025-01-11 17:47:56
 * @LastEditors: llm
 * @LastEditTime: 2025-05-14 18:11:10
 * @Description:
-->
<template>
  <view>
    <el-card shadow="never" v-loading="state.loading">
      <template #header>
        <div>保险信息</div>
      </template>
      <el-form :disabled="computedDisabledState" ref="step2FormRef" :model="state.formData" :rules="rules" label-width="100px">
        <el-row :gutter="0">
          <el-col :span="6">
            <el-form-item label="保险类型" prop="insuranceType">
              <el-select :empty-values="[null, undefined]" clearable placeholder="请选择保险类型" v-model="state.formData.insuranceType" filterable>
                <el-option :label="item.reportName" :value="item.reportName" v-for="item in state.insuranceTypeList"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 报案时间 -->
          <el-col :span="8">
            <el-form-item label="报案时间" prop="reportTime">
              <el-date-picker
                type="datetime"
                v-model="state.formData.reportTime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder=""
                clearable
              />
            </el-form-item>
          </el-col>
          <!-- 报案号 -->
          <el-col :span="6">
            <el-form-item label="报案号" prop="reportNo">
              <el-input clearable placeholder="" :maxLength="20" v-model="state.formData.reportNo"></el-input>
            </el-form-item>
          </el-col>
          <!-- 是否受理 -->
          <el-col :span="4">
            <el-form-item label="是否受理" prop="accept">
              <el-checkbox v-model="state.formData.accept" :value="true"></el-checkbox>
            </el-form-item>
          </el-col>
          <!-- 受理时间 -->
          <el-col :span="8">
            <el-form-item label="受理时间" prop="acceptTime">
              <el-date-picker
                type="datetime"
                v-model="state.formData.acceptTime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder=""
                clearable
              />
            </el-form-item>
          </el-col>
          <!-- 是否勘察 -->
          <el-col :span="3">
            <el-form-item label="是否勘察" prop="survey">
              <el-checkbox v-model="state.formData.survey" :value="true"></el-checkbox>
            </el-form-item>
          </el-col>
          <!-- 质保险材料提供状态 -->
          <el-col :span="6">
            <el-form-item label="质保险材料提供状态" prop="dataSubmitStatus" label-width="140px">
              <el-select :empty-values="[null, undefined]" clearable placeholder="请选择" v-model="state.formData.dataSubmitStatus" filterable>
                <el-option :label="item.label" :value="item.value" v-for="item in state.dataSubmitStatusList"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 保险公司理赔金额 -->
          <el-col :span="7">
            <el-form-item label="保险公司理赔金额" prop="compensationAmount" label-width="140px">
              <el-input-number clearable placeholder="" :precision="2" :maxLength="20" v-model="state.formData.compensationAmount">
                <template #append>元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <!-- 回款状态 -->
          <el-col :span="8">
            <el-form-item label="回款状态" prop="paymentCollectionStatus">
              <el-select clearable placeholder="" v-model="state.formData.paymentCollectionStatus" filterable>
                <el-option :label="item.label" :value="item.value" v-for="item in state.paymentCollectionStatusList"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 回款时间 -->
          <el-col :span="8">
            <el-form-item label="回款时间" prop="paymentCollectionTime">
              <el-date-picker
                type="datetime"
                v-model="state.formData.paymentCollectionTime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder=""
                clearable
              />
            </el-form-item>
          </el-col>
          <!-- 销案时间 -->
          <el-col :span="8">
            <el-form-item label="销案时间" prop="finishTime">
              <el-date-picker
                type="datetime"
                v-model="state.formData.finishTime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder=""
                clearable
              />
            </el-form-item>
          </el-col>
          <!-- 备注 -->
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input clearable placeholder="" :maxLength="20" v-model="state.formData.remark" type="textarea"></el-input>
            </el-form-item>
          </el-col>
          <!-- 保险材料 -->
          <el-col :span="24">
            <el-form-item label="保险材料" prop="insurancePics">
              <UploadImageComponent tip="支持扩展名.png.jpeg.jpg，可多张上传最多9张" ref="uploadImageRef" :limit="9" :multiple="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-card shadow="never" header="保险材料邮寄信息">
          <el-row :gutter="0" v-for="(item, index) in state.formData.mails" :key="index">
            <el-col :span="6">
              <el-form-item label="邮寄单号" prop="mailTime">
                <el-input clearable placeholder="" :maxLength="20" v-model="item.mailNo"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="邮寄时间" prop="mailTime">
                <el-date-picker
                  clearable
                  type="datetime"
                  v-model="item.mailTime"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder=""
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="邮寄人" prop="mailPerson" label-width="60px">
                <el-input clearable placeholder="" :maxLength="20" v-model="item.mailPerson"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="备注" prop="remark" label-width="60px">
                <el-input clearable placeholder="" :maxLength="20" v-model="item.remark"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <div class="flex items-center ml-10px mt-3px">
                <!-- 新增 -->
                <el-button type="primary" icon="Plus" circle size="small" @click="addMail(index)"></el-button>
                <!-- 删除 -->
                <el-button type="danger" icon="Minus" circle size="small" @click="deleteMail(index)" v-if="state.formData.mails.length > 1"></el-button>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <template #footer> </template>
    </el-card>
  </view>
</template>

<script setup lang="ts">
  import { getQualityLossInsuranceTypeSelectOptionApi } from '@/api/businessManagement'
  import { QuailtyLossInsuranceVO } from '@/api/businessManagement/type'
  import UploadImageComponent from '@/components/UploadImageComponent/index.vue'
  import { UploadUserFile } from 'element-plus'
  const uploadImageRef = ref()
  const step2FormRef = ref()
  const state = reactive({
    loading: false,
    insuranceTypeList: [] as any[],
    dataSubmitStatusList: [
      {
        label: '全部寄出',
        value: '全部寄出',
      },
      {
        label: '补齐材料中',
        value: '补齐材料中',
      },
      {
        label: '准备材料中',
        value: '准备材料中',
      },
    ] as SelectOptions[],
    paymentCollectionStatusList: [
      {
        label: '待回款',
        value: '待回款',
      },
      {
        label: '已回款',
        value: '已回款',
      },
    ] as SelectOptions[],
    formData: {
      accept: false,
      acceptTime: '',
      compensationAmount: 0,
      createTime: '',
      createUserName: '',
      dataSubmitStatus: '',
      deptId: 0,
      finishTime: '',
      insurancePics: '',
      insuranceType: '',
      mails: [
        {
          mailNo: '',
          mailPerson: '',
          mailTime: '',
          remark: '',
          createTime: '',
          createUserName: '',
          deptId: 0,
          modifyTime: '',
          modifyUserName: '',
          qualityLossInsuranceId: 0,
        },
      ],
      modifyTime: '',
      modifyUserName: '',
      paymentCollectionStatus: '',
      paymentCollectionTime: '',
      qualityLossId: 0,
      remark: '',
      reportNo: '',
      reportTime: '',
      survey: '',
    } as QuailtyLossInsuranceVO,
  })
  const props = defineProps({
    currentRow: {
      type: Object as PropType<CurrentRowVO>,
      required: true,
    },
  })
  interface CurrentRowVO {
    /**
     * 审批状态
     */
    auditStatus: string
    /**
     * 完成情况
     */
    dealStatus: string
  }
  const computedDisabledState = computed(() => {
    if (JSON.stringify(props.currentRow) !== '{}') {
      return props.currentRow.dealStatus === '已完成'
    } else {
      return false
    }
  })
  const rules = ref({
    // insuranceType: [{ required: true, message: '请选择保险类型', trigger: 'blur' }],
    // reportTime: [{ required: true, message: '请选择报案时间', trigger: 'blur' }],
    // reportNo: [{ required: true, message: '请填写报案号', trigger: 'blur' }],
    // accept: [{ required: true, message: '请选择是否受理', trigger: 'blur' }],
    // acceptTime: [{ required: true, message: '请选择受理时间', trigger: 'blur' }],
    // survey: [{ required: true, message: '请选择是否勘察', trigger: 'blur' }],
    // dataSubmitStatus: [{ required: true, message: '请选择质保险材料提供状态', trigger: 'blur' }],
    // compensationAmount: [{ required: true, message: '请填写保险公司理赔金额', trigger: 'blur' }],
    // paymentCollectionStatus: [{ required: true, message: '请选择回款状态', trigger: 'blur' }],
    // paymentCollectionTime: [{ required: true, message: '请选择回款时间', trigger: 'blur' }],
    // finishTime: [{ required: true, message: '请选择销案时间', trigger: 'blur' }],
    // remark: [{ required: true, message: '请填写备注', trigger: 'blur' }],
    // insurancePics: [{ required: true, message: '请上传保险材料', trigger: 'blur' }],
  })
  // 获取上传图片列表
  const getUploadImageList = () => {
    return uploadImageRef.value.uploadImageList
  }
  //设置上传图片列表
  const setUploadImageList = (list: UploadUserFile[]) => {
    uploadImageRef.value.uploadImageList = list
  }
  // 新增邮寄信息
  const addMail = (index: number) => {
    state.formData.mails.push({
      mailNo: '',
      mailPerson: '',
      mailTime: '',
      remark: '',
      createTime: '',
      createUserName: '',
      deptId: 0,
      modifyTime: '',
      modifyUserName: '',
      qualityLossInsuranceId: 0,
    })
  }
  // 删除邮寄信息
  const deleteMail = (index: number) => {
    state.formData.mails.splice(index, 1)
  }
  // 获取保险类型下拉
  const getInsuranceTypeSelectOption = async () => {
    const { data } = await getQualityLossInsuranceTypeSelectOptionApi({ enable: true, type: '保险类型' })
    state.insuranceTypeList = data as SelectOptions[]
  }

  defineExpose({
    state,
    step2FormRef,
    getUploadImageList,
    setUploadImageList,
    getInsuranceTypeSelectOption,
  })
</script>
