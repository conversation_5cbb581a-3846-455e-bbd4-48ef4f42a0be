<!--
 * @Author: llm
 * @Date: 2023-06-28 09:45:08
 * @LastEditors: llm
 * @LastEditTime: 2025-02-27 17:22:16
 * @Description: 费用申请
 *
-->
<template>
  <div>
    <el-card shadow="never" v-loading="state.loading">
      <template #header>
        <div>费用申请</div>
      </template>
      <div>
        <el-descriptions :column="3" size="default" :border="true" class="mb-20px">
          <el-descriptions-item label="质损总金额">
            <el-text>{{ props.step1FormData.amount || '-' }}元</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="质损金额类型" v-if="props.step1FormData?.type">
            <el-text>{{ props.step1FormData?.type.join('、') || '-' }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="维修费用">
            <el-text>{{ props.step1FormData.maintenanceFee || '-' }}元</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="降价费用">
            <el-text>{{ props.step1FormData.reducePrice || '-' }}元</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="承运商">
            <el-text>
              {{
                state.detailsFormData.carrierName
                  ? state.detailsFormData.carrierName
                  : `${props.step1FormData.carrierName || '-'} ${props.step1FormData.carrierName ? `(${props.step1FormData.carrierType})` : ''}`
              }}</el-text
            >
          </el-descriptions-item>
          <el-descriptions-item label="司机">
            <el-text>{{
              state.detailsFormData.driverName
                ? state.detailsFormData.driverName
                : `${props.step1FormData.driverName || '-'} ${props.step1FormData.driverName ? `(${props.step1FormData.driverType || '-'})` : ''}`
            }}</el-text>
          </el-descriptions-item>
        </el-descriptions>
        <el-form ref="step3FormRef" label-width="auto" :disabled="computedDisabledState" :model="state.detailsFormData">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="垫付金额："  prop="driverAdvancePaymentAmount">
                <el-input-number :style="{ width: '240px' }" v-model="state.detailsFormData.driverAdvancePaymentAmount" :min="0" :precision="2"
                  ><template #suffix>
                    <span>元</span>
                  </template>
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="司机罚款类型："  prop="fineConfigId">
                <el-select
                  v-model="state.detailsFormData.fineConfigId"
                  :empty-values="[null, undefined]"
                  placeholder="请选择罚款类型"
                  clearable
                  :style="{ width: '240px' }"
                  @change="selectPenaltyType"
                >
                  <el-option v-for="item in state.penaltyTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24"
              ><el-form-item label="挂账金额："  prop="driverChargeAmount">
                <el-input-number :style="{ width: '240px' }" v-model="state.detailsFormData.driverChargeAmount" :precision="2">
                  <template #suffix>
                    <span>元</span>
                  </template>
                </el-input-number>
                <el-radio-group style="margin-left: 20px" v-model="state.detailsFormData.chargeType">
                  <el-radio :value="'预计金额'">预计金额</el-radio>
                  <el-radio :value="'实际金额'">实际金额</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="司机承担金额："  prop="driverBearAmount">
                <el-input-number :style="{ width: '240px' }" v-model="state.detailsFormData.driverBearAmount" :min="0" :precision="2"
                  ><template #suffix>
                    <span>元</span>
                  </template>
                </el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="formClass">
        <h3 style="font-weight: 600" >公司对公支付信息</h3>
        <el-button type="primary" size="small" @click="addForm('public')" v-if="!computedDisabledState">新增</el-button>
      </div>
      <el-table ref="qualityLossDataRef" show-overflow-tooltip :border="true" :data="state.detailsFormData.publicFees">
        <!-- 序号 -->
        <el-table-column align="center" label="序号" type="index" width="60" fixed="left"></el-table-column>
        <el-table-column v-for="item in publicTableConfig.tableItem" :align="item.align" :key="item.name" :label="item.label" :prop="item.name">
          <template #default="{ row }">
            <div v-if="item.name === 'invoiceFile'" style="display: flex; align-items: center; justify-content: center; width: 100%">
              <el-button v-if="row.invoicePicList && row.invoicePicList.length > 0" type="primary" link @click="downLoadFile(row.invoicePicList)"
                >下载</el-button
              >
            </div>
            <div v-else style="display: flex; align-items: center; justify-content: center; width: 100%">
              <el-text>{{ row[item.name] }}</el-text>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180px" align="center" fixed="right">
          <template #default="scope">
            <div style="display: flex; align-items: center; justify-content: center; width: 100%">
              <el-button
                type="primary"
                link
                @click="editColumn(scope.row, scope.$index, 'public', 'editTicket')"
                v-if="currentRow.auditStatus === '待提交' || currentRow.auditStatus === '审批驳回'"
                >编辑</el-button
              >
              <el-button
                type="primary"
                link
                @click="editColumn(scope.row, scope.$index, 'public', 'addTicket')"
                v-if="
                  currentRow.auditStatus !== '待提交' &&
                  currentRow.auditStatus !== '审批驳回' &&
                  (scope.row.invoicePicList === null || scope.row.invoicePicList.length === 0)
                "
                >发票识别</el-button
              >
              <el-button type="primary" link @click="deleteColumn(scope.row, scope.$index, 'public')" v-if="!computedDisabledState">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="formClass">
        <h3 style="font-weight: 600">公司对私支付信息</h3>
        <el-button type="primary" size="small" @click="addForm('private')" v-if="!computedDisabledState">新增</el-button>
      </div>
      <el-table ref="qualityLossDataRef" show-overflow-tooltip :border="true" :data="state.detailsFormData.privateFees">
        <!-- 序号 -->
        <el-table-column align="center" label="序号" type="index" width="60" fixed="left"></el-table-column>
        <el-table-column v-for="item in privateTableConfig.tableItem" :align="item.align" :key="item.name" :label="item.label" :prop="item.name">
          <template #default="{ row }">
            <div style="display: flex; align-items: center; justify-content: center; width: 100%">
              <el-text>{{ row[item.name] }}</el-text>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180px" align="center" fixed="right" v-if="!computedDisabledState">
          <template #default="scope">
            <div style="display: flex; align-items: center; justify-content: center; width: 100%">
              <el-button type="primary" link @click="editColumn(scope.row, scope.$index, 'private')">编辑</el-button>
              <el-button type="primary" link @click="deleteColumn(scope.row, scope.$index, 'private')">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 新增编辑对公对私信息 -->
    <el-dialog
      v-model="state.publicPrivateDialog"
      append-to-body
      :title="state.isAdd ? '新增' : '编辑'"
      :draggable="true"
      :close-on-click-modal="false"
      @close="closeAddDialog"
    >
      <el-form ref="formRef" :model="publicFrom" label-width="80px" v-if="state.formType === 'public'">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="发票文件">
              <el-upload
                :file-list="publicFrom.invoicePicList"
                ref="uploadFileRef"
                action="#"
                :auto-upload="false"
                :limit="1"
                :on-change="
                  (file: any, fileList: any[]) => {
                    uploadFile(file, fileList)
                  }
                "
                :on-remove="
                  (file: any, fileList: any[]) => {
                    handleRemove(file, fileList)
                  }
                "
                :on-exceed="handleExceed"
              >
                <template #trigger>
                  <el-button type="primary">点击上传</el-button>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发票号">
              <el-input :disabled="state.ocrDisabledObj.invoiceNo" v-model="publicFrom.invoiceNo" style="width: 240px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="未税金额" prop="untaxedAmount" :rules="{ required: true, message: '请输入未税金额' }">
              <el-input-number
                :disabled="state.ocrDisabledObj.untaxedAmount"
                :style="{ width: '240px' }"
                v-model="publicFrom.untaxedAmount"
                :min="0"
                :precision="2"
                ><template #suffix>
                  <span>元</span>
                </template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="税额" prop="taxAmount" :rules="{ required: true, message: '请输入税额' }">
              <el-input-number :disabled="state.ocrDisabledObj.taxAmount" :style="{ width: '240px' }" v-model="publicFrom.taxAmount" :min="0" :precision="2"
                ><template #suffix>
                  <span>元</span>
                </template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="含税金额" prop="taxedAmount" :rules="{ required: true, message: '请输入含税金额' }">
              <el-input-number
                :disabled="state.ocrDisabledObj.taxedAmount"
                :style="{ width: '240px' }"
                v-model="publicFrom.taxedAmount"
                :min="0"
                :precision="2"
              >
                <template #suffix>
                  <span>元</span>
                </template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户银行" prop="bank" :rules="{ required: true, message: '请输入开户银行' }">
              <el-input :disabled="state.ocrDisabledObj.bank" v-model="publicFrom.bank" style="width: 240px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户名称" prop="accountName" :rules="{ required: true, message: '请输入开户名称' }">
              <el-input :disabled="state.ocrDisabledObj.accountName" v-model="publicFrom.accountName" style="width: 240px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="银行账号" prop="accountNo" :rules="{ required: true, message: '请输入银行账号' }">
              <el-input :disabled="state.ocrDisabledObj.accountNo" v-model="publicFrom.accountNo" style="width: 240px" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form ref="formRef" :model="privateFrom" label-width="80px" v-if="state.formType === 'private'">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="姓名">
              <el-input v-model="privateFrom.name" style="width: 240px" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="金额">
              <el-input-number :style="{ width: '240px' }" v-model="privateFrom.amount" :min="0" :precision="2">
                <template #suffix>
                  <span>元</span>
                </template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="银行账号">
              <el-input v-model="privateFrom.accountNo" style="width: 240px" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeAddDialog()">取消</el-button>
          <el-button type="primary" @click="submitForm()">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import {
    getAccidentApprovalDetailsApi,
    fineSelectOptionApi,
    postQualityLossFineAmountApi,
    postQualityLossPublicFeeApplyAddInvoiceApi,
  } from '@/api/businessManagement'
  import { UploadProps, UploadRawFile, genFileId } from 'element-plus'
  import { uploadFileApi } from '@/api/auth'
  import { PrivateFee, PublicFee, QualityLossFeeApplyVO, QualityLossVO } from '@/api/businessManagement/type'
  const step3FormRef = ref()
  const props = defineProps({
    origin: {
      type: String,
      default: 'add', // add 新增 edit 编辑
    },
    qualityLossId: {
      type: String,
      default: '',
    },
    step1FormData: {
      type: Object as PropType<QualityLossVO>,
      default: {
        type: [],
      },
    },
    currentRow: {
      type: Object as PropType<CurrentRowVO>,
      default: {},
    },
  })
  watch(
    () => props.step1FormData,
    (newVal) => {},
  )

  interface CurrentRowVO {
    /**
     * 审批状态
     */
    auditStatus: string
    /**
     * 完成情况
     */
    dealStatus: string
  }
  const computedDisabledState = computed(() => {
    if (JSON.stringify(props.currentRow) !== '{}') {
      return props.currentRow.dealStatus === '已完成' || (props.currentRow.auditStatus !== '待提交' && props.currentRow.auditStatus !== '审批驳回')
    } else {
      return false
    }
  })
  const emit = defineEmits(['closeDialog'])
  const formRef = ref()
  const uploadFileRef = ref()

  const state = reactive({
    loading: false,
    isAdd: true,
    formType: '',
    ticketEditType: '', // 发票识别类型， 补票 、 新增
    detailsFormData: {
      carrierName: '',
      driverName: '',
      fineConfigId: '',
      fineConfigType: '',
      chargeType: '预计金额',
      driverBearAmount: 0,
      driverAdvancePaymentAmount: 0,
      driverChargeAmount: 0,
      privateFees: [] as PrivateFee[],
      publicFees: [] as PublicFee[],
      qualityLossId: props.qualityLossId,
      penaltyType: '',
    } as QualityLossFeeApplyVO,
    publicPrivateDialog: false,
    qualityLossPrivateFeeApplyList: [] as PrivateFee[],
    qualityLossPublicFeeApplyList: [] as PublicFee[],
    fileList: [],
    penaltyTypeList: [] as SelectOptions[],
    ocrDisabledObj: {
      invoiceNo: false,
      taxAmount: false,
      untaxedAmount: false,
      taxedAmount: false,
      bank: false,
      accountName: false,
      accountNo: false,
    },
  })

  /**
   * 表单参数
   */
  const publicFrom = ref<PublicFee>({
    id: '',
    accidentId: '',
    accountName: '',
    accountNo: '',
    bank: '',
    invoiceNo: '',
    invoicePicList: [],
    taxAmount: 0,
    taxedAmount: 0,
    untaxedAmount: 0,
    qualityLossId: '',
  })
  const privateFrom = ref<PrivateFee>({
    amount: 0,
    name: '',
    accountNo: '',
    qualityLossId: '',
  })
  // 对公信息
  const publicTableConfig = {
    tableItem: [
      {
        name: 'invoiceNo',
        label: '发票号',
        align: 'center',
      },
      {
        name: 'untaxedAmount',
        label: '未税金额',
        align: 'center',
      },
      {
        name: 'taxAmount',
        label: '税额',
        align: 'center',
      },
      {
        name: 'taxedAmount',
        label: '含税金额',
        align: 'center',
      },
      {
        name: 'bank',
        label: '开户银行',
        align: 'center',
      },
      {
        name: 'accountName',
        label: '开户名称',
        align: 'center',
      },
      {
        name: 'accountNo',
        label: '银行账号',
        align: 'center',
      },
      {
        name: 'invoiceFile',
        label: '发票文件',
        align: 'center',
      },
    ],
  }
  // 对私信息
  const privateTableConfig = {
    tableItem: [
      {
        name: 'amount',
        label: '金额',
        align: 'center',
      },
      {
        name: 'name',
        label: '姓名',
        align: 'center',
      },
      {
        name: 'accountNo',
        label: '银行账户',
        align: 'center',
      },
    ],
  }
  // 编辑
  const editColumn = (row: any, index: number, type: string, editType?: string) => {
    state.ticketEditType = editType ?? ''
    let rowData = JSON.parse(JSON.stringify(row))
    if (type === 'public') {
      publicFrom.value = {
        invoiceNo: rowData.invoiceNo,
        untaxedAmount: rowData.untaxedAmount ? rowData.untaxedAmount : 0,
        taxAmount: rowData.taxAmount ? rowData.taxAmount : 0,
        taxedAmount: rowData.taxedAmount ? rowData.taxedAmount : 0,
        bank: rowData.bank,
        accountName: rowData.accountName,
        accountNo: rowData.accountNo,
        invoicePicList: rowData.invoicePicList ?? [],
        accidentId: rowData.accidentId,
        fontTempId: rowData.fontTempId,
        qualityLossId: props.qualityLossId,
        id: rowData.id,
      }
      if (
        (props.currentRow.dealStatus === '未完成' && props.currentRow.auditStatus !== '待提交' && props.currentRow.auditStatus !== '审批驳回') ||
        props.currentRow.dealStatus === '已完成'
      ) {
        state.ocrDisabledObj.invoiceNo = rowData.invoiceNo ? true : false
        state.ocrDisabledObj.taxAmount = rowData.taxAmount ? true : false
        state.ocrDisabledObj.untaxedAmount = rowData.untaxedAmount ? true : false
        state.ocrDisabledObj.taxedAmount = rowData.taxedAmount ? true : false
        state.ocrDisabledObj.bank = rowData.bank ? true : false
        state.ocrDisabledObj.accountName = rowData.accountName ? true : false
        state.ocrDisabledObj.accountNo = rowData.accountNo ? true : false
      }
      // state.attachmentsUrl = rowData.invoicePicList ? JSON.parse(rowData.invoicePicList)[0].url : ''
      // state.attachments = rowData.invoicePicList ? JSON.parse(rowData.invoicePicList)[0].name : ''
    } else if (type === 'private') {
      privateFrom.value = {
        amount: rowData.amount ? rowData.amount : 0,
        name: rowData.name,
        accountNo: rowData.accountNo,
        fontTempId: rowData.fontTempId,
        qualityLossId: props.qualityLossId,
      }
    }
    state.formType = type
    state.isAdd = false
    state.publicPrivateDialog = true
  }

  // 获取数据
  const getDetails = async (id: string) => {
    // 获取下拉数据
    getFineDataList()
    getAccidentApprovalDetailsApi({ id: id })
      .then((res: any) => {
        state.detailsFormData = res.data
        state.detailsFormData.privateFees = res.data.qualityLossPrivateFeeApplyList
        state.detailsFormData.publicFees = res.data.qualityLossPublicFeeApplyList.map((item: any) => {
          return {
            ...item,
            invoicePicList: item.invoicePicList ?? [],
          }
        })
      })
      .catch(() => {
        state.detailsFormData.privateFees = []
        state.detailsFormData.publicFees = []
      })
  }

  // 获取司机罚款类型
  const getFineDataList = async () => {
    fineSelectOptionApi({})
      .then((res: any) => {
        state.penaltyTypeList = res.data
      })
      .catch(() => {
        state.penaltyTypeList = []
      })
  }

  /**
   * 保存
   */
  const submitForm = async () => {
    formRef.value.validate().then(async () => {
      if (state.formType === 'public') {
        const formParams = JSON.parse(JSON.stringify(publicFrom.value))
        const params: PublicFee = {
          qualityLossId: props.qualityLossId,
          accountName: formParams.accountName,
          accountNo: formParams.accountNo,
          bank: formParams.bank,
          invoiceNo: formParams.invoiceNo,
          invoicePicList: formParams.invoicePicList,
          taxAmount: formParams.taxAmount,
          taxedAmount: formParams.taxedAmount,
          untaxedAmount: formParams.untaxedAmount,
          fontTempId: formParams.fontTempId,
        }
        let currentRow = {} as any
        if (state.isAdd) {
          currentRow = state.detailsFormData.publicFees.find((item: any) => item.fontTempId === formParams.fontTempId)
        } else {
          currentRow = state.detailsFormData.publicFees.find((item: any) => item.id === formParams.id)
        }
        if (currentRow) {
          //更新
          currentRow.invoiceNo = formParams.invoiceNo
          currentRow.untaxedAmount = formParams.untaxedAmount
          currentRow.taxAmount = formParams.taxAmount
          currentRow.taxedAmount = formParams.taxedAmount
          currentRow.bank = formParams.bank
          currentRow.accountName = formParams.accountName
          currentRow.accountNo = formParams.accountNo
          currentRow.invoicePicList = formParams.invoicePicList
        } else {
          state.detailsFormData.publicFees.push(params)
        }
        if (!state.isAdd && state.ticketEditType === 'addTicket') {
          const res = await postQualityLossPublicFeeApplyAddInvoiceApi({
            id: currentRow?.id,
            invoicePicList: formParams.invoicePicList,
          })
          ElMessage.success('上传成功')
        }
      } else if (state.formType === 'private') {
        const formParams = JSON.parse(JSON.stringify(privateFrom.value))
        const params: PrivateFee = {
          accountNo: formParams.accountNo,
          amount: formParams.amount,
          name: formParams.name,
          qualityLossId: props.qualityLossId,
          fontTempId: formParams.fontTempId,
        }
        const currentRow = state.detailsFormData.privateFees.find((item: any) => item.fontTempId === formParams.fontTempId)
        if (currentRow) {
          //更新
          currentRow.amount = formParams.amount
          currentRow.name = formParams.name
          currentRow.accountNo = formParams.accountNo
        } else {
          state.detailsFormData.privateFees.push(params)
        }
      }
      state.publicPrivateDialog = false
    })
  }

  // 下载文件
  const downLoadFile = (row: any) => {
    let newRow = row
    window.open(newRow[0].url)
  }

  // 删除
  const deleteColumn = (row: any, index: number, type: string) => {
    if (type === 'public') {
      // if (props.origin === 'add') {
      //   state.detailsFormData.publicFees = state.detailsFormData.publicFees.filter((item: any) => item.fontTempId !== row.fontTempId)
      // } else {
      //   state.detailsFormData.publicFees = state.detailsFormData.publicFees.filter((item: any) => item.id !== row.id)
      // }
      state.detailsFormData.publicFees = state.detailsFormData.publicFees.filter((item: any) => {
        if (item.fontTempId && item.fontTempId === row.fontTempId) {
          return false
        }
        if (item.id && item.id === row.id) {
          return false
        }
        return true
      })
    } else if (type === 'private') {
      // if (props.origin === 'add') {
      //   state.detailsFormData.privateFees = state.detailsFormData.privateFees.filter((item: any) => item.fontTempId !== row.fontTempId)
      // } else {
      //   state.detailsFormData.privateFees = state.detailsFormData.privateFees.filter((item: any) => item.id !== row.id)
      // }
      state.detailsFormData.privateFees = state.detailsFormData.privateFees.filter((item: any) => {
        if (item.fontTempId && item.fontTempId === row.fontTempId) {
          return false
        }
        if (item.id && item.id === row.id) {
          return false
        }
        return true
      })
    }
  }


  // 新增
  const addForm = (type: any) => {
    state.formType = type
    state.isAdd = true
    if (type === 'public') {
      publicFrom.value = {
        accidentId: '',
        invoiceNo: '',
        untaxedAmount: 0,
        taxAmount: 0,
        taxedAmount: 0,
        bank: '',
        accountName: '',
        accountNo: '',
        qualityLossId: props.qualityLossId,
        invoicePicList: [],
        fontTempId: new Date().getTime().toString(),
      }
    } else if (type === 'private') {
      privateFrom.value = {
        amount: 0,
        name: '',
        accountNo: '',
        qualityLossId: props.qualityLossId,
        fontTempId: new Date().getTime().toString(),
      }
    }
    state.publicPrivateDialog = true
  }

  /**
   * 关闭弹窗
   */
  const closeDialog = () => {
    emit('closeDialog')
  }

  //上传发票
  const uploadFile = async (file: any, fileList: any[]) => {
    if (file.status !== 'ready') return
    const params = {
      file: file.raw,
      businessLine: 204,
    }
    let res = await uploadFileApi('logistics/system/common/invoice/ocr', params) // 此处为自己的上传接口
    const bankInfo = res.data.buyerAccount ?? ''
    //银行是从后截取到不是数字为止
    const accountNo = bankInfo?.match(/\d+$/)?.[0] ?? ''
    const bank = bankInfo.replace(accountNo, '')
    publicFrom.value.invoicePicList = res.data.uploads

    if (
      (props.currentRow.dealStatus === '未完成' && props.currentRow.auditStatus !== '待提交' && props.currentRow.auditStatus !== '审批驳回') ||
      props.currentRow.dealStatus === '已完成'
    ) {
      state.ocrDisabledObj.invoiceNo = publicFrom.value.invoiceNo ? true : false
      state.ocrDisabledObj.taxAmount = publicFrom.value.taxAmount ? true : false
      state.ocrDisabledObj.untaxedAmount = publicFrom.value.untaxedAmount ? true : false
      state.ocrDisabledObj.taxedAmount = publicFrom.value.taxedAmount ? true : false
      state.ocrDisabledObj.bank = publicFrom.value.bank ? true : false
      state.ocrDisabledObj.accountName = publicFrom.value.accountName ? true : false
      state.ocrDisabledObj.accountNo = publicFrom.value.accountNo ? true : false

      publicFrom.value.invoiceNo = publicFrom.value.invoiceNo || res.data.number // 发票号
      publicFrom.value.taxAmount = publicFrom.value.taxAmount || res.data.taxAmount // 税额
      publicFrom.value.untaxedAmount = publicFrom.value.untaxedAmount || res.data.amount // 未税金额
      publicFrom.value.taxedAmount = publicFrom.value.taxedAmount || res.data.totalAmount // 含税金额
      publicFrom.value.bank = publicFrom.value.bank || bank // 开户银行
      publicFrom.value.accountName = publicFrom.value.accountName || res.data.buyerName // 开户名称
      publicFrom.value.accountNo = publicFrom.value.accountNo || accountNo // 银行账号
    } else {
      state.ocrDisabledObj.invoiceNo = false
      state.ocrDisabledObj.taxAmount = false
      state.ocrDisabledObj.untaxedAmount = false
      state.ocrDisabledObj.taxedAmount = false
      state.ocrDisabledObj.bank = false
      state.ocrDisabledObj.accountName = false
      state.ocrDisabledObj.accountNo = false

      publicFrom.value.invoiceNo = res.data.number // 发票号
      publicFrom.value.taxAmount = res.data.taxAmount // 税额
      publicFrom.value.untaxedAmount = res.data.amount // 未税金额
      publicFrom.value.taxedAmount = res.data.totalAmount // 含税金额
      publicFrom.value.bank = bank // 开户银行
      publicFrom.value.accountName = res.data.buyerName // 开户名称
      publicFrom.value.accountNo = accountNo // 银行账号
    }
  }

  const handleRemove = (file: any, fileList: any[]) => {
    //删除当前文件
    publicFrom.value.invoicePicList = publicFrom.value.invoicePicList.filter((item: any) => item.uid !== file.uid)
  }

  // 替换文件
  const handleExceed: UploadProps['onExceed'] = (files) => {
    uploadFileRef.value!.clearFiles()
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    uploadFileRef.value!.handleStart(file)
  }

  const closeAddDialog = () => {
    state.publicPrivateDialog = false
  }
  const selectPenaltyType = async (e: string) => {
    if (!e) return
    const params = {
      fineConfigId: e,
      qualityLossId: props.qualityLossId,
    }
    state.detailsFormData.fineConfigId = e
    state.detailsFormData.fineConfigType = state.penaltyTypeList.find((item: any) => item.value === e)!.label
    const { data } = await postQualityLossFineAmountApi(params)
    state.detailsFormData.driverChargeAmount = data
  }

  defineExpose({
    state, //状态
    step3FormRef,
    getFineDataList,
    getDetails, //获取质量损失
    closeDialog, //关闭弹窗
  })
</script>

<style scoped>
  .textColor {
    color: #fca130;
  }
  .formClass {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    border-bottom: 1px solid #eee;
    padding: 8px 10px;
    margin: 20px 0;
  }
</style>
