<!--
 * @Author: llm
 * @Date: 2025-01-21 15:25:54
 * @LastEditors: llm
 * @LastEditTime: 2025-02-15 11:13:04
 * @Description:
-->
<template>
  <div>
    <div class="flex justify-center" v-if="!computedDisabledState && state.formData.recycles.length === 0">
      <el-button type="primary" @click="addTableItem(0)">新增回收物品信息</el-button>
    </div>
    <el-form :disabled="computedDisabledState" ref="step4FormRef" :model="state.formData" label-width="120px" v-loading="state.loading" v-else>
      <el-row :gutter="0" v-for="(item, index) in state.formData.recycles" :key="index">
        <el-col :span="7">
          <el-form-item label="回收物品名称" :prop="`recycles.${index}.name`" :rules="[{ required: true, message: '请输入回收物品名称', trigger: 'blur' }]">
            <el-input placeholder="请输入" :maxLength="20" v-model="item.name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item
            label="回收物品去向"
            :prop="`recycles.${index}.destination`"
            :rules="[{ required: true, message: '请输入回收物品去向', trigger: 'blur' }]"
          >
            <el-input placeholder="请输入" v-model="item.destination"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="接收人" :prop="`recycles.${index}.recipient`" :rules="[{ required: true, message: '请输入接收人', trigger: 'blur' }]">
            <el-input placeholder="请输入" v-model="item.recipient"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="3" v-if="!computedDisabledState">
          <el-form-item label-width="20">
            <div class="flex items-center mt-4px">
              <el-button type="primary" icon="Plus" size="small" circle @click="addTableItem(index)" />
              <el-button type="danger" icon="Delete" size="small" circle @click="deleteTableItem(index)" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
  import { QualityLossRecycleVO } from '@/api/businessManagement/type'
  const step4FormRef = ref()
  const props = defineProps({
    currentRow: {
      type: Object as PropType<CurrentRowVO>,
      required: true,
    },
  })
  interface CurrentRowVO {
    /**
     * 审批状态
     */
    auditStatus: string
    /**
     * 完成情况
     */
    dealStatus: string
  }
  const computedDisabledState = computed(() => {
    if (JSON.stringify(props.currentRow) !== '{}') {
      return props.currentRow.dealStatus === '已完成'
    } else {
      return false
    }
  })
  const state = reactive({
    loading: false,
    formData: {
      qualityLossId: '',
      recycles: [
        // {
        //   destination: '',
        //   name: '',
        //   qualityLossId: '',
        //   recipient: '',
        // },
      ],
    } as QualityLossRecycleVO,
    originRecycles: [] as QualityLossRecycleVO[], //用来区分新增还是编辑，当数据>0则走编辑
  })

  const addTableItem = (index: number) => {
    state.formData.recycles.push({
      destination: '',
      name: '',
      qualityLossId: '',
      recipient: '',
    })
  }
  const deleteTableItem = (index: number) => {
    state.formData.recycles.splice(index, 1)
  }
  defineExpose({
    step4FormRef,
    state,
  })
</script>
