<!--
 * @Author: llm
 * @Date: 2025-01-11 11:16:01
 * @LastEditors: llm
 * @LastEditTime: 2025-05-14 18:09:24
 * @Description:
-->
<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :lock-scroll="true"
      :draggable="true"
      v-model="state.dialogVisible.visible"
      :title="state.dialogVisible.title"
      width="80%"
      @close="closeDialog"
    >
      <div class="custom-style mb-10px">
        <el-segmented :disabled="state.origin === 'add'" v-model="stepIndex" :options="state.options" @change="changeStep">
          <template #default="{ item }: { item: any }">
            <div class="flex items-center">
              <div>{{ item.label }}</div>
              <el-icon size="20">
                <component :is="item.icon" />
              </el-icon>
            </div>
          </template>
        </el-segmented>
      </div>
      <el-scrollbar max-height="60vh" class="formClass">
        <Step1 v-show="stepIndex === '1'" ref="step1Ref" :currentRow="state.currentRow" :origin="state.origin" />
        <Step2 v-show="stepIndex === '2'" :qualityLossId="state.qualityLossId" :currentRow="state.currentRow" ref="step2Ref" />
        <Step3
          v-show="stepIndex === '3'"
          :qualityLossId="state.qualityLossId"
          :step1FormData="step1Ref?.state.formData || {}"
          ref="step3Ref"
          :origin="state.origin"
          :currentRow="state.currentRow"
        />
        <Step4 v-show="stepIndex === '4'" :currentRow="state.currentRow" :qualityLossId="state.qualityLossId" ref="step4Ref" />
      </el-scrollbar>
      <template #footer>
        <div class="flex justify-end" v-if="stepIndex === '1'">
          <!-- <el-button @click="saveStep1">保存</el-button> -->
          <el-button v-if="state.currentRow.dealStatus !== '已完成'" type="primary" @click="nextStep">保存当前并下一步</el-button>
          <el-button v-if="state.currentRow.dealStatus === '已完成'" type="primary" @click="nextStep1">下一步</el-button>
        </div>
        <div class="flex justify-end" v-if="stepIndex === '2'">
          <el-button type="primary" @click="prevStep">上一步</el-button>
          <!-- <el-button @click="saveStep2">保存</el-button> -->
          <el-button v-if="state.currentRow.dealStatus !== '已完成'" type="primary" @click="nextStep">保存当前并下一步</el-button>
          <el-button v-if="state.currentRow.dealStatus === '已完成'" type="primary" @click="nextStep1">下一步</el-button>
        </div>
        <div class="flex justify-end" v-if="stepIndex === '3'">
          <el-button type="primary" @click="prevStep">上一步</el-button>
          <!-- <el-button @click="saveStep3">保存</el-button> -->
          <el-button type="primary" @click="nextStep" v-if="state.currentRow.dealStatus !== '已完成'">保存当前并下一步</el-button>
          <el-button v-if="state.currentRow.dealStatus === '已完成'" type="primary" @click="nextStep1">下一步</el-button>
          <!-- 完成情况=未完成时，非待提交和审批驳回不展示按钮 -->
          <el-button
            type="warning"
            @click="submitStep3"
            v-if="
              JSON.stringify(state.currentRow) === '{}' ||
              (state.currentRow.dealStatus === '未完成' && (state.currentRow.auditStatus === '待提交' || state.currentRow.auditStatus === '审批驳回'))
            "
            >金额确认无误，提交审批</el-button
          >
        </div>
        <div class="flex justify-end" v-if="stepIndex === '4'">
          <el-button type="primary" @click="prevStep">上一步</el-button>
          <el-button @click="saveStep4" v-if="state.currentRow.dealStatus !== '已完成'">保存</el-button>
          <el-button v-if="state.currentRow.dealStatus === '已完成'" type="primary" @click="nextStep1">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import { ArrowRight } from '@element-plus/icons-vue'
  import Step1 from './components/step1.vue'
  import Step2 from './components/step2.vue'
  import Step3 from './components/step3.vue'
  import Step4 from './components/step4.vue'
  import { QualityLossVO } from '@/api/businessManagement/type'
  import {
    getQualityLossApi,
    getQualityLossFeeInfoApi,
    getQualityLossInsuranceInfoApi,
    getQualityLossRecycleAllApi,
    postFleetOrderDispatchQualityLossApi,
    postFleetOrderDispatchQualityLossFeeApplyApi,
    postFleetOrderDispatchQualityLossInsuranceApi,
    postFleetOrderDispatchQualityLossSubmitApproveApi,
    postQualityLossRecycleBatchAddApi,
    postQualityLossRecycleBatchEditApi,
    putFleetOrderDispatchQualityLossApi,
    putFleetOrderDispatchQualityLossInsuranceApi,
  } from '@/api/businessManagement'
  import { useUserStoreHook } from '@/store/modules/user'
  const userStore = useUserStoreHook()
  const emit = defineEmits(['closeDialog', 'closeQualityLossDialog'])
  const step1Ref = ref()
  const step2Ref = ref()
  const step3Ref = ref()
  const step4Ref = ref()
  const state = reactive({
    origin: 'add',
    dialogVisible: {
      visible: false,
      title: '新增质损信息',
    },
    qualityLossId: '', //质损id
    formData: {} as QualityLossVO,
    currentRow: {} as any,
    options: [] as any[],
  })
  watch(
    () => state.dialogVisible.visible,
    (newVal) => {
      if (newVal) {
        if (state.origin === 'add' && stepIndex.value === '1') {
          nextTick(() => {
            step1Ref.value.state.formData.safetyOfficer = userStore.userInfo.realName
          })
        }
      }
    },
  )
  watch(
    () => state.currentRow,
    (newVal) => {
      if (newVal.accidentNo) {
        state.options = [
          {
            label: '质损信息',
            value: '1',
            icon: ArrowRight,
          },
          {
            label: '保险信息',
            value: '2',
            icon: ArrowRight,
          },
          {
            label: '回收旧件',
            value: '4',
            icon: ArrowRight,
          },
        ]
      } else {
        state.options = [
          {
            label: '质损信息',
            value: '1',
            icon: ArrowRight,
          },
          {
            label: '保险信息',
            value: '2',
            icon: ArrowRight,
          },
          {
            label: '费用申请',
            value: '3',
            icon: ArrowRight,
          },
          {
            label: '回收旧件',
            value: '4',
            icon: ArrowRight,
          },
        ]
      }
    },
  )
  const stepIndex = ref<string>('1')

  const closeDialog = () => {
    state.dialogVisible.visible = false
    step1Ref.value.setUploadImageList([])
    step2Ref.value.setUploadImageList([])
    step3Ref.value.state.detailsFormData.id = ''
    step4Ref.value.state.formData.id = ''
    step1Ref.value.state.formData = {
      accidentNo: '',
      amount: 0,
      brandName: '',
      buyUp: '',
      carrierInfo: '',
      carrierName: '',
      carrierType: '',
      chargeType: '预计金额',
      createTime: '',
      createUserName: '',
      customerName: '',
      damagedArea: [],
      dealStatus: '',
      dealTime: '',
      dealType: '',
      dealUserName: '',
      dealerContacts: '',
      dealerMobile: '',
      dealerName: '',
      deptId: 0,
      dispatchNo: '',
      driverAdvancePaymentAmount: '',
      driverChargeAmount: '',
      driverId: 0,
      driverInfo: '',
      driverName: '',
      driverType: '',
      happenAddress: '',
      happenTime: '',
      maintenanceFee: 0,
      modifyTime: '',
      modifyUserName: '',
      no: '',
      picsList: [],
      problemDesc: '',
      reducePrice: 0,
      remark: '',
      responsibilityType: '',
      safetyOfficer: '',
      type: [],
      vehicleModelName: '',
      vehicleNo: '',
      videosList: [],
      vin: '',
      cancelLoad: false,
    }
    //清除校验
    step1Ref.value.step1FormRef.resetFields()
    step2Ref.value.state.formData = {
      accept: false,
      acceptTime: '',
      compensationAmount: 0,
      createTime: '',
      createUserName: '',
      dataSubmitStatus: '',
      deptId: 0,
      finishTime: '',
      insurancePics: '',
      insuranceType: '',
      mails: [
        {
          mailNo: '',
          mailPerson: '',
          mailTime: '',
          remark: '',
          createTime: '',
          createUserName: '',
          deptId: 0,
          modifyTime: '',
          modifyUserName: '',
          qualityLossInsuranceId: 0,
        },
      ],
      modifyTime: '',
      modifyUserName: '',
      paymentCollectionStatus: '',
      paymentCollectionTime: '',
      qualityLossId: 0,
      remark: '',
      reportNo: '',
      reportTime: '',
      survey: '',
    }
    step2Ref.value.step2FormRef.resetFields()
    step2Ref.value.state.formData.mails = []
    step3Ref.value.state.detailsFormData = {
      fineConfigId: '',
      fineConfigType: '',
      chargeType: '预计金额',
      driverAdvancePaymentAmount: 0,
      driverChargeAmount: 0,
      privateFees: [],
      publicFees: [],
      qualityLossId: state.qualityLossId,
      penaltyType: '',
    }
    step3Ref.value.step3FormRef.resetFields()
    step4Ref.value.state.formData = {
      qualityLossId: '',
      recycles: [],
    }
    if (step4Ref.value.state.formData.recycles.length > 0) {
      step4Ref.value.step4FormRef.resetFields()
    }
    state.dialogVisible.visible = false
    emit('closeDialog')
  }
  const nextStep = async () => {
    switch (stepIndex.value) {
      case '1':
        await saveStep1()
        stepIndex.value = '2'
        changeStep('2')
        break
      case '2':
        await saveStep2()
        stepIndex.value = '3'
        changeStep('3')
        break
      case '3':
        await saveStep3()
        stepIndex.value = '4'
        changeStep('4')
        break
      case '4':
        await saveStep4()
        break
      default:
        break
    }
  }
  const nextStep1 = async () => {
    switch (stepIndex.value) {
      case '1':
        stepIndex.value = '2'
        changeStep('2')
        break
      case '2':
        stepIndex.value = '3'
        changeStep('3')
        break
      case '3':
        stepIndex.value = '4'
        changeStep('4')
        break
      case '4':
        state.dialogVisible.visible = false
        break
      default:
        break
    }
  }
  const saveStep1 = async () => {
    //校验step1表单
    const valid = await step1Ref.value.step1FormRef.validate()
    if (!valid) return
    const params = {
      ...step1Ref.value.state.formData,
      driverId: undefined,
      picsList: getUploadStep1ImageList(),
      damagedArea: step1Ref.value.state.formData.damagedArea.join(','),
      type: step1Ref.value.state.formData.type.join(','),
    }
    if (!params.id) {
      const { data } = await postFleetOrderDispatchQualityLossApi(params)
      ElMessage.success('保存成功')
      state.qualityLossId = data
    } else {
      const { data } = await putFleetOrderDispatchQualityLossApi(params)
      ElMessage.success('保存成功')
    }
  }
  const saveStep2 = async () => {
    const params = {
      ...step2Ref.value.state.formData,
      qualityLossId: state.qualityLossId,
      insurancePicsList: getUploadStep2ImageList(),
    }
    if (!params.id) {
      await postFleetOrderDispatchQualityLossInsuranceApi(params)
    } else {
      await putFleetOrderDispatchQualityLossInsuranceApi(params)
    }
    ElMessage.success('保存成功')
  }
  const saveStep3 = async () => {
    //将step3Ref.value.state.detailsFormData.publicFees中的invoiceUrl置成undefined
    step3Ref.value.state.detailsFormData.publicFees.forEach((item: any) => {
      item.invoiceUrl = undefined
    })
    const params = {
      ...step3Ref.value.state.detailsFormData,
      qualityLossId: state.qualityLossId,
    }
    await postFleetOrderDispatchQualityLossFeeApplyApi(params)
    ElMessage.success('保存成功')
  }
  const submitStep3 = async () => {
    step3Ref.value.state.detailsFormData.publicFees.forEach((item: any) => {
      item.invoiceUrl = undefined
    })
    const params = {
      ...step3Ref.value.state.detailsFormData,
      qualityLossId: state.qualityLossId,
    }
    await postFleetOrderDispatchQualityLossSubmitApproveApi(params)
    ElMessage.success('提交成功')
    stepIndex.value = '4'
    changeStep('4')
  }
  const saveStep4 = async () => {
    if (step4Ref.value.step4FormRef) {
      //校验step4表单
      const valid = await step4Ref.value.step4FormRef.validate()
      if (!valid) return
    }
    const params = {
      ...step4Ref.value.state.formData,
      qualityLossId: state.qualityLossId,
    }
    try {
      if (step4Ref.value.state.originRecycles.length === 0) {
        await postQualityLossRecycleBatchAddApi(params)
      } else {
        await postQualityLossRecycleBatchEditApi(params)
      }
      ElMessage.success('保存成功')
      state.dialogVisible.visible = false
      emit('closeQualityLossDialog')
    } catch (error) {
      return
    }
  }
  const prevStep = () => {
    if (stepIndex.value === '2') {
      stepIndex.value = '1'
      getStep1Detail(state.qualityLossId)
    } else if (stepIndex.value === '3') {
      stepIndex.value = '2'
      getStep2Detail(state.qualityLossId)
    } else if (stepIndex.value === '4') {
      stepIndex.value = '3'
      getStep3Detail(state.qualityLossId)
    }
  }
  const getUploadStep1ImageList = () => {
    return step1Ref.value.getUploadImageList()
  }
  const getUploadStep2ImageList = () => {
    return step2Ref.value.getUploadImageList()
  }
  const changeStep = (value: string) => {
    if (value === '1') {
      nextTick(() => {
        if (state.qualityLossId) {
          getStep1Detail(state.qualityLossId)
        }
      })
    } else if (value === '2') {
      nextTick(() => {
        if (state.qualityLossId) {
          getStep2Detail(state.qualityLossId)
        }
        step2Ref.value.getInsuranceTypeSelectOption()
      })
    } else if (value === '3') {
      nextTick(() => {
        step3Ref.value.getFineDataList()
        if (state.qualityLossId) {
          getStep3Detail(state.qualityLossId)
        }
      })
    } else if (value === '4') {
      nextTick(() => {
        if (state.qualityLossId) {
          getStep4Detail(state.qualityLossId)
        }
      })
    }
  }
  const getStep1Detail = async (id: string) => {
    nextTick(() => {
      step1Ref.value.state.loading = true
    })
    try {
      const { data } = await getQualityLossApi({ id })
      step1Ref.value.state.formData = data
      step1Ref.value.state.formData.damagedArea = data.damagedArea.split(',')
      step1Ref.value.state.formData.type = data.type.split(',')
      step1Ref.value.setUploadImageList(data.picsList || [])
      nextTick(() => {
        step1Ref.value.getDispatchList()
        step1Ref.value.state.loading = false
      })
    } catch (error) {
      step1Ref.value.state.loading = false
    }
  }
  const getStep2Detail = async (qualityLossId: string) => {
    step2Ref.value.state.loading = true
    try {
      const { data } = await getQualityLossInsuranceInfoApi({ id: qualityLossId })

      if (data) {
        step2Ref.value.state.formData = data
      } else {
        step2Ref.value.state.formData = {
          accept: false,
          acceptTime: '',
          compensationAmount: '',
          createTime: '',
          createUserName: '',
          dataSubmitStatus: '',
          deptId: 0,
          finishTime: '',
          insurancePics: '',
          insuranceType: '',
          mails: [
            {
              mailNo: '',
              mailPerson: '',
              mailTime: '',
              remark: '',
              createTime: '',
              createUserName: '',
              deptId: 0,
              modifyTime: '',
              modifyUserName: '',
              qualityLossInsuranceId: 0,
            },
          ],
          modifyTime: '',
          modifyUserName: '',
          paymentCollectionStatus: '',
          paymentCollectionTime: '',
          qualityLossId: 0,
          remark: '',
          reportNo: '',
          reportTime: '',
          survey: '',
        }
      }
      step2Ref.value.setUploadImageList(data.insurancePicsList || [])
      step2Ref.value.state.loading = false
    } catch (error) {
      step2Ref.value.state.loading = false
    }
  }
  const getStep3Detail = async (qualityLossId: string) => {
    step3Ref.value.state.loading = true
    try {
      const { data } = await getQualityLossFeeInfoApi({ id: qualityLossId })
      step3Ref.value.state.detailsFormData = data
      step3Ref.value.state.detailsFormData.chargeType = data.chargeType ?? '预计金额'
      step3Ref.value.state.loading = false
    } catch (error) {
      step3Ref.value.state.loading = false
    }
  }
  const getStep4Detail = async (qualityLossId: string) => {
    step4Ref.value.state.loading = true
    try {
      const { data } = await getQualityLossRecycleAllApi({ qualityLossId })
      step4Ref.value.state.originRecycles = JSON.parse(JSON.stringify(data))
      step4Ref.value.state.formData.recycles = data
    } catch (error) {
    } finally {
      step4Ref.value.state.loading = false
    }
  }

  defineExpose({
    state,
    getStep1Detail,
    stepIndex,
    step1Ref,
  })
</script>
