<template>
  <div>
    <el-dialog :close-on-click-modal="false" v-model="state.dialogVisible.visible" :title="state.dialogVisible.title" width="78vw" @close="handleClose">
      <el-scrollbar>
        <!-- 表单部分 -->
        <el-form ref="ruleFormRef" :model="formData" label-width="100px" :rules="rules">
          <el-row class="center-label-form-item">
            <!-- 第一行 -->
            <el-col :span="6">
              <el-form-item label="客户名称" prop="customerId">
                <el-select
                  value-key="name"
                  v-model="formData.customerId"
                  placeholder="下拉单选，客户列表数据"
                  style="width: 100%"
                  @change="selectCustomerNo"
                  size="default"
                >
                  <el-option v-for="item in formData.customerListData" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <!--  -->
            <el-col :span="6">
              <el-form-item label="批量VIN码" prop="vin">
                <div class="batch-input-container">
                  <el-input
                    style="width: 100%"
                    v-model="formData.vin"
                    type="textBatch"
                    :row="1"
                    clearable
                    placeholder="批量查询"
                    @click="openShortVinBatch"
                    @clear="emptyTextBatch(shortVinBatchRef)"
                    size="default"
                  />
                  <div class="batch-popup-wrapper">
                    <VINbatch
                      ref="shortVinBatchRef"
                      @arraySent="handleArrayReceived"
                      :showTextBatch="showShortVinBatch"
                      :targetField="{ name: 'shortVin', label: '批量查询', message: '请输入批量六位VIN码', max: 100 }"
                      :closeTextBatch="closeShortVinBatch"
                      :initialValue="currentShortVinBatchValue"
                    />
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <!--  -->
            <el-col :span="6">
              <el-form-item label="订单状态" prop="orderStatus">
                <el-select value-key="orderStatus" v-model="formData.orderStatus" placeholder="下拉单选" style="width: 100%" size="default">
                  <el-option v-for="item in formData.orederList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="计划下达时间" prop="issueTimer">
                <el-date-picker
                  v-model="formData.issueTimer"
                  type="daterange"
                  unlink-panels
                  style="width: 100%"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  placeholder="请选择预达时间"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  size="default"
                />
              </el-form-item>
            </el-col>

            <!-- 第二行 -->
            <el-col :span="6">
              <el-form-item label="回单状态" prop="receiptSlip">
                <el-select value-key="receiptSlip" v-model="formData.receiptSlip" placeholder="下拉单选" style="width: 100%" size="default">
                  <el-option v-for="item in formData.receiptList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="装车时间" prop="loadingTime">
                <el-date-picker
                  v-model="formData.loadingTime"
                  type="daterange"
                  unlink-panels
                  style="width: 100%"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  size="default"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="交车时间" prop="deliveryTime">
                <el-date-picker
                  v-model="formData.deliveryTime"
                  type="daterange"
                  unlink-panels
                  style="width: 100%"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD"
                  size="default"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="计划回款时间" prop="scheduleTime">
                <el-date-picker
                  v-model="formData.scheduleTime"
                  type="daterange"
                  unlink-panels
                  style="width: 100%"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD"
                  size="default"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>

            <!-- 第三行 -->
            <el-col :span="6">
              <el-form-item label="承运商" prop="generalCarrier">
                <el-input v-model="formData.generalCarrier" placeholder="模糊查询" style="width: 100%" size="default" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="司机" prop="driver">
                <el-input v-model="formData.driver" placeholder="模糊查询" style="width: 100%" size="default" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="车牌号" prop="cardCode">
                <el-input v-model="formData.cardCode" placeholder="模糊查询" style="width: 100%" size="default" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item>
                <div style="display: flex; gap: 10px">
                  <el-button type="primary" @click="onSubmit(ruleFormRef)" size="default">搜索</el-button>
                  <el-button type="" @click="resetForm(ruleFormRef)" size="default">重置</el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="heng"></div>
        <!-- VIN统计部分 -->
        <div class="vind">
          <div class="VIN1 vinds">VIN台数</div>
          <div class="VIN2">
            <span style="font-family: SourceHanSansCN, SourceHanSansCN; color: #3f97fd">{{ VINcount }}</span
            >台
          </div>
          <div class="VIN3 vinds">对账总额</div>
          <div class="VIN2">
            <span style="font-family: SourceHanSansCN, SourceHanSansCN; color: #3f97fd">{{ allPrice }}</span
            >元
          </div>
        </div>
        <!-- 选择区域 -->
        <!-- 客户补贴 -->
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="62px">
          <el-form-item label="">
            <span style="margin-left: 20px; margin-right: 10px">客户补贴</span>
            <el-select
              value-key="khbt"
              size="default"
              v-model="formInline.khbt"
              placeholder="客户补贴"
              @change="selectDedution"
              multiple
              collapse-tags
              clearable
            >
              <el-option
                v-for="item in formInline.khbtListData"
                :key="item.id"
                :label="item.reasonForDeduction + '-' + item.deductionAmount"
                :value="item.id"
              />
            </el-select>
            <div class="tablers">
              <span class="color:red">{{ numberPrice }}</span> <span style="color: #000">元</span>
            </div>
          </el-form-item>
          <!-- 客户扣款 -->
          <el-form-item label="">
            <span style="margin-right: 10px; margin-left: 20px">客户扣款</span>
            <el-select value-key="khkk" size="default" v-model="formInline.khkk" placeholder="客户扣款" @change="selectDeduct" multiple collapse-tags clearable>
              <el-option
                v-for="item in formInline.khkkListData"
                :key="item.id"
                :label="item.reasonForDeduction + '-' + item.deductionAmount"
                :value="item.id"
              />
            </el-select>
            <div class="tablers">
              <span class="color:red">{{ numberPriceon }}</span
              ><span style="color: #000">元</span>
            </div>
          </el-form-item>
          <el-form-item>
            <span style="margin-left: 20px">应收金额</span>
            <div class="tablers">
              <span class="color:red">{{ amountReceivable }}</span
              ><span style="color: #000">元</span>
            </div>
            <!-- <h3 style="margin-right: 110px">应收金额：{{ amountReceivable }}</h3> -->
            <el-button @click="computFun" type="primary" size="default" style="margin-left: 20px">计算</el-button>
          </el-form-item>
        </el-form>
        <!-- 对账备注 -->
        <div class="flex" style="margin-top: 20px; margin-bottom: 20px">
          <span style="width: 90px">对账备注</span>
          <el-input style="width: 100%" size="default" placeholder="备注" v-model="ramaker" :rows="1" type="textarea" />
        </div>

        <!-- 表单 -->
        <div>
          <el-table :data="tableData" border style="width: 100%" max-height="300" ref="multipleTableRef" @selection-change="selectTable">
            <el-table-column type="selection" width="55" />
            <el-table-column align="center" label="序号" width="60">
              <template #default="scope">{{ scope.$index + 1 }}</template>
            </el-table-column>
            <el-table-column align="center" v-for="item in tabletitle" :key="item.label" :property="item.property" :label="item.label" width="110">
              <template #default="scope">
                <!-- 严格判断 false 值 -->
                <span v-if="scope.row[item.property] == false">否</span>
                <span v-else-if="scope.row[item.property] == true">是</span>
                <span v-else="scope.row[item.property] == false">{{ scope.row[item.property] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!--  -->
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="showButton == ''" @click="cancal">取消</el-button>
          <el-button v-if="showButton == ''" type="primary" @click="ok"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { getDeduction, dropDownApi, settlementApi, generateSettlementApi } from '@/api/auth/index'
  import type { FormInstance } from 'element-plus'
  import VINbatch from '@/components/TopQueryGroupComponent/components/VINbatch.vue'

  const emit = defineEmits(['refresh'])
  const state = reactive({
    dialogVisible: {
      visible: false,
      title: '批量对账',
    },
    customerName: '', //客户名称
  })

  const showButton = ref('')
  const multipleTableRef = ref<any>(null)

  const formData = ref({
    customerId: '', //客户名称
    orderStatus: '', //订单状态
    vin: '', // VIN码
    issueTimer: '', //计划下达时间
    receiptSlip: '', // 回单状态
    loadingTime: '', //装车时间
    deliveryTime: '', //交车时间
    scheduleTime: '', //计划回款时间
    generalCarrier: '', //承运商
    driver: '',
    cardCode: '',
    customerListData: [] as any, //客户列表
    orederList: [
      { label: '待分配', value: '1' },
      { label: '待装车', value: '2' },
      { label: '运输中', value: '3' },
      { label: '已送达', value: '4' },
    ],
    receiptList: [
      { label: '未返回', value: '1' },
      { label: '已上传回单', value: '2' },
      { label: '已返回驻点', value: '3' },
      { label: '已返回公司', value: '4' },
      { label: '已寄回客户', value: '5' },
    ],
  })

  const formInline = ref({
    khbt: [] as string[], // 改为字符串数组存储id
    khkk: [] as string[],
    khbtListData: [] as any[],
    khkkListData: [] as any[],
  })

  const ramaker = ref<string>('')

  const tabletitle = [
    { property: 'customerName', label: '客户名称' },
    { property: 'orderNo', label: '订单编号' },
    { property: 'vin', label: 'VIN' },
    { property: 'childCompanyName', label: '所属基地' },
    { property: 'customerOrderNo', label: '客户单号' },
    { property: 'brandName', label: '品牌' },
    { property: 'modelName', label: '车型' },
    { property: 'startArea', label: '起点' },
    { property: 'endArea', label: '终点' },
    { property: 'lineName', label: '线路名称' },
    { property: 'pickUpPointName', label: '提车点名称' },
    { property: 'dropUpPointName', label: '交车点名称' },
    { property: 'orderIssueDatetime', label: '计划下达时间' },
    { property: 'loadTime', label: '装车时间' },
    { property: 'modifyTime', label: '交车时间' },
    { property: 'carrierName', label: '承运商' },
    { property: 'carrierType', label: '承运商类型' },
    { property: 'driverName', label: '司机' },
    { property: 'vehicleNo', label: '车牌号' },
    { property: 'qualityLoss', label: '是否质损' },
    { property: 'amountReceivable', label: '应收金额' },
    { property: 'amountReceivable', label: '对账金额' },
    { property: 'planRefundDate', label: '计划回款日期' },
    { property: 'whetherDriverCollectName', label: '是否驾驶员收款' },
    { property: 'orderStatusName', label: '订单状态' },
    { property: 'haveDeliverySlip', label: '有无交接单' },
    { property: 'reorderStatusName', label: '回单状态' },
    { property: 'settlementRemark', label: '对账备注' },
  ]
  const tableData = ref<any>([])

  const showTable = ref<any>('')

  const VINcount = ref<any>(0) // VIN台数
  const allPrice = ref<any>(0) // 对账总额
  const numberPrice = ref<any>(0) //客户补贴价格
  const numberPriceon = ref<any>(0) //客户扣款价格
  const amountReceivable = ref<any>(0) //应收金额

  const objectDetail = ref<any>({
    //客户名称
    customerId: '',
    vin: '', // VIN
    orderStatus: '', //订单状态
    startOrderIssueDatetime: '', //计划下达时间开始
    endOrderIssueDatetime: '', //计划下达时间结束
    reorderStatus: '', //回单状态
    startLoadTime: '', //装车时间开始
    endLoadTime: '', // 装车时间结束
    startActualDropTime: '', //交车时间
    endActualDropTime: '',
    carrierName: '', //承运商
    driverName: '', //司机
    vehicleNo: '', //车牌号
    //计划回款时间
    settlementStatus: '未对账',
  })

  const rules = ref({
    customerId: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
  })

  // 搜索
  const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        objectDetail.value.vin = formData.value.vin
        objectDetail.value.orderStatus = formData.value.orderStatus
        objectDetail.value.startOrderIssueDatetime = formData.value.issueTimer == null ? '' : formData.value.issueTimer[0]
        objectDetail.value.endOrderIssueDatetime = formData.value.issueTimer == null ? '' : formData.value.issueTimer[1]
        objectDetail.value.reorderStatus = formData.value.receiptSlip
        objectDetail.value.startLoadTime = formData.value.deliveryTime == null ? '' : formData.value.deliveryTime[0]
        objectDetail.value.endLoadTime = formData.value.deliveryTime == null ? '' : formData.value.deliveryTime[1]
        objectDetail.value.startActualDropTime = formData.value.loadingTime == null ? '' : formData.value.loadingTime[0]
        objectDetail.value.endActualDropTime = formData.value.loadingTime == null ? '' : formData.value.loadingTime[1]
        // 计划回款时间scheduleTime
        objectDetail.value.carrierName = formData.value.generalCarrier
        objectDetail.value.driverName = formData.value.driver
        objectDetail.value.vehicleNo = formData.value.cardCode
        fun()
      }
    })
  }
  const fun = async () => {
    let data = JSON.parse(JSON.stringify(objectDetail.value))
    let res = await settlementApi(data)
    tableData.value = res.data
    showTable.value = true
    getDetail()
    // 关键点：等待视图更新完成
    await nextTick()
    // 确认表格实例存在后操作
    if (multipleTableRef.value) {
      multipleTableRef.value.toggleAllSelection()
    }
  }

  // 重置
  const ruleFormRef = ref<FormInstance>()

  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
    showTable.value = false
    // 清空可能残留的数据
    objectDetail.value = {
      customerId: '',
      vin: '',
      orderStatus: '',
      startOrderIssueDatetime: '',
      endOrderIssueDatetime: '',
      reorderStatus: '',
      startLoadTime: '',
      endLoadTime: '',
      startActualDropTime: '',
      endActualDropTime: '',
      carrierName: '',
      driverName: '',
      vehicleNo: '',
      settlementStatus: '未对账',
    }
    // 重置多选表格的选中状态
    if (multipleTableRef.value) {
      multipleTableRef.value.clearSelection()
    }
    formInline.value.khbt = []
    formInline.value.khkk = []
    numberPrice.value = 0
    numberPriceon.value = 0
    amountReceivable.value = 0
    tableData.value = []
  }

  let tableListID = [] as any
  // 表格变化
  const selectTable = (value: any) => {
    let num = 0
    VINcount.value = value.length
    // 计算选中表格的应收金额 settlementAmount
    value.forEach((e: any) => {
      num += e.settlementAmount
    })
    allPrice.value = num.toFixed(2)
    let arr = [] as any
    value.forEach((elem: any) => {
      arr.push(elem.id)
    })
    tableListID = arr
  }

  // 防抖
  const debounce = (fn: any, delay = 300) => {
    let timer: any = null
    return (...args: any) => {
      clearTimeout(timer)
      timer = setTimeout(() => {
        fn.apply(this, args)
      }, delay)
    }
  }

  // 确定 提交
  const ok = debounce(async () => {
    // 如果没有勾选数据，提示“未选择对账订单”
    if (VINcount.value == 0) {
      ElMessage({
        message: '未选择对账订单',
        type: 'warning',
      })
    } else {
      // 确定发送的参数
      let obj = {
        customerDeductionIds: listdeduArr, //	客户扣款id集合
        customerId: formData.value.customerId, //客户id
        customerSubsidyIds: listArr, //	客户补贴id集合
        remark: ramaker.value, //对账备注
        settlementDetailIds: tableListID, //	对账明细id集合
      }
      let res = await generateSettlementApi(obj)
      ElMessage.success(res.message)
      state.dialogVisible.visible = false
      emit('refresh')
    }
  })

  // 获取客户下拉接口
  const dropDownFun = async () => {
    let res = await dropDownApi({})
    formData.value.customerListData = res.data
  }
  const selectCustomerNo = async (value: any) => {
    formData.value.customerId = value
    objectDetail.value.customerId = formData.value.customerId
    // 全部
  }

  // 获取客户补贴
  const getDetail = async () => {
    let res = await getDeduction({
      billState: '未对账',
      type: '客户补贴',
      customerId: formData.value.customerId || '',
    })
    formInline.value.khbtListData = res.data

    let result = await getDeduction({
      billState: '未对账',
      type: '客户扣款',
      customerId: formData.value.customerId || '',
    })
    formInline.value.khkkListData = result.data
  }

  // 修改金额计算逻辑
  const selectDedution = (selectedIds: string[]) => {
    listArr = selectedIds // 直接存储选中id数组
    numberPrice.value = formInline.value.khbtListData
      .filter((item) => selectedIds.includes(item.id))
      .reduce((sum, item) => sum + Number(item.deductionAmount), 0)
  }

  // //	客户补贴id集合
  let listArr = [] as any
  //	客户扣款id集合
  let listdeduArr = [] as any
  const selectDeduct = (selectedIds: string[]) => {
    listdeduArr = selectedIds // 直接存储选中id数组
    numberPriceon.value = formInline.value.khkkListData
      .filter((item) => selectedIds.includes(item.id))
      .reduce((sum, item) => sum + Number(item.deductionAmount), 0)
  }
  const computFun = () => {
    amountReceivable.value = Number(allPrice.value) + Number(numberPrice.value) - Number(numberPriceon.value)
  }
  // 取消按钮
  const cancal = () => {
    state.dialogVisible.visible = false
  }

  const handleClose = () => {
    resetForm(ruleFormRef.value) // 调用表单重置
    // 重置其他状态变量
    showTable.value = false
    VINcount.value = 0
    allPrice.value = 0
    numberPrice.value = 0
    numberPriceon.value = 0
    amountReceivable.value = 0
    ramaker.value = ''
    tableData.value = []

    listArr = []
    listdeduArr = []
    tableListID = []
    state.dialogVisible.visible = false
  }

  // 批量样式的修改
  const shortVinBatchRef = ref()
  const currentBatchField = ref('')
  const currentShortVinBatchField = ref('')
  const currentBatchValue = ref('')
  const currentShortVinBatchValue = ref('')
  const showShortVinBatch = ref(false)

  function openShortVinBatch() {
    currentShortVinBatchField.value = 'shortVin'
    currentShortVinBatchValue.value = formData.value.vin || '' // 保存当前值
    showShortVinBatch.value = true
  }
  const emptyTextBatch = (fieldRef: any) => {
    if (fieldRef?.list && fieldRef.list.length > 0) {
      fieldRef.list = []
    }
  }
  const handleArrayReceived = (array: any, targetField: { name: string | number }) => {
    formData.value.vin = Object.values(array).join(',')
    currentBatchValue.value = formData.value.vin
  }

  const closeShortVinBatch = () => {
    showShortVinBatch.value = false
  }
  // 到此

  defineExpose({
    state,
    // getDetail,
    dropDownFun,
  })
</script>

<style scoped>
  .demo-date-picker {
    display: flex;
    width: 100%;
    padding: 0;
    flex-wrap: wrap;
  }

  .demo-date-picker .block {
    padding: 30px 0;
    text-align: center;
    border-right: solid 1px var(--el-border-color);
    flex: 1;
  }

  .demo-date-picker .block:last-child {
    border-right: none;
  }

  .demo-date-picker .demonstration {
    display: block;
    color: var(--el-text-color-secondary);
    font-size: 14px;
    margin-bottom: 20px;
  }
  .vind {
    width: 100%;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ededed;
  }
  .vinds {
    width: 25%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .vinds:nth-child(odd) {
    background: #f9f9f9;
  }
  .demo-form-inline {
    margin-top: 24px;
  }
  .tablers {
    width: 115px;
    height: 32px;
    background: #f8f9fb;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #ededed;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: bold;
    font-size: 16px;
    color: #ff0000;
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }
  .flex {
    display: flex;
    align-items: center;
    text-align: center;
  }
  .heng {
    width: 1490px;
    height: 15px;
    background: #f8f9fb;
    margin-bottom: 30px;
  }
  .hengxian {
    height: 1px;
    border: 1px solid #ededed;
    margin-bottom: 30px;
  }
  .VIN2 {
    width: 25%;
    height: 100%;
    text-align: center;
    line-height: 50px;
  }
  /* 使用深度选择器穿透 scoped 样式 */
  .center-label-form-item :deep(.el-form-item__label) {
    display: flex;
    width: 100%;
    line-height: 32px;
  }
  /* 容器需要相对定位 */
  .batch-input-container {
    position: relative;
    width: 100%;
  }

  /* 输入框样式 */
  .batch-input {
    width: 100%;
    cursor: pointer; /* 显示可点击状态 */
  }

  /* 弹出层容器 */
  .batch-popup-wrapper {
    position: absolute;
    width: 100%;
    top: calc(100% - 1px); /* 显示在输入框下方 */
    left: 0;
    z-index: 2000; /* 确保高于其他元素 */
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); /* 添加阴影区分层级 */
    border-radius: 4px;
    background: #fff;
  }

  /* 解决z-index上下文问题 */
  .el-form-item {
    position: static; /* 确保弹出层不受父级定位影响 */
  }
</style>
