<template>
  <div>
    <el-dialog :close-on-click-modal="false" v-model="state.dialogVisible.visible" :title="state.dialogVisible.title" width="80vw" @close="handleClose">
      <div class="hengxian"></div>
      <el-scrollbar>
        <!-- 表单部分：付款-批量对账 -->
        <el-form
          style="min-width: 1000px; overflow: hidden"
          ref="ruleFormRef"
          :model="objectDetail"
          :label-width="!isDisabled ? '100px' : ''"
          :rules="rules"
          :disabled="isDisabled"
        >
          <el-form-item label="承运商" prop="carrierId" v-if="isDisabled">
            <el-tree-select
              filterable
              v-model="objectDetail.carrierId"
              :data="formData.customerListData"
              :render-after-expand="false"
              clearable
              style="width: 260px"
              @change="selectCustomerNo"
            >
              <!-- 添加自定义清空按钮 -->
              <template #suffix>
                <el-icon v-if="objectDetail.carrierId" class="clear-icon" @click.stop="handleClearCarrier">
                  <Close />
                </el-icon>
              </template>
            </el-tree-select>
          </el-form-item>
          <el-row class="center-label-form-item">
            <!-- 第一行 -->
            <el-col :span="6" v-if="!isDisabled">
              <el-form-item label="承运商" prop="carrierId">
                <el-tree-select
                  filterable
                  v-model="objectDetail.carrierId"
                  :data="formData.customerListData"
                  :render-after-expand="false"
                  clearable
                  style="width: 100%"
                  @change="selectCustomerNo"
                  size="default"
                >
                  <!-- 添加自定义清空按钮 -->
                  <template #suffix>
                    <el-icon v-if="objectDetail.carrierId" class="clear-icon" @click.stop="handleClearCarrier">
                      <Close />
                    </el-icon>
                  </template>
                </el-tree-select>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="!isDisabled">
              <!-- <el-form-item label="批量VIN码" prop="vin">
                <el-input type="textarea" :rows="1" v-model="objectDetail.vin" :placeholder="isDisabled ? '' : '批量查询'" style="width: 100%" size="default" />
              </el-form-item> -->
              <el-form-item label="批量VIN码" prop="vin">
                <div class="batch-input-container">
                  <el-input
                    style="width: 100%"
                    v-model="objectDetail.vin"
                    type="textBatch"
                    :row="1"
                    clearable
                    :placeholder="isDisabled ? '' : '批量查询'"
                    @click="openShortVinBatch"
                    @clear="emptyTextBatch(shortVinBatchRef)"
                    size="default"
                  />
                  <div class="batch-popup-wrapper">
                    <VINbatch
                      ref="shortVinBatchRef"
                      @arraySent="handleArrayReceived"
                      :showTextBatch="showShortVinBatch"
                      :targetField="{ name: 'shortVin', label: '批量查询', message: '请输入批量六位VIN码', max: 100 }"
                      :closeTextBatch="closeShortVinBatch"
                      :initialValue="currentShortVinBatchValue"
                    />
                  </div>
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="6" v-if="!isDisabled">
              <el-form-item label="调度时间" prop="dispatchTime">
                <el-date-picker
                  v-model="objectDetail.dispatchTime"
                  type="daterange"
                  unlink-panels
                  style="width: 100%"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  placeholder="请选择预达时间"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  size="default"
                />
              </el-form-item>
            </el-col>
            <!-- 第二行 -->

            <el-col :span="6" v-if="!isDisabled">
              <el-form-item label="装车时间" prop="loadTime">
                <el-date-picker
                  v-model="objectDetail.loadTime"
                  type="daterange"
                  unlink-panels
                  style="width: 100%"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  size="default"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="!isDisabled">
              <el-form-item label="交车时间" prop="actualDropTime">
                <el-date-picker
                  v-model="objectDetail.actualDropTime"
                  type="daterange"
                  unlink-panels
                  style="width: 100%"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  size="default"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="!isDisabled">
              <el-form-item label="回单状态" prop="reorderStatus">
                <el-select
                  value-key="reorderStatus"
                  v-model="objectDetail.reorderStatus"
                  :placeholder="isDisabled ? '' : '下拉单选'"
                  style="width: 100%"
                  size="default"
                >
                  <el-option v-for="item in formData.receiptList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="!isDisabled">
              <el-form-item label="是否质损" prop="qualityLoss">
                <el-select
                  value-key="qualityLoss"
                  v-model="objectDetail.qualityLoss"
                  :placeholder="isDisabled ? '' : '下拉单选,不限/是/否'"
                  style="width: 100%"
                  size="default"
                >
                  <el-option v-for="item in formData.orederList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="!isDisabled">
              <el-form-item label="质损处理结果" prop="dealStatus">
                <el-select
                  value-key="dealStatus"
                  v-model="objectDetail.dealStatus"
                  :placeholder="isDisabled ? '' : '下拉单选,不限/已完成/未完成'"
                  style="width: 100%"
                  size="default"
                >
                  <el-option v-for="item in formData.treatmentList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 第三行 -->
            <el-col :span="6" v-if="!isDisabled">
              <el-form-item label="客户" prop="customerName">
                <el-input v-model="objectDetail.customerName" :placeholder="isDisabled ? '' : '模糊查询'" style="width: 100%" size="default" />
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="!isDisabled">
              <el-form-item label="司机" prop="driver">
                <el-input v-model="objectDetail.driverName" :placeholder="isDisabled ? '' : '模糊查询'" style="width: 100%" size="default" />
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="!isDisabled">
              <el-form-item label="车牌号" prop="cardCode">
                <el-input v-model="objectDetail.vehicleNo" :placeholder="isDisabled ? '' : '模糊查询'" style="width: 100%" size="default" />
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="!isDisabled">
              <el-form-item>
                <div style="display: flex; gap: 10px">
                  <el-button type="primary" @click="onSubmit(ruleFormRef)" size="default">搜索</el-button>
                  <el-button type="" @click="resetForm(ruleFormRef)" size="default">重置</el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="content"></div>
        <!-- VIN统计部分 -->
        <div class="vind">
          <div class="VIN1 vinds">VIN台数</div>
          <div class="VIN2">
            <span style="font-family: SourceHanSansCN, SourceHanSansCN; color: #3f97fd">{{ VINcount }}</span
            >台
          </div>
          <div class="VIN3 vinds">对账总额</div>
          <div class="VIN2">
            <span style="font-family: SourceHanSansCN, SourceHanSansCN; color: #3f97fd">{{ allPrice }}</span
            >元
          </div>
        </div>
        <!-- 历史挂账 -->
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="62px">
          <el-form-item label="">
            <span style="margin-left: 36px; margin-right: 10px">历史挂账</span>
            <div class="tablers" style="height: 32px; line-height: 32px">
              <span class="color:red;">{{ numberPrice }}</span
              ><span style="color: #000">元</span>
            </div>
            <!-- <div class="refresh" @click="refreshfun">刷新</div> -->
            <el-button class="refresh" @click="refreshfun" type="primary" :disabled="isDisabled" size="default">刷新</el-button>
          </el-form-item>

          <!-- 本次冲账 -->
          <el-form-item label="">
            <span style="margin-right: 10px; margin-left: 20px">本次冲账</span>
            <div>
              <el-input-number v-model="numberPriceon" style="width: 180px" :precision="2" :disabled="isDisabled" size="default">
                <template #suffix>
                  <span>元</span>
                </template>
              </el-input-number>
              <!-- <el-input type="number" v-model="numberPriceon" placeholder="正负都可，保留两位小数" /> -->
            </div>
          </el-form-item>
          <el-form-item>
            <!-- <h3 style="margin-right: 110px; margin-left: 100px">应付金额：{{ amountReceivable }}</h3> -->
            <span style="margin-left: 20px">应付金额</span>
            <div class="tablers">
              <span class="color:red">{{ amountReceivable }}</span
              ><span style="color: #000">元</span>
            </div>
            <!-- <el-button type="primary" @click="computFun" >计算</el-button> -->
            <el-button @click="computFun" type="primary" size="default" style="margin-left: 20px" :disabled="isDisabled">计算</el-button>
          </el-form-item>
        </el-form>
        <!-- 对账备注 -->
        <!-- <div class="flex gap-4 mb-4" style="margin-top: 20px">
          <span style="width: 100px">对账备注</span>
          <el-input style="width: 100%" size="default" v-model="ramaker" :placeholder="!isDisabled ? '备注' : ''" :disabled="isDisabled" />
        </div> -->
        <div class="flex" style="margin-top: 10px; margin-bottom: 20px">
          <span style="width: 110px">对账备注</span>
          <el-input size="default" :placeholder="!isDisabled ? '备注' : ''" v-model="ramaker" :rows="1" type="textarea" :disabled="isDisabled" />
        </div>

        <!-- 表单 -->
        <div class="scrollTable">
          <el-table :data="tableData" border style="width: 100%" max-height="300" ref="multipleTableRef" @selection-change="selectTable">
            <el-table-column type="selection" width="55" v-if="!isDisabled" />
            <el-table-column align="center" label="序号" width="60">
              <template #default="scope">{{ scope.$index + 1 }}</template>
            </el-table-column>
            <el-table-column align="center" v-for="item in tabletitle" :key="item.label" :property="item.property" :label="item.label" width="110">
              <template #default="scope">
                <!-- 严格判断 false 值 -->
                <span v-if="scope.row[item.property] == false">否</span>
                <span v-else-if="scope.row[item.property] == true">是</span>
                <span v-else="scope.row[item.property] == false">{{ scope.row[item.property] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!--  -->
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="showButton == ''" @click="cancal">取消</el-button>
          <el-button v-if="showButton == ''" type="primary" @click="ok"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import {
    getDeduction,
    carrierApi,
    settlementSearchApi,
    fkgenerateSettlementApi,
    historicalOutstandingApi,
    historyGenerateSettlementApi,
  } from '@/api/auth/index'
  import type { FormInstance } from 'element-plus'
  import VINbatch from '@/components/TopQueryGroupComponent/components/VINbatch.vue'

  const emit = defineEmits(['refresh'])

  const state = reactive({
    dialogVisible: {
      visible: false,
      title: '批量对账',
    },
    customerName: '', //客户名称
  })

  const showButton = ref('')
  const multipleTableRef = ref<any>(null)

  const formData = ref({
    customerId: '', //承运商
    orderStatus: '', //是否质损
    vin: '', // VIN码
    issueTimer: '', //调度时间
    receiptSlip: '', // 回单状态
    loadingTime: '', //装车时间
    deliveryTime: '', //交车时间
    generalCarrier: '', //承运商
    treatment: '', //质损处理结果
    driver: '',
    cardCode: '',
    customerListData: [] as any, //客户列表
    orederList: [
      { label: '不限', value: '不限' },
      { label: '是', value: '是' },
      { label: '否', value: '否' },
    ],
    receiptList: [
      { label: '无需回单', value: '0' },
      { label: '未返回', value: '1' },
      { label: '已上传回单', value: '2' },
      { label: '已返回驻点', value: '3' },
      { label: '已返回公司', value: '4' },
      { label: '已寄回客户', value: '5' },
    ],
    treatmentList: [
      { label: '不限', value: '不限' },
      { label: '已完成', value: '已完成' },
      { label: '未完成', value: '未完成' },
    ],
  })

  const formInline = ref({
    khbt: [] as string[], // 改为字符串数组存储id
    khkk: [] as string[],
    khbtListData: [] as any[],
    khkkListData: [] as any[],
  })

  const tabletitle = [
    { property: 'customerName', label: '客户名称' },
    { property: 'orderCode', label: '订单编号' },
    { property: 'vin', label: 'VIN' },
    { property: 'childCompanyName', label: '所属基地' },
    { property: 'customerOrderNo', label: '客户单号' },
    { property: 'brandName', label: '品牌' },
    { property: 'modelName', label: '车型' },
    { property: 'startArea', label: '起点' },
    { property: 'endArea', label: '终点' },
    { property: 'lineName', label: '线路名称' },
    { property: 'pickUpPointName', label: '提车点名称' },
    { property: 'dropUpPointName', label: '交车点名称' },
    { property: 'orderIssueDatetime', label: '计划下达时间' },
    { property: 'loadTime', label: '装车时间' },
    { property: 'modifyTime', label: '交车时间' },
    { property: 'carrierName', label: '承运商' },
    { property: 'carrierType', label: '承运商类型' },
    { property: 'driverName', label: '司机' },
    { property: 'vehicleNo', label: '车牌号' },
    { property: 'qualityLoss', label: '是否质损' },
    { property: 'downPaymentAmount', label: '应付金额' },
    { property: 'settlementAmount', label: '对账金额' },
    { property: 'planRefundDate', label: '计划回款日期' },
    { property: 'whetherDriverCollectName', label: '是否驾驶员收款' },
    { property: 'orderStatusName', label: '订单状态' },
    { property: 'haveDeliverySlip', label: '有无交接单' },
    { property: 'reorderStatusName', label: '回单状态' },
    { property: 'settlementRemark', label: '对账备注' },
  ]
  const tableData = ref<any>([])

  const showTable = ref<any>('')

  const VINcount = ref<any>(0) // VIN台数
  const allPrice = ref<any>(0) // 对账总额
  const numberPrice = ref<any>(0) //客户补贴价格
  const numberPriceon = ref<any>(0) //客户扣款价格
  const amountReceivable = ref<any>(0) //应收金额

  const objectDetail = ref<any>({
    carrierId: '', //承运商
    vin: '', // VIN
    driverName: '', //司机
    vehicleNo: '', //车牌号
    dispatchTime: '', // 调度时间
    loadTime: '', // 装车时间
    actualDropTime: '', // 交车时间
    reorderStatus: '', // 回单状态
    qualityLoss: '', // 是否质损
    dealStatus: '', // 质损处理结果
    customerName: '', // 客户
  })

  const rules = ref({
    carrierId: [{ required: true, message: '请选择承运商名称', trigger: 'blur' }],
  })
  // 是否禁用
  const isDisabled = ref<any>(false)
  const ramaker = ref<string>('')
  // 获取数据

  const getDateList = (data: any) => {
    isDisabled.value = true
    objectDetail.value.carrierId = data.carrierName
    VINcount.value = data.vinCount
    allPrice.value = data.amount
    numberPriceon.value = data.offsetAmount
    amountReceivable.value = data.payableAmount.toFixed(2) //应付金额
    // 备注
    ramaker.value = data.remark
    numberPrice.value = data.chargeAmount == null ? 0 : data.chargeAmount
    // historyEcho(data.carrierId)
    getHistoryDate(data.carrierId, data.no)
  }
  // 历史挂账回显
  const historyEcho = async (id: any) => {
    let { data } = await historicalOutstandingApi({
      carrierId: id,
    })
    numberPrice.value = data.chargeAmount == null ? 0 : data.chargeAmount
  }
  // 查询数据
  const getHistoryDate = async (carrierId: any, no: any) => {
    let { data } = await historyGenerateSettlementApi({
      carrierId,
      no,
    })
    tableData.value = data
    if (multipleTableRef.value) {
      multipleTableRef.value.toggleAllSelection()
    }
  }

  // 搜索
  const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        let params = {
          ...objectDetail.value,
          qualityLoss: objectDetail.value.qualityLoss == '不限' ? '' : objectDetail.value.qualityLoss,
          dealStatus: objectDetail.value.dealStatus == '不限' ? '' : objectDetail.value.dealStatus,
          startDtispatchTime: objectDetail.value.dispatchTime ? objectDetail.value.dispatchTime[0] : null,
          endDtispatchTime: objectDetail.value.dispatchTime ? objectDetail.value.dispatchTime[1] : null,
          startLoadTime: objectDetail.value.loadTime ? objectDetail.value.loadTime[0] : null,
          endLoadTime: objectDetail.value.loadTime ? objectDetail.value.loadTime[1] : null,
          tartActualDropTime: objectDetail.value.actualDropTime ? objectDetail.value.actualDropTime[0] : null,
          endActualDropTime: objectDetail.value.actualDropTime ? objectDetail.value.actualDropTime[1] : null,
          dispatchTime: '', // 调度时间
          loadTime: '', // 装车时间
          actualDropTime: '', // 交车时间
        }
        // 保存当前查询的承运商ID
        lastQueriedCarrierId.value = objectDetail.value.carrierId
        refreshfun()
        fun(params)
      }
    })
  }
  const fun = async (params: any) => {
    let res = await settlementSearchApi(params)
    tableData.value = res.data
    showTable.value = true
    // 关键点：等待视图更新完成
    await nextTick()
    // 确认表格实例存在后操作
    if (multipleTableRef.value) {
      multipleTableRef.value.toggleAllSelection()
    }
  }

  // 重置
  const ruleFormRef = ref<FormInstance>()
  const lastQueriedCarrierId = ref<string>('') // 保存最后一次查询的承运商ID

  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
    showTable.value = false
    // 清空可能残留的数据
    objectDetail.value = {
      settlementStatus: '未对账',
      carrierId: '',
      vin: '',
      driverName: '',
      vehicleNo: '',
      dispatchTime: '',
      loadTime: '',
      actualDropTime: '',
      reorderStatus: '',
      qualityLoss: '',
      dealStatus: '',
      customerName: '',
    }
    // 重置多选表格的选中状态
    if (multipleTableRef.value) {
      multipleTableRef.value.clearSelection()
    }
    // 重置最后一次查询的承运商ID
    lastQueriedCarrierId.value = ''
    formInline.value.khbt = []
    formInline.value.khkk = []
    numberPrice.value = 0
    numberPriceon.value = 0
    tableData.value = []
  }

  let tableListID = [] as any
  // 表格变化
  const selectTable = (value: any) => {
    let num = 0
    VINcount.value = value.length
    // 计算选中表格的应收金额 settlementAmount
    value.forEach((e: any) => {
      num += e.settlementAmount
    })
    allPrice.value = num.toFixed(2)
    let arr = [] as any
    value.forEach((elem: any) => {
      arr.push(elem.id)
    })
    tableListID = arr
  }

  // 防抖
  const debounce = (fn: any, delay = 300) => {
    let timer: any = null
    return (...args: any) => {
      clearTimeout(timer)
      timer = setTimeout(() => {
        fn.apply(this, args)
      }, delay)
    }
  }

  // 确定 提交
  const ok = debounce(async () => {
    // 如果没有勾选数据，提示“未选择对账订单”
    if (!isDisabled.value) {
      if (VINcount.value == 0) {
        ElMessage({
          message: '未选择对账订单',
          type: 'warning',
        })
      } else {
        // 确定发送的参数
        let obj = {
          remark: ramaker.value, //对账备注
          settlementDetailIds: tableListID, //	对账明细id集合
          offsetAmount: numberPriceon.value, //本次冲账金额
          amount: allPrice.value, //对账总额
          carrierId: objectDetail.value.carrierId, //承运商id
          chargeAmount: numberPrice.value,
        }
        let res = await fkgenerateSettlementApi(obj)
        ElMessage.success(res.message)
        state.dialogVisible.visible = false
        emit('refresh')
      }
    } else {
      state.dialogVisible.visible = false
    }
  })

  // 获取客户下拉接口
  const dropDownFun = async () => {
    let res = await carrierApi({})
    formData.value.customerListData = res.data
  }
  const selectCustomerNo = async (value: any) => {}

  // 修改金额计算逻辑
  const selectDedution = (selectedIds: string[]) => {
    listArr = selectedIds // 直接存储选中id数组
    numberPrice.value = formInline.value.khbtListData
      .filter((item) => selectedIds.includes(item.id))
      .reduce((sum, item) => sum + Number(item.deductionAmount), 0)
  }

  // //	客户补贴id集合
  let listArr = [] as any
  //	客户扣款id集合
  let listdeduArr = [] as any
  const selectDeduct = (selectedIds: string[]) => {
    listdeduArr = selectedIds // 直接存储选中id数组
    numberPriceon.value = formInline.value.khkkListData
      .filter((item) => selectedIds.includes(item.id))
      .reduce((sum, item) => sum + Number(item.deductionAmount), 0)
  }
  const computFun = () => {
    amountReceivable.value = (Number(allPrice.value) - Number(numberPriceon.value)).toFixed(2)
  }
  // 取消按钮
  const cancal = () => {
    state.dialogVisible.visible = false
  }

  const handleClose = () => {
    resetForm(ruleFormRef.value) // 调用表单重置
    // 重置其他状态变量
    showTable.value = false
    VINcount.value = 0
    allPrice.value = 0
    numberPrice.value = 0
    numberPriceon.value = 0
    amountReceivable.value = 0
    ramaker.value = ''
    tableData.value = []
    listArr = []
    listdeduArr = []
    tableListID = []
    state.dialogVisible.visible = false
    isDisabled.value = false
  }

  const oldCarrierId = ref<any>()
  // 监听 objectDetail.value.carrierId 的变化
  watch(
    () => objectDetail.value.carrierId,
    (newValue, oldValue) => {
      // 在这里处理 carrierId 变化后的逻辑
      oldCarrierId.value = oldValue
    },
  )

  // 刷新历史挂账
  const refreshfun = async () => {
    if (lastQueriedCarrierId.value) {
      let { data } = await historicalOutstandingApi({
        carrierId: lastQueriedCarrierId.value,
      })
      numberPrice.value = data == null ? 0 : data.balanceAmount == null ? 0 : data.balanceAmount
    } else {
      ElMessage({
        message: '请先选择承运商并点击查询',
        type: 'warning',
      })
    }
  }
  // 清空承运商选择
  const handleClearCarrier = () => {
    objectDetail.value.carrierId = null
    // 如果需要可以手动触发change事件
    selectCustomerNo(null)
  }

  // 批量样式的修改
  const shortVinBatchRef = ref()
  const currentBatchField = ref('')
  const currentShortVinBatchField = ref('')
  const currentBatchValue = ref('')
  const currentShortVinBatchValue = ref('')
  const showShortVinBatch = ref(false)

  function openShortVinBatch() {
    currentShortVinBatchField.value = 'shortVin'
    currentShortVinBatchValue.value = objectDetail.value.vin || '' // 保存当前值
    showShortVinBatch.value = true
  }
  const emptyTextBatch = (fieldRef: any) => {
    if (fieldRef?.list && fieldRef.list.length > 0) {
      fieldRef.list = []
    }
  }
  const handleArrayReceived = (array: any, targetField: { name: string | number }) => {
    objectDetail.value.vin = Object.values(array).join(',')
    currentBatchValue.value = objectDetail.value.vin
  }

  const closeShortVinBatch = () => {
    showShortVinBatch.value = false
  }
  // 到此

  defineExpose({
    state,
    // getDetail,
    dropDownFun,
    getDateList,
  })
</script>

<style scoped>
  .demo-date-picker {
    display: flex;
    width: 100%;
    padding: 0;
    flex-wrap: wrap;
  }

  .demo-date-picker .block {
    padding: 30px 0;
    text-align: center;
    border-right: solid 1px var(--el-border-color);
    flex: 1;
  }

  .demo-date-picker .block:last-child {
    border-right: none;
  }

  .demo-date-picker .demonstration {
    display: block;
    color: var(--el-text-color-secondary);
    font-size: 14px;
    margin-bottom: 20px;
  }
  .vind {
    width: 100%;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ededed;
  }
  .vinds {
    width: 25%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .vinds:nth-child(odd) {
    background: #f9f9f9;
  }
  .demo-form-inline {
    margin-top: 24px;
  }
  /* .tablers {
    width: 180px;
    height: 100%;
    border: 1px solid #dddddd;
    text-align: center;
    color: red;
  } */

  .refresh {
    cursor: pointer;
    margin-left: 20px;
  }
  .flex {
    display: flex;
    align-items: center;
    text-align: center;
  }

  /* el-table 列数据为空自动显示 -- */
  .className :empty::before {
    content: '--';
    color: gray;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .clear-icon {
    cursor: pointer;
    color: #999;
    margin-right: 8px;
    transition: color 0.2s;
  }

  .clear-icon:hover {
    color: #666;
  }
  .content {
    height: 15px;
    background: #f8f9fb;
    margin-bottom: 20px;
  }
  .hengxian {
    height: 1px;
    border: 1px solid #ededed;
    margin-bottom: 30px;
  }
  .tablers {
    width: 115px;
    height: 42px;
    background: #f8f9fb;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #ededed;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: bold;
    color: #ff0000;
    line-height: 24px;
    text-align: left;
    font-style: normal;
  }
  .VIN2 {
    width: 25%;
    height: 100%;
    text-align: center;
    line-height: 50px;
  }
  /* 使用深度选择器穿透 scoped 样式 */
  .center-label-form-item :deep(.el-form-item__label) {
    display: flex;
    width: 100%;
    line-height: 32px;
  }
  /* 使用深度选择器穿透 scoped 样式 */
  .center-label-form-item :deep(.el-form-item__label) {
    display: flex;
    width: 100%;
    line-height: 32px;
  }
  /* 容器需要相对定位 */
  .batch-input-container {
    position: relative;
    width: 100%;
  }

  /* 输入框样式 */
  .batch-input {
    width: 100%;
    cursor: pointer; /* 显示可点击状态 */
  }

  /* 弹出层容器 */
  .batch-popup-wrapper {
    position: absolute;
    width: 100%;
    top: calc(100% - 1px); /* 显示在输入框下方 */
    left: 0;
    z-index: 2000; /* 确保高于其他元素 */
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); /* 添加阴影区分层级 */
    border-radius: 4px;
    background: #fff;
  }

  /* 解决z-index上下文问题 */
  .el-form-item {
    position: static; /* 确保弹出层不受父级定位影响 */
  }
</style>
