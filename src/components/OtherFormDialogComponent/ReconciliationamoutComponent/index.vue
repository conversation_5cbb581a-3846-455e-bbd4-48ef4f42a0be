<!--
 * @Author: llm
 * @Date: 2025-02-04 10:40:46
 * @LastEditors: llm
 * @LastEditTime: 2025-02-15 18:24:58
 * @Description: 订单管理-编辑对账金额
-->
<template>
  <div>
    <el-dialog :close-on-click-modal="false" v-model="state.dialogVisible.visible" :title="state.dialogVisible.title" width="400px" @close="handleClose">
      <el-scrollbar height="10vh">
        <el-col class="el_menu">
          <div class="el_jiner">对账金额</div>
          <div class="el_price">
            <span class="span_count">{{ state.settlementAmount }}</span
            >元
          </div>
        </el-col>
        <el-col style="display: flex; margin-top: 20px">
          <span class="span_count">*</span>
          <span style="width: 130px">修改对账金额</span>
          <el-input type="number" v-model="number" placeholder="正数，保留两位小时" style="margin-left: 20px" @input="handleInput"></el-input>
        </el-col>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="showButton == ''" @click="cancal">取消</el-button>
          <el-button v-if="showButton == ''" type="primary" @click="ok"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { globalRequestApi } from '@/api/planManagement'

  const emit = defineEmits(['refresh'])

  const state = reactive({
    dialogVisible: {
      visible: false,
      title: '编辑对账金额',
    },
    uri: '',
    id: '',
    settlementAmount: '',
  })

  const showButton = ref('')

  const number = ref()

  const handleInput = (rawValue: string) => {
    // 只允许数字和一个小数点
    let filtered = rawValue.replace(/[^0-9.]/g, '')

    // 如果没有内容，直接返回
    if (filtered === '') {
      return
    }

    // 处理多个小数点，只保留第一个
    const firstDotIndex = filtered.indexOf('.')
    if (firstDotIndex !== -1) {
      const beforeDot = filtered.substring(0, firstDotIndex + 1)
      const afterDot = filtered.substring(firstDotIndex + 1).replace(/\./g, '') // 移除额外的小数点
      filtered = beforeDot + afterDot
    }

    // 分割整数部分和小数部分
    const parts = filtered.split('.')
    let integerPart = parts[0] || ''
    let decimalPart = parts.length > 1 ? parts[1] : ''

    // 处理前导零（例如，001 -> 1）
    integerPart = integerPart.replace(/^0+/, '') || '0'

    // 限制小数部分最多两位
    decimalPart = decimalPart.slice(0, 2)

    // 组合结果
    let result = integerPart
    if (decimalPart.length > 0) {
      result += '.' + decimalPart
    }

    // 设置过滤后的结果
    number.value = result
  }

  const ok = async () => {
    // 如果修改金额为空则报错
    if (number.value == undefined || number.value == '') {
      ElMessage.error('请输入正确的金额')
      number.value = ''
    } else {
      //定义传递的参数
      let params = {
        id: state.id,
        settlementAmount: number.value,
      } as any

      globalRequestApi(params, 'post', state.uri).then(async (res) => {
        state.dialogVisible.visible = false
        emit('refresh')
      })
    }
  }

  const cancal = () => {
    state.dialogVisible.visible = false
    number.value = ''
  }

  const handleClose = () => {
    state.dialogVisible.visible = false
    number.value = ''
  }
  defineExpose({
    state,
  })
</script>

<style scoped>
  .el_menu {
    display: flex;
    align-content: center;
    border-radius: 10px;
    /* box-shadow: 10px 5px 5px #fff; */
    border-block: 1px solid #f0f0f0;
  }
  .el_jiner {
    width: 50%;
    height: 30px;
    background: #f0f0f0;
    display: flex;
    align-content: center;
    justify-content: center;
    align-items: center;
    border-radius: 10px 0 0 10px;
  }
  .el_price {
    width: 50%;
    height: 30px;
    display: flex;
    align-content: center;
    justify-content: center;
    align-items: center;
    border-radius: 0 10px 10px 0;
  }
  .span_count {
    color: red;
  }
</style>
