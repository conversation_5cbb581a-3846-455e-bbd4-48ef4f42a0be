<!--
 * @Author: llm
 * @Date: 2024-04-26 15:10:04
 * @LastEditors: llm
 * @LastEditTime: 2024-05-23 10:42:23
 * @Description: 安全标准弹窗
-->
<template>
  <div>
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" width="80%" :draggable="true" :close-on-click-modal="false" @close="closeDialog">
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form ref="formRef" :model="state.formData" :rules="rules" label-width="130px" :inline="true">
          <el-form-item label="安全标准名称" prop="name">
            <el-input v-model="state.formData.name" :disabled="state.origin === 'viewSafeStandard'"></el-input>
          </el-form-item>
          <el-form-item label="根据仓库设置路径" prop="patrolPathList">
            <el-button type="primary" @click="openInspectionDialog" size="small">设置巡视路径</el-button>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="state.formData.remark" :disabled="state.origin === 'viewSafeStandard'"></el-input>
          </el-form-item>
        </el-form>
        <el-divider border-style="dashed">设置安全标准</el-divider>
        <el-form ref="formRef" :model="state.formData" :rules="rules" label-width="130px" :inline="true" :disabled="state.origin === 'viewSafeStandard'">
          <el-table :data="state.formData.majorItemList">
            <el-table-column label="序号" type="index" width="55" align="center" />
            <el-table-column width="90px" :disabled="state.origin === 'viewSafeStandard'">
              <template #default="scope">
                <el-button
                  type="primary"
                  icon="Plus"
                  size="small"
                  circle
                  @click="addTableItem"
                  v-if="scope.$index === state.formData.majorItemList.length - 1"
                />
                <el-button
                  type="danger"
                  icon="Delete"
                  size="small"
                  circle
                  @click="deleteTableItem(scope.row, scope.$index)"
                  v-if="state.formData.majorItemList.length > 1"
                />
              </template>
            </el-table-column>
            <el-table-column prop="majorItem" label="安全检查项目" width="180" align="center">
              <template #default="scope">
                <el-input v-model="scope.row.majorItem" placeholder="请输入安全检查项目"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="subItem" label="安全检查分项" width="180" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0]">
                  <el-input v-model="item.subItem" placeholder="请输入安全检查分项"></el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="content" label="安全检查内容" width="180" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0]">
                  <el-input v-model="item.content" placeholder="请输入安全检查内容"></el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="检查结果" width="180" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0]">
                  <el-select v-model="result" disabled>
                    <el-option label="符合/不符合" value="1"></el-option>
                  </el-select>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="subItemWeight" label="异常情况说明" width="160" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0]">
                  <el-input v-model="item.abnormalDesc" placeholder="异常情况说明"></el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="whetherUploadPicture" label="是否拍照上传" width="180" align="center">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0]">
                  <el-select v-model="item.whetherUploadPicture">
                    <el-option :label="_item.label" :value="_item.value" v-for="(_item, _index) in whetherUploadPictureOptions" :key="_index"></el-option>
                  </el-select>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.subItemList" :key="index" class="p-[5px_0] h-42px flex justify-center items-center">
                  <el-button
                    type="danger"
                    icon="Delete"
                    circle
                    size="small"
                    @click="deleteSubItem(scope.row, scope.$index, index)"
                    v-if="state.formData.majorItemList[scope.$index].subItemList.length > 1"
                  />
                  <el-button
                    type="primary"
                    icon="Plus"
                    circle
                    size="small"
                    @click="addSubItem(scope.row, scope.$index, index)"
                    v-if="scope.row.category != 1 && index === state.formData.majorItemList[scope.$index].subItemList.length - 1"
                  />
                  <el-button type="primary" icon="Top" circle size="small" @click="upSubItem(scope.row, scope.$index, index)" />
                  <el-button type="primary" icon="Bottom" circle size="small" @click="downSubItem(scope.row, scope.$index, index)" />
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-scrollbar>
      <template #footer v-if="state.origin !== 'viewSafeStandard'">
        <div class="dialog-footer">
          <el-button @click="closeDialog()">取 消</el-button>
          <el-button type="primary" v-loading="submitLoading" @click="handleSubmit(formRef)">保 存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { ExamineTemplateVO, MajorItemList } from '@/api/supplierManagement/type'
  import {
    examineTemplateAddApi,
    examineTemplateUpdateApi,
    examineTemplateCategorySelectOptionsApi,
    examineTemplateDetailApi,
    examineTemplateMajorSelectOptionsApi,
    examineTemplateUseTargetSelectOptionsApi,
  } from '@/api/supplierManagement'
  import type { ComponentSize, FormInstance, FormRules } from 'element-plus'

  import { postSafetyStandardTemplateAddApi, updateSafetyStandardTemplateApi } from '@/api/safetyManagement'
  import { usePatrolPathListStore } from '@/store/modules/patrolPathList'

  const patrolPathListStore = usePatrolPathListStore()
  const dialogVisible = reactive<DialogOption>({
    visible: false,
  })
  const whetherUploadPictureOptions = ref([
    { label: '是', value: true },
    { label: '否', value: false },
  ])
  const emit = defineEmits(['resetQuery', 'openInspectionDialog'])
  const submitLoading = ref(false)
  const formRef = ref()

  const rules = {
    templateName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  }
  const result = ref('1')
  const state = reactive({
    origin: '',
    row: {},
    formData: {
      name: '',
      remark: '',
      patrolPathList: [],
      majorItemList: [
        {
          subItemList: [
            {
              subItem: '',
              subItemSort: 0,
              content: '',
              abnormalDesc: '',
              whetherUploadPicture: true,
            },
          ],
          majorItem: '',
          serialNumber: 0,
        },
      ],
    } as any,
  })
  watch(
    () => dialogVisible.visible,
    (val) => {
      if (val) {
        examineTemplateUseTargetSelectOptions()
        examineTemplateCategorySelectOptions()
      }
    },
  )
  const useTargetOptions = ref<OptionType[]>([])
  const categoryOptions = ref<OptionType[]>([])
  const majorOptions = ref<string[]>([])
  //查看模板详情
  const examineTemplateDetail = async (row: ExamineTemplateVO) => {
    const { data } = await examineTemplateDetailApi({ id: row.id })
    //如果state.formData与data存在相同key则覆盖
    Object.assign(state.formData, data)
  }
  const examineTemplateUseTargetSelectOptions = async () => {
    const { data } = await examineTemplateUseTargetSelectOptionsApi()
    useTargetOptions.value = data as OptionType[]
  }
  const examineTemplateCategorySelectOptions = async () => {
    const { data } = await examineTemplateCategorySelectOptionsApi()
    categoryOptions.value = data as OptionType[]
  }
  const examineTemplateMajorSelectOptions = async (e: any) => {
    const params = {
      useTarget: e,
    }
    const { data } = await examineTemplateMajorSelectOptionsApi(params)
    majorOptions.value = data as any[]
  }

  //选择考核类别
  const changeCategory = (e: any, row: any) => {
    row.majorItem = ''
    examineTemplateMajorSelectOptions(state.formData.useTarget)
    //清除当前行所有数据
    row.subItemList = [
      {
        subItem: '',
        subItemSort: 0,
        content: '',
        abnormalDesc: '',
        whetherUploadPicture: true,
      },
    ]
  }
  const handleSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        submitLoading.value = true
        //遍历表单数据,将subItemSort赋值为数组所在位置的下标,serialNumber赋值为数组所在位置的下标
        state.formData.majorItemList.forEach((item: any, index: number) => {
          item.subItemList.forEach((subItem: any, subIndex: number) => {
            subItem.subItemSort = subIndex
          })
          item.serialNumber = index
        })
        //更新
        if (state.formData.standardNo) {
          if (!state.formData.patrolPathList) {
            state.formData.patrolPathList = []
          }
          await updateSafetyStandardTemplateApi(state.formData)
            .then(() => {
              submitLoading.value = false
              dialogVisible.visible = false
              ElMessage.success('操作成功')
              closeDialog()
              emit('resetQuery')
            })
            .catch((error: any) => {
              submitLoading.value = false
            })
        } else {
          await postSafetyStandardTemplateAddApi(state.formData)
            .then(() => {
              submitLoading.value = false
              dialogVisible.visible = false
              ElMessage.success('操作成功')
              closeDialog()
              emit('resetQuery')
            })
            .catch((error: any) => {
              submitLoading.value = false
            })
        }
      } else {
      }
    })
  }
  const openInspectionDialog = async () => {
    emit('openInspectionDialog', state.row, state.origin)
  }
  const closeDialog = () => {
    //清空表单数据
    state.formData.templateName = ''
    state.formData.useTarget = 0
    state.formData.desc = ''
    state.formData.systemType = 'JIAOY'
    state.formData.majorItemList = [
      {
        subItemList: [
          {
            subItem: '',
            subItemSort: 0,
            content: '',
            abnormalDesc: '',
            whetherUploadPicture: true,
          },
        ],
        majorItem: '',
        serialNumber: 0,
      },
    ]
    formRef.value.resetFields()
    dialogVisible.visible = false
  }
  /**
   * 新增考核项
   */
  const addTableItem = () => {
    state.formData.majorItemList.push({
      subItemList: [
        {
          subItem: '',
          subItemSort: 0,
          content: '',
          abnormalDesc: '',
          whetherUploadPicture: true,
        },
      ],
      majorItem: '',
      serialNumber: 0,
    })
  }
  /**
   * 删除考核项
   */
  const deleteTableItem = (row: any, index: number) => {
    state.formData.majorItemList.splice(index, 1)
  }
  /**
   * 新增考核子项
   */
  const addSubItem = (row: any, rowIndex: number, index: number) => {
    state.formData.majorItemList[rowIndex].subItemList.push({
      subItem: '',
      assessMark: 0,
      subItemWeight: 0,
      subItemSort: 0,
      whetherUploadPicture: true,
    })
  }
  /**
   * 删除考核子项
   */
  const deleteSubItem = (row: any, rowIndex: number, index: number) => {
    state.formData.majorItemList[rowIndex].subItemList.splice(index, 1)
  }
  /**
   * 上移考核子项
   */
  const upSubItem = (row: any, rowIndex: number, subItemIndex: number) => {
    if (subItemIndex > 0) {
      let temp = state.formData.majorItemList[rowIndex].subItemList[subItemIndex]
      state.formData.majorItemList[rowIndex].subItemList[subItemIndex] = state.formData.majorItemList[rowIndex].subItemList[subItemIndex - 1]
      state.formData.majorItemList[rowIndex].subItemList[subItemIndex - 1] = temp
    }
  }
  /**
   * 下移考核子项
   */
  const downSubItem = (row: any, rowIndex: number, index: number) => {
    if (index < state.formData.majorItemList[rowIndex].subItemList.length - 1) {
      let temp = state.formData.majorItemList[rowIndex].subItemList[index]
      state.formData.majorItemList[rowIndex].subItemList[index] = state.formData.majorItemList[rowIndex].subItemList[index + 1]
      state.formData.majorItemList[rowIndex].subItemList[index + 1] = temp
    }
  }
  const resetForm = () => {
    state.formData = {
      name: '',
      remark: '',
      patrolPathList: [],
      majorItemList: [
        {
          subItemList: [
            {
              subItem: '',
              subItemSort: 0,
              content: '',
              abnormalDesc: '',
              whetherUploadPicture: true,
            },
          ],
          majorItem: '',
          serialNumber: 0,
        },
      ],
    }
  }
  defineExpose({
    dialogVisible,
    examineTemplateDetail,
    state,
    resetForm,
  })
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    border-top-color: #e89e42;
  }

  :deep(.el-divider__text) {
    color: #e89e42;
  }
</style>
