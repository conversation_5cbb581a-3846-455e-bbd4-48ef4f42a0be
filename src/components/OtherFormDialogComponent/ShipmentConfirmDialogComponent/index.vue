<!--
 * @Author: llm
 * @Date: 2024-01-15 15:57:17
 * @LastEditors: llm
 * @LastEditTime: 2024-04-11 09:35:04
 * @Description: 运单确认-一键确认
-->
<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="60%" :draggable="true" :close-on-click-modal="false" @close="cancel(ruleFormRef)">
    <el-scrollbar max-height="60vh" class="formClass">
      <el-form ref="ruleFormRef" label-width="120px" :inline="true" :model="state.formData" style="width: 93%; margin: 0 auto">
        <el-row>
          <el-col :span="24">
            <el-form-item label="车辆" style="width: 100%">
              <el-select filterable v-model="state.batchShipmentInfo!.vehicleId" style="width: 220px" placeholder="请选择车辆" @change="handleVehicleChange">
                <el-option v-for="(item, index) in vehicleList || []" :key="index" :label="item.label" :value="item.value!" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="任务" style="width: 100%" prop="shipmentNo" :rules="[{ required: false, message: '请选择任务', trigger: 'change' }]">
              <el-select filterable v-model="state.batchShipmentInfo!.shipmentNo" style="width: 220px" placeholder="请选择任务" @change="handleTaskChange">
                <el-option v-for="(item, index) in taskList || []" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-card shadow="never" v-if="state.batchShipmentInfo!.baseWareItems && state.batchShipmentInfo!.baseWareItems!.length > 0">
          <div v-for="(item, index) in state.batchShipmentInfo!.baseWareItems" :key="index" style="margin-bottom: 10px">
            <el-form-item label="仓库">
              <el-input v-model="item.baseWareName" disabled style="width: 220px" />
            </el-form-item>
            <el-form-item label="车道" :prop="`baseWareItems.${index}.laneId`" :rules="[{ required: false, message: '请选择预约车道', trigger: 'change' }]">
              <el-select v-model="item.laneId" style="width: 220px" placeholder="请选择车道" @change="handleLaneChange(item, index, $event)">
                <el-option v-for="(_item, _index) in item.lanes || []" :key="_index" :label="_item.laneName" :value="_item.laneId" />
              </el-select>
            </el-form-item>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="预约日期"
                  :prop="`baseWareItems.${index}.confirmDate`"
                  :rules="[{ required: false, message: '请选择装车日期', trigger: 'blur' }]"
                >
                  <el-date-picker
                    v-model="item.confirmDate"
                    type="date"
                    placeholder="请选择装车日期"
                    style="width: 220px"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    @change="handleDateChange(item, index, $event)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="预约时间段"
                  :prop="`baseWareItems.${index}.confirmTime`"
                  :rules="[{ required: false, message: '请选择装车时间段', trigger: 'blur' }]"
                >
                  <el-time-picker
                    v-model="item.confirmTime"
                    is-range
                    format="HH:mm"
                    value-format="HH:mm"
                    range-separator="-"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    clearable
                    style="width: 220px"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div style="width: 100%">
              <TableComponent :tableData="item.orders" :tableConfig="tableConfig" />
            </div>
          </div>
        </el-card>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel(ruleFormRef)">取消</el-button>
        <el-button type="primary" @click="confirm(ruleFormRef)">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
  import { appointmentIdsApi, appointmentSubmitApi, shipmentOrderApplyInfoByVehicleApi, shipmentOrderConfirmSubmitApi } from '@/api/shipmentManagement'
  import {
    ApplyVehicleInfoVO,
    AppointmentsVO,
    BatchShipmentConfirmVO,
    ConfirmFormDataVO,
    StateVO,
    VehiclesVO,
    appointmentIdsRequest,
    baseWareItemsVO,
    highWayBatchShipmentConfirmVO,
    lanesVO,
    shipmentOrderConfirmSubmitItemsVO,
    shipmentOrderConfirmSubmitVO,
    vehicleWaresVO,
  } from '@/api/shipmentManagement/type'
  import { FormInstance } from 'element-plus'
  import { PropType } from 'vue'
  import { VehicleVO } from '../../../types/global'
  import { carrierVehicleSelect, carrierVehicleSimpleSelect, vehicleTaskSelect } from '@/api/planManagement'
  import { CarriersVO } from '../../../api/shipmentManagement/type'
  const emit = defineEmits(['closeDialog', 'shipmentOrderConfirmSubmit'])
  const ruleFormRef = ref<FormInstance>()
  const props = defineProps({
    /**
     * 行信息
     */
    tableRow: {
      type: Object,
      default: () => {
        return {}
      },
    },
    /**
     * 弹窗属性
     */
    dialog: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
    /**
     * 运单信息 3 公路 4 铁水 9 分拨
     */
    transportType: {
      type: Number,
      default: 3,
    },
  })
  const tableConfig = {
    tableItem: [
      {
        name: 'vin',
        label: '车架号',
        listEnable: true,
      },
    ],
  }
  const state = reactive<BatchShipmentConfirmVO>({
    formData: {},
    batchShipmentInfo: {},
  })
  /**
   * 承运商列表
   */
  const carriers = ref<OptionType[]>()
  /**
   * 车辆列表
   */
  const vehicleList = ref<OptionType[]>()
  /**
   * 任务列表
   */
  const taskList = ref<OptionType[]>()
  /**
   * 当前选中的车道
   */
  const currentLanes = ref<lanesVO[]>([])
  /**
   * 选中的车辆
   */
  const currentVehicle = ref<VehiclesVO>()
  /**
   * 一键确认-选中的承运商
   */
  const currentCarrierId = ref<string>()
  /**
   * 一键确认-选中的车辆
   */
  const currentVehicleId = ref<string>()
  /**
   * 选中的车辆绑定的倒板仓
   */
  const currentVehicleWares = ref<vehicleWaresVO[]>()
  /**
   * @description: 选择承运商
   * @return {*}
   */
  async function handleCarrierChange(e: string): Promise<any> {
    vehicleList.value = await getCarrierVehicleSelect('')
  }
  async function handleVehicleChange(e: string) {
    //清空数据
    taskList.value = []
    state.batchShipmentInfo!.shipmentNo = ''
    state.batchShipmentInfo!.baseWareItems = []
    taskList.value = await getVehicleTaskSelect(e)
    // state.batchShipmentInfo!.baseWareItems = await shipmentOrderApplyInfoByVehicle(e);
  }
  /**
   * @description: 选择任务
   */
  async function handleTaskChange(e: string) {
    state.batchShipmentInfo!.baseWareItems = await shipmentOrderApplyInfoByVehicle(e)
  }

  /**
   * 获取承运商车辆下拉
   * @param carrierId 承运商id
   */
  const getCarrierVehicleSelect = (carrierId: string) => {
    currentCarrierId.value = carrierId
    return new Promise<OptionType[]>((resolve, reject) => {
      vehicleList.value = []
      const params = {
        simple: true,
      }
      carrierVehicleSimpleSelect(params).then((res) => {
        resolve(res.data as OptionType[])
      })
    })
  }
  /**
   * 获取车辆下任务列表
   * @param vehicleId 车辆ID
   */
  const getVehicleTaskSelect = (vehicleId: string) => {
    currentVehicleId.value = vehicleId
    return new Promise<OptionType[]>((resolve, reject) => {
      taskList.value = []
      vehicleTaskSelect(vehicleId).then((res) => {
        resolve(res.data as OptionType[])
      })
    })
  }
  /**
   * 根据所选车辆获取运单确认信息
   * @param vehicleId 车辆id
   */
  const shipmentOrderApplyInfoByVehicle = (shipmentNo: string) => {
    return new Promise<baseWareItemsVO[]>((resolve, reject) => {
      const params = {
        shipmentNo,
      }
      shipmentOrderApplyInfoByVehicleApi(params).then((res: any) => {
        resolve(res.data.baseWareItems as baseWareItemsVO[])
      })
    })
  }
  /**
   *
   */
  /**
   * @description: 选择预约车道
   * @param {*} item 当前项
   * @param {*} index 当前索引
   * @param {*} event 值
   * @return {*}
   */
  function handleLaneChange(item: appointmentIdsRequest, index: number, event: any): void {
    const lane = state.batchShipmentInfo!.baseWareItems![index].lanes.find((lane: lanesVO) => {
      return event.includes(lane.laneId)
    })
    state.batchShipmentInfo!.baseWareItems![index].laneName = lane!.laneName
  }
  /**
   *
   * @param formEl 关闭弹窗
   */
  function cancel(formEl: FormInstance | undefined) {
    //清空表单数据
    formEl!.resetFields()
    state.batchShipmentInfo!.shipmentNo = ''
    state.batchShipmentInfo!.vehicleId = ''
    state.batchShipmentInfo!.baseWareItems = []
    taskList.value = []
    vehicleList.value = []
    emit('closeDialog')
  }
  // 提交表单
  const confirm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        const items = state.batchShipmentInfo?.baseWareItems!.map((item) => {
          return {
            confirmDate: item.confirmDate,
            confirmTime: item.confirmTime!.join('~'),
            laneId: item.laneId,
            laneName: item.laneName,
            orderIds: item.orders.map((orderId) => orderId.id),
          }
        })
        const params = {
          items,
        } as shipmentOrderConfirmSubmitVO
        emit('shipmentOrderConfirmSubmit', params)
      } else {
      }
    })
  }
  /**
   * @description: 选择预约时间段
   * @param {*} item 当前项
   * @param {*} index 当前索引
   * @param {*} event 值
   * @return {*}
   */
  function handleDateChange(item: appointmentIdsRequest, index: number, event: string): void {
    const params = {
      appointmentDate: item.appointmentDate,
      laneId: item.laneId,
    }
    appointmentIdsApi(params).then((res) => {
      const { data } = res
      if (currentLanes.value.length > 0) {
        if (data.length > 0) {
          data.forEach((v: any) => {
            currentLanes.value![0].appointments.map((item) => {
              if (v === item.appointmentId) {
                item.appointmentStatus = 1
              } else {
                item.appointmentStatus = 0
              }
            })
          })
        } else {
          currentLanes.value![0].appointments.map((item) => {
            item.appointmentStatus = 0
          })
        }
      }
    })
  }
  defineExpose({
    state,
    carriers,
    ruleFormRef,
    handleCarrierChange,
    // handleLaneChange,
  })
</script>
<style scoped lang="scss">
  .lane-date {
    display: inline-block;
    width: 100px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border: 1px solid #ebeef5;
    background-color: #ffffff;
    cursor: pointer;
    border-radius: 4px;
    &.active {
      background-color: #1890ff;
      color: #ffffff;
    }
    &.disabled {
      background-color: #e3e3e3;
      //禁止点击
      cursor: not-allowed;
    }
  }
  .time-card {
    border-bottom: none;
    :deep(.el-card__body) {
      padding-bottom: 0;
    }
  }
</style>
