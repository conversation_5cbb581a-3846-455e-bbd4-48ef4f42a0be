<!--
 * @Author: llm
 * @Date: 2025-02-04 10:40:46
 * @LastEditors: llm
 * @LastEditTime: 2025-04-17 10:02:21
 * @Description: 外协结算-付款管理-补贴付款
-->
<template>
  <div>
    <el-dialog :close-on-click-modal="false" v-model="state.dialogVisible.visible" :title="state.dialogVisible.title" width="800px" @close="handleClose">
      <el-scrollbar height="60vh">
        <div>
          <el-card>
            <el-form :model="state.formData" label-width="120px">
              <el-row>
                <el-form-item label="司机姓名" prop="settlementNo">
                  <el-input v-model="state.formData.driverName" :disabled="true" style="width: 200px" />
                </el-form-item>
                <el-form-item label="结算单号" prop="settlementNo">
                  <el-input v-model="state.formData.settlementNo" :disabled="true" style="width: 200px" />
                </el-form-item>
              </el-row>
              <el-row>
                <el-col :span="7">
                  <el-form-item label="现金付款合计" prop="cashPaymentAmount">
                    <el-input v-model="state.formData.cashPaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="已付款金额" prop="cashHavePaymentAmount">
                    <el-input v-model="state.formData.cashHavePaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="未付款金额" prop="cashHaveNotPaymentAmount">
                    <el-input v-model="state.formData.cashHaveNotPaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item label="" prop="" label-width="20">
                    <el-link
                      :type="state.paymentType === 1 ? 'info' : 'primary'"
                      :disabled="state.paymentType === 1 || state.formData.cashHaveNotPaymentAmount == 0"
                      @click="handlePayment(1)"
                      :underline="false"
                      >打款</el-link
                    >
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- <el-row>
                <el-col :span="7">
                  <el-form-item label="万金油付款合计" prop="wjyOutcomeIncludeTaxed">
                    <el-input v-model="state.formData.wjyOutcomeIncludeTaxed" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="已付款金额" prop="wjyHavePaymentAmount">
                    <el-input v-model="state.formData.wjyHavePaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="未付款金额" prop="wjyHaveNotPaymentAmount">
                    <el-input v-model="state.formData.wjyHaveNotPaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item label="" prop="paymentAmount" label-width="20">
                    <el-link
                      :type="state.paymentType === 2 ? 'info' : 'primary'"
                      :disabled="state.paymentType === 2"
                      @click="handlePayment(2)"
                      :underline="false"
                      >打款</el-link
                    >
                  </el-form-item>
                </el-col>
              </el-row> -->
              <!-- <el-row>
                <el-col :span="7">
                  <el-form-item label="开票付款合计" prop="invoiceOutcomeIncludeTaxed">
                    <el-input v-model="state.formData.invoiceOutcomeIncludeTaxed" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="已付款金额" prop="invoiceHavePaymentAmount">
                    <el-input v-model="state.formData.invoiceHavePaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="未付款金额" prop="invoiceHaveNotPaymentAmount">
                    <el-input v-model="state.formData.invoiceHaveNotPaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item label="" prop="paymentAmount" label-width="20">
                    <el-link
                      :type="state.paymentType === 3 ? 'info' : 'primary'"
                      :disabled="state.paymentType === 3"
                      @click="handlePayment(3)"
                      :underline="false"
                      >打款</el-link
                    >
                  </el-form-item>
                </el-col>
              </el-row> -->
              <el-row>
                <el-col :span="7">
                  <el-form-item label="油付款合计" prop="oilPaymentAmount">
                    <el-input v-model="state.formData.oilPaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="已付款金额" prop="oilHavePaymentAmount">
                    <el-input v-model="state.formData.oilHavePaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="未付款金额" prop="oilHaveNotPaymentAmount">
                    <el-input v-model="state.formData.oilHaveNotPaymentAmount" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item label="" prop="paymentAmount" label-width="20">
                    <el-link
                      :type="state.paymentType === 3 ? 'info' : 'primary'"
                      :disabled="state.paymentType === 3 || state.formData.oilHaveNotPaymentAmount == 0"
                      @click="handlePayment(3)"
                      :underline="false"
                      >打款</el-link
                    >
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-card>
          <el-card
            :header="state.paymentType === 1 ? '现金打款' : state.paymentType === 2 ? '万金油打款' : '油付款'"
            style="margin-top: 10px"
            v-if="state.paymentType === 1 || state.paymentType === 2 || state.paymentType === 3"
          >
            <el-form
              ref="paymentCashFormRef"
              :rules="rules"
              :model="state.paymentCashFormData"
              label-width="120px"
              v-if="state.paymentType === 1 || state.paymentType === 2"
            >
              <el-form-item label="付款方式" prop="methodval">
                <el-select v-model="methodval" placeholder="请选择付款方式" style="width: 220px" @change="selMethod">
                  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="付款金额" prop="paymentAmount">
                <el-input v-model="state.paymentCashFormData.paymentAmount" type="number" autocomplete="off" :precision="2" @change="getAmountone" :min="0" />
              </el-form-item>
              <el-form-item label="付款时间" prop="paymentDate">
                <el-date-picker
                  v-model="state.paymentCashFormData.paymentDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  autocomplete="off"
                  :disabled-date="disableFutureDates"
                />
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input v-model="state.paymentCashFormData.remark" type="textarea" autocomplete="off" />
              </el-form-item>
              <el-form-item label="上传打款凭证" prop="invoiceUrls">
                <UploadImageComponent ref="uploadImageRef" tip="支持图片格式jpg、jpeg、png" />
              </el-form-item>
            </el-form>
            <el-form ref="paymentInvoiceFormRef" :rules="rules" :model="state.paymentInvoiceFormData" label-width="120px" v-if="state.paymentType === 3">
              <!-- <el-form-item label="关联发票" prop="invoiceId">
                <el-select v-model="state.paymentInvoiceFormData.invoiceId" placeholder="请选择关联发票">
                  <el-option v-for="item in state.invoiceList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item> -->
              <!-- <el-form-item label="打款方式" prop="paymentMethod">
                <el-select v-model="state.paymentInvoiceFormData.paymentMethod" placeholder="请选择打款方式">
                  <el-option v-for="item in paymentTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item> -->
              <el-form-item label="付款金额" prop="paymentAmount">
                <el-input v-model="state.paymentInvoiceFormData.paymentAmount" type="number" autocomplete="off" @change="getAmount" :min="0" />
                <!-- <el-input-number
                  v-model="state.paymentInvoiceFormData.paymentAmount"
                  controls-position="right"
                  :precision="2"
                  :min="0"
                  @change="getAmount"
                  style="width: 100%"
                ></el-input-number> -->
              </el-form-item>
              <el-form-item label="付款时间" prop="paymentDate">
                <el-date-picker
                  v-model="state.paymentInvoiceFormData.paymentDate"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  autocomplete="off"
                  :disabled-date="disableFutureDates"
                />
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input v-model="state.paymentInvoiceFormData.remark" type="textarea" autocomplete="off" />
              </el-form-item>
              <el-form-item label="上传打款凭证" prop="invoiceUrls">
                <UploadImageComponent ref="uploadImageRef" tip="支持图片格式Jpg、jpeg、png" />
              </el-form-item>
            </el-form>
            <div style="text-align: center" v-if="state.paymentType === 1">
              <el-button type="primary" @click="handleSave" :disabled="state.formData.cashHaveNotPaymentAmount == 0">保存</el-button>
            </div>
            <div style="text-align: center" v-if="state.paymentType === 3">
              <el-button type="primary" @click="handleSave" :disabled="state.formData.oilHaveNotPaymentAmount == 0">保存</el-button>
            </div>
          </el-card>
        </div>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import {
    getOutFleetOrderCarrierCostPaymentDetailApi,
    getOutFleetOrderCarrierCostPaymentInvoiceListApi,
    getOutFleetOrderCarrierCostPaymentSubsidyDetailApi,
    postOutFleetOrderCarrierCostPaymentPartApi,
    postSubsidyPaymentApi,
  } from '@/api/financialManagement'
  import UploadImageComponent from '@/components/UploadImageComponent/index.vue'
  import { paymentTypeOptions } from '@/utils/selectOptions'
  import { UploadUserFile } from 'element-plus'
  const uploadImageRef = ref()
  const paymentCashFormRef = ref()
  const paymentInvoiceFormRef = ref()
  // 'refresh'
  const emit = defineEmits(['closeDialog', 'resetQuery'])
  const state = reactive<any>({
    noPaymentAmount: '', //未付款金额
    invoiceList: [] as any[], //发票列表
    paymentType: 1,
    dialogVisible: {
      visible: false,
      title: '编辑打款信息',
    },
    origin: 'add',
    formData: {
      driverName: '',
      settlementNo: '',
      cashPaymentAmount: '', //现金付款合计
      // cashOutcomeIncludeTaxed: '', //现金付款合计
      cashHavePaymentAmount: '', //现金已经付款合计
      cashHaveNotPaymentAmount: '', //现金暂未付款合计
      // wjyOutcomeIncludeTaxed: '', //万金油付款合计
      // wjyHavePaymentAmount: '', //万金油已经付款合计
      // wjyHaveNotPaymentAmount: '', //万金油暂未付款合计
      invoiceOutcomeIncludeTaxed: '', //开票付款合计
      invoiceHavePaymentAmount: '', //开票已经付款合计
      invoiceHaveNotPaymentAmount: '', //开票暂未付款合计
      oilPaymentAmount: '', //油付款合计
      oilHavePaymentAmount: '', //油已经付款计
      oilHaveNotPaymentAmount: '', //油暂未付款合计
    },
    id: '',
    paymentCashFormData: {
      paymentAmount: '',
      paymentDate: '',
      remark: '',
      paymentMethod: '现金打款',
      paymentId: '',
      invoiceUrlsList: [],
      paymentType: '', //付款方式
      methodval: '',
    },
    paymentInvoiceFormData: {
      paymentAmount: '',
      paymentDate: '',
      remark: '',
      paymentMethod: '油支付',
      paymentId: '',
      invoiceUrlsList: [],
      invoiceId: '',
      paymentType: '油打款', //付款方式
      methodval: '',
    },
  })
  const methodval = ref<any>(null)

  const selMethod = () => {
    state.paymentCashFormData.paymentType = methodval.value
    state.paymentCashFormData.methodval = methodval.value
  }
  const options = [
    {
      value: '现金',
      label: '现金',
    },
    {
      value: '转账',
      label: '转账',
    },
  ]
  const validatePaymentAmount = (rule: any, value: any, callback: any) => {
    let notPaymentAmount = 0
    if (state.paymentType === 1) {
      notPaymentAmount = Number(state.formData.cashHaveNotPaymentAmount)
    }
    // else if (state.paymentType === 2) {
    //   notPaymentAmount = Number(state.formData.wjyHaveNotPaymentAmount)
    // }
    else if (state.paymentType === 3) {
      notPaymentAmount = Number(state.formData.oilHaveNotPaymentAmount)
    }
    if (value === '') {
      callback(new Error('请输入打款金额'))
    } else if (value > notPaymentAmount) {
      callback(new Error('打款金额不能大于未付款金额'))
    } else {
      callback()
    }
  }
  // 禁用未来日期选择
  const disableFutureDates = (time: Date) => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return time.getTime() > today.getTime()
  }

  const rules = {
    paymentAmount: [
      { required: true, message: '请输入打款金额', trigger: 'blur' },
      { validator: validatePaymentAmount, trigger: 'blur' },
    ],
    paymentDate: [{ required: true, message: '请选择打款时间', trigger: 'change' }],
    paymentMethod: [{ required: true, message: '请选择打款方式', trigger: 'change' }],
    invoiceId: [{ required: true, message: '请选择关联发票', trigger: 'change' }],
    methodval: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
  }

  const getInvoiceList = async (id: string) => {
    const { data } = await getOutFleetOrderCarrierCostPaymentInvoiceListApi({ id: state.id })
    state.invoiceList = data as SelectVO[]
  }
  // 获取上传图片列表
  const getUploadImageList = () => {
    return uploadImageRef.value.uploadImageList
  }
  //设置上传图片列表
  const setUploadImageList = (list: UploadUserFile[]) => {
    uploadImageRef.value.uploadImageList = list
  }

  const params = ref<any>({
    paymentSubsidyId: '', //补贴付款id
    paymentType: '',
    amount: '', //付款金额
    paymentPic: '', //付款凭证
    deptId: '', //部门id
    paymentPics: '', //付款凭证
  })

  const getAmount = (value: any) => {
    if (value !== '') {
      if (value <= 0) {
        state.paymentInvoiceFormData.paymentAmount = 0
        params.value.amount = 0
      } else {
        state.paymentInvoiceFormData.paymentAmount = parseFloat(value).toFixed(2)
        params.value.amount = state.paymentInvoiceFormData.paymentAmount
      }
    }
  }
  const getAmountone = (value: any) => {
    if (value !== '') {
      if (value <= 0) {
        state.paymentCashFormData.paymentAmount = 0
        params.value.amount = 0
      } else {
        state.paymentCashFormData.paymentAmount = parseFloat(value).toFixed(2)
        params.value.amount = state.paymentCashFormData.paymentAmount
      }
    }
  }

  const getDetail = async (id: string) => {
    state.id = id
    const { data } = await getOutFleetOrderCarrierCostPaymentSubsidyDetailApi({ id })
    params.value.deptId = data.driverId
    params.value.paymentSubsidyId = data.id
    state.formData = data as any
    state.formData.driverName = data.driverName
    state.formData.settlementNo = data.settlementNo
    // 获取当前日期
    const currentDate = new Date()
    const formattedDate = currentDate.toISOString().split('T')[0]
    state.paymentCashFormData.paymentDate = formattedDate
    state.paymentInvoiceFormData.paymentDate = formattedDate
    // 若现金未付款金额为0 切换到油支付
    // if (data.cashHaveNotPaymentAmount == 0) {
    //   state.paymentType = 3
    // }
  }
  const handlePayment = (type: number) => {
    if (paymentCashFormRef.value) {
      paymentCashFormRef.value.resetFields()
    }
    if (paymentInvoiceFormRef.value) {
      paymentInvoiceFormRef.value.resetFields()
    }
    state.paymentCashFormData.invoiceUrlsList = []
    state.paymentInvoiceFormData.invoiceUrlsList = []
    state.paymentType = type
    // 获取当前日期
    const currentDate = new Date()
    const formattedDate = currentDate.toISOString().split('T')[0]
    switch (type) {
      case 1:
        state.paymentCashFormData.paymentMethod = '现金打款'
        state.paymentCashFormData.paymentDate = formattedDate
        break
      case 2:
        state.paymentCashFormData.paymentMethod = '油打款'
        state.paymentInvoiceFormData.paymentDate = formattedDate
        break
      case 3:
        getInvoiceList(state.id)
        break
    }
  }

  const handleSave = () => {
    if (state.paymentType === 1 || state.paymentType === 2) {
      state.paymentCashFormData.invoiceUrlsList = getUploadImageList()
      state.paymentCashFormData.paymentId = state.id
      paymentCashFormRef.value.validate(async (valid: boolean) => {
        if (valid) {
          try {
            // 保存
            params.value.paymentPics = state.paymentCashFormData.invoiceUrlsList

            let param = { ...state.formData, ...params.value, ...state.paymentCashFormData }
            const { data } = await postSubsidyPaymentApi(param)
            ElMessage.success('信息已更新')
            //重置表单
            paymentCashFormRef.value.resetFields()
            setUploadImageList([])
            await getDetail(state.id)
            emit('resetQuery') // 触发列表刷新
          } catch (error) {}
        } else {
          return false
        }
      })
    } else {
      state.paymentInvoiceFormData.invoiceUrlsList = getUploadImageList()
      state.paymentInvoiceFormData.paymentId = state.id
      paymentInvoiceFormRef.value.validate(async (valid: boolean) => {
        if (valid) {
          try {
            params.value.paymentPics = state.paymentInvoiceFormData.invoiceUrlsList
            let param = { ...state.formData, ...params.value, ...state.paymentInvoiceFormData }

            const { data } = await postSubsidyPaymentApi(param)
            ElMessage.success('信息已更新')
            //重置表单
            paymentInvoiceFormRef.value.resetFields()
            setUploadImageList([])

            await getDetail(state.id)
            emit('resetQuery') // 触发列表刷新
          } catch (error) {}
        }
      })
    }
  }
  const handleClose = () => {
    if (state.paymentType === 3) {
      paymentInvoiceFormRef.value.resetFields()
    } else {
      paymentCashFormRef.value.resetFields()
    }
    state.paymentType = 1
    state.paymentCashFormData.invoiceUrlsList = []
    state.paymentInvoiceFormData.invoiceUrlsList = []
    methodval.value = ''
    setUploadImageList([])
  }
  defineExpose({
    state,
    getDetail,
  })
</script>
