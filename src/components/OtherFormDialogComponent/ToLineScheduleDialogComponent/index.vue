/* * @Author: llm * @Date: 2024-05-13 17:57:52 * @LastEditors: llm * @LastEditTime: 2024-05-27 11:16:50 * @Description: 干线排程弹窗 */
<script setup lang="ts">
  import { ref } from 'vue'
  import { loadBaiDuDrawMap } from '@/utils/bmpgl_lib'
  import qidian from '@/assets/images/dingwei_qidian.png'
  import zhongdian from '@/assets/images/dingwei_zhongdian.png'
  import peisong1 from '@/assets/images/dingwei_peisong1.png'
  import peisong2 from '@/assets/images/dingwei_peisong2.png'
  import peisong3 from '@/assets/images/dingwei_peisong3.png'
  import peisong4 from '@/assets/images/dingwei_peisong4.png'
  import peisong5 from '@/assets/images/dingwei_peisong5.png'
  import xian from '@/assets/xian.png'
  import { PointProps, ShareFormVO } from '@/api/InTransitManagement/types'
  import { mainLineShare } from '@/api/InTransitManagement'
  import { bd_encrypt } from '@/utils'

  const loading = ref(true)
  const dialogVisible = reactive<DialogOption>({
    visible: false,
    title: '干线排程',
  })
  const state = reactive({
    data: {} as any,
    miniMap: null as any,
    mapCenter: [116.397451, 39.909187], // 默认位置
    innerMapMarkerList: [] as any[],
    shareForm: {} as ShareFormVO, //分享路线信息
    shipmentNo: '',
  })
  const mapObj = ref()
  const mapSearch = ref(null)
  const detailsTableHeight = ref('300px')
  // 初始化地图
  const initMiniMap = async () => {
    return new Promise((resolve, reject) => {
      loadBaiDuDrawMap().then((_BMapGL) => {
        let BMapGL = null
        // 创建地图实例
        if (_BMapGL.BMapGL) {
          BMapGL = _BMapGL.BMapGL
        } else {
          BMapGL = _BMapGL
        }
        // 创建地图实例
        mapObj.value = new BMapGL.Map(mapSearch.value)
        // 添加比例尺控件
        mapObj.value.addControl(
          new BMapGL.ScaleControl({
            anchor: BMAP_ANCHOR_BOTTOM_LEFT,
            offset: new BMapGL.Size(10, 10),
          }),
        )
        // 添加缩放控件
        mapObj.value.addControl(
          new BMapGL.ZoomControl({
            anchor: BMAP_ANCHOR_BOTTOM_RIGHT,
            offset: new BMapGL.Size(10, 10),
          }),
        )
        // 保存地图
        state.miniMap = mapObj.value
        //清除地图上所有覆盖物
        state.miniMap!.clearOverlays()
        //默认
        const point = new BMapGL.Point(Number(state.mapCenter[0]), Number(state.mapCenter[1]))
        // 初始化地图，设置中心点坐标和地图级别
        state.miniMap.centerAndZoom(point, 5)
        //开启鼠标滚轮缩放
        state.miniMap.enableScrollWheelZoom(true)
        resolve(state.miniMap)
      })
    })
  }

  function addMarker(location: any[], type: string, index?: number) {
    let iconImg = ''
    if (type == 'start') {
      iconImg = qidian
    } else if (type == 'end') {
      iconImg = zhongdian
    } else if (type == 'distribution') {
      switch (index) {
        case 1:
          iconImg = peisong1
          break
        case 2:
          iconImg = peisong2
          break
        case 3:
          iconImg = peisong3
          break
        case 4:
          iconImg = peisong4
          break
        case 5:
          iconImg = peisong5
          break
        default:
          break
      }
    }
    //高德转百度
    const bd_point = bd_encrypt(location[0], location[1])
    const point = new BMapGL.Point(Number(bd_point.bd_lng), Number(bd_point.bd_lat))
    const myIcon = new BMapGL.Icon(iconImg, new BMapGL.Size(36, 38))
    const marker = new BMapGL.Marker(point, { icon: myIcon }) // 创建标注
    state.miniMap.addOverlay(marker) //添加标注
  }

  //画轨迹
  const drawLine = async (data: any) => {
    if (state.miniMap) {
      //清除地图上所有覆盖物
      state.miniMap.clearOverlays()
      const start_bd_point = bd_encrypt(data.startInfo.coordinate.split(',')[0], data.startInfo.coordinate.split(',')[1])
      //添加起点
      const start = [start_bd_point.bd_lng, start_bd_point.bd_lat]
      addMarker(start, 'start', 0)
      //添加终点
      const end_bd_point = bd_encrypt(data.endInfo.coordinate.split(',')[0], data.endInfo.coordinate.split(',')[1])
      const end = [end_bd_point.bd_lng, end_bd_point.bd_lat]
      addMarker(end, 'end', 1)

      //添加途径点
      data.waypoints?.forEach((item: any, index: number) => {
        const way_bd_point = bd_encrypt(item.addressInfo.coordinate.split(',')[0], item.addressInfo.coordinate.split(',')[1])
        const distribution = [way_bd_point.bd_lng, way_bd_point.bd_lat]
        addMarker(distribution, 'distribution', index + 1)
      })
      // //添加行驶轨迹
      let tracks = [] as any[]
      let trackList = data.tracks.split(';')
      trackList.map((item: any) => {
        const track = item.split(',')
        const way_bd_point = bd_encrypt(track[0], track[1])
        tracks.push(new BMapGL.Point(Number(way_bd_point.bd_lng), Number(way_bd_point.bd_lat)))
      })
      const drivingPath = new BMapGL.Polyline(tracks, {
        enableEditing: false, //是否启用线编辑，默认为false
        // enableClicking: true,//是否响应点击事件，默认为true
        strokeWeight: 8, //折线的宽度，以像素为单位
        strokeOpacity: 1, //折线的透明度，取值范围0
        strokeTexture: {
          // width/height 需要是2的n次方
          url: xian,
          width: 16,
          height: 64,
        },
      })
      state.miniMap.addOverlay(drivingPath)
      //缩放到合适的视野级别
      state.miniMap.setViewport(tracks)
    }
  }

  // 合计
  function getSummaries(param: any) {
    const { columns, data } = param
    const sums = [] as any
    columns.forEach((column: any, index: number) => {
      switch (index) {
        case 0:
          sums[index] = '合计'
          break
        case 3:
          sums[index] = state.data.drivingDistanceStr
          break
        case 4:
          sums[index] = state.data.drivingTimeStr
          break
        case 5:
          sums[index] = state.data.totalTollDistanceStr
          break
        case 6:
          sums[index] = state.data.highwayFeeStr
          break
        case 7:
          sums[index] = state.data.oilFeeStr
          break
        case 8:
          sums[index] = state.data.totalFeeStr
          break
        default:
          break
      }
    })
    return sums
  }
  const shareDialogFormVisible = ref(false)
  const shareRouteLine = () => {
    shareDialogFormVisible.value = true
  }
  const shareConfirm = () => {
    state.shareForm.endInfo = state.data.endInfo
    state.shareForm.startInfo = state.data.startInfo
    state.shareForm.tracks = state.data.tracks
    state.shareForm.waypoints = state.data.waypoints?.map((item: any) => item.addressInfo)
    state.shareForm.vinLoadList = state.data.vinLoadList
    state.shareForm.shipmentNo = state.shipmentNo
    mainLineShare(state.shareForm).then((res) => {
      ElMessage({
        message: '分享成功',
        type: 'success',
      })
      shareDialogFormVisible.value = false
    })
  }
  defineExpose({
    dialogVisible,
    state,
    initMiniMap,
    drawLine,
    loading,
  })
</script>

<template>
  <div>
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" width="80%" :draggable="true" :close-on-click-modal="false">
      <div v-loading="loading" element-loading-text="加载中...">
        <!-- 地图 -->
        <div id="container" class="pos-relative" ref="mapSearch"></div>
        <div class="pos-absolute right-50px top-80px z-999" v-if="state.data">
          <el-card style="max-width: 480px">
            <template #header>
              <div class="card-header">
                <span>装车顺序</span>
              </div>
            </template>
            <el-scrollbar max-height="35vh" v-if="state.data.vinLoadList && state.data.vinLoadList.length > 0">
              <p v-for="i in state.data.vinLoadList" :key="i">{{ i }}</p>
            </el-scrollbar>
            <div class="text-center">
              <el-button type="primary" @click="shareRouteLine">路线分享</el-button>
            </div>
          </el-card>
        </div>
        <!--				<div id="bottomTable" ref="topHeight">-->
        <!--					&lt;!&ndash;table表格开始&ndash;&gt;-->
        <!--					<el-table-->
        <!--						:data="state.data.itemList"-->
        <!--						:max-height="detailsTableHeight"-->
        <!--						:summary-method="getSummaries"-->
        <!--						border-->
        <!--						class="tableBox"-->
        <!--						fit-->
        <!--						highlight-current-row-->
        <!--						show-summary-->
        <!--						size="small"-->
        <!--						style="width: 100%"-->
        <!--					>-->
        <!--						<el-table-column align="center" label="路段" type="index" width="60">-->
        <!--							<template #default="scope">-->
        <!--								<span>{{ '第' + (scope.$index + 1) + '段' }}</span>-->
        <!--							</template>-->
        <!--						</el-table-column>-->
        <!--						<el-table-column :show-overflow-tooltip="true" align="center" label="起点" width="140"-->
        <!--						                 prop="start"></el-table-column>-->
        <!--						<el-table-column :show-overflow-tooltip="true" align="center" label="终点" width="140"-->
        <!--						                 prop="end"></el-table-column>-->
        <!--						<el-table-column :show-overflow-tooltip="true" align="center" label="行驶距离(km)" width="120"-->
        <!--						                 prop="distanceStr"></el-table-column>-->
        <!--						<el-table-column :show-overflow-tooltip="true" align="center" label="时间" width="120"-->
        <!--						                 prop="durationStr"></el-table-column>-->
        <!--						<el-table-column :show-overflow-tooltip="true" align="center" label="高速距离(km)" width="120"-->
        <!--						                 prop="highwayDistanceStr"></el-table-column>-->
        <!--						<el-table-column :show-overflow-tooltip="true" align="center" label="高速费(元)"-->
        <!--						                 prop="highwayFeeStr"></el-table-column>-->
        <!--						<el-table-column :show-overflow-tooltip="true" align="center" label="油费(元)"-->
        <!--						                 prop="oilFeeStr"></el-table-column>-->
        <!--						<el-table-column :show-overflow-tooltip="true" align="center" label="总费用(元)"-->
        <!--						                 prop="totalFeeStr"></el-table-column>-->
        <!--					</el-table>-->
        <!--				</div>-->
      </div>
    </el-dialog>
    <!--分享-->
    <el-dialog :draggable="true" v-model="shareDialogFormVisible" title="路线分享" width="500">
      <el-form :model="state.shareForm">
        <el-form-item label="">
          <el-checkbox-group v-model="state.shareForm.shareTargetList">
            <el-checkbox label="小程序" :value="1" />
            <el-checkbox label="app" :value="2" />
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="shareDialogFormVisible = false">取消</el-button>
          <el-button type="primary" @click="shareConfirm">分享</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
  #container {
    position: relative;
    width: 100%;
    height: 60vh;
  }

  :deep(.el-card__header) {
    padding: 6px 10px !important;
  }

  :deep(.el-card__body) {
    padding: 6px 10px !important;
  }
</style>
