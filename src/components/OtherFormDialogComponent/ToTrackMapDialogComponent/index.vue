/* * @Author: llm * @Date: 2024-05-13 17:57:52 * @LastEditors: llm * @LastEditTime: 2024-05-27 11:16:50 * @Description: 查看轨迹弹窗 */
<script setup lang="ts">
  import { ref } from 'vue'
  import { loadBaiDuDrawMap } from '@/utils/bmpgl_lib'
  import green_xian from '@/assets/green_xian.png'
  import blue_xian from '@/assets/blue_xian.png'
  import orange_xian from '@/assets/orange_xian.png'
  import red_xian from '@/assets/red_xian.png'
  import pink_xian from '@/assets/pink_xian.png'
  import { PointProps, ShareFormVO } from '@/api/InTransitManagement/types'
  import { mainLineShare } from '@/api/InTransitManagement'
  import { bd_encrypt } from '@/utils'

  const loading = ref(true)
  const dialogVisible = reactive<DialogOption>({
    visible: false,
    title: '查看轨迹',
  })
  const state = reactive({
    data: {} as any,
    miniMap: null as any,
    mapCenter: [116.397451, 39.909187], // 默认位置
    innerMapMarkerList: [] as any[],
    shipmentNo: '',
  })
  const mapObj = ref()
  const mapSearch = ref(null)
  const detailsTableHeight = ref('300px')
  // 初始化地图
  const initMiniMap = async () => {
    return new Promise((resolve, reject) => {
      loadBaiDuDrawMap().then((_BMapGL) => {
        let BMapGL = null
        // 创建地图实例
        if (_BMapGL.BMapGL) {
          BMapGL = _BMapGL.BMapGL
        } else {
          BMapGL = _BMapGL
        }
        // 创建地图实例
        mapObj.value = new BMapGL.Map(mapSearch.value)
        // 添加比例尺控件
        mapObj.value.addControl(
          new BMapGL.ScaleControl({
            anchor: BMAP_ANCHOR_BOTTOM_LEFT,
            offset: new BMapGL.Size(10, 10),
          }),
        )
        // 添加缩放控件
        mapObj.value.addControl(
          new BMapGL.ZoomControl({
            anchor: BMAP_ANCHOR_BOTTOM_RIGHT,
            offset: new BMapGL.Size(10, 10),
          }),
        )
        // 保存地图
        state.miniMap = mapObj.value
        //清除地图上所有覆盖物
        state.miniMap!.clearOverlays()
        //默认
        const point = new BMapGL.Point(Number(state.mapCenter[0]), Number(state.mapCenter[1]))
        // 初始化地图，设置中心点坐标和地图级别
        state.miniMap.centerAndZoom(point, 5)
        //开启鼠标滚轮缩放
        state.miniMap.enableScrollWheelZoom(true)
        resolve(state.miniMap)
      })
    })
  }
  interface PointVO {
    adcode: any
    address: string
    content: string
    coordinate: string
    eventType: number
    hddCode: any
    imageUrl: any
    name: string
  }
  function addMarker(location: any[], type: string, index?: number, eventType?: number, item?: PointVO) {
    let icon = ''
    if (type == 'distribution') {
      switch (eventType) {
        case 2: //超速
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/chao_icon.png'
          break
        case 3: //正常休息
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/xiuxi_icon.png'
          break
        case 4: //睡觉
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/shuijiao_icon.png'
          break
        case 5: //急刹车
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/ji_icon.png'
          break
        case 6: //急加速
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/ji_icon.png'
          break
        case 7: //急转弯
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/ji_icon.png'
          break
        case 8: //疲劳驾驶
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/pilao_icon.png'
          break
        case 9: //短暂停留
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/tingche_icon.png'
          break
        case 10: //结束导航
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/dingwei_zhongdian.png'
          break
        case 11: //偏航
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/pianli_icon.png'
          break
        case 12: //开始导航点
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/dingwei_qidian.png'
          break
        case 13: //进入高速
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/shirugaosu_icon.png'
          break
        case 14: //离开高速
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/shichugaosu_icon.png'
          break
        case 15: //离开高速
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/changchangtingche_icon.png'
          break
        case 16: //继续导航
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/qita_icon.png'
          break
        case 17: //刷新路线
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/qita_icon.png'
          break
        case 18: //动态规划
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/qita_icon.png'
          break
        case 19: //调整策略路线
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/qita_icon.png'
          break
        case 20: // 收费站
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/feestation_icon.png'
          break
        case 21: // 离线
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/phone_offline.png'
          break
        case 22: // 在线
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/phone_online.png'
          break
        case 30: // 装车
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/zhuang_icon.png'
          break
        case 31: // 卸车
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/xie_icon.png'
          break
        default:
          icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/qita_icon.png'
          break
      }
    }
    //高德转百度
    const bd_point = bd_encrypt(location[0], location[1])
    const point = new BMapGL.Point(Number(bd_point.bd_lng), Number(bd_point.bd_lat))
    const myIcon = new BMapGL.Icon(icon, new BMapGL.Size(36, 38))
    const marker = new BMapGL.Marker(point, { icon: myIcon }) // 创建标注
    // 信息窗口内容----点击图标打开信息窗口
    let opts = {
      width: 255, // 信息窗口宽度
      minHeight: 70, // 信息窗口高度
      title: item!.name,
    }
    let content = ` <div style="padding:10px">
                        <div>
                          <span style="color:rgb(63, 145, 107)">时长:</span>${item!.content}
                        </div>
                        <div><span style="color:rgb(63, 145, 107)">发生位置:</span>${item!.address ? item!.address : '无'}</div>
                      </div>`
    let infoWindow = new BMapGL.InfoWindow(content, opts) // 创建信息窗口对象
    marker.addEventListener('click', (e: any) => {
      state.miniMap.openInfoWindow(infoWindow, point) //开启信息窗口
    })
    state.miniMap.addOverlay(marker) //添加标注
  }
  //清除地图上所有覆盖物
  const clearMapOverlays = () => {
    state.miniMap.clearOverlays()
  }
  //画轨迹
  const drawLine = async (data: any, index: number) => {
    if (state.miniMap) {
      //添加途径点
      data.pointList?.forEach((item: any, index: number) => {
        const distribution_bd_point = bd_encrypt(item.coordinate.split(',')[0], item.coordinate.split(',')[1])
        const distribution = [distribution_bd_point.bd_lng, distribution_bd_point.bd_lat]
        addMarker(distribution, 'distribution', index + 1, item.eventType, item)
      })
      // //添加行驶轨迹
      let tracks = [] as any[]
      if (!data.track) {
        ElMessage.error('暂无轨迹信息')
        return
      }
      let trackList = data.track?.split(';')
      trackList?.map((item: any) => {
        const track = item.split(',')
        const track_bd_point = bd_encrypt(track[0], track[1])
        tracks.push(new BMapGL.Point(Number(track_bd_point.bd_lng), Number(track_bd_point.bd_lat)))
      })
      let lineUrl = ''
      switch (index) {
        case 0:
          lineUrl = green_xian
          break
        case 1:
          lineUrl = blue_xian
          break
        case 2:
          lineUrl = pink_xian
          break
        case 3:
          lineUrl = orange_xian
          break
        case 4:
          lineUrl = red_xian
          break
          break
        default:
          lineUrl = green_xian
          break
      }
      const drivingPath = new BMapGL.Polyline(tracks, {
        enableEditing: false, //是否启用线编辑，默认为false
        // enableClicking: true,//是否响应点击事件，默认为true
        strokeWeight: 8, //折线的宽度，以像素为单位
        strokeOpacity: 1, //折线的透明度，取值范围0
        strokeTexture: {
          // width/height 需要是2的n次方
          url: lineUrl,
          width: 16,
          height: 64,
        },
      })
      state.miniMap.addOverlay(drivingPath)
      //缩放到合适的视野级别
      state.miniMap.setViewport(tracks)
    }
  }

  const shareDialogFormVisible = ref(false)
  const shareRouteLine = () => {
    shareDialogFormVisible.value = true
  }
  defineExpose({
    dialogVisible,
    state,
    initMiniMap,
    drawLine,
    loading,
    clearMapOverlays,
  })
</script>

<template>
  <div>
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" width="80%" :draggable="true" :close-on-click-modal="false">
      <div v-loading="loading" element-loading-text="加载中...">
        <!-- 地图 -->
        <div id="container" class="pos-relative" ref="mapSearch"></div>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
  #container {
    position: relative;
    width: 100%;
    height: 60vh;
  }

  :deep(.el-card__header) {
    padding: 6px 10px !important;
  }

  :deep(.el-card__body) {
    padding: 6px 10px !important;
  }
</style>
