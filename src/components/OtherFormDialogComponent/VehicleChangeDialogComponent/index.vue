<!--
 * @Author: llm
 * @Date: 2023-07-14 10:39:43
 * @LastEditors: llm
 * @LastEditTime: 2024-10-21 15:29:21
 * @Description: 换车
 *
-->
<template>
  <el-dialog v-model="dialogVisible.visible" title="车辆变更" width="50%" :draggable="true" :show-close="false">
    <el-scrollbar max-height="60vh" class="formClass">
      <el-card class="box-card">
        <el-form ref="formRef" :model="formData" :inline="true" label-width="100px">
          <div v-for="(v, i) in formData.body" :key="i">
            <div>
              <el-form-item label="选择换车位置" prop="addressType">
                <el-radio-group v-model="v.addressType" @change="selectAddressType">
                  <el-radio :label="1">自选位置</el-radio>
                  <el-radio :label="2">仓库</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div>
              <el-form-item
                label="请输入位置"
                prop="warehousePlace"
                v-if="v.addressType === 1"
                :rules="[{ required: true, message: '请输入位置', trigger: 'change' }]"
              >
                <el-input
                  v-model="v.warehousePlace"
                  type="text"
                  clearable
                  :readonly="true"
                  placeholder="请输入仓库地址"
                  @click.native.stop="loctionAdress(i)"
                />
              </el-form-item>
              <el-form-item
                label="请选择仓库"
                prop="warehouseId"
                v-else-if="v.addressType === 2"
                :rules="[{ required: true, message: '请选择仓库', trigger: 'change' }]"
              >
                <el-select v-model="v.warehouseId" style="width: 200px" filterable clearable placeholder="模糊搜索">
                  <el-option v-for="item in JSON.parse(JSON.stringify(carriedWarehouseList))" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="待换车牌号" prop="vehicleId" :rules="[{ required: true, message: '请选择车牌号', trigger: 'change' }]">
                <el-select v-model="formData.origin!.vehicleId" style="width: 200px" filterable placeholder="模糊搜索" @change="selectApplyVehicle">
                  <el-option
                    v-for="item in JSON.parse(JSON.stringify(carriedVehicleList))"
                    :key="item.vehicleId"
                    :label="item.vehicleNo"
                    :value="item.vehicleId"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="目标车牌号" prop="vehicleId" :rules="[{ required: true, message: '请选择车牌号', trigger: 'change' }]">
                <el-select v-model="v.vehicleId" style="width: 200px" filterable clearable placeholder="模糊搜索">
                  <el-option
                    v-for="item in JSON.parse(JSON.stringify(carriedVehicleList))"
                    :key="item.vehicleId"
                    :label="item.vehicleNo"
                    :value="item.vehicleId"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="待换司机" prop="driverId" :rules="[{ required: true, message: '请选择司机', trigger: 'change' }]">
                <el-select
                  v-model="formData.origin!.driverId"
                  style="width: 200px"
                  filterable
                  clearable
                  placeholder="模糊搜索"
                  :remote-method="remoteDriverMethod"
                  :loading="driverListLoading"
                >
                  <el-option v-for="item in JSON.parse(JSON.stringify(carriedDriverList))" :key="item.id" :label="item.realName" :value="item.driverId" />
                </el-select>
              </el-form-item>
              <el-form-item label="目标司机" prop="driverId" :rules="[{ required: true, message: '请选择司机', trigger: 'change' }]">
                <el-select
                  v-model="v.driverId"
                  style="width: 200px"
                  filterable
                  clearable
                  placeholder="模糊搜索"
                  :remote-method="remoteDriverMethod"
                  :loading="driverListLoading"
                >
                  <el-option v-for="item in JSON.parse(JSON.stringify(carriedDriverList))" :key="item.id" :label="item.realName" :value="item.driverId" />
                </el-select>
              </el-form-item>
            </div>
            <div v-if="shipmentList.length > 0">
              <el-form-item label="待换任务" prop="shipmentNo" :rules="[{ required: true, message: '请选择待换任务', trigger: 'change' }]">
                <el-select v-model="currentShipmentId" style="width: 200px" filterable clearable placeholder="模糊搜索" @change="changeShipment">
                  <el-option v-for="item in JSON.parse(JSON.stringify(shipmentList))" :key="item.shipmentNo" :label="item.taskName" :value="item.shipmentNo" />
                </el-select>
              </el-form-item>
            </div>
            <el-form-item label="VIN" prop="vin" v-if="taskVinList">
              <el-input disabled v-model="taskVinList" type="textarea" autosize></el-input>
            </el-form-item>
          </div>
        </el-form>
      </el-card>
    </el-scrollbar>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" v-loading="submitLoading" @click="handleSubmit()">确 定</el-button>
        <el-button @click="closeDialog()">取 消</el-button>
      </div>
    </template>
    <map-search-dialog
      v-if="mapDialog.visible"
      :dialog="mapDialog"
      :pointData="mapData"
      @closeMapDialog="closeMapDialog"
      @submitLocation="submitLocation"
    ></map-search-dialog>
  </el-dialog>
</template>
<script setup lang="ts">
  import MapSearchDialog from '@/components/MapSearchDialog/index.vue'
  import { DriverVO, VehicleVO, WarehouseVO } from '@/types/global'
  import { ExchangeVehicleParamsVo, ReplaceCarrierParamsVO, VinInfoVO } from '@/api/planManagement/type'
  import {
    carrierVehiclesApi,
    carrierDriversApi,
    carrierWarehouseApi,
    vehicleVinList,
    transferVehicle,
    getShipmentNoByVehicleId,
    carrierDriverSelect,
  } from '@/api/planManagement/index'
  import { carrierListApi, carrierVehicleSelect } from '@/api/InTransitManagement'
  const emit = defineEmits([
    'closeDialog', //关闭弹窗
    'exchangeVehicleResult', //换车结果
  ])
  const tabActive = ref(0)
  const handleClick = (e: any) => {}
  const formRef = ref()
  const props = defineProps({
    /**
     * 弹窗属性
     */
    dialogVisible: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
    vehicleChangeParams: {
      require: true,
      type: Object as PropType<any>,
      default: () => {
        return {}
      },
    },
  })
  watch(
    () => props.dialogVisible.visible,
    (visible) => {
      if (visible) {
        remoteVehicleMethod('')
        remoteDriverMethod('')
        remoteWarehouseMethod('')
        shipmentList.value = []
        taskVinList.value = ''
      } else {
        emit('closeDialog')
      }
    },
    {
      deep: true,
    },
  )
  watch(
    () => tabActive.value,
    (val) => {
      if (val === 1) {
        getCarrierList()
      }
    },
    { deep: true },
  )
  const mapData = ref({})
  //车辆列表加载loading
  const vehicleListLoading = ref<boolean>(false)
  //司机列表加载loading
  const driverListLoading = ref<boolean>(false)
  //仓库列表加载loading
  const warehouseListLoading = ref<boolean>(false)
  //承运商列表
  const carrierList = ref<any>()
  //承运商车辆列表
  const carriedVehicleList = ref<VehicleVO[]>([])
  //承运商司机列表
  const carriedDriverList = ref<DriverVO[]>([])
  //承运商仓库列表
  const carriedWarehouseList = ref<WarehouseVO[]>([])
  const replaceCarriedVehicleList = ref<VehicleVO[]>([])
  const replaceCarriedDriverList = ref<DriverVO[]>([])
  const replaceCarrierList = ref<any>()
  interface ShipmentVinVO {
    shipmentNo: string
    vinItemList: VinInfoVO[]
    lineName: string
    carrierName: string
    carrierId: string
  }
  //运单列表及运单下的所有vin
  const shipmentList = ref<ShipmentVinVO[]>([])
  //当前选中的运单号
  const currentShipmentId = ref<string>('')
  const carrierName = ref<string>('')
  const formData = ref<ExchangeVehicleParamsVo>({
    body: [
      {
        addressType: 1,
      },
    ],
    origin: {
      vehicleId: '',
      driverId: '',
      idList: [],
    },
    operationType: 2, //操作类型1-倒板 2-换车
    source: 0, //申请来源：0-调度 1-司机
    shipmentNo: '', //运单号
  })
  const formData1 = ref<ReplaceCarrierParamsVO>({
    idList: [],
    vehicleId: '',
    carrierId: '',
    shipmentNo: '', //运单号
    replaceVehicleId: '',
    replaceDriverId: '',
    replaceCarrierId: '',
  })
  const formData1VinList = ref<VinInfoVO[]>([])
  const routeLineName = ref<string>('')
  const mapDialog = reactive<DialogOption>({
    visible: false,
  })
  //定义当选选中项下标
  const currentIndex = ref(0)
  /**
   * 选择位置
   * @param index 选中项下标
   */
  const loctionAdress = async (index: number) => {
    currentIndex.value = index
    mapDialog.visible = true //开启弹窗
    mapDialog.title = '选择位置'
    mapData.value = {
      address: formData.value.body![index].warehousePlace,
      coordinate: formData.value.body![index].coordinate,
    }
  }
  // 关闭子弹窗
  function closeMapDialog() {
    mapDialog.visible = false //关闭弹窗
  }
  /**
   * 获取承运商列表
   */
  const getCarrierList = async () => {
    carrierListApi().then((res) => {
      replaceCarrierList.value = res.data
    })
  }
  // 子弹窗确认
  const submitLocation = (data: any) => {
    if (data) {
      formData.value.body![currentIndex.value].warehousePlace = data.address
      formData.value.body![currentIndex.value].coordinate = data.coordinate
      closeMapDialog()
    }
  }
  //切换换车地
  const selectAddressType = (e: any) => {
    //循环 formData.value.body，如果e===1 初始化formData.value.body 中所有项中的warehousePlace='',如果e===2 初始化formData.value.body warehouseId 和coordinate=''
    formData.value.body!.forEach((item: any) => {
      if (e === 1) {
        item.warehousePlace = ''
      }
      if (e === 2) {
        item.warehouseId = ''
        item.coordinate = ''
      }
    })
  }
  /**
   * 获取板车上所有vin
   */
  const getVehicleVinList = (vehicleId: string) => {
    vehicleVinList({ vehicleId }).then((res) => {
      formData.value.origin!.idList = res.data.map((item: any) => item.id)
    })
  }
  //选择承运商
  const changeCarrier = (e: string) => {
    formData1.value.replaceDriverId = ''
    formData1.value.replaceVehicleId = ''
    getVehicleByCarrierId(e)
    getDriverByCarrierId(e)
  }
  /**
   * 获取车辆下的所有运单号
   */
  const getShipmentNoByVehicleIdFun = (vehicleId: string) => {
    const params = {
      vehicleId,
      transportType: props.vehicleChangeParams.transportType,
      shipmentType: props.vehicleChangeParams.shipmentType,
    }
    getShipmentNoByVehicleId(params).then((res) => {
      currentShipmentId.value = ''
      if (res.data.length > 0) {
        shipmentList.value = res.data as ShipmentVinVO[]
      } else {
        shipmentList.value = []
        ElMessage.warning('当前车辆没有绑定运单号')
      }
      // formData.value.origin!.idList = res.data.map((item: any) => item.id);
    })
  }
  /**
   * 选择申请的车牌号，并查询所绑定的运单号
   */
  const selectApplyVehicle = (vehicleId: string) => {
    getShipmentNoByVehicleIdFun(vehicleId)
    shipmentList.value = []
    taskVinList.value = ''
    // if (tabActive.value === 1) {
    //   formData1.value.shipmentNo = '';
    //   formData1.value.idList = [];
    //   formData1VinList.value = [];
    //   routeLineName.value = '';
    // }
  }
  const taskVinList = ref<string>()
  //选择运单号，并获取下面绑定的vin
  const changeShipment = (shipmentNo: string) => {
    const currentShipmentInfo = shipmentList.value.find((item: any) => item.shipmentNo === shipmentNo)
    const vinItemList = currentShipmentInfo!.vinItemList || []
    if (tabActive.value === 0) {
      //找出shipmentList.value中item.shipmentId = shipmentId对应的vinItemList
      formData.value.shipmentNo = shipmentNo
      formData.value.origin!.idList = vinItemList.map((item: any) => item.id)
      taskVinList.value = vinItemList.map((item) => item.vin).join('\n')
    } else if (tabActive.value === 1) {
      routeLineName.value = currentShipmentInfo!.lineName || ''
      carrierName.value = currentShipmentInfo!.carrierName || ''
      formData1.value.carrierId = currentShipmentInfo!.carrierId || ''
      formData1.value.shipmentNo = shipmentNo
      formData1.value.idList = vinItemList.map((item: any) => item.id)
      formData1VinList.value = vinItemList
    }
  }
  /**
   * 数据
   */
  const data = ref<any>()
  /**
   * 提交加载动画
   */
  const submitLoading = ref(false)
  /**
   * 选择承运商弹窗属性
   */
  const dialogFormVisible = reactive<DialogOption>({
    visible: false,
  })
  /**
   * 选中的vin
   */
  const selectVinsArr = ref<any[]>([])
  /**
   * 当前选中data数据中的某项下标
   */
  const currentDataIndex = ref<number>(0)
  /**
   * 提交
   */
  const handleSubmit = () => {
    if (tabActive.value === 0) {
      emit('exchangeVehicleResult', formData.value, tabActive.value)
    } else if (tabActive.value === 1) {
      emit('exchangeVehicleResult', formData1.value, tabActive.value)
    }
  }
  /**
   * 关闭弹窗
   */
  const closeDialog = () => {
    shipmentList.value = []
    taskVinList.value = ''
    if (tabActive.value === 0) {
      formData.value = {
        body: [
          {
            addressType: 1,
          },
        ],
        origin: {
          vehicleId: '',
          driverId: '',
          idList: [],
        },
        operationType: 2, //操作类型1-倒板 2-换车
        source: 0, //申请来源：0-调度 1-司机
        shipmentNo: '',
      }
      currentShipmentId.value = ''
    } else if (tabActive.value === 1) {
      formData1.value = {
        idList: [],
        vehicleId: '',
        carrierId: '',
        shipmentNo: '', //运单号
        replaceVehicleId: '',
        replaceDriverId: '',
        replaceCarrierId: '',
      }
    }
    tabActive.value = 0
    emit('closeDialog')
  }
  const exchangeVehicleParams = ref({
    transportType: '',
    shipmentType: '',
  })
  //远程获取承运商车辆列表
  const remoteVehicleMethod = (query: string) => {
    vehicleListLoading.value = true
    carrierVehiclesApi({ vehicleNo: query }).then((res) => {
      carriedVehicleList.value = res.data as VehicleVO[]
    })
    vehicleListLoading.value = false
  }
  //远程获取承运商仓库列表
  const remoteDriverMethod = (query: string) => {
    // if (query) {
    driverListLoading.value = true
    carrierDriversApi({ name: query }).then((res) => {
      carriedDriverList.value = res.data as DriverVO[]
    })
    // } else {
    //   // carriedDriverList.value = [];
    // }
    driverListLoading.value = false
  }
  //远程获取承运商仓库列表
  const remoteWarehouseMethod = (query: string) => {
    // if (query) {
    warehouseListLoading.value = true
    carrierWarehouseApi({ name: query }).then((res) => {
      carriedWarehouseList.value = res.data as WarehouseVO[]
    })
    // } else {
    //   // carriedWarehouseList.value = [];
    // }
    warehouseListLoading.value = false
  }
  //根据承运商获取司机
  const getDriverByCarrierId = (carrierId: string) => {
    carrierDriverSelect(carrierId).then((res) => {
      replaceCarriedDriverList.value = res.data as DriverVO[]
    })
  }
  //根据承运商获取车辆
  const getVehicleByCarrierId = (carrierId: string) => {
    carrierVehicleSelect(carrierId).then((res) => {
      replaceCarriedVehicleList.value = res.data as VehicleVO[]
    })
  }
  /**
   * 列表配置项
   */
  const tableConfig = reactive<TableConfig>({
    showHandleSelection: true,
    showSort: true,
    tableItem: [
      {
        name: 'vin',
        label: 'VIN',
        align: 'left',
        type: 'text',
        listEnable: true,
      },
      {
        name: 'applyFlag',
        label: '是否配板',
        align: 'center',
        type: 'text',
        listEnable: true,
      },
      {
        name: 'takeCarFactoryName',
        label: '提车工厂',
        align: 'center',
        type: 'tree',
        listEnable: true,
      },
      {
        name: 'targetCenterCity',
        label: '交付地',
        align: 'center',
        type: 'tree',
        listEnable: true,
      },
    ],
  })
  defineExpose({ data, formData, remoteVehicleMethod, shipmentList, taskVinList })
</script>
<style lang="scss" scoped>
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .box-card {
    margin-bottom: 20px;
  }
</style>
