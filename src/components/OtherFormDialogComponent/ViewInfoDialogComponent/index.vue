<!--
 * @Author: llm
 * @Date: 2024-09-24 16:36:04
 * @LastEditors: 周宗文
 * @LastEditTime: 2024-10-24 17:34:56
 * @Description: 方案对比弹窗
-->
<template>
  <div>
    <el-dialog v-model="dialogVisible.visible" :title="dialogVisible.title" width="50%" :draggable="true" :close-on-click-modal="false" @close="closeDialog">
      <el-scrollbar max-height="60vh" class="formClass">
        <el-form :model="state.currentItem" label-width="auto">
          <el-form-item label="失败原因：">
            <el-input v-model="state.currentItem.failReason" disabled type="textarea" />
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <!-- <div class="dialog-footer">
          <el-button @click="closeDialog()">关 闭</el-button>
        </div> -->
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  const state = reactive({
    currentItem: {
      failReason: '',
    }, //当前行的信息
  })
  /**
   * 查看列表项详情弹窗
   */
  const dialogVisible = reactive<DialogOption>({
    visible: false,
    title: '详情',
  })

  /**
   * 关闭弹窗
   */
  function closeDialog() {
    dialogVisible.visible = false
  }

  defineExpose({
    dialogVisible,
    state,
  })
</script>
<style lang="scss" scoped></style>
