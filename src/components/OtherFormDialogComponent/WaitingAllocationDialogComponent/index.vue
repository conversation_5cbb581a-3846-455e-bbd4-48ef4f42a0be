<!--
 * @Author: llm
 * @Date: 2024-04-26 15:10:04
 * @LastEditors: llm
 * @LastEditTime: 2024-09-02 10:26:27
 * @Description: 待配板划分弹窗
-->
<template>
  <div>
    <el-dialog
      v-model="waitingProjectDialogVisible.visible"
      :title="waitingProjectDialogVisible.title"
      width="50%"
      :draggable="true"
      :close-on-click-modal="false"
      @closed="closeDialog"
    >
      <el-scrollbar max-height="60vh">
        <el-form ref="formRef" :model="state.formData" label-width="120px" :inline="true" :rules="rules">
          <el-form-item label="绿色">
            <div class="flex justify-start items-center">
              <el-input v-model="state.formData.warningDayOne" placeholder="单位/天"></el-input>
              <el-tag class="ml-2 mr-2" type="info" size="large"><</el-tag>
              <el-tag type="info" size="large">待配板滞留天数</el-tag>
              <el-tag class="ml-2 mr-2" type="info" size="large">≤</el-tag>
              <el-input v-model="state.formData.warningDayTwo" disabled placeholder="单位/天"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="黄色">
            <div class="flex justify-start items-center">
              <el-input v-model="state.formData.warningDayTwo" placeholder="单位/天"></el-input>
              <el-tag class="ml-2 mr-2" type="info" size="large"><</el-tag>
              <el-tag type="info" size="large">待配板滞留天数</el-tag>
              <el-tag class="ml-2 mr-2" type="info" size="large">≤</el-tag>
              <el-input v-model="state.formData.warningDayThree" disabled placeholder="单位/天"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="橙色">
            <div class="flex justify-start items-center">
              <el-input v-model="state.formData.warningDayThree" placeholder="单位/天"></el-input>
              <el-tag class="ml-2 mr-2" type="info" size="large"><</el-tag>
              <el-tag type="info" size="large">待配板滞留天数</el-tag>
              <el-tag class="ml-2 mr-2" type="info" size="large">≤</el-tag>
              <el-input v-model="state.formData.warningDayFour" disabled placeholder="单位/天"></el-input>
            </div>
          </el-form-item>
          <el-form-item label="红色">
            <div class="flex justify-start items-center">
              <el-tag type="info" size="large">待配板滞留天数</el-tag>
              <el-tag class="ml-2 mr-2" type="info" size="large">></el-tag>
              <el-input v-model="state.formData.warningDayFour" placeholder="单位/天"></el-input>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog()">取 消</el-button>
          <el-button type="primary" v-loading="submitLoading" @click="handleSubmit(formRef)">保 存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { WarningProjectVO } from '@/api/waitingAllocation/type'
  import { examineListAddApi, examineListUpdateApi, examineListDetailApi, examineTemplateUseTargetSelectOptionsApi } from '@/api/supplierManagement'
  import { FormInstance } from 'element-plus'

  const formRef = ref()
  const rules = reactive({
    examineName: [{ required: true, message: '请输入考核项目名称', trigger: 'blur' }],
    templateNo: [{ required: true, message: '请选择考核模板', trigger: 'change' }],
    useTarget: [{ required: true, message: '请选择考核对象', trigger: 'change' }],
  })

  const waitingProjectDialogVisible = reactive<DialogOption>({
    visible: false,
    title: '待配板划分设置',
  })
  const emit = defineEmits(['resetQuery'])
  const submitLoading = ref(false)
  const state = reactive({
    dateRange: '',
    dateMonth: '',
    formData: {
      id: '',
      warningDayOne: 10,
      warningDayTwo: 11,
      warningDayThree: 12,
      warningDayFour: 13,
    } as WarningProjectVO,
  })

  // 接受父组件传值
  const props = defineProps({
    waitingFormData: {
      type: Object,
      default: {},
    },
  })

  watch(
    () => props.waitingFormData,
    (value) => {
      if (value) {
        Object.assign(state.formData, value)
      }
    },
    { deep: true },
  )

  // const useTargetOptions = ref<OptionType[]>([]);
  const majorOptions = ref<string[]>([])
  // const examineTemplateUseTargetSelectOptions = async () => {
  //   const { data } = await examineTemplateUseTargetSelectOptionsApi();
  //   useTargetOptions.value = data as OptionType[];
  // };

  const handleSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        submitLoading.value = true

        // 求和标准分数的总和不等于100,则不能提交
        let totalAssessMark = 0
        if (totalAssessMark !== 100) {
          ElMessage.error('标准分数总和必须等于100')
          submitLoading.value = false
          return
        }
        try {
          //如果
          if (state.formData.id) {
            await examineListUpdateApi(state.formData)
          } else {
            await examineListAddApi(state.formData)
          }
          submitLoading.value = false
          waitingProjectDialogVisible.visible = false
          ElMessage.success('操作成功')
          closeDialog()
          emit('resetQuery')
        } catch (e) {
          submitLoading.value = false
        }
      } else {
      }
    })
  }

  function resetForm() {
    //清空表单数据
    formRef.value.resetFields()
    state.formData.id = ''
    state.formData.warningDayOne = 10
    state.formData.warningDayTwo = 10
    state.formData.warningDayThree = 10
    state.formData.warningDayFour = 10
  }

  const closeDialog = () => {
    resetForm()
    waitingProjectDialogVisible.visible = false
  }

  defineExpose({
    waitingProjectDialogVisible,
    resetForm,
  })
</script>
<style lang="scss" scoped>
  :deep(.el-divider--horizontal) {
    border-top-color: #e89e42;
  }

  :deep(.el-divider__text) {
    color: #e89e42;
  }
</style>
