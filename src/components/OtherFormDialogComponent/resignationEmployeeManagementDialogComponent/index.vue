<!--
 * @Author: llm
 * @Date: 2025-02-12 12:01:49
 * @LastEditors: llm
 * @LastEditTime: 2025-05-10 18:19:02
 * @Description:
-->
<template>
  <div>
    <el-dialog
      v-model="state.dialogVisible.visible"
      :title="state.dialogVisible.title"
      width="80%"
      :draggable="true"
      :close-on-click-modal="false"
      @close="closeDialog"
      v-loading="JSON.stringify(state.userInfo) === '{}'"
    >
      <el-scrollbar max-height="60vh" class="formClass">
        <el-descriptions title="" :column="5" size="small" border>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item">姓名</div>
            </template>
            {{ state.userInfo.realName }}
          </el-descriptions-item>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item">性别</div>
            </template>
            {{ state.userInfo.gender }}
          </el-descriptions-item>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item">年龄</div>
            </template>
            {{ state.userInfo.age }}
          </el-descriptions-item>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item">岗位</div>
            </template>
            {{ state.userInfo.post }}
          </el-descriptions-item>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item">类型</div>
            </template>
            {{ state.userInfo.staffType }}
          </el-descriptions-item>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item">工龄</div>
            </template>
            {{ state.userInfo.workingAge }}
          </el-descriptions-item>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item">保证金金额</div>
            </template>
            {{ state.userInfo.liquidationEarnestSetAmount }}
          </el-descriptions-item>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item">备用金金额</div>
            </template>
            {{ state.userInfo.liquidationImpressDeductAmount }}
          </el-descriptions-item>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item">现金挂账</div>
            </template>
            {{ state.userInfo.liquidationChargeAmount }}
          </el-descriptions-item>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item">油费挂账</div>
            </template>
            {{ state.userInfo.liquidationOilFeesChargeAmount }}
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions class="mt-10px" title="待处理事项" :column="5" size="small" border>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item" :class="{ 'text-red': state.userInfo.totalDispatchTask > 0 }">调度任务</div>
            </template>
            <el-text :type="state.userInfo.totalDispatchTask > 0 ? 'danger' : ''">{{ state.userInfo.totalDispatchTask }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item" :class="{ 'text-red': state.userInfo.waitingQualityLoss > 0 }">质损处理</div>
            </template>
            <el-text :type="state.userInfo.waitingQualityLoss > 0 ? 'danger' : ''">{{ state.userInfo.waitingQualityLoss }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item" :class="{ 'text-red': state.userInfo.waitingBorrowingMoney > 0 }">借款处理</div>
            </template>
            <el-text :type="state.userInfo.waitingBorrowingMoney > 0 ? 'danger' : ''">{{ state.userInfo.waitingBorrowingMoney }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item" :class="{ 'text-red': state.userInfo.waitingBorrowingOil > 0 }">借油处理</div>
            </template>
            <el-text :type="state.userInfo.waitingBorrowingOil > 0 ? 'danger' : ''">{{ state.userInfo.waitingBorrowingOil }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item :width="100">
            <template #label>
              <div class="cell-item" :class="{ 'text-red': state.userInfo.waitingProcessingTire > 0 }">轮胎处理</div>
            </template>
            <el-text :type="state.userInfo.waitingProcessingTire > 0 ? 'danger' : ''">{{ state.userInfo.waitingProcessingTire }}</el-text>
          </el-descriptions-item>
        </el-descriptions>
        <el-card class="mt-10px" shadow="never">
          <template #header> 物品领用信息 </template>
          <el-table :data="state.userInfo.receiveManagementList" style="width: 100%" border>
            <el-table-column type="index" label="序号" width="60px" />
            <el-table-column prop="itemName" label="物品名称" />
            <el-table-column prop="itemAmount" label="物品金额(元)" />
            <el-table-column prop="needReturn" label="离职是否需要归还">
              <template #default="scope">
                <el-text :type="scope.row.needReturn ? 'success' : 'danger'">{{ scope.row.needReturn ? '是' : '否' }}</el-text>
              </template>
            </el-table-column>
            <el-table-column prop="collectionTime" label="领用时间" />
            <el-table-column prop="recipientOperator" label="领用操作人" />
            <el-table-column prop="returnTime" label="归还时间" />
            <el-table-column prop="returnOperator" label="归还操作人" />
          </el-table>
        </el-card>
        <el-form ref="formRef" label-width="220px" :model="state.formData" :rules="rules" :inline="true" class="mt-10px">
          <el-row :gutter="0">
            <el-col :span="12">
              <el-form-item label="离职日期" prop="resignationDate">
                <el-date-picker
                  v-model="state.formData.resignationDate"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  type="date"
                  placeholder="请选择离职日期"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="离职金额归于工资月度" prop="salaryBelongMonth">
                <el-date-picker
                  v-model="state.formData.salaryBelongMonth"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  type="month"
                  placeholder="请选择薪资归属月份"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="离职原因" prop="resignationReason">
                <el-input placeholder="请填写离职原因" type="textarea" :maxLength="220" v-model="state.formData.resignationReason"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="备注" prop="resignationRemark">
                <el-input placeholder="请填写离职备注" type="textarea" :maxLength="220" v-model="state.formData.resignationRemark"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { postOutFleetAdministrationOutFleetStaffManagementSubmitRecognitionApi } from '@/api/administrativeManagement'
  const emit = defineEmits(['resetQuery'])
  defineOptions({
    name: 'ResignationEmployeeManagementDialogComponent',
    inheritAttrs: false,
  })
  const formRef = ref()
  const state = reactive({
    dialogVisible: {
      visible: false,
      title: '',
    },
    userInfo: {} as UserInfo,
    formData: {
      resignationDate: '',
      resignationReason: '',
      salaryBelongMonth: '',
      resignationRemark: '',
    },
  })
  interface UserInfo {
    id: string
    realName: string
    gender: string
    age: string
    post: string
    staffType: string
    workingAge: string
    liquidationEarnestSetAmount: string
    liquidationChargeAmount: string
    liquidationOilFeesChargeAmount: string
    liquidationImpressDeductAmount: string
    receiveManagementList: any[]
    totalDispatchTask: number //调度任务数
    waitingQualityLoss: number //质损处理数
    waitingBorrowingMoney: number //借款处理数
    waitingBorrowingOil: number //借油处理数
    waitingProcessingTire: number //轮胎处理数
    waitingExpense: number //报销处理数
  }
  const rules = reactive({
    resignationDate: [{ required: true, message: '请选择离职日期', trigger: 'change' }],
    salaryBelongMonth: [{ required: true, message: '请选择离职金额归于工资月度', trigger: 'change' }],
  })
  const closeDialog = () => {
    // 清空表单
    formRef.value.resetFields()
    state.dialogVisible.visible = false
  }
  const submitForm = async () => {
    formRef.value.validate(async (valid: boolean) => {
      if (valid) {
        //如果存在代办则提示有待办事项未处理
        if (
          state.userInfo.totalDispatchTask > 0 ||
          state.userInfo.waitingQualityLoss > 0 ||
          state.userInfo.waitingBorrowingMoney > 0 ||
          state.userInfo.waitingBorrowingOil > 0 ||
          state.userInfo.waitingProcessingTire > 0
        ) {
          ElMessage.warning('有待办事项未处理')
          return
        }
        const params = {
          ...state.formData,
          id: state.userInfo.id,
        }
        const { data } = await postOutFleetAdministrationOutFleetStaffManagementSubmitRecognitionApi(params)
        ElMessage.success('提交成功')
        closeDialog()
        emit('resetQuery')
      }
    })
  }
  const getDetail = (id: string) => {}
  defineExpose({
    state,
    getDetail,
  })
</script>
