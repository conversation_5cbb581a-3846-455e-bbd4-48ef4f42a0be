<!--
 * @Author: llm
 * @Date: 2025-03-06 20:34:05
 * @LastEditors: llm
 * @LastEditTime: 2025-04-30 15:16:14
 * @Description: 解绑轮胎弹窗
-->
<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      v-model="state.dialogVisible.visible"
      :title="state.dialogVisible.title"
      width="60%"
      :draggable="true"
      @closed="closed"
    >
      <el-scrollbar max-height="60vh" class="formClass">
        <div>
          <el-form :model="state.form" :rules="rules" ref="formRef" label-width="120">
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item prop="no" label="轮胎号" label-width="120">
                  <el-input v-model="state.form.tireNo" placeholder="请输入" :disabled="true"> </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="standardMileage" label="轮胎核定行驶里程" label-width="120">
                  <el-input v-model="state.form.standardMileage" placeholder="请输入" :disabled="true">
                    <template #append>km</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item prop="currentVehicle" label="绑定车辆" label-width="120">
                  <el-input v-model="state.form.currentVehicle" placeholder="" :disabled="true"> </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="bindMileage" label="绑定时车辆里程" label-width="120">
                  <el-input v-model="state.form.bindMileage" placeholder="请输入" :disabled="true">
                    <template #append>km</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item prop="driverInfo" label="驾驶员信息" label-width="120">
                  <el-input v-model="state.form.driverInfo" placeholder="请输入" :disabled="true"> </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="unbindMileage" label="解绑时车辆里程" label-width="120">
                  <el-input v-model="state.form.unbindMileage" placeholder="请输入" type="number" :min="0">
                    <template #append>km</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item prop="行驶里程" label="行驶里程" label-width="120">
                  <el-input v-model="driveMileage" placeholder="请输入" :disabled="true">
                    <template #append>km</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="unbindDate" label="解绑时间" label-width="120">
                  <el-date-picker
                    value-format="YYYY-MM-DD"
                    v-model="state.form.unbindDate"
                    :clearable="true"
                    placeholder="请选择解绑时间"
                    type="date"
                    style="width: 100%"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="12">
                <el-form-item prop="driverCost" label="司机奖惩" label-width="120">
                  <div class="flex items-center">
                    <el-input v-model="state.form.driverCost" placeholder="正数保留两位小数" type="number" step="0.01" min="0" class="w-100%">
                      <template #prepend>
                        <el-select v-model="state.form.costType" placeholder="请选择" style="width: 60px">
                          <el-option label="罚款" value="7" />
                          <el-option label="奖励" value="6" />
                        </el-select>
                      </template>
                    </el-input>
                    <!-- <el-tag type="warning" class="ml-10px">正数为罚款，负数为奖励</el-tag> -->
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="unbindBaseId" label="回收基地" label-width="120">
                  <el-select v-model="state.form.unbindBaseId" :clearable="true" placeholder="请选择" :filterable="true">
                    <el-option :label="option.label" :value="option.value" v-for="option in select1Options"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="totalMileage" label="轮胎总行驶里程" label-width="120">
                  <el-input v-model="totalMileage" placeholder="请输入" :readonly="true" :class="{ 'exceeded-mileage': isTotalMileageExceeded }">
                    <template #append>km</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="12">
              <el-col :span="24">
                <el-form-item prop="unBindPics" label="上传里程表图片" label-width="120">
                  <UploadImageComponent ref="uploadImageRef" :limit="1" v-model="state.form.unBindPics" tip="支持扩展名.jpg、.png、.jpeg，可多张上传最多1张" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-scrollbar>
      <template #footer>
        <el-button type="warning" @click="unBindAndScrap(formRef)">解绑并报废</el-button>
        <el-button @click="closed">取消</el-button>
        <el-button type="primary" @click="submitForm(formRef)">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import { getFleetCompanyDepartmentSelectOptionApi } from '@/api/financialManagement'
  import type { FormInstance, UploadUserFile } from 'element-plus'
  import UploadImageComponent from '@/components/UploadImageComponent/index.vue'
  import { postOutFleetUnBindAndScrap, postOutFleetUnBindTire } from '@/api/outboundMaintenanceManagement'
  import { debounce } from 'lodash'
  const uploadImageRef = ref<InstanceType<typeof UploadImageComponent>>()
  const emit = defineEmits(['refresh'])
  const state = reactive({
    dialogVisible: {
      visible: false,
      title: '解绑车辆',
    },
    form: {
      id: '',
      tireNo: '',
      standardMileage: 0,
      currentVehicle: '',
      bindMileage: 0,
      driverInfo: '',
      unbindMileage: '',
      unbindDate: '',
      unbindBaseId: '',
      driverCost: '',
      currentMileage: 0,
      unBindPics: [],
      costType: '6',
    },
  })
  const closed = () => {
    state.dialogVisible.visible = false
    resetForm(formRef.value)
    setUploadImageList([])
  }
  const unBindAndScrap = debounce(async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid: any, fields: any) => {
      if (valid) {
        const params = {
          tireNo: state.form.tireNo,
          id: state.form.id,
          unbindMileage: state.form.unbindMileage,
          unbindDate: state.form.unbindDate,
          unbindBaseId: state.form.unbindBaseId,
          driverCost: state.form.driverCost,
          unBindPics: getUploadImageList(),
          costType: state.form.costType,
        }
        const res = await postOutFleetUnBindAndScrap(params)
        ElMessage.success('解绑并报废成功')
        emit('refresh')
        closed()
      } else {
        console.log('validation failed:', fields)
      }
    })
  }, 300)

  const formRef = ref<FormInstance>()
  const validateUnbindMileage = (rule: any, value: any, callback: any) => {
    if (value < state.form.bindMileage) {
      callback(new Error('解绑时车辆里程不能小于绑定时车辆里程'))
    } else {
      callback()
    }
  }
  const rules = ref({
    unbindMileage: [
      { required: true, message: '请输入解绑时车辆里程', trigger: 'blur' },
      { validator: validateUnbindMileage, trigger: 'blur' },
    ],
    unbindDate: [{ required: true, message: '请选择解绑时间', trigger: 'change' }],
    unbindBaseId: [{ required: true, message: '请选择回收基地', trigger: 'change' }],
    driverCost: [
      {
        required: true,
        message: '请输入司机奖惩金额',
        trigger: 'blur',
      },
      {
        validator: (rule: any, value: string | number, callback: (error?: Error) => void) => {
          if (value) {
            const num = Number(value)
            if (isNaN(num)) {
              callback(new Error('请输入正确的格式'))
            } else if (!/^\d+(\.\d{1,2})?$/.test(String(value))) {
              callback(new Error('请输入正确的格式，最多保留2位小数'))
            } else {
              callback()
            }
          } else {
            callback()
          }
        },
        trigger: 'blur',
      },
    ],
  })

  const driveMileage = computed(() => {
    const unbindMileage = state.form.unbindMileage ? Number(state.form.unbindMileage) : 0
    return unbindMileage - state.form.bindMileage < 0 ? 0 : (unbindMileage - state.form.bindMileage).toFixed(2)
  })
  const totalMileage = computed(() => {
    return (state.form.currentMileage + Number(driveMileage.value)).toFixed(2)
  })
  const isTotalMileageExceeded = computed(() => {
    return Number(totalMileage.value) >= state.form.standardMileage
  })
  watch(
    () => state.dialogVisible.visible,
    async (val) => {
      if (val) {
        const res = await getFleetCompanyDepartmentSelectOptionApi({ brandOffice: true })
        select1Options.value = res.data as SelectOptions[]
      }
    },
  )
  watch(
    () => state.form.unbindMileage,
    (val) => {
      //如果是负数，则赋值为0
      if (Number(val) < 0) {
        state.form.unbindMileage = ''
      }
    },
  )
  const select1Options = ref<SelectOptions[]>([])

  const submitForm = debounce(async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid: any, fields: any) => {
      if (valid) {
        const params = {
          tireNo: state.form.tireNo,
          id: state.form.id,
          unbindMileage: state.form.unbindMileage,
          unbindDate: state.form.unbindDate,
          unbindBaseId: state.form.unbindBaseId,
          driverCost: state.form.driverCost,
          unBindPics: getUploadImageList(),
          costType: state.form.costType,
        }
        const res = await postOutFleetUnBindTire(params)
        ElMessage.success('解绑成功')
        emit('refresh')
        closed()
      } else {
        console.log('validation failed:', fields)
      }
    })
  }, 300)

  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
  }
  // 获取上传图片列表
  const getUploadImageList = () => {
    return uploadImageRef.value!.uploadImageList
  }

  //设置上传图片列表
  const setUploadImageList = (list: UploadUserFile[]) => {
    uploadImageRef.value!.uploadImageList = list
  }
  defineExpose({
    state,
  })
</script>

<style scoped lang="scss">
  .exceeded-mileage :deep(.el-input__wrapper) {
    background-color: #fff;
    box-shadow: 0 0 0 1px #f56c6c inset;
  }

  .exceeded-mileage :deep(.el-input__wrapper) {
    --el-input-text-color: #f56c6c;
  }
</style>
