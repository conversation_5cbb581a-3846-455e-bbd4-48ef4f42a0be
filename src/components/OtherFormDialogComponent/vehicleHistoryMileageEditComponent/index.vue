<template>
  <el-dialog v-model="state.dialogVisible.visible" :title="state.dialogVisible.title" width="500" :before-close="handleClose">
    <el-form ref="ruleFormRef" :rules="rules" style="max-width: 600px" :model="state" status-icon label-width="auto" class="demo-ruleForm">
      <el-form-item label="车辆牌照" prop="vehicleNo">
        <el-select v-model="state.vehicleNo" placeholder="Select" style="width: 100%" :disabled="true">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="车牌颜色" prop="plateColor">
        <el-select v-model="state.plateColor" placeholder="Select" style="width: 100%" :disabled="true">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item
        v-for="(item, index) in state.arrayMonth"
        :key="item.month"
        :prop="`arrayMonth.${index}.mileage`"
        :label="item.month"
        style="display: flex; align-items: center"
      >
        <el-input
          v-model="item.mileage"
          autocomplete="off"
          style="flex: 1"
          type="number"
          :max="item.maxMileage"
          :min="item.minMileage"
          @change="editData(item)"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="resetForm(ruleFormRef)">取消</el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef)"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import type { FormInstance, FormRules } from 'element-plus'
  import { getMonthApi, saveEditDataApi } from '@/api/financialManagement'
  import type { Action } from 'element-plus'
  const emit = defineEmits(['resetQuery', 'subsidyConfirmSubmit'])
  const state = reactive<any>({
    dialogVisible: {
      visible: false,
      title: '新增驾驶员结算单',
    },
    vehicleNo: '',
    plateColor: '',
    page: '',
    arrayMonth: [],
  })

  const ruleFormRef = ref<FormInstance>()
  const ruleForm = reactive({
    pass: '',
    checkPass: '',
    age: '',
  })
  const options = ref<any>([])
  // 校验函数：检查用户输入是否为正整数，还要判断是否在允许的[minMileage, maxMileage]之间
  const validateMileage = (rule: any, value: any, callback: any) => {
    // value可能为空或null
    if (value === '' || value === null || value === undefined) {
      return callback(new Error('请输入里程数'))
    }
    // 利用正则表达式判断是否为正整数
    if (!/^\d+$/.test(value)) {
      return callback(new Error('请输入正整数'))
    }
    // 根据 prop 名称获取当前校验的索引
    // rule.field 的格式为 "arrayMonth.index.mileage"
    const parts = rule.field.split('.')

    const index = Number(parts[1])
    const item = state.arrayMonth[index]
    if (item.maxMileage == null) {
      item.maxMileage = 99999999999
    }

    const intVal = parseInt(value)
    if (intVal < item.minMileage || intVal > item.maxMileage) {
      return callback(new Error(`里程数应在 ${item.minMileage} 至 ${item.maxMileage} 之间`))
      // return callback(new Error('ex：19200'))
    }
    callback() // 校验通过
  }
  // 校验规则
  const rules = {
    vehicleNo: [{ required: true, message: '请选择车辆牌照', trigger: 'change' }],
    // 利用动态属性名称为每个月份的 mileage 添加相同校验规则
    'arrayMonth.0.mileage': [{ validator: validateMileage, trigger: 'blur' }],
    'arrayMonth.1.mileage': [{ validator: validateMileage, trigger: 'blur' }],
    'arrayMonth.2.mileage': [{ validator: validateMileage, trigger: 'blur' }],
    'arrayMonth.3.mileage': [{ validator: validateMileage, trigger: 'blur' }],
    'arrayMonth.4.mileage': [{ validator: validateMileage, trigger: 'blur' }],
    'arrayMonth.5.mileage': [{ validator: validateMileage, trigger: 'blur' }],
  }

  // 获取编辑月份数据
  const getMonth = async (vehicleNo: String) => {
    let { data } = await getMonthApi({
      vehicleNo,
      plateColor: state.plateColor,
    })
    state.arrayMonth = data
  }
  // 编辑日期为正整数并且有最大值和最小值
  const editData = (item: any) => {
    // console.log(item)
  }

  // 确认
  const submitForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate((valid) => {
      if (valid) {
        ElMessageBox.alert('请确认里程', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnPressEscape: false,
          showClose: false,
          closeOnClickModal: false,
          callback: (action: Action) => {
            if (action === 'confirm') {
              let params = {
                vehicleNo: state.vehicleNo,
                plateColor: state.plateColor,
                data: state.arrayMonth,
              }
              saveEditDataApi(params).then((res) => {
                // emit('resetQuery')
                emit('subsidyConfirmSubmit')
              })
            } else {
              return
            }
          },
        })

        state.dialogVisible.visible = false
      } else {
        console.log('error submit!')
      }
    })
  }
  // 重置==取消
  const resetForm = (formEl: FormInstance | undefined) => {
    state.dialogVisible.visible = false
    if (!formEl) return
    formEl.resetFields()
  }

  // 关闭弹框
  const handleClose = (done: () => void) => {
    done()
  }
  defineExpose({
    state,
    getMonth,
  })
</script>
