<!--
 * @Author: llm
 * @Date: 2025-01-25 17:35:16
 * @LastEditors: llm
 * @LastEditTime: 2025-05-28 17:11:39
 * @Description:
-->
<template>
  <div :class="'pagination ' + { hidden: hidden }">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :background="background"
      :layout="layout"
      :page-sizes="pageSizes"
      :total="total"
      :pager-count="pagerCount"
      :small="small"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
  import { computed, PropType } from 'vue'
  import { scrollTo } from '@/utils/scroll-to'

  const props = defineProps({
    total: {
      required: true,
      type: Number as PropType<number>,
      default: 0,
    },
    page: {
      type: Number,
      default: 1,
    },
    limit: {
      type: Number,
      default: 50,
    },
    pageSizes: {
      type: Array as PropType<number[]>,
      default() {
        return [30, 50, 100, 150, 200, 300, 500, 1000]
      },
    },
    small: {
      type: Boolean,
      default: false,
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper',
    },
    background: {
      type: Boolean,
      default: true,
    },
    autoScroll: {
      type: Boolean,
      default: true,
    },
    hidden: {
      type: Boolean,
      default: false,
    },
    pagerCount: {
      type: Number,
      default: 7,
    },
  })

  const emit = defineEmits(['pagination'])

  const currentPage = useVModel(props, 'page', emit)

  const pageSize = useVModel(props, 'limit', emit)

  function handleSizeChange(val: number) {
    currentPage.value = 1
    emit('pagination', { page: 1, limit: val })
    if (props.autoScroll) {
      scrollTo(0, 800)
    }
  }

  function handleCurrentChange(val: number) {
    currentPage.value = val
    emit('pagination', { page: val, limit: props.limit })
    if (props.autoScroll) {
      scrollTo(0, 800)
    }
  }
</script>

<style lang="scss" scoped>
  .pagination {
    padding: 12px;

    &.hidden {
      display: none;
    }
  }
</style>
