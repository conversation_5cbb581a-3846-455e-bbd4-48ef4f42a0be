<!--
 * @Author: llm
 * @Date: 2023-09-21 10:15:25
 * @LastEditors: llm
 * @LastEditTime: 2025-06-13 12:18:13
 * @Description: 查看图片弹窗组件
 *
-->
<template>
  <div>
    <el-dialog :draggable="true" v-model="picDialogVisible" title="查看" width="50%" align-center>
      <el-scrollbar max-height="80vh">
        <el-image
          v-for="(url, index) in imageList"
          :key="index"
          :src="url as string"
          :style="{
            width: imageList.length === 1 ? '100%' : imageList.length === 2 ? '45%' : '30%',
          }"
          fit="contain"
          lazy
          :zoom-rate="1.2"
          :preview-src-list="imageList as any"
        >
          <template #placeholder>
            <div class="image-slot">
              加载中
              <span class="dot">...</span>
            </div>
          </template>
        </el-image>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  const props = defineProps({
    //图片地址列表
    imageList: {
      required: true,
      type: Array,
      default: () => {
        return []
      },
    },
  })
  const picDialogVisible = ref(false)
  defineExpose({
    picDialogVisible,
  })
</script>
