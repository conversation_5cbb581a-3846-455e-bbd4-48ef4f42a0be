<template>
  <div id="PrintView">
    <div class="order-detail">
      <table cellspacing="0" cellpadding="5" border="1" class="cargo-info">
        <thead>
          <tr>
            <!-- <th style="width: 50px">序号</th> -->
            <th v-for="(item, index) in props.printInfo.titleList" :key="index">{{ item }}</th>
          </tr>
        </thead>
        <tbody>
          <tr class="table-content" v-for="(item, index) in props.printInfo.dataList" :key="index">
            <!-- <td style="width: 30px">{{ parseInt(index + 1) }}</td> -->
            <td v-for="(_item, _index) in item" :key="_index" class="word-break" :style="{ width: 'auto' }">
              {{ _item }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { PrintVO } from '@/types/global'

  const props = defineProps({
    printInfo: {
      type: Object as PropType<PrintVO>,
      default: () => {
        return {}
      },
    },
  })
</script>
<style scoped>
  .order-detail {
    page-break-after: always;
    width: 100%;
    margin: 3mm auto 0;
  }

  .order-title {
    font-size: 20px;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    color: #333;
    letter-spacing: 2px;
    margin: 37px 0 42px 0;
    display: flex;
    display: -ms-flexbox;
    justify-content: center;
    -ms-flex-pack: center;
    align-items: center;
    -ms-flex-align: center;
  }

  .logo-url {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #efefef;
    margin-right: 10px;
  }

  .order-sub-title {
    padding: 0;
    margin: 0;
    font-size: 12px;
    font-family: PingFangSC-Regular;
    color: #333;
    padding-bottom: 10px;
    margin-bottom: 20px;
    border-bottom: 2px solid #a7afc1;
  }

  .order-sub-title > li {
    display: inline-block;
    list-style: none;
    width: 25%;
  }

  .table-box {
    display: flex;
    display: -ms-flexbox;
    justify-content: space-between;
    -ms-flex-pack: justify;
  }

  .city-style {
    display: flex;
    display: -ms-flexbox;
    align-items: flex-start;
    -ms-flex-align: flex-start;
  }

  .city-style > div:first-child {
    flex-shrink: 0;
  }

  .city-style > .content {
    width: 80%;
    word-break: break-word;
  }

  .order-info {
    width: 49%;
    font-size: 12px;
    color: #333;
    border-collapse: collapse;
    border-color: #333;
  }

  .order-info .table-main {
    width: 12px;
    background-color: #c9ced9;
  }

  .spacing-letter {
    letter-spacing: 4px;
  }

  .cargo-info {
    margin-top: 40px;
    width: 100%;
    font-size: 12px;
    color: #333;
    border-collapse: collapse;
    border-color: #333;
  }

  .cargo-info-content {
    height: 130px;
    word-break: break-all;
  }

  .pickup {
    width: 20%;
  }

  .customerOrderNo,
  .customerWaybillNo {
    width: 40%;
  }

  th {
    background-color: #c9ced9;
  }

  .total-fee {
    font-size: 16px;
    font-family: PingFangSC-Medium;
    font-weight: bold;
    color: #333;
    line-height: 2;
  }

  .total-row-right {
    font-size: 14px;
    font-family: PingFangSC-Regular;
    color: #333;
    line-height: 1.5;
  }

  .total-row-right span {
    display: inline-block;
    width: 33.3%;
  }

  .table-footer {
    margin-top: 20px;
    width: 100%;
    font-size: 12px;
    color: #333;
  }

  .table-footer td {
    width: 50%;
  }

  .table-footer td span {
    display: inline-block;
    width: 50%;
  }

  .table-content {
    text-align: center;
  }

  .table-row-total {
    font-size: 16px;
    font-family: PingFangSC-Medium;
    font-weight: bold;
    color: #333;
    text-align: center;
    line-height: 1.5;
  }
  .word-break {
    word-break: break-all;
    overflow-wrap: break-word;
  }

  .cargo-info {
    margin-top: 10px;
    width: 100%;
    font-size: 12px;
    color: #333;
    border-collapse: collapse;
    border-color: #333;
  }

  .cargo-info-content {
    height: 130px;
    word-break: break-all;
  }

  .pickup {
    width: 20%;
  }

  .customerOrderNo,
  .customerWaybillNo {
    width: 40%;
  }

  th {
    background-color: #999;
  }

  .total-fee {
    font-size: 16px;
    font-family: PingFangSC-Medium;
    font-weight: bold;
    color: #333;
    line-height: 2;
  }

  .total-row-right {
    font-size: 14px;
    font-family: PingFangSC-Regular;
    color: #333;
    line-height: 1.5;
  }

  .total-row-right span {
    display: inline-block;
    width: 33.3%;
  }

  .table-footer {
    margin-top: 20px;
    width: 100%;
    font-size: 12px;
    color: #333;
  }

  .table-footer td {
    width: 50%;
  }

  .table-footer td span {
    display: inline-block;
    width: 50%;
  }

  .table-content {
    text-align: center;
  }

  .table-row-total {
    font-size: 16px;
    font-family: PingFangSC-Medium;
    font-weight: bold;
    color: #333;
    text-align: center;
    line-height: 1.5;
  }
  .word-break {
    word-break: break-all;
    overflow-wrap: break-word;
  }
  /* 表格边框 */
  table {
    border-collapse: collapse;
    border: 1px solid #666;
  }
  /* 表格内容边框 */
  td,
  th {
    border: 1px solid #666;
  }
  tr {
    height: 60px;
  }
  :deep(.my-label) {
    color: #333333 !important;
    background: #999 !important;
  }
  :deep(.my-content) {
    color: #333333 !important;
    background: #999;
  }
  :deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
    border: 1px solid #333333;
  }
</style>
