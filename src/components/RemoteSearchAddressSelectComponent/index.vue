<!--
 * @Author: llm
 * @Date: 2024-10-30 16:24:04
 * @LastEditors: llm
 * @LastEditTime: 2025-07-28 18:41:06
 * @Description:位置下拉搜索组件
-->
<template>
  <el-select
    v-model="value"
    style="width: 187px"
    filterable
    remote
    reserve-keyword
    placeholder="请输入搜索地址"
    :remote-method="remoteMethod"
    :loading="loading"
    @change="selectValue"
  >
    <el-option v-for="item in options" :key="item.uid" :label="item.city + item.district + item.name" :value="item.uid" />
  </el-select>
</template>
<script>
  import { bdMapSuggestion } from '@/api/Global'
  import { gcj02tobd09, bd09togcj02 } from '@/utils'
  export default {
    data() {
      return {
        loading: false,
        value: '',
        options: [],
      }
    },
    watch: {
      value(newValue) {
        // 当value变化时执行的逻辑
        this.value = newValue
        console.log('值已更新为：', newValue)
      },
    },
    methods: {
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(async () => {
            this.loading = false
            const suggestOptions = await this.bdMapSuggestionFun(query.trim())
            suggestOptions.map((item) => {
              const bdPoint = gcj02tobd09(Number(item.location.lng), Number(item.location.lat))
              item.location.lng = bdPoint[0]
              item.location.lat = bdPoint[1]
            })
            console.log(suggestOptions)

            this.options = suggestOptions
          }, 200)
        } else {
          this.options = []
        }
      },
      bdMapSuggestionFun(query) {
        return new Promise((resolve, reject) => {
          bdMapSuggestion({ query, city: '全国' })
            .then((res) => {
              const { result } = res
              resolve(result)
            })
            .catch((err) => {
              reject(err)
            })
        })
      },
      selectValue(e) {
        const pointInfo = this.options.find((item) => item.uid === e)
        const point = {
          address: pointInfo.city + pointInfo.district + pointInfo.name,
          city: pointInfo.city,
          province: pointInfo.province,
          longitude: pointInfo.location.lng,
          latitude: pointInfo.location.lat,
        }
        this.$emit('selectPointInfo', point)
      },
    },
  }
</script>
