<template>
  <el-dialog v-model="dialogVisible" title="还款" width="1000px" :close-on-click-modal="false" @close="handleClose" class="loan-repayment-dialog">
    <el-form ref="formRef" :model="form" :rules="rules" label-position="left" class="repayment-form">
      <!-- 贷款信息选择 -->
      <el-form-item label="贷款信息" prop="loanId" required>
        <el-select v-model="form.loanId" placeholder="请选择贷款信息" class="full-width-input" @change="handleLoanInfoChange">
          <el-option v-for="(item, i) in loanInfoOptions" :key="i" :label="item.label" :value="item.value" />
        </el-select>
        <!-- 剩余信息提示 -->
        <el-alert v-if="selectedLoanInfo" type="warning" :closable="true" class="info-alert">
          剩余期数: {{ selectedLoanInfo.surplusPeriod }}， 剩余还款金额: {{ selectedLoanInfo.surplusRepayAmount }}， 保证金金额: {{ depositAmount }}
          <template v-if="canUseDeposit">
            <span class="text-red">可以使用保证金进行抵扣</span>
          </template>
        </el-alert>
      </el-form-item>

      <div class="form-row">
        <!-- 还款金额 -->
        <el-form-item label="还款金额" label-width="auto" prop="repaymentAmount" required>
          <!-- <el-input
            v-model.number="form.repaymentAmount"
            placeholder="正数保留两位小数"
            class="full-width-input"
            @input="handleRepaymentAmountChange"
          /> -->
          <el-input-number @change="handleRepaymentAmountChange"  v-model="form.repaymentAmount" :min="0" :precision="2" />
        </el-form-item>

        <!-- 还款编号 -->
        <el-form-item label="还款编号"  prop="repaymentNo">
          <el-input v-model="form.repaymentNo" placeholder="请输入还款编号" class="full-width-input" />
        </el-form-item>

        <!-- 期次 -->
        <el-form-item label="期次"  prop="periodIssue"  required>
          <el-input-number v-model="form.periodIssue" :min="1" />
        </el-form-item>
      </div>

      <div class="form-row">
        <!-- 还款日期 -->
        <el-form-item label="还款日期"  label-width="auto" prop="repaymentDate" required>
          <el-date-picker
            v-model="form.repaymentDate"
            type="date"
            placeholder="选择还款日期"
            value-format="YYYY-MM-DD"
            class="full-width-input"
          />
<!-- 
          <el-date-picker
            v-model="form.repaymentDate"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          /> -->
        </el-form-item>

        <!-- 本金 -->
        <el-form-item label="本金" label-width="60px">
          <!-- <el-input
            v-model="form.principal"
            placeholder="正数保留两位小数"
            class="full-width-input"
          /> -->
          <el-input-number v-model="form.principal" :min="0" :precision="2" />
        </el-form-item>

        <!-- 利息 -->
        <el-form-item label="利息" label-width="45px">
          <!-- <el-input
            v-model="form.interest"
            placeholder="正数保留两位小数"
            class="full-width-input"
          /> -->
          <el-input-number v-model="form.interest" :min="0" :precision="2" />
        </el-form-item>
      </div>

      <div class="form-row">
        <!-- 开户行 -->
        <el-form-item  label="开户行"  label-width="66px" prop="bank" >
          <el-input v-model="form.bank" placeholder="请输入开户行" class="full-width-input" />
        </el-form-item>

        <!-- 账户名称 -->
        <el-form-item  label="账户名称" prop="accountName" >
          <el-input v-model="form.accountName" placeholder="请输入账户名称" class="full-width-input" />
        </el-form-item>

        <!-- 账号 -->
        <el-form-item label="账号" label-width="45px" prop="accountNo" >
          <el-input v-model="form.accountNo" placeholder="请输入账号" class="full-width-input" />
        </el-form-item>
      </div>

      <!-- 是否使用保证金抵扣 -->
      <el-form-item label="是否使用保证金抵扣">
        <el-radio-group v-model="form.marginDeduction" @change="handleDepositChange">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <div class="form-row" v-if="form.marginDeduction === 1">
        <!-- 保证金金额 -->
        <el-form-item label="保证金金额">
          <el-input v-model="depositAmount" disabled placeholder="保证金金额" class="full-width-input" />
        </el-form-item>

        <!-- 差额 -->
        <el-form-item label="差额">
          <el-input v-model="form.diffAmount" disabled placeholder="正数保留两位小数" class="full-width-input">
            <template #append>
              <span class="input-tip">负数表示退款，正数表示补款</span>
            </template>
          </el-input>
        </el-form-item>
      </div>

      <!-- 凭证上传 -->
      <el-form-item label="凭证">
        <UploadImageComponent v-model:value="form.imageUrlList" @file-data="handleFileData" />
      </el-form-item>

      <!-- 备注 -->
      <el-form-item label="备注">
        <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" class="full-width-input" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { ElMessage } from 'element-plus'
  import UploadImageComponent from '@/components/UploadImageComponent/index.vue'
  import type { FormInstance, FormRules } from 'element-plus'
  import { getselectOptionApi, getOptionSelectApi, PostOptionSelectApi } from '@/api/repayment'

  const emit = defineEmits(['ConfirmSubmit'])
  // 贷款信息接口
  interface LoanInfo {
    id: string
    label: string
    surplusRepayAmount: number
    bondAmount: number
    bondAmountDeduction: number
    periodAmount: number
    surplusPeriod: number
    currentPeriod: number
    repaymentDay: number
    bank: string
    accountName: string
    accountNo: string
  }

  const dialogVisible = ref(false)
  const LoanInfoId = ref(null)
  const formRef = ref<FormInstance>()
  const loanInfoOptions = ref<any>([])
  const selectedLoanInfo = ref<LoanInfo | null>(null)

  // 表单数据
  const form = ref({
    loanId: '', // 贷款信息ID
    repaymentAmount: 0, // 还款金额
    repaymentNo: '', // 还款编号
    periodIssue: 1, // 期次
    repaymentDate: '', // 还款日期
    principal: '', // 本金
    interest: '', // 利息
    bank: '', // 开户行
    accountName: '', // 账户名称
    accountNo: '', // 账号
    marginDeduction: 0, // 是否使用保证金抵扣 0:否 1:是
    bondAmountDeduction: '', // 保证金抵扣
    diffAmount: '0', // 差额
    remark: '', // 备注
    imageUrlList: [], // 凭证
  })
  // 计算保证金金额
  const depositAmount = computed(() => {
    if (!selectedLoanInfo.value) return 0
    //保证金 或 保证金抵扣 == null 的时候，相应的字段 = 0
    if (selectedLoanInfo.value.bondAmount == null) {
      selectedLoanInfo.value.bondAmount = 0
    }
    if (selectedLoanInfo.value.bondAmountDeduction == null) {
      selectedLoanInfo.value.bondAmountDeduction = 0
    }
    return (Number(selectedLoanInfo.value.bondAmount) - Number(selectedLoanInfo.value.bondAmountDeduction)).toFixed(2)
  })

  // 判断是否可以使用保证金抵扣
  const canUseDeposit = computed(() => {
    if (!selectedLoanInfo.value || !depositAmount.value) return false
    return Number(selectedLoanInfo.value.surplusRepayAmount) <= Number(depositAmount.value)
  })

  // 处理贷款信息变更
  const handleLoanInfoChange = (value: string) => {
    console.log(value, 'value')
    getOptionSelect(value)
  }

  // 处理还款金额变更
  const handleRepaymentAmountChange = () => {
    if (form.value.marginDeduction === 1 && form.value.repaymentAmount != null) {
      form.value.diffAmount = (Number(form.value.repaymentAmount) - Number(form.value.bondAmountDeduction)).toFixed(2)
    }
  }

  // 处理保证金抵扣变更
  const handleDepositChange = (val: string | number | boolean | undefined) => {
    form.value.marginDeduction = val ? 1 : 0
    if (val) {
      form.value.bondAmountDeduction = String(depositAmount.value)
      handleRepaymentAmountChange()
    }
  }

  const getOptionSelect = async (id: string) => {
    const res = await getOptionSelectApi(id)

    const selected = res.data as any

    if (selected) {
      selectedLoanInfo.value = selected
      console.log(selectedLoanInfo.value, 'selectedLoanInfo.value')

      form.value.loanId = selected.id
      form.value.repaymentAmount = selected.periodRepayAmount != null ? Number(selected.periodRepayAmount) : 0
      // 还款日期 = 结束日期 : 结束日期为空时，还款日期为今天
      form.value.repaymentDate = selected.endDate ? selected.endDate : new Date().toISOString().split('T')[0]
      form.value.periodIssue = Number(selected.currentPeriod) + 1
      form.value.bank = selected.repaymentBank
      form.value.accountName = selected.repaymentAccountName
      form.value.accountNo = selected.repaymentAccountNo

    }
  }

  watch(dialogVisible, (newVal) => {
    if (newVal) {
      getLoanInfo()
      if (newVal) {
        form.value.loanId = LoanInfoId.value ? LoanInfoId.value : ''
        if (form.value.loanId) {
          getOptionSelect(form.value.loanId)
        }
      }
    }
  })

  const getLoanInfo = async () => {
    const res = await getselectOptionApi()
    loanInfoOptions.value = res.data
  }

  getLoanInfo()
  // 表单校验规则
  const rules: FormRules = {
    loanId: [{ required: true, message: '请选择贷款信息', trigger: 'change' }],
    repaymentAmount: [
      { required: true, message: '请输入还款金额', trigger: 'blur' },
      { type: 'number', message: '还款金额必须为数字', trigger: 'blur' },
    ],
    periodIssue: [{ required: true, message: '请输入期次', trigger: 'blur' }],
    repaymentDate: [{ required: true, message: '请选择还款日期', trigger: 'change' }],
  }

  // 处理关闭
  const handleClose = () => {
    dialogVisible.value = false
    form.value = {
    loanId: '', // 贷款信息ID
    repaymentAmount: 0, // 还款金额
    repaymentNo: '', // 还款编号
    periodIssue: 1, // 期次
    repaymentDate: '', // 还款日期
    principal: '', // 本金
    interest: '', // 利息
    bank: '', // 开户行
    accountName: '', // 账户名称
    accountNo: '', // 账号
    marginDeduction: 0, // 是否使用保证金抵扣 0:否 1:是
    bondAmountDeduction: '', // 保证金抵扣
    diffAmount: '0', // 差额
    remark: '', // 备注
    imageUrlList: [], // 凭证
  }
    formRef.value?.resetFields()
  }

  const handleFileData = (data: any) => {
    form.value.imageUrlList = data
  }

  // 处理提交
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      // 转换数据类型
      const submitData = {
        ...form.value,
        repaymentAmount: Number(form.value.repaymentAmount),
        principal: Number(form.value.principal),
        interest: Number(form.value.interest),
        bondAmountDeduction: Number(form.value.bondAmountDeduction),
        diffAmount: Number(form.value.diffAmount),
      }

      PostOptionSelectApi(submitData).then((res) => {
        emit('ConfirmSubmit', res)
        ElMessage.success('提交成功')
        handleClose()
      })
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  // 暴露方法给父组件
  defineExpose({
    dialogVisible,
    LoanInfoId,
  })
</script>

<style scoped>
  .loan-repayment-dialog {
    :deep(.el-dialog) {
      border-radius: 4px;
      min-width: 800px;

      .el-dialog__header {
        margin: 0;
        padding: 10px 16px;
        border-bottom: 1px solid #ebeef5;

        .el-dialog__title {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }
      }

      .el-dialog__body {
        padding: 16px 24px;
      }

      .el-dialog__footer {
        margin: 0;
        padding: 10px 16px;
        border-top: 1px solid #ebeef5;
        text-align: right;
      }
    }
  }

  .repayment-form {
    :deep(.el-form-item) {
      margin-bottom: 16px;
      display: flex;
      width: 100%;
      }
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
    }

  .info-alert {
    margin-top: 8px;
  }

  .input-tip {
    font-size: 12px;
    color: #909399;
    white-space: nowrap;
  }

  .text-red {
    color: #f56c6c;
  }
</style>
