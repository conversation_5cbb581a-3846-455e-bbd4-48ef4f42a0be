<template>
  <!-- 车辆信息 -->
  <div
    v-if="(JSON.stringify(dataInfo) != '{}' || dataInfo) && isShowHide"
    class="statisticsInfo"
    :style="{ width: newTypeName === 'dispatchNo' ? '745px' : '480px' }"
  >
    <div class="topInfo">
      <span></span>
      <el-button type="primary" @click.stop="closeInfo(false)">隐藏</el-button>
    </div>
    <div class="buttonBox">
      <el-tabs v-model="newTypeName" class="tabChangeClass" type="border-card" @tab-change="handleChange">
        <el-tab-pane label="车辆信息" name="vehicle">
          <div class="vehicleInfo">
            <el-row>
              <el-col :span="12" class="colItem">
                <span class="lableText">承运商：</span>
                <span>{{ dataInfo.carrierName }}</span>
              </el-col>
              <el-col :span="12" class="colItem">
                <span class="lableText">车牌号：</span>
                <span>{{ dataInfo.vehicleNo }}</span>
              </el-col>
              <el-col :span="12" class="colItem">
                <span class="lableText">行驶里程：</span>
                <span>{{ dataInfo.navTotalMileage }}</span>
              </el-col>
              <el-col :span="12" class="colItem">
                <span class="lableText">驾驶时长：</span>
                <span>{{ dataInfo.drivingTotalTime }}</span>
              </el-col>
              <el-col :span="12" class="colItem">
                <span class="lableText">停车时长：</span>
                <span>{{ dataInfo.parkingTotalTime }}</span>
              </el-col>
              <el-col :span="12" class="colItem">
                <span class="lableText">平均速度：</span>
                <span>{{ dataInfo.averageSpeed }}</span>
              </el-col>
              <el-col :span="24" class="colItem">
                <div class="button-block">
                  <el-button type="primary" style="text-align: right" @click.stop="viewTrajectory(dataInfo)">{{ btnText }}</el-button>
                  <!-- 动态设置播放速度 -->
                  <div class="slider-block">
                    <span class="demonstration">播放速度</span>
                    <el-slider v-model="speedValue" :min="1000" :max="100000" :step="1000" @change="handleSpeedChange" />
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
        <el-tab-pane label="调度信息" name="task" v-if="currentTaskList.length > 0">
          <div class="containerInfo">
            <el-row class="itemInfo" v-for="(item, index) in currentTaskList" :key="index">
              <el-col :span="24" class="colItem">
                <span class="lableText">调度单号：</span>
                <span class="valueText" style="color: #0081ff; cursor: pointer" @click="linkToPage(item)">{{ item.taskNo ? item.taskNo : '--' }}</span>
              </el-col>
              <el-col :span="24" class="colItem">
                <span class="lableText">调度路线：</span>
                <span class="valueText">{{ item.lineInfo ? item.lineInfo : '--' }}</span>
              </el-col>
            </el-row>
          </div>
          <!-- <div class="buttonInfo">
            <el-button size="mini" type="primary" @click="viewOrderDetail()">订单信息</el-button>
            <el-button size="mini" type="primary" @click="viewTrajectory()">查看轨迹</el-button>
          </div> -->
        </el-tab-pane>
        <el-tab-pane label="调度单详情" name="dispatchNo" v-if="vinInfoList.length > 0">
          <div class="containerInfo">
            <el-table :data="vinInfoList" border fit highlight-current-row size="small" max-height="240px">
              <el-table-column :resizable="false" align="center" :show-overflow-tooltip="true" width="100" label="客户" prop="customerName"></el-table-column>
              <el-table-column :resizable="false" align="center" :show-overflow-tooltip="true" width="100" label="VIN" prop="vin"></el-table-column>
              <el-table-column
                :resizable="false"
                align="center"
                :show-overflow-tooltip="true"
                width="100"
                label="订单状态"
                prop="orderStatusName"
              ></el-table-column>
              <el-table-column :resizable="false" align="center" :show-overflow-tooltip="true" width="100" label="运输线路" prop="lineName"></el-table-column>
              <el-table-column :resizable="false" align="center" :show-overflow-tooltip="true" width="100" label="装车时间" prop="loadTime"></el-table-column>
              <el-table-column
                :resizable="false"
                align="center"
                :show-overflow-tooltip="true"
                width="100"
                label="预计交车时间"
                prop="estimateArriveTime"
              ></el-table-column>
              <el-table-column :resizable="false" align="center" :show-overflow-tooltip="true" width="100" label="交车时间" prop="dropTime"></el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
  <div v-if="JSON.stringify(dataInfo) != '{}' && !isShowHide" style="position: absolute; top: 94px; right: 20px; z-index: 9">
    <el-button type="primary" @click.stop="closeInfo(true)">展示</el-button>
  </div>
</template>

<script>
  import router from '@/router'
  import { useFormStore } from '@/store/modules/form'
  const formStore = useFormStore()
  export default {
    emits: ['closeInformation'],
    props: {
      dataInfo: {
        type: Object,
        default: {},
      },
      isShowHide: {
        type: Boolean,
        default: false,
      },
      currentTaskNo: {
        type: String,
        default: '',
      },
      // 任务列表
      currentTaskList: {
        type: Array,
        default: [],
      },
      // 调度单信息
      vinInfoList: {
        type: Array,
        default: [],
      },
      btnText: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        newTypeName: 'vehicle',
        speedValue: 50000,
      }
    },
    watch: {},
    methods: {
      // 隐藏展示车辆信息
      closeInfo(value) {
        this.$emit('closeInformation', !this.isShowHide)
      },

      // tab切换
      handleChange(tab) {
        this.newTypeName = tab
      },

      // 查看订单信息
      viewOrderDetail(value) {
        this.$emit('viewOrderDetail', value)
      },

      // 查看轨迹
      viewTrajectory(data) {
        this.$emit('viewTrajectory', data)
      },

      // 跳转调度页面
      linkToPage(item) {
        //不刷新查询条件
        formStore.setIsRefreshTopQueryParams(false)
        formStore.$patch((state) => {
          state.routerParams = {
            dispatchNo: item.taskNo,
          }
        })
        router.push({
          path: '/businessManagement/smartDispatch',
          query: {
            time: new Date().getTime(), //获取当前时间戳,防止不刷新页面
          },
        })
      },

      // 动态设置播放速度
      handleSpeedChange(value) {
        this.$emit('speedChange', value)
      },
    },
  }
</script>

<style scoped>
  /* 车辆信息展示 */
  .statisticsInfo {
    /* width: 480px; */
    padding: 0 10px 10px;
    background: #f9f9f9;
    position: absolute;
    top: 94px;
    right: 10px;
    border-radius: 10px;
    border: 1px solid #999;
    z-index: 9;
  }

  .statisticsInfo :deep(.el-tabs__content) {
    padding: 0;
  }

  .statisticsInfo .vehicleInfo {
    margin: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    max-height: 240px;
    overflow: scroll;
    overflow-x: hidden;
    overflow-y: auto;
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
  }

  .statisticsInfo .containerInfo {
    margin: 10px;
    max-height: 240px;
    overflow: scroll;
    overflow-x: hidden;
    overflow-y: auto;
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
  }

  .statisticsInfo .vehicleInfo::-webkit-scrollbar,
  .statisticsInfo .containerInfo::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
  }

  .statisticsInfo .containerInfo .itemInfo {
    border: 1px solid #eee;
    border-bottom: none;
    border-radius: 4px;
    margin-bottom: 10px;
  }

  .statisticsInfo .topInfo {
    display: flex;
    align-items: center;
    height: 40px;
    justify-content: space-between;
    border-bottom: 1px solid #ccc;
  }

  .statisticsInfo .topInfo .title {
    font-size: 16px;
    padding: 0 4px;
    font-weight: bold;
  }

  .statisticsInfo .colItem {
    display: flex;
    padding: 4px 5px;
    font-size: 14px;
    border-bottom: 1px solid #eee;
  }

  .vehicleInfo .colItem:nth-last-child(2),
  .vehicleInfo .colItem:last-child {
    border-bottom: 0;
  }

  .statisticsInfo .lableText {
    color: #333;
    font-weight: 500;
  }

  .statisticsInfo .valueText {
    flex: 1;
    font-weight: 500;
  }

  .statisticsInfo .rowText {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .statisticsInfo .buttonBox {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-top: 8px;
  }

  .colItem :deep(.el-button--mini) {
    padding: 2px 8px;
    margin-left: 4px;
  }

  .buttonInfo {
    margin: 10px;
    text-align: end;
  }
  .slider-block {
    width: 75%;
    display: flex;
    align-items: center;
  }
  .slider-block .el-slider {
    margin-top: 0;
    margin-left: 12px;
  }
  .slider-block .demonstration {
    text-align: center;
    font-size: 14px;
    color: var(--el-text-color-secondary);
    line-height: 44px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 0;
  }
  .slider-block .demonstration + .el-slider {
    flex: 0 0 70%;
  }
  .button-block {
    width: 100%;
    display: flex;
    align-items: center;
  }
</style>
