<template>
    <el-dialog
    v-model="RoutedialogVisible"
    title="路线对比"
    width="800px"
    align-center
    :close-on-click-modal="false"
    @close="RouteColse"
  >
    <RouteMap ref="routeMapRef" :value="piontList" />

    <el-table :data="tableData" border style="width: 100%">
    <el-table-column prop="routeName" label="项目"  show-overflow-tooltip/>
    <el-table-column prop="lineName" label="线路地点"  show-overflow-tooltip />
    <el-table-column prop="mileage" label="线路总里程"  show-overflow-tooltip/>
    <el-table-column prop="durationStr" label="耗时"  show-overflow-tooltip/>
    <el-table-column prop="oilFeeDesc" label="油费" width="300" show-overflow-tooltip/>
  </el-table>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="RoutedialogVisible = false">取消</el-button>
        <el-button type="primary" @click="RoutedialogVisible = false">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getroutecompare } from '@/api/repayment'
import RouteMap from '@/components/RouteMap/map.vue'
import { ref,useTemplateRef } from 'vue'
const RoutedialogVisible = ref(false)
const dispatchNo = ref('')
const tableData = ref([])
const piontList = ref({
    startend: [],
    waypoints: [],
    baidutrace: []
})

const routeMapRef = useTemplateRef('routeMapRef')
watch(RoutedialogVisible, async (newVal) => {
    if (newVal) {
        await getRouteCompare()
    }
})
const getRouteCompare = async () => {
    const res = await getroutecompare(dispatchNo.value)

    tableData.value = res.data.routes
    piontList.value.startend = res.data.startEnd.split(';')
    piontList.value.waypoints = res.data.wayPoints.split(';')
    // '规划路线'
    if(!res.data.routes.find(item=> item.routeName === '规划线路')){
        piontList.value.baidutrace = []
    }else{  
        piontList.value.baidutrace = res.data.routes.find(item=> item.routeName === '规划线路').trace.split(';')
    }
    // '司机路线'
    if(!res.data.routes.find(item=> item.routeName === '司机线路')){
        piontList.value.sijitrace = []
    }else{  
        piontList.value.sijitrace = res.data.routes.find(item=> item.routeName === '司机线路').trace.split(';')
    }
    
}
const RouteColse = () => {
    RoutedialogVisible.value = false
    piontList.value.startend = []
    piontList.value.waypoints = []
    piontList.value.baidutrace = []
    tableData.value = []
    routeMapRef.value.removeOverlay()
}
defineExpose({
    RoutedialogVisible,
    dispatchNo
})
</script>

<style lang="scss" scoped>

</style>