<template>
  <div>
    <div id="container"></div>

    <div class="btnMenu">
      <div class="btnMenu-item">
        规划路线
        <div class="btnMenu-active"></div>
      </div>
      <div class="btnMenu-item">
        司机路线
        <div class="btnMenu-active1"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { loadBaiDuDrawMap } from '@/utils/bmpgl_lib'
  import qidianIcon from '@/assets/makerImg/qidian.png'
  import zhongdianIcon from '@/assets/makerImg/zhongdian.png'
  import waypointIcon from '@/assets/makerImg/tujing.png'
  import { ref, watch, nextTick } from 'vue'
  
  let map = ref(null)

  const props = defineProps({
    value: {
      type: Boolean,
      default: false,
    },
  })


  const removeOverlay = () => {
    if(!map.value) return
    var allOverlay = map.value.getOverlays();
    if (allOverlay.length) {
       for (var i = 0; i < allOverlay.length; i++) {
            map.value.removeOverlay(allOverlay[i]);
       }
    }
  }


  const initMap = async () => {
    try {
      if (!props.value || !props.value.startend || props.value.startend.length < 1) return
      
      await nextTick()
      const BMapGL = await loadBaiDuDrawMap()
      
      // 确保BMapGL已经加载
      if (!BMapGL || !window.BMapGL) {
        console.error('百度地图API未正确加载')
        return
      }

      // 创建地图实例
      map.value = new window.BMapGL.Map('container')
      
      // 中心点是中国地图的中心点
      const centerPoint = new window.BMapGL.Point(116.404, 39.915)
      map.value.centerAndZoom(centerPoint, 4.8)
      map.value.enableScrollWheelZoom(true) //开启鼠标滚轮缩放

      // 转换坐标字符串为数字数组
      const parseCoordinates = (coordStr) => {
        const [lng, lat] = coordStr.split(',').map(Number)
        return [lng, lat]
      }

      // 解析起点和终点坐标
      const [startLng, startLat] = parseCoordinates(props.value.startend[0])
      const [endLng, endLat] = parseCoordinates(props.value.startend[1])

      let arr = [props.value.baidutrace,props.value.sijitrace]
      let color = ['#5888FE','#d7a244']
      arr.forEach((item,index) => {
        if(item.length > 0){
          let trace = item.map((item) => {
            const [lng, lat] = item.split(',').map(Number)
            return new window.BMapGL.Point(lng, lat)
          })
          var polyline = new window.BMapGL.Polyline(trace, { 
            strokeColor: color[index], 
            strokeWeight: 6, 
            strokeOpacity: 1 
          })
          map.value.addOverlay(polyline)
        }
      })

      let waypoints = props.value.waypoints.map((item) => {
        const [lng, lat] = item.split(',').map(Number)
        return new window.BMapGL.Point(lng, lat)
      })

      const start = new window.BMapGL.Point(startLng, startLat)
      const end = new window.BMapGL.Point(endLng, endLat)

      var startIcon = new window.BMapGL.Icon(qidianIcon, new window.BMapGL.Size(30, 30))
      var endIcon = new window.BMapGL.Icon(zhongdianIcon, new window.BMapGL.Size(40, 40))
      var waypointIcons = new window.BMapGL.Icon(waypointIcon, new window.BMapGL.Size(30, 30))
      
      // 创建标注对象并添加到地图
      var startmarker = new window.BMapGL.Marker(start, { icon: startIcon })
      var endmarker = new window.BMapGL.Marker(end, { icon: endIcon })

      map.value.addOverlay(startmarker)
      map.value.addOverlay(endmarker)

      waypoints.forEach((item) => {
        var marker = new window.BMapGL.Marker(item, { icon: waypointIcons })
        map.value.addOverlay(marker)
      })
    } catch (error) {
      console.error('地图初始化失败:', error)
    }
  }

  watch(
    () => props.value,
    (newVal) => {
      if (newVal) {
        nextTick(() => {
          initMap()
        })
      }
    },
    //开启页面初始化监听
    {
      immediate: true,
      deep: true,
    },
  )

  defineExpose({
    removeOverlay
  })
</script>

<style scoped>
  #container {
    width: 768px;
    height: 400px;
  }
  .btnMenu {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .btnMenu-item {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-right: 10px;
      .btnMenu-active {
        width: 100px;
        height: 20px;
        background-color: #5888fe;
      }
      .btnMenu-active1 {
        width: 100px;
        height: 20px;
        background-color: #d7a244;
      }
    }
  }
</style>
