<template>
  <el-dialog v-model="dialogVisible" :title="title" width="700" :before-close="handleClose">
    <el-form ref="ruleFormRef" :model="form" label-width="auto" style="max-width: 610px; margin: 0 auto" :rules="rules">
      <el-form-item label="线路">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="起点" prop="startFenceId">
            <el-select
              v-model="form.startFenceId"
              popper-class="my-selects"
              remote-show-suffix
              clearable
              :multiple="false"
              filterable
              :fit-input-width="true"
              @change="(val: string) => changeSelectMethod(val, 'start')"
              remote
              reserve-keyword
              placeholder="模糊搜索"
              :remote-method="(query: string) => remoteSelectMethod(query, 'start')"
            >
              <el-option v-for="i in startpointData || []" :key="i.value" :label="i.label" :value="i.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="终点" prop="endFenceId">
            <el-select
              v-model="form.endFenceId"
              popper-class="my-selects"
              remote-show-suffix
              clearable
              :multiple="false"
              :fit-input-width="true"
              filterable
              @change="(val: string) => changeSelectMethod(val, 'end')"
              remote
              reserve-keyword
              placeholder="模糊搜索"
              :remote-method="(query: string) => remoteSelectMethod(query, 'end')"
            >
              <el-option v-for="i in endpointData || []" :key="i.value" :label="i.label" :value="i.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="系统里程">
            <div class="flex justify-between w-full">
              <span>约{{ form.systemMileage || 0 }}km</span>
              <el-button type="primary" :loading="submitLoading" @click="getSelectApi">里程计算</el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="驾驶员结算里程" prop="mileage">
            <el-input-number style="width: 100%" :min="0" controls-position="right" v-model="form.mileage" size="small" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="高速费">
            <el-input-number style="width: 100%" :min="0" controls-position="right" v-model="form.highwayFee" size="small" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="备注">
        <el-input v-model="form.remark" type="textarea" :rows="3" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm(ruleFormRef)">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
  import { getGeofenceListApi, postDispatchLineApi, postFleetRouteDispatchMileageApi, postmileage } from '@/api/businessManagement'
  import { ref } from 'vue'
  import { ElMessageBox, FormInstance, FormRules } from 'element-plus'
  const emit = defineEmits(['refresh'])
  let title = ref('')
  let dialogVisible = ref(false)
  let ruleFormRef = ref<FormInstance>()
  let form = ref({
    name: '', //线路
    startFenceId: '', //起点
    endFenceId: '', //终点
    systemMileage: '', //系统里程
    mileage: '', //驾驶员结算里程
    highwayFee: '', //高速费
    remark: '', //备注
  })

  let state = reactive({
    addressFuzzySearchParams: {
      label: 'name',
      dataSource: '围栏',
      value: 'id',
      fuzzy: true,
      keyword: '',
    },
  })

  const rules = reactive<FormRules>({
    startFenceId: [{ required: true, message: '请选择起点', trigger: 'blur' }],
    endFenceId: [
      {
        required: true,
        message: '请选择终点',
        trigger: 'blur',
      },
    ],
    mileage: [
      {
        required: true,
        message: '请输入驾驶员结算里程',
        trigger: 'blur',
      },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value <= 0) {
            callback(new Error('驾驶员结算里程不能小于0'))
          } else {
            callback()
          }
        },
      },
    ],
  })

  let submitLoading = ref(false)
  let startpointData: any = ref([])
  let endpointData: any = ref([])
  const handleClose = () => {

    form.value = {
      name: '', //线路
      startFenceId: '', //起点
      endFenceId: '', //终点
      systemMileage: '', //系统里程
      mileage: '', //驾驶员结算里程
      highwayFee: '', //高速费
      remark: '', //备注
    }
    startpointData.value = []
    endpointData.value = []
    ruleFormRef.value?.resetFields()
    dialogVisible.value = false
  }

  watch(
    () => dialogVisible.value,
    (newval) => {
      if (!newval) return
    },
  )

  // 防抖
  const debounce = (fn: any, delay = 300) => {
    let timer: any = null
    return (...args: any) => {
      clearTimeout(timer)
      timer = setTimeout(() => {
        fn.apply(this, args)
      }, delay)
    }
  }
  //远程模糊搜索下拉
  const remoteSelectMethod = debounce(async (query: string, key: string) => {
    if (query) {
      state.addressFuzzySearchParams.keyword = query
      const { data } = await getGeofenceListApi(state.addressFuzzySearchParams)
      if (key === 'start') {
        startpointData.value = data
      } else {
        endpointData.value = data
      }
    }
  }, 1000)

  const changeSelectMethod = (val: string, key: string) => {
    console.log(val, key, 'val, key')
    if (key === 'start') {
      form.value.startFenceId = val
    } else {
      form.value.endFenceId = val
    }
  }

  const getSelectApi = async () => {
    submitLoading.value = true
    let params = {
      endPoint: {
        coordinate: '',
        fenceId: form.value.endFenceId,
        name: endpointData.value.find((item: any) => item.value === form.value.endFenceId)?.label,
      },
      startPoint: {
        coordinate: '',
        fenceId: form.value.startFenceId,
        name: startpointData.value.find((item: any) => item.value === form.value.startFenceId)?.label,
      },
    }
    try {
      let res = await postFleetRouteDispatchMileageApi(params)
      form.value.systemMileage = res.data.distanceKm
    } catch (error) {
      console.log(error, 'error')
    } finally {
      submitLoading.value = false
    }
  }

  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        postmileageApi()
      } else {
        console.log('error submit!', fields)
      }
    })
  }

  const postmileageApi = async () => {
    let res = await postmileage(form.value)
    ElMessage.success('新增成功')
    handleClose()
    emit('refresh')
  }

  

  defineExpose({
    dialogVisible,
    title,
    form
  })
</script>

<style scoped lang="scss">
  .my-selects .el-select-dropdown__item {
    box-sizing: border-box;
    color: var(--el-text-color-regular);
    cursor: pointer;
    font-size: var(--el-font-size-base);
    line-height: 20px !important;
    height: auto !important;
    overflow: auto !important;
    padding: 10px 32px 10px 20px !important;
    position: relative;

    /* 文本溢出处理 - 兼容性优化 */
    text-overflow: clip !important;

    /* 换行处理 - 兼容性优化 */
    white-space: normal !important; /* wrap 不是标准值，改为 normal */
    word-wrap: break-word !important; /* 添加 word-wrap 兼容老版本浏览器 */
    word-break: break-all !important;

    /* 添加 Webkit 前缀支持 */
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
  }

  /* 为老版本 IE 添加回退样式 */
  .my-selects .el-select-dropdown__item {
    *zoom: 1; /* IE6/7 触发 hasLayout */
  }

  /* 针对不支持 CSS 变量的浏览器添加回退值 */
  .my-selects .el-select-dropdown__item {
    color: #606266; /* 回退颜色值 */
    font-size: 14px; /* 回退字体大小 */
  }

  .my-selects .el-select-dropdown__item {
    color: var(--el-text-color-regular, #606266);
    font-size: var(--el-font-size-base, 14px);
  }
</style>
