<!--
 * @Author: llm
 * @Date: 2025-01-06 16:49:14
 * @LastEditors: llm
 * @LastEditTime: 2025-03-27 12:12:20
 * @Description:
-->
<script setup lang="ts">
  import { useAppStore } from '@/store/modules/app'
  import SvgIcons from '@/components/SvgIcons/index.vue'
  const appStore = useAppStore()

  const sizeOptions = ref([
    { label: '默认', value: 'default' },
    { label: '大型', value: 'large' },
    { label: '小型', value: 'small' },
  ])

  function handleSizeChange(size: string) {
    appStore.changeSize(size)
    ElMessage.success('切换布局大小成功')
  }
</script>

<template>
  <el-dropdown trigger="click" @command="handleSizeChange">
    <div>
      <svg-icons icon-class="size" />
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item v-for="item of sizeOptions" :key="item.value" :disabled="appStore.size == item.value" :command="item.value">
          {{ item.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
