<!--
 * @Author: llm
 * @Date: 2025-01-08
 * @LastEditors: llm
 * @LastEditTime: 2025-01-08
 * @Description: 智能 Tooltip 组件，自动处理层级问题
-->
<template>
  <el-tooltip
    :append-to="appendTo"
    :auto-close="autoClose"
    :content="content"
    :disabled="disabled"
    :effect="effect"
    :enterable="enterable"
    :hide-after="hideAfter"
    :manual="manual"
    :offset="offset"
    :persistent="persistent"
    :placement="placement"
    :popper-class="computedPopperClass"
    :popper-options="popperOptions"
    :show-after="showAfter"
    :show-arrow="showArrow"
    :teleported="teleported"
    :transition="transition"
    :trigger="trigger"
    :virtual-ref="virtualRef"
    :virtual-triggering="virtualTriggering"
    @hide="handleHide"
    @show="handleShow"
    @before-show="handleBeforeShow"
    @before-hide="handleBeforeHide"
  >
    <slot />
  </el-tooltip>
</template>

<script lang="ts" setup>
  import { computed, ref, onMounted, onUnmounted, nextTick } from 'vue'

  interface Props {
    content?: string
    placement?:
      | 'left'
      | 'right'
      | 'top'
      | 'bottom'
      | 'top-start'
      | 'top-end'
      | 'bottom-start'
      | 'bottom-end'
      | 'left-start'
      | 'left-end'
      | 'right-start'
      | 'right-end'
    effect?: 'dark' | 'light'
    showAfter?: number
    disabled?: boolean
    offset?: number
    showArrow?: boolean
    popperClass?: string
    enterable?: boolean
    hideAfter?: number
    autoClose?: number
    manual?: boolean
    popperOptions?: any
    teleported?: boolean
    trigger?: 'hover' | 'focus' | 'click' | 'contextmenu'
    virtualTriggering?: boolean
    virtualRef?: any
    appendTo?: string | HTMLElement
    transition?: string
    persistent?: boolean
    // 自定义 z-index，如果不提供则自动计算
    zIndex?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    placement: 'top',
    effect: 'dark',
    showAfter: 100,
    disabled: false,
    offset: 12,
    showArrow: true,
    enterable: true,
    hideAfter: 200,
    autoClose: 0,
    manual: false,
    teleported: true,
    trigger: 'hover',
    virtualTriggering: false,
    transition: 'el-fade-in-linear',
    persistent: false,
  })

  const emit = defineEmits<{
    show: []
    hide: []
    beforeShow: []
    beforeHide: []
  }>()

  // 动态计算 popper-class，只在 VXE Table 环境中添加高层级样式类
  const computedPopperClass = computed(() => {
    let classes = props.popperClass || ''

    // 检查是否在 VXE Table 容器中
    const vxeTableElement = document.querySelector('.vxe-table, .vxe-table-container')
    if (vxeTableElement) {
      classes += ' smart-tooltip-high-z-index'
    }

    return classes.trim()
  })

  // 监听弹窗状态变化
  const observer = ref<MutationObserver | null>(null)

  onMounted(() => {
    // 创建观察器来监听 DOM 变化
    observer.value = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // 检查是否有弹窗相关元素被添加或移除
          const addedNodes = Array.from(mutation.addedNodes)
          const removedNodes = Array.from(mutation.removedNodes)

          const hasModalChange = [...addedNodes, ...removedNodes].some(
            (node) =>
              node instanceof Element &&
              (node.classList.contains('el-dialog') || node.classList.contains('el-drawer') || node.classList.contains('el-popover')),
          )

          if (hasModalChange) {
            // 触发重新计算（通过响应式系统自动处理）
            // computedZIndex 会自动重新计算
          }
        }
      })
    })

    // 开始观察 body 的变化
    observer.value.observe(document.body, {
      childList: true,
      subtree: true,
    })
  })

  onUnmounted(() => {
    if (observer.value) {
      observer.value.disconnect()
    }
  })

  // 事件处理
  const handleShow = () => {
    // 只在 VXE Table 环境中强制设置高 z-index
    nextTick(() => {
      const vxeTableElement = document.querySelector('.vxe-table, .vxe-table-container')
      if (vxeTableElement) {
        const tooltips = vxeTableElement.querySelectorAll('.el-tooltip__popper')
        tooltips.forEach((tooltip) => {
          if (tooltip instanceof HTMLElement) {
            tooltip.style.setProperty('z-index', '9999', 'important')
          }
        })
      }
    })
    emit('show')
  }

  const handleHide = () => {
    emit('hide')
  }

  const handleBeforeShow = () => {
    emit('beforeShow')
  }

  const handleBeforeHide = () => {
    emit('beforeHide')
  }
</script>

<style>
  /* VXE Table 专用高层级 tooltip 样式 */
  .vxe-table .smart-tooltip-high-z-index,
  .vxe-table-container .smart-tooltip-high-z-index {
    z-index: 9999 !important;
  }
</style>
