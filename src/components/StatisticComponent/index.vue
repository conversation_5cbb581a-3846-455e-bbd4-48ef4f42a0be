<!--
 * @Author: llm 
 * @Date: 2023-09-15 14:40:35
 * @LastEditors: llm 
 * @LastEditTime: 2023-09-21 15:24:13
 * @Description: 统计组件
 * 
-->
<template>
  <div style="display: flex; align-items: center; justify-content: space-around">
    <div v-for="(item, index) in statisticList" :key="index">
      <el-statistic :value="item.number * 1" class="text-center">
        <template #title>
          <div style="display: inline-flex; align-items: center">
            {{ item.label }}
          </div>
        </template>
        <template #suffix>{{ item.suffix }}</template>
      </el-statistic>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { StatisticColumnVO } from '@/types/global'
  const props = defineProps({
    statisticColumn: {
      required: true,
      default: () => {
        return [] as StatisticColumnVO[]
      },
    },
    /**
     * 列表项
     */
    tableColumn: {
      required: true,
      default: () => {
        return [] as TableItem[]
      },
    },
  })
  const statisticList = ref<StatisticColumnVO[]>([])

  watch(
    () => props.tableColumn,
    (tableColumn) => {
      const filterColumn = tableColumn.filter((item) => {
        return item.listEnable
      })
      watch(
        () => props.statisticColumn,
        (statisticColumn) => {
          //过滤props.statisticColumn 在filterColumn中存在的
          statisticList.value = statisticColumn.filter((item) => {
            return filterColumn.find((i) => i.name === item.name)
          })
        },
      )
    },
  )
</script>
<style lang="scss" scoped>
  :deep(.el-statistic__head) {
    font-size: 16px;
  }
</style>
