<!--
 * @Author: llm
 * @Date: 2025-04-21 19:32:35
 * @LastEditors: llm
 * @LastEditTime: 2025-06-25 15:09:38
 * @Description: 查看文件弹窗组件
-->
<template>
  <el-dialog
    v-loading="state.loading"
    draggable
    :close-on-click-modal="false"
    v-model="state.dialogVisible"
    :title="state.column.jump?.title || '文件预览'"
    width="60%"
    :before-close="handleClose"
  >
    <el-scrollbar height="60vh">
      <!-- 图片预览区域 -->
      <el-card shadow="hover" v-if="state.imageFiles.length > 0">
        <div slot="header">
          <el-text size="large">图片列表</el-text>
        </div>
        <div class="image-preview-container">
          <div>
            <el-row :gutter="10">
              <el-col :span="6" v-for="(item, index) in state.imageFiles" :key="index" style="padding: 10px">
                <el-image
                  :src="item.url"
                  lazy
                  style="width: 100%; height: 100%"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="state.imageFiles.map((item) => item.url)"
                  :preview-teleported="true"
                  :initial-index="index"
                  show-progress
                />
              </el-col>
            </el-row>
          </div>
        </div>
      </el-card>

      <!-- 文件列表表格 -->
      <el-card style="margin-top: 10px" shadow="hover" v-if="state.pdfFiles.length > 0">
        <div slot="header">
          <el-text size="large">文件列表</el-text>
        </div>
        <el-table :data="state.pdfFiles" style="width: 100%; margin-top: 20px" border>
          <el-table-column prop="name" label="文件名">
            <template #default="scope">
              <el-link :href="scope.row.url" target="_blank">{{ scope.row.name }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleDownload(scope.row)"> 下载 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <el-card style="margin-top: 10px" shadow="hover" v-if="state.mp4Files.length > 0">
        <div slot="header">
          <el-text size="large">视频列表</el-text>
        </div>
        <div>
          <el-row :gutter="10">
            <el-col :span="6" v-for="(item, index) in state.mp4Files" :key="index" style="padding: 10px">
              <video :src="item.url" controls style="width: 100%; height: 100%"></video>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </el-scrollbar>
    <div class="dialog-footer">
      <el-button v-for="(item, index) in state.column?.jump?.btns" :key="index" type="primary" @click="handleClickBtn(item)">{{ item.label }}</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
  import { postFormApi, refreshCheckGetApi, refreshCheckPostApi } from '@/api/shipmentManagement'
  import { isFileType } from '@/utils'
  import { composeRequestParams, composeRequestParamsMultiRow, composeRequestQueryParams, downloadFileGlobalFun } from '@/utils/common'
  import { cloneDeep } from 'lodash'

  const state = reactive({
    row: {} as any,
    imageFiles: [] as any[],
    pdfFiles: [] as any[],
    mp4Files: [] as any[],
    dialogVisible: false,
    column: {} as any,
    loading: false,
  })

  // 处理下载
  const handleDownload = (file: any) => {
    window.open(file.url)
  }

  // 处理关闭
  const handleClose = () => {
    state.dialogVisible = false
  }

  const showFileDialog = (row: any, column: any) => {
    state.column = column
    state.row = row
    const files = row[column.name] || []

    state.imageFiles = files.filter((file: any) => isFileType(file, 'image'))
    state.pdfFiles = files.filter((file: any) => isFileType(file, 'document'))
    state.mp4Files = files.filter((file: any) => isFileType(file, 'video'))
  }

  // 处理批量下载
  const handleClickBtn = async (btnItem: BtnRequestVO) => {
    state.loading = true
    const download = btnItem.download
    let res = {} as any
    let params = {
      ids: state.row.id,
    } as any
    try {
      // await confirmDialogParams(params, state.row, btnItem)
      if (btnItem.method === 'post' || btnItem.method === 'POST') {
        res = await refreshCheckPostApi(btnItem.uri!, params)
      } else if (btnItem.method === 'get' || btnItem.method === 'GET') {
        res = await refreshCheckGetApi(btnItem.uri!, params)
      } else if (btnItem.method === 'postForm') {
        res = await postFormApi(btnItem.uri!, btnItem.responseType, params)
      }
      if (download) {
        if (download === 'file') {
          if (res.data.indexOf(',') !== -1) {
            res.data.split(',').forEach((item: any) => {
              window.open(item)
            })
          } else {
            window.open(res.data)
          }
        } else if (download === 'stream') {
          await downloadFileGlobalFun(res)
        }
      }
      if (btnItem.label === '取消') {
        state.dialogVisible = false
      }
    } catch (error) {
    } finally {
      state.loading = false
    }
  }
  const confirmDialogParams = async (params: any, row: TableItem, btnItem: BtnRequestVO) => {
    if (btnItem.params) {
      const _params = cloneDeep(params)
      //定义传递的参数
      btnItem.params?.map((_item) => {
        if (_item.dependFrom === 'listData') {
          composeRequestParams(_params, _item, null, null, row, null)
        }
      })
    }
    composeRequestQueryParams(params, null, state.column.menu, null, null, null)
  }
  defineExpose({
    showFileDialog,
    state,
  })
</script>
<style scoped lang="scss">
  .dialog-footer {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
</style>
