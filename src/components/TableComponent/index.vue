<!--
 * @Author: llm
 * @Date: 2023-06-27 14:55:21
 * @LastEditors: llm
 * @LastEditTime: 2025-06-27 10:05:53
 * @Description: table组件
 *
-->
/* tslint:disable */
<template>
  <el-table
    class="custom-summary-table"
    v-if="refreshTable"
    ref="dataTableRef"
    v-loading="loading"
    :max-height="height"
    :stripe="false"
    size="small"
    tooltip
    :highlight-current-row="highlightCurrentRow"
    :border="tableBorder"
    :header-cell-style="{ backgroundColor: '#FAFAFA', textAlign: 'center' }"
    :cell-style="handleCellStyle"
    :row-key="rowKey"
    :default-expand-all="isExpandAll || defaultExpandAll"
    :data="tableData"
    :key="tableConfig.isKey as any"
    :header-cell-class-name="handleHeadAddClass"
    :row-class-name="tableRowClassName"
    :span-method="props.objectSpanMethod"
    @selection-change="handleSelectionChange"
    @sort-change="handleSortChange"
    table-layout="auto"
    :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    @cell-click="handleCellClick"
    :style="{ '--hideFirstColumn': hideFirstColumn }"
    @select="handleSelectChange"
    @select-all="handleSelectAllChange"
    show-summary
    :summary-method="getSummaries"
  >
    <el-table-column :selectable="selectable" type="selection" :width="props.totalSummary ? 90 : 45" align="center" v-if="tableConfig.showHandleSelection" />

    <el-table-column fixed="left" align="center" type="index" width="45" :index="indexMethod" v-if="tableConfig.showSort">
      <template #header>
        <div style="display: flex; align-items: center; justify-content: center">序号</div>
      </template>
      <template #default="scope">
        <div style="display: flex; align-items: center; justify-content: center">
          {{ scope.$index + 1 }}
        </div>
      </template>
    </el-table-column>
    <template #empty>{{ loading ? '' : '暂无数据' }}</template>
    <template v-for="item in tableConfig.tableItem">
      <template v-if="item.listEnable">
        <template v-if="item.children && item.children.length > 0">
          <el-table-column :label="item.label" align="center" :fixed="item.attributes?.fixed">
            <template v-for="child in item.children" :key="child.name">
              <!-- 标签显示 -->
              <el-table-column
                :fixed="child.attributes ? child.attributes!.fixed : false"
                :prop="child.name"
                :min-width="child.minWidth"
                :width="child.width"
                :align="child.align"
                :sortable="child.sortAble"
                v-if="child.type === 'tag'"
              >
                <template #header>
                  <div style="display: flex; align-items: center; justify-content: center">
                    {{ child.label }}
                    <span v-if="state.orderBys.find((item: any) => item.column === child.name)" class="sort-priority">
                      {{ state.orderBys.findIndex((item: any) => item.column === child.name) + 1 }}
                    </span>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="child.attributes!.content ?? '--'"
                      placement="top-start"
                      v-if="child.attributes && child.attributes!.content"
                    >
                      <el-icon>
                        <WarningFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="scope" v-if="child.name">
                  <el-tag
                    v-for="(v, i) in scope.row[child.name]?.split(',')"
                    effect="dark"
                    type="success"
                    :color="scope.row[child.name + 'Color'] || '#7EC050'"
                    :key="i"
                    style="margin: 5px"
                    >{{ v }}</el-tag
                  >
                </template>
              </el-table-column>
              <!-- 树形显示 -->
              <el-table-column
                :fixed="child.attributes ? child.attributes!.fixed : false"
                :prop="child.name"
                :min-width="child.minWidth"
                :width="child.width"
                :align="child.align"
                show-overflow-tooltip
                :sortable="child.sortAble ? 'custom' : false"
                v-else-if="child.type === 'tree'"
              >
                <template #header>
                  <div style="display: flex; align-items: center; justify-content: center">
                    {{ child.label }}
                    <span v-if="state.orderBys.find((item: any) => item.column === child.name)" class="sort-priority">
                      {{ state.orderBys.findIndex((item: any) => item.column === child.name) + 1 }}
                    </span>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="child.attributes!.content ?? '--'"
                      placement="top-start"
                      v-if="child.attributes && child.attributes!.content"
                    >
                      <el-icon>
                        <WarningFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
              <!-- 开关显示 -->
              <el-table-column
                :fixed="child.attributes ? child.attributes!.fixed : false"
                :prop="child.name"
                :min-width="child.minWidth"
                :width="child.width"
                :align="child.align"
                show-overflow-tooltip
                :sortable="child.sortAble ? 'custom' : false"
                v-else-if="child.type === 'switch'"
              >
                <template #header>
                  <div style="display: flex; align-items: center; justify-content: center">
                    {{ child.label }}
                    <span v-if="state.orderBys.find((item: any) => item.column === child.name)" class="sort-priority">
                      {{ state.orderBys.findIndex((item: any) => item.column === child.name) + 1 }}
                    </span>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="child.attributes!.content ?? '--'"
                      placement="top-start"
                      v-if="child.attributes && child.attributes!.content"
                    >
                      <el-icon>
                        <WarningFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="scope">
                  <el-switch
                    v-model="scope.row.enable"
                    :active-value="true"
                    inline-prompt
                    active-text="启用"
                    inactive-text="禁用"
                    :disabled="scope.row.canEdit"
                    @change="handleStatusChange(scope.row)"
                  />
                </template>
              </el-table-column>
              <!-- 图片显示 -->
              <el-table-column
                :fixed="child.attributes ? child.attributes!.fixed : false"
                :prop="child.name"
                :sortable="child.sortAble ? 'custom' : false"
                v-else-if="child.type === 'viewPictures'"
              >
                <template #header>
                  <div style="display: flex; align-items: center; justify-content: center">
                    {{ child.label }}
                    <span v-if="state.orderBys.find((item: any) => item.column === child.name)" class="sort-priority">
                      {{ state.orderBys.findIndex((item: any) => item.column === child.name) + 1 }}
                    </span>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="child.attributes!.content ?? '--'"
                      placement="top-start"
                      v-if="child.attributes && child.attributes!.content"
                    >
                      <el-icon>
                        <WarningFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="scope">
                  <el-image
                    style="width: 50px; height: 50px"
                    v-for="url in scope.row[child.name].map((_item: any) => _item.url) ?? []"
                    :src="url"
                    :zoom-rate="1.2"
                    :max-scale="7"
                    :min-scale="0.2"
                    :preview-src-list="scope.row[child.name].map((_item: any) => _item.url) ?? []"
                    fit="cover"
                  />
                </template>
              </el-table-column>
              <el-table-column
                :fixed="child.attributes ? child.attributes!.fixed : false"
                :prop="child.name"
                :sortable="child.sortAble ? 'custom' : false"
                v-else-if="child.type === 'imageThumbnail'"
              >
                <template #header>
                  <div style="display: flex; align-items: center; justify-content: center">
                    {{ child.label }}
                    <span v-if="state.orderBys.find((item: any) => item.column === child.name)" class="sort-priority">
                      {{ state.orderBys.findIndex((item: any) => item.column === child.name) + 1 }}
                    </span>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="child.attributes!.content ?? '--'"
                      placement="top-start"
                      v-if="child.attributes && child.attributes!.content"
                    >
                      <el-icon>
                        <WarningFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="scope">
                  <el-link type="primary" v-if="scope.row[child.name] && scope.row[child.name].length > 1" @click="showPic(scope.row, child.name)"
                    >查看</el-link
                  >
                  <div v-else>
                    <el-image
                      style="width: 50px; height: 50px"
                      v-for="(_item, _index) in scope.row[child.name] ?? []"
                      :key="_index"
                      :src="_item.url"
                      :zoom-rate="1.2"
                      :max-scale="7"
                      :min-scale="0.2"
                      :preview-src-list="scope.row[child.name].map((_item: any) => _item.url) ?? []"
                      fit="cover"
                    />
                  </div>
                </template>
              </el-table-column>
              <!-- 文件回显 -->
              <el-table-column
                :fixed="child.attributes ? child.attributes!.fixed : false"
                :prop="child.name"
                :min-width="child.minWidth"
                :width="child.width"
                :align="child.align"
                show-overflow-tooltip
                :sortable="child.sortAble ? 'custom' : false"
                v-else-if="child.type === 'viewFiles'"
              >
                <template #header>
                  <div style="display: inline-block; text-align: center">
                    {{ child.label }}
                    <span v-if="state.orderBys.find((item: any) => item.column === child.name)" class="sort-priority">
                      {{ state.orderBys.findIndex((item: any) => item.column === child.name) + 1 }}
                    </span>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="child.attributes!.content ?? '--'"
                      placement="top-start"
                      v-if="child.attributes && child.attributes!.content"
                    >
                      <el-icon>
                        <WarningFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="scope">
                  <div style="display: flex; align-items: center; justify-content: space-between">
                    <div v-if="scope.row[child.name]">
                      <div v-for="(_item, _index) in scope.row[child.name].split(',')" :key="_index">
                        <el-link type="primary" @click="downloadFile(_item)">
                          {{ child.name !== undefined ? getRowValue(scope.row, child.name).split('/').pop() || child.name : '' }}
                        </el-link>
                      </div>
                    </div>
                    <div v-else>--</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :fixed="child.attributes ? child.attributes!.fixed : false"
                :prop="child.name"
                :min-width="child.minWidth"
                :width="child.width"
                :align="child.align"
                show-overflow-tooltip
                :sortable="child.sortAble ? 'custom' : false"
                v-else-if="child.type === 'showFiles'"
              >
                <template #header>
                  <div style="display: inline-block; text-align: center">
                    {{ child.label }}
                    <span v-if="state.orderBys.find((item: any) => item.column === child.name)" class="sort-priority">
                      {{ state.orderBys.findIndex((item: any) => item.column === child.name) + 1 }}
                    </span>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="child.attributes!.content ?? '--'"
                      placement="top-start"
                      v-if="child.attributes && child.attributes!.content"
                    >
                      <el-icon>
                        <WarningFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="scope">
                  <div style="display: flex; align-items: center; justify-content: space-between">
                    <div v-if="scope.row[child.name]">
                      <div v-for="(_item, _index) in scope.row[child.name]" :key="_index">
                        <el-link type="primary" @click="downloadFile(_item.url)">
                          {{ child.name !== undefined ? _item.name : '' }}
                        </el-link>
                      </div>
                    </div>
                    <div v-else>--</div>
                  </div>
                </template>
              </el-table-column>
              <!-- 按钮显示 -->
              <el-table-column
                :fixed="child.attributes ? child.attributes!.fixed : false"
                :prop="child.name"
                :min-width="child.minWidth"
                :width="child.width"
                :align="child.align"
                show-overflow-tooltip
                :sortable="child.sortAble ? 'custom' : false"
                v-else-if="child.type === 'operation'"
              >
                <template #header>
                  <div style="display: inline-block; text-align: center">
                    {{ child.label }}
                    <span v-if="state.orderBys.find((item: any) => item.column === child.name)" class="sort-priority">
                      {{ state.orderBys.findIndex((item: any) => item.column === child.name) + 1 }}
                    </span>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="child.attributes!.content ?? '--'"
                      placement="top-start"
                      v-if="child.attributes && child.attributes!.content"
                    >
                      <el-icon>
                        <WarningFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="scope">
                  <div style="display: flex; align-items: center; justify-content: space-between">
                    <div v-if="scope.row[child.name] === null">
                      {{ scope.row[child.name] === null ? '--' : scope.row[child.name] }}
                    </div>
                    <el-link
                      type="primary"
                      @click="(event: any) => linkTo(scope.row, child, event)"
                      v-else-if="
                        !child.jump?.dependsOn ||
                        child.jump.dependsOn.every((depend: DependsOn) => {
                          if (!depend.operator) {
                            depend.operator = 'eq'
                          }
                          var target = scope.row[depend.field!]
                          var when = depend.when!
                          if (!operatorCalculate(target, when, depend.operator)) {
                            return false
                          }
                          return operatorCalculate(target, when, depend.operator)
                        })
                      "
                    >
                      {{ child.name !== undefined ? getRowValue(scope.row, child.name) : '' }}
                    </el-link>
                    <el-text v-else>{{ child.name !== undefined ? getRowValue(scope.row, child.name) : '' }}</el-text>
                  </div>
                </template>
              </el-table-column>
              <!-- 富文本显示 -->
              <el-table-column
                :fixed="child.attributes ? child.attributes!.fixed : false"
                :prop="child.name"
                :min-width="child.minWidth"
                :width="child.width"
                :align="child.align"
                show-overflow-tooltip
                :sortable="child.sortAble ? 'custom' : false"
                v-else-if="child.type === 'textRich'"
              >
                <template #header>
                  <div style="display: inline-block; text-align: center">
                    {{ child.label }}
                    <span v-if="state.orderBys.find((item: any) => item.column === child.name)" class="sort-priority">
                      {{ state.orderBys.findIndex((item: any) => item.column === child.name) + 1 }}
                    </span>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="child.attributes!.content ?? '--'"
                      placement="top-start"
                      v-if="child.attributes && child.attributes!.content"
                    >
                      <el-icon>
                        <WarningFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="scope">
                  <div style="display: flex; align-items: center; justify-content: space-between">
                    <div v-html="scope.row[child.name] === null ? '--' : scope.row[child.name]"></div>
                  </div>
                </template>
              </el-table-column>
              <!-- 默认显示 -->
              <el-table-column
                :fixed="child.attributes ? child.attributes!.fixed : false"
                :prop="child.name"
                :min-width="child.minWidth"
                :width="child.width"
                :align="child.align"
                show-overflow-tooltip
                :sortable="child.sortAble ? 'custom' : false"
                v-else
              >
                <template #header>
                  <div style="display: flex; align-items: center; justify-content: center">
                    {{ child.label }}
                    <span v-if="state.orderBys.find((item: any) => item.column === child.name)" class="sort-priority">
                      {{ state.orderBys.findIndex((item: any) => item.column === child.name) + 1 }}
                    </span>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="child.attributes!.content ?? '--'"
                      placement="top-start"
                      v-if="child.attributes && child.attributes!.content"
                    >
                      <el-icon>
                        <WarningFilled />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="scope">
                  <div style="display: flex; align-items: center; justify-content: space-between">
                    <!-- 如果值是boolean类型，则显示是否 -->

                    <div v-if="typeof scope.row[child.name] === 'boolean'">
                      <!-- 如果 child.name==='applyFlag' 则显示配板状态 需要显示 配/常 -->
                      <div v-if="child.name === 'applyFlag'">
                        <!-- <el-tag
                                    :type="scope.row[child.name] ? '' : 'success'"
                                    class="mx-1"
                                    effect="dark"
                                    round
                                  >
                                    {{ scope.row[child.name] ? "配" : "常" }}
                                  </el-tag> -->
                        <el-switch
                          :inline-prompt="true"
                          v-model="scope.row[child.name]"
                          style="--el-switch-on-color: #409eff; --el-switch-off-color: #13ce66"
                          active-text="配"
                          inactive-text="常"
                        />
                      </div>
                      <!-- 否则显示 是/否 -->
                      <div v-else>{{ scope.row[child.name] ? '是' : '否' }}</div>
                    </div>
                    <!-- 否则直接显示返回文字 -->
                    <div v-else>
                      <div class="flex-row items-center" v-if="child.name === 'title' && showReadStatus">
                        <div class="read-status" v-if="scope.row.readStatus !== 1"></div>
                        {{ scope.row[child.name] === null ? '--' : scope.row[child.name] }}
                      </div>
                      <div v-else style="display: flex; align-items: center">
                        <!-- 如果child.fromJson为true，显示scope.row[item.name]?.[child.name] -->
                        <span v-if="child.fromJson">
                          {{ scope.row[item.name]?.[child.name] }}
                        </span>
                        <!-- 否则直接显示scope.row[child.name] -->
                        <span v-else>{{ scope.row[child.name] === null ? '--' : scope.row[child.name] }}</span>
                        <svg-icons
                          :icon-class="scope.row.styleItem.find((v: any) => v.name === child.name).icon"
                          :color="scope.row.styleItem.find((v: any) => v.name === child.name).color"
                          style="margin-left: 10px"
                          size="18"
                          v-if="scope.row.styleItem && scope.row.styleItem.find((v: any) => v.name === child.name)"
                        />
                      </div>
                    </div>
                  </div>
                  <el-tooltip content="点击复制" style="display: flex; align-items: center">
                    <div class="ml-2 mt-1">
                      <el-icon @click.stop="copy(scope.row[child.name])" v-if="child.attributes?.copy" color="#409eff">
                        <CopyDocument />
                      </el-icon>
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column>
            </template>
          </el-table-column>
        </template>
        <template v-else>
          <!-- 标签显示 -->
          <el-table-column
            :fixed="item.attributes ? item.attributes!.fixed : false"
            :prop="item.name"
            :min-width="item.minWidth"
            :width="item.width"
            :align="item.align"
            :sortable="item.sortAble"
            v-if="item.type === 'tag'"
          >
            <template #header>
              <div style="display: flex; align-items: center; justify-content: center">
                {{ item.label }}
                <el-tooltip
                  v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))"
                  effect="dark"
                  :content="`优先级排序:${state.orderBys.findIndex((order: any) => order.column === item.name) + 1}`"
                  placement="top"
                  :show-after="100"
                >
                  <span v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))" class="sort-priority">
                    {{ state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1 }}
                  </span>
                </el-tooltip>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="item.attributes!.content ?? '--'"
                  placement="top-start"
                  v-if="item.attributes && item.attributes!.content"
                >
                  <el-icon>
                    <WarningFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="scope" v-if="item.name">
              <el-tag
                v-for="(v, i) in scope.row[item.name]?.split(',')"
                effect="dark"
                type="success"
                :color="scope.row[item.name + 'Color'] || '#7EC050'"
                :key="i"
                style="margin: 5px"
                >{{ v }}</el-tag
              >
            </template>
          </el-table-column>
          <!-- 树形显示 -->
          <el-table-column
            :fixed="item.attributes ? item.attributes!.fixed : false"
            :prop="item.name"
            :min-width="item.minWidth"
            :width="item.width"
            :align="item.align"
            show-overflow-tooltip
            :sortable="item.sortAble ? 'custom' : false"
            v-else-if="item.type === 'tree'"
          >
            <template #header>
              <div style="display: flex; align-items: center; justify-content: center">
                {{ item.label }}
                <el-tooltip
                  v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))"
                  effect="dark"
                  :content="`优先级排序:${state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1}`"
                  placement="top"
                  :show-after="100"
                >
                  <span v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))" class="sort-priority">
                    {{ state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1 }}
                  </span>
                </el-tooltip>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="item.attributes!.content ?? '--'"
                  placement="top-start"
                  v-if="item.attributes && item.attributes!.content"
                >
                  <el-icon>
                    <WarningFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <!-- 开关显示 -->
          <el-table-column
            :fixed="item.attributes ? item.attributes!.fixed : false"
            :prop="item.name"
            :min-width="item.minWidth"
            :width="item.width"
            :align="item.align"
            show-overflow-tooltip
            :sortable="item.sortAble ? 'custom' : false"
            v-else-if="item.type === 'switch'"
          >
            <template #header>
              <div style="display: flex; align-items: center; justify-content: center">
                {{ item.label }}
                <el-tooltip
                  v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))"
                  effect="dark"
                  :content="`优先级排序:${state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1}`"
                  placement="top"
                  :show-after="100"
                >
                  <span v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))" class="sort-priority">
                    {{ state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1 }}
                  </span>
                </el-tooltip>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="item.attributes!.content ?? '--'"
                  placement="top-start"
                  v-if="item.attributes && item.attributes!.content"
                >
                  <el-icon>
                    <WarningFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="scope">
              <el-switch
                v-model="scope.row.enable"
                :active-value="true"
                inline-prompt
                active-text="启用"
                inactive-text="禁用"
                :disabled="scope.row.canEdit"
                @change="handleStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
          <!-- 图片显示 -->
          <el-table-column
            :fixed="item.attributes ? item.attributes!.fixed : false"
            :prop="item.name"
            :sortable="item.sortAble ? 'custom' : false"
            v-else-if="item.type === 'viewPictures'"
          >
            <template #header>
              <div style="display: flex; align-items: center; justify-content: center">
                {{ item.label }}
                <el-tooltip
                  v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))"
                  effect="dark"
                  :content="`优先级排序:${state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1}`"
                  placement="top"
                  :show-after="100"
                >
                  <span v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))" class="sort-priority">
                    {{ state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1 }}
                  </span>
                </el-tooltip>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="item.attributes!.content ?? '--'"
                  placement="top-start"
                  v-if="item.attributes && item.attributes!.content"
                >
                  <el-icon>
                    <WarningFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="scope">
              <el-image
                style="width: 50px; height: 50px"
                v-for="url in scope.row[item.name]?.map((_item: any) => _item.url) ?? []"
                :src="url"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="scope.row[item.name]?.map((_item: any) => _item.url) ?? []"
                fit="cover"
              />
            </template>
          </el-table-column>
          <el-table-column
            :fixed="item.attributes ? item.attributes!.fixed : false"
            :prop="item.name"
            :sortable="item.sortAble ? 'custom' : false"
            v-else-if="item.type === 'imageThumbnail'"
          >
            <template #header>
              <div style="display: flex; align-items: center; justify-content: center">
                {{ item.label }}
                <el-tooltip
                  v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))"
                  effect="dark"
                  :content="`优先级排序:${state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1}`"
                  placement="top"
                  :show-after="100"
                >
                  <span v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))" class="sort-priority">
                    {{ state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1 }}
                  </span>
                </el-tooltip>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="item.attributes!.content ?? '--'"
                  placement="top-start"
                  v-if="item.attributes && item.attributes!.content"
                >
                  <el-icon>
                    <WarningFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="scope">
              <el-link type="primary" v-if="scope.row[item.name] && scope.row[item.name].length > 1" @click="showPic(scope.row, item.name)">查看</el-link>
              <div v-else>
                <el-image
                  style="width: 50px; height: 50px"
                  v-for="(_item, _index) in scope.row[item.name] ?? []"
                  :key="_index"
                  :src="_item.url"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="scope.row[item.name]?.map((_item: any) => _item.url) ?? []"
                  fit="cover"
                />
              </div>
            </template>
          </el-table-column>
          <!-- 文件回显 -->
          <el-table-column
            :fixed="item.attributes ? item.attributes!.fixed : false"
            :prop="item.name"
            :min-width="item.minWidth"
            :width="item.width"
            :align="item.align"
            show-overflow-tooltip
            :sortable="item.sortAble ? 'custom' : false"
            v-else-if="item.type === 'viewFiles'"
          >
            <template #header>
              <div style="display: inline-block; text-align: center">
                {{ item.label }}
                <el-tooltip
                  v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))"
                  effect="dark"
                  :content="`优先级排序:${state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1}`"
                  placement="top"
                  :show-after="100"
                >
                  <span v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))" class="sort-priority">
                    {{ state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1 }}
                  </span>
                </el-tooltip>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="item.attributes!.content ?? '--'"
                  placement="top-start"
                  v-if="item.attributes && item.attributes!.content"
                >
                  <el-icon>
                    <WarningFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="scope">
              <div style="display: flex; align-items: center; justify-content: space-between">
                <div v-if="scope.row[item.name]">
                  <div v-for="(_item, _index) in scope.row[item.name].split(',')" :key="_index">
                    <el-link type="primary" @click="downloadFile(_item)">
                      {{ item.name !== undefined ? getRowValue(scope.row, item.name).split('/').pop() || item : '' }}
                    </el-link>
                  </div>
                </div>
                <div v-else>--</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            :fixed="item.attributes ? item.attributes!.fixed : false"
            :prop="item.name"
            :min-width="item.minWidth"
            :width="item.width"
            :align="item.align"
            show-overflow-tooltip
            :sortable="item.sortAble ? 'custom' : false"
            v-else-if="item.type === 'showFiles'"
          >
            <template #header>
              <div style="display: inline-block; text-align: center">
                {{ item.label }}
                <el-tooltip
                  v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))"
                  effect="dark"
                  :content="`优先级排序:${state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1}`"
                  placement="top"
                  :show-after="100"
                >
                  <span v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))" class="sort-priority">
                    {{ state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1 }}
                  </span>
                </el-tooltip>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="item.attributes!.content ?? '--'"
                  placement="top-start"
                  v-if="item.attributes && item.attributes!.content"
                >
                  <el-icon>
                    <WarningFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="scope">
              <!-- <div style="display: flex; align-items: center; justify-content: space-between">
                <div v-if="scope.row[item.name]">
                  <div v-for="(_item, _index) in scope.row[item.name]" :key="_index">
                    <el-link type="primary" @click="downloadFile(_item.url)">
                      {{ item.name !== undefined ? _item.name : '' }}
                    </el-link>
                  </div>
                </div>
                <div v-else>--</div>
              </div> -->
              <el-link type="primary" v-if="scope.row[item.name] && scope.row[item.name].length > 1" @click="showFileDialog(scope.row, item)">查看</el-link>
              <div v-else-if="isFileType(scope.row[item.name]?.[0], 'image')">
                <el-image
                  style="width: 50px; height: 50px"
                  v-for="(_item, _index) in scope.row[item.name] ?? []"
                  :key="_index"
                  :src="_item.url"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="scope.row[item.name]?.map((_item: any) => _item.url) ?? []"
                  fit="cover"
                />
              </div>
              <div v-else-if="isFileType(scope.row[item.name]?.[0], 'document')">
                <el-link type="primary" @click="downloadFile(scope.row[item.name][0].url)">
                  {{ item.name !== undefined ? scope.row[item.name][0].name : '' }}
                </el-link>
              </div>
              <div v-else-if="isFileType(scope.row[item.name]?.[0], 'video')">
                <div class="video-thumbnail" @click="playVideo(scope.row[item.name][0].url)">
                  <el-icon class="play-icon">
                    <VideoPlay />
                  </el-icon>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- 按钮显示 -->
          <el-table-column
            :fixed="item.attributes ? item.attributes!.fixed : false"
            :prop="item.name"
            :min-width="item.minWidth"
            :width="item.width"
            :align="item.align"
            show-overflow-tooltip
            :sortable="item.sortAble ? 'custom' : false"
            v-else-if="item.type === 'operation'"
          >
            <template #header>
              <div style="display: inline-block; text-align: center">
                {{ item.label }}
                <el-tooltip
                  effect="dark"
                  v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))"
                  :content="`优先级排序:${state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1}`"
                  placement="top"
                  :show-after="100"
                >
                  <span v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))" class="sort-priority">
                    {{ state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1 }}
                  </span>
                </el-tooltip>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="item.attributes!.content ?? '--'"
                  placement="top-start"
                  v-if="item.attributes && item.attributes!.content"
                >
                  <el-icon>
                    <WarningFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="scope">
              <div style="display: flex; align-items: center; justify-content: space-between">
                <div
                  v-if="
                    scope.row[item.name] === null ||
                    item.jump?.dependsOn?.every((depend: DependsOn) => {
                      if (!depend.operator) {
                        depend.operator = 'eq'
                      }
                      var target = scope.row[depend.field!]
                      var when = depend.when!
                      return !operatorCalculate(target, when, depend.operator)
                    })
                  "
                >
                  {{ scope.row[item.name] === null ? '--' : scope.row[item.name] }}
                </div>
                <!-- 如果 item.jump?.jumpType === 'importFile' 则显示导入模版 -->
                <div v-else-if="item.jump?.jumpType === 'importFile'">
                  <el-upload
                    :accept="'.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar,.7z'"
                    ref="uploadFileRef"
                    :show-file-list="false"
                    action="#"
                    :auto-upload="false"
                    :multiple="false"
                    :limit="1"
                    :on-change="
                      (file: any, fileList: any[]) => {
                        uploadFile(file, fileList, item, scope.row)
                      }
                    "
                    :on-exceed="handleExceed"
                    :before-upload="beforeUpload"
                  >
                    <template #trigger>
                      <el-link type="primary">
                        {{ item.name !== undefined ? getRowValue(scope.row, item.name) : '' }}
                      </el-link>
                    </template>
                  </el-upload>
                </div>
                <el-link
                  type="primary"
                  @click="(event: any) => linkTo(scope.row, item, event)"
                  v-else-if="
                    !item.jump?.dependsOn ||
                    item.jump.dependsOn.every((depend: DependsOn) => {
                      if (!depend.operator) {
                        depend.operator = 'eq'
                      }
                      var target = scope.row[depend.field!]
                      var when = depend.when!
                      if (!operatorCalculate(target, when, depend.operator)) {
                        return false
                      }
                      return operatorCalculate(target, when, depend.operator)
                    })
                  "
                >
                  {{ item.name !== undefined ? getRowValue(scope.row, item.name) : '' }}
                </el-link>

                <el-text v-else>{{ item.name !== undefined ? getRowValue(scope.row, item.name) : '' }}</el-text>
              </div>
            </template>
          </el-table-column>
          <!-- 富文本显示 -->
          <el-table-column
            :fixed="item.attributes ? item.attributes!.fixed : false"
            :prop="item.name"
            :min-width="item.minWidth"
            :width="item.width"
            :align="item.align"
            show-overflow-tooltip
            :sortable="item.sortAble ? 'custom' : false"
            v-else-if="item.type === 'textRich'"
          >
            <template #header>
              <div style="display: inline-block; text-align: center">
                {{ item.label }}
                <el-tooltip
                  v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))"
                  effect="dark"
                  :content="`优先级排序:${state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1}`"
                  placement="top"
                  :show-after="100"
                >
                  <span v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))" class="sort-priority">
                    {{ state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1 }}
                  </span>
                </el-tooltip>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="item.attributes!.content ?? '--'"
                  placement="top-start"
                  v-if="item.attributes && item.attributes!.content"
                >
                  <el-icon>
                    <WarningFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="scope">
              <div :style="{ display: 'flex', 'align-items': 'center', 'justify-content': item.align, width: '100%' }">
                <div v-html="scope.row[item.name] === null ? '--' : scope.row[item.name]"></div>
              </div>
            </template>
          </el-table-column>
          <!-- 默认显示 -->
          <el-table-column
            :fixed="item.attributes ? item.attributes!.fixed : false"
            :prop="item.name"
            :min-width="item.minWidth"
            :width="item.width"
            :align="item.align"
            show-overflow-tooltip
            :sortable="item.sortAble ? 'custom' : false"
            v-else
          >
            <template #header>
              <div style="display: flex; align-items: center; justify-content: center">
                {{ item.label }}
                <el-tooltip
                  v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))"
                  effect="dark"
                  :content="`优先级排序:${state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1}`"
                  placement="top"
                  :show-after="100"
                >
                  <span v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))" class="sort-priority">
                    {{ state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1 }}
                  </span>
                </el-tooltip>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="item.attributes!.content ?? '--'"
                  placement="top-start"
                  v-if="item.attributes && item.attributes!.content"
                >
                  <el-icon>
                    <WarningFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="scope">
              <div :style="{ display: 'flex', 'align-items': 'center', 'justify-content': item.align, width: '100%' }">
                <!-- 如果值是boolean类型，则显示是否 -->

                <div v-if="typeof scope.row[item.name] === 'boolean'">
                  <!-- 如果 item.name==='applyFlag' 则显示配板状态 需要显示 配/常 -->
                  <div v-if="item.name === 'applyFlag'">
                    <!-- <el-tag
                                    :type="scope.row[item.name] ? '' : 'success'"
                                    class="mx-1"
                                    effect="dark"
                                    round
                                  >
                                    {{ scope.row[item.name] ? "配" : "常" }}
                                  </el-tag> -->
                    <el-switch
                      :inline-prompt="true"
                      v-model="scope.row[item.name]"
                      style="--el-switch-on-color: #409eff; --el-switch-off-color: #13ce66"
                      active-text="配"
                      inactive-text="常"
                    />
                  </div>
                  <!-- 否则显示 是/否 -->
                  <div v-else>{{ scope.row[item.name] ? '是' : '否' }}</div>
                </div>
                <!-- 否则直接显示返回文字 -->
                <div v-else>
                  <div class="flex-row items-center" v-if="item.name === 'title' && showReadStatus">
                    <div class="read-status" v-if="scope.row.readStatus !== 1"></div>
                    {{ scope.row[item.name] === null ? '--' : scope.row[item.name] }}
                  </div>
                  <div v-else style="display: flex; align-items: center">
                    <span>{{ scope.row[item.name] === null ? '--' : scope.row[item.name] }}</span>
                    <svg-icons
                      :icon-class="scope.row.styleItem.find((v: any) => v.name === item.name).icon"
                      :color="scope.row.styleItem.find((v: any) => v.name === item.name).color"
                      style="margin-left: 10px"
                      size="18"
                      v-if="scope.row.styleItem && scope.row.styleItem.find((v: any) => v.name === item.name)"
                    />
                  </div>
                </div>
                <el-tooltip content="点击复制" style="display: flex; align-items: center">
                  <div class="ml-2 mt-1">
                    <el-icon @click.stop="copy(scope.row[item.name])" v-if="item.attributes?.copy" color="#409eff">
                      <CopyDocument />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </template>
      </template>
    </template>
    <el-table-column v-if="tableConfig.operation" type="operation" fixed="right" min-width="80px" :width="operationWidth" align="center">
      <template #header>
        <div class="flex items-center justify-center">
          <span class="mr-4px" :data-width="operationWidth">操作</span>
          <el-icon size="16" @click="showCustomTableHeader" v-if="showCustomTableHeaderBtn">
            <Tools />
          </el-icon>
        </div>
      </template>
      <template #default="scope">
        <div class="flex items-center justify-center">
          <!-- 显示按钮 -->
          <template v-for="(item, index) in getVisibleButtons(props.buttonPermissionGroup, scope.row)" :key="index">
            <el-button
              type="primary"
              size="small"
              link
              @click.native.stop="operation(item.meta?.purpose, scope.row, item.meta?.position, item.meta, item)"
              :style="{ color: item.meta.background }"
              :data-uri="item.meta.uri ?? '全局的'"
            >
              <div>{{ item.meta?.title }}</div>
            </el-button>
          </template>

          <!-- 如果有更多按钮，显示下拉菜单 -->
          <el-dropdown placement="left-end" v-if="getHiddenButtons(props.buttonPermissionGroup, scope.row).length > 0" trigger="hover">
            <el-button type="primary" size="small" link>
              更多
              <el-icon>
                <ArrowDown />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="(item, index) in getHiddenButtons(props.buttonPermissionGroup, scope.row)"
                  :key="index"
                  @click="operation(item.meta?.purpose, scope.row, item.meta?.position, item.meta, item)"
                >
                  <el-button type="primary" size="small" link :style="{ color: item.meta.background }" :data-uri="item.meta.uri ?? '全局的'">
                    {{ item.meta?.title }}
                  </el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <!-- 自定义表头弹窗 -->
  <CustomTableHeaderComponent ref="customTableHeaderRef" :requestUri="requestUri" @refreshPageTableColumn="refreshPageTableColumn" />
  <!-- 查看照片 -->
  <PicDialogComponent ref="picDialogComponent" :image-list="imageList" />
  <!-- 查看文件 -->
  <ShowFileDialogComponent ref="showFileDialogComponentRef" />
  <!-- 视频播放弹窗 -->
  <el-dialog v-model="videoDialogVisible" title="视频播放" width="60%" destroy-on-close>
    <video ref="videoPlayer" controls style="width: 100%; max-height: 70vh"></video>
  </el-dialog>
  <!-- 鼠标悬浮展示 -->
  <el-dialog v-model="mouseOverShowDialogVisible" :title="mouseOverShowInfo.title" width="400px" destroy-on-close>
    <div v-html="mouseOverShowInfo.data"></div>
  </el-dialog>
</template>
<script setup lang="ts">
  import { useRouter } from 'vue-router'
  import { PropType } from 'vue'
  import { useSideBarStore } from '@/store/modules/sideBar'
  // 状态管理依赖
  import { useButtonGroupStore } from '@/store/modules/buttonGroup'
  import { useFormStore } from '@/store/modules/form.js'
  import { globalRequestApi, globalRequestUrlApi } from '@/api/planManagement'
  import { composeRequestParams, operatorCalculate } from '@/utils/common'
  import CustomTableHeaderComponent from '@/components/CustomTableHeaderComponent/index.vue'
  import SvgIcons from '@/components/SvgIcons/index.vue'
  import PicDialogComponent from '@/components/PicDialogComponent/index.vue'
  import { ArrowDown } from '@element-plus/icons-vue'
  import ShowFileDialogComponent from '@/components/TableComponent/components/showFileDialogComponent.vue'
  import { isFileType } from '@/utils'
  import { importFileGlobalBtnUriFun } from '@/api/auth'
  const sideBarStore = useSideBarStore()

  const buttonGroupStore = useButtonGroupStore()
  const customTableHeaderRef = ref()
  const showFileDialogComponentRef = ref()
  const formStore = useFormStore()
  const { searchParams } = toRaw(formStore)
  const router = useRouter()
  const props = defineProps({
    isExpandAll: {
      type: Boolean,
      default: false,
    },
    requestUri: {
      type: String,
      default: '',
    },
    objectSpanMethod: {
      require: false,
      type: Object as PropType<any>,
      default: () => {},
    },
    /**
     * 高度
     */
    height: {
      type: String,
      default: '60vh',
    },
    highlightCurrentRow: {
      type: Boolean,
      default: false,
    },
    tableBorder: {
      type: Boolean,
      default: true,
    },
    /**
     * rowKey
     */
    rowKey: {
      require: false,
      type: String,
      default: '',
    },
    /**
     * 是否展开所有
     */
    defaultExpandAll: {
      require: false,
      type: Boolean,
      default: false,
    },
    /**
     * 禁用行复选框
     */
    selectable: {
      require: false,
      default: () => {
        return (row: any, index: number) => {
          return true
        }
      },
    },
    /**
     * tree属性
     */
    treeProps: {
      require: false,
      type: Object,
      default: () => {
        return {
          children: 'children',
          hasChildren: 'hasChildren',
        }
      },
    },
    /**
     * 操作按钮权限
     */
    buttonPermissionGroup: {
      require: false,
      type: Array as PropType<MenuVO[]>,
      default: () => {
        return []
      },
    },
    /**
     * 顶部按钮权限
     */
    topButtonPermissionGroup: {
      require: false,
      type: Array as PropType<MenuVO[]>,
      default: () => {
        return []
      },
    },
    /**
     * 加载中
     */
    loading: {
      require: true,
      type: Boolean,
      default: false,
    },
    /**
     * 列表数据
     */
    tableData: {
      require: true,
      type: Array,
      default: () => {
        return []
      },
    },
    /**
     * 分页统计
     */
    pageSummary: {
      require: false,
      type: Object,
      default: () => {},
    },
    /**
     * 总计统计
     */
    totalSummary: {
      require: false,
      type: Object,
      default: () => {},
    },
    /**
     * 是否展示复选框
     */
    showSelection: {
      require: true,
      type: Boolean,
      default: true,
    },
    /**
     * 是否展示全选复选框
     */
    showAllSelection: {
      require: true,
      type: Boolean,
      default: true,
    },
    /**
     * table 配置项
     */
    tableConfig: {
      require: true,
      type: Object as PropType<TableConfig>,
      default: () => {
        return {
          showSort: true,
        }
      },
    },
    /**
     * 是否需要显示已读未读标记
     */
    showReadStatus: {
      require: false,
      type: Boolean,
      default: false,
    },
  })
  const showCustomTableHeaderBtn = ref(false)
  const operationWidth = ref('80px')

  // 计算操作列宽度的方法
  const calculateOperationWidth = () => {
    //如果按钮数<=3,则获取所有按钮的字符数*单个字符数的宽度
    if (props.buttonPermissionGroup.length <= 3) {
      const width = props.buttonPermissionGroup.reduce((acc, item) => acc + (item.meta?.title?.length ?? 0) * 12, 20)
      operationWidth.value = width < 80 ? '80px' : width + 'px'
    } else {
      //如果按钮数>3,则操作列宽度为前三个按钮的宽度
      const width = props.buttonPermissionGroup.slice(0, 3).reduce((acc, item) => acc + (item.meta?.title?.length ?? 0) * 12, 30)
      operationWidth.value = width < 80 ? '80px' : width + 'px'
    }
  }
  const state = reactive({
    sortField: {} as Record<string, string | null>,
    orderBys: [] as { column: string; direction: string | undefined }[],
  })
  // 监听按钮组变化
  watch(
    () => props.buttonPermissionGroup,
    () => {
      calculateOperationWidth()
    },
    { immediate: true },
  )

  // 组件挂载时计算宽度
  onMounted(() => {
    calculateOperationWidth()
    resetTableColumnWidths()
  })

  // 组件被激活时重新计算宽度
  onActivated(() => {
    calculateOperationWidth()
    resetTableColumnWidths()
  })

  watch(
    () => props.topButtonPermissionGroup,
    () => {
      //props.topButtonPermissionGroup 存在item.meta?.purpose === 'definePageHeader'项则showCustomTableHeaderBtn=true
      showCustomTableHeaderBtn.value = props.topButtonPermissionGroup.some((item) => item.meta?.purpose === 'definePageHeader')
    },
    {
      immediate: true,
      deep: true,
    },
  )

  const refreshTable = ref(true) //重新渲染表格
  const isExpandAll = ref(false)
  /**
   * 展开折叠
   */
  const toggleExpandAll = () => {
    refreshTable.value = false
    isExpandAll.value = !isExpandAll.value
    nextTick(() => {
      refreshTable.value = true
    })
  }
  const hideFirstColumn = computed(() => (props.showAllSelection ? 'block' : 'none'))
  const dataTableRef = ref(null)
  const emit = defineEmits([
    'handleSelectRows', //选择的行列表
    'handleSelectionChange', //选择的行列表
    'refreshPageTableColumn', //刷新页面数据列
    'copy', //复制表单
    'addItem', //新增数据
    'batchDelete', //批量删除
    'deleteItem', //单项删除
    'updateItem', //更新数据
    'editById', //更新数据，调接口
    'openMenuDialog', //操作权限
    'openDataColumnMenuDialog', //字段权限
    'handleStatusChange', //更改状态
    'handleQuery', //刷新列表数据
    'updatePassword', //修改密码
    'viewPosition', //查看位置
    'showRowDetail', //弹窗展示
    'confirm', //确认操作
    'showPic', //展示图片
    'cancel', //取消
    'defaultHandle', //默认操作
    'showRowMenuDialog', //弹窗显示菜单
    'handleCellClick', //选中当前行
    'confirmDialog', //自定义提示弹窗
    'showRowObjectDialog', //对象弹窗
    'approvalOperation', //下级审批操作
    'customTableTemplateHeader', //编辑报表模板弹窗
    'fileDownload', //下载文件
    'editExamineTemplate', //编辑考核模板
    'editExamineProject', //编辑考核项目
    'score', //考核项目打分
    'publish', //考核项目发布
    'toLineSchedule', //排程
    'toTrackMap', //查看轨迹
    'meeting', //查看会议纪要
    'editSafeStandard', //编辑安全标准
    'viewSafeStandard', //查看安全标准
    'editInspectionWarehouse', //管理适用仓库
    'editAuth', //编辑带权限的
    'showCanAllocateDetail', //查看可配板数
    'viewYkp', //查看移库计划详情
    'viewPlanLimit', //查看约束调整
    'transportPlanDetail', //规划详情
    'viewInfo', //查看详情
    'outFleetAuditSettingEdit', //审批流程详情
    'editSubsidy', //司机计算编辑补贴
    'editDispatch', //编辑调度
    'editGenerateSettlement', //编辑生成结算单
    'viewGenerateSett', //查看生成结算单
    'editGenerateStatement', //编辑对账单
    'editRepairStatement', //编辑维修对账单
    'viewGenerateStatement', //查看对账单
    'viewRepairGenerateStatement', //查看维修结算单
    'editGenerateSubsidyStatement', //编辑外协对账单
    'viewGenerateSubsidyStatement', //查看外协对账单
    'editOpenInvoiceManagment', //编辑开票管理
    'editOpenInvoiceRepairManagement', //维修编辑开票管理
    'outFleetAuditSettingCopy', //复制审批流程
    'editOpenInvoiceOutsourcingManagement', //外协编辑开票管理
    'editFine', //新增
    'editQualityLoss', //质损编辑
    'editOutsourcingPayment', //编辑外协付款
    'editSubsidyPayment', //编辑补贴付款
    'paymentPart', //外协付款明细
    'resignationEmployeeManagement', //离职
    'editFleetReport', //编辑车队维修
    'viewFleetReport', //查看车队维修
    'editSupplierRepair', //编辑供应商维修
    'adjustCostSupplierRepair', //调整费用供应商维修
    'editEmployeeManagement', //编辑工资管理
    'dynamicAuditDetail', //动态详情
    'columnDynamicAuditDetail', //数据列动态详情
    'unBindTire', //轮胎列表-解绑
    'modifyCustomerIncome', //客户结算-运费计算-调整收款
    'compareModifyCustomerIncome', //客户结算-运费计算-调整收款-对比
    'modifyCarrierPayment', //外协结算-运费计算-调整收款
    'editInspectionConfiguration', //编辑配置
    'copyInspectionConfiguration', //复制配置
    'editOrderManagement', //订单管理-编辑
    'formulaCustomerContract', //客户阶梯价格
    'formulaOutsourcingContract', //外协阶梯价格
    'showRowMenuFormDialog', //弹窗显示表单
    'viewReconciliationPayment', //
    'editSettlementAmount', //编辑对账金额
    'importFile', //车队管理-里程管理-导入模版
    'resetQuery', //重置查询，刷新列表
    'sort-change', // 添加排序事件
    'oilLoanEdit', // 油费借支
    'etcLoanEdit', // 路桥费借支
  ])
  /**
   * 序号
   * @param index 序号从1开始
   */
  const indexMethod = (index: number) => {
    return index + 1
  }
  /**
   * 选中的列表项
   */
  const selectRowIds = ref<any[]>([])
  const selectRowData = ref<any[]>([])
  /**
   * 选中的列表项
   * @param e 选中的列表数组
   */
  const handleSelectionChange = (e: any[]) => {
    selectRowIds.value = e.map((item) => item.rowIndex)
    selectRowData.value = e

    emit('batchDelete', e)
    emit('handleSelectionChange', e)
    totalSummary()
  }
  //选中行的合计
  const selectedRowSummary = ref<any>({})
  //计算选中行的合计
  const totalSummary = () => {
    const objKeys = Object.keys(props.totalSummary || props.pageSummary || {})
    if (objKeys.length > 0) {
      objKeys.forEach((key) => {
        selectedRowSummary.value[key] = Number(selectRowData.value.reduce((acc, item) => acc + (Number(item[key]) || 0), 0)).toFixed(2)
      })
    }
  }
  /**
   * 修改状态
   */
  function handleStatusChange(row: { [key: string]: any }) {
    //更新状态
    emit('handleStatusChange', row)
  }

  /**
   * 操作
   * @param type 类型 add、edit、delete、view、auth、password
   * @param row 行数据
   * @param position  按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   * @param meta 获取按钮中meta中的uri,如果存在，则使用按钮的uri,没有则使用页面级uri
   * @param menu
   */
  const operation = async (type: string | undefined, row: any, position?: string, meta?: MetaVO, menu?: MenuVO) => {
    //设置表单中是否需要展示地图，mapType=geoFence 的时候显示地图
    buttonGroupStore.setShowMap(meta!.mapType!)
    let query: { [key: string]: any } = {}
    const storeData = sideBarStore.$state.storeDialogFormParams || {}
    const storeFormData = sideBarStore.$state.mergeDialogFormParams || {}
    //获取需要传递的formData中的属性保存到store中
    let mergeDialogFormParams: { [key: string]: any } = {}
    let storeDialogFormParams: { [key: string]: any } = {}
    if (meta!.form?.query) {
      for (let item of meta!.form?.query!) {
        composeRequestParams(query, item, menu, storeData, row, searchParams.value)
      }
    }
    //需要传递的formData中的属性保存到store中
    if (meta!.form?.formData) {
      for (let item of meta!.form!.formData!) {
        composeRequestParams(mergeDialogFormParams, item, menu, storeData, row, searchParams.value)
      }
    }
    //需要传递的storeData中的属性保存到store中
    if (meta!.form?.storeData) {
      for (let item of meta!.form!.storeData!) {
        composeRequestParams(storeDialogFormParams, item, menu, storeData, row, searchParams.value)
      }
    }
    sideBarStore.$patch((state) => {
      state.btnMenuId = meta!.form?.menuId!
      state.btnMenuQuery = query
      state.mergeDialogFormParams = Object.assign(storeFormData, mergeDialogFormParams) //获取需要传递的formData中的属性保存到store中
      state.storeDialogFormParams = Object.assign(storeData, storeDialogFormParams) //获取需要传递的全局存储的属性保存到store中
    })
    switch (type) {
      case 'topAdd':
        emit('addItem', row, position, meta)
        break
      //修改当前行
      case 'topEdit':
        emit('updateItem', row, position, meta)
        break
      //修改当前行,调接口
      case 'editById':
        emit('editById', row, position, menu)
        break
      //删除当前行
      case 'delete':
        ElMessageBox.confirm('确认删除已选中的数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          emit('deleteItem', row)
        })
        break
      //查看
      case 'view':
        emit('addItem', row, position, meta)
        break
      //操作权限
      case 'assignMenuPermission':
        emit('openDataColumnMenuDialog', row)
        break
      //字段权限
      case 'assignDataColumnPermission':
        emit('openMenuDialog', row)
        break
      //修改密码
      case 'password':
        emit('updatePassword', row)
        break
      //查看位置
      case 'viewPosition':
        emit('viewPosition', row)
        break
      //确认
      case 'confirm':
        emit('confirm', row, position, meta)
        break
      //自定义提示弹窗
      case 'confirmDialog':
        if (meta?.form?.confirmContent) {
          ElMessageBox.confirm(meta?.form?.confirmContent, meta?.form?.confirmTitle, {
            confirmButtonText: meta?.form?.btns![0].label,
            cancelButtonText: meta?.form?.btns![1].label,
            showCancelButton: !!meta?.form?.btns![1],
            showConfirmButton: !!meta?.form?.btns![0],
            type: 'warning',
          }).then(() => {
            emit('confirmDialog', row, position, meta)
          })
        } else {
          emit('confirmDialog', row, position, meta)
        }
        break
      //取消
      case 'circleCloseFilled':
        ElMessageBox.confirm('确认取消已选中的数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          emit('cancel', row, position, meta)
        })
        break
      //跳转页面
      case 'btnJump':
        let listSelect = {} as any
        //获取查询条件并跳转
        let btnJumpQuery: { [key: string]: any } = {}
        //获取需要传递的formData中的属性保存到store中
        let mergeFormParams: { [key: string]: any } = {}
        let storeFormParams: { [key: string]: any } = {}
        if (meta?.form?.formUri) {
          //定义传递的参数
          let params = {} as any
          meta?.form?.params?.map((_item) => {
            composeRequestParams(params, _item, null, null, row, null)
          })

          const resData = await globalRequestUrlApi(params, meta?.form?.method!, meta?.form?.formUri).then(async (res) => {
            return res.data
          })
          //
          //遍历listSelect，取第一项的数据
          if (meta.form?.listSelect?.length > 0) {
            if (meta.form.listSelect[0].dependFrom === 'responseData') {
              listSelect[meta.form.listSelect[0].name!] = resData[meta.form.listSelect[0].name!]
            } else if (meta.form.listSelect[0].dependFrom === 'listData') {
              if (meta.form.listSelect[0].value === '$#$#') {
                listSelect[meta.form.listSelect[0].name!] = row[meta.form.listSelect[0].name!]
              }
            }
          }

          // formStore.$patch(state => {
          //   state.defaultTableIds = listSelect; //将默认选中的列表ids存到store,切换菜单要重置
          //   state.routerParams = btnJumpQuery; //获取查询条件并跳转
          //   state.mergeFormData = mergeFormParams; //获取需要传递的formData中的属性保存到store中
          // });
          // router.push({
          //   path: meta.form?.targetField!,
          //   query: {
          //     time: new Date().getTime(), //获取当前时间戳,防止不刷新页面
          //   },
          // });
        }
        //需要传递的formData中的属性保存到store中
        if (meta!.form?.formData) {
          for (let item of meta!.form?.formData!) {
            composeRequestParams(mergeFormParams, item, null, null, row, searchParams.value)
          }
        }
        //需要传递的查询条件保存到store中
        if (meta!.form?.query) {
          for (let item of meta!.form?.query!) {
            composeRequestParams(btnJumpQuery, item, null, null, row, searchParams.value)
          }
        }
        //需要传递的storeData中的属性保存到store中
        if (meta!.form?.storeData) {
          for (let item of meta!.form?.storeData!) {
            composeRequestParams(storeFormParams, item, null, null, row, searchParams.value)
          }
        }

        formStore.$patch((state) => {
          state.defaultTableIds = listSelect //将默认选中的列表ids存到store,切换菜单要重置
          state.routerParams = btnJumpQuery //获取查询条件并跳转
          state.mergeFormData = mergeFormParams //获取需要传递的formData中的属性保存到store中
          state.storeFormParams = storeFormParams //获取需要传递的全局存储的属性保存到store中
        })
        router.push({
          path: meta!.form?.targetField!,
          query: {
            time: new Date().getTime(), //获取当前时间戳,防止不刷新页面
          },
        })
        // linkTo(row);
        break

      case 'menuDialog':
        emit('showRowMenuDialog', query, { label: meta!.form?.title }, menu)
        break
      // 下级审批操作
      case 'approvalOperation':
        emit('approvalOperation', row, position, menu)
        break
      // 编辑报表模板
      case 'customTableTemplateHeader':
        emit('customTableTemplateHeader', row, position, menu)
        break
      // 下载文件
      case 'fileDownload':
        emit('fileDownload', row, position, menu, type)
        break
      // 下载文件流
      case 'streamDownload':
        emit('fileDownload', row, position, menu, type)
        break
      // 编辑考核模板
      case 'editExamineTemplate':
        emit('editExamineTemplate', row, position, menu)
        break
      // 编辑考核项目
      case 'editExamineProject':
        emit('editExamineProject', row, position, menu)
        break
      // 复制表单
      case 'copy':
        emit('copy', row, position, menu)
        break
      // 考核项目打分
      case 'score':
        emit('score', row, position, menu)
        break
      // 考核项目发布
      case 'publish':
        ElMessageBox.confirm('是否确认发布?', '发布确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          emit('publish', row, position, meta)
        })
        break
      // 排程
      case 'toLineSchedule':
        emit('toLineSchedule', row, position, menu)
        break
      // 查看轨迹弹窗
      case 'toTrackMap':
        emit('toTrackMap', row, position, menu)
        break
      // 编辑安全标准
      case 'editSafeStandard':
        emit('editSafeStandard', row, position, menu)
        break
      // 查看安全标准
      case 'viewSafeStandard':
        emit('viewSafeStandard', row, position, menu)
        break
      // 管理适用仓库
      case 'editInspectionWarehouse':
        emit('editInspectionWarehouse', row, position, menu)
        break
      // 编辑带权限的
      case 'editAuth':
        emit('editAuth', row, position, menu)
        break
      // 查看移库计划详情
      case 'viewYkp':
        emit('viewYkp', row, position, menu)
        break
      // 查看约束调整
      case 'viewPlanLimit':
        emit('viewPlanLimit', row, position, menu)
        break
      // 长安民生 - 规划详情
      case 'transportPlanDetail':
        emit('transportPlanDetail', query, { label: '规划详情' })
        break
      // 长安民生 - 查看失败信息
      case 'viewInfo':
        emit('viewInfo', row, position, menu)
        break
      // 编辑审批流程
      case 'outFleetAuditSettingEdit':
        emit('outFleetAuditSettingEdit', row, position, menu)
        break
      // 编辑审批流程
      case 'editSubsidy':
        emit('editSubsidy', row, position, menu)
        break
      // 智能调度-编辑调度
      case 'editDispatch':
        emit('editDispatch', row, position, menu)
        break
      // 财务管理-司机补贴-补贴对账-编辑
      case 'editGenerateSettlement':
        emit('editGenerateSettlement', row, position, menu)
        break
      // 财务管理-客户结算-对账管理-编辑
      case 'editGenerateStatement':
        emit('editGenerateStatement', row, position, menu)
        break
      // 财务管理-维修结算-对账管理-编辑
      case 'editRepairStatement':
        emit('editRepairStatement', row, position, menu)
        break
      // 财务管理-外协结算-对账管理-编辑
      case 'editGenerateSubsidyStatement':
        emit('editGenerateSubsidyStatement', row, position, menu)
        break
      //财务管理-客户结算-开票管理-发票管理
      case 'editOpenInvoiceManagment':
        emit('editOpenInvoiceManagment', row, position, menu)
        break
      //财务管理-维修结算-开票管理-发票管理
      case 'editOpenInvoiceRepairManagement':
        emit('editOpenInvoiceRepairManagement', row, position, menu)
        break
      //系统配置-配置管理-审批流程-复制
      case 'outFleetAuditSettingCopy':
        emit('outFleetAuditSettingCopy', row, position, menu)
        break
      //外协编辑开票管理
      case 'editOpenInvoiceOutsourcingManagement':
        emit('editOpenInvoiceOutsourcingManagement', row, position, menu)
        break
      //罚款配置-编辑
      case 'editFine':
        emit('editFine', row, position, menu)
        break
      // 质损编辑
      case 'editQualityLoss':
        emit('editQualityLoss', row, position, menu)
        break
      // 外协付款-编辑
      case 'editOutsourcingPayment':
        emit('editOutsourcingPayment', row, position, menu)
        break
      // 补贴付款-编辑
      case 'editSubsidyPayment':
        emit('editSubsidyPayment', row, position, menu)
        break
      // 行政管理-员工管理-离职
      case 'resignationEmployeeManagement':
        emit('resignationEmployeeManagement', row, position, menu)
        break
      // 车队管理-维修管理-车队维修
      case 'editFleetReport':
        emit('editFleetReport', row, position, menu)
        break
      // 车队管理-维修管理-供应商维修
      case 'editSupplierRepair':
        emit('editSupplierRepair', row, position, menu)
        break
      // 车队管理-维修管理-调整费用供应商维修
      case 'adjustCostSupplierRepair':
        emit('adjustCostSupplierRepair', row, position, menu)
        break
      // 行政管理-工资管理-编辑
      case 'editEmployeeManagement':
        emit('editEmployeeManagement', row, position, menu)
        break
      // 动态审批详情
      case 'dynamicAuditDetail':
        emit('dynamicAuditDetail', row, position, menu)
        break
      // 解绑轮胎
      case 'unBindTire':
        emit('unBindTire', row, position, menu)
        break
      // 客户结算-运费计算-调整收款
      case 'modifyCustomerIncome':
        emit('modifyCustomerIncome', row, position, menu, 'customer')
        break
      // 客户结算-运费计算-客户在线对比-调整收款-对比
      case 'compareModifyCustomerIncome':
        emit('compareModifyCustomerIncome', row, position, menu, 'customer')
        break
      // 外协结算-运费计算-调整收款
      case 'modifyCarrierPayment':
        emit('modifyCarrierPayment', row, position, menu, 'carrier')
        break
      // 编辑配置
      case 'editInspectionConfiguration':
        emit('editInspectionConfiguration', row, position, menu)
        break
      // 复制配置
      case 'copyInspectionConfiguration':
        emit('copyInspectionConfiguration', row, position, menu)
        break
      // 订单管理-编辑
      case 'editOrderManagement':
        emit('editOrderManagement', row, position, menu)
        break
      //客户阶梯价格
      case 'formulaCustomerContract':
        emit('formulaCustomerContract', row, position, menu)
        break
      //外协阶梯价格
      case 'formulaOutsourcingContract':
        emit('formulaOutsourcingContract', row, position, menu)
        break
      // 编辑对账金额
      case 'editSettlementAmount':
        emit('editSettlementAmount', row, position, menu)
        break
      // 油费借支
      case 'oilLoanEdit':
        emit('oilLoanEdit', row, position, menu)
        break
      // 路桥费借支
      case 'etcLoanEdit':
        emit('etcLoanEdit', row, position, menu)
        break
      // 默认操作
      default:
        emit('defaultHandle', row, position, menu)
        break
    }
  }
  const imageList = ref([])
  const picDialogComponent = ref()
  const showPic = (row: any, name: string) => {
    if (row[name]) {
      imageList.value = row[name] ? row[name].map((item: any) => item.url) : []
      picDialogComponent.value.picDialogVisible = true
    } else {
      ElMessage.warning('当前未上传证件')
    }
  }
  /**
   *
   * @param row 当前列表项
   * @param column 列
   */
  const linkTo = (row: TableItem, column: TableItem, event: Event) => {
    //查看图片
    if (column.jump?.jumpType === 'image') {
      //弹窗展示
      showPic(row, column.jump?.targetField!)
      // emit('showPic', row, column.jump?.targetField)
    }
    //下载pdf
    else if (column.jump?.jumpType === 'pdf') {
      window.open(row[column.jump?.targetField!])
    }
    //下载文件
    else if (column.jump?.jumpType === 'file') {
      window.open(row[column.jump?.targetField!])
    }
    //查看会议纪要
    else if (column.jump?.jumpType === 'meeting') {
      emit('meeting', row, column.jump?.targetField!)
    }
    //存在->跳转
    else if (column.jump?.jumpType === 'menu') {
      let query: { [key: string]: any } = {}
      if (column.jump?.query!) {
        for (let item of column.jump?.query!) {
          composeRequestParams(query, item, null, null, row, searchParams.value)
        }
      }
      //不刷新查询条件
      formStore.setIsRefreshTopQueryParams(false)
      formStore.$patch((state) => {
        state.routerParams = query
      })
      let targetField = column.jump?.targetField!
      targetField = targetField.replaceAll('/#/', '/')
      router.push({
        path: targetField,
        query: {
          time: new Date().getTime(), //获取当前时间戳,防止不刷新页面
        },
      })
    }
    //存在->先请求最新状态后再跳转
    else if (column.jump?.jumpType === 'jumpByVinStatus') {
      let params = {
        vin: row.vin,
        vehicleNo: row.vehicleNo,
        type: row.type,
      }
      globalRequestUrlApi(params, 'get', 'tms/msg/user/ref/jumpByVin').then(async (res) => {
        if (res.data) {
          let targetField = res.data?.targetField!
          targetField = targetField.replaceAll('/#/', '/')
          router.push({
            path: targetField,
            query: res.data.query,
          })
        }
      })
    }
    //先请求再跳转
    else if (column.jump?.jumpType === 'dynamicUri') {
      /******start固定传值，将参数带过去********/
      let params = {
        jumpField: column.jump.jumpField,
        jumpType: column.jump.jumpType,
        query: JSON.parse(JSON.stringify(column.jump.query)),
      }
      /******end固定传值，将参数带过去********/
      params.query!.map((_item: QueryItemVO) => {
        if (_item?.dependFrom === 'listData') {
          if (_item.value === '$#$#') {
            _item.value = row[_item.name!]
          }
        }
        if (_item?.dependFrom === 'queryForm' && searchParams.value) {
          if (_item.value === '$#$#') {
            _item.value = searchParams.value[_item.name!]
          }
        }
      })
      globalRequestApi(params, 'post', column.jump.requestUri).then(async (res) => {
        if (res.data) {
          let targetField = res.data?.targetField!
          targetField = targetField.replaceAll('/#/', '/')
          router.push({
            path: targetField,
            query: res.data.query,
          })
        }
      })
    }
    //弹窗菜单
    else if (column.jump?.jumpType === 'menuDialog') {
      //获取需要传递的formData中的属性保存到store中
      let mergeDialogFormParams: { [key: string]: any } = {}
      let storeDialogFormParams: { [key: string]: any } = {}
      const storeData = sideBarStore.$state.storeDialogFormParams || {}
      const storeFormData = sideBarStore.$state.mergeDialogFormParams || {}
      //需要传递的formData中的属性保存到store中
      if (column.jump?.formData) {
        for (let item of column.jump!.formData!) {
          composeRequestParams(mergeDialogFormParams, item, null, null, row, searchParams.value)
        }
      }
      if (column.jump?.storeData) {
        for (let item of column.jump!.storeData!) {
          composeRequestParams(storeDialogFormParams, item, null, null, row, searchParams.value)
        }
      }
      let query: { [key: string]: any } = {}
      if (column.jump?.query) {
        for (let item of column.jump!.query!) {
          composeRequestParams(query, item, null, null, row, searchParams.value)
        }
      }
      // router.push({
      //   name: column.jump?.targetField,
      //   query: query,
      // });
      sideBarStore.$patch((state) => {
        state.btnMenuId = column.jump?.menuId!
        state.btnMenuQuery = query
        state.mergeDialogFormParams = Object.assign(storeFormData, mergeDialogFormParams) //获取需要传递的formData中的属性保存到store中
        state.storeDialogFormParams = Object.assign(storeData, storeDialogFormParams) //获取需要传递的全局存储的属性保存到store中
      })
      emit('showRowMenuDialog', query, column)
    } else if (column.jump?.jumpType === 'menuFormDialog') {
      emit('showRowMenuFormDialog', row, column)
    }
    //弹窗对象
    else if (column.jump?.jumpType === 'objectDialog') {
      let storeFormParams: { [key: string]: any } = {}
      let query: { [key: string]: any } = {}
      if (column.jump?.query) {
        for (let item of column.jump?.query!) {
          composeRequestParams(query, item, null, null, row, searchParams.value)
        }
      }
      //需要传递的storeData中的属性保存到store中
      if (column.jump!.storeData) {
        for (let item of column.jump!.storeData!) {
          composeRequestParams(storeFormParams, item, null, null, row, searchParams.value)
        }
      }
      formStore.$patch((state) => {
        state.storeFormParams = storeFormParams //获取需要传递的全局存储的属性保存到store中
      })
      sideBarStore.$patch((state) => {
        state.btnMenuId = column.jump?.menuId!
        state.btnMenuQuery = query
      })
      emit('showRowObjectDialog', query, column)
    }
    //查看可配板数
    else if (column?.name === 'canAllocateCount') {
      emit('showCanAllocateDetail', row)
    }
    //外协结算-对账管理-结算单号
    else if (column.jump?.jumpType === 'viewGenerateSubsidyStatement') {
      emit('viewGenerateSubsidyStatement', row)
    }
    //客户结算-对账管理-对账单号
    else if (column.jump?.jumpType === 'viewGenerateStatement') {
      emit('viewGenerateStatement', row)
    }
    //维修结算-对账管理-对账单号
    else if (column.jump?.jumpType === 'viewRepairGenerateStatement') {
      emit('viewRepairGenerateStatement', row)
    }
    //司机补贴-补贴对账-结算单号
    else if (column.jump?.jumpType === 'viewGenerateSett') {
      emit('viewGenerateSett', row)
    }
    //车队管理-维修管理-查看车队维修
    else if (column.jump?.jumpType === 'viewFleetReport') {
      emit('viewFleetReport', row)
    }
    //财务管理-付款管理-外协付款-已支付金额明细
    else if (column.jump?.jumpType === 'paymentPart') {
      emit('paymentPart', row)
    }
    //车队管理-里程管理-导入模版
    else if (column.jump?.jumpType === 'importFile') {
      emit('importFile', row)
    }
    //列表项查看详情详情
    else if (column.jump?.jumpType === 'dynamicAuditDetail') {
      //获取需要传递的formData中的属性保存到store中
      let mergeDialogFormParams: { [key: string]: any } = {}
      let storeDialogFormParams: { [key: string]: any } = {}
      const storeData = sideBarStore.$state.storeDialogFormParams || {}
      const storeFormData = sideBarStore.$state.mergeDialogFormParams || {}
      //需要传递的formData中的属性保存到store中
      if (column.jump?.params) {
        for (let item of column.jump!.params!) {
          composeRequestParams(mergeDialogFormParams, item, null, null, row, searchParams.value)
        }
      }
      if (column.jump?.storeData) {
        for (let item of column.jump!.storeData!) {
          composeRequestParams(storeDialogFormParams, item, null, null, row, searchParams.value)
        }
      }
      let query: { [key: string]: any } = {}
      if (column.jump?.query) {
        for (let item of column.jump?.query!) {
          composeRequestParams(query, item, null, null, row, searchParams.value)
        }
      }
      const params = {
        ...query,
        ...mergeDialogFormParams,
        ...storeDialogFormParams,
      }
      emit('columnDynamicAuditDetail', row, column, params)
    }
    //零散订单-付款-对账付款
    else if (column.jump?.jumpType === 'viewReconciliationPayment') {
      emit('viewReconciliationPayment', row)
    }
    //数据列弹窗展示一条文本
    else if (column.jump?.jumpType === 'mouseOverShow') {
      mouseOverShowDialogVisible.value = true
      mouseOverShowInfo.value = {
        title: column.jump.title || column.label || '',
        data: row[column.jump.field!],
      }
    }
    //否则展示弹窗
    else {
      emit('showRowDetail', row)
    }
  }
  const getRowValue = (row: any, key: string) => {
    return row[key]
  }
  //选中当前行
  const handleCellClick = (row: any) => {
    emit('handleCellClick', row)
  }
  /**
   * 复制
   */
  const copy = (name: string) => {
    navigator.clipboard
      .writeText(name)
      .then((res) => {
        ElMessage.success('复制成功')
      })
      .catch((err) => {})
  }
  //下载文件
  const downloadFile = (item: string) => {
    window.open(item)
  }
  //自定义表头
  const showCustomTableHeader = () => {
    customTableHeaderRef.value.state.visible = true
    customTableHeaderRef.value.getCustomTableHeader(props.requestUri)
  }
  const refreshPageTableColumn = () => {
    emit('refreshPageTableColumn')
  }
  // 选择行
  const handleSelectChange = async (selectRowList: any[]) => {
    emit('handleSelectRows', selectRowList)
  }
  // 全选
  const handleSelectAllChange = async (selectRowList: any[]) => {
    await handleSelectChange(selectRowList)
  }

  // 获取可见的按钮
  const getVisibleButtons = (buttons: any[], row: any) => {
    const filteredButtons = buttons.filter(
      (item) =>
        item.meta?.operation &&
        (!item.meta.dependsOn ||
          (item.meta.dependsOn &&
            item.meta.dependsOn.every((depend: DependsOn) => {
              if (depend.dependFrom === 'listData') {
                let target = row[depend.field!]
                if (!depend.operator) {
                  depend.operator = 'eq'
                }
                let when = depend.when!
                return operatorCalculate(target, when, depend.operator)
              }
            }))) &&
        ((item.meta.purpose === 'editAuth' && row.type !== '目录') || item.meta.purpose !== 'editAuth'),
    )

    // 如果按钮数量小于等于3，显示所有按钮
    if (filteredButtons.length <= 3) {
      return filteredButtons
    }
    // 否则只显示前2个按钮
    return filteredButtons.slice(0, 2)
  }

  // 获取隐藏的按钮
  const getHiddenButtons = (buttons: any[], row: any) => {
    const filteredButtons = buttons.filter(
      (item) =>
        item.meta?.operation &&
        (!item.meta.dependsOn ||
          (item.meta.dependsOn &&
            item.meta.dependsOn.every((depend: DependsOn) => {
              if (depend.dependFrom === 'listData') {
                let target = row[depend.field!]
                if (!depend.operator) {
                  depend.operator = 'eq'
                }
                let when = depend.when!
                return operatorCalculate(target, when, depend.operator)
              }
            }))) &&
        ((item.meta.purpose === 'editAuth' && row.type !== '目录') || item.meta.purpose !== 'editAuth'),
    )

    // 如果按钮数量小于等于3，不显示更多按钮
    if (filteredButtons.length <= 3) {
      return []
    }
    // 否则返回第3个及以后的按钮
    return filteredButtons.slice(2)
  }

  /**
   * 计算表格合计行
   * @param param 表格参数
   * @returns 合计行数据
   */
  const getSummaries = (param: { columns: any[]; data: any[] }) => {
    const { columns } = param
    const { pageSummary, totalSummary } = props // 从props获取两种合计数据
    //如果pageSummary和totalSummary为空，则不显示合计行
    if (!pageSummary || !totalSummary) {
      return []
    }
    // 创建两个合计行数组
    const pageSums: any[] = [] // 当页合计行

    columns.forEach((column, index) => {
      // 处理第一列文本
      if (index === 0) {
        pageSums[index] = h('div', [h('span', '当页合计'), h('br'), h('span', '总合计'), h('br'), h('span', '选中行合计')])
        return
      }

      // 跳过特殊列
      if (['selection', 'index', 'operation'].includes(column.type)) {
        pageSums[index] = ''
        return
      }

      // 处理当页合计
      const prop = column.property
      pageSums[index] = h('div', [
        parseSummaryValue(pageSummary?.[prop]),
        h('br'),
        parseSummaryValue(totalSummary?.[prop] ?? '-'),
        h('br'),
        parseSummaryValue(selectedRowSummary.value?.[prop] ?? '-'),
      ])
    })

    // 返回两个合计行组成的二维数组
    return pageSums
  }

  // 公共数值处理方法
  const parseSummaryValue = (value: any) => {
    const num = Number(value)
    return isNaN(num) ? '-' : num
  }

  defineExpose({
    dataTableRef, //table ref
    toggleExpandAll,
  })

  // 重置表格列宽度
  const resetTableColumnWidths = () => {
    if (dataTableRef.value) {
      // 强制重新渲染表格
      refreshTable.value = false
      nextTick(() => {
        refreshTable.value = true
      })
    }
  }
  /**
   * 设置选中行样式
   */
  const tableRowClassName = ({ row, rowIndex }: { row: any; rowIndex: number }) => {
    //高亮选中行
    for (let i = 0; i < selectRowIds.value.length; i++) {
      if (row.rowIndex === selectRowIds.value[i]) {
        return 'primary-row'
      }
    }
    //把每一行的索引放进row
    row.rowIndex = rowIndex
    if (row.rowStyle) {
      return row.rowStyle.row.bgColor
    }
    return ''
  }
  /**
   * 处理单元格样式
   * @param {Object} params - { row, column, rowIndex, columnIndex }
   * @returns {Object} 样式对象
   */
  const handleCellStyle = ({ row, column }: { row: any; column: any }) => {
    // 检查行数据中是否有单元格样式信息
    if (row.rowStyle?.cell && column.property && row.rowStyle.cell[column.property]) {
      return {
        backgroundColor: row.rowStyle.cell[column.property].bgColor,
        color: row.rowStyle.cell[column.property].color,
      }
    }
    // 如果没有设置特定单元格样式，则返回默认样式
    return {}
  }
  const showFileDialog = (row: any, item: any) => {
    showFileDialogComponentRef.value.showFileDialog(row, item)
    showFileDialogComponentRef.value.state.dialogVisible = true
  }

  const videoDialogVisible = ref(false)
  const videoPlayer = ref<HTMLVideoElement | null>(null)
  const mouseOverShowDialogVisible = ref(false)
  const mouseOverShowInfo = ref({
    title: '',
    data: '',
  })
  const uploadFileRef = ref<any[]>([])

  // 播放视频
  const playVideo = (url: string) => {
    videoDialogVisible.value = true
    nextTick(() => {
      if (videoPlayer.value) {
        videoPlayer.value.src = url
        videoPlayer.value.play()
      }
    })
  }
  /**
   * 导入Excel
   * @param file 文件
   */
  const importLoading = ref(false)
  const uploadFile = useThrottleFn(async (file: any, fileList: any[], item: any, row: any) => {
    importLoading.value = true
    let uri = item.jump?.formUri
    let formDataParams = {
      id: row.id,
    }
    importFileGlobalBtnUriFun(file.raw, uri, formDataParams)
      .then(async (res: any) => {
        ElMessage.success('导入成功')
        emit('resetQuery')
      })
      .catch((err) => {})
      .finally(() => {
        importLoading.value = false
      })
    if (file && file.raw) {
      if (uploadFileRef.value[0]) {
        uploadFileRef.value[0].clearFiles()
      }
    }
  }, 3000)

  const handleExceed = (files: any[], file: any) => {
    //清除之前上传的
    if (uploadFileRef.value[0]) {
      uploadFileRef.value[0].clearFiles()
    }
  }
  const beforeUpload = (file: any) => {
    // Add any additional validation logic you want to execute before uploading
    return true
  }

  // Add sort change handler
  // const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  //   // Emit sort change event to parent component
  //   emit('sort-change', { prop, order })
  // }
  const handleSortChange = ({ column, order, prop }: { column: any; order: string; prop: string }) => {
    // 触发的排序和缓存的排序相同时，取消该字段的排序
    if (!order || state.sortField[prop] === order) {
      state.sortField[prop] = null
    } else {
      state.sortField[prop] = order
    }

    // 查找对应的表格项，检查是否有自定义排序列名
    let sortColumn = prop
    const findTableItem = (items: any[]): string => {
      for (const item of items) {
        if (item.name === prop) {
          if (item.attributes?.sortColumn) {
            return item.attributes.sortColumn
          }
          return prop
        }
        if (item.children && item.children.length > 0) {
          const childResult: string = findTableItem(item.children)
          if (childResult !== prop) {
            return childResult
          }
        }
      }
      return prop
    }

    if (props.tableConfig.tableItem && props.tableConfig.tableItem.length > 0) {
      sortColumn = findTableItem(props.tableConfig.tableItem)
    }

    let direction = undefined
    // 更新或移除对应的排序项
    const existingIndex = state.orderBys.findIndex((item) => item.column === sortColumn)

    if (existingIndex !== -1) {
      // 如果存在该列，则更新或移除
      if (!order) {
        // 取消排序，移除该项
        state.orderBys.splice(existingIndex, 1)
      } else {
        // 更新排序方向
        direction = order === 'ascending' ? 'asc' : 'desc'
        state.orderBys[existingIndex].direction = direction
      }
    } else if (order) {
      // 如果不存在且需要排序，则添加新项
      direction = order === 'ascending' ? 'asc' : 'desc'
      state.orderBys.push({
        column: sortColumn,
        direction: direction,
      })
    }
    emit('sort-change', state.orderBys) //调用后端查询接口
  }
  const handleHeadAddClass = ({ column }: { column: any }) => {
    if (state.sortField[column.property]) {
      column.order = state.sortField[column.property]
    }
    return 'sort-column'
  }
</script>
<style scoped lang="scss">
  .read-status {
    width: 8px;
    height: 8px;
    margin-right: 3px;
    background-color: darkseagreen;
    border-radius: 50%;
  }

  :deep(.el-table .el-table__header-wrapper .el-checkbox) {
    display: none;
  }

  :deep(.el-tag--dark.el-tag--success) {
    border-color: transparent !important;
  }

  :deep(.primary-row) {
    --el-table-tr-bg-color: var(--el-color-primary-light-9);
  }

  :deep(.el-table__header tr th:nth-child(1) .cell) {
    display: var(--hideFirstColumn);
  }

  // 单元格样式
  :deep(.el-table__cell) {
    position: static !important; // 解决el-image 和 el-table冲突层级冲突问题
  }

  :deep(.el-button + .el-button) {
    margin-left: 0 !important;
  }

  /* 调整合计行样式 */
  .custom-summary-table .el-table__footer td {
    background-color: #fafafa;
    font-weight: bold;
  }

  /* 第二行合计特殊样式 */
  .custom-summary-table .el-table__footer tr:nth-child(2) td {
    background-color: #e6f7ff;
  }

  /* 视频缩略图样式 */
  .video-thumbnail {
    width: 50px;
    height: 50px;
    background-color: #f5f7fa;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      background-color: #e6f7ff;

      .play-icon {
        transform: scale(1.2);
        color: var(--el-color-primary);
      }
    }

    .play-icon {
      font-size: 24px;
      color: #909399;
      transition: all 0.3s;
    }
  }

  .sort-priority {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    margin-left: 4px;
    background-color: var(--el-color-primary);
    color: white;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
  }

  :deep(.sort-column) {
    .sort-priority {
      background-color: var(--el-color-primary);
    }
  }
</style>
