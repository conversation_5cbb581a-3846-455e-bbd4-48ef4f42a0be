<template>
  <div class="vinDialog" v-show="showTextBatch" @mouseleave="close">
    <el-scrollbar max-height="400px">
      <ul class="list">
        <li v-for="(item, index) in list">{{ item }}<span @click="delOne(index)">X</span></li>
      </ul>
    </el-scrollbar>
    <div class="entry-container">
      <el-input ref="inputRef" v-model="input" style="width: 100%" :placeholder="`${targetField}`" @keydown.enter="add(input)" />
    </div>
    <div class="empty">
      <el-button :icon="Delete" type="info" link @click="empty">清空</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { Delete } from '@element-plus/icons-vue'
  const input = ref()

  const { showTextBatch, targetField, closeTextBatch } = defineProps({
    showTextBatch: {
      type: <PERSON><PERSON><PERSON>,
    },
    targetField: {
      type: String,
      required: false,
    },
    closeTextBatch: {
      type: Function,
      required: true,
    },
  })

  // 定义所有可能的分隔符
  const delimiters = [
    ',', // 英文逗号
    '，', // 中文逗号
    ';', // 分号
    '；', // 中文分号
    ' ', // 空格
    '\n', // 回车
    '\r',
    '\t',
    '	',
    '、', // 顿号
  ]
  const pattern = new RegExp(`[${delimiters.join('')}]+`, 'g')

  const list = ref<any>([])
  import { ElInput } from 'element-plus'

  const inputRef = ref<InstanceType<typeof ElInput>>()

  const add = (str: any) => {
    const splitItems = splitString(str)
    const itemsToAdd = splitItems.slice(0, splitItems.length)
    list.value = list.value.concat(itemsToAdd)
    input.value = ''
    emits('arraySent', list.value, targetField)

    nextTick(() => {
      const scrollbar = document.querySelector('.vinDialog .el-scrollbar__wrap')
      if (scrollbar) {
        scrollbar.scrollTop = scrollbar.scrollHeight
      }
    })
  }

  const close = () => {
    if (input.value && input.value.trim()) {
      add(input.value)
    }
    closeTextBatch()
    input.value = ''
    emits('arraySent', list.value, targetField)
  }

  function splitString(str: string) {
    return str.split(pattern).filter((item) => item.trim() !== '')
  }

  const delOne = (index: number) => {
    list.value.splice(index, 1)
    emits('arraySent', list.value, targetField)
  }

  const empty = () => {
    list.value = []
    emits('arraySent', list.value, targetField)
  }

  const emits = defineEmits(['arraySent'])

  // 暴露list给父组件
  defineExpose({
    list,
    empty,
  })

  watch(
    () => showTextBatch,
    (newVal) => {
      if (newVal) {
        nextTick(() => {
          inputRef.value?.focus()
        })
      }
    },
  )
</script>

<style scoped>
  .vinDialog {
    width: 100%;
    min-height: 300px;
    max-height: 600px;
    z-index: 9999;
    background-color: #fff;
    border: 0.1px solid rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
  }

  .list {
    list-style-type: none;
    padding: 0;
    margin: 0;
    align-items: center;
  }

  .list li {
    margin: 5px;
    padding-left: 5px;
    background-color: #1c94fa;
    color: #fff;
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
  }

  .list li span {
    margin-right: 10px;
    cursor: pointer;
  }

  .entry-container {
    flex: 1;
    align-items: center;
  }

  .empty {
    align-self: flex-end;
  }

  input {
    flex: 1;
  }
</style>
