<!-- 公共table -->
<template>
  <el-table
    ref="filterTable"
    v-loading="tableConfig.loading"
    :data="data"
    :element-loading-text="tableConfig.loadingText ? tableConfig.loadingText : null"
    :max-height="tableConfig.tableHeight"
    :header-cell-style="{ backgroundColor: '#FAFAFA', textAlign: 'center' }"
    :row-class-name="rowStyle"
    border
    class="tableBox"
    fit
    highlight-current-row
    style="width: 100%"
    @current-change="chooseCurrentRow"
    @selection-change="handleSelectionChange"
    @filter-change="handleFilterChange"
    @row-click="handleRowClick"
  >
    <el-table-column v-if="tableConfig.showHandleSelection" align="center" type="selection" width="50" />
    <el-table-column :fixed="tableConfig.indexLeft ? tableConfig.indexLeft : ''" align="center" label="序号" type="index" width="60">
      <template #default="scope">
        <span v-if="tableConfig.isPagination">{{ scope.$index + (tableConfig.pageNo - 1) * tableConfig.pageSize + 1 }}</span>
        <span v-else>{{ scope.$index + 1 }}</span>
      </template>
    </el-table-column>
    <template v-for="item in tableConfig.tableItem">
      <!-- 如果可以筛选 -->
      <template v-if="item.filters">
        <!-- 在途跟踪的筛选 -->
        <template v-if="item.name === 'vehicleStatusValue'">
          <el-table-column
            :key="item.name"
            :column-key="item.name"
            :filter-multiple="false"
            :filters="item.filters"
            :label="item.label"
            :min-width="item.width ? item.width : 'auto'"
            :prop="item.name"
            :show-overflow-tooltip="true"
            align="center"
          >
            <template #default="scope">
              <span
                :class="{
                  activeZero: scope.row.vehicleStatusValue === '修车中',
                  activeOne: scope.row.vehicleStatusValue === '导航中',
                  activeTwo: scope.row.vehicleStatusValue === '行驶中',
                  activeThree: scope.row.vehicleStatusValue === '停车中',
                  activeFour: scope.row.vehicleStatusValue === '北斗离线',
                  activeFour: scope.row.vehicleStatusValue === '疑似黑车',
                }"
              >
                {{ scope.row.vehicleStatusValue }}
              </span>
            </template>
          </el-table-column>
        </template>
        <!-- 正常的可筛选的 -->
        <template v-else>
          <template v-if="item.fixedLeft">
            <el-table-column
              :key="item.name + 1"
              :column-key="item.name"
              :filter-multiple="false"
              :filters="item.filters"
              :fixed="item.fixedLeft"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            />
          </template>
          <template v-else>
            <el-table-column
              :key="item.name + 1"
              :column-key="item.name"
              :filter-multiple="false"
              :filters="item.filters"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            />
          </template>
        </template>
      </template>
      <!-- </el-table-column> -->
      <!-- 不筛选 -->
      <template v-else>
        <el-table-column
          v-if="item.label == '服务状态'"
          :key="item.name"
          :label="item.label"
          :min-width="item.width ? item.width : 'auto'"
          :prop="item.name"
          :show-overflow-tooltip="true"
          align="center"
        >
          <template #default="scope">
            <span v-if="isDisabledSwitch">
              <el-switch v-model="scope.row.on" active-color="#13ce66" disabled inactive-color="#ff4949" />
            </span>
            <span v-else>
              <el-switch v-model="scope.row.on" active-color="#13ce66" inactive-color="#ff4949" @change="switchChange(scope.row)" />
            </span>
          </template>
        </el-table-column>
        <template v-else>
          <template v-if="item.operation">
            <el-table-column
              :key="item.name"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template #default="scope">
                <template v-if="tableConfig.name === 'vehicleRiskManagement'">
                  <el-button
                    v-if="scope.row.hasOwnProperty('scenePhoto') && scope.row.scenePhoto"
                    type="primary"
                    size="small"
                    link
                    @click="operation(scope.row, item.operation, scope.$index)"
                  >
                    {{ item.operation }}
                  </el-button>
                </template>
                <template v-else>
                  <!-- 是否有认证属性并且上传了驾驶证或行驶证 -->
                  <span v-if="scope.row.hasOwnProperty('haveAuth') && scope.row.haveAuth === false">未上传</span>
                  <el-button v-else type="primary" size="small" link @click="operation(scope.row, item.operation, scope.$index)">{{
                    item.operation
                  }}</el-button>
                </template>
              </template>
            </el-table-column>
          </template>
          <!-- 上下游对账 -->
          <template v-else-if="item.canCopy">
            <el-table-column
              :key="item.name"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template #default="scope">
                <div class="enclosureName" v-if="item.name === 'taskNo'">
                  <el-link type="primary" @click="linkToTaskManagement(scope.row.taskNo)">{{ scope.row.taskNo }}</el-link>
                  <i class="el-icon-copy-document" @click.stop="copyName(scope.row.taskNo)" />
                </div>
                <div class="enclosureName" v-else>
                  <span>{{ scope.row[item.name] }}</span>
                  <i class="el-icon-copy-document" @click.stop="copyName(scope.row[item.name])" />
                </div>
              </template>
            </el-table-column>
          </template>
          <!-- 自定义围栏 -->
          <template v-else-if="item.name === 'geoFenceName'">
            <el-table-column
              :key="item.name"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template #default="scope">
                <div class="enclosureName">
                  <span>{{ scope.row.geoFenceName }}</span>
                  <i class="el-icon-copy-document" @click.stop="copyName(scope.row.geoFenceName)" />
                </div>
              </template>
            </el-table-column>
          </template>
          <!-- 多路线规划算路 -->
          <template v-else-if="item.name === 'batchrRoutePlanningStatus'">
            <el-table-column
              :key="item.name"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template #default="scope">
                <span v-if="scope.row.batchrRoutePlanningStatus" style="color: green">通过</span>
                <span v-else style="color: red">异常</span>
              </template>
            </el-table-column>
          </template>
          <!-- 油费 异常 -->
          <template v-else-if="item.name === 'isExcep' && tableConfig.name === 'fuelCostStatistics'">
            <el-table-column
              :key="item.name"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template #default="scope">
                <span v-if="scope.row.isExcep" style="color: red">
                  <!-- <i class="el-icon-close"></i> -->
                  异常
                </span>
                <span v-else style="color: green">
                  <!-- <i class="el-icon-check"></i> -->
                  正常
                </span>
              </template>
            </el-table-column>
          </template>
          <!-- 人员设定-姓名后追加详情按钮 -->
          <!-- <template v-else-if="item.name === '姓名' && tableConfig.name === 'generalPersonalTable'">
                        <el-table-column
                            :key="item.name"
                            :label="item.label"
                            :fixed="item.fixedLeft"
                            :min-width="item.width ? item.width : 'auto'"
                            :prop="item.name"
                            :show-overflow-tooltip="true"
                            align="center"
                        >
                            <template #default="scope">
                                <el-link type="primary" @click="showBindingInfo(scope.row.driverId)">
                                    {{ scope.row['姓名'] }}
                                </el-link>
                            </template>
                        </el-table-column>
                    </template> -->
          <!-- 成本统计-精细统计-高速费 -->
          <template v-else-if="item.name === 'highwayCost' && tableConfig.name === 'highwayFeeStatistics'">
            <el-table-column
              :key="item.name"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template #default="scope">
                <span>{{ scope.row.highwayCost }}</span>
                <el-button
                  v-if="scope.row.highwayCost && parseFloat(scope.row.highwayCost) > 0 && scope.row.accurateHighwayType"
                  style="margin-left: 10px"
                  type="primary"
                  size="small"
                  link
                  @click="operation(scope.row, item.operation, scope.$index, tableConfig.name, item.name)"
                >
                  明细
                </el-button>
              </template>
            </el-table-column>
          </template>
          <!-- 成本统计-精细统计-国省道收费 -->
          <template v-else-if="item.name === 'guoshengCost' && tableConfig.name === 'fineStatistic'">
            <el-table-column
              :key="item.name"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template #default="scope">
                <span>{{ scope.row.guoshengCost }}</span>
                <el-button
                  v-if="scope.row.guoshengCost && parseFloat(scope.row.guoshengCost) > 0 && scope.row.accurateHighwayType"
                  style="margin-left: 10px"
                  type="primary"
                  size="small"
                  link
                  @click="operation(scope.row, item.operation, scope.$index, tableConfig.name, item.name)"
                >
                  明细
                </el-button>
              </template>
            </el-table-column>
          </template>
          <!-- 成本统计-精细统计-消耗油量 -->
          <template v-else-if="item.name === 'totalFuel' && tableConfig.name === 'totalFeeStatistics'">
            <el-table-column
              :key="item.name"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template #default="scope">
                <span>{{ scope.row.totalFuel }}</span>
                <!--                <el-button-->
                <!--                  v-if="-->
                <!--                    scope.row.totalFuel &&-->
                <!--                      parseFloat(scope.row.totalFuel) > 0 &&-->
                <!--                      scope.row.accurateFuleConsumptionType-->
                <!--                  "-->
                <!--                  link-->
                <!--                  style="margin-left: 10px"-->
                <!--                  @click="-->
                <!--                    operation(-->
                <!--                      scope.row,-->
                <!--                      item.operation,-->
                <!--                      scope.$index,-->
                <!--                      tableConfig.name,-->
                <!--                      item.name-->
                <!--                    )-->
                <!--                  "-->
                <!--                >-->
                <!--                  明细-->
                <!--                </el-button>-->
              </template>
            </el-table-column>
          </template>
          <!-- 承运商管理 车辆监控 -->
          <template v-else-if="item.name === 'monitoringStatusValue'">
            <el-table-column
              :key="item.name"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template #default="scope">
                <el-switch
                  v-model="scope.row.enableMonitorVehicle"
                  class="switchStyle"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  active-text="开启"
                  inactive-text="关闭"
                  @change="monitoringChange($event, scope.row)"
                ></el-switch>
              </template>
            </el-table-column>
          </template>
          <!-- 客户列表 || 承运商 - 报价计税方式 -->
          <template v-else-if="item.name === 'includeTaxRate'">
            <el-table-column
              :key="item.name"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template #default="scope">
                <span v-if="scope.row.includeTaxRate">含税</span>
                <span v-if="!scope.row.includeTaxRate">不含税</span>
              </template>
            </el-table-column>
          </template>
          <!-- 客户列表 查看上游收入项 start -->
          <template v-else-if="item.name === 'incomeItemsView'">
            <el-table-column
              :key="item.name"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template #default="scope">
                <el-button v-if="scope.row.incomeItems" type="primary" size="small" link @click="operation(scope.row, 'incomeItemsView', scope.$index)"
                  >查看</el-button
                >
              </template>
            </el-table-column>
          </template>
          <!-- 客户列表 查看上游收入项 end -->
          <!-- 客户列表 查看补贴标准 start -->
          <template v-else-if="item.name === 'subsidy'">
            <el-table-column
              :key="item.name"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template v-if="scope.row.subsidy" #default="scope">
                <el-button type="primary" size="small" link @click="operation(scope.row, 'subsidyView', scope.$index)">查看</el-button>
              </template>
            </el-table-column>
          </template>
          <!-- 客户列表 查看补贴标准 end -->
          <!-- 查看图片 -->
          <template v-else-if="item.name === 'img'">
            <el-table-column
              :key="item.name + 1"
              :column-key="item.name"
              :filter-multiple="false"
              :filters="item.filters"
              :label="item.label"
              :min-width="item.width ? item.width : 'auto'"
              :prop="item.name"
              :show-overflow-tooltip="true"
              align="center"
            >
              <template #default="scope">
                <el-button
                  v-if="scope.row.imageUrl || scope.row.oilImageUrl"
                  type="primary"
                  size="small"
                  link
                  @click="operation(scope.row, item.name, scope.$index)"
                  >查看</el-button
                >
              </template>
            </el-table-column>
          </template>
          <!-- 上下游对账 - 收支调整明细 -->
          <el-table-column
            align="center"
            :label="item.label"
            width="120"
            :key="item.name"
            v-else-if="item.name === 'payAdjustReason' || item.name === 'incomeAdjustReason'"
          >
            <template #default="scope">
              <el-button
                v-if="scope.row.payAdjustAmount != 0 && scope.row.payAdjustAmount != null"
                type="primary"
                size="small"
                link
                @click="adjustDetialsDialog(scope.row)"
                >调整详情</el-button
              >
              <el-button
                v-if="scope.row.incomeAdjustAmount != 0 && scope.row.incomeAdjustAmount != null"
                type="primary"
                size="small"
                link
                @click="adjustDetialsDialog(scope.row)"
                >调整详情</el-button
              >
            </template>
          </el-table-column>
          <!-- 上下游对账 start -->
          <el-table-column
            :show-overflow-tooltip="true"
            align="center"
            :label="item.label"
            v-else-if="item.name === '_vehicleInfo'"
            :key="item.name + '_vehicleInfo'"
          >
            <template #default="scope">
              <el-button type="primary" size="small" link @click="vechileOperation(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            align="center"
            :label="item.label"
            v-else-if="item.name === '_deliveryInfo'"
            :key="item.name + '_deliveryInfo'"
          >
            <template #default="scope">
              <el-button type="primary" size="small" link @click="showAddressFun(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
          <!-- 上下游对账 end -->
          <template v-else>
            <template v-if="item.fixedLeft">
              <el-table-column
                :key="item.name"
                :fixed="item.fixedLeft"
                :label="item.label"
                :min-width="item.width ? item.width : 'auto'"
                :prop="item.name"
                :show-overflow-tooltip="true"
                :sortable="item.sortable ? item.sortable : false"
                align="center"
              ></el-table-column>
            </template>
            <template v-else>
              <el-table-column
                :key="item.name"
                :label="item.label"
                :min-width="item.width ? item.width : 'auto'"
                :prop="item.name"
                :show-overflow-tooltip="true"
                :sortable="item.sortable ? item.sortable : false"
                align="center"
              ></el-table-column>
            </template>
          </template>
        </template>
      </template>
    </template>
    <el-table-column
      v-if="tableConfig.operation"
      :label="tableConfig.operation.label"
      :min-width="tableConfig.operation.width ? tableConfig.operation.width : 'auto'"
      align="center"
      class-name="small-padding fixed-width"
      fixed="right"
    >
      <template #default="scope">
        <div v-for="(item, index) in tableConfig.operation.name" :key="index" style="display: inline-block; padding: 0 5px">
          <!-- 车队设定 -->
          <el-button v-if="item === '绑定的司机'" type="primary" size="small" link @click="operation(scope.row, item, scope.$index)">
            {{ scope.row.type !== 3 ? item : '' }}
          </el-button>
          <!-- 能耗统计 查看照片 -->
          <el-button v-else-if="item === '查看照片'" type="primary" size="small" link @click="operation(scope.row, item, scope.$index)">
            {{ scope.row.imageUrl ? item : '' }}
          </el-button>
          <!-- 车队设定 -->
          <el-button v-else-if="item === '修改手机号'" type="primary" size="small" link @click="operation(scope.row, item, scope.$index)">
            {{ scope.row.type !== 3 ? item : '' }}
          </el-button>
          <el-button v-else-if="item === '离职'" type="primary" size="small" link @click="operation(scope.row, item, scope.$index)">
            {{ item }}
          </el-button>
          <el-button v-else-if="item === '驾照管理'" type="primary" size="small" link @click="operation(scope.row, item, scope.$index)">
            {{ item }}
          </el-button>
          <!-- 消息限行 -->
          <el-button v-else-if="item === '查看限行'" type="primary" size="small" link @click="operation(scope.row, item, scope.$index)">
            {{ scope.row.otherContent ? item : '' }}
          </el-button>
          <!-- 出险管理-理赔状态为已出险的 不显示删除按钮 -->
          <template v-else-if="tableConfig.name === 'vehicleRiskManagement'">
            <el-button
              v-if="item === '删除' && scope.row.whetherClaim !== '1'"
              type="primary"
              size="small"
              link
              @click="operation(scope.row, item, scope.$index)"
            >
              {{ item }}
            </el-button>
            <el-button v-if="item !== '删除'" type="primary" size="small" link @click="operation(scope.row, item, scope.$index)">
              {{ item }}
            </el-button>
          </template>

          <!-- 判断车辆列表是否是被授权 -->
          <el-button v-else type="primary" size="small" link @click="operation(scope.row, item, scope.$index)">
            {{ scope.row.vehicleListTypeName !== 3 ? item : '' }}
          </el-button>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  import bus from '@/utils/bus'
  // 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
  // 例如：import 《组件名称》 from '《组件路径》';

  // import http from '@/config/http.js'
  export default {
    // import引入的组件需要注入到对象中才能使用
    name: '',
    components: {},
    // props: ['data', 'tableConfig', 'tableHeight'],
    props: {
      data: {
        type: Array,
        default: function () {
          return []
        },
      },
      tableConfig: {
        type: Object,
        default: function () {
          return {}
        },
      },
      tableHeight: {
        type: String,
        default: function () {
          return ''
        },
      },
    },
    data() {
      // 这里存放数据
      return {
        current: null,
        isDisabledSwitch: localStorage.getItem('demoCompany'),
      }
    },

    // 监听属性 类似于data概念
    computed: {},
    // 生命周期 - 创建完成（可以访问当前this实例）
    created() {
      // eslint-disable-next-line no-extend-native
      String.prototype.bool = function () {
        return /^true$/i.test(this)
      }
      this.isDisabledSwitch = this.isDisabledSwitch ? this.isDisabledSwitch.bool() : false
      bus.emit('filterTable', 'filterTable')
    },
    // 生命周期 - 挂载完成（可以访问DOM元素）
    mounted() {},
    // 如果页面有keep-alive缓存功能，这个函数会触发
    activated() {},
    // 方法集合
    methods: {
      //点击运单号跳转到任务管理
      linkToTaskManagement(taskNo) {
        this.$router.push({
          path: '/businessManagement/taskManagement',
          query: {
            taskNo: taskNo,
          },
        })
      },
      /**
       * @description:  修改行样式
       * @param {*} row   前行
       * @param {*} rowIndex   行下标
       * @return {*}
       */
      rowStyle({ row, rowIndex }) {
        if (this.tableConfig.name === 'peoplewareFeeStatistics' && row.haveException) {
          return 'err-row'
        } else if (this.tableConfig.name === 'fuelCostStatistics' && row.isExcep) {
          return 'err-row'
        }
      },

      /**
       * @description:  操作
       * @param {*} row   前行
       * @param {*} val   按钮内容
       * @param {*} index 行下标
       * @param {*} page  页面名称
       * @param {*} name  列项
       * @return {*}
       */

      operation(row, val, index, page, name) {
        if (val === '删除') {
          this.del(row)
        } else if (val === '编辑') {
          this.$emit('showDialogVisible', row, 'edit')
          bus.emit('showDialogVisible', row, 'edit')
        } else if (val === '保养') {
          this.$emit('showBaoyangDialogVisible', row, 'baoyang')
        } else if (val === '查看照片') {
          this.$emit('showImageDialogVisible', row, 'zhaopian')
        } else if (val === '共享对象' || val === '分享') {
          this.$emit('sharedObject', row)
        } else if (val === '查看轨迹' || val === '查看路线') {
          this.$emit('showTrajectory', row, index)
        } else if (val === '围栏记录') {
          this.$emit('showManagement', row)
        } else if (val === '修改手机号') {
          this.$emit('modifyPhone', row)
        } else if (val === '离职') {
          this.$emit('departPersonnel', row)
        } else if (val === '驾照管理') {
          this.$emit('licenseManagement', row)
        } else if (val === '线路列表') {
          this.$emit('routeLineListVisible', row)
        } else if (val === 'incomeItemsView') {
          this.$emit('incomeListVisible', row)
        } else if (val === 'subsidyView') {
          this.$emit('subsidyListVisible', row)
        } else if (val === '委托人') {
          this.$emit('clientVisible', row)
        } else if (val === '数据授权') {
          this.$emit('dataGrant', row)
        } else if (val === '车辆状态') {
          this.$emit('vehicleStatusChange', row)
        } else if (val === '查看线路') {
          this.$emit('viewRoutes', row)
        } else if (val === '复制线路') {
          this.$emit('copyRoutes', row)
        } else {
          this.$emit('showDialogVisible', row, 'see', index)
          bus.emit('showDialogVisible', row, 'see', index)
        }
        if (page === 'totalFeeStatistics') {
          this.$emit('showMingxiDialog', row, name)
        }
      },
      del(row) {
        this.$confirm('此操作将永久删除该项, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.$emit('deleteItem', row)
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除',
            })
          })
      },
      // 服务状态改变
      switchChange(row) {
        this.$emit('switchType', row)
      },
      // 服务状态改变
      monitoringChange(value, row) {
        this.$emit('switchStatus', value, row)
      },
      // 单选
      chooseCurrentRow(currentRow, oldCurrentRow) {
        this.$emit('chooseCurrentRow', currentRow)
      },
      // 多选
      handleSelectionChange(val) {
        this.$emit('handleSelectionChange', val)
      },
      // 筛选
      filterHandler(value, row, column) {
        const property = column['property']
        return row[property] === value
      },
      handleFilterChange(value) {
        this.$emit('handleFilterChange', value)
      },
      // 点击当前行
      handleRowClick(value) {
        this.$emit('handleRowDetail', value)
      },
      // 点击复制围栏名称
      copyName(value) {
        navigator.clipboard.writeText(value).then((res) => {
          this.$notify({
            title: '提示',
            message: '复制成功',
            type: 'success',
          })
        })
      },
      //获取人员设定页面 人员绑定信息
      showBindingInfo(driverId) {
        this.$emit('getBindingInfoByDriverId', driverId)
      },
      // 上下游对账车辆信息明细
      vechileOperation(row) {
        this.$emit('vechileOperation', row)
      },
      //上下游对账取送地明细
      showAddressFun(row) {
        this.$emit('showAddressFun', row)
      },
      // 上下游对账收支调整明细
      adjustDetialsDialog(row) {
        this.$emit('adjustDetialsDialog', row)
      },
    },
    // mixins: [http]
  }
</script>
<style lang="scss">
  .err-row {
    background-color: #feeae5 !important;
  }

  /*@import url(); 引入公共css类*/
  .el-table--mini {
    font-size: 14px;
  }

  .tableBox > .el-table__column-filter-trigger i {
    color: #000;
    font-size: 16px;
  }

  .enclosureName {
    position: relative;
    padding-right: 24px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .enclosureName .el-icon-copy-document {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translate(0, -50%);
    padding: 4px;
  }

  .enclosureName .el-icon-copy-document:hover {
    color: blue;
  }

  .activeZero {
    color: #decb00;
  }

  .activeOne {
    color: #42b983;
  }

  .activeTwo {
    color: #102b6a;
  }

  .activeThree {
    color: #6c6c6c;
  }

  .activeFour {
    color: #ff0000;
  }
</style>

<style scoped>
  /*switch设置样式*/
  .switchStyle {
    line-height: 18px;
    margin-bottom: 3px;
  }

  .switchStyle :deep(.el-switch__label) {
    position: absolute;
    display: none;
    color: #fff;
  }

  /*打开时文字位置设置*/
  .switchStyle :deep(.el-switch__label--right) {
    z-index: 1;
    right: 22px;
  }

  /*关闭时文字位置设置*/
  .switchStyle :deep(.el-switch__label--left) {
    z-index: 1;
    left: 22px;
  }

  /*显示文字*/
  .switchStyle :deep(.el-switch__label.is-active) {
    display: block;
    width: max-content;
  }

  .switchStyle :deep(.el-switch__core) {
    width: 55px !important;
  }

  .switchStyle :deep(.el-switch__label) * {
    font-size: 12px;
  }
</style>
