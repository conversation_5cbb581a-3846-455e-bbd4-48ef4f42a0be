<!--
 * <AUTHOR> llm
 * @Date         : 2022-04-22 10:45:15
 * @LastEditors: llm
 * @LastEditTime: 2025-01-14 12:25:22
 * @FilePath     : /src/components/UploadExcel/index.vue
 * @Description  : excel导入component
-->
<template>
  <el-upload
    ref="uploadRef"
    :auto-upload="false"
    :file-list="fileList"
    :limit="1"
    :on-change="(file) => uploadExcel(file)"
    :show-file-list="false"
    action="#"
    class="ml-[12px] mr-[12px]"
    style="display: inline-block"
  >
    <el-button type="primary" :loading="loading">
      <i-ep-upload />
      {{ title }}
    </el-button>
  </el-upload>
</template>

<script>
  export default {
    props: {
      title: { default: '一键导入', type: String },
      loading: { default: false, type: Boolean },
      id: { default: 'fileExport', type: String },
      styleObject: {
        default: 'margin-left: 10px',
        type: String,
      },
      btnSize: {
        default: '',
        type: String,
      },
      btnType: {
        default: 'primary',
        type: String,
      },
    },
    data() {
      return {
        // loading: false
        fileCancel: false,
        fileList: [],
      }
    },
    methods: {
      handleUpload() {
        this.$refs.inputer.dispatchEvent(new MouseEvent('click'))
        this.$emit('importClick', true, this.id)
        window.addEventListener(
          'focus',
          () => {
            setTimeout(() => {
              // 取消逻辑处理
              this.$emit('importClick', false, this.id)
            }, 300)
          },
          { once: true },
        )
      },
      handleFileChange(e) {
        let inputDOM = this.$refs.inputer
        let file = inputDOM.files[0] // 通过DOM取文件数据
        // let size = Math.floor(file.size / 1024) //计算文件的大小
        let formData = new FormData() //new一个formData事件
        formData.append('file', file) //将file属性添加到formData里 //此时formData就是我们要向后台传的参数了
        this.$emit('importExcelFile', formData, file.name)
        // 当上传两次相同脚本的时候，就是因为两个值相等，所以不能进行onchange事件的触发。
        // 所以只需要在上传完脚本之后，将input中的值设置为空，就可以避免多次上传相同脚本的时候不能触发onchage事件。
        document.getElementById(this.id).value = null
        // 清空input值，确保下次选择的不是同一个文件 -- 上一种方式不好使了 所以用这个方法
        e.target.value = null
      },
      /**
       * 导入Excel
       * @param item
       * @param file
       */
      uploadExcel(file) {
        if (!/\.(xlsx|xls|XLSX|XLS)$/.test(file.name)) {
          ElMessage.warning('上传Excel只能为xlsx、xls格式')
          return false
        }
        this.$emit('importExcelFile', file.raw)
        this.fileList = []
      },
    },
  }
</script>

<style scoped>
  .excel-upload-input {
    display: none;
    z-index: -9999;
  }

  .drop {
    border: 2px dashed #bbb;
    width: 600px;
    height: 160px;
    line-height: 160px;
    margin: 0 auto;
    font-size: 24px;
    border-radius: 5px;
    text-align: center;
    color: #bbb;
    position: relative;
  }
</style>
