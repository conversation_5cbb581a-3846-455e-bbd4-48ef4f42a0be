<!--
 * @Author: llm
 * @Date: 2023-07-05 21:41:09
 * @LastEditors: llm
 * @LastEditTime: 2025-06-25 17:53:21
 * @Description: 顶部按钮组
 *
-->
<template>
  <el-scrollbar>
    <div class="flex-row">
      <template v-for="(item, index) in btnGroups">
        <div v-if="item.meta?.purpose === 'import'">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :file-list="fileList"
            :limit="1"
            :on-change="(file: any) => uploadExcel(item, file)"
            :show-file-list="false"
            action="#"
            class="ml-[12px] mr-[12px]"
            style="display: inline-block"
            :on-exceed="
              (files: any, fileList: any[]) => {
                handleExceed(files, fileList, item)
              }
            "
          >
            <el-button :color="item.meta.background" size="small" :loading="props.importButtonLoading">
              <i-ep-upload />
              {{ item.meta?.title }}
            </el-button>
          </el-upload>
        </div>

        <el-button
          v-else-if="item.meta?.purpose === 'print'"
          @click="handleClick(item.meta?.purpose as string, item.meta?.position as string, item)"
          :style="{ color: item.meta.background ? '#ffffff' : '#606266' }"
          :color="item.meta.background"
          :loading="item.meta?.purpose === 'print' && props.printButtonLoading"
          size="small"
        >
          {{ item.meta?.title }}
        </el-button>
        <template
          v-else-if="item.meta?.purpose === 'customTemplateDownload' || item.meta?.purpose === 'imageDownload' || item.meta?.purpose === 'templateList'"
        >
          <el-popover popper-class="no-padding-popover" placement="bottom" width="auto" trigger="click">
            <template #reference>
              <el-button
                @click="handleCustomTemplateDownload(item)"
                :icon="item.meta.icon"
                size="small"
                :style="{ color: item.meta.background ? '#ffffff' : '#606266' }"
                class="mb-6px"
                :color="item.meta.background"
              >
                {{ item.meta?.title }}
                <el-icon>
                  <i-ep-arrow-down />
                </el-icon>
              </el-button>
            </template>
            <div class="flex-col" v-loading="customTemplateLoading" element-loading-text="正在导出...">
              <div class="editClassFater" v-for="_item in templateList" :key="_item.id">
                <!-- <el-link @click="downloadCustomExcelTemplate(item.meta!, _item)">{{ _item.label }}</el-link> -->
                <div class="editClass" @click="downloadCustomExcelTemplate(item.meta!, _item)">{{ _item.label }}</div>
                <!-- <el-button
                  size="small"
                  type="primary"
                  class="w-100%"
                  @click="downloadCustomExcelTemplate(item.meta?.purpose as string, item.meta?.position as string, item)"
                >
                  {{ item.label }}
                </el-button> -->
              </div>
            </div>
          </el-popover>
        </template>
        <template v-else-if="item.meta?.purpose === 'customTemplateImport' || item.meta?.purpose === 'importList'">
          <el-popover popper-class="no-padding-popover" placement="bottom" trigger="click" width="auto">
            <template #reference>
              <el-button
                @click="handleCustomTemplateDownload(item)"
                :icon="item.meta.icon"
                :style="{ color: item.meta.background ? '#ffffff' : '#606266' }"
                :color="item.meta.background"
                size="small"
              >
                {{ item.meta?.title }}
                <el-icon>
                  <i-ep-arrow-down />
                </el-icon>
              </el-button>
            </template>
            <div class="flex-col" v-loading="importCustomExcelButtonLoading" element-loading-text="正在导入...">
              <div class="mb-5px" v-for="_item in templateList" :key="_item.value" style="width: 100%">
                <el-upload
                  class="editClassFater"
                  style="width: 100%; background: #ffffff; border-radius: 8px; text-align: center; line-height: 42px"
                  ref="uploadRef"
                  :auto-upload="false"
                  :file-list="fileCustomList"
                  :limit="1"
                  :on-change="(file: any) => uploadCustomExcel(item, _item, file)"
                  :show-file-list="false"
                  action="#"
                  :on-exceed="
                    (files: any, fileList: any[]) => {
                      handleExceed(files, fileList, _item)
                    }
                  "
                >
                  <!-- <el-link>{{ _item.label }}</el-link> -->
                  <div class="editClass">{{ _item.label }}</div>

                  <!-- <el-button type="primary" style="width: 100%" size="small" :disabled="props.importCustomExcelButtonLoading">
                    {{ _item.name }}
                  </el-button> -->
                </el-upload>
              </div>
            </div>
          </el-popover>
        </template>

        <template v-else>
          <el-button
            :data-uri="item.meta.uri || '全局的'"
            :style="{ color: item.meta.background ? '#ffffff' : '#606266' }"
            :color="item.meta.background"
            size="small"
            :disabled="
              (item.meta?.purpose === 'delete' && ids.length === 0) || //批量删除按钮
              (item.meta?.purpose === 'shipment' && ids.length === 0) || //运单录入按钮
              (item.meta?.purpose === 'updateStatement' && !statementId) || //更新对账单
              (item.meta?.purpose === 'appointment' && !ids.length) || //一键预约按钮
              (item.meta?.purpose === 'railWaterAppointment' && ids.length === 0) || //一键预约按钮
              (item.meta?.dataColumn?.filter((item: any) => item.name === 'ids').length > 0 && ids.length === 0) ||
              (item.meta?.form?.min! > 0 && ids.length === 0) //至少勾选一条
            "
            @click="handleClick(item.meta?.purpose as string, item.meta?.position as string, item)"
            :loading="
              (item.meta?.purpose === 'export' && props.exportButtonLoading) ||
              (item.meta?.purpose === 'template' && props.downloadButtonLoading) ||
              (item.meta?.purpose === 'manualDelivery' && props.showLoading) ||
              (item.meta?.purpose === 'transportPlanStart' && props.transportPlanLoading)
            "
            v-if="(item.meta?.position === 'listTop' || item.meta?.position === 'listTabTop') && item.meta?.purpose !== 'search'"
            :icon="item.meta.icon"
          >
            {{ item.meta?.title }}
          </el-button>
          <!-- <el-button icon="Refresh" @click="resetQuery" v-if="item.meta?.purpose === 'search'" size="small">重置</el-button> -->
        </template>
        <el-button
          ref="printButton"
          v-show="false"
          v-print="printObj"
          :style="{ color: item.meta!.background ? '#ffffff' : '#606266' }"
          :color="item.meta!.background"
          :loading="item.meta?.purpose === 'print' && props.printButtonLoading"
          size="small"
        >
          {{ item.meta?.title }}
        </el-button>
      </template>
    </div>
  </el-scrollbar>
</template>
<script setup lang="ts">
  // 状态管理依赖
  import { useButtonGroupStore } from '@/store/modules/buttonGroup'
  import { composeRequestParams } from '@/utils/common'

  import { useSideBarStore } from '@/store/modules/sideBar'
  import { genFileId, UploadRawFile } from 'element-plus'
  import { getFleetOrderCustomTemplateOptionApi, getTemplatePageApi } from '@/api/outboundDispatchManagement'
  const sideBarStore = useSideBarStore()

  const buttonGroupStore = useButtonGroupStore()
  const uploadRef = ref<any>()
  const fileList = ref([])
  const emit = defineEmits([
    'addItem', //新增
    'editItem', //编辑
    'handleDelete', //删除
    'exportExcel', //数据导出
    'downloadExcelTemplate', //下载导入模版
    'importExcel', //导入Excel
    'batchRead', //批量已读
    'defaultHandle', //默认操作
    'exchangeVehicle', //换车
    'filpBoard', //倒板
    'assignedVehicle', //分配车辆
    'manualDelivery', //手动下发
    'autoDelivery', //自动下发
    'manualMerge', //手动合并
    'reAllocation', //重新分配
    'updateStatement', //更新对账单
    'laneAppointment', //车道预约（自定义弹窗）
    'railWaterwayLaneAppointment', //铁水车道预约（自定义弹窗）
    'appointment', //公路批量预约（自定义弹窗）
    'railWaterAppointment', //铁水批量预约（自定义弹窗）
    'batchShipmentConfirm', //运单确认-批量录入
    'shipmentConfirm', //公路运单确认-一键确认
    'vehicleChange', //车辆变更
    'vehicleFlipBoard', //车辆倒板
    'printBillTable', //打印提货单
    'printTable', //打印
    'setCustomTableHeader', //自定义表头
    'setCustomTableTemplateHeader', //创建业务报表模板
    'addExamineTemplate', //新建考核模板
    'addExamineProject', //新建考核项目
    'meeting', //新建会议纪要
    'addSafeStandard', //新增安全标准
    'startRouteCompare', //路线对比
    'cancelRouteCompare', //取消路线对比
    // 'waitingAllocateVinWarning', //待配板划分设置
    'addLine', //新增线路
    'addSubLine', //新增联运线路
    'addYkp', //新增移库计划
    'transportPlanConfig', //设置规划参数
    'transportPlanStart', //规划方案
    'transportPlanCompare', //方案对比
    'samePlanCompare', //同方案对比
    'crossPlanCompare', //跨方案对比
    'refreshPlanList', //刷新列表
    'treeExpand', //展开折叠
    'showMenuDialog', //弹窗菜单
    'searchQuery', //查询
    'resetQuery', //重置查询
    'outFleetAuditSettingAdd', //审批设置弹窗
    'batchSchedule', //批量调度弹窗
    'emptyDispatch', //空调度弹窗
    'topBtnConfirmDialog', //弹窗确认
    'generateSettlement', //财务管理-司机补贴-补贴对账-同步生成结算单
    'generateStatement', //财务管理-客户结算-对账管理-生成对账单
    'generateRepairStatement', //财务管理-维修结算-生成结算单
    'generateSubsidyStatement', //财务管理-外协结算-对账管理-生成对账单
    'addQualityLoss', //新增质损信息
    'addFine', //新增罚款信息
    'recompute', //重新计算
    'alterLoadTime', //智能调度-变更装车时间
    'alterActualDropTime', //智能调度-变更交车时间
    'addFleetReport', //新增车队维修
    'addSupplierRepair', //新增供应商维修
    'orderTemplateConfig', //订单模板配置
    'downloadCustomExcelTemplate', //下载自定义导入模版
    'importCustomExcel', //导入自定义模板
    'addInspectionConfiguration', //新增点检标准
    'addOrderManagement', //新增订单管理
    'scatteredReceiveSettlement', //批量对账
    'scatteredReceivePayment', //对账付款-批量对账
    'vehicleHistoryMileageEdit', // 里程管理
    'oilLoanAdd', // 新增油费借支
    'etcLoanAdd', // 新增路桥费借支
    'quickChangeVehicle', //一键换车
    'AddOnRouteMaintenance', //在途维修-新增
    'addRepayment', //还款-新增
    'addRouteMileage', //新增线路里程
  ])

  const printButton = ref()
  const props = defineProps({
    printObj: {
      type: Object,
      default: () => ({}),
    },
    ids: {
      require: true,
      type: Array<string>,
      default: [],
    },
    /**
     * 对账单id
     */
    statementId: {
      require: true,
      type: String,
      default: '',
    },
    exportButtonLoading: {
      require: true,
      type: Boolean,
      default: false,
    },
    importCustomExcelButtonLoading: {
      require: true,
      type: Boolean,
      default: false,
    },
    customTemplateLoading: {
      require: true,
      type: Boolean,
      default: false,
    },
    printButtonLoading: {
      require: true,
      type: Boolean,
      default: false,
    },
    importButtonLoading: {
      require: true,
      type: Boolean,
      default: false,
    },
    downloadButtonLoading: {
      require: true,
      type: Boolean,
      default: false,
    },
    /**
     * 显示加载动画
     */
    showLoading: {
      require: true,
      type: Boolean,
      default: false,
    },
    /**
     * 规划方案
     */
    transportPlanLoading: {
      require: true,
      type: Boolean,
      default: false,
    },
    /**
     * 按钮组权限
     */
    buttonPermissionGroup: {
      require: false,
      type: Array<MenuVO>,
      default: [],
    },
    metaInfo: {
      require: false,
      type: Object as PropType<MetaVO>,
      default: {},
    },
  })
  const btnGroups = ref<MenuVO[]>([])
  watch(
    () => props.buttonPermissionGroup,
    () => {
      //按钮权限 过滤掉自定义表头按钮 definePageHeader
      btnGroups.value = props.buttonPermissionGroup.filter((item) => item.meta?.purpose !== 'definePageHeader')
    },
    {
      immediate: true,
      deep: true,
    },
  )
  //重置查询
  const resetQuery = () => {
    emit('resetQuery')
  }
  /**
   * 新增
   * @param purpose 按钮类型
   * @param position 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   * @param menu 按钮信息
   */
  const handleClick = (purpose: string, position?: string, menu?: MenuVO) => {
    //设置表单中是否需要展示地图，mapType=geoFence 的时候显示地图
    buttonGroupStore.setShowMap(menu!.meta!.mapType!)
    let query: { [key: string]: any } = {}
    const storeData = sideBarStore.$state.storeDialogFormParams || {}
    const storeFormData = sideBarStore.$state.mergeDialogFormParams || {}
    //获取需要传递的formData中的属性保存到store中
    let mergeDialogFormParams: { [key: string]: any } = {}
    let storeDialogFormParams: { [key: string]: any } = {}
    if (menu!.meta!.form?.query) {
      for (let item of menu!.meta!.form?.query!) {
        composeRequestParams(query, item, menu, storeData, null, null)
      }
    }
    //需要传递的formData中的属性保存到store中
    if (menu!.meta!.form?.formData) {
      for (let item of menu!.meta!.form!.formData!) {
        composeRequestParams(mergeDialogFormParams, item, menu, storeData, null, null)
      }
    }
    //需要传递的storeData中的属性保存到store中
    if (menu!.meta!.form?.storeData) {
      for (let item of menu!.meta!.form!.storeData!) {
        composeRequestParams(storeDialogFormParams, item, menu, storeData, null, null)
      }
    }
    sideBarStore.$patch((state) => {
      state.btnMenuId = menu!.meta!.form?.menuId!
      state.btnMenuQuery = query
      state.mergeDialogFormParams = Object.assign(storeFormData, mergeDialogFormParams) //获取需要传递的formData中的属性保存到store中
      state.storeDialogFormParams = Object.assign(storeData, storeDialogFormParams) //获取需要传递的全局存储的属性保存到store中
    })

    switch (purpose) {
      /**
       * 新增
       */
      case 'topAdd':
        emit('addItem', position, purpose, menu)
        break
      /**
       * 编辑
       */
      case 'topEdit':
        emit('editItem', position, purpose, menu)
        break
      /**
       * 查询
       */
      case 'search':
        emit('searchQuery', position, purpose, menu)
        break
      /**
       * 导出
       */
      case 'export':
        emit('exportExcel', position, purpose, menu)
        break
      /**
       * 导出
       */
      case 'template':
        emit('downloadExcelTemplate', position, purpose, menu)
        break
      /**
       * 换车
       */
      case 'switch':
        emit('exchangeVehicle', position, purpose, menu)
        break
      /**
       * 倒板
       */
      case 'connection':
        emit('filpBoard', position, purpose, menu)
        break
      /**
       * 分配车辆
       */
      case 'van':
        emit('assignedVehicle', position, purpose, menu)
        break
      /**
       * 手动下发
       */
      case 'manualDelivery':
        emit('manualDelivery', position, purpose, menu)
        break
      /**
       * 下发
       */
      case 'autoDelivery':
        emit('autoDelivery', position, purpose, menu)
        break
      /**
       * 重新分配
       */
      case 'reAllocation':
        emit('reAllocation', position, purpose, menu)
        break
      /**
       * 手动合并
       */
      case 'merge':
        emit('manualMerge', position, purpose, menu)
        break
      /**
       * 车道预约
       */
      case 'laneAppointment':
        emit('laneAppointment', position, purpose, menu)
        break
      /**
       * 铁水车道预约
       */
      case 'railWaterwayLaneAppointment':
        emit('railWaterwayLaneAppointment', position, purpose, menu)
        break
      /**
       * 公路发运-运单申请-批量预约
       */
      case 'appointment':
        emit('appointment', position, purpose, menu, '3')
        break
      /**
       * 铁水发运-运单申请-批量预约
       */
      case 'railWaterAppointment':
        emit('appointment', position, purpose, menu, '4')
        break
      /**
       * 批量删除
       */
      case 'delete':
        ElMessageBox.confirm('确认' + menu?.meta?.title + '?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          emit('handleDelete', position, purpose, menu)
        })
        break
      /**
       * 批量已读
       */
      case 'read':
        ElMessageBox.confirm('确认将勾选的消息标记为已处理状态？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          emit('batchRead', position, purpose, menu)
        })
        break
      /**
       * 弹窗确认
       */
      case 'confirmDialog':
        if (menu!.meta?.form?.confirmContent) {
          ElMessageBox.confirm(menu!.meta?.form?.confirmContent, menu!.meta?.form?.confirmTitle, {
            confirmButtonText: menu!.meta?.form?.btns![0].label,
            cancelButtonText: menu!.meta?.form?.btns![1].label,
            showCancelButton: !!menu!.meta?.form?.btns![1],
            showConfirmButton: !!menu!.meta?.form?.btns![0],
            type: 'warning',
          }).then(() => {
            emit('topBtnConfirmDialog', position, purpose, menu)
          })
        } else {
          emit('topBtnConfirmDialog', position, purpose, menu)
        }
        break
      /**
       * 更新对账单
       */
      case 'updateStatement':
        ElMessageBox.confirm('确认更新当前选中对账单?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          emit('updateStatement', position, purpose, menu)
        })
        break
      /**
       * 运单确认-批量录入
       */
      case 'batchShipmentConfirm':
        emit('batchShipmentConfirm', position, purpose, menu)
        break
      /**
       * 公路运单确认-一键确认
       */
      case 'roadShipmentConfirm':
        emit('shipmentConfirm', position, purpose, menu, 3)
        break
      /**
       * 铁水运单确认-一键确认
       */
      case 'railWaterShipmentConfirm':
        emit('shipmentConfirm', position, purpose, menu, 4)
        break
      /**
       * 分拨运单确认-一键确认
       */
      case 'shipmentConfirm':
        emit('shipmentConfirm', position, purpose, menu, 9)
        break
      /**
       * 公路发运-短驳-车辆变更
       */
      case 'independentShortBarge':
        emit('vehicleChange', position, purpose, menu, 3, 1)
        break
      /**
       * 公路发运-干线倒板-车辆变更
       */
      case 'independenFlipBoard':
        emit('vehicleChange', position, purpose, menu, 3, 2)
        break
      /**
       * 公路发运-干线直发-车辆变更
       */
      case 'independentMainLine':
        emit('vehicleChange', position, purpose, menu, 3, 3)
        break
      /**
       * 公路发运-运单查询-车辆变更
       */
      case 'independentWaybillQuery':
        emit('vehicleChange', position, purpose, menu, 3, 100)
        break
      /**
       * 铁水发运-短驳运单-车辆变更
       */
      case 'independentRailWaterShortBarge':
        emit('vehicleChange', position, purpose, menu, 4, 1)
        break
      /**
       * 分拨发运-短驳运单-车辆变更
       */
      case 'independentAllocateShortBarge':
        emit('vehicleChange', position, purpose, menu, 9, 1)
        break
      /**
       * 分拨发运-干线倒板-车辆变更
       */
      case 'independentAllocateFlipBoard':
        emit('vehicleChange', position, purpose, menu, 9, 2)
        break
      /**
       * 分拨发运-干线直发-车辆变更
       */
      case 'independentAllocateMainLine':
        emit('vehicleChange', position, purpose, menu, 9, 3)
        break
      /**
       * 分拨发运-运单查询-车辆变更
       */
      case 'independentAllocateWaybillQuery':
        emit('vehicleChange', position, purpose, menu, 9, 100)
        break
      /**
       * 公路发运-干线倒板-车辆倒板
       */
      case 'fbFlipBoard':
        emit('vehicleFlipBoard', position, purpose, menu, 3, 2)
        break
      /**
       * 公路发运-干线直发-车辆倒板
       */
      case 'fbMainLine':
        emit('vehicleFlipBoard', position, purpose, menu, 3, 3)
        break
      /**
       * 公路发运-运单查询-车辆倒板
       */
      case 'fbWaybillQuery':
        emit('vehicleFlipBoard', position, purpose, menu, 3, 100)
        break
      /**
       * 分拨发运-干线倒板-车辆倒板
       */
      case 'fbAllocateFlipBoard':
        emit('vehicleFlipBoard', position, purpose, menu, 9, 2)
        break
      /**
       * 分拨发运-干线直发-车辆倒板
       */
      case 'fbAllocateMainLine':
        emit('vehicleFlipBoard', position, purpose, menu, 9, 3)
        break
      /**
       * 分拨发运-运单查询-车辆倒板
       */
      case 'fbAllocateWaybillQuery':
        emit('vehicleFlipBoard', position, purpose, menu, 9, 100)
        break
      /**
       * 打印提货单
       */
      case 'print':
        emit('printBillTable', position, purpose, menu)
        break
      /**
       * 打印表格
       */
      case 'printTable':
        emit('printTable', position, purpose, menu)
        break
      /**
       * 业务报表自定义表头弹窗
       */
      case 'customTableBusinessHeader':
        emit('setCustomTableHeader', position, purpose, menu)
        break
      /**
       * 业务报表自定义表头弹窗
       */
      case 'financeReport':
        emit('setCustomTableHeader', position, purpose, menu)
        break
      /**
       * 创建业务报表模板
       */
      case 'customTableTemplateHeader':
        emit('setCustomTableTemplateHeader', position, purpose, menu)
        break
      /**
       * 新建考核模板
       */
      case 'addExamineTemplate':
        emit('addExamineTemplate', position, purpose, menu)
        break
      /**
       * 新建考核项目
       */
      case 'addExamineProject':
        emit('addExamineProject', position, purpose, menu)
        break
      /**
       * 新建业务会议纪要
       */

      case 'businessMeeting':
        emit('meeting', position, purpose, menu, 1)
        break
      /**
       * 新建财务会议纪要
       */
      case 'financeMeeting':
        emit('meeting', position, purpose, menu, 2)
        break
      /**
       * 新增安全标准
       */
      case 'addSafeStandard':
        emit('addSafeStandard', position, purpose, menu)
        break
      /**
       * 路线对比
       */
      case 'startRouteCompare':
        emit('startRouteCompare', position, purpose, menu)
        break
      /**
       * 取消路线对比
       */
      case 'cancelRouteCompare':
        emit('cancelRouteCompare', position, purpose, menu)
        break
      /**
       * 待配板划分设置
       */
      // case 'waitingAllocateVinWarning':
      //   emit('waitingAllocateVinWarning', position, purpose, menu);
      //   break;
      /**
       * 线路管理-新增线路
       */
      case 'addLine':
        emit('addLine', position, purpose, menu)
        break
      /**
       * 线路管理-联运线路-新增线路
       */
      case 'addSubLine':
        emit('addSubLine', position, purpose, menu)
        break
      /**
       * 仓储中心-移库管理-新增移库计划
       */
      case 'addYkp':
        emit('addYkp', position, purpose, menu)
        break
      /**
       * 长安民生 - 设置规划参数
       */
      case 'transportPlanConfig':
        emit('transportPlanConfig', position, purpose, menu)
        break
      /**
       * 长安民生 - 规划方案
       */
      case 'transportPlanStart':
        emit('transportPlanStart', position, purpose, menu)
        break
      /**
       * 长安民生 - 方案对比
       */
      case 'transportPlanCompare':
        emit('transportPlanCompare', position, purpose, menu)
        break
      /**
       * 长安民生 - 同方案对比
       */
      case 'samePlanCompare':
        emit('samePlanCompare', position, purpose, menu)
        break
      /**
       * 长安民生 - 跨方案对比
       */
      case 'crossPlanCompare':
        emit('crossPlanCompare', position, purpose, menu)
        break
      /**
       * 长安民生 - 刷新列表
       */
      case 'refreshPlanList':
        emit('refreshPlanList', position, purpose, menu)
        break
      /**
       * 展开折叠
       */
      case 'treeExpand':
        emit('treeExpand', position, purpose, menu)
        break
      /**
       * 弹窗菜单
       */
      case 'menuDialog':
        emit('showMenuDialog', position, purpose, menu)
        break
      /**
       * 新增审批设置流程
       */
      case 'outFleetAuditSettingAdd':
        emit('outFleetAuditSettingAdd', position, purpose, menu)
        break
      /**
       * 智能调度-批量调度
       */
      case 'batchSchedue':
        emit('batchSchedule', position, purpose, menu)
        break
      /**
       * 智能调度-创建空调度单
       */
      case 'emptyDispatch':
        emit('emptyDispatch', position, purpose, menu)
        break
      /**
       * 财务管理-司机补贴-补贴对账-同步生成结算单
       */
      case 'generateSettlement':
        emit('generateSettlement', position, purpose, menu)
        break
      /**
       * 财务管理-客户结算-对账管理-生成对账单
       */
      case 'generateStatement':
        emit('generateStatement', position, purpose, menu)
        break
      /**
       * 财务管理-维修结算-生成结算单
       */
      case 'generateRepairStatement':
        emit('generateRepairStatement', position, purpose, menu)
        break
      /**
       * 财务管理-外协结算-对账管理-生成对账单
       */
      case 'generateSubsidyStatement':
        emit('generateSubsidyStatement', position, purpose, menu)
        break
      /**
       * 新增质损信息
       */
      case 'addQualityLoss':
        emit('addQualityLoss', position, purpose, menu)
        break
      /**
       * 新增罚款信息
       */
      case 'addFine':
        emit('addFine', position, purpose, menu)
        break
      /**
       * 重新计算
       */
      case 'recompute':
        emit('recompute', position, purpose, menu)
        break
      /**
       * 智能调度-变更装车时间
       */
      case 'alterLoadTime':
        emit('alterLoadTime', position, purpose, menu)
        break
      /**
       * 智能调度-变更交车时间
       */
      case 'alterActualDropTime':
        emit('alterActualDropTime', position, purpose, menu)
        break
      /**
       * 新增车队维修
       */
      case 'addFleetReport':
        emit('addFleetReport', position, purpose, menu)
        break
      /**
       * 新增供应商维修
       */
      case 'addSupplierRepair':
        emit('addSupplierRepair', position, purpose, menu)
        break
      /**
       * 订单模板配置
       */
      case 'orderTemplateConfig':
        emit('orderTemplateConfig', position, purpose, menu)
        break
      /**
       * 新增点检标准
       */
      case 'addInspectionConfiguration':
        emit('addInspectionConfiguration', position, purpose, menu)
        break
      /**
       * 新增订单
       */
      case 'addOrderManagement':
        emit('addOrderManagement', position, purpose, menu)
        break

      /**
       * 批量对账
       */
      case 'scatteredReceiveSettlement':
        emit('scatteredReceiveSettlement', position, purpose, menu)
        break
      /**
       * 对账付款-批量对账
       */
      case 'scatteredReceivePayment':
        emit('scatteredReceivePayment', position, purpose, menu)
        break
      /**
       * 历程管理-历史-编辑
       */
      case 'vehicleHistoryMileageEdit':
        emit('vehicleHistoryMileageEdit', position, purpose, menu)
        break
      /**
       * 财务管理-借支管理-油费管理-油费列表-新增借支
       */
      case 'oilLoanAdd':
        emit('oilLoanAdd', position, purpose, menu)
        break
      /**
       * 财务管理-借支管理-路桥费管理-路桥费列表-新增借支
       */
      case 'etcLoanAdd':
        emit('etcLoanAdd', position, purpose, menu)
        break
      /**
       * 智能调度一键换车
       */
      case 'quickChangeVehicle':
        emit('quickChangeVehicle', position, purpose, menu)
        break
      /**
       * 在途维修-新增
       */
      case 'AddOnRouteMaintenance':
        emit('AddOnRouteMaintenance', position, purpose, menu)
        break
      /**
       * 还款-新增
       */
      case 'add-repayment':
        emit('addRepayment', position, purpose, menu)
        break
      /**
       * 还款-新增
       */
      case 'addRouteMileage':
        emit('addRouteMileage', position, purpose, menu)
        break
      /**
       * 默认操作
       */
      default:
        emit('defaultHandle', position, purpose, menu)
        break
    }
  }
  /**
   * 导入Excel
   * @param item
   * @param file
   */
  const uploadExcel = (item: MenuVO, file: any) => {
    if (!/\.(xlsx|xls|XLSX|XLS)$/.test(file.name)) {
      ElMessage.warning('上传Excel只能为xlsx、xls格式')
      return false
    }
    emit('importExcel', file.raw, item.meta)
    fileList.value = []
  }
  /**
   * 超过限制
   */
  const handleExceed = (files: any, fileList: any[], item: TableItem) => {
    uploadRef.value[0]!.clearFiles()
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    uploadRef.value[0]!.handleStart(file)
  }
  const templateList = ref<any[]>([])
  /**
   * 下载自定义模板
   */
  const handleCustomTemplateDownload = async (item: TableItem) => {
    try {
      let uri = null
      //默认是templateList
      if (item.meta?.purpose === 'templateList' || item.meta?.purpose === 'importList') {
        uri = props.metaInfo?.uri + '/template/option'
      } else {
        uri = item.meta.uri
      }
      const { data } = await getFleetOrderCustomTemplateOptionApi({}, uri)
      templateList.value = data as any[]
    } catch (error) {
    } finally {
    }
  }
  /**
   * 下载自定义模板
   */
  const downloadCustomExcelTemplate = (meta: MetaVO, item?: any) => {
    emit('downloadCustomExcelTemplate', meta, item)
  }
  const fileCustomList = ref([])
  /**
   * 上传自定义模板
   */
  const uploadCustomExcel = (menu: MenuVO, _item: any, file: any) => {
    console.log('%c [ menu ] -> ', 'font-size:16px; background:#43badd; color:#87feff;', menu)
    if (!/\.(xlsx|xls|XLSX|XLS)$/.test(file.name)) {
      ElMessage.warning('上传Excel只能为xlsx、xls格式')
      return false
    }
    emit('importCustomExcel', file.raw, menu, _item)
    fileCustomList.value = []
  }
  defineExpose({
    printButton,
  })
</script>

<style scoped lang="scss">
  :deep(.el-upload) {
    width: 100% !important;
  }
  .editClassFater {
    background: #ffffff;
    border-radius: 8px;
    text-align: center;
    line-height: 42px;
  }
  .editClass {
    width: 100%;
    height: 42px;
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 400;
    color: #333333;
    text-align: center;
    font-style: normal;
  }
  .editClass:hover {
    background: #f7f9fa;
  }
</style>
<style>
  /* 必须无scoped */
  .no-padding-popover {
    padding: 0 !important;
    border-radius: 4px;
  }
</style>
