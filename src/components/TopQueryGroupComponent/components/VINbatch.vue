<!--
 * @Author: llm
 * @Date: 2025-03-28 14:08:44
 * @LastEditors: llm
 * @LastEditTime: 2025-05-09 10:45:50
 * @Description: 
-->
<template>
  <div class="vinDialog" v-show="showTextBatch" @mouseleave="close">
    <el-scrollbar max-height="400px">
      <ul class="list">
        <li v-for="(item, index) in list">{{ item }}<span @click="delOne(index)">X</span></li>
      </ul>
    </el-scrollbar>
    <div class="entry-container">
      <el-input ref="inputRef" v-model="input" style="width: 100%" :placeholder="`请输入${targetField.label}`" @keydown.enter="add(input)" />
    </div>
    <div class="empty">
      <el-button :icon="Delete" type="info" link @click="empty">清空</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ElInput } from 'element-plus'
  import { Delete } from '@element-plus/icons-vue'
  const input = ref()

  const { showTextBatch, targetField, closeTextBatch, initialValue } = defineProps({
    initialValue: {
      type: String,
      default: '',
    },
    showTextBatch: {
      type: Boolean,
    },
    targetField: {
      type: Object as PropType<{
        max: number
        message: string
        name?: string
        label?: String
      }>,
      required: true,
    },
    closeTextBatch: {
      type: Function,
      required: true,
    },
  })

  // 定义所有可能的分隔符
  const delimiters = [
    ',', // 英文逗号
    '，', // 中文逗号
    ';', // 分号
    '；', // 中文分号
    ' ', // 空格
    '\n', // 回车
    '\r',
    '\t',
    '	',
    '、', // 顿号
  ]
  const pattern = new RegExp(`[${delimiters.join('')}]+`, 'g')

  const list = ref<any>(initialValue ? initialValue.split(',') : [])
  // 监听initialValue变化
  watch(
    () => initialValue,
    (newVal) => {
      if (newVal) {
        list.value = newVal.split(',')
      } else {
        list.value = []
      }
    },
    { immediate: true },
  )

  const inputRef = ref<InstanceType<typeof ElInput>>()

  const add = (str: any) => {
    const splitItems = splitString(str)
    // 过滤掉已存在的项目，避免重复添加
    const uniqueItems = splitItems.filter((item) => !list.value.includes(item))

    if (targetField.max) {
      const remainingSpace = targetField.max - list.value.length
      if (remainingSpace <= 0) {
        ElMessage.error(targetField.message)
        return
      }
      const itemsToAdd = uniqueItems.slice(0, remainingSpace)
      list.value = list.value.concat(itemsToAdd)
      // 如果有被截断的数据，提示用户
      if (uniqueItems.length > remainingSpace) {
        ElMessage.warning(`已添加${remainingSpace}条数据，超出部分未添加`)
      }
    } else {
      list.value = list.value.concat(uniqueItems)
    }
    input.value = ''
    emits('arraySent', list.value, targetField)

    nextTick(() => {
      const scrollbar = document.querySelector('.vinDialog .el-scrollbar__wrap')
      if (scrollbar) {
        scrollbar.scrollTop = scrollbar.scrollHeight
      }
    })
  }

  const close = () => {
    if (input.value && input.value.trim()) {
      add(input.value)
    }
    closeTextBatch()
    input.value = ''
    emits('arraySent', list.value, targetField)
  }

  function splitString(str: string) {
    return str.split(pattern).filter((item) => item.trim() !== '')
  }

  const delOne = (index: number) => {
    list.value.splice(index, 1)
    emits('arraySent', list.value, targetField)
  }

  const empty = () => {
    list.value = []
    emits('arraySent', list.value, targetField)
  }

  const emits = defineEmits(['arraySent'])

  // 暴露list给父组件
  defineExpose({
    list,
  })

  watch(
    () => showTextBatch,
    (newVal) => {
      if (newVal) {
        nextTick(() => {
          inputRef.value?.focus()
        })
      }
    },
  )
</script>

<style scoped>
  .vinDialog {
    width: 100%;
    min-height: 300px;
    max-height: 600px;
    z-index: 9999;
    background-color: #fff;
    border: 0.1px solid rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
  }

  .list {
    list-style-type: none;
    padding: 0;
    margin: 0;
    align-items: center;
  }

  .list li {
    margin: 5px;
    padding-left: 5px;
    background-color: #1c94fa;
    color: #fff;
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
  }

  .list li span {
    margin-right: 10px;
    cursor: pointer;
  }

  .entry-container {
    flex: 1;
    align-items: center;
  }

  .empty {
    align-self: flex-end;
  }

  input {
    flex: 1;
  }
</style>
