<template>
  <el-scrollbar max-height="200px">
    <el-tree :data="treeData" :props="defaultProps" default-expand-all node-key="value" :expand-on-click-node="false" ref="tree">
      <template #default="{ node, data }">
        <template v-if="node.level === 1">
          <el-checkbox v-model="checkedNodes" :label="data.value" @change="handleCheckboxChange(data, node)">
            {{ node.label }}
          </el-checkbox>
        </template>
        <template v-else>
          <el-radio-group v-model="selectedNodes[node.parent.data.value]" @change="handleRadioChange(node.parent.data.value)">
            <el-radio :label="data.value">{{ node.label }}</el-radio>
          </el-radio-group>
        </template>
      </template>
    </el-tree>
  </el-scrollbar>
</template>

<script setup>
  const props = defineProps({
    treeData: {
      type: Array,
      required: true,
    },
  })

  const emit = defineEmits(['update:selectedNodes'])

  const defaultProps = {
    children: 'children',
    label: 'label',
  }

  const checkedNodes = ref([])
  const selectedNodes = ref({})

  watch(props.treeData, (newValue) => {
    // Initialize selectedNodes when treeData changes
    // selectedNodes.value = newValue.reduce((acc, node) => {
    //   acc[node.value] = node.children && node.children.length > 0 ? node.children[0].value : null;
    //   return acc;
    // }, {});
  })

  const tree = ref(null)

  const handleCheckboxChange = (data, node) => {
    if (checkedNodes.value.includes(data.value)) {
      if (data.children && data.children.length > 0) {
        selectedNodes.value[data.value] = data.children[0].value
        tree.value.store.nodesMap[data.value].expanded = true
      }
    } else {
      clearChildSelections(data.children)
      if (selectedNodes.value[data.value]) {
        delete selectedNodes.value[data.value]
      }
    }
  }

  const clearChildSelections = (children) => {
    if (!children) return
    children.forEach((child) => {
      if (selectedNodes.value[child.value]) {
        delete selectedNodes.value[child.value]
      }
      if (checkedNodes.value.includes(child.value)) {
        const index = checkedNodes.value.indexOf(child.value)
        if (index > -1) {
          checkedNodes.value.splice(index, 1)
        }
      }
      if (child.children && child.children.length > 0) {
        clearChildSelections(child.children)
      }
    })
  }

  const emitSelectedNodes = () => {
    emit('update:selectedNodes', {
      checked: checkedNodes.value,
      selected: selectedNodes.value,
    })
  }

  const handleRadioChange = () => {
    const childrenIds = Object.values(selectedNodes.value)
    let selectedChildrenIds = []
    childrenIds.forEach(async (item) => {
      //遍历props.treeData,如果item在props.treeData中的children中存在，则与props.treeData项的id组成新的数据[父，子]id
      props.treeData.forEach((_item) => {
        if (_item.children) {
          _item.children.forEach((i) => {
            if (i.value === item) {
              selectedChildrenIds.push([_item.value, item])
            }
          })
        }
      })
    })
    emit('update:selectedNodes', {
      selected: selectedChildrenIds,
    })
  }
</script>

<style scoped>
  .el-tree {
    max-width: 400px;
  }
</style>
