<!--
 * <AUTHOR> llm
 * @Date         : 2022-04-22 10:45:15
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-04-28 11:08:16
 * @FilePath     : /src/components/UploadExcel/index.vue
 * @Description  : excel导入component
-->
<template>
    <span>
        <input
            :id="id"
            ref="inputer"
            accept=".xlsx, .xls"
            class="excel-upload-input"
            type="file"
            @change="handleFileChange"
        />
        <el-button :loading="loading" :style="styleObject" :size="btnSize" :type="btnType" @click="handleUpload">{{
            title
        }}</el-button>
    </span>
</template>

<script>
export default {
    props: {
        title: { default: '一键导入', type: String },
        loading: { default: false, type: Boolean },
        id: { default: 'fileExport', type: String },
        styleObject: {
          default: 'margin-left: 10px',
          type: String
        },
        btnSize: {
          default: '',
          type: String
        },
        btnType: {
          default: 'primary',
          type: String
        },
    },
    data() {
        return {
            // loading: false
            fileCancel: false
        }
    },
    methods: {
        handleUpload() {
            this.$refs.inputer.dispatchEvent(new MouseEvent('click'))
            this.$emit('importClick', true, this.id)
            window.addEventListener(
                'focus',
                () => {
                    setTimeout(() => {
                        // 取消逻辑处理
                        this.$emit('importClick', false, this.id)
                    }, 300)
                },
                { once: true }
            )
        },
        handleFileChange(e) {
            let inputDOM = this.$refs.inputer
            let file = inputDOM.files[0] // 通过DOM取文件数据
            // let size = Math.floor(file.size / 1024) //计算文件的大小
            let formData = new FormData() //new一个formData事件
            formData.append('file', file) //将file属性添加到formData里 //此时formData就是我们要向后台传的参数了
            this.$emit('importExcelFile', formData, file.name)
            // 当上传两次相同脚本的时候，就是因为两个值相等，所以不能进行onchange事件的触发。
            // 所以只需要在上传完脚本之后，将input中的值设置为空，就可以避免多次上传相同脚本的时候不能触发onchage事件。
            document.getElementById(this.id).value = null;
            // 清空input值，确保下次选择的不是同一个文件 -- 上一种方式不好使了 所以用这个方法
            e.target.value = null;
        }
    }
}
</script>

<style scoped>
.excel-upload-input {
    display: none;
    z-index: -9999;
}

.drop {
    border: 2px dashed #bbb;
    width: 600px;
    height: 160px;
    line-height: 160px;
    margin: 0 auto;
    font-size: 24px;
    border-radius: 5px;
    text-align: center;
    color: #bbb;
    position: relative;
}
</style>
