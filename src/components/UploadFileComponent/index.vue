<!--
 * @Author: llm
 * @Date: 2025-01-17 10:31:35
 * @LastEditors: llm
 * @LastEditTime: 2025-06-26 10:25:15
 * @Description: 图片上传组件
-->
<template>
  <div>
    <el-upload
      :accept="accept"
      :show-file-list="showFileList"
      :file-list="uploadFileList"
      ref="uploadFileRef"
      action="#"
      :auto-upload="false"
      :multiple="multiple"
      :limit="limit"
      :on-change="
        (file: any, fileList: any[]) => {
          uploadFile(file, fileList)
        }
      "
      :on-remove="
        (file: any, fileList: any[]) => {
          handleRemove(file, fileList)
        }
      "
      :on-exceed="handleExceed"
      :disabled="disabled"
    >
      <template #trigger>
        <el-button type="primary" class="mr-10px">{{ btnText }}</el-button>
        <el-text v-if="title">{{ title }}</el-text>
      </template>
      <template #tip v-if="tip">
        <div>
          <el-tag type="warning">
            <div class="flex items-center">
              <el-icon>
                <Warning />
              </el-icon>
              {{ tip }}
            </div>
          </el-tag>
        </div>
      </template>
    </el-upload>
  </div>
</template>
<script setup lang="ts">
  import { globalUploadApi, globalUploadV3Api } from '@/api/Global'
  import {
    ElLoading,
    ElMessage,
    genFileId,
    UploadFile,
    UploadFiles,
    UploadInstance,
    UploadProgressEvent,
    UploadProps,
    UploadRawFile,
    UploadUserFile,
  } from 'element-plus'
  const emit = defineEmits(['fileData', 'removeFile'])
  const props = defineProps({
    listType: {
      type: String as PropType<'picture-card' | 'picture' | 'text'>,
      default: 'picture-card',
    },
    limit: {
      type: Number,
      default: 9,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    tip: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    btnText: {
      type: String,
      default: '点击上传',
    },
    accept: {
      type: String,
      default: '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar,.7z',
    },
    showFileList: {
      type: Boolean,
      default: true,
    },
    maxSize: {
      type: Number,
      default: 10, // 默认最大10MB
    },
    title: {
      type: String,
      default: '',
    },
  })
  const timer = ref()
  const uploadFileList = ref<UploadUserFile[]>([])
  const uploadFileRef = ref<UploadInstance>()
  const uploadFile = (file: UploadUserFile, fileList: UploadUserFile[]) => {
    console.log('file', file)
    // 检查文件大小
    if (file.size && file.size / 1024 / 1024 > props.maxSize) {
      ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`)
      // 移除超出大小限制的文件
      const index = fileList.findIndex((item) => item.uid === file.uid)
      if (index > -1) {
        fileList.splice(index, 1)
      }
      return
    }

    fileList.map((item) => {
      item.status = 'uploading'
      item.percentage = 0
    })
    //防抖 只触发最后一次
    clearTimeout(timer.value)
    timer.value = setTimeout(() => {
      const params = {
        businessLine: 204,
      }
      globalUploadApi(fileList, params, 'logistics/api/out/fleet/order/custom/template/getSheetTitles ')
        .then((res: any) => {
          const { data } = res
          fileList.map((item) => {
            item.status = 'success'
            item.percentage = 100
          })
          emit('fileData', data, file)
        })
        .finally(() => {
          timer.value = null
        })
    })
  }
  const handleRemove = (file: UploadUserFile, fileList: UploadUserFile[]) => {
    const index = fileList.findIndex((item) => {
      if (item.uid) {
        return item.uid === file.uid
      }
      return false
    })
    if (index > -1) {
      fileList.splice(index, 1)
    }
    uploadFileList.value = fileList
    emit('removeFile', fileList)
  }
  const handleExceed: UploadProps['onExceed'] = (files) => {
    uploadFileRef.value!.clearFiles()
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    uploadFileRef.value!.handleStart(file)
  }
  defineExpose({
    uploadFileList,
    uploadFileRef,
  })
</script>
<style scoped lang="scss">
  :deep(.hide .el-upload--picture-card) {
    display: none;
  }
</style>
