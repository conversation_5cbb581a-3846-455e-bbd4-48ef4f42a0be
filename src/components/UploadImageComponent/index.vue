<!--
 * @Author: llm
 * @Date: 2025-01-17 10:31:35
 * @LastEditors: llm
 * @LastEditTime: 2025-06-25 15:36:58
 * @Description: 图片上传组件
-->
<template>
  <div>
    <el-upload
      ref="uploadImageRef"
      :accept="accept"
      drag
      :class="{ hide: uploadImageList.length >= limit }"
      :auto-upload="false"
      :file-list="uploadImageList"
      :limit="limit"
      :disabled="disabled"
      :on-change="
        (file: any, fileList: any[]) => {
          uploadImage(file, fileList)
        }
      "
      :multiple="multiple"
      :on-preview="handlePictureCardPreview"
      :on-progress="handleImageProgress"
      :list-type="listType"
      action="#"
      :on-remove="handleImageRemove"
    >
      <img src="@/assets/images/upload_icon.png" mode="scaleToFill" style="width: 40px; height: auto" />
      <div class="el-upload__text text-12px!"><em>拖拽或点击上传</em></div>
      <template #tip v-if="tip">
        <div>
          <el-tag type="warning">
            <div class="flex items-center">
              <el-icon>
                <Warning />
              </el-icon>
              {{ tip }}
            </div>
          </el-tag>
        </div>
      </template>
    </el-upload>
    <!-- 图片查看弹窗 -->
    <pic-dialog-component ref="picDialogRef" :imageList="dialogImageUrl"></pic-dialog-component>
  </div>
</template>
<script setup lang="ts">
  import { globalUploadV3Api } from '@/api/Global'
  import { ElLoading, ElMessage, UploadFile, UploadFiles, UploadProgressEvent, UploadProps, UploadUserFile } from 'element-plus'
  import PicDialogComponent from '@/components/PicDialogComponent/index.vue'
  const emit = defineEmits(['fileData'])
  const props = defineProps({
    listType: {
      type: String as PropType<'picture-card' | 'picture' | 'text'>,
      default: 'picture-card',
    },
    limit: {
      type: Number,
      default: 9,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    tip: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    accept: {
      type: String,
      default: 'image/png, image/jpeg, image/jpg',
    },
  })
  const uploadImageRef = ref('')
  const timer = ref()
  const uploadImageList = ref<UploadUserFile[]>([])
  //预览弹窗
  const picDialogRef = ref<any>()
  //预览图地址
  const dialogImageUrl = ref<string[]>()
  const uploadImage = (file: UploadUserFile, fileList: UploadUserFile[]) => {
    //检查文件类型
    if (!props.accept.includes(file.raw!.type)) {
      ElMessage.error('图片格式不支持')
      //删除文件
      fileList.splice(fileList.indexOf(file), 1)
      return
    }
    //检查文件大小
    if (file.raw!.size > 10 * 1024 * 1024) {
      ElMessage.error('图片大小不能超过10MB')
      //删除文件
      fileList.splice(fileList.indexOf(file), 1)
      return
    }
    fileList.map((item) => {
      item.status = 'uploading'
      item.percentage = 0
    })
    //防抖 只触发最后一次
    clearTimeout(timer.value)
    timer.value = setTimeout(() => {
      const params = {
        businessLine: 204,
      }
      const loading = ElLoading.service({
        lock: true,
        text: '正在上传中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })

      // Store original files before making API call
      const originalImageList = [...uploadImageList.value]

      globalUploadV3Api(fileList, params)
        .then((res: any) => {
          const updatedFiles = res.data.uploads
          uploadImageList.value = uploadImageList.value.concat(updatedFiles)
          uploadImageList.value.map((item) => {
            item.status = 'success'
            item.percentage = 100
          })
          loading.close()
          emit('fileData', uploadImageList.value)
        })
        .catch((error) => {
          console.error('Upload failed:', error)
          // Restore the original list to keep previously uploaded images
          uploadImageList.value = originalImageList

          // Just mark the current files as failed
          fileList.forEach((file) => {
            file.status = 'fail'
          })

          ElMessage.error('图片上传失败，请重试')
        })
        .finally(() => {
          timer.value = null
          loading.close()
        })
    })
  }
  const handleImageRemove = (file: UploadUserFile, fileList: UploadUserFile[]) => {
    const index = fileList.findIndex((item) => {
      if (item.uid) {
        return item.uid === file.uid
      }
      return false
    })
    if (index > -1) {
      fileList.splice(index, 1)
    }
    uploadImageList.value = fileList
  }
  /**
   * 图片预览
   */
  const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
    dialogImageUrl.value = [uploadFile.url!]
    picDialogRef.value.picDialogVisible = true
  }
  const handleImageProgress = (evt: UploadProgressEvent, uploadFile: UploadFile, uploadFiles: UploadFiles) => {}
  defineExpose({
    uploadImageList,
    uploadImageRef,
  })
</script>
<style scoped lang="scss">
  :deep(.hide .el-upload--picture-card) {
    display: none;
  }
</style>
