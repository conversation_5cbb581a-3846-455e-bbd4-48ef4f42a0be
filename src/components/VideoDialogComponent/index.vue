<!--
 * @Author: llm
 * @Date: 2023-09-21 10:15:25
 * @LastEditors: llm
 * @LastEditTime: 2025-03-25 20:41:15
 * @Description: 查看视频弹窗组件
 *
-->
<template>
  <div>
    <el-dialog :draggable="true" v-model="videoDialogVisible" title="查看" width="50%" align-center @closed="closeVideo">
      <video v-for="(item, index) in videoList" :key="index" controls :src="item" style="width: 100%; height: auto" />
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  const videoList = ref([])
  const videoDialogVisible = ref(false)
  const closeVideo = () => {
    videoDialogVisible.value = false
    // 释放视频资源
    videoList.value = []
  }
  defineExpose({
    videoList,
    videoDialogVisible,
  })
</script>
