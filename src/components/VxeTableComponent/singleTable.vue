<!--
* @Author: llm
* @Date: 2025-06-10 15:17:50
 * @LastEditors: llm
 * @LastEditTime: 2025-06-16 16:08:09
* @Description:
-->

<script lang="ts" setup>
  import type { VxeColumnPropTypes, VxeTableEvents, VxeTableInstance, VxeTablePropTypes } from 'vxe-table'

  const emit = defineEmits(['getSelectRowIds'])
  const props = defineProps({
    height: {
      type: String,
      default: '500px',
    },
    showFooter: {
      type: Boolean,
      default: false,
    },
    footerData: {
      type: Array as PropType<VxeTablePropTypes.FooterData>,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    tableConfig: {
      type: Object as PropType<TableConfig>,
      default: () => ({
        showHandleSelection: true,
        tableItem: [] as any, // 添加默认空数组
      }),
    },
    /**
     * 单选、多选
     */
    selectStatus: {
      type: String as PropType<VxeColumnPropTypes.Type>,
      default: 'checkbox',
    },
  })
  const tableRef = ref()
  const handleSelectionChange: VxeTableEvents.CheckboxChange<any> = ({ checked }) => {
    const $table = tableRef.value
    if ($table) {
      const records = $table.getCheckboxRecords()
      const ids = records.map((item: any) => item.id)
      emit('getSelectRowIds', ids, records)
    }
  }
  //全选
  const handleSelectAllChange: VxeTableEvents.CheckboxAll<any> = async ({ checked }) => {
    const $table = tableRef.value
    if ($table) {
      const records = $table.getCheckboxRecords()
      const ids = records.map((item: any) => item.id)
      emit('getSelectRowIds', ids, records)
    }
  }
  //单选
  const radioChangeEvent: VxeTableEvents.RadioChange<any> = ({ row }) => {
    emit('getSelectRowIds', [row.id], [row])
  }
  const setSelectRow = (row: any) => {
    console.log(row, 'row')
    const $table = tableRef.value as VxeTableInstance
    if ($table) {
      $table.setRadioRow(row)
    }
  }
  defineExpose({
    setSelectRow,
  })
</script>

<template>
  <vxe-table
    ref="tableRef"
    :data="tableData"
    :loading="loading"
    :radio-config="{ highlight: true }"
    :scroll-x="{ enabled: true, gt: 0 }"
    :scroll-y="{ enabled: true, gt: 0 }"
    border
    :height="height"
    :show-footer="showFooter"
    :footer-data="footerData"
    show-footer-overflow
    show-header-overflow
    show-overflow
    size="mini"
    @checkbox-change="handleSelectionChange"
    @checkbox-all="handleSelectAllChange"
    @radio-change="radioChangeEvent"
  >
    <vxe-column :type="selectStatus" align="center" fixed="left" width="60" v-if="tableConfig.showHandleSelection"></vxe-column>
    <vxe-column align="center" field="seq" fixed="left" type="seq" width="70"></vxe-column>
    <vxe-column
      v-for="(item, index) in tableConfig.tableItem"
      :key="index"
      :field="item.name"
      :min-width="item.width || 100"
      :title="item.label"
      align="center"
      show-footer-overflow
      show-header-overflow
      show-overflow="title"
    ></vxe-column>
  </vxe-table>
</template>

<style lang="scss" scoped></style>
