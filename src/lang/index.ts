/*
 * @Author: llm
 * @Date: 2025-01-25 17:35:16
 * @LastEditors: llm
 * @LastEditTime: 2025-03-07 10:54:46
 * @Description:
 */
import { createI18n } from 'vue-i18n'
import { useAppStore } from '@/store/modules/app'
// 本地语言包
import enLocale from './package/en'
import zhCnLocale from './package/zh-cn'

const appStore = useAppStore()

const messages = {
  'zh-cn': {
    ...zhCnLocale,
  },
  en: {
    ...enLocale,
  },
}

const i18n = createI18n({
  legacy: false,
  // locale: appStore.language,
  locale: 'zh-cn',
  messages,
  globalInjection: true,
})

export default i18n
