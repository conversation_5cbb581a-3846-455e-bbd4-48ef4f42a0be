<!--
 * @Author: llm 
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2025-03-21 16:16:45
 * @Description: 
 * 
-->
<script setup lang="ts">
  import { useTagsViewStore } from '@/store/modules/tagsView'
  const tagsViewStore = useTagsViewStore()
</script>

<template>
  <section class="app-main">
    <router-view v-slot="{ Component, route }">
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="tagsViewStore.cachedViews">
          <component :is="Component" :key="route.fullPath" />
        </keep-alive>
      </transition>
    </router-view>
  </section>
</template>

<style lang="scss" scoped>
  .app-main {
    position: relative;
    width: 100%;

    /* 50= navbar  50  */
    min-height: calc(100vh - 50px);
    overflow: hidden;
    background-color: var(--el-bg-color-page);
  }

  .fixed-header + .app-main {
    padding-top: 50px;
  }

  .hasTagsView {
    .app-main {
      /* 84 = navbar + tags-view = 50 + 34 */
      min-height: calc(100vh - 84px);
    }

    .fixed-header + .app-main {
      min-height: 100vh;
      padding-top: 84px;
    }
  }
</style>
