<script setup lang="ts">
  import { storeToRefs } from 'pinia'
  import { LocationQuery, LocationQueryValue, useRoute, useRouter } from 'vue-router'
  import { useAppStore } from '@/store/modules/app'
  import { useTagsViewStore } from '@/store/modules/tagsView'
  import { useUserStore } from '@/store/modules/user'
  import HeaderSearch from '@/components/HeaderSearch/index.vue'
  import { useSystemNameStore } from '@/store/modules/systemName'
  import { CloseBold } from '@element-plus/icons-vue'
  import SvgIcons from '@/components/SvgIcons/index.vue'
  import { editUserPassword, getUserMenuList } from '@/api/auth/index'
  import { useSettingsStore } from '@/store'
  import { color } from 'echarts'
  const appStore = useAppStore()
  const tagsViewStore = useTagsViewStore()
  const userStore = useUserStore()
  const settingsStore = useSettingsStore()
  const route = useRoute()
  const router = useRouter()

  const { device, sidebar } = storeToRefs(appStore) // 设备类型：desktop-宽屏设备 || mobile-窄屏设备
  const date: Date = new Date()
  const greetings = computed(() => {
    if (date.getHours() >= 6 && date.getHours() < 12) {
      return '上午好🌞！'
    } else if (date.getHours() >= 12 && date.getHours() < 18) {
      return '下午好☕！'
    } else if (date.getHours() >= 18 && date.getHours() < 24) {
      return '晚上好🌃！'
    } else if (date.getHours() >= 0 && date.getHours() < 6) {
      return '晚安🌛！'
    }
  })
  const systemDialogVisible = ref(false)
  const currentSystem = ref()
  const systemsList = userStore.getSystemList()
  const menuList = useStorage('menuList', Array<any>)
  const selectedSystem = ref('')
  /**
   * 请求地址前缀
   */
  const requestUri = ref<string>()
  /**
   * 左侧菜单栏显示/隐藏
   */
  function toggleSideBar() {
    appStore.toggleSidebar(true)
  }

  /**
   * vueUse 全屏
   */
  const { isFullscreen, toggle } = useFullscreen()

  /**
   * 注销
   */
  function logout() {
    userStore
      .logout()
      .then(() => {
        tagsViewStore.delAllViews()
      })
      .then(() => {
        router.push(`/login?redirect=${route.fullPath}`)
      })
  }
  function getUserMenuListFun() {
    return new Promise((resolve, reject) => {
      const menuList = getUserMenuList()
      resolve(menuList)
    })
  }
  function selectSystem(systemName: string) {
    selectedSystem.value = systemName
    currentSystem.value = systemsList.find((item) => item.systemName === systemName)
  }
  //登录选择的系统
  async function loginSystem() {
    if (!currentSystem.value || !currentSystem.value.systemName) {
      ElMessage.error('请选择需要登录的系统！')
      return
    }
    settingsStore.changeSetting({ key: 'systemName', value: currentSystem.value!.systemName })
    document.title = settingsStore.systemName
    userStore.setSystemType(currentSystem.value!.systemType)

    const menus = (await getUserMenuListFun()) as any
    menuList.value = menus.data
    await goHomePage()
    systemDialogVisible.value = false
    //刷新页面
    // window.location.reload()
    setTimeout(() => {
      window.location.replace('/#/homePage')
      window.location.reload()
    }, 100)
  }
  const goHomePage = () => {
    return new Promise<void>((resolve, reject) => {
      const query: LocationQuery = route.query

      const redirect = '/homePage'

      const otherQueryParams = Object.keys(query).reduce((acc: any, cur: string) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
      const systemNameStore = useSystemNameStore()
      systemNameStore.systemNameChange(currentSystem.value?.systemName as string)
      router.push({ path: redirect, query: otherQueryParams })
      resolve()
    })
  }
  /**
   * 修改密码
   */
  function editPassword() {
    ElMessageBox.prompt('密码长度为8-16位，必须包含大写字母+小写字母+数字。', '重置密码', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern:
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,16}$/,
      inputErrorMessage: '密码长度为8-16位，必须包含大写字母+小写字母+数字',
      inputPlaceholder: '请输入新密码',
    })
      .then(({ value }) => {
        if (!value) {
          ElMessage.warning('请输入新密码')
          return false
        }
        const hasToken = localStorage.getItem('accessToken')
        if (hasToken) {
          let userId = hasToken.split('.')[1]
          requestUri.value = 'tms/warehouse/operators/user'
          editUserPassword(userId, { password: value }).then(() => {
            ElMessage.success('密码修改成功，新密码是：' + value)
            setTimeout(() => {
              userStore
                .logout()
                .then(() => {
                  tagsViewStore.delAllViews()
                })
                .then(() => {
                  router.push(`/login?redirect=${route.fullPath}`)
                })
            }, 500)
          })
        }
      })
      .catch(() => {})
  }

  /**
   * 跳转到监控消息
   */
  function linkToMessage() {
    router.push('/alarmCenter/monitoringNotify')
  }
  function closeCurrentTab() {
    try {
      window.close()
    } catch (e) {}
  }
  // 退出登录
  const outLogin = ref(false)
</script>

<template>
  <!-- 顶部导航栏 -->
  <div class="navbar">
    <!-- 左侧面包屑 -->
    <div class="flex items-center">
      <!-- <svg-icons icon-class="hamburger" :is-active="sidebar.opened" class="hamburger-container" @click="toggleSideBar" /> -->
      <img src="@/assets/images/shousuo.png" alt="" class="w-18px h-18px m-4px" @click="toggleSideBar" v-if="sidebar.opened" />
      <img src="@/assets/images/zhankai.png" alt="" class="w-18px h-18px m-4px" @click="toggleSideBar" v-else />
      <breadcrumb />
    </div>
    <!-- 中间区域 -->
    <div class="leading-[40px]">
      <el-text>{{ greetings }}</el-text>
    </div>
    <!-- 右侧导航设置 -->
    <div class="flex" style="padding-right: 30px">
      <!-- 导航栏设置(窄屏隐藏)-->
      <div v-if="device !== 'mobile'" class="setting-container">
        <header-search id="header-search" class="right-menu-item" />
        <!-- <el-badge :value="999" class="badge" @click="linkToMessage">
          <el-icon size="24" color="#606266"><BellFilled /></el-icon>
        </el-badge> -->
        <!-- 用户头像 -->
        <el-dropdown>
          <span class="el-dropdown-link">
            <div class="avatar-container">
              <img :src="userStore.avatar" />
              <!-- <i-ep-caret-bottom class="w-3 h-3" /> -->
            </div>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <!-- 上部分 -->
              <div class="info-container">
                <div>
                  <div style="margin-left: 10px; margin-top: 10px">
                    <img :src="userStore.avatar" style="width: 50px; height: 50px" />
                  </div>
                </div>
                <div>
                  <el-dropdown-item style="cursor: default">
                    <el-text>{{ userStore.userInfo.userName }}</el-text>
                  </el-dropdown-item>
                  <el-dropdown-item style="cursor: default">
                    <el-text>{{ userStore.userInfo.companyName }}</el-text>
                  </el-dropdown-item>
                </div>
              </div>
              <!-- 下部分 -->
              <!-- <el-dropdown-item style="cursor: default">
                <el-button v-if="systemsList.length > 1" type="text" size="small" @click="systemDialogVisible = true">切换系统</el-button>
              </el-dropdown-item> -->
              <div class="el-items">
                <el-dropdown-item @click="systemDialogVisible = true" v-if="systemsList.length > 1">
                  <div style="color: #1c94fa; font-size: 11px" class="eldropdownitems">
                    <!-- <el-icon>
                      <Edit />
                    </el-icon> -->
                    <img src="@/assets/qiehuan.png" style="width: 18px; height: 20px" />
                    切换系统
                  </div>
                </el-dropdown-item>
                <el-dropdown-item @click="editPassword">
                  <el-text style="font-size: 11px" class="eldropdownitems">
                    <!-- <el-icon>
                      <Edit />
                    </el-icon> -->
                    <img src="@/assets/mima.png" style="width: 18px; height: 20px" />
                    修改密码
                  </el-text>
                </el-dropdown-item>
                <el-dropdown-item @click="outLogin = true">
                  <el-text style="font-size: 11px; color: #f56c6c" class="eldropdownitems">
                    <!-- <el-icon>
                      <SwitchButton />
                    </el-icon> -->
                    <img src="@/assets/outlog.png" style="width: 18px; height: 20px" />
                    退出登录
                  </el-text>
                </el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <!-- <el-button type="danger" title="关闭当前页面" :icon="CloseBold" size="small" circle @click="closeCurrentTab"></el-button> -->
        <!--全屏 -->
        <!-- <div class="setting-item" @click="toggle">
          <svg-icon
            :icon-class="isFullscreen ? 'exit-fullscreen' : 'fullscreen'"
          />
        </div> -->
        <!-- 布局大小 -->
        <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select class="setting-item" />
        </el-tooltip> -->
        <!--语言选择-->
        <!-- <lang-select class="setting-item" /> -->
      </div>
    </div>
    <el-dialog v-model="systemDialogVisible" align-center width="30%" :show-close="false" style="width: 100px; height: 100px; position: relative">
      <div class="loginDialog">
        <div class="close_dialog" @click="systemDialogVisible = false">×</div>
        <div style="margin-bottom: 15px">
          <img src="@/assets/logo.jpg" alt="" />
        </div>
        <div>
          <img src="@/assets/logoName.jpg" alt="" />
        </div>
        <div class="loginText">请选择：</div>
        <div class="selectLogin">
          <div
            v-for="system in systemsList"
            :key="system.systemName"
            class="selectItem"
            :class="{ selected: selectedSystem === system.systemName }"
            @click="selectSystem(system.systemName)"
          >
            <img v-if="system.systemName === 'VTMS' && selectedSystem === system.systemName" src="@/assets/vtms1.jpg" alt="" />
            <img v-if="system.systemName === 'VTMS' && selectedSystem !== system.systemName" src="@/assets/vtms.jpg" alt="" />
            <img v-if="system.systemName === 'VWMS' && selectedSystem === system.systemName" src="@/assets/vwms1.png" alt="" />
            <img v-if="system.systemName === 'VWMS' && selectedSystem !== system.systemName" src="@/assets/vwms.png" alt="" />
            <div class="selectText" :style="{ color: selectedSystem === system.systemName ? '#3F97FD' : '#D1D3D7' }">
              {{ system.systemName }}
            </div>
          </div>
        </div>

        <div style="width: 100%; display: flex; justify-content: center; margin-top: 20px">
          <el-button
            type="primary"
            style="width: 117px; height: 46px; border-radius: 9px"
            :style="{
              background: currentSystem?.systemName ? '#3F97FD' : '#B9DAFF',
              color: '#fff',
            }"
            @click="loginSystem"
            >确定</el-button
          >
        </div>
      </div>
    </el-dialog>

    <!-- 退出登录 -->
    <el-dialog v-model="outLogin" :append-to-body="true" align-center width="30%" :show-close="false">
      <div class="images" >
        <img src="@/assets/bianzu.png" style="width: 218px; height: 144px; margin-top: -63px" />
      </div>
      <div class="alter">提示</div>
      <div class="message">确定注销并退出系统吗？</div>
      <div>
        <span class="dialog-footer">
          <el-button size="large" @click="outLogin = false">取消</el-button>
          <el-button type="primary" size="large" @click="logout">确定</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>


<style lang="scss" scoped>
  .navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    background-color: #fff;
    box-shadow: 0 0 1px #0003;

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .setting-container {
      display: flex;
      align-items: center;

      .logout {
        color: red;
      }
    }

    .badge {
      display: flex;
      align-items: center;
      margin-right: 24px;
      cursor: pointer;
    }

    .avatar-container {
      display: flex;
      align-items: center;
      justify-items: center;
      margin: 0 5px;
      // cursor: pointer;

      img {
        width: 30px;
        height: 30px;
        border-radius: 5px;
      }
    }

    :deep(.el-dropdown-link:focus) {
      outline: none;
    }
  }
  .loginDialog {
    width: 616px;
    height: 602px;
    position: absolute;
    top: -301px;
    left: -308px;
    border-radius: 20px;
    padding: 78px 64px;
    background: url('@/assets/loginborder.jpg') no-repeat;

    .close_dialog {
      font-size: 26px;
      padding: 8px 8px;
      color: #999999;
      position: absolute;
      top: 0%;
      right: 0%;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 15px;
      margin-right: 30px;
      margin-top: 30px;
      cursor: pointer;

      &:hover {
        background-color: #efefef;
        /* 鼠标悬停时背景变黑 */
      }
    }

    img {
      background-color: transparent;
      mix-blend-mode: multiply;
    }

    .loginText {
      width: 72px;
      height: 18px;
      margin-top: 47px;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      line-height: 27px;
      text-align: center;
      font-style: normal;
      margin-bottom: 21px;
    }

    .selectLogin {
      display: flex;
      justify-content: space-between;

      .selectItem {
        width: 230px;
        height: 193px;
        background: #f3f9ff;
        border-radius: 19px;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;

        img {
          margin-top: 50px;
        }

        .selectText {
          width: 100px;
          height: 26px;
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: bold;
          font-size: 26px;
          line-height: 39px;
          text-align: center;
          font-style: normal;
          margin-top: 20px;
        }

        &.selected {
          background: #e6f1ff;
        }
      }
    }
  }
  .info-container {
    display: flex;
    align-items: center;
  }
  .el-items {
    display: flex;
  }
  .eldropdownitems {
    width: 61px;
    height: 65px;
    background: #f7f9fa;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    &:hover {
      border-radius: 50%;
    }
  }
  .images {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .alter {
    width: 100%;
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: bold;
    font-size: 20px;
    color: #333333;
    text-align: center;
    font-style: normal;
    text-align: center;
  }
  .message {
    width: 100%;
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: 400;
    font-size: 18px;
    color: #333333;
    text-align: center;
    font-style: normal;
  }
  .dialog-footer {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 28px;
  }
</style>
