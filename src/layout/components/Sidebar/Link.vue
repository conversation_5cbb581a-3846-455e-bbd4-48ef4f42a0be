<!--
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2023-10-14 09:40:25
 * @Description:
 *
-->
<script lang="ts" setup>
  import { computed } from 'vue'
  import { isExternal } from '@/utils/index'
  import { useRouter } from 'vue-router'

  import { useAppStore } from '@/store/modules/app'
  import { useFormStore } from '@/store/modules/form.js'
  const formStore = useFormStore()
  const appStore = useAppStore()

  const sidebar = computed(() => appStore.sidebar)
  const device = computed(() => appStore.device)

  const props = defineProps({
    to: {
      type: String,
      required: true,
    },
  })

  const router = useRouter()
  function push() {
    if (device.value === 'mobile' && sidebar.value.opened == true) {
      appStore.closeSideBar(false)
    }
    // 切换菜单 清空搜索组件内容
    formStore.$patch((state) => {
      state.routerParams = null
    })
    router.push(props.to).catch((err) => {})
  }
</script>

<template>
  <a v-if="isExternal(to)" :href="to" target="_blank" rel="noopener">
    <slot />
  </a>
  <div v-else @click="push">
    <slot />
  </div>
</template>
