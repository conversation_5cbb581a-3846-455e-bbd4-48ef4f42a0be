<script setup lang="ts">
  import path from 'path-browserify'
  import { isExternal } from '@/utils/index'
  import AppLink from './Link.vue'

  import { translateRouteTitleI18n } from '@/utils/i18n'
  import { useFormStore } from '@/store/modules/form'
  const formStore = useFormStore()
  const props = defineProps({
    /**
     * 路由(eg:level_3_1)
     */
    item: {
      type: Object,
      required: true,
    },

    /**
     * 父层级完整路由路径(eg:/level/level_3/level_3_1)
     */
    basePath: {
      type: String,
      required: true,
    },
    /**
     * 菜单上的数字(eg:[{menuId:4100000000000,count:3}])
     */
    menuCountList: {
      type: Array<any>,
      required: true,
      default: () => [],
    },
  })

  const onlyOneChild = ref() // 临时变量，唯一子路由

  // 检查子菜单是否有count
  const hasChildWithCount = computed(() => {
    if (!props.item.children) return false
    return props.item.children.some((child: { menuId: string | number }) => {
      const menuCount = props.menuCountList.find((item) => item.menuId === child.menuId)
      return menuCount && menuCount.count
    })
  })

  onMounted(() => {
    // getMenuCountFun();
  })
  /**
   * 判断当前路由是否只有一个子路由
   *
   * 1：如果只有一个子路由： 返回 true
   * 2：如果无子路由 ：返回 true
   *
   * @param children 子路由数组
   * @param parent 当前路由
   */
  function hasOneShowingChild(children = [], parent: any) {
    // 需要显示的子路由数组
    const showingChildren = children.filter((item: any) => {
      if (item.meta?.hidden) {
        return false // 过滤不显示的子路由
      } else {
        onlyOneChild.value = item // 唯一子路由赋值（多个子路由情况 onlyOneChild 变量是用不上的）
        return true
      }
    })

    // 1：如果只有一个子路由, 返回 true
    if (showingChildren.length === 1) {
      return true
    }

    // 2：如果无子路由, 复制当前路由信息作为其子路由，满足只拥有一个子路由的条件，所以返回 true
    if (showingChildren.length === 0) {
      onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
      return true
    }
    return false
  }

  /**
   * 解析路径
   *
   * @param routePath 路由路径
   */
  function resolvePath(routePath: string) {
    //如果为true，表示需要刷新顶部查询条件
    if (formStore.getIsRefreshTopQueryParams()) {
      formStore.setSearchParams({})
    }
    if (isExternal(routePath)) {
      return routePath
    }
    if (isExternal(props.basePath)) {
      return props.basePath
    }
    // 完整路径 = 父级路径(/level/level_3) + 路由路径
    const fullPath = path.resolve(props.basePath, routePath) // 相对路径 → 绝对路径
    return fullPath
  }
</script>
<template>
  <div v-if="!item.meta || !item.meta.hidden">
    <!-- 只包含一个子路由节点的路由，显示其【唯一子路由】 -->
    <template v-if="hasOneShowingChild(item.children, item) && (!onlyOneChild.children || onlyOneChild.noShowingChildren)">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)" :menuId="onlyOneChild.menuId ? onlyOneChild.menuId : ''">
        <el-menu-item :index="resolvePath(onlyOneChild.path)">
          <!-- <svg-icons v-if="onlyOneChild.meta && onlyOneChild.meta.icon" :icon-class="onlyOneChild.meta.icon" /> -->
          <img v-if="onlyOneChild.meta && onlyOneChild.meta.ext?.menuIcon" :src="onlyOneChild.meta.ext?.menuIcon" class="w-16px mr-4px" />
          <template #title>
            <div style="display: flex; align-items: center; justify-content: space-between; width: 100%">
              {{ translateRouteTitleI18n(onlyOneChild.meta.title) }} &nbsp;
              <el-tag
                type="danger"
                effect="dark"
                size="small"
                round
                v-if="
                  props.menuCountList.find((item) => item.menuId === onlyOneChild.menuId) &&
                  props.menuCountList.find((item) => item.menuId === onlyOneChild.menuId)?.count
                "
              >
                {{ props.menuCountList.find((item) => item.menuId === onlyOneChild.menuId)?.count }}
              </el-tag>
            </div>
          </template>
        </el-menu-item>
      </app-link>
    </template>

    <!-- 包含多个子路由  -->
    <el-sub-menu v-else :index="resolvePath(item.path)" teleported>
      <template #title>
        <div style="display: flex; align-items: center; justify-content: space-between; width: 100%">
          <div style="display: flex; align-items: center">
            <!-- <svg-icons v-if="item.meta && item.meta.icon" :icon-class="item.meta.icon" /> -->
            <img v-if="item.meta && item.meta.icon" :src="item.meta.ext?.menuIcon" class="w-16px mr-4px" />
            <span v-if="item.meta && item.meta.title">{{ translateRouteTitleI18n(item.meta.title) }}&nbsp;</span>
          </div>

          <div style="display: flex; align-items: center">
            <el-tag
              type="danger"
              effect="dark"
              size="small"
              round
              v-if="
                props.menuCountList.find((_item) => _item.menuId === item.meta.menuId) &&
                props.menuCountList.find((_item) => _item.menuId === item.meta.menuId)?.count
              "
            >
              {{ props.menuCountList.find((_item) => _item.menuId === item.meta.menuId)?.count }}
            </el-tag>
            <div v-if="hasChildWithCount" class="w-8px h-8px rounded-full bg-red-500 ml-2"></div>
          </div>
        </div>
      </template>
      <sidebar-item v-for="child in item.children" :key="child.path" :item="child" :menuCountList="props.menuCountList" :base-path="resolvePath(child.path)" />
    </el-sub-menu>
  </div>
</template>
