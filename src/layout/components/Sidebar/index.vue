<!--
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2025-05-14 10:34:36
 * @Description:
 *
-->
<script setup lang="ts">
  import { useRoute } from 'vue-router'
  import SidebarItem from './SidebarItem.vue'
  import Logo from './Logo.vue'
  import { storeToRefs } from 'pinia'
  import variables from '@/styles/variables.module.scss'
  import { useSettingsStore } from '@/store/modules/settings'
  import { usePermissionStore } from '@/store/modules/permission'
  import { useAppStore } from '@/store/modules/app'
  import { useSideBarStore } from '@/store/modules/sideBar'
  import { getMenuCount } from '@/api/planManagement'
  import { useFormStore } from '@/store/modules/form'
  const { proxy }: any = getCurrentInstance()
  import { SelectLeftTreeRowsVO } from '@/types/global'
  const emit = defineEmits([
    'showRowMenuDialog', //清空携带的参数
  ])
  const sideBarStore = useSideBarStore()
  const settingsStore = useSettingsStore()
  const permissionStore = usePermissionStore()
  const appStore = useAppStore()
  const currRoute = useRoute()
  const formStore = useFormStore()
  const { sidebarLogo } = storeToRefs(settingsStore)
  //获取菜单上的数字
  const getMenuCountFun = () => {
    getMenuCount().then((res) => {
      sideBarStore.$patch((state) => {
        state.menuCount = res.data as any
      })
    })
  }
  /**
   * 切换菜单
   * @param indexPath 当前路径
   */
  const selectMenu = (indexPath: string) => {
    sideBarStore.$patch((state) => {
      state.btnMenuId = ''
      state.btnMenuQuery = {}
    })
    //切页清除携带的参数
    emit('showRowMenuDialog', null, null)
    //清除传回的默认选中列表项
    formStore.$patch((state) => {
      state.defaultTableIds = []
    })
  }
  watch(
    () => currRoute,
    (currentRoute) => {
      //切页更新菜单数值
      getMenuCountFun()
      sideBarStore.setGetMenuCountFun(getMenuCountFun) //设置获取菜单数值的函数
      sideBarStore.$patch((state) => {
        state.pageType = currentRoute.meta.pageType as string //当前页面样式类型
        state.pageName = currentRoute.name as string //当前页面名称
        state.menuId = currentRoute.meta.menuId as string //获取当前路由下的menuId，用于查询当前菜单下的数据列
      })
      formStore.$patch((state) => {
        //判断sideBarStore.menuId 在state.selectLeftTreeRows 中是否存在,如果不存在则在state.selectLeftTreeRows新增一条数据{sideBarStore.menuId:row}
        if (state.selectLeftTreeRows.some((item) => item.menuId === currentRoute.meta.menuId)) {
          state.selectLeftTreeRows = state.selectLeftTreeRows.map((item) => {
            return item
          })
        } else {
          state.selectLeftTreeRows.push({
            menuId: currentRoute.meta.menuId as string,
            selectLeftTreeRow: null,
          })
        }
      })
    },
    { deep: true, immediate: true },
  )

  // onMounted(() => {
  //   menuCountList.value = sideBarStore.$state.menuCount;
  // });
</script>

<template>
  <div :class="{ 'has-logo': sidebarLogo }">
    <logo v-if="sidebarLogo" :collapse="!appStore.sidebar.opened" />
    <el-scrollbar>
      <el-menu
        :default-active="currRoute.path"
        :collapse="!appStore.sidebar.opened"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :active-text-color="variables.menuActiveText"
        :unique-opened="true"
        :collapse-transition="false"
        mode="vertical"
        @select="selectMenu"
      >
        <sidebar-item
          v-for="(route, index) in permissionStore.routes"
          :key="index + Math.random() * 10 + Math.random() * 10"
          :item="route"
          :base-path="route.path"
          :is-collapse="!appStore.sidebar.opened"
          :menuCountList="sideBarStore.$state.menuCount"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>
