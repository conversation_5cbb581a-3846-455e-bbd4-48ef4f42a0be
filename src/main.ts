/*
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2025-06-13 10:08:52
 * @Description:
 *
 */
import { createApp } from 'vue'
import App from './App.vue'
import router from '@/router'
import { setupStore } from '@/store'
import { setupDirective } from '@/directive'
import '@/permission'
// icon
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
// 本地SVG图标
import 'virtual:svg-icons-register'
import { hiPrintPlugin } from 'vue-plugin-hiprint'
import VxeUIAll, { VxeUI } from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'
import VxeUITable from 'vxe-table'
import 'vxe-table/lib/style.css'
import VxeUIPluginRenderElement from '@vxe-ui/plugin-render-element'
// 国际化
import i18n from '@/lang/index'
// 引入打印组件样式
// import "sv-print/dist/style.css";
// 样式
// import 'element-plus/theme-chalk/dark/css-vars.css';
import 'element-plus/dist/index.css'
import '@/styles/index.scss'
import '@/assets/font/font.css' //引入字体库
import 'uno.css'
import { useSideBarStore } from '@/store/modules/sideBar'
import 'view-ui-plus/dist/styles/viewuiplus.css'
// 只保留调试工具，移除全局自动修复
import '@/utils/tooltipDebug'
import '@/utils/checkZIndex'
// 引入dataV
import DataVVue3 from '@kjgl77/datav-vue3'
// @ts-ignore
import Print from 'vue3-print-nb'
import BaiduMap, { BmlMarkerClusterer } from 'vue-baidu-map-3x'
import SvgIcons from '@/components/SvgIcons/index.vue'
const sideBarStore = useSideBarStore()
const app = createApp(App)
app.config.globalProperties.$sideBarStore = sideBarStore
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
VxeUI.use(VxeUIPluginRenderElement)
app.component('svg-icons', SvgIcons)
app.component('bml-marker-clusterer', BmlMarkerClusterer)
app.use(hiPrintPlugin, '$hiprint', false)
hiPrintPlugin.disAutoConnect()
app.use(BaiduMap, {
  // ak 是在百度地图开发者平台申请的密钥 详见 http://lbsyun.baidu.com/apiconsole/key */
  ak: 'ZHr1DjwmXSKTV5Rg0m5WnIFvG44gFTsC',
  v: '3.0', // 默认使用3.0
  // type: 'WebGL' // ||API 默认API  (使用此模式 BMap=BMapGL)
})
// 全局注册 自定义指令(directive)
setupDirective(app)
// 全局注册 状态管理(store)
setupStore(app)
app.use(router).use(i18n).use(DataVVue3).use(Print).use(VxeUIAll).use(VxeUITable).mount('#app')
