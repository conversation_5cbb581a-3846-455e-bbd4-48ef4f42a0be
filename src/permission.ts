/*
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2023-07-08 15:57:50
 * @Description:
 *
 */
import NProgress from 'nprogress'
import router from '@/router'
import { useUserStoreHook } from '@/store/modules/user'
import { usePermissionStoreHook } from '@/store/modules/permission'

import 'nprogress/nprogress.css'

NProgress.configure({ showSpinner: false }) // 进度条

const permissionStore = usePermissionStoreHook()

// 白名单路由
const whiteList = ['/login']

router.beforeEach(async (to, from, next) => {
  NProgress.start()
  const hasToken = localStorage.getItem('accessToken')
  if (hasToken) {
    // 三级菜单组件无法缓存问题
    if (to.matched && to.matched.length > 2) {
      to.matched.splice(1, to.matched.length - 2)
    }
    if (to.path === '/login') {
      // 如果已登录，跳转首页
      next({ path: '/' })
      NProgress.done()
    } else {
      const userStore = useUserStoreHook()
      const hasRoles = userStore.roles && userStore.roles.length > 0
      if (hasRoles) {
        // 未匹配到任何路由，跳转404
        if (to.matched.length === 0) {
          from.name ? next({ name: from.name }) : next('/404')
        } else {
          next()
        }
      } else {
        try {
          const { roles } = await userStore.getInfo()
          const accessRoutes = await permissionStore.generateRoutes(roles)
          accessRoutes.forEach((route) => {
            router.addRoute(route)
          })
          next({ ...to, replace: true })
        } catch (error) {
          // 移除 token 并跳转登录页
          await userStore.resetToken()
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    // 未登录可以访问白名单页面
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
