import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'

export const Layout = () => import('@/layout/index.vue')

// 静态路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/redirect',
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue'),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    meta: { hidden: true },
  },

  {
    path: '/',
    component: Layout,
    redirect: '/homePage',
    children: [
      {
        path: 'homePage',
        component: () => import('@/views/homePage/index.vue'),
        name: 'HomePage',
        meta: { title: '首页', ext: { menuIcon: 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/mr/left_menu_icon/shouye.png' }, affix: true },
      },
      {
        path: '401',
        component: () => import('@/views/error-page/401.vue'),
        meta: { hidden: true },
      },
      {
        path: '404',
        component: () => import('@/views/error-page/404.vue'),
        meta: { hidden: true },
      },
      {
        path: '/:catchAll(.*)',
        meta: { hidden: true },
        component: () => import('@/views/error-page/404.vue'),
      },
    ],
  },
  // 外部链接
  /* {
        path: '/external-link',
        component: Layout,
        children: [
            {
                path: 'https://www.cnblogs.com/haoxianrui/',
                meta: { title: '外部链接', icon: 'link' }
            }
        ]
    } */
  // 多级嵌套路由
]

/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes as RouteRecordRaw[],
  // 刷新时，滚动条位置还原
  scrollBehavior: () => ({ left: 0, top: 0 }),
})
// 处理路由跳转后页面是否缓存
router.beforeEach(async (to, from, next) => {
  keepAliveSplice(to)
  next()
})
export function keepAliveSplice(to: any) {
  if (to.matched && to.matched.length > 2) {
    to.matched.map((v: any, k: any) => {
      if (v.components.default instanceof Function) {
        // 区分非懒路由
        if (v.components.default.length === 0) {
          v.components.default().then((components: any) => {
            if (components.default.name === 'parent') {
              to.matched.splice(k, 1)
              router.push({ path: to.path, query: to.query })
              keepAliveSplice(to)
            }
          })
        }
      } else if (v.components.default.name === 'parent') {
        to.matched.splice(k, 1)
        keepAliveSplice(to)
      }
    })
  }
}

/**
 * 重置路由
 */
export function resetRouter() {
  router.replace({ path: '/login' })
}

export default router
