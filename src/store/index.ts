/*
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2024-11-06 11:45:34
 * @Description:
 *
 */
import type { App } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

const store = createPinia()
store.use(piniaPluginPersistedstate)
// 全局注册 store
export function setupStore(app: App<Element>) {
  app.use(store)
}
export * from './modules/tagsView'
export * from './modules/user'
export * from './modules/app'
export * from './modules/settings'
export * from './modules/permission'
export { store }
