/*
 * @Author: llm
 * @Date: 2023-08-28 09:25:53
 * @LastEditors: llm
 * @LastEditTime: 2023-08-28 09:42:27
 * @Description: 按钮组
 *
 */
import { defineStore } from 'pinia'

export const useButtonGroupStore = defineStore('buttonGroup', () => {
  /**
   * 点击按钮打开表单，是否展示地图
   */
  const showMap = ref<null | string>(null)

  function setShowMap(val: string | null) {
    showMap.value = val
  }
  return {
    showMap,
    setShowMap,
  }
})
