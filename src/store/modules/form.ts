/*
 * @Author: llm
 * @Date: 2023-07-24 10:41:59
 * @LastEditors: llm
 * @LastEditTime: 2025-06-25 18:25:11
 * @Description: 勾选列表项数据，用于传参
 *
 */
import { SelectLeftTreeRowsVO } from '@/types/global'
import { defineStore } from 'pinia'

export const useFormStore = defineStore(
  'form',
  () => {
    /**
     * 选中的列表项数据
     */
    const selectRows = ref<any[]>([])
    /**
     * basePage左侧列表选中的列表项数据
     */
    const selectLeftTreeRows = ref<SelectLeftTreeRowsVO[]>([])
    /**
     * 顶部搜索条件
     */
    const searchParams = ref<any>({})
    /**
     * 是否刷新顶部搜索条件(切换菜单需要刷新,点击表单跳转不刷新),默认刷新
     */
    const isRefreshTopQueryParams = ref<boolean>(true)
    /**
     *
     * 路由传参
     */
    const routerParams = ref<any>({})
    /**
     * 列表需要默认勾选的ids数据
     */
    const defaultTableIds = ref<any>([])
    /**
     * 需要与当前formData进行合并
     */
    const mergeFormData = ref<any>({})

    /**
     * 全局存储的参数
     */
    const storeFormParams = ref<any>({})

    /**
     * 全局存储的对象，判断key是否相同，相同都缓存key对应的value
     */
    const localMap = ref<any>({})

    /**
     * 全局存储表单下的按钮btns
     * @param params
     */
    const storeFormBtns = ref<BtnRequestVO[]>([])

    function setRouterParams(params: any) {
      routerParams.value = params
    }
    function setSearchParams(params: any) {
      searchParams.value = params
    }

    function setSelectRows(rows: any[]) {
      selectRows.value = rows
    }

    function setSelectLeftTreeRows(rows: any[]) {
      selectLeftTreeRows.value = rows
    }

    function setLocalMap(key: string, val: any) {
      localMap.value[key] = val
    }
    function setIsRefreshTopQueryParams(status: boolean) {
      isRefreshTopQueryParams.value = status
      if (status) {
        //清空顶部搜索条件
        routerParams.value = {}
      }
    }
    function getIsRefreshTopQueryParams() {
      return isRefreshTopQueryParams.value
    }
    function getSearchParams() {
      return searchParams.value
    }

    return {
      selectRows,
      selectLeftTreeRows,
      searchParams,
      routerParams,
      defaultTableIds,
      mergeFormData,
      storeFormParams,
      storeFormBtns,
      localMap,
      setSearchParams,
      setSelectRows,
      setSelectLeftTreeRows,
      setRouterParams,
      setLocalMap,
      setIsRefreshTopQueryParams,
      getIsRefreshTopQueryParams,
      getSearchParams,
    }
  },
  {
    // 开启数据缓存
    persist: {
      storage: localStorage,
      // paths: ['routerParams'], // 指定要长久化的字段
      paths: [], // 指定要长久化的字段
    },
  },
)
