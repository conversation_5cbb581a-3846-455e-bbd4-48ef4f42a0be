/*
 * @Author: llm
 * @Date: 2025-03-21 15:58:37
 * @LastEditors: llm
 * @LastEditTime: 2025-03-21 16:03:03
 * @Description: 全局打印
 */
import { defineStore } from 'pinia'

export const useGlobalPrintStore = defineStore('globalPrint', () => {
  /**
   * 打印数据
   */
  const printData = ref<any>()

  function setPrintData(data: any) {
    printData.value = data
  }

  function getPrintData() {
    return printData.value
  }

  return {
    printData,
    setPrintData,
    getPrintData,
  }
})
