/*
 * @Author: llm
 * @Date: 2024-07-03 12:00:06
 * @LastEditors: llm
 * @LastEditTime: 2024-07-03 12:00:06
 * @Description:
 */
import { defineStore } from 'pinia'

export const usePatrolPathListStore = defineStore('patrolPathList', () => {
  /**
   * 选择的巡视路径
   */
  const patrolPathList = ref<any[]>([
    {
      warehouseId: '',
      warehouseName: '',
      centreCoordinate: '',
      placeCoordinateItemList: [],
    },
  ])

  function setPatrolPathList(rows: any[]) {
    patrolPathList.value = rows
  }

  function getPatrolPathList() {
    return patrolPathList.value
  }

  return {
    patrolPathList,
    setPatrolPathList,
    getPatrolPathList,
  }
})
