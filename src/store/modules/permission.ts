/*
 * @Author: llm
 * @Date: 2025-02-13 19:23:15
 * @LastEditors: llm
 * @LastEditTime: 2025-07-31 14:53:54
 * @Description:
 */
import { RouteRecordRaw } from 'vue-router'
import { defineStore } from 'pinia'
import { constantRoutes } from '@/router'
import { store } from '@/store'
import path from 'path-browserify'

const menuList = useStorage('menuList', {})
const modules = import.meta.glob('../../views/**/**.vue')
const Layout = () => import('@/layout/index.vue')

/**
 * Use meta.role to determine if the current user has permission
 *
 * @param roles 用户角色集合
 * @param route 路由
 * @returns
 */
const hasPermission = (roles: string[], route: RouteRecordRaw) => {
  if (route.meta && route.meta.roles) {
    // 角色【超级管理员】拥有所有权限，忽略校验
    if (roles.includes('ROOT')) {
      return true
    }
    return roles.some((role) => {
      if (route.meta?.roles !== undefined) {
        return (route.meta.roles as string[]).includes(role)
      }
    })
  }
  return false
}

/**
 * 递归过滤有权限的异步(动态)路由
 *
 * @param routes 接口返回的异步(动态)路由
 * @param roles 用户角色集合
 * @returns 返回用户有权限的异步(动态)路由
 */
const filterAsyncRoutes = (routes: RouteRecordRaw[], roles: string[]) => {
  const asyncRoutes: RouteRecordRaw[] = []

  routes.forEach((route) => {
    const tmpRoute = { ...route } // ES6扩展运算符复制新对象

    // 判断用户(角色)是否有该路由的访问权限
    // if (hasPermission(roles, tmpRoute)) {
    if (tmpRoute.component?.toString() == 'Layout') {
      tmpRoute.component = Layout
    } else {
      const component = modules[`../../views/${tmpRoute.component}.vue`]
      if (component) {
        tmpRoute.component = component
      } else {
        tmpRoute.component = modules['../../views/error-page/404.vue']
      }
    }

    if (tmpRoute.children) {
      tmpRoute.children = filterAsyncRoutes(tmpRoute.children, roles)
    }

    asyncRoutes.push(tmpRoute)
    // }
  })

  return asyncRoutes
}

// setup
export const usePermissionStore = defineStore(
  'permission',
  () => {
    // state
    const routes = ref<RouteRecordRaw[]>([])
    const menu = ref<RouteRecordRaw[]>([])

    // actions
    function setRoutes(newRoutes: RouteRecordRaw[]) {
      routes.value = constantRoutes.concat(newRoutes)
    }

    /**
     * 生成动态路由
     *
     * @param roles 用户角色集合
     * @returns
     */
    const WmsMenu = [
      //基础数据
      {
        path: '/wmsBasicData',
        component: 'Layout',
        redirect: '/wmsBasicData/customerCenter/customerManagement',
        name: 'WmsBasicData',
        meta: {
          title: '基础数据',
          icon: 'nested',
          hidden: false,
        },
        children: [
          //客户中心
          {
            path: 'customerCenter',
            component: 'wms/basicData/customerCenter/index',
            name: 'WmsBasicDataCustomerCenter',
            meta: {
              title: '客户中心',
              hidden: false,
            },
            children: [
              //客户管理
              {
                path: 'customerManagement',
                component: 'wms/basicData/customerCenter/customerManagement/index',
                name: 'WmsBasicDataCustomerCenterCustomerManagement',
                meta: {
                  title: '客户管理',
                  hidden: false,
                },
              },
              //客户合同
              {
                path: 'customerContract',
                component: 'wms/basicData/customerCenter/customerContract/index',
                name: 'WmsBasicDataCustomerCenterCustomerContract',
                meta: {
                  title: '客户合同',
                  hidden: false,
                },
              },
            ],
          },
          //仓储中心
          {
            path: '/wmsStorageCenter',
            component: 'wms/basicData/storageCenter/warehouseManagement/index',
            redirect: '/wmsBasicData/wmsStorageCenter/warehouseManagement',
            name: 'WmsBasicDataStorageCenter',
            meta: {
              title: '仓储中心',
              icon: 'nested',
              hidden: false,
            },
            children: [
              //仓库管理
              {
                path: 'warehouseManagement',
                component: 'wms/basicData/storageCenter/warehouseManagement/index',
                name: 'WmsBasicDataStorageCenterWarehouseManagement',
                meta: {
                  title: '仓库管理',
                  hidden: false,
                },
              },
              //库区管理
              {
                path: 'storageAreaManagement',
                component: 'wms/basicData/storageCenter/storageAreaManagement/index',
                name: 'WmsBasicDataStorageCenterStorageAreaManagement',
                meta: {
                  title: '库区管理',
                  hidden: false,
                },
              },
              //人员管理
              {
                path: 'personnelManagement',
                component: 'wms/basicData/storageCenter/personnelManagement/index',
                name: 'WmsBasicDataStorageCenterPersonnelManagement',
                meta: {
                  title: '人员管理',
                  hidden: false,
                },
              },
              //费用配置
              {
                path: 'feeConfiguration',
                component: 'wms/basicData/storageCenter/feeConfiguration/index',
                name: 'WmsBasicDataStorageCenterFeeConfiguration',
                meta: {
                  title: '费用配置',
                  hidden: false,
                },
              },
            ],
          },
        ],
      },
      //统计中心
      {
        path: '/wmsStatisticsCenter',
        component: 'Layout',
        redirect: '/wmsStatisticsCenter/warehouseStatistics',
        name: 'WmsStatisticsCenter',
        meta: {
          title: '统计中心',
          icon: 'nested',
          hidden: false,
        },
        children: [
          //仓储计费统计主表
          {
            path: 'warehouseStatistics',
            component: 'wms/statisticsCenter/warehouseStatistics/index',
            name: 'WmsStatisticsCenterWarehouseStatistics',
            meta: {
              title: '仓储计费统计主表',
              hidden: false,
            },
          },
          //仓储计费统计明细
          {
            path: 'warehouseStatisticsDetail',
            component: 'wms/statisticsCenter/warehouseStatisticsDetail/index',
            name: 'WmsStatisticsCenterWarehouseStatisticsDetail',
            meta: {
              title: '仓储计费统计明细',
              hidden: false,
            },
          },
        ],
      },
    ]
    const newOutboundMenu = [
      //基础数据
      {
        path: '/outboundBasicData',
        component: 'Layout',
        redirect: '/outboundBasicData/routeMileage',
        name: 'OutboundBasicData',
        meta: {
          title: '基础数据',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //线路里程
          {
            path: 'routeMileage',
            component: 'outbound/basicData/businessData/routeMileage/index',
            name: 'OutboundBasicDataBusinessDataRouteMileage',
            meta: {
              title: '线路里程',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //线路管理
          {
            path: 'routeManagement',
            component: 'outbound/basicData/businessData/routeManagement/index',
            name: 'OutboundBasicDataBusinessDataRouteManagement',
            meta: {
              title: '线路管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //区域管理
          {
            path: 'areaManagement',
            component: 'outbound/basicData/businessData/areaManagement/index',
            name: 'OutboundBasicDataBusinessDataAreaManagement',
            meta: {
              title: '区域管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //地点围栏
          {
            path: 'locationFence',
            component: 'outbound/basicData/businessData/locationFence/index',
            name: 'OutboundBasicDataBusinessDataLocationFence',
            meta: {
              title: '地点围栏',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //收入公式
          {
            path: 'salaryFormula',
            component: 'outbound/basicData/salaryFormula/index',
            name: 'OutboundBasicDataSalaryFormula',
            meta: {
              title: '收入公式',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //自动收入
              {
                path: 'automaticIncome',
                component: 'outbound/basicData/salaryFormula/automaticIncome/index',
                name: 'OutboundBasicDataSalaryFormulaAutomaticIncome',
                meta: {
                  title: '自动计算',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //固定收入
              {
                path: 'fixedIncome',
                component: 'outbound/basicData/salaryFormula/fixedIncome/index',
                name: 'OutboundBasicDataSalaryFormulaFixedIncome',
                meta: {
                  title: '固定收入',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //补贴收入
              {
                path: 'subsidyIncome',
                component: 'outbound/basicData/salaryFormula/subsidyIncome/index',
                name: 'OutboundBasicDataSalaryFormulaSubsidyIncome',
                meta: {
                  title: '补贴设置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //变动收入
              {
                path: 'changeIncome',
                component: 'outbound/basicData/salaryFormula/changeIncome/index',
                name: 'OutboundBasicDataSalaryFormulaChangeIncome',
                meta: {
                  title: '变动收入',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //数据统计
              {
                path: 'dataStatistics',
                component: 'outbound/basicData/salaryFormula/dataStatistics/index',
                name: 'OutboundBasicDataSalaryFormulaDataStatistics',
                meta: {
                  title: '数据统计',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //经销商管理
          {
            path: 'dealerManagement',
            component: 'outbound/basicData/dealerManagement/index',
            name: 'OutboundBasicDataDealerManagement',
            meta: {
              title: '经销商管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //地址管理
              {
                path: 'addressManagement',
                component: 'outbound/basicData/dealerManagement/addressManagement/index',
                name: 'OutboundBasicDataDealerManagementAddressManagement',
                meta: {
                  title: '地址管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //费用管理
              {
                path: 'feeManagement',
                component: 'outbound/basicData/dealerManagement/feeManagement/index',
                name: 'OutboundBasicDataDealerManagementFeeManagement',
                meta: {
                  title: '费用管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
        ],
      },
      //客户管理
      {
        path: '/outboundCustomerManagement',
        component: 'Layout',
        redirect: '/outboundCustomerManagement/customerManagement',
        name: 'OutboundCustomerManagement',
        meta: {
          title: '客户管理',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //客户管理
          {
            path: 'customerManagement',
            component: 'outbound/basicData/businessData/customerManagement/index',
            name: 'OutboundBasicDataBusinessDataCustomerManagement',
            meta: {
              title: '客户管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //客户合同
          {
            path: 'customerContract',
            component: 'outbound/basicData/businessData/customerContract/index',
            name: 'OutboundBasicDataBusinessDataCustomerContract',
            meta: {
              title: '客户合同',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //客户账期
          {
            path: 'customerAccountPeriod',
            component: 'outbound/basicData/businessData/customerAccountPeriod/index',
            name: 'OutboundBasicDataBusinessDataCustomerAccountPeriod',
            meta: {
              title: '客户账期',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //客户回单
          {
            path: 'customerReturn',
            component: 'outbound/basicData/businessData/customerReturn/index',
            name: 'OutboundBasicDataBusinessDataCustomerReturn',
            meta: {
              title: '客户回单',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
        ],
      },
      //外协管理
      {
        path: '/outboundOutsourcingManagement',
        component: 'Layout',
        redirect: '/outboundOutsourcingManagement/outsourcingManagement',
        name: 'OutboundOutsourcingManagement',
        meta: {
          title: '外协管理',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //资质管理
          {
            path: 'qualificationManagement',
            component: 'outbound/basicData/outsourcingManagement/qualificationManagement/index',
            name: 'OutboundBasicDataOutsourcingManagementQualificationManagement',
            meta: {
              title: '资质管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //外协列表
          {
            path: 'outsourcingManagement',
            component: 'outbound/basicData/outsourcingManagement/outsourcingManagement/index',
            name: 'OutboundBasicDataOutsourcingManagementOutsourcingManagement',
            meta: {
              title: '外协列表',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //外协合同
          {
            path: 'outsourcingContract',
            component: 'outbound/basicData/outsourcingManagement/outsourcingContract/index',
            name: 'OutboundBasicDataOutsourcingManagementOutsourcingContract',
            meta: {
              title: '外协合同',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //外协运力
          {
            path: 'powerManagement',
            component: 'outbound/basicData/outsourcingManagement/powerManagement/index',
            name: 'OutboundBasicDataOutsourcingManagementPowerManagement',
            meta: {
              title: '外协运力',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
        ],
      },
      //车队管理
      {
        path: '/outboundFleetManagement',
        component: 'Layout',
        redirect: '/outboundFleetManagement/basicData/vehicleHeaderManagement',
        name: 'OutboundFleetManagement',
        meta: {
          title: '车队管理',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //基础数据
          {
            path: 'basicData',
            component: 'outbound/basicData/fleetData/vehicleHeaderManagement/index',
            name: 'OutboundFleetManagementBasicData',
            meta: {
              title: '基础数据',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //车头管理
              {
                path: 'vehicleHeaderManagement',
                component: 'outbound/basicData/fleetData/vehicleHeaderManagement/index',
                name: 'OutboundBasicDataFleetDataVehicleHeaderManagement',
                meta: {
                  title: '车头管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //挂车管理
              {
                path: 'trailerManagement',
                component: 'outbound/basicData/fleetData/trailerManagement/index',
                name: 'OutboundBasicDataFleetDataTrailerManagement',
                meta: {
                  title: '挂车管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //整车管理
              {
                path: 'vehicleManagement',
                component: 'outbound/basicData/fleetData/vehicleManagement/index',
                name: 'OutboundBasicDataFleetDataVehicleManagement',
                meta: {
                  title: '整车管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //车队分组
              {
                path: 'fleetGroup',
                component: 'outbound/basicData/fleetData/fleetGroup/index',
                name: 'OutboundBasicDataFleetDataFleetGroup',
                meta: {
                  title: '车队分组',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //在途费用
          {
            path: 'onRouteCost',
            component: 'outbound/fleetManagement/onRouteCost/index',
            name: 'OutboundFleetManagementOnRouteCost',
            meta: {
              title: '在途费用',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //油费管理
              {
                path: 'oilFeeManagement',
                component: 'outbound/fleetManagement/oilFeeManagement/index',
                name: 'OutboundFleetManagementOilFeeManagement',
                meta: {
                  title: '油费管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //高速费管理
              {
                path: 'highwayFeeManagement',
                component: 'outbound/fleetManagement/highwayFeeManagement/index',
                name: 'OutboundFleetManagementHighwayFeeManagement',
                meta: {
                  title: '高速费管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //在途费用
              {
                path: 'onRouteCost',
                component: 'outbound/fleetManagement/onRouteCost/onRouteCost/index',
                name: 'OutboundFleetManagementOnRouteCostOnRouteCost',
                meta: {
                  title: '在途费用',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //点检记录
          {
            path: 'inspectionRecord',
            component: 'outbound/fleetManagement/inspectionRecord/index',
            name: 'OutboundFleetManagementInspectionRecord',
            meta: {
              title: '点检记录',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //运输车点检记录
              {
                path: 'transportVehicleInspectionRecord',
                component: 'outbound/fleetManagement/inspectionRecord/transportVehicleInspectionRecord/index',
                name: 'OutboundFleetManagementInspectionRecordTransportVehicleInspectionRecord',
                meta: {
                  title: '运输车点检记录',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //商品车点检记录
              {
                path: 'productVehicleInspectionRecord',
                component: 'outbound/fleetManagement/inspectionRecord/productVehicleInspectionRecord/index',
                name: 'OutboundFleetManagementInspectionRecordProductVehicleInspectionRecord',
                meta: {
                  title: '商品车点检记录',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //证件图片
          {
            path: 'certificateImage',
            component: 'outbound/fleetManagement/certificateImage/index',
            name: 'OutboundFleetManagementCertificateImage',
            meta: {
              title: '证件图片',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //车头证件
              {
                path: 'vehicleHeaderCertificate',
                component: 'outbound/fleetManagement/certificateImage/vehicleHeaderCertificate/index',
                name: 'OutboundFleetManagementCertificateImageVehicleHeaderCertificate',
                meta: {
                  title: '车头证件',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //挂车证件
              {
                path: 'trailerCertificate',
                component: 'outbound/fleetManagement/certificateImage/trailerCertificate/index',
                name: 'OutboundFleetManagementCertificateImageTrailerCertificate',
                meta: {
                  title: '挂车证件',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //年审记录
              {
                path: 'annualInspectionRecord',
                component: 'outbound/fleetManagement/certificateImage/annualInspectionRecord/index',
                name: 'OutboundFleetManagementCertificateImageAnnualInspectionRecord',
                meta: {
                  title: '年审记录',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
        ],
      },
      //维修保养
      {
        path: '/outboundMaintenanceManagement',
        component: 'Layout',
        redirect: '/outboundMaintenanceManagement/basicData/supplierManagement',
        name: 'OutboundMaintenanceManagement',
        meta: {
          title: '维修保养',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //基础数据
          {
            path: 'basicData',
            component: 'outbound/basicData/repairManagement/index',
            name: 'OutboundBasicDataRepairManagement',
            meta: {
              title: '基础数据',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //供应商管理
              {
                path: 'supplierManagement',
                component: 'outbound/basicData/repairManagement/supplierManagement/index',
                name: 'OutboundBasicDataRepairManagementSupplierManagement',
                meta: {
                  title: '供应商管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //维修项目单价
              {
                path: 'repairItemPrice',
                component: 'outbound/basicData/repairManagement/repairItemPrice/index',
                name: 'OutboundBasicDataRepairManagementRepairItemPrice',
                meta: {
                  title: '维修项目单价',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //在途维修
          {
            path: 'onRouteMaintenance',
            component: 'outbound/fleetManagement/onRouteMaintenance/index',
            name: 'OutboundFleetManagementOnRouteMaintenance',
            meta: {
              title: '在途维修',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //在场维修
          {
            path: 'onSiteMaintenance',
            component: 'outbound/fleetManagement/onSiteMaintenance/index',
            name: 'OutboundFleetManagementOnSiteMaintenance',
            meta: {
              title: '在场维修',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //轮胎管理
          {
            path: 'tireManagement',
            component: 'outbound/fleetManagement/tireManagement/index',
            name: 'OutboundFleetManagementTireManagement',
            meta: {
              title: '轮胎管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //轮胎明细
              {
                path: 'tireDetail',
                component: 'outbound/fleetManagement/tireManagement/tireDetail/index',
                name: 'OutboundFleetManagementTireManagementTireDetail',
                meta: {
                  title: '轮胎明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //绑定记录
              {
                path: 'bindingRecord',
                component: 'outbound/fleetManagement/tireManagement/bindingRecord/index',
                name: 'OutboundFleetManagementTireManagementBindingRecord',
                meta: {
                  title: '绑定记录',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
        ],
      },
      //安全管理
      {
        path: '/outboundSafetyManagement',
        component: 'Layout',
        redirect: '/outboundSafetyManagement/insuranceCompany',
        name: 'OutboundSafetyManagement',
        meta: {
          title: '安全管理',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //外协质损管理
          {
            path: 'outsourcingQualityDamageManagement',
            component: 'outbound/safetyManagement/outsourcingQualityDamageManagement/index',
            name: 'OutboundSafetyManagementOutsourcingQualityDamageManagement',
            meta: {
              title: '外协质损管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //事故赔偿
          {
            path: 'accidentCompensation',
            component: 'outbound/safetyManagement/accidentCompensation/index',
            name: 'OutboundSafetyManagementAccidentCompensation',
            meta: {
              title: '事故赔偿',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //违章管理
          {
            path: 'violationManagement',
            component: 'outbound/safetyManagement/violationManagement/index',
            name: 'OutboundSafetyManagementViolationManagement',
            meta: {
              title: '违章管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //保险公司
          {
            path: 'insuranceCompany',
            component: 'outbound/safetyManagement/insuranceCompany/index',
            name: 'OutboundSafetyManagementInsuranceCompany',
            meta: {
              title: '保险公司',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //营运车保险
          {
            path: 'operatingVehicleInsurance',
            component: 'outbound/safetyManagement/operatingVehicleInsurance/index',
            name: 'OutboundSafetyManagementOperatingVehicleInsurance',
            meta: {
              title: '营运车保险',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //商品车保险
          {
            path: 'productVehicleInsurance',
            component: 'outbound/safetyManagement/productVehicleInsurance/index',
            name: 'OutboundSafetyManagementProductVehicleInsurance',
            meta: {
              title: '商品车保险',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //追保管理
          {
            path: 'chaseInsurance',
            component: 'outbound/safetyManagement/chaseInsurance/index',
            name: 'OutboundSafetyManagementChaseInsurance',
            meta: {
              title: '追保管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //车辆保险
          {
            path: 'vehicleInsurance',
            component: 'outbound/safetyManagement/vehicleInsurance/index',
            name: 'OutboundSafetyManagementVehicleInsurance',
            meta: {
              title: '车辆保险',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //代购保险
              {
                path: 'proxyPurchaseInsurance',
                component: 'outbound/safetyManagement/vehicleInsurance/proxyPurchaseInsurance/index',
                name: 'OutboundSafetyManagementVehicleInsuranceProxyPurchaseInsurance',
                meta: {
                  title: '代购保险',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
        ],
      },
      //调度管理
      {
        path: '/outboundDispatchManagement',
        component: 'Layout',
        redirect: '/outboundDispatchManagement/orderManagement',
        name: 'OutboundDispatchManagement',
        meta: {
          title: '调度管理',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //报告管理
          {
            path: 'reportManagement',
            component: 'outbound/businessManagement/reportManagement/index',
            name: 'OutboundBusinessManagementReportManagement',
            meta: {
              title: '报告管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //订单管理
          {
            path: 'orderManagement',
            component: 'outbound/businessManagement/orderManagement/index',
            name: 'OutboundBusinessManagementOrderManagement',
            meta: {
              title: '订单管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //智能调度
          {
            path: 'smartDispatch',
            component: 'outbound/businessManagement/smartDispatch/index',
            name: 'OutboundBusinessManagementSmartDispatch',
            meta: {
              title: '智能调度',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
        ],
      },
      //运输在途
      {
        path: '/transportInTransit',
        component: 'Layout',
        redirect: '/transportInTransit/transportRadar',
        name: 'OutboundTransportInTransit',
        meta: {
          title: '运输在途',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //运力雷达
          {
            path: 'transportRadar',
            component: 'outbound/transportInTransit/transportRadar/index',
            name: 'OutboundTransportInTransitTransportRadar',
            meta: {
              title: '运力雷达',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //车辆轨迹
          {
            path: 'vehicleTrack',
            component: 'outbound/transportInTransit/vehicleTrack/index',
            name: 'OutboundTransportInTransitVehicleTrack',
            meta: {
              title: '车辆轨迹',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //路线规划
          {
            path: 'routePlanning',
            component: 'outbound/transportInTransit/routePlanning/index',
            name: 'OutboundTransportInTransitRoutePlanning',
            meta: {
              title: '路线规划',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //路线共享
          {
            path: 'routeSharing',
            component: 'outbound/transportInTransit/routeSharing/index',
            name: 'OutboundTransportInTransitRouteSharing',
            meta: {
              title: '路线共享',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
        ],
      },
      //监控中心
      {
        path: '/monitorCenter',
        component: 'Layout',
        redirect: '/monitorCenter/fenceMonitor',
        name: 'OutboundMonitorCenter',
        meta: {
          title: '监控中心',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //监控设置
          {
            path: 'monitorSetting',
            component: 'outbound/monitorCenter/monitorSetting/index',
            name: 'OutboundMonitorCenterMonitorSetting',
            meta: {
              title: '监控设置',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //围栏监控设置
              {
                path: 'fenceMonitorSetting',
                component: 'outbound/monitorCenter/monitorSetting/fenceMonitorSetting/index',
                name: 'OutboundMonitorCenterMonitorSettingFenceMonitorSetting',
                meta: {
                  title: '围栏监控设置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //预警监控设置
              {
                path: 'warningMonitorSetting',
                component: 'outbound/monitorCenter/monitorSetting/warningMonitorSetting/index',
                name: 'OutboundMonitorCenterMonitorSettingWarningMonitorSetting',
                meta: {
                  title: '预警监控设置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //监控消息
          {
            path: 'monitorMessage',
            component: 'outbound/monitorCenter/monitorMessage/index',
            name: 'OutboundMonitorCenterMonitorMessage',
            meta: {
              title: '监控消息',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //预警消息
              {
                path: 'warningMessage',
                component: 'outbound/monitorCenter/monitorMessage/warningMessage/index',
                name: 'OutboundMonitorCenterMonitorMessageWarningMessage',
                meta: {
                  title: '预警消息',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //出入围栏
              {
                path: 'fenceMessage',
                component: 'outbound/monitorCenter/monitorMessage/fenceMessage/index',
                name: 'OutboundMonitorCenterMonitorMessageFenceMessage',
                meta: {
                  title: '出入围栏',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //车辆停留
              {
                path: 'vehicleStopMessage',
                component: 'outbound/monitorCenter/monitorMessage/vehicleStopMessage/index',
                name: 'OutboundMonitorCenterMonitorMessageVehicleStopMessage',
                meta: {
                  title: '车辆停留',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //提醒消息
              {
                path: 'remindMessage',
                component: 'outbound/monitorCenter/monitorMessage/remindMessage/index',
                name: 'OutboundMonitorCenterMonitorMessageRemindMessage',
                meta: {
                  title: '提醒消息',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
        ],
      },
      //统计中心
      {
        path: '/statisticsCenter',
        component: 'Layout',
        redirect: '/statisticsCenter/headquartersStatistics',
        name: 'OutboundStatisticsCenter',
        meta: {
          title: '统计中心',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          // 事故质损统计
          {
            path: 'accidentStatistics',
            component: 'outbound/statisticsCenter/accidentStatistics/index',
            name: 'OutboundStatisticsCenterAccidentStatistics',
            meta: {
              title: '事故质损统计',
              hidden: false,
              roles: ['ADMIN'],
            },
            children:[
              // 日统计明细
              {
                path: 'dayStatisticsDetail',
                component: 'outbound/statisticsCenter/accidentStatistics/dayStatisticsDetail/index',
                name: 'OutboundStatisticsCenterAccidentStatisticsDayStatisticsDetail',
                meta: {
                  title: '日统计明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              // 月统计明细
              {
                path: 'monthStatisticsDetail',
                component: 'outbound/statisticsCenter/accidentStatistics/monthStatisticsDetail/index',
                name: 'OutboundStatisticsCenterAccidentStatisticsMonthStatisticsDetail',
                meta: {
                  title: '月统计明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              }
            ]
          },
          //板车成本统计
          {
            path: 'trailerCostStatistics',
            component: 'outbound/statisticsCenter/trailerCostStatistics/index',
            name: 'OutboundStatisticsCenterTrailerCostStatistics',
            meta: {
              title: '板车成本统计',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          // 自有生产成本
          {
            path: 'ownProductionCost',
            component: 'outbound/statisticsCenter/ownProductionCost/index',
            name: 'OutboundStatisticsCenterOwnProductionCost',
            redirect: '/outboundStatisticsCenter/ownProductionCost/costDetail',
            meta: {
              title: '自有生产成本',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //成本明细
              {
                path: 'costDetail',
                component: 'outbound/statisticsCenter/ownProductionCost/costDetail/index',
                name: 'OutboundStatisticsCenterOwnProductionCostCostDetail',
                meta: {
                  title: '成本明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //成本汇总
              {
                path: 'costSummary',
                component: 'outbound/statisticsCenter/ownProductionCost/costSummary/index',
                name: 'OutboundStatisticsCenterOwnProductionCostCostSummary',
                meta: {
                  title: '成本汇总',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //总部及分公司统计
          {
            path: 'headquartersStatistics',
            component: 'outbound/statisticsCenter/headquartersStatistics/index',
            name: 'OutboundStatisticsCenterHeadquartersStatistics',
            meta: {
              title: '总部及分公司统计',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //员工绩效报表
              {
                path: 'staffPerformanceReport',
                component: 'outbound/statisticsCenter/headquartersStatistics/staffPerformanceReport/index',
                name: 'OutboundStatisticsCenterHeadquartersStatisticsStaffPerformanceReport',
                meta: {
                  title: '员工绩效报表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //司机绩效报表
              {
                path: 'driverPerformanceReport',
                component: 'outbound/statisticsCenter/headquartersStatistics/driverPerformanceReport/index',
                name: 'OutboundStatisticsCenterHeadquartersStatisticsDriverPerformanceReport',
                meta: {
                  title: '司机绩效报表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //司机绩效明细
              {
                path: 'driverPerformanceDetail',
                component: 'outbound/statisticsCenter/headquartersStatistics/driverPerformanceDetail/index',
                name: 'OutboundStatisticsCenterHeadquartersStatisticsDriverPerformanceDetail',
                meta: {
                  title: '司机绩效明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //车辆营运报表
              {
                path: 'vehicleOperatingReport',
                component: 'outbound/statisticsCenter/headquartersStatistics/vehicleOperatingReport/index',
                name: 'OutboundStatisticsCenterHeadquartersStatisticsVehicleOperatingReport',
                meta: {
                  title: '车辆营运报表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //车辆营运明细
              {
                path: 'vehicleOperatingDetail',
                component: 'outbound/statisticsCenter/headquartersStatistics/vehicleOperatingDetail/index',
                name: 'OutboundStatisticsCenterHeadquartersStatisticsVehicleOperatingDetail',
                meta: {
                  title: '车辆营运明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //业务执行报表
              {
                path: 'businessExecutionReport',
                component: 'outbound/statisticsCenter/headquartersStatistics/businessExecutionReport/index',
                name: 'OutboundStatisticsCenterHeadquartersStatisticsBusinessExecutionReport',
                meta: {
                  title: '业务执行报表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //客户统计
          {
            path: 'customerStatistics',
            component: 'outbound/statisticsCenter/customerStatistics/index',
            name: 'OutboundStatisticsCenterCustomerStatistics',
            meta: {
              title: '客户统计',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //收支统计
              {
                path: 'revenueStatistics',
                component: 'outbound/statisticsCenter/customerStatistics/revenueStatistics/index',
                name: 'OutboundStatisticsCenterCustomerStatisticsRevenueStatistics',
                meta: {
                  title: '收支统计',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //统计明细
              {
                path: 'statisticsDetail',
                component: 'outbound/statisticsCenter/customerStatistics/statisticsDetail/index',
                name: 'OutboundStatisticsCenterCustomerStatisticsStatisticsDetail',
                meta: {
                  title: '统计明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //承运商统计
          {
            path: 'carrierStatistics',
            component: 'outbound/statisticsCenter/carrierStatistics/index',
            name: 'OutboundStatisticsCenterCarrierStatistics',
            meta: {
              title: '承运商统计',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //承运商营运报表
              {
                path: 'carrierOperatingReport',
                component: 'outbound/statisticsCenter/carrierStatistics/carrierOperatingReport/index',
                name: 'OutboundStatisticsCenterCarrierStatisticsCarrierOperatingReport',
                meta: {
                  title: '承运商营运报表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //统计明细
              {
                path: 'statisticsDetail',
                component: 'outbound/statisticsCenter/carrierStatistics/statisticsDetail/index',
                name: 'OutboundStatisticsCenterCarrierStatisticsStatisticsDetail',
                meta: {
                  title: '统计明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //发运线路统计
          {
            path: 'deliveryLineStatistics',
            component: 'outbound/statisticsCenter/deliveryLineStatistics/index',
            name: 'OutboundStatisticsCenterDeliveryLineStatistics',
            meta: {
              title: '发运线路统计',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //线路统计主表
              {
                path: 'lineStatisticsMainTable',
                component: 'outbound/statisticsCenter/deliveryLineStatistics/lineStatisticsMainTable/index',
                name: 'OutboundStatisticsCenterDeliveryLineStatisticsLineStatisticsMainTable',
                meta: {
                  title: '线路统计主表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //线路统计明细
              {
                path: 'lineStatisticsDetail',
                component: 'outbound/statisticsCenter/deliveryLineStatistics/lineStatisticsDetail/index',
                name: 'OutboundStatisticsCenterDeliveryLineStatisticsLineStatisticsDetail',
                meta: {
                  title: '线路统计明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //异常统计
          {
            path: 'abnormalStatistics',
            component: 'outbound/statisticsCenter/abnormalStatistics/index',
            name: 'OutboundStatisticsCenterAbnormalStatistics',
            meta: {
              title: '异常统计',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //统计主表
              {
                path: 'statisticsMainTable',
                component: 'outbound/statisticsCenter/abnormalStatistics/statisticsMainTable/index',
                name: 'OutboundStatisticsCenterAbnormalStatisticsStatisticsMainTable',
                meta: {
                  title: '统计主表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //统计明细
              {
                path: 'statisticsDetail',
                component: 'outbound/statisticsCenter/abnormalStatistics/statisticsDetail/index',
                name: 'OutboundStatisticsCenterAbnormalStatisticsStatisticsDetail',
                meta: {
                  title: '统计明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //车辆异常统计
          {
            path: 'vehicleAbnormalStatistics',
            component: 'outbound/statisticsCenter/vehicleAbnormalStatistics/index',
            name: 'OutboundStatisticsCenterVehicleAbnormalStatistics',
            meta: {
              title: '车辆异常统计',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //调度单明细
          {
            path: 'dispatchOrderDetail',
            component: 'outbound/statisticsCenter/dispatchOrderDetail/index',
            name: 'OutboundStatisticsCenterDispatchOrderDetail',
            meta: {
              title: '调度单明细',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //自有调度统计
          {
            path: 'ownDispatchStatistics',
            component: 'outbound/statisticsCenter/ownDispatchStatistics/index',
            name: 'OutboundStatisticsCenterOwnDispatchStatistics',
            meta: {
              title: '自有调度统计',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //商品车明细
          {
            path: 'commodityCarDetail',
            component: 'outbound/statisticsCenter/commodityCarDetail/index',
            name: 'OutboundStatisticsCenterCommodityCarDetail',
            meta: {
              title: '商品车明细',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //报销统计
          {
            path: 'reimbursementStatistics',
            component: 'outbound/statisticsCenter/reimbursementStatistics/index',
            name: 'OutboundStatisticsCenterReimbursementStatistics',
            meta: {
              title: '报销统计',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //运营管理看板
          {
            path: 'operationManagementDashboard',
            component: 'outbound/statisticsCenter/operationManagementDashboard/index',
            name: 'OutboundStatisticsCenterOperationManagementDashboard',
            meta: {
              title: '运营管理看板',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //分子公司纬度
              {
                path: 'subCompanyLevel',
                component: 'outbound/statisticsCenter/operationManagementDashboard/subCompanyLevel/index',
                name: 'OutboundStatisticsCenterOperationManagementDashboardSubCompanyLevel',
                meta: {
                  title: '分子公司纬度',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //客户纬度
              {
                path: 'customerLevel',
                component: 'outbound/statisticsCenter/operationManagementDashboard/customerLevel/index',
                name: 'OutboundStatisticsCenterOperationManagementDashboardCustomerLevel',
                meta: {
                  title: '客户纬度',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //自有车队纬度
              {
                path: 'ownFleetLevel',
                component: 'outbound/statisticsCenter/operationManagementDashboard/ownFleetLevel/index',
                name: 'OutboundStatisticsCenterOperationManagementDashboardOwnFleetLevel',
                meta: {
                  title: '自有车队纬度',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //承运商纬度
              {
                path: 'carrierLevel',
                component: 'outbound/statisticsCenter/operationManagementDashboard/carrierLevel/index',
                name: 'OutboundStatisticsCenterOperationManagementDashboardCarrierLevel',
                meta: {
                  title: '承运商纬度',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
        ],
      },
      //审批中心
      {
        path: '/approvalCenter',
        component: 'Layout',
        redirect: '/approvalCenter/loanManagement',
        name: 'ApprovalCenter',
        meta: {
          title: '审批中心',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //事务审批
          {
            path: 'loanManagement',
            component: 'outbound/approvalCenter/loanManagement/index',
            name: 'OutboundApprovalCenterLoanManagement',
            meta: {
              title: '事务审批',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //经费审批
          {
            path: 'expenseManagement',
            component: 'outbound/approvalCenter/expenseManagement/index',
            name: 'OutboundApprovalCenterExpenseManagement',
            meta: {
              title: '经费审批',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //调度单审批
          {
            path: 'dispatchOrderApproval',
            component: 'outbound/approvalCenter/dispatchOrderApproval/index',
            name: 'OutboundApprovalCenterDispatchOrderApproval',
            meta: {
              title: '调度单审批',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
        ],
      },
      //业务管理
      {
        path: '/businessManagement',
        component: 'Layout',
        redirect: '/businessManagement/aiCostBudget',
        name: 'BusinessManagement',
        meta: {
          title: '业务管理',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //AI成本测算
          {
            path: 'aiCostBudget',
            component: 'outbound/businessManagement/aiCostBudget/index',
            name: 'OutboundBusinessManagementAiCostBudget',
            meta: {
              title: 'AI成本测算',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
        ],
      },
      //财务管理
      {
        path: '/financialManagement',
        component: 'Layout',
        redirect: '/financialManagement/customerSettlementManagement/freightCalculation',
        name: 'FinancialManagement',
        meta: {
          title: '财务管理',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //零散订单收款
          {
            path: 'scatteredOrderCollection',
            component: 'outbound/financialManagement/scatteredOrderCollection/index',
            name: 'OutboundFinancialManagementScatteredOrderCollection',
            meta: {
              title: '零散订单收款',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //维修结算
          {
            path: 'supplierRepair',
            component: 'outbound/financialManagement/supplierRepair/index',
            name: 'OutboundFinancialManagementSupplierRepair',
            meta: {
              title: '维修结算',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //结算对账
              {
                path: 'settlementReconciliation',
                component: 'outbound/financialManagement/supplierRepair/settlementReconciliation/index',
                name: 'OutboundFinancialManagementSupplierRepairSettlementReconciliation',
                meta: {
                  title: '结算对账',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //开票管理
              {
                path: 'invoiceManagement',
                component: 'outbound/financialManagement/supplierRepair/invoiceManagement/index',
                name: 'OutboundFinancialManagementSupplierRepairInvoiceManagement',
                meta: {
                  title: '开票管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //发票管理
              {
                path: 'overInvoiceManagement',
                component: 'outbound/financialManagement/supplierRepair/overInvoiceManagement/index',
                name: 'OutboundFinancialManagementSupplierRepairOverInvoiceManagement',
                meta: {
                  title: '发票管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //结算汇总
              {
                path: 'settlementSummary',
                component: 'outbound/financialManagement/supplierRepair/settlementSummary/index',
                name: 'OutboundFinancialManagementSupplierRepairSettlementSummary',
                meta: {
                  title: '结算汇总',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //客户结算管理
          {
            path: 'customerSettlementManagement',
            component: 'outbound/financialManagement/customerSettlementManagement/index',
            name: 'OutboundFinancialManagementCustomerSettlementManagement',
            meta: {
              title: '客户结算管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //运费计算
              {
                path: 'freightCalculation',
                component: 'outbound/financialManagement/customerSettlementManagement/freightCalculation/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementFreightCalculation',
                meta: {
                  title: '运费计算',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //对账管理
              {
                path: 'reconciliationManagement',
                component: 'outbound/financialManagement/customerSettlementManagement/reconciliationManagement/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementReconciliationManagement',
                meta: {
                  title: '对账管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //挂账管理
              {
                path: 'overdueManagement',
                component: 'outbound/financialManagement/customerSettlementManagement/overdueManagement/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementOverdueManagement',
                meta: {
                  title: '挂账管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //对账审批
              {
                path: 'reconciliationApproval',
                component: 'outbound/financialManagement/customerSettlementManagement/reconciliationApproval/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementReconciliationApproval',
                meta: {
                  title: '对账审批',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //开票管理
              {
                path: 'invoiceManagement',
                component: 'outbound/financialManagement/customerSettlementManagement/invoiceManagement/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementInvoiceManagement',
                meta: {
                  title: '开票管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //发票管理
              {
                path: 'overInvoiceManagement',
                component: 'outbound/financialManagement/customerSettlementManagement/overInvoiceManagement/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementOverInvoiceManagement',
                meta: {
                  title: '发票管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //收款管理
              {
                path: 'receiptManagement',
                component: 'outbound/financialManagement/customerSettlementManagement/receiptManagement/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementReceiptManagement',
                meta: {
                  title: '收款管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //应收账汇总
              {
                path: 'receivableAccountSummary',
                component: 'outbound/financialManagement/customerSettlementManagement/receivableAccountSummary/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementReceivableAccountSummary',
                meta: {
                  title: '应收账汇总',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //回单管理
          {
            path: 'refundManagement',
            component: 'outbound/financialManagement/refundManagement/index',
            name: 'OutboundFinancialManagementRefundManagement',
            meta: {
              title: '回单管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //车辆贷款
          {
            path: 'vehicleLoanManagement',
            component: 'outbound/financialManagement/vehicleLoanManagement/index',
            name: 'OutboundFinancialManagementVehicleLoanManagement',
            meta: {
              title: '车辆贷款',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //贷款信息
              {
                path: 'loanInformation',
                component: 'outbound/financialManagement/vehicleLoanManagement/loanInformation/index',
                name: 'OutboundFinancialManagementVehicleLoanManagementLoanInformation',
                meta: {
                  title: '贷款信息',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //还款信息
              {
                path: 'repaymentInformation',
                component: 'outbound/financialManagement/vehicleLoanManagement/repaymentInformation/index',
                name: 'OutboundFinancialManagementVehicleLoanManagementRepaymentInformation',
                meta: {
                  title: '还款信息',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },

          //下游结算管理
          {
            path: 'downstreamSettlementManagement',
            component: 'outbound/financialManagement/downstreamSettlementManagement/index',
            name: 'OutboundFinancialManagementDownstreamSettlementManagement',
            meta: {
              title: '下游结算管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //运费计算
              {
                path: 'freightCalculation',
                component: 'outbound/financialManagement/downstreamSettlementManagement/freightCalculation/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementFreightCalculation',
                meta: {
                  title: '运费计算',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //对账管理
              {
                path: 'reconciliationManagement',
                component: 'outbound/financialManagement/downstreamSettlementManagement/reconciliationManagement/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementReconciliationManagement',
                meta: {
                  title: '对账管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //对账审批
              {
                path: 'reconciliationApproval',
                component: 'outbound/financialManagement/downstreamSettlementManagement/reconciliationApproval/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementReconciliationApproval',
                meta: {
                  title: '对账审批',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //开票管理
              {
                path: 'invoiceManagement',
                component: 'outbound/financialManagement/downstreamSettlementManagement/invoiceManagement/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementInvoiceManagement',
                meta: {
                  title: '开票管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //发票管理
              {
                path: 'overInvoiceManagement',
                component: 'outbound/financialManagement/downstreamSettlementManagement/overInvoiceManagement/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementOverInvoiceManagement',
                meta: {
                  title: '发票管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //付款管理
              {
                path: 'paymentManagement',
                component: 'outbound/financialManagement/downstreamSettlementManagement/paymentManagement/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementPaymentManagement',
                meta: {
                  title: '付款管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //应付账汇总
              {
                path: 'payableAccountSummary',
                component: 'outbound/financialManagement/downstreamSettlementManagement/payableAccountSummary/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementPayableAccountSummary',
                meta: {
                  title: '应付账汇总',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //司机补贴结算
          {
            path: 'driverSubsidySettlement',
            component: 'outbound/financialManagement/driverSubsidySettlement/index',
            name: 'OutboundFinancialManagementDriverSubsidySettlement',
            meta: {
              title: '司机补贴结算',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //补贴计算
              {
                path: 'subsidyCalculation',
                component: 'outbound/financialManagement/driverSubsidySettlement/subsidyCalculation/index',
                name: 'OutboundFinancialManagementDriverSubsidySettlementSubsidyCalculation',
                meta: {
                  title: '补贴计算',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //补贴对账
              {
                path: 'subsidyReconciliation',
                component: 'outbound/financialManagement/driverSubsidySettlement/subsidyReconciliation/index',
                name: 'OutboundFinancialManagementDriverSubsidySettlementSubsidyReconciliation',
                meta: {
                  title: '补贴对账',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //补贴审批
              {
                path: 'subsidyApproval',
                component: 'outbound/financialManagement/driverSubsidySettlement/subsidyApproval/index',
                name: 'OutboundFinancialManagementDriverSubsidySettlementSubsidyApproval',
                meta: {
                  title: '补贴审批',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //补贴结算汇总
              {
                path: 'subsidySettlementSummary',
                component: 'outbound/financialManagement/driverSubsidySettlement/subsidySettlementSummary/index',
                name: 'OutboundFinancialManagementDriverSubsidySettlementSubsidySettlementSummary',
                meta: {
                  title: '补贴结算汇总',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //借支管理
          {
            path: 'loanManagement',
            component: 'outbound/financialManagement/loanManagement/index',
            name: 'OutboundFinancialManagementLoanManagement',
            meta: {
              title: '生产借支管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //现金借支管理
              {
                path: 'cashLoanManagement',
                component: 'outbound/financialManagement/loanManagement/cashLoanManagement/index',
                name: 'OutboundFinancialManagementLoanManagementCashLoanManagement',
                meta: {
                  title: '现金借支管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
                // children: [
                //   //现金借支申请
                //   {
                //     path: 'cashLoanApplication',
                //     component: 'outbound/financialManagement/loanManagement/cashLoanManagement/cashLoanApplication/index',
                //     name: 'OutboundFinancialManagementLoanManagementCashLoanManagementCashLoanApplication',
                //     meta: {
                //       title: '现金借支申请',
                //       hidden: false,
                //       roles: ['ADMIN'],
                //     },
                //   },
                //   //现金借支审批
                //   {
                //     path: 'cashLoanApproval',
                //     component: 'outbound/financialManagement/loanManagement/cashLoanManagement/cashLoanApproval/index',
                //     name: 'OutboundFinancialManagementLoanManagementCashLoanManagementCashLoanApproval',
                //     meta: {
                //       title: '现金借支审批',
                //       hidden: false,
                //       roles: ['ADMIN'],
                //     },
                //   },
                //   //现金借支明细
                //   {
                //     path: 'cashLoanDetail',
                //     component: 'outbound/financialManagement/loanManagement/cashLoanManagement/cashLoanDetail/index',
                //     name: 'OutboundFinancialManagementLoanManagementCashLoanManagementCashLoanDetail',
                //     meta: {
                //       title: '现金借支明细',
                //       hidden: false,
                //       roles: ['ADMIN'],
                //     },
                //   },
                // ],
              },
              //油费管理
              {
                path: 'oilLoanManagement',
                component: 'outbound/financialManagement/loanManagement/oilLoanManagement/index',
                name: 'OutboundFinancialManagementLoanManagementOilLoanManagement',
                meta: {
                  title: '油费管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
                children: [
                  // //油费借支申请
                  // {
                  //   path: 'oilLoanApplication',
                  //   component: 'outbound/financialManagement/loanManagement/oilLoanManagement/oilLoanApplication/index',
                  //   name: 'OutboundFinancialManagementLoanManagementOilLoanManagementOilLoanApplication',
                  //   meta: {
                  //     title: '油费借支申请',
                  //     hidden: false,
                  //     roles: ['ADMIN'],
                  //   },
                  // },
                  // //油费借支审批
                  // {
                  //   path: 'oilLoanApproval',
                  //   component: 'outbound/financialManagement/loanManagement/oilLoanManagement/oilLoanApproval/index',
                  //   name: 'OutboundFinancialManagementLoanManagementOilLoanManagementOilLoanApproval',
                  //   meta: {
                  //     title: '油费借支审批',
                  //     hidden: false,
                  //     roles: ['ADMIN'],
                  //   },
                  // },
                  // //油费借支明细
                  // {
                  //   path: 'oilLoanDetail',
                  //   component: 'outbound/financialManagement/loanManagement/oilLoanManagement/oilLoanDetail/index',
                  //   name: 'OutboundFinancialManagementLoanManagementOilLoanManagementOilLoanDetail',
                  //   meta: {
                  //     title: '油费借支明细',
                  //     hidden: false,
                  //     roles: ['ADMIN'],
                  //   },
                  // },
                  // //油费借支配置
                  // {
                  //   path: 'oilLoanConfig',
                  //   component: 'outbound/financialManagement/loanManagement/oilLoanManagement/oilLoanConfig/index',
                  //   name: 'OutboundFinancialManagementLoanManagementOilLoanManagementOilLoanConfig',
                  //   meta: {
                  //     title: '油费借支配置',
                  //     hidden: false,
                  //     roles: ['ADMIN'],
                  //   },
                  // },
                  // 油费列表
                  {
                    path: 'oilList',
                    component: 'outbound/financialManagement/loanManagement/oilLoanManagement/oilList/index',
                    name: 'OutboundFinancialManagementLoanManagementOilLoanManagementOilList',
                    meta: {
                      title: '油费列表',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                  //油卡管理
                  {
                    path: 'oilCardManagement',
                    component: 'outbound/financialManagement/loanManagement/oilLoanManagement/oilCardManagement/index',
                    name: 'OutboundFinancialManagementLoanManagementOilLoanManagementOilCardManagement',
                    meta: {
                      title: '油卡管理',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                  //油费设置
                  {
                    path: 'oilConfig',
                    component: 'outbound/financialManagement/loanManagement/oilLoanManagement/oilConfig/index',
                    name: 'OutboundFinancialManagementLoanManagementOilLoanManagementOilConfig',
                    meta: {
                      title: '油费设置',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                ],
              },
              //ETC管理
              {
                path: 'etcManagement',
                component: 'outbound/financialManagement/loanManagement/etcManagement/index',
                name: 'OutboundFinancialManagementLoanManagementEtcManagement',
                meta: {
                  title: 'ETC管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
                children: [
                  //ETC费用列表
                  {
                    path: 'etcList',
                    component: 'outbound/financialManagement/loanManagement/etcManagement/etcList/index',
                    name: 'OutboundFinancialManagementLoanManagementEtcManagementEtcList',
                    meta: {
                      title: 'ETC费用列表',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                  //ETC卡管理
                  {
                    path: 'etcCardManagement',
                    component: 'outbound/financialManagement/loanManagement/etcManagement/etcCardManagement/index',
                    name: 'OutboundFinancialManagementLoanManagementEtcManagementEtcCardManagement',
                    meta: {
                      title: 'ETC卡管理',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                ],
              },
            ],
          },
          //基地油管理
          {
            path: 'baseOilManagement',
            component: 'outbound/financialManagement/baseOilManagement/index',
            name: 'OutboundFinancialManagementBaseOilManagement',
            meta: {
              title: '基地油管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //撬装供应商
              {
                path: 'craneSupplierManagement',
                component: 'outbound/financialManagement/baseOilManagement/craneSupplierManagement/index',
                name: 'OutboundFinancialManagementBaseOilManagementCraneSupplierManagement',
                meta: {
                  title: '撬装供应商',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //油库管理
              {
                path: 'oilWarehouseManagement',
                component: 'outbound/financialManagement/baseOilManagement/oilWarehouseManagement/index',
                name: 'OutboundFinancialManagementBaseOilManagementOilWarehouseManagement',
                meta: {
                  title: '油库管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //加油台账
              {
                path: 'oilAccount',
                component: 'outbound/financialManagement/baseOilManagement/oilAccount/index',
                name: 'OutboundFinancialManagementBaseOilManagementOilAccount',
                meta: {
                  title: '加油台账',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //采买管理
          {
            path: 'purchaseManagement',
            component: 'outbound/financialManagement/purchaseManagement/index',
            name: 'OutboundFinancialManagementPurchaseManagement',
            meta: {
              title: '采买管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //采买列表
              {
                path: 'purchaseList',
                component: 'outbound/financialManagement/purchaseManagement/purchaseList/index',
                name: 'OutboundFinancialManagementPurchaseManagementPurchaseList',
                meta: {
                  title: '采买列表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //采买配置
              {
                path: 'purchaseConfig',
                component: 'outbound/financialManagement/purchaseManagement/purchaseConfig/index',
                name: 'OutboundFinancialManagementPurchaseManagementPurchaseConfig',
                meta: {
                  title: '采买配置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //付款管理
          {
            path: 'paymentManagement',
            component: 'outbound/financialManagement/paymentManagement/index',
            name: 'OutboundFinancialManagementPaymentManagement',
            meta: {
              title: '付款管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //补贴付款
              {
                path: 'subsidyPayment',
                component: 'outbound/financialManagement/paymentManagement/subsidyPayment/index',
                name: 'OutboundFinancialManagementPaymentManagementSubsidyPayment',
                meta: {
                  title: '补贴付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //借支付款
              {
                path: 'loanPayment',
                component: 'outbound/financialManagement/paymentManagement/loanPayment/index',
                name: 'OutboundFinancialManagementPaymentManagementLoanPayment',
                meta: {
                  title: '借支付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //采买付款
              {
                path: 'purchasePayment',
                component: 'outbound/financialManagement/paymentManagement/purchasePayment/index',
                name: 'OutboundFinancialManagementPaymentManagementPurchasePayment',
                meta: {
                  title: '采买付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //工资结算
              {
                path: 'salarySettlement',
                component: 'outbound/financialManagement/paymentManagement/salarySettlement/index',
                name: 'OutboundFinancialManagementPaymentManagementSalarySettlement',
                meta: {
                  title: '工资结算',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //报销付款
              {
                path: 'reimbursementPayment',
                component: 'outbound/financialManagement/paymentManagement/reimbursementPayment/index',
                name: 'OutboundFinancialManagementPaymentManagementReimbursementPayment',
                meta: {
                  title: '报销付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //油费付款
              {
                path: 'oilPayment',
                component: 'outbound/financialManagement/paymentManagement/oilPayment/index',
                name: 'OutboundFinancialManagementPaymentManagementOilPayment',
                meta: {
                  title: '油费付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //维修付款
              {
                path: 'supplierRepairPayment',
                component: 'outbound/financialManagement/paymentManagement/supplierRepairPayment/index',
                name: 'OutboundFinancialManagementPaymentManagementSupplierRepairPayment',
                meta: {
                  title: '维修付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //ETC付款
              {
                path: 'etcPayment',
                component: 'outbound/financialManagement/paymentManagement/etcPayment/index',
                name: 'OutboundFinancialManagementPaymentManagementEtcPayment',
                meta: {
                  title: 'ETC付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //质损付款
              {
                path: 'lossPayment',
                component: 'outbound/financialManagement/paymentManagement/lossPayment/index',
                name: 'OutboundFinancialManagementPaymentManagementLossPayment',
                meta: {
                  title: '质损付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //外协付款
              {
                path: 'outsourcingPayment',
                component: 'outbound/financialManagement/paymentManagement/outsourcingPayment/index',
                name: 'OutboundFinancialManagementPaymentManagementOutsourcingPayment',
                meta: {
                  title: '外协付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //维修付款
              {
                path: 'maintenancePayment',
                component: 'outbound/financialManagement/paymentManagement/maintenancePayment/index',
                name: 'OutboundFinancialManagementPaymentManagementMaintenancePayment',
                meta: {
                  title: '维修付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //司机账目管理
          {
            path: 'driverAccountManagement',
            component: 'outbound/financialManagement/driverAccountManagement/index',
            name: 'OutboundFinancialManagementDriverAccountManagement',
            meta: {
              title: '司机账目管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //驾驶员现金挂账
              {
                path: 'cashAccount',
                component: 'outbound/financialManagement/driverAccountManagement/cashAccount/index',
                name: 'OutboundFinancialManagementDriverAccountManagementCashAccount',
                meta: {
                  title: '驾驶员现金挂账',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //驾驶员油费挂账
              {
                path: 'oilAccount',
                component: 'outbound/financialManagement/driverAccountManagement/oilAccount/index',
                name: 'OutboundFinancialManagementDriverAccountManagementOilAccount',
                meta: {
                  title: '驾驶员油费挂账',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //驾驶员保障金管理
              {
                path: 'guaranteeManagement',
                component: 'outbound/financialManagement/driverAccountManagement/guaranteeManagement/index',
                name: 'OutboundFinancialManagementDriverAccountManagementGuaranteeManagement',
                meta: {
                  title: '驾驶员保障金管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
                children: [
                  //保障金设置
                  {
                    path: 'guaranteeConfig',
                    component: 'outbound/financialManagement/driverAccountManagement/guaranteeManagement/guaranteeConfig/index',
                    name: 'OutboundFinancialManagementDriverAccountManagementGuaranteeManagementGuaranteeConfig',
                    meta: {
                      title: '保障金设置',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                  //保障金明细
                  {
                    path: 'guaranteeDetail',
                    component: 'outbound/financialManagement/driverAccountManagement/guaranteeManagement/guaranteeDetail/index',
                    name: 'OutboundFinancialManagementDriverAccountManagementGuaranteeManagementGuaranteeDetail',
                    meta: {
                      title: '保障金明细',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                ],
              },
              //驾驶员备用金明细
              {
                path: 'reserveDetail',
                component: 'outbound/financialManagement/driverAccountManagement/reserveDetail/index',
                name: 'OutboundFinancialManagementDriverAccountManagementReserveDetail',
                meta: {
                  title: '驾驶员备用金明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //司机收款明细
              {
                path: 'driverCollectionDetail',
                component: 'outbound/financialManagement/driverAccountManagement/driverCollectionDetail/index',
                name: 'OutboundFinancialManagementDriverAccountManagementDriverCollectionDetail',
                meta: {
                  title: '司机收款明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //报销管理
          {
            path: 'reimbursementManagement',
            component: 'outbound/financialManagement/reimbursementManagement/index',
            name: 'OutboundFinancialManagementReimbursementManagement',
            meta: {
              title: '报销管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //报销申请
              {
                path: 'reimbursementApply',
                component: 'outbound/financialManagement/reimbursementManagement/reimbursementApply/index',
                name: 'OutboundFinancialManagementReimbursementManagementReimbursementApply',
                meta: {
                  title: '报销申请',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //报销审批
              {
                path: 'reimbursementApproval',
                component: 'outbound/financialManagement/reimbursementManagement/reimbursementApproval/index',
                name: 'OutboundFinancialManagementReimbursementManagementReimbursementApproval',
                meta: {
                  title: '报销审批',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //报销明细
              {
                path: 'reimbursementDetail',
                component: 'outbound/financialManagement/reimbursementManagement/reimbursementDetail/index',
                name: 'OutboundFinancialManagementReimbursementManagementReimbursementDetail',
                meta: {
                  title: '报销明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //报销配置
              {
                path: 'reimbursementConfig',
                component: 'outbound/financialManagement/reimbursementManagement/reimbursementConfig/index',
                name: 'OutboundFinancialManagementReimbursementManagementReimbursementConfig',
                meta: {
                  title: '报销配置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //油费管理
          {
            path: 'oilManagement',
            component: 'outbound/financialManagement/oilManagement/index',
            name: 'OutboundFinancialManagementOilManagement',
            meta: {
              title: '油费管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //基地进油管理
              {
                path: 'baseInOilManagement',
                component: 'outbound/financialManagement/oilManagement/baseInOilManagement/index',
                name: 'OutboundFinancialManagementOilManagementBaseInOilManagement',
                meta: {
                  title: '基地进油管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //司机加油管理
              {
                path: 'driverInOilManagement',
                component: 'outbound/financialManagement/oilManagement/driverInOilManagement/index',
                name: 'OutboundFinancialManagementOilManagementDriverInOilManagement',
                meta: {
                  title: '司机加油管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
        ],
      },
      //行政管理
      {
        path: '/administrativeManagement',
        component: 'Layout',
        redirect: '/administrativeManagement/qualityLossManagement',
        name: 'AdministrativeManagement',
        meta: {
          title: '行政管理',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //公司资质管理
          {
            path: 'companyQualificationManagement',
            component: 'outbound/administrativeManagement/companyQualificationManagement/index',
            name: 'OutboundAdministrativeManagementCompanyQualificationManagement',
            meta: {
              title: '公司资质管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //工资管理
          {
            path: 'salaryManagement',
            component: 'outbound/administrativeManagement/salaryManagement/index',
            name: 'OutboundAdministrativeManagementSalaryManagement',
            meta: {
              title: '工资管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //人员保险
          {
            path: 'personnelInsurance',
            component: 'outbound/administrativeManagement/personnelInsurance/index',
            name: 'OutboundAdministrativeManagementPersonnelInsurance',
            meta: {
              title: '人员保险',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //员工管理
          {
            path: 'employeeManagement',
            component: 'outbound/administrativeManagement/employeeManagement/index',
            name: 'OutboundAdministrativeManagementEmployeeManagement',
            meta: {
              title: '员工管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //离职管理
          {
            path: 'resignationManagement',
            component: 'outbound/administrativeManagement/resignationManagement/index',
            name: 'OutboundAdministrativeManagementResignationManagement',
            meta: {
              title: '离职管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //请假管理
          {
            path: 'leaveManagement',
            component: 'outbound/administrativeManagement/leaveManagement/index',
            name: 'OutboundAdministrativeManagementLeaveManagement',
            meta: {
              title: '请假管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //领用管理
          {
            path: 'useManagement',
            component: 'outbound/administrativeManagement/useManagement/index',
            name: 'OutboundAdministrativeManagementUseManagement',
            meta: {
              title: '领用管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
        ],
      },
    ]
    const OutboundMenu = [
      //基础数据
      {
        path: '/outboundBasicData',
        component: 'Layout',
        redirect: '/outboundBasicData/personnelData/staffManagement',
        name: 'OutboundBasicData',
        meta: {
          title: '基础数据',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          {
            //业务数据
            path: 'businessData',
            component: 'outbound/basicData/businessData/index',
            name: 'OutboundBasicDataBusinessData',
            meta: {
              title: '业务数据',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              // 保险管理
              {
                path: 'insuranceManagement',
                component: 'outbound/basicData/businessData/insuranceManagement/index',
                name: 'OutboundBasicDataBusinessDataInsuranceManagement',
                meta: {
                  title: '保险管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //线路里程
              {
                path: 'routeMileage',
                component: 'outbound/basicData/businessData/routeMileage/index',
                name: 'OutboundBasicDataBusinessDataRouteMileage',
                meta: {
                  title: '线路里程',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //客户管理
              {
                path: 'customerManagement',
                component: 'outbound/basicData/businessData/customerManagement/index',
                name: 'OutboundBasicDataBusinessDataCustomerManagement',
                meta: {
                  title: '客户管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //客户合同
              {
                path: 'customerContract',
                component: 'outbound/basicData/businessData/customerContract/index',
                name: 'OutboundBasicDataBusinessDataCustomerContract',
                meta: {
                  title: '客户合同',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //线路管理
              {
                path: 'routeManagement',
                component: 'outbound/basicData/businessData/routeManagement/index',
                name: 'OutboundBasicDataBusinessDataRouteManagement',
                meta: {
                  title: '线路管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //里程管理
              {
                path: 'mileageManagement',
                component: 'outbound/basicData/businessData/mileageManagement/index',
                name: 'OutboundBasicDataBusinessDataMileageManagement',
                meta: {
                  title: '里程管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //商品车管理
              {
                path: 'productVehicleManagement',
                component: 'outbound/basicData/businessData/productVehicleManagement/index',
                name: 'OutboundBasicDataBusinessDataProductVehicleManagement',
                meta: {
                  title: '商品车管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //区域管理
              {
                path: 'areaManagement',
                component: 'outbound/basicData/businessData/areaManagement/index',
                name: 'OutboundBasicDataBusinessDataAreaManagement',
                meta: {
                  title: '区域管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //省份管理
              {
                path: 'provinceManagement',
                component: 'outbound/basicData/businessData/provinceManagement/index',
                name: 'OutboundBasicDataBusinessDataProvinceManagement',
                meta: {
                  title: '省份管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //地点围栏
              {
                path: 'locationFence',
                component: 'outbound/basicData/businessData/locationFence/index',
                name: 'OutboundBasicDataBusinessDataLocationFence',
                meta: {
                  title: '地点围栏',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //品牌车型
              {
                path: 'brandModel',
                component: 'outbound/basicData/businessData/brandModel/index',
                name: 'OutboundBasicDataBusinessDataBrandModel',
                meta: {
                  title: '品牌车型',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          {
            //车队数据
            path: 'fleetData',
            component: 'outbound/basicData/fleetData/index',
            name: 'OutboundBasicDataFleetData',
            meta: {
              title: '自有车队管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //车头管理
              {
                path: 'vehicleHeaderManagement',
                component: 'outbound/basicData/fleetData/vehicleHeaderManagement/index',
                name: 'OutboundBasicDataFleetDataVehicleHeaderManagement',
                meta: {
                  title: '车头管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //挂车管理
              {
                path: 'trailerManagement',
                component: 'outbound/basicData/fleetData/trailerManagement/index',
                name: 'OutboundBasicDataFleetDataTrailerManagement',
                meta: {
                  title: '挂车管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //整车管理
              {
                path: 'vehicleManagement',
                component: 'outbound/basicData/fleetData/vehicleManagement/index',
                name: 'OutboundBasicDataFleetDataVehicleManagement',
                meta: {
                  title: '整车管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //驾驶员管理
              {
                path: 'driverManagement',
                component: 'outbound/basicData/fleetData/driverManagement/index',
                name: 'OutboundBasicDataFleetDataDriverManagement',
                meta: {
                  title: '驾驶员管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //车队管理
              {
                path: 'fleetManagement',
                component: 'outbound/basicData/fleetData/fleetManagement/index',
                name: 'OutboundBasicDataFleetDataFleetManagement',
                meta: {
                  title: '车队管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //外协管理
          {
            path: 'outsourcingManagement',
            component: 'outbound/basicData/outsourcingManagement/index',
            name: 'OutboundBasicDataOutsourcingManagement',
            meta: {
              title: '外协管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //外协管理
              {
                path: 'outsourcingManagement',
                component: 'outbound/basicData/outsourcingManagement/outsourcingManagement/index',
                name: 'OutboundBasicDataOutsourcingManagementOutsourcingManagement',
                meta: {
                  title: '外协管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //外协合同
              {
                path: 'outsourcingContract',
                component: 'outbound/basicData/outsourcingManagement/outsourcingContract/index',
                name: 'OutboundBasicDataOutsourcingManagementOutsourcingContract',
                meta: {
                  title: '外协合同',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //运力管理
              {
                path: 'powerManagement',
                component: 'outbound/basicData/outsourcingManagement/powerManagement/index',
                name: 'OutboundBasicDataOutsourcingManagementPowerManagement',
                meta: {
                  title: '运力管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //工资公式
          {
            path: 'salaryFormula',
            component: 'outbound/basicData/salaryFormula/index',
            name: 'OutboundBasicDataSalaryFormula',
            meta: {
              title: '收入公式',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //其他收入项
              {
                path: 'otherIncome',
                component: 'outbound/basicData/salaryFormula/otherIncome/index',
                name: 'OutboundBasicDataSalaryFormulaOtherIncome',
                meta: {
                  title: '其他收入项',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //自动收入
              {
                path: 'automaticIncome',
                component: 'outbound/basicData/salaryFormula/automaticIncome/index',
                name: 'OutboundBasicDataSalaryFormulaAutomaticIncome',
                meta: {
                  title: '自动收入',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //固定收入
              {
                path: 'fixedIncome',
                component: 'outbound/basicData/salaryFormula/fixedIncome/index',
                name: 'OutboundBasicDataSalaryFormulaFixedIncome',
                meta: {
                  title: '固定收入',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //补贴收入
              {
                path: 'subsidyIncome',
                component: 'outbound/basicData/salaryFormula/subsidyIncome/index',
                name: 'OutboundBasicDataSalaryFormulaSubsidyIncome',
                meta: {
                  title: '补贴收入',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //变动收入
              {
                path: 'changeIncome',
                component: 'outbound/basicData/salaryFormula/changeIncome/index',
                name: 'OutboundBasicDataSalaryFormulaChangeIncome',
                meta: {
                  title: '变动收入',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          // 维修管理
          {
            path: 'repairManagement',
            component: 'outbound/basicData/repairManagement/index',
            name: 'OutboundBasicDataRepairManagement',
            meta: {
              title: '维修管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //供应商管理
              {
                path: 'supplierManagement',
                component: 'outbound/basicData/repairManagement/supplierManagement/index',
                name: 'OutboundBasicDataRepairManagementSupplierManagement',
                meta: {
                  title: '供应商管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //维修项目单价
              {
                path: 'repairItemPrice',
                component: 'outbound/basicData/repairManagement/repairItemPrice/index',
                name: 'OutboundBasicDataRepairManagementRepairItemPrice',
                meta: {
                  title: '维修项目单价',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
        ],
      },
      //业务管理
      {
        path: '/businessManagement',
        component: 'Layout',
        redirect: '/businessManagement/orderManagement',
        name: 'OutboundBusinessManagement',
        meta: {
          title: '业务管理',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //订单管理
          {
            path: 'orderManagement',
            component: 'outbound/businessManagement/orderManagement/index',
            name: 'OutboundBusinessManagementOrderManagement',
            meta: {
              title: '订单管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //智能调度
          {
            path: 'smartDispatch',
            component: 'outbound/businessManagement/smartDispatch/index',
            name: 'OutboundBusinessManagementSmartDispatch',
            meta: {
              title: '智能调度',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //质损管理
          {
            path: 'qualityLossManagement',
            component: 'outbound/businessManagement/qualityLossManagement/index',
            name: 'OutboundBusinessManagementQualityLossManagement',
            meta: {
              title: '质损管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //买断管理
          {
            path: 'buyDownManagement',
            component: 'outbound/businessManagement/buyDownManagement/index',
            name: 'OutboundBusinessManagementBuyDownManagement',
            meta: {
              title: '买断管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
        ],
      },
      //运输在途
      {
        path: '/transportInTransit',
        component: 'Layout',
        redirect: '/transportInTransit/transportRadar',
        name: 'OutboundTransportInTransit',
        meta: {
          title: '运输在途',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //运力雷达
          {
            path: 'transportRadar',
            component: 'outbound/transportInTransit/transportRadar/index',
            name: 'OutboundTransportInTransitTransportRadar',
            meta: {
              title: '运力雷达',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //车辆轨迹
          {
            path: 'vehicleTrack',
            component: 'outbound/transportInTransit/vehicleTrack/index',
            name: 'OutboundTransportInTransitVehicleTrack',
            meta: {
              title: '车辆轨迹',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //路线规划
          {
            path: 'routePlanning',
            component: 'outbound/transportInTransit/routePlanning/index',
            name: 'OutboundTransportInTransitRoutePlanning',
            meta: {
              title: '路线规划',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //路线共享
          {
            path: 'routeSharing',
            component: 'outbound/transportInTransit/routeSharing/index',
            name: 'OutboundTransportInTransitRouteSharing',
            meta: {
              title: '路线共享',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
        ],
      },
      //监控中心
      {
        path: '/monitorCenter',
        component: 'Layout',
        redirect: '/monitorCenter/fenceMonitor',
        name: 'OutboundMonitorCenter',
        meta: {
          title: '监控中心',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //监控设置
          {
            path: 'monitorSetting',
            component: 'outbound/monitorCenter/monitorSetting/index',
            name: 'OutboundMonitorCenterMonitorSetting',
            meta: {
              title: '监控设置',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //围栏监控设置
              {
                path: 'fenceMonitorSetting',
                component: 'outbound/monitorCenter/monitorSetting/fenceMonitorSetting/index',
                name: 'OutboundMonitorCenterMonitorSettingFenceMonitorSetting',
                meta: {
                  title: '围栏监控设置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //预警监控设置
              {
                path: 'warningMonitorSetting',
                component: 'outbound/monitorCenter/monitorSetting/warningMonitorSetting/index',
                name: 'OutboundMonitorCenterMonitorSettingWarningMonitorSetting',
                meta: {
                  title: '预警监控设置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //监控消息
          {
            path: 'monitorMessage',
            component: 'outbound/monitorCenter/monitorMessage/index',
            name: 'OutboundMonitorCenterMonitorMessage',
            meta: {
              title: '监控消息',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //预警消息
              {
                path: 'warningMessage',
                component: 'outbound/monitorCenter/monitorMessage/warningMessage/index',
                name: 'OutboundMonitorCenterMonitorMessageWarningMessage',
                meta: {
                  title: '预警消息',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //出入围栏
              {
                path: 'fenceMessage',
                component: 'outbound/monitorCenter/monitorMessage/fenceMessage/index',
                name: 'OutboundMonitorCenterMonitorMessageFenceMessage',
                meta: {
                  title: '出入围栏',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //车辆停留
              {
                path: 'vehicleStopMessage',
                component: 'outbound/monitorCenter/monitorMessage/vehicleStopMessage/index',
                name: 'OutboundMonitorCenterMonitorMessageVehicleStopMessage',
                meta: {
                  title: '车辆停留',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //提醒消息
              {
                path: 'remindMessage',
                component: 'outbound/monitorCenter/monitorMessage/remindMessage/index',
                name: 'OutboundMonitorCenterMonitorMessageRemindMessage',
                meta: {
                  title: '提醒消息',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
        ],
      },
      //车队管理
      {
        path: '/fleetManagement',
        component: 'Layout',
        redirect: '/fleetManagement/oilFeeManagement',
        name: 'OutboundFleetManagement',
        meta: {
          title: '车队管理',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //里程管理
          {
            path: 'mileageManagement',
            component: 'outbound/fleetManagement/mileageManagement/index',
            name: 'OutboundFleetManagementMileageManagement',
            meta: {
              title: '里程管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //交车费管理
          {
            path: 'deliveryFeeManagement',
            component: 'outbound/fleetManagement/deliveryFeeManagement/index',
            name: 'OutboundFleetManagementDeliveryFeeManagement',
            meta: {
              title: '交车费管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //油费管理
          {
            path: 'oilFeeManagement',
            component: 'outbound/fleetManagement/oilFeeManagement/index',
            name: 'OutboundFleetManagementOilFeeManagement',
            meta: {
              title: '油费管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //高速费管理
          {
            path: 'highwayFeeManagement',
            component: 'outbound/fleetManagement/highwayFeeManagement/index',
            name: 'OutboundFleetManagementHighwayFeeManagement',
            meta: {
              title: '高速费管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //维修管理
          {
            path: 'repairManagement',
            component: 'outbound/fleetManagement/repairManagement/index',
            name: 'OutboundFleetManagementRepairManagement',
            meta: {
              title: '维修管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //供应商维修
          {
            path: 'supplierRepair',
            component: 'outbound/fleetManagement/supplierRepair/index',
            name: 'OutboundFleetManagementSupplierRepair',
            meta: {
              title: '供应商维修',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //车队提报
              {
                path: 'fleetReport',
                component: 'outbound/fleetManagement/supplierRepair/fleetReport/index',
                name: 'OutboundFleetManagementSupplierRepairFleetReport',
                meta: {
                  title: '车队提报',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //维修明细
              {
                path: 'repairDetail',
                component: 'outbound/fleetManagement/supplierRepair/repairDetail/index',
                name: 'OutboundFleetManagementSupplierRepairRepairDetail',
                meta: {
                  title: '维修明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //供应商维修
              {
                path: 'supplierRepair',
                component: 'outbound/fleetManagement/supplierRepair/supplierRepair/index',
                name: 'OutboundFleetManagementSupplierRepairSupplierRepair',
                meta: {
                  title: '供应商维修',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          }, //供应商维修
          {
            path: 'supplierRepair',
            component: 'outbound/fleetManagement/supplierRepair/index',
            name: 'OutboundFleetManagementSupplierRepair',
            meta: {
              title: '供应商维修',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //车队提报
              {
                path: 'fleetReport',
                component: 'outbound/fleetManagement/supplierRepair/fleetReport/index',
                name: 'OutboundFleetManagementSupplierRepairFleetReport',
                meta: {
                  title: '车队提报',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //维修明细
              {
                path: 'repairDetail',
                component: 'outbound/fleetManagement/supplierRepair/repairDetail/index',
                name: 'OutboundFleetManagementSupplierRepairRepairDetail',
                meta: {
                  title: '维修明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //供应商维修
              {
                path: 'supplierRepair',
                component: 'outbound/fleetManagement/supplierRepair/supplierRepair/index',
                name: 'OutboundFleetManagementSupplierRepairSupplierRepair',
                meta: {
                  title: '供应商维修',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          // //尿素管理
          // {
          //   path: 'urineManagement',
          //   component: 'outbound/fleetManagement/urineManagement/index',
          //   name: 'OutboundFleetManagementUrineManagement',
          //   meta: {
          //     title: '尿素管理',
          //     hidden: false,
          //     roles: ['ADMIN'],
          //   },
          // },
          // //轮胎管理
          // {
          //   path: 'tireManagement',
          //   component: 'outbound/fleetManagement/tireManagement/index',
          //   name: 'OutboundFleetManagementTireManagement',
          //   meta: {
          //     title: '轮胎管理',
          //     hidden: false,
          //     roles: ['ADMIN'],
          //   },
          // },
          //事故管理
          {
            path: 'accidentManagement',
            component: 'outbound/fleetManagement/accidentManagement/index',
            name: 'OutboundFleetManagementAccidentManagement',
            meta: {
              title: '事故管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          // //报险管理
          // {
          //   path: 'insuranceManagement',
          //   component: 'outbound/fleetManagement/insuranceManagement/index',
          //   name: 'OutboundFleetManagementInsuranceManagement',
          //   meta: {
          //     title: '报险管理',
          //     hidden: false,
          //     roles: ['ADMIN'],
          //   },
          // },
          //维保管理
          {
            path: 'maintenanceManagement',
            component: 'outbound/fleetManagement/maintenanceManagement/index',
            name: 'OutboundFleetManagementMaintenanceManagement',
            meta: {
              title: '维保管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //违章罚款
          {
            path: 'penaltyManagement',
            component: 'outbound/fleetManagement/penaltyManagement/index',
            name: 'OutboundFleetManagementPenaltyManagement',
            meta: {
              title: '违章罚款',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //其他费用
          {
            path: 'otherFeeManagement',
            component: 'outbound/fleetManagement/otherFeeManagement/index',
            name: 'OutboundFleetManagementOtherFeeManagement',
            meta: {
              title: '其他费用',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
        ],
      },
      //统计中心
      {
        path: '/statisticsCenter',
        component: 'Layout',
        redirect: '/statisticsCenter/headquartersStatistics',
        name: 'OutboundStatisticsCenter',
        meta: {
          title: '统计中心',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //总部及分公司统计
          {
            path: 'headquartersStatistics',
            component: 'outbound/statisticsCenter/headquartersStatistics/index',
            name: 'OutboundStatisticsCenterHeadquartersStatistics',
            meta: {
              title: '总部及分公司统计',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //员工绩效报表
              {
                path: 'staffPerformanceReport',
                component: 'outbound/statisticsCenter/headquartersStatistics/staffPerformanceReport/index',
                name: 'OutboundStatisticsCenterHeadquartersStatisticsStaffPerformanceReport',
                meta: {
                  title: '员工绩效报表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //司机绩效报表
              {
                path: 'driverPerformanceReport',
                component: 'outbound/statisticsCenter/headquartersStatistics/driverPerformanceReport/index',
                name: 'OutboundStatisticsCenterHeadquartersStatisticsDriverPerformanceReport',
                meta: {
                  title: '司机绩效报表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //司机绩效明细
              {
                path: 'driverPerformanceDetail',
                component: 'outbound/statisticsCenter/headquartersStatistics/driverPerformanceDetail/index',
                name: 'OutboundStatisticsCenterHeadquartersStatisticsDriverPerformanceDetail',
                meta: {
                  title: '司机绩效明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //车辆营运报表
              {
                path: 'vehicleOperatingReport',
                component: 'outbound/statisticsCenter/headquartersStatistics/vehicleOperatingReport/index',
                name: 'OutboundStatisticsCenterHeadquartersStatisticsVehicleOperatingReport',
                meta: {
                  title: '车辆营运报表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //车辆营运明细
              {
                path: 'vehicleOperatingDetail',
                component: 'outbound/statisticsCenter/headquartersStatistics/vehicleOperatingDetail/index',
                name: 'OutboundStatisticsCenterHeadquartersStatisticsVehicleOperatingDetail',
                meta: {
                  title: '车辆营运明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //业务执行报表
              {
                path: 'businessExecutionReport',
                component: 'outbound/statisticsCenter/headquartersStatistics/businessExecutionReport/index',
                name: 'OutboundStatisticsCenterHeadquartersStatisticsBusinessExecutionReport',
                meta: {
                  title: '业务执行报表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //客户统计
          {
            path: 'customerStatistics',
            component: 'outbound/statisticsCenter/customerStatistics/index',
            name: 'OutboundStatisticsCenterCustomerStatistics',
            meta: {
              title: '客户统计',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //收支统计
              {
                path: 'revenueStatistics',
                component: 'outbound/statisticsCenter/customerStatistics/revenueStatistics/index',
                name: 'OutboundStatisticsCenterCustomerStatisticsRevenueStatistics',
                meta: {
                  title: '收支统计',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //统计明细
              {
                path: 'statisticsDetail',
                component: 'outbound/statisticsCenter/customerStatistics/statisticsDetail/index',
                name: 'OutboundStatisticsCenterCustomerStatisticsStatisticsDetail',
                meta: {
                  title: '统计明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //承运商统计
          {
            path: 'carrierStatistics',
            component: 'outbound/statisticsCenter/carrierStatistics/index',
            name: 'OutboundStatisticsCenterCarrierStatistics',
            meta: {
              title: '承运商统计',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //承运商营运报表
              {
                path: 'carrierOperatingReport',
                component: 'outbound/statisticsCenter/carrierStatistics/carrierOperatingReport/index',
                name: 'OutboundStatisticsCenterCarrierStatisticsCarrierOperatingReport',
                meta: {
                  title: '承运商营运报表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //统计明细
              {
                path: 'statisticsDetail',
                component: 'outbound/statisticsCenter/carrierStatistics/statisticsDetail/index',
                name: 'OutboundStatisticsCenterCarrierStatisticsStatisticsDetail',
                meta: {
                  title: '统计明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //发运线路统计
          {
            path: 'deliveryLineStatistics',
            component: 'outbound/statisticsCenter/deliveryLineStatistics/index',
            name: 'OutboundStatisticsCenterDeliveryLineStatistics',
            meta: {
              title: '发运线路统计',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //线路统计主表
              {
                path: 'lineStatisticsMainTable',
                component: 'outbound/statisticsCenter/deliveryLineStatistics/lineStatisticsMainTable/index',
                name: 'OutboundStatisticsCenterDeliveryLineStatisticsLineStatisticsMainTable',
                meta: {
                  title: '线路统计主表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //线路统计明细
              {
                path: 'lineStatisticsDetail',
                component: 'outbound/statisticsCenter/deliveryLineStatistics/lineStatisticsDetail/index',
                name: 'OutboundStatisticsCenterDeliveryLineStatisticsLineStatisticsDetail',
                meta: {
                  title: '线路统计明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //异常统计
          {
            path: 'abnormalStatistics',
            component: 'outbound/statisticsCenter/abnormalStatistics/index',
            name: 'OutboundStatisticsCenterAbnormalStatistics',
            meta: {
              title: '异常统计',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //统计主表
              {
                path: 'statisticsMainTable',
                component: 'outbound/statisticsCenter/abnormalStatistics/statisticsMainTable/index',
                name: 'OutboundStatisticsCenterAbnormalStatisticsStatisticsMainTable',
                meta: {
                  title: '统计主表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //统计明细
              {
                path: 'statisticsDetail',
                component: 'outbound/statisticsCenter/abnormalStatistics/statisticsDetail/index',
                name: 'OutboundStatisticsCenterAbnormalStatisticsStatisticsDetail',
                meta: {
                  title: '统计明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //运营管理看板
          {
            path: 'operationManagementDashboard',
            component: 'outbound/statisticsCenter/operationManagementDashboard/index',
            name: 'OutboundStatisticsCenterOperationManagementDashboard',
            meta: {
              title: '运营管理看板',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //分子公司纬度
              {
                path: 'subCompanyLevel',
                component: 'outbound/statisticsCenter/operationManagementDashboard/subCompanyLevel/index',
                name: 'OutboundStatisticsCenterOperationManagementDashboardSubCompanyLevel',
                meta: {
                  title: '分子公司纬度',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //客户纬度
              {
                path: 'customerLevel',
                component: 'outbound/statisticsCenter/operationManagementDashboard/customerLevel/index',
                name: 'OutboundStatisticsCenterOperationManagementDashboardCustomerLevel',
                meta: {
                  title: '客户纬度',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //自有车队纬度
              {
                path: 'ownFleetLevel',
                component: 'outbound/statisticsCenter/operationManagementDashboard/ownFleetLevel/index',
                name: 'OutboundStatisticsCenterOperationManagementDashboardOwnFleetLevel',
                meta: {
                  title: '自有车队纬度',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //承运商纬度
              {
                path: 'carrierLevel',
                component: 'outbound/statisticsCenter/operationManagementDashboard/carrierLevel/index',
                name: 'OutboundStatisticsCenterOperationManagementDashboardCarrierLevel',
                meta: {
                  title: '承运商纬度',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
        ],
      },
      // {
      //   path: '/reportStatistics',
      //   component: 'outbound/fleetManagement/reportStatistics/index',
      //   name: 'OutboundFleetManagementReportStatistics',
      //   meta: {
      //     title: '报表统计',
      //     hidden: false,
      //     roles: ['ADMIN'],
      //   },
      //   children: [
      //     //人员统计
      //     {
      //       path: 'personnelStatistics',
      //       component: 'outbound/fleetManagement/reportStatistics/personnelStatistics/index',
      //       name: 'OutboundFleetManagementReportStatisticsPersonnelStatistics',
      //       meta: {
      //         title: '人员统计',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //车队统计
      //     {
      //       path: 'fleetStatistics',
      //       component: 'outbound/fleetManagement/reportStatistics/fleetStatistics/index',
      //       name: 'OutboundFleetManagementReportStatisticsFleetStatistics',
      //       meta: {
      //         title: '车队统计',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //业务统计
      //     {
      //       path: 'businessStatistics',
      //       component: 'outbound/fleetManagement/reportStatistics/businessStatistics/index',
      //       name: 'OutboundFleetManagementReportStatisticsBusinessStatistics',
      //       meta: {
      //         title: '业务统计',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //车辆统计
      //     {
      //       path: 'vehicleStatistics',
      //       component: 'outbound/fleetManagement/reportStatistics/vehicleStatistics/index',
      //       name: 'OutboundFleetManagementReportStatisticsVehicleStatistics',
      //       meta: {
      //         title: '车辆统计',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //运营统计
      //     {
      //       path: 'operationStatistics',
      //       component: 'outbound/fleetManagement/reportStatistics/operationStatistics/index',
      //       name: 'OutboundFleetManagementReportStatisticsOperationStatistics',
      //       meta: {
      //         title: '运营统计',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //异常统计
      //     {
      //       path: 'abnormalStatistics',
      //       component: 'outbound/fleetManagement/reportStatistics/abnormalStatistics/index',
      //       name: 'OutboundFleetManagementReportStatisticsAbnormalStatistics',
      //       meta: {
      //         title: '异常统计',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //运单成本利润
      //     {
      //       path: 'orderCostProfit',
      //       component: 'outbound/fleetManagement/reportStatistics/orderCostProfit/index',
      //       name: 'OutboundFleetManagementReportStatisticsOrderCostProfit',
      //       meta: {
      //         title: '运单成本利润',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //客户收入统计
      //     {
      //       path: 'customerIncomeStatistics',
      //       component: 'outbound/fleetManagement/reportStatistics/customerIncomeStatistics/index',
      //       name: 'OutboundFleetManagementReportStatisticsCustomerIncomeStatistics',
      //       meta: {
      //         title: '客户收入统计',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //外协成本统计
      //     {
      //       path: 'outsourcingCostStatistics',
      //       component: 'outbound/fleetManagement/reportStatistics/outsourcingCostStatistics/index',
      //       name: 'OutboundFleetManagementReportStatisticsOutsourcingCostStatistics',
      //       meta: {
      //         title: '外协成本统计',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //自有车经营成本统计
      //     {
      //       path: 'ownBusinessCostStatistics',
      //       component: 'outbound/fleetManagement/reportStatistics/ownBusinessCostStatistics/index',
      //       name: 'OutboundFleetManagementReportStatisticsOwnBusinessCostStatistics',
      //       meta: {
      //         title: '自有车经营成本统计',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //       children: [
      //         //经营明细
      //         {
      //           path: 'businessDetails',
      //           component: 'outbound/fleetManagement/reportStatistics/ownBusinessCostStatistics/businessDetails/index',
      //           name: 'OutboundFleetManagementReportStatisticsOwnBusinessCostStatisticsBusinessDetails',
      //           meta: {
      //             title: '经营明细',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //线路成本统计（累计）
      //         {
      //           path: 'lineCostStatistics',
      //           component: 'outbound/fleetManagement/reportStatistics/ownBusinessCostStatistics/lineCostStatistics/index',
      //           name: 'OutboundFleetManagementReportStatisticsOwnBusinessCostStatisticsLineCostStatistics',
      //           meta: {
      //             title: '线路成本统计（累计）',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //线路成本统计（平均）
      //         {
      //           path: 'lineCostStatisticsAverage',
      //           component: 'outbound/fleetManagement/reportStatistics/ownBusinessCostStatistics/lineCostStatisticsAverage/index',
      //           name: 'OutboundFleetManagementReportStatisticsOwnBusinessCostStatisticsLineCostStatisticsAverage',
      //           meta: {
      //             title: '线路成本统计（平均）',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //线路成本统计（单趟）
      //         {
      //           path: 'lineCostStatisticsSingle',
      //           component: 'outbound/fleetManagement/reportStatistics/ownBusinessCostStatistics/lineCostStatisticsSingle/index',
      //           name: 'OutboundFleetManagementReportStatisticsOwnBusinessCostStatisticsLineCostStatisticsSingle',
      //           meta: {
      //             title: '线路成本统计（单趟）',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //       ],
      //     },
      //     //驾驶员行为及业务统计
      //     {
      //       path: 'driverBehaviorStatistics',
      //       component: 'outbound/fleetManagement/reportStatistics/driverBehaviorStatistics/index',
      //       name: 'OutboundFleetManagementReportStatisticsDriverBehaviorStatistics',
      //       meta: {
      //         title: '驾驶员行为及业务统计',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //办事处收入成本统计
      //     {
      //       path: 'officeIncomeStatistics',
      //       component: 'outbound/fleetManagement/reportStatistics/officeIncomeStatistics/index',
      //       name: 'OutboundFleetManagementReportStatisticsOfficeIncomeStatistics',
      //       meta: {
      //         title: '办事处收入成本统计',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //自有车里程统计
      //     {
      //       path: 'ownCarMileageStatistics',
      //       component: 'outbound/fleetManagement/reportStatistics/ownCarMileageStatistics/index',
      //       name: 'OutboundFleetManagementReportStatisticsOwnCarMileageStatistics',
      //       meta: {
      //         title: '自有车里程统计',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //       children: [
      //         //车牌号别
      //         {
      //           path: 'plateNumber',
      //           component: 'outbound/fleetManagement/reportStatistics/ownCarMileageStatistics/plateNumber/index',
      //           name: 'OutboundFleetManagementReportStatisticsOwnCarMileageStatisticsPlateNumber',
      //           meta: {
      //             title: '车牌号别',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //年度/月份
      //         {
      //           path: 'yearMonth',
      //           component: 'outbound/fleetManagement/reportStatistics/ownCarMileageStatistics/yearMonth/index',
      //           name: 'OutboundFleetManagementReportStatisticsOwnCarMileageStatisticsYearMonth',
      //           meta: {
      //             title: '年度/月份',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //       ],
      //     },
      //     //运量统计
      //     {
      //       path: 'volumeStatistics',
      //       component: 'outbound/fleetManagement/reportStatistics/volumeStatistics/index',
      //       name: 'OutboundFleetManagementReportStatisticsVolumeStatistics',
      //       meta: {
      //         title: '运量统计',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //       children: [
      //         //客户运量（区域）
      //         {
      //           path: 'customerVolumeArea',
      //           component: 'outbound/fleetManagement/reportStatistics/volumeStatistics/customerVolumeArea/index',
      //           name: 'OutboundFleetManagementReportStatisticsVolumeStatisticsCustomerVolumeArea',
      //           meta: {
      //             title: '客户运量（区域）',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //客户运量（省份）
      //         {
      //           path: 'customerVolumeProvince',
      //           component: 'outbound/fleetManagement/reportStatistics/volumeStatistics/customerVolumeProvince/index',
      //           name: 'OutboundFleetManagementReportStatisticsVolumeStatisticsCustomerVolumeProvince',
      //           meta: {
      //             title: '客户运量（省份）',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //客户运量（地点）
      //         {
      //           path: 'customerVolumePlace',
      //           component: 'outbound/fleetManagement/reportStatistics/volumeStatistics/customerVolumePlace/index',
      //           name: 'OutboundFleetManagementReportStatisticsVolumeStatisticsCustomerVolumePlace',
      //           meta: {
      //             title: '客户运量（地点）',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //       ],
      //     },
      //   ],
      // },
      //审批中心
      {
        path: '/approvalCenter',
        component: 'Layout',
        redirect: '/approvalCenter/loanManagement',
        name: 'ApprovalCenter',
        meta: {
          title: '审批中心',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //事务审批
          {
            path: 'loanManagement',
            component: 'outbound/approvalCenter/loanManagement/index',
            name: 'OutboundApprovalCenterLoanManagement',
            meta: {
              title: '事务审批',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //经费审批
          {
            path: 'expenseManagement',
            component: 'outbound/approvalCenter/expenseManagement/index',
            name: 'OutboundApprovalCenterExpenseManagement',
            meta: {
              title: '经费审批',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
        ],
      },
      //行政管理
      {
        path: '/administrativeManagement',
        component: 'Layout',
        redirect: '/administrativeManagement/qualityLossManagement',
        name: 'AdministrativeManagement',
        meta: {
          title: '行政管理',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          //工资管理
          {
            path: 'salaryManagement',
            component: 'outbound/administrativeManagement/salaryManagement/index',
            name: 'OutboundAdministrativeManagementSalaryManagement',
            meta: {
              title: '工资管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //员工管理
          {
            path: 'employeeManagement',
            component: 'outbound/administrativeManagement/employeeManagement/index',
            name: 'OutboundAdministrativeManagementEmployeeManagement',
            meta: {
              title: '员工管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //离职管理
          {
            path: 'resignationManagement',
            component: 'outbound/administrativeManagement/resignationManagement/index',
            name: 'OutboundAdministrativeManagementResignationManagement',
            meta: {
              title: '离职管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //请假管理
          {
            path: 'leaveManagement',
            component: 'outbound/administrativeManagement/leaveManagement/index',
            name: 'OutboundAdministrativeManagementLeaveManagement',
            meta: {
              title: '请假管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //领用管理
          {
            path: 'useManagement',
            component: 'outbound/administrativeManagement/useManagement/index',
            name: 'OutboundAdministrativeManagementUseManagement',
            meta: {
              title: '领用管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
        ],
      },
      //财务管理
      {
        path: '/financialManagement',
        component: 'Layout',
        redirect: '/financialManagement/customerSettlementManagement/freightCalculation',
        name: 'FinancialManagement',
        meta: {
          title: '财务管理',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          // 零散订单
          {
            path: 'scatteredOrderManagement',
            component: 'outbound/financialManagement/scatteredOrderManagement/index',
            name: 'OutboundFinancialManagementScatteredOrderManagement',
            meta: {
              title: '零散订单',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //收款
              {
                path: 'receiptManagement',
                component: 'outbound/financialManagement/scatteredOrderManagement/receiptManagement/index',
                name: 'OutboundFinancialManagementScatteredOrderManagementReceiptManagement',
                meta: {
                  title: '收款',
                },
                children: [
                  //订单列表
                  {
                    path: 'orderList',
                    component: 'outbound/financialManagement/scatteredOrderManagement/receiptManagement/orderList/index',
                    name: 'OutboundFinancialManagementScatteredOrderManagementReceiptManagementOrderList',
                    meta: {
                      title: '订单列表',
                    },
                  },
                  //对账收款
                  {
                    path: 'reconciliationReceipt',
                    component: 'outbound/financialManagement/scatteredOrderManagement/receiptManagement/reconciliationReceipt/index',
                    name: 'OutboundFinancialManagementScatteredOrderManagementReceiptManagementReconciliationReceipt',
                    meta: {
                      title: '对账收款',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                ],
              },
              //付款
              {
                path: 'paymentManagement',
                component: 'outbound/financialManagement/scatteredOrderManagement/paymentManagement/index',
                name: 'OutboundFinancialManagementScatteredOrderManagementPaymentManagement',
                meta: {
                  title: '付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
                children: [
                  //订单列表
                  {
                    path: 'orderList',
                    component: 'outbound/financialManagement/scatteredOrderManagement/paymentManagement/orderList/index',
                    name: 'OutboundFinancialManagementScatteredOrderManagementPaymentManagementOrderList',
                    meta: {
                      title: '订单列表',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                  //对账付款
                  {
                    path: 'reconciliationPayment',
                    component: 'outbound/financialManagement/scatteredOrderManagement/paymentManagement/reconciliationPayment/index',
                    name: 'OutboundFinancialManagementScatteredOrderManagementPaymentManagementReconciliationPayment',
                    meta: {
                      title: '对账付款',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                ],
              },
            ],
          },
          //维修结算
          {
            path: 'supplierRepair',
            component: 'outbound/financialManagement/supplierRepair/index',
            name: 'OutboundFinancialManagementSupplierRepair',
            meta: {
              title: '维修结算',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //结算对账
              {
                path: 'settlementReconciliation',
                component: 'outbound/financialManagement/supplierRepair/settlementReconciliation/index',
                name: 'OutboundFinancialManagementSupplierRepairSettlementReconciliation',
                meta: {
                  title: '结算对账',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //开票管理
              {
                path: 'invoiceManagement',
                component: 'outbound/financialManagement/supplierRepair/invoiceManagement/index',
                name: 'OutboundFinancialManagementSupplierRepairInvoiceManagement',
                meta: {
                  title: '开票管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //发票管理
              {
                path: 'overInvoiceManagement',
                component: 'outbound/financialManagement/supplierRepair/overInvoiceManagement/index',
                name: 'OutboundFinancialManagementSupplierRepairOverInvoiceManagement',
                meta: {
                  title: '发票管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //结算汇总
              {
                path: 'settlementSummary',
                component: 'outbound/financialManagement/supplierRepair/settlementSummary/index',
                name: 'OutboundFinancialManagementSupplierRepairSettlementSummary',
                meta: {
                  title: '结算汇总',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //客户结算管理
          {
            path: 'customerSettlementManagement',
            component: 'outbound/financialManagement/customerSettlementManagement/index',
            name: 'OutboundFinancialManagementCustomerSettlementManagement',
            meta: {
              title: '客户结算管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //客户扣款
              {
                path: 'customerDeduction',
                component: 'outbound/financialManagement/customerSettlementManagement/customerDeduction/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementCustomerDeduction',
                meta: {
                  title: '客户扣款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //运费计算
              {
                path: 'freightCalculation',
                component: 'outbound/financialManagement/customerSettlementManagement/freightCalculation/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementFreightCalculation',
                meta: {
                  title: '运费计算',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //对账管理
              {
                path: 'reconciliationManagement',
                component: 'outbound/financialManagement/customerSettlementManagement/reconciliationManagement/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementReconciliationManagement',
                meta: {
                  title: '对账管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //对账审批
              {
                path: 'reconciliationApproval',
                component: 'outbound/financialManagement/customerSettlementManagement/reconciliationApproval/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementReconciliationApproval',
                meta: {
                  title: '对账审批',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //开票管理
              {
                path: 'invoiceManagement',
                component: 'outbound/financialManagement/customerSettlementManagement/invoiceManagement/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementInvoiceManagement',
                meta: {
                  title: '开票管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //发票管理
              {
                path: 'overInvoiceManagement',
                component: 'outbound/financialManagement/customerSettlementManagement/overInvoiceManagement/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementOverInvoiceManagement',
                meta: {
                  title: '发票管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //收款管理
              {
                path: 'receiptManagement',
                component: 'outbound/financialManagement/customerSettlementManagement/receiptManagement/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementReceiptManagement',
                meta: {
                  title: '收款管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //应收账汇总
              {
                path: 'receivableAccountSummary',
                component: 'outbound/financialManagement/customerSettlementManagement/receivableAccountSummary/index',
                name: 'OutboundFinancialManagementCustomerSettlementManagementReceivableAccountSummary',
                meta: {
                  title: '应收账汇总',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //回单管理
          {
            path: 'refundManagement',
            component: 'outbound/financialManagement/refundManagement/index',
            name: 'OutboundFinancialManagementRefundManagement',
            meta: {
              title: '回单管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //下游结算管理
          {
            path: 'downstreamSettlementManagement',
            component: 'outbound/financialManagement/downstreamSettlementManagement/index',
            name: 'OutboundFinancialManagementDownstreamSettlementManagement',
            meta: {
              title: '下游结算管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //运费计算
              {
                path: 'freightCalculation',
                component: 'outbound/financialManagement/downstreamSettlementManagement/freightCalculation/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementFreightCalculation',
                meta: {
                  title: '运费计算',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //对账管理
              {
                path: 'reconciliationManagement',
                component: 'outbound/financialManagement/downstreamSettlementManagement/reconciliationManagement/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementReconciliationManagement',
                meta: {
                  title: '对账管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //对账审批
              {
                path: 'reconciliationApproval',
                component: 'outbound/financialManagement/downstreamSettlementManagement/reconciliationApproval/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementReconciliationApproval',
                meta: {
                  title: '对账审批',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //开票管理
              {
                path: 'invoiceManagement',
                component: 'outbound/financialManagement/downstreamSettlementManagement/invoiceManagement/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementInvoiceManagement',
                meta: {
                  title: '开票管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //发票管理
              {
                path: 'overInvoiceManagement',
                component: 'outbound/financialManagement/downstreamSettlementManagement/overInvoiceManagement/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementOverInvoiceManagement',
                meta: {
                  title: '发票管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //付款管理
              {
                path: 'paymentManagement',
                component: 'outbound/financialManagement/downstreamSettlementManagement/paymentManagement/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementPaymentManagement',
                meta: {
                  title: '付款管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //应付账汇总
              {
                path: 'payableAccountSummary',
                component: 'outbound/financialManagement/downstreamSettlementManagement/payableAccountSummary/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementPayableAccountSummary',
                meta: {
                  title: '应付账汇总',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //外协奖惩
              {
                path: 'externalRewardPunishment',
                component: 'outbound/financialManagement/downstreamSettlementManagement/externalRewardPunishment/index',
                name: 'OutboundFinancialManagementDownstreamSettlementManagementExternalRewardPunishment',
                meta: {
                  title: '外协奖惩',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //司机补贴结算
          {
            path: 'driverSubsidySettlement',
            component: 'outbound/financialManagement/driverSubsidySettlement/index',
            name: 'OutboundFinancialManagementDriverSubsidySettlement',
            meta: {
              title: '司机补贴结算',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //补贴计算
              {
                path: 'subsidyCalculation',
                component: 'outbound/financialManagement/driverSubsidySettlement/subsidyCalculation/index',
                name: 'OutboundFinancialManagementDriverSubsidySettlementSubsidyCalculation',
                meta: {
                  title: '补贴计算',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //补贴对账
              {
                path: 'subsidyReconciliation',
                component: 'outbound/financialManagement/driverSubsidySettlement/subsidyReconciliation/index',
                name: 'OutboundFinancialManagementDriverSubsidySettlementSubsidyReconciliation',
                meta: {
                  title: '补贴对账',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //补贴审批
              {
                path: 'subsidyApproval',
                component: 'outbound/financialManagement/driverSubsidySettlement/subsidyApproval/index',
                name: 'OutboundFinancialManagementDriverSubsidySettlementSubsidyApproval',
                meta: {
                  title: '补贴审批',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //补贴结算汇总
              {
                path: 'subsidySettlementSummary',
                component: 'outbound/financialManagement/driverSubsidySettlement/subsidySettlementSummary/index',
                name: 'OutboundFinancialManagementDriverSubsidySettlementSubsidySettlementSummary',
                meta: {
                  title: '补贴结算汇总',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //借支管理
          {
            path: 'loanManagement',
            component: 'outbound/financialManagement/loanManagement/index',
            name: 'OutboundFinancialManagementLoanManagement',
            meta: {
              title: '生产借支管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //现金借支管理
              {
                path: 'cashLoanManagement',
                component: 'outbound/financialManagement/loanManagement/cashLoanManagement/index',
                name: 'OutboundFinancialManagementLoanManagementCashLoanManagement',
                meta: {
                  title: '现金借支管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
                // children: [
                //   //现金借支申请
                //   {
                //     path: 'cashLoanApplication',
                //     component: 'outbound/financialManagement/loanManagement/cashLoanManagement/cashLoanApplication/index',
                //     name: 'OutboundFinancialManagementLoanManagementCashLoanManagementCashLoanApplication',
                //     meta: {
                //       title: '现金借支申请',
                //       hidden: false,
                //       roles: ['ADMIN'],
                //     },
                //   },
                //   //现金借支审批
                //   {
                //     path: 'cashLoanApproval',
                //     component: 'outbound/financialManagement/loanManagement/cashLoanManagement/cashLoanApproval/index',
                //     name: 'OutboundFinancialManagementLoanManagementCashLoanManagementCashLoanApproval',
                //     meta: {
                //       title: '现金借支审批',
                //       hidden: false,
                //       roles: ['ADMIN'],
                //     },
                //   },
                //   //现金借支明细
                //   {
                //     path: 'cashLoanDetail',
                //     component: 'outbound/financialManagement/loanManagement/cashLoanManagement/cashLoanDetail/index',
                //     name: 'OutboundFinancialManagementLoanManagementCashLoanManagementCashLoanDetail',
                //     meta: {
                //       title: '现金借支明细',
                //       hidden: false,
                //       roles: ['ADMIN'],
                //     },
                //   },
                // ],
              },
              //油费管理
              {
                path: 'oilLoanManagement',
                component: 'outbound/financialManagement/loanManagement/oilLoanManagement/index',
                name: 'OutboundFinancialManagementLoanManagementOilLoanManagement',
                meta: {
                  title: '油费管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
                children: [
                  // //油费借支申请
                  // {
                  //   path: 'oilLoanApplication',
                  //   component: 'outbound/financialManagement/loanManagement/oilLoanManagement/oilLoanApplication/index',
                  //   name: 'OutboundFinancialManagementLoanManagementOilLoanManagementOilLoanApplication',
                  //   meta: {
                  //     title: '油费借支申请',
                  //     hidden: false,
                  //     roles: ['ADMIN'],
                  //   },
                  // },
                  // //油费借支审批
                  // {
                  //   path: 'oilLoanApproval',
                  //   component: 'outbound/financialManagement/loanManagement/oilLoanManagement/oilLoanApproval/index',
                  //   name: 'OutboundFinancialManagementLoanManagementOilLoanManagementOilLoanApproval',
                  //   meta: {
                  //     title: '油费借支审批',
                  //     hidden: false,
                  //     roles: ['ADMIN'],
                  //   },
                  // },
                  // //油费借支明细
                  // {
                  //   path: 'oilLoanDetail',
                  //   component: 'outbound/financialManagement/loanManagement/oilLoanManagement/oilLoanDetail/index',
                  //   name: 'OutboundFinancialManagementLoanManagementOilLoanManagementOilLoanDetail',
                  //   meta: {
                  //     title: '油费借支明细',
                  //     hidden: false,
                  //     roles: ['ADMIN'],
                  //   },
                  // },
                  // //油费借支配置
                  // {
                  //   path: 'oilLoanConfig',
                  //   component: 'outbound/financialManagement/loanManagement/oilLoanManagement/oilLoanConfig/index',
                  //   name: 'OutboundFinancialManagementLoanManagementOilLoanManagementOilLoanConfig',
                  //   meta: {
                  //     title: '油费借支配置',
                  //     hidden: false,
                  //     roles: ['ADMIN'],
                  //   },
                  // },
                  // 油费列表
                  {
                    path: 'oilList',
                    component: 'outbound/financialManagement/loanManagement/oilLoanManagement/oilList/index',
                    name: 'OutboundFinancialManagementLoanManagementOilLoanManagementOilList',
                    meta: {
                      title: '油费列表',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                  //油卡管理
                  {
                    path: 'oilCardManagement',
                    component: 'outbound/financialManagement/loanManagement/oilLoanManagement/oilCardManagement/index',
                    name: 'OutboundFinancialManagementLoanManagementOilLoanManagementOilCardManagement',
                    meta: {
                      title: '油卡管理',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                  //油费设置
                  {
                    path: 'oilConfig',
                    component: 'outbound/financialManagement/loanManagement/oilLoanManagement/oilConfig/index',
                    name: 'OutboundFinancialManagementLoanManagementOilLoanManagementOilConfig',
                    meta: {
                      title: '油费设置',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                ],
              },
              //ETC管理
              {
                path: 'etcManagement',
                component: 'outbound/financialManagement/loanManagement/etcManagement/index',
                name: 'OutboundFinancialManagementLoanManagementEtcManagement',
                meta: {
                  title: 'ETC管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
                children: [
                  //ETC费用列表
                  {
                    path: 'etcList',
                    component: 'outbound/financialManagement/loanManagement/etcManagement/etcList/index',
                    name: 'OutboundFinancialManagementLoanManagementEtcManagementEtcList',
                    meta: {
                      title: 'ETC费用列表',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                  //ETC卡管理
                  {
                    path: 'etcCardManagement',
                    component: 'outbound/financialManagement/loanManagement/etcManagement/etcCardManagement/index',
                    name: 'OutboundFinancialManagementLoanManagementEtcManagementEtcCardManagement',
                    meta: {
                      title: 'ETC卡管理',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                  //ETC票管理
                  {
                    path: 'etcTicketManagement',
                    component: 'outbound/financialManagement/loanManagement/etcManagement/etcTicketManagement/index',
                    name: 'OutboundFinancialManagementLoanManagementEtcManagementEtcTicketManagement',
                    meta: {
                      title: 'ETC票管理',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                ],
              },
            ],
          },
          //采买管理
          {
            path: 'purchaseManagement',
            component: 'outbound/financialManagement/purchaseManagement/index',
            name: 'OutboundFinancialManagementPurchaseManagement',
            meta: {
              title: '采买管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //采买列表
              {
                path: 'purchaseList',
                component: 'outbound/financialManagement/purchaseManagement/purchaseList/index',
                name: 'OutboundFinancialManagementPurchaseManagementPurchaseList',
                meta: {
                  title: '采买列表',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //采买配置
              {
                path: 'purchaseConfig',
                component: 'outbound/financialManagement/purchaseManagement/purchaseConfig/index',
                name: 'OutboundFinancialManagementPurchaseManagementPurchaseConfig',
                meta: {
                  title: '采买配置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //付款管理
          {
            path: 'paymentManagement',
            component: 'outbound/financialManagement/paymentManagement/index',
            name: 'OutboundFinancialManagementPaymentManagement',
            meta: {
              title: '付款管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //借支付款
              {
                path: 'loanPayment',
                component: 'outbound/financialManagement/paymentManagement/loanPayment/index',
                name: 'OutboundFinancialManagementPaymentManagementLoanPayment',
                meta: {
                  title: '借支付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //采买付款
              {
                path: 'purchasePayment',
                component: 'outbound/financialManagement/paymentManagement/purchasePayment/index',
                name: 'OutboundFinancialManagementPaymentManagementPurchasePayment',
                meta: {
                  title: '采买付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //工资结算
              {
                path: 'salarySettlement',
                component: 'outbound/financialManagement/paymentManagement/salarySettlement/index',
                name: 'OutboundFinancialManagementPaymentManagementSalarySettlement',
                meta: {
                  title: '工资结算',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //油费付款
              {
                path: 'oilPayment',
                component: 'outbound/financialManagement/paymentManagement/oilPayment/index',
                name: 'OutboundFinancialManagementPaymentManagementOilPayment',
                meta: {
                  title: '油费付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //维修付款
              {
                path: 'supplierRepairPayment',
                component: 'outbound/financialManagement/paymentManagement/supplierRepairPayment/index',
                name: 'OutboundFinancialManagementPaymentManagementSupplierRepairPayment',
                meta: {
                  title: '维修付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //ETC付款
              {
                path: 'etcPayment',
                component: 'outbound/financialManagement/paymentManagement/etcPayment/index',
                name: 'OutboundFinancialManagementPaymentManagementEtcPayment',
                meta: {
                  title: 'ETC付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //质损付款
              {
                path: 'lossPayment',
                component: 'outbound/financialManagement/paymentManagement/lossPayment/index',
                name: 'OutboundFinancialManagementPaymentManagementLossPayment',
                meta: {
                  title: '质损付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //外协付款
              {
                path: 'outsourcingPayment',
                component: 'outbound/financialManagement/paymentManagement/outsourcingPayment/index',
                name: 'OutboundFinancialManagementPaymentManagementOutsourcingPayment',
                meta: {
                  title: '外协付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //维修付款
              {
                path: 'maintenancePayment',
                component: 'outbound/financialManagement/paymentManagement/maintenancePayment/index',
                name: 'OutboundFinancialManagementPaymentManagementMaintenancePayment',
                meta: {
                  title: '维修付款',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //司机账目管理
          {
            path: 'driverAccountManagement',
            component: 'outbound/financialManagement/driverAccountManagement/index',
            name: 'OutboundFinancialManagementDriverAccountManagement',
            meta: {
              title: '司机账目管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //驾驶员现金挂账
              {
                path: 'cashAccount',
                component: 'outbound/financialManagement/driverAccountManagement/cashAccount/index',
                name: 'OutboundFinancialManagementDriverAccountManagementCashAccount',
                meta: {
                  title: '驾驶员现金挂账',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //驾驶员油费挂账
              {
                path: 'oilAccount',
                component: 'outbound/financialManagement/driverAccountManagement/oilAccount/index',
                name: 'OutboundFinancialManagementDriverAccountManagementOilAccount',
                meta: {
                  title: '驾驶员油费挂账',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //驾驶员保障金管理
              {
                path: 'guaranteeManagement',
                component: 'outbound/financialManagement/driverAccountManagement/guaranteeManagement/index',
                name: 'OutboundFinancialManagementDriverAccountManagementGuaranteeManagement',
                meta: {
                  title: '驾驶员保障金管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
                children: [
                  //保障金设置
                  {
                    path: 'guaranteeConfig',
                    component: 'outbound/financialManagement/driverAccountManagement/guaranteeManagement/guaranteeConfig/index',
                    name: 'OutboundFinancialManagementDriverAccountManagementGuaranteeManagementGuaranteeConfig',
                    meta: {
                      title: '保障金设置',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                  //保障金明细
                  {
                    path: 'guaranteeDetail',
                    component: 'outbound/financialManagement/driverAccountManagement/guaranteeManagement/guaranteeDetail/index',
                    name: 'OutboundFinancialManagementDriverAccountManagementGuaranteeManagementGuaranteeDetail',
                    meta: {
                      title: '保障金明细',
                      hidden: false,
                      roles: ['ADMIN'],
                    },
                  },
                ],
              },
              //驾驶员备用金明细
              {
                path: 'reserveDetail',
                component: 'outbound/financialManagement/driverAccountManagement/reserveDetail/index',
                name: 'OutboundFinancialManagementDriverAccountManagementReserveDetail',
                meta: {
                  title: '驾驶员备用金明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //司机收款明细
              {
                path: 'driverCollectionDetail',
                component: 'outbound/financialManagement/driverAccountManagement/driverCollectionDetail/index',
                name: 'OutboundFinancialManagementDriverAccountManagementDriverCollectionDetail',
                meta: {
                  title: '司机收款明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //报销管理
          {
            path: 'reimbursementManagement',
            component: 'outbound/financialManagement/reimbursementManagement/index',
            name: 'OutboundFinancialManagementReimbursementManagement',
            meta: {
              title: '报销管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //报销申请
              {
                path: 'reimbursementApply',
                component: 'outbound/financialManagement/reimbursementManagement/reimbursementApply/index',
                name: 'OutboundFinancialManagementReimbursementManagementReimbursementApply',
                meta: {
                  title: '报销申请',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //报销审批
              {
                path: 'reimbursementApproval',
                component: 'outbound/financialManagement/reimbursementManagement/reimbursementApproval/index',
                name: 'OutboundFinancialManagementReimbursementManagementReimbursementApproval',
                meta: {
                  title: '报销审批',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //报销明细
              {
                path: 'reimbursementDetail',
                component: 'outbound/financialManagement/reimbursementManagement/reimbursementDetail/index',
                name: 'OutboundFinancialManagementReimbursementManagementReimbursementDetail',
                meta: {
                  title: '报销明细',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //报销配置
              {
                path: 'reimbursementConfig',
                component: 'outbound/financialManagement/reimbursementManagement/reimbursementConfig/index',
                name: 'OutboundFinancialManagementReimbursementManagementReimbursementConfig',
                meta: {
                  title: '报销配置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //油费管理
          {
            path: 'oilManagement',
            component: 'outbound/financialManagement/oilManagement/index',
            name: 'OutboundFinancialManagementOilManagement',
            meta: {
              title: '油费管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //基地进油管理
              {
                path: 'baseInOilManagement',
                component: 'outbound/financialManagement/oilManagement/baseInOilManagement/index',
                name: 'OutboundFinancialManagementOilManagementBaseInOilManagement',
                meta: {
                  title: '基地进油管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //司机加油管理
              {
                path: 'driverInOilManagement',
                component: 'outbound/financialManagement/oilManagement/driverInOilManagement/index',
                name: 'OutboundFinancialManagementOilManagementDriverInOilManagement',
                meta: {
                  title: '司机加油管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
        ],
      },
      //权限管理
      // {
      //   path: '/permissionManagement',
      //   component: 'Layout',
      //   redirect: '/permissionManagement/roleManagement',
      //   name: 'PermissionManagement',
      //   meta: {
      //     title: '权限管理',
      //     hidden: false,
      //     roles: ['ADMIN'],
      //   },
      //   children: [
      //     {
      //       path: 'roleManagement',
      //       component: 'GlobalMenu/permissionManagement/RoleManagement/index',
      //       name: 'PermissionManagementRoleManagement',
      //       meta: {
      //         title: '角色管理',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //组织管理
      //     {
      //       path: 'organizationManagement',
      //       component: 'GlobalMenu/permissionManagement/organizationManagement/index',
      //       name: 'PermissionManagementOrganizationManagement',
      //       meta: {
      //         title: '组织管理',
      //         roles: ['ADMIN'],
      //         hidden: false,
      //         keepAlive: true,
      //       },
      //     },
      //     //用户管理
      //     {
      //       path: 'userManagement',
      //       component: 'GlobalMenu/permissionManagement/userManagement/index',
      //       name: 'PermissionManagementUserManagement',
      //       meta: {
      //         title: '用户管理',
      //         roles: ['ADMIN'],
      //         hidden: false,
      //         keepAlive: true,
      //       },
      //     },
      //   ],
      // },
      //系统配置
      {
        path: '/systemConfiguration',
        component: 'Layout',
        redirect: '/systemConfiguration/operationLog',
        name: 'SystemConfiguration',
        meta: {
          title: '系统设置',
          hidden: false,
          roles: ['ADMIN'],
        },
        children: [
          // //定时任务
          // {
          //   path: 'timingTask',
          //   component: 'GlobalMenu/systemConfiguration/timingTask/index',
          //   name: 'SystemConfigurationTimingTask',
          //   meta: {
          //     title: '定时任务',
          //     hidden: false,
          //     roles: ['ADMIN'],
          //   },
          // },
          // //操作日志
          // {
          //   path: 'operationLog',
          //   component: 'GlobalMenu/systemConfiguration/operationLog/index',
          //   name: 'SystemConfigurationOperationLog',
          //   meta: {
          //     title: '操作日志',
          //     hidden: false,
          //     roles: ['ADMIN'],
          //   },
          // },
          // //登录日志
          // {
          //   path: 'loginLog',
          //   component: 'GlobalMenu/systemConfiguration/loginLog/index',
          //   name: 'SystemConfigurationLoginLog',
          //   meta: {
          //     title: '登录日志',
          //     hidden: false,
          //     roles: ['ADMIN'],
          //   },
          // },
          //组织管理
          {
            path: 'organizationManagement',
            component: 'GlobalMenu/systemConfiguration/organizationManagement/index',
            name: 'SystemConfigurationOrganizationManagement',
            meta: {
              title: '组织管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //部门管理
              {
                path: 'departmentManagement',
                component: 'GlobalMenu/systemConfiguration/organizationManagement/departmentManagement/index',
                name: 'SystemConfigurationOrganizationManagementDepartmentManagement',
                meta: {
                  title: '部门管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //账号管理
              {
                path: 'accountManagement',
                component: 'GlobalMenu/systemConfiguration/organizationManagement/accountManagement/index',
                name: 'SystemConfigurationOrganizationManagementAccountManagement',
                meta: {
                  title: '账号管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //角色管理
              {
                path: 'roleManagement',
                component: 'GlobalMenu/systemConfiguration/organizationManagement/roleManagement/index',
                name: 'SystemConfigurationOrganizationManagementRoleManagement',
                meta: {
                  title: '角色管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //员工管理
              {
                path: 'staffManagement',
                component: 'GlobalMenu/systemConfiguration/organizationManagement/staffManagement/index',
                name: 'SystemConfigurationOrganizationManagementStaffManagement',
                meta: {
                  title: '员工管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
          //配置管理
          {
            path: 'configurationManagement',
            component: 'GlobalMenu/systemConfiguration/configurationManagement/index',
            name: 'SystemConfigurationConfigurationManagement',
            meta: {
              title: '配置管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //数据导入
              {
                path: 'dataImport',
                component: 'GlobalMenu/systemConfiguration/configurationManagement/dataImport/index',
                name: 'SystemConfigurationConfigurationManagementDataImport',
                meta: {
                  title: '数据导入',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //质损配置
              {
                path: 'defectConfiguration',
                component: 'GlobalMenu/systemConfiguration/configurationManagement/defectConfiguration/index',
                name: 'SystemConfigurationConfigurationManagementDefectConfiguration',
                meta: {
                  title: '质损配置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //系统配置
              {
                path: 'systemConfiguration',
                component: 'GlobalMenu/systemConfiguration/configurationManagement/systemConfiguration/index',
                name: 'SystemConfigurationConfigurationManagementSystemConfiguration',
                meta: {
                  title: '系统配置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //通知管理
              {
                path: 'noticeManagement',
                component: 'GlobalMenu/systemConfiguration/configurationManagement/noticeManagement/index',
                name: 'SystemConfigurationConfigurationManagementNoticeManagement',
                meta: {
                  title: '通知管理',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //维保配置
              {
                path: 'maintenanceConfiguration',
                component: 'GlobalMenu/systemConfiguration/configurationManagement/maintenanceConfiguration/index',
                name: 'SystemConfigurationConfigurationManagementMaintenanceConfiguration',
                meta: {
                  title: '维保配置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //证件提醒
              {
                path: 'certificateReminder',
                component: 'GlobalMenu/systemConfiguration/configurationManagement/certificateReminder/index',
                name: 'SystemConfigurationConfigurationManagementCertificateReminder',
                meta: {
                  title: '证件提醒',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //罚款配置
              {
                path: 'fineConfiguration',
                component: 'GlobalMenu/systemConfiguration/configurationManagement/fineConfiguration/index',
                name: 'SystemConfigurationConfigurationManagementFineConfiguration',
                meta: {
                  title: '罚款配置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //报销比例设置
              {
                path: 'expenseRatioSetting',
                component: 'GlobalMenu/systemConfiguration/configurationManagement/expenseRatioSetting/index',
                name: 'SystemConfigurationConfigurationManagementExpenseRatioSetting',
                meta: {
                  title: '报销比例设置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //板车类型
              {
                path: 'trailerType',
                component: 'GlobalMenu/systemConfiguration/configurationManagement/trailerType/index',
                name: 'SystemConfigurationConfigurationManagementTrailerType',
                meta: {
                  title: '板车类型',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //业务类型
              {
                path: 'businessType',
                component: 'GlobalMenu/systemConfiguration/configurationManagement/businessType/index',
                name: 'SystemConfigurationConfigurationManagementBusinessType',
                meta: {
                  title: '业务类型',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //运输配置
              {
                path: 'transportConfiguration',
                component: 'GlobalMenu/systemConfiguration/configurationManagement/transportConfiguration/index',
                name: 'SystemConfigurationConfigurationManagementTransportConfiguration',
                meta: {
                  title: '运输配置',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //审批流程
              {
                path: 'approvalProcess',
                component: 'GlobalMenu/systemConfiguration/configurationManagement/approvalProcess/index',
                name: 'SystemConfigurationConfigurationManagementApprovalProcess',
                meta: {
                  title: '审批流程',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //点检配置
              {
                path: 'inspectionConfiguration',
                component: 'GlobalMenu/systemConfiguration/configurationManagement/inspectionConfiguration/index',
                name: 'SystemConfigurationConfigurationManagementInspectionConfiguration',
                meta: {
                  title: '点检配置',
                },
              },
            ],
          },
          //数据导出管理
          {
            path: 'dataExportManagement',
            component: 'GlobalMenu/systemConfiguration/dataExportManagement/index',
            name: 'SystemConfigurationDataExportManagement',
            meta: {
              title: '数据导出管理',
              hidden: false,
              roles: ['ADMIN'],
            },
          },
          //日志管理
          {
            path: 'logManagement',
            component: 'GlobalMenu/systemConfiguration/logManagement/index',
            name: 'SystemConfigurationLogManagement',
            meta: {
              title: '日志管理',
              hidden: false,
              roles: ['ADMIN'],
            },
            children: [
              //操作日志
              {
                path: 'operationLog',
                component: 'GlobalMenu/systemConfiguration/operationLog/index',
                name: 'SystemConfigurationOperationLog',
                meta: {
                  title: '操作日志',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
              //登录日志
              {
                path: 'loginLog',
                component: 'GlobalMenu/systemConfiguration/loginLog/index',
                name: 'SystemConfigurationLoginLog',
                meta: {
                  title: '登录日志',
                  hidden: false,
                  roles: ['ADMIN'],
                },
              },
            ],
          },
        ],
      },
      //智慧大屏
      {
        path: '/smartScreen',
        component: 'GlobalMenu/smartScreen/index',
        name: 'SmartScreen',
        meta: {
          title: '智慧大屏',
          hidden: false,
          roles: ['ADMIN'],
        },
      },
    ]
    const InboundMenu = [
      // {
      //   //基础数据
      //   path: '/basicData',
      //   component: 'Layout',
      //   redirect: '/basicData/customerCenter',
      //   name: 'BasicData',
      //   meta: {
      //     title: '基础数据',
      //     icon: 'nested',
      //     hidden: false,
      //     roles: ['ADMIN'],
      //   },
      //   children: [
      //     //客户中心
      //     {
      //       path: 'customerCenter',
      //       component: 'Inbound/basicData/customerCenter/index',
      //       name: 'BasicDataCustomerCenter',
      //       meta: {
      //         title: '客户中心',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //       children: [
      //         //工厂信息
      //         {
      //           path: 'factoryInfo',
      //           component: 'Inbound/basicData/customerCenter/factoryInfo/index',
      //           name: 'BasicDataCustomerCenterFactoryInfo',
      //           meta: {
      //             title: '工厂信息',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //品牌信息
      //         {
      //           path: 'brandInfo',
      //           component: 'Inbound/basicData/customerCenter/brandInfo/index',
      //           name: 'BasicDataCustomerCenterBrandInfo',
      //           meta: {
      //             title: '品牌信息',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //车型信息
      //         {
      //           path: 'vehicleInfo',
      //           component: 'Inbound/basicData/customerCenter/vehicleInfo/index',
      //           name: 'BasicDataCustomerCenterVehicleInfo',
      //           meta: {
      //             title: '车型信息',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //产品信息
      //         {
      //           path: 'productInfo',
      //           component: 'Inbound/basicData/customerCenter/productInfo/index',
      //           name: 'BasicDataCustomerCenterProductInfo',
      //           meta: {
      //             title: '产品信息',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //零部件信息
      //         {
      //           path: 'componentInfo',
      //           component: 'Inbound/basicData/customerCenter/componentInfo/index',
      //           name: 'BasicDataCustomerCenterComponentInfo',
      //           meta: {
      //             title: '零部件信息',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //供应商信息
      //         {
      //           path: 'supplierInfo',
      //           component: 'Inbound/basicData/customerCenter/supplierInfo/index',
      //           name: 'BasicDataCustomerCenterSupplierInfo',
      //           meta: {
      //             title: '供应商信息',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //           children: [
      //             //供应商列表
      //             {
      //               path: 'supplierList',
      //               component: 'Inbound/basicData/customerCenter/supplierInfo/supplierList/index',
      //               name: 'BasicDataCustomerCenterSupplierInfoSupplierList',
      //               meta: {
      //                 title: '供应商列表',
      //                 hidden: false,
      //                 roles: ['ADMIN'],
      //               },
      //             },
      //             //车队信息
      //             {
      //               path: 'fleetInfo',
      //               component: 'Inbound/basicData/customerCenter/supplierInfo/fleetInfo/index',
      //               name: 'BasicDataCustomerCenterSupplierInfoFleetInfo',
      //               meta: {
      //                 title: '车队信息',
      //                 hidden: false,
      //                 roles: ['ADMIN'],
      //               },
      //             },
      //             //车辆信息
      //             {
      //               path: 'vehicleInfo',
      //               component: 'Inbound/basicData/customerCenter/supplierInfo/vehicleInfo/index',
      //               name: 'BasicDataCustomerCenterSupplierInfoVehicleInfo',
      //               meta: {
      //                 title: '车辆信息',
      //                 hidden: false,
      //                 roles: ['ADMIN'],
      //               },
      //             },
      //             //司机信息
      //             {
      //               path: 'driverInfo',
      //               component: 'Inbound/basicData/customerCenter/supplierInfo/driverInfo/index',
      //               name: 'BasicDataCustomerCenterSupplierInfoDriverInfo',
      //               meta: {
      //                 title: '司机信息',
      //                 hidden: false,
      //                 roles: ['ADMIN'],
      //               },
      //             },
      //             //车型信息
      //             {
      //               path: 'vehicleModelInfo',
      //               component: 'Inbound/basicData/customerCenter/supplierInfo/vehicleModelInfo/index',
      //               name: 'BasicDataCustomerCenterSupplierInfoVehicleModelInfo',
      //               meta: {
      //                 title: '车型信息',
      //                 hidden: false,
      //                 roles: ['ADMIN'],
      //               },
      //             },
      //           ],
      //         },
      //         //器具信息
      //         {
      //           path: 'toolInfo',
      //           component: 'Inbound/basicData/customerCenter/toolInfo/index',
      //           name: 'BasicDataCustomerCenterToolInfo',
      //           meta: {
      //             title: '器具信息',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //           children: [
      //             //器具列表
      //             {
      //               path: 'toolList',
      //               component: 'Inbound/basicData/customerCenter/toolInfo/toolList/index',
      //               name: 'BasicDataCustomerCenterToolInfoToolList',
      //               meta: {
      //                 title: '器具列表',
      //                 hidden: false,
      //                 roles: ['ADMIN'],
      //               },
      //             },
      //             //器具维修
      //             {
      //               path: 'toolRepair',
      //               component: 'Inbound/basicData/customerCenter/toolInfo/toolRepair/index',
      //               name: 'BasicDataCustomerCenterToolInfoToolRepair',
      //               meta: {
      //                 title: '器具维修',
      //                 hidden: false,
      //                 roles: ['ADMIN'],
      //               },
      //             },
      //           ],
      //         },
      //       ],
      //     },
      //     //业务数据
      //     {
      //       path: 'businessData',
      //       component: 'Inbound/basicData/businessData/index',
      //       name: 'BasicDataBusinessData',
      //       meta: {
      //         title: '业务数据',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //       children: [
      //         //路线信息
      //         {
      //           path: 'routeInfo',
      //           component: 'Inbound/basicData/businessData/routeInfo/index',
      //           name: 'BasicDataBusinessDataRouteInfo',
      //           meta: {
      //             title: '路线信息',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //中转库管理
      //         {
      //           path: 'transferWarehouseManagement',
      //           component: 'Inbound/basicData/businessData/transferWarehouseManagement/index',
      //           name: 'BasicDataBusinessDataTransferWarehouseManagement',
      //           meta: {
      //             title: '中转库管理',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //VMI仓管理
      //         {
      //           path: 'vmiWarehouseManagement',
      //           component: 'Inbound/basicData/businessData/vmiWarehouseManagement/index',
      //           name: 'BasicDataBusinessDataVmiWarehouseManagement',
      //           meta: {
      //             title: 'VMI仓管理',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //站点围栏
      //         {
      //           path: 'siteFence',
      //           component: 'Inbound/basicData/businessData/siteFence/index',
      //           name: 'BasicDataBusinessDataSiteFence',
      //           meta: {
      //             title: '站点围栏',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //月台信息
      //         {
      //           path: 'platformInfo',
      //           component: 'Inbound/basicData/businessData/platformInfo/index',
      //           name: 'BasicDataBusinessDataPlatformInfo',
      //           meta: {
      //             title: '月台信息',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //           children: [
      //             //道口管理
      //             {
      //               path: 'crossingManagement',
      //               component: 'Inbound/basicData/businessData/platformInfo/crossingManagement/index',
      //               name: 'BasicDataBusinessDataPlatformInfoCrossingManagement',
      //               meta: {
      //                 title: '道口管理',
      //                 hidden: false,
      //                 roles: ['ADMIN'],
      //               },
      //             },
      //             //工作日历
      //             {
      //               path: 'workCalendar',
      //               component: 'Inbound/basicData/businessData/platformInfo/workCalendar/index',
      //               name: 'BasicDataBusinessDataPlatformInfoWorkCalendar',
      //               meta: {
      //                 title: '工作日历',
      //                 hidden: false,
      //                 roles: ['ADMIN'],
      //               },
      //             },
      //             //月台列表
      //             {
      //               path: 'platformList',
      //               component: 'Inbound/basicData/businessData/platformInfo/platformList/index',
      //               name: 'BasicDataBusinessDataPlatformInfoPlatformList',
      //               meta: {
      //                 title: '月台列表',
      //                 hidden: false,
      //                 roles: ['ADMIN'],
      //               },
      //             },
      //             //线路配置
      //             {
      //               path: 'lineConfiguration',
      //               component: 'Inbound/basicData/businessData/platformInfo/lineConfiguration/index',
      //               name: 'BasicDataBusinessDataPlatformInfoLineConfiguration',
      //               meta: {
      //                 title: '线路配置',
      //                 hidden: false,
      //                 roles: ['ADMIN'],
      //               },
      //             },
      //           ],
      //         },
      //       ],
      //     },
      //   ],
      // },
      // {
      //   //订单管理
      //   path: '/orderManagement',
      //   component: 'Layout',
      //   redirect: '/orderManagement/orderList',
      //   name: 'OrderManagement',
      //   meta: {
      //     title: '订单管理',
      //     icon: 'nested',
      //     hidden: false,
      //     roles: ['ADMIN'],
      //   },
      //   children: [
      //     //订单管理
      //     {
      //       path: 'orderList',
      //       component: 'Inbound/orderManagement/orderList/index',
      //       name: 'OrderManagementOrderList',
      //       meta: {
      //         title: '订单管理',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //   ],
      // },
      // {
      //   //运输管理
      //   path: '/transportManagement',
      //   component: 'Layout',
      //   redirect: '/transportManagement/deliveryOrderManagement/deliveryOrderList',
      //   name: 'TransportManagement',
      //   meta: {
      //     title: '运输管理',
      //     icon: 'nested',
      //     hidden: false,
      //     roles: ['ADMIN'],
      //   },
      //   children: [
      //     //送货单管理
      //     {
      //       path: 'deliveryOrderManagement',
      //       component: 'Inbound/transportManagement/deliveryOrderManagement/index',
      //       name: 'TransportManagementDeliveryOrderManagement',
      //       meta: {
      //         title: '送货单管理',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //       children: [
      //         //送货单列表
      //         {
      //           path: 'deliveryOrderList',
      //           component: 'Inbound/transportManagement/deliveryOrderManagement/deliveryOrderList/index',
      //           name: 'TransportManagementDeliveryOrderManagementDeliveryOrderList',
      //           meta: {
      //             title: '送货单列表',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //货垛明细
      //         {
      //           path: 'cargoStackDetails',
      //           component: 'Inbound/transportManagement/deliveryOrderManagement/cargoStackDetails/index',
      //           name: 'TransportManagementDeliveryOrderManagementCargoStackDetails',
      //           meta: {
      //             title: '货垛明细',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //       ],
      //     },
      //     //集货单管理
      //     {
      //       path: 'collectionOrderManagement',
      //       component: 'Inbound/transportManagement/collectionOrderManagement/index',
      //       name: 'TransportManagementCollectionOrderManagement',
      //       meta: {
      //         title: '集货单管理',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //       children: [
      //         //集货单列表
      //         {
      //           path: 'collectionOrderList',
      //           component: 'Inbound/transportManagement/collectionOrderManagement/collectionOrderList/index',
      //           name: 'TransportManagementCollectionOrderManagementCollectionOrderList',
      //           meta: {
      //             title: '集货单列表',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //         //箱单明细
      //         {
      //           path: 'boxDetails',
      //           component: 'Inbound/transportManagement/collectionOrderManagement/boxDetails/index',
      //           name: 'TransportManagementCollectionOrderManagementBoxDetails',
      //           meta: {
      //             title: '箱单明细',
      //             hidden: false,
      //             roles: ['ADMIN'],
      //           },
      //         },
      //       ],
      //     },
      //     //运单管理
      //     {
      //       path: 'waybillManagement',
      //       component: 'Inbound/transportManagement/waybillManagement/index',
      //       name: 'TransportManagementWaybillManagement',
      //       meta: {
      //         title: '运单管理',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //   ],
      // },
      // {
      //   //中转库管理
      //   path: '/transferWarehouseManagement',
      //   component: 'Layout',
      //   redirect: '/transferWarehouseManagement/inboundManagement',
      //   name: 'TransferWarehouseManagement',
      //   meta: {
      //     title: '中转库管理',
      //     icon: 'nested',
      //     hidden: false,
      //     roles: ['ADMIN'],
      //   },
      //   children: [
      //     //入库管理
      //     {
      //       path: 'inboundManagement',
      //       component: 'Inbound/transferWarehouseManagement/inboundManagement/index',
      //       name: 'TransferWarehouseManagementInboundManagement',
      //       meta: {
      //         title: '入库管理',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //在库管理
      //     {
      //       path: 'storageManagement',
      //       component: 'Inbound/transferWarehouseManagement/storageManagement/index',
      //       name: 'TransferWarehouseManagementStorageManagement',
      //       meta: {
      //         title: '在库管理',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //出库管理
      //     {
      //       path: 'outboundManagement',
      //       component: 'Inbound/transferWarehouseManagement/outboundManagement/index',
      //       name: 'TransferWarehouseManagementOutboundManagement',
      //       meta: {
      //         title: '出库管理',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //高低库预警
      //     {
      //       path: 'warningManagement',
      //       component: 'Inbound/transferWarehouseManagement/warningManagement/index',
      //       name: 'TransferWarehouseManagementWarningManagement',
      //       meta: {
      //         title: '高低库预警',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //   ],
      // },
      // {
      //   //VMI仓管理
      //   path: '/vmiWarehouseManagement',
      //   component: 'Layout',
      //   redirect: '/vmiWarehouseManagement/inboundManagement',
      //   name: 'VmiWarehouseManagement',
      //   meta: {
      //     title: 'VMI仓管理',
      //     icon: 'nested',
      //     hidden: false,
      //     roles: ['ADMIN'],
      //   },
      //   children: [
      //     //入库管理
      //     {
      //       path: 'inboundManagement',
      //       component: 'Inbound/vmiWarehouseManagement/inboundManagement/index',
      //       name: 'VmiWarehouseManagementInboundManagement',
      //       meta: {
      //         title: '入库管理',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //在库管理
      //     {
      //       path: 'storageManagement',
      //       component: 'Inbound/vmiWarehouseManagement/storageManagement/index',
      //       name: 'VmiWarehouseManagementStorageManagement',
      //       meta: {
      //         title: '在库管理',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //出库管理
      //     {
      //       path: 'outboundManagement',
      //       component: 'Inbound/vmiWarehouseManagement/outboundManagement/index',
      //       name: 'VmiWarehouseManagementOutboundManagement',
      //       meta: {
      //         title: '出库管理',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //在库预警
      //     {
      //       path: 'storageWarning',
      //       component: 'Inbound/vmiWarehouseManagement/storageWarning/index',
      //       name: 'VmiWarehouseManagementStorageWarning',
      //       meta: {
      //         title: '在库预警',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //     //仓库异常
      //     {
      //       path: 'warehouseAbnormal',
      //       component: 'Inbound/vmiWarehouseManagement/warehouseAbnormal/index',
      //       name: 'VmiWarehouseManagementWarehouseAbnormal',
      //       meta: {
      //         title: '仓库异常',
      //         hidden: false,
      //         roles: ['ADMIN'],
      //       },
      //     },
      //   ],
      // },
    ]
    function generateRoutes(roles: string[]) {
      return new Promise<RouteRecordRaw[]>((resolve, reject) => {
        const { data: asyncRoutes } = {
          data: menuList.value,
          // data: WmsMenu,
          // data: OutboundMenu,
          // data: InboundMenu,
          // data: newOutboundMenu,
        }
        const accessedRoutes = filterAsyncRoutes(asyncRoutes as RouteRecordRaw[], roles)
        setRoutes(accessedRoutes)
        resolve(accessedRoutes)
      })
    }

    return {
      menu,
      routes,
      setRoutes,
      generateRoutes,
    }
  },
  {
    persist: {
      key: 'aaa',
      storage: sessionStorage,
    },
  },
)

// 非setup
export function usePermissionStoreHook() {
  return usePermissionStore(store)
}
