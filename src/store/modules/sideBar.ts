/*
 * @Author: llm
 * @Date: 2023-07-03 17:39:56
 * @LastEditors: llm
 * @LastEditTime: 2025-05-26 20:42:51
 * @Description: 左侧菜单栏
 *
 */
import { defineStore } from 'pinia'

export const useSideBarStore = defineStore(
  'sideBar',
  () => {
    // state
    const menuList = ref([])
    const menuId = ref('')
    const menu = ref({})
    const meta = ref({})
    const btnMenuId = ref('')
    const btnMenuQuery = ref({})
    const menuCount = ref([])
    const mergeDialogFormParams = ref({})
    const storeDialogFormParams = ref({} as any)
    const pageType = ref('')
    const pageName = ref('')
    const getMenuCountFun = ref<null | (() => void)>()

    // action
    function setGetMenuCountFun(fn: () => void) {
      getMenuCountFun.value = fn
    }

    return {
      menuList,
      menuId,
      menu,
      meta,
      btnMenuId,
      btnMenuQuery,
      menuCount,
      mergeDialogFormParams,
      storeDialogFormParams,
      pageType,
      pageName,
      getMenuCountFun,
      setGetMenuCountFun,
    }
  },
  {
    // 开启数据缓存
    persist: {
      storage: localStorage,
      paths: ['menuId', 'menuList', 'pageType'], // 指定要长久化的字段
    },
  },
)
