/*
 * @Author: llm
 * @Date: 2024-12-13 15:51:21
 * @LastEditors: llm
 * @LastEditTime: 2024-12-13 15:59:27
 * @Description:智能调度
 */
import { defineStore } from 'pinia'

export const useSmartDispatchStore = defineStore(
  'smartDispatch',
  () => {
    /**
     * 智能调度弹窗中的右侧整车列表
     */
    const smartDispatchRightVehicleList = ref<any[]>([])

    function setSmartDispatchRightVehicleList(data: any[]) {
      smartDispatchRightVehicleList.value = data
    }

    function getSmartDispatchRightVehicleList() {
      return smartDispatchRightVehicleList.value
    }

    return {
      setSmartDispatchRightVehicleList,
      getSmartDispatchRightVehicleList,
      smartDispatchRightVehicleList,
    }
  },
  {
    // 开启数据缓存
    persist: {
      storage: localStorage,
      paths: ['smartDispatchRightVehicleList'], // 指定要长久化的字段
    },
  },
)
