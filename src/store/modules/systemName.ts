import { defineStore } from 'pinia'

export const useSystemNameStore = defineStore(
  'useSystemNameStore',
  () => {
    /**
     * 智能调度弹窗中的右侧整车列表
     */
    const systemName = ref<String>('VTMS')

    function systemNameChange(data: string) {
      systemName.value = data
    }

    return {
      systemName,
      systemNameChange,
    }
  },
  {
    // 开启数据缓存
    persist: {
      storage: localStorage,
      paths: ['systemName'], // 指定要长久化的字段
    },
  },
)
