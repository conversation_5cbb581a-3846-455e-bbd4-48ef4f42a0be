/*
 * @Author: llm
 * @Date: 2023-06-10 12:12:01
 * @LastEditors: llm
 * @LastEditTime: 2025-07-17 15:22:28
 * @Description:
 *
 */
import { defineStore } from 'pinia'

import { useStorage } from '@vueuse/core'
import { loginApi } from '@/api/auth'
import { resetRouter } from '@/router'
import { store } from '@/store'

import { LoginData } from '@/api/auth/types'

import Avatar from '@/assets/avatar.png'
export const useUserStore = defineStore(
  'user',
  () => {
    // state
    const TOKEN = useStorage('accessToken', '')
    const systemType = ref('')
    const userInfo = useStorage('userInfo', { userName: '', companyName: '', realName: '' })
    const nickname = ref('')
    const avatar = ref('')
    const roles = ref<string[]>([]) // 用户角色编码集合 → 判断路由权限
    const menuList = useStorage('menuList', Array<any>)
    const systemList = useStorage('systemList', Array<any>)

    function setSystemList(params: any) {
      systemList.value = params
    }
    function getSystemList() {
      return systemList.value
    }

    function setSystemType(params: any) {
      systemType.value = params
    }
    function getSystemType() {
      return systemType.value
    }
    /**
     * 登录调用
     *
     * @param {LoginData}
     * @returns
     */
    function login(loginData: LoginData) {
      return new Promise<void>((resolve, reject) => {
        loginApi(loginData)
          .then(async (response: any) => {
            const { token, userName, companyName, realName } = response.data
            TOKEN.value = token
            userInfo.value.userName = userName!
            userInfo.value.companyName = companyName!
            userInfo.value.realName = realName!

            // 移除登录成功后的超时定时器相关代码
            // setLastActivityTime()
            // 使用简化的token管理器
            // import('@/utils/tokenManager').then(({ updateActivity }) => {
            //   updateActivity()
            // })

            // menuList.value = menus;
            resolve(response.data)
          })
          .catch((error) => {
            reject(error)
          })
      })
    }

    // 获取信息(用户昵称、头像、角色集合、权限集合)
    function getInfo(): any {
      return new Promise((resolve, reject) => {
        const data = {
          avatar: Avatar,
          nickname: '系统管理员',
          roles: ['ROOT'],
          userId: 2,
        }
        if (!data) {
          return reject('Verification failed, please Login again.')
        }
        if (!data.roles || data.roles.length <= 0) {
          reject('getUserInfo: roles must be a non-null array!')
        }
        nickname.value = data.nickname
        avatar.value = data.avatar
        roles.value = data.roles
        resolve(data)
      })
    }

    // 注销
    function logout() {
      return new Promise<void>((resolve, reject) => {
        // 移除清除定时器相关代码
        // import('@/utils/tokenManager').then(({ cleanupTokenManager }) => {
        //   cleanupTokenManager()
        // })
        resetRouter()
        resetToken()
        location.reload() // 清空路由
        resolve()
      })
    }

    // 重置
    function resetToken() {
      // 清除定时器
      // import('@/utils/tokenManager').then(({ cleanupTokenManager }) => {
      //   cleanupTokenManager()
      // })
      TOKEN.value = ''
      systemType.value = ''
      userInfo.value = { userName: '', companyName: '', realName: '' }
      nickname.value = ''
      avatar.value = ''
      roles.value = []
      // 移除清除最后活动时间
      // clearLastActivityTime()
    }

    // 保留这些函数，但移除实际功能，避免引用错误
    // function setLastActivityTime() {
    //   const currentTime = Date.now()
    //   localStorage.setItem('lastActivityTime', currentTime.toString())
    // }

    function getLastActivityTime(): number | null {
      return null
    }

    function clearLastActivityTime() {
      // localStorage.removeItem('lastActivityTime')
      // 移除功能实现
    }

    return {
      TOKEN,
      systemType,
      nickname,
      userInfo,
      avatar,
      roles,
      login,
      getInfo,
      logout,
      resetToken,
      setSystemType,
      getSystemType,
      setSystemList,
      getSystemList,
      // setLastActivityTime,
      // getLastActivityTime,
      // clearLastActivityTime,
    }
  },
  {
    persist: {
      storage: localStorage,
      paths: ['systemType', 'systemList'], // 指定要长久化的字段
    },
  },
)

// 非setup
export function useUserStoreHook() {
  return useUserStore(store)
}
