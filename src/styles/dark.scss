html.dark {
  --menuBg: var(--el-bg-color-overlay);
  --menuText: #fff;
  --menuActiveText: var(--el-menu-active-color);
  --menuHover: rgb(0 0 0 / 20%);
  --subMenuBg: var(--el-menu-bg-color);
  --subMenuActiveText: var(--el-menu-active-color);
  --subMenuHover: rgb(0 0 0 / 20%);

  .navbar {
    color: var(--el-text-color-regular);
    background-color: var(--el-bg-color);

    .setting-container .setting-item:hover {
      background: var(--el-fill-color-light);
    }
  }

  .right-panel-btn {
    background-color: var(--el-color-primary-dark);
  }

  .svg-icon,
  svg {
    fill: var(--el-text-color-regular);
  }

  .sidebar-container {
    .el-menu-item.is-active .svg-icon {
      fill: var(--el-color-primary);
    }
  }
}
