@use 'variables' as *;
@use './sidebar';
@use './reset';
@use './dark';
@use './transition';
@use './quill-editor';

.app-container {
  margin: 10px;

  .search {
    padding: 18px 0 0 10px;
    margin-bottom: 10px;
    background-color: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    box-shadow: var(--el-box-shadow-light);
  }

  .flex-row {
    display: flex;
    flex-direction: row;
  }

  .flex-col {
    display: flex;
    flex-direction: column;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-evenly {
    justify-content: space-evenly;
  }

  .justify-around {
    justify-content: space-around;
  }

  .justify-between {
    justify-content: space-between;
  }

  .items-start {
    align-items: flex-start;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .text-right {
    text-align: right;
  }

  .text-left {
    text-align: left;
  }
}

.cursor {
  cursor: pointer;
}

/* 弹出层设置背景色  头部 */
.el-dialog__header {
  margin-right: 0 !important;
  // background: $dialogHeaderBg;
}

/* 引入 VXE Table 专用 tooltip 样式 */
@import './vxe-table-tooltip.scss';

.app-padding {
  padding: 10px 20px 10px 0;
}

.single-line {
  overflow: hidden; //溢出隐藏
  text-overflow: ellipsis; //...
  white-space: nowrap; //禁止换行
}

.top-query {
  .el-card__body {
    padding-bottom: 0 !important;
    padding-top: 10px !important;
  }
}

/* 异常点窗体信息 */

.BMap_bubble_title {
  color: white;
  font-size: 16px;
  font-weight: bold;
  text-align: left;
  padding: 5px 10px;
  border-bottom: 1px solid rgb(191, 191, 192);
  background-color: #3f916b;
}
.BMap_bubble_title .titleText {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/* 消息内容 */
.BMap_bubble_content {
  background-color: white;
}
.BMap_bubble_content .titleBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #228b22;
  font-weight: 500;
  color: #fff;
  padding: 5px 10px;
  font-size: 16px;
}
.BMap_bubble_content .titleBox .close-btn {
  border: none;
  padding: 0 4px;
  cursor: pointer;
  background: transparent;
  color: #fff;
}
.BMap_bubble_content .contentBox {
  position: relative;
  padding: 15px 10px;
  font-size: 14px;
}
.BMap_bubble_content .contentBox .title {
  color: #228b22;
  font-weight: 500;
}
/* 内容 */
.BMap_pop div:nth-child(9) {
  top: 16px !important;
  border-radius: 7px;
}
/* 左上角删除按键 */
.BMap_pop img {
  display: none;
}
.BMap_shadow img {
  display: none;
}
.BMap_top {
  display: none;
}
.BMap_top div {
  background-color: white;
}
.BMap_bottom {
  display: none;
}
.BMap_center {
  display: none;
}
/* 隐藏边角 */
.BMap_pop div:nth-child(1) div {
  display: none;
}
.BMap_pop div:nth-child(3) {
  display: none;
}
.BMap_pop div:nth-child(5) {
  display: none;
}
.BMap_pop div:nth-child(7) {
  display: none;
}
.el-form--inline {
  .el-form-item {
    .el-input,
    .el-cascader,
    .el-select,
    .el-autocomplete {
      width: 240px;
    }
  }
}
.el-table__cell {
  .el-form-item {
    margin-bottom: 0 !important;
  }
}

.el-card {
  --el-card-padding: 15px !important;
}
.el-select-group .el-select-dropdown__item {
  padding-left: 40px !important;
}
.el-upload-dragger {
  padding: 0 !important;
  width: 100px !important;
  height: 100px !important;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: none !important;
}
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
.el-table .danger-row {
  --el-table-tr-bg-color: var(--el-color-danger-light-9);
}
.el-table .info-row {
  --el-table-tr-bg-color: var(--el-color-info-light-9);
}
.el-table .primary-row {
  --el-table-tr-bg-color: var(--el-color-primary-light-9);
}
.el-upload--picture-card {
  --el-upload-picture-card-size: 102px !important;
}
.el-upload-list--picture-card {
  --el-upload-list-picture-card-size: 102px !important;
}
.el-table .cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.vxe-cell--wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}
