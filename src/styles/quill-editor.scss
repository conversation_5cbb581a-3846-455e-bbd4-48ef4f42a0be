:deep(.ql-snow .ql-tooltip[data-mode='link']::before) {
  content: '请输入链接地址:';
}
:deep(.ql-snow .ql-tooltip.ql-editing a.ql-action::after) {
  border-right: 0px;
  content: '保存';
  padding-right: 0px;
}

:deep(.ql-snow .ql-tooltip[data-mode='video']::before) {
  content: '请输入视频地址:';
}

:deep(.ql-snow .ql-picker.ql-size .ql-picker-label::before),
:deep(.ql-snow .ql-picker.ql-size .ql-picker-item::before) {
  content: '14px';
}
:deep(.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='small']::before),
:deep(.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='small']::before) {
  content: '10px';
}
:deep(.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='large']::before),
:deep(.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='large']::before) {
  content: '18px';
}
:deep(.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='huge']::before),
:deep(.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='huge']::before) {
  content: '32px';
}

:deep(.ql-snow .ql-picker.ql-header .ql-picker-label::before),
:deep(.ql-snow .ql-picker.ql-header .ql-picker-item::before) {
  content: '文本';
}
:deep(.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='1']::before),
:deep(.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='1']::before) {
  content: '标题1';
}
:deep(.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='2']::before),
:deep(.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='2']::before) {
  content: '标题2';
}
:deep(.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='3']::before),
:deep(.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='3']::before) {
  content: '标题3';
}
:deep(.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='4']::before),
:deep(.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='4']::before) {
  content: '标题4';
}
:deep(.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='5']::before),
:deep(.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='5']::before) {
  content: '标题5';
}
:deep(.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='6']::before),
:deep(.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='6']::before) {
  content: '标题6';
}

:deep(.ql-snow .ql-picker.ql-font .ql-picker-label::before),
:deep(.ql-snow .ql-picker.ql-font .ql-picker-item::before) {
  content: '标准字体';
}
:deep(.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='serif']::before),
:deep(.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='serif']::before) {
  content: '衬线字体';
}
:deep(.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='monospace']::before),
:deep(.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='monospace']::before) {
  content: '等宽字体';
}
:deep(.ql-align-center) {
  text-align: center;
}
:deep(.ql-align-right) {
  text-align: right;
}
:deep(.ql-align-left) {
  text-align: left;
}
:deep(.ql-editor) {
  height: 200px !important;
}
