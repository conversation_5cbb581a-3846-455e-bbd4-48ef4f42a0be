@use 'sass:map';

// 全局SCSS变量
:root {
  --menuBg: #041527;
  --menuText: #a6aaae;
  --menuActiveText: #fff;
  --menuHover: #041527;
  --subMenuBg: #000c17;
  --subMenuActiveText: #fff;
  --subMenuHover: #041527;
  --subMenuActiveBg: #1890ff;
  --dialogHeaderBg: #f8f8f8;
  --el-menu-item-height: 46px;
  --el-card-padding: 10px !important;
  --el-upload-picture-card-size: 100px !important;
  --el-upload-list-picture-card-size: 100px !important;
}

// 基础变量
$menuBg: #304156 !default;
$menuText: #bfcbd9 !default;
$menuActiveText: #409eff !default;
$menuHover: #263445 !default;

$subMenuBg: #1f2d3d !default;
$subMenuHover: #001528 !default;
$subMenuActiveText: #f4f4f5 !default;
$subMenuActiveBg: #263445 !default;

$dialogHeaderBg: var(--dialogHeaderBg) !default;

// 布局变量
$sideBarWidth: 210px !default;
$navBarHeight: 50px !default;
$hideSideBarWidth: 54px !default;
