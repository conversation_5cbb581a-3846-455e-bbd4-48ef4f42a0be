declare global {
  declare const BMapGL: any
  declare const BMAP_ANCHOR_BOTTOM_LEFT: any // 根据实际情况定义类型
  declare const BMAP_ANCHOR_BOTTOM_RIGHT: any // 根据实际情况定义类型
  /**
   * 分页查询参数
   */
  interface PageQuery {
    page?: number
    limit?: number
  }

  /**
   * basePage 左侧列表查询条件
   */
  interface BasePageQuery extends PageQuery {
    /**
     * 名称
     */
    name: string
  }

  /**
   * 请求结果响应对象
   */
  interface ResponseResult {
    // forEach(arg0: (item: any) => void): unknown;
    //
    // forEach: any;
    //
    // forEach(arg0: (item: any) => void): unknown;

    [x: string]: any

    /**
     * 状态码
     */
    code?: number
    /**
     * 提示信息
     */
    message?: string
    /**
     * 数据
     */
    data?: any
  }

  /**
   * 分页响应对象
   */
  interface PageResult {
    /**
     * 数据列表
     */
    rows: any[]
    /**
     * 数据总数
     */
    total: number
    /**
     * 统计数据
     */
    statistics: Array<any>
    /**
     * 分页统计
     */
    pageSummary: any
    /**
     * 总计统计
     */
    totalSummary: any
  }

  /**
   * 弹窗属性
   */
  interface DialogOption {
    /**
     * 弹窗标题
     */
    title?: string
    /**
     * 是否显示
     */
    visible?: boolean
    /**
     * 弹窗宽度
     */
    dialogWidth?: string
  }

  /**
   * 组件数据源
   */
  interface OptionType {
    /**
     * 值
     */
    value: any
    /**
     * 文本
     */
    label: string
    /**
     * 子列表
     */
    children?: OptionType[]
  }

  /**
   * table配置项
   */
  export interface TableConfig {
    /**
     * 显示复选框
     */
    showHandleSelection?: boolean
    /**
     * 显示序号
     */
    showSort?: boolean
    /**
     * 文字太长时，是否显示tooltip
     */
    showToolTip?: boolean
    /**
     * 是否需要分页
     */
    isPagination?: boolean
    /**
     * 表格高度
     */
    tableHeight?: string
    /**
     * 列表项
     */
    tableItem: TableItem[]
    /**
     * 操作项
     */
    operation?: Operation | undefined
    /**
     * 更新数据列
     */
    isKey?: boolean
  }

  export interface Operation {
    /**
     * 表头
     */
    label?: string
    /**
     * 操作项
     */
    items?: OperationItems[]
    /**
     * 列宽
     */
    width?: string
  }

  /**
   * 各项操作按钮
   */
  export interface OperationItems {
    /**
     * 按钮名称
     */
    title?: string
    /**
     * 按钮类型
     */
    type: string
    /**
     * 按钮id
     */
    menuId?: string
  }

  /**
   * 列表配置项
   */
  export interface TableItem {
    /**
     * id
     */
    id?: string

    /**
     * 列表项对应的key
     */
    [x: string]: any

    /**
     * 是否需要从json中解析,默认是取list中的key
     */
    fromJson?: boolean
    /**
     * 列表项名称
     */
    label?: string
    /**
     * 对齐方式
     */
    align?: string
    /**
     * 列表项宽度
     */
    width?: string
    /**
     * 列表项最小宽度
     */
    minWidth?: string
    /**
     * 列表项操作按钮名称
     */
    operation?: string
    /**
     * 过滤项
     */
    filters?: Array<any>
    /**
     * 类型 (text-文本；switch-开关；tag-标签;tree-树形；operation-按钮)
     */
    type?: string
    /**
     * 如果类型是tag,则是需要渲染的key
     */
    tagNames?: string | undefined
    /**
     * 跳转链接（带参数）
     */
    routerLink?: RouterLink | undefined
    /**
     * form表单中的数据
     */
    form?: FormVO
    /**
     * 搜索项中的数据
     */
    query?: FormVO
    /**
     * 搜索项是否需要展示
     */
    selectEnable?: boolean
    /**
     * 表单中是否展示
     */
    insertEnable?: boolean
    /**
     * 表单中编辑是否展示
     */
    updateEnable?: boolean
    /**
     * 表单中是否可编辑
     */
    canEdit?: boolean
    /**
     * 栅格布局，每行显示几列
     */
    colSpan?: number
    /**
     * 保留的小数点位数
     */
    precision?: number
    /**
     * 是否支持模糊搜索
     */
    canSearch?: boolean
    /**
     * 是否需要列表中展示
     */
    listEnable?: boolean | undefined
    /**
     * 列属性
     */
    attributes?: attributesVO
    /**
     * 跳转方式
     */
    jump?: JumpVO
    /**
     * 运单号
     */
    currentNo?: string
    /**
     * 子列表
     */
    children?: TableItem[]
    /**
     * 表单分组
     */
    group?: GroupVO
    /**
     * 分组key
     */
    groupKey?: string
    /**
     * 是否可排序
     */
    sortAble?: boolean
    /**
     * 是否需要展示下拉菜单
     */
    btns?: DataColumnBtnsVO[]
    /**
     * 说明
     */
    instructions?: string
    /**
     * 菜单id
     */
    menuId?: string
    /**
     * 子列表
     */
    items?: SelectGroupItem[]
  }

  export interface SelectGroupItem {
    /**
     * 默认值
     */
    defaultValue?: string
    /**
     * 下拉项
     */
    option?: FormOptions
    /**
     * 下拉项名称
     */
    name: string
    /**
     * 类型
     */
    type: string
    /**
     * 依赖项
     */
    dependsOn?: DependsOn[]
    /**
     * 最大值
     */
    max?: number
    /**
     * 最小值
     */
    min?: number
    /**
     * 保留的小数点位数
     */
    precision?: number
  }

  export interface DataColumnBtnsVO {
    /**
     * 按钮文案
     */
    label: string
    /**
     * 按钮类型
     */
    purpose: string
    /**
     * 按钮位置
     */
    position: string
  }

  export interface GroupVO {
    /**
     * 分组名称
     */
    title: string
    /**
     * 排序
     */
    sortNo: number
    /**
     * 标题后的描述（富文本）
     */
    instructions: string
  }

  export interface JumpVO {
    /**
     * 类型(menu->跳转菜单; image->弹窗图片展示; pdf->直接下载; file->直接下载; mouseOverShow->鼠标悬浮展示)
     */
    jumpType?: string
    jumpField?: string
    /**
     * 需要展示的字段
     */
    field?: string
    /**
     * 当jumpType = menu时跳转对应菜单的name(路由上的name)，当jumpType=pdf或file ，为下载地址
     */
    targetField?: string
    /**
     * 当jumpType=menu时，需要传递的参数
     */
    query?: Array<QueryItemVO>
    /**
     * 菜单id
     */
    menuId?: string
    /**
     * 标题
     */
    title: string
    /**
     * 跳转目标菜单需要全局存储的storeData
     */
    storeData: FormRequestParamsVO[]
    /**
     * 跳转目标菜单需要合并的formData
     */
    formData: FormRequestParamsVO[]
    /**
     * 跳转目标菜单需要传递的参数
     */
    params: FormRequestParamsVO[]
    /**
     * 请求地址
     */
    requestUri: string
    /**
     * 弹窗宽度
     */
    dialogWidth: string
    /**
     * 是否全屏
     */
    fullScreen: boolean
    /**
     * 依赖项
     */
    dependsOn: DependsOn[]
    /**
     * 请求方法
     */
    method: string
    /**
     * 请求地址
     */
    formUri: string
  }

  /**
   * 当jumpType=menu时，需要传递的参数
   */
  export interface QueryItemVO {
    /**
     * 参数来源（listData->列表中的数据; queryForm->搜索条件中的数据）
     */
    dependFrom?: string
    /**
     * 参数名称
     */
    name?: string
    /**
     * 当jumpType=menu时，跳转到菜单后需要赋值的目标字段名称
     */
    targetName?: string
    /**
     * 当jumpType=menu时，跳转到菜单后需要赋值的目标字段名称对应的值，（当value=$#$#时，value赋值为dependFrom对应的name的值，否则直接赋值）
     */
    value?: string | number | boolean | Array<string | number | boolean>
  }

  /**
   * form表单中的数据
   */
  export interface FormVO {
    [x: string]: any

    /**
     * 默认值
     */
    defaultValue: any
    /**
     * 动态值(e:-1 并且type = mouth 时，表示上一个月)
     */
    dynamicValue?: any
    /**
     * 默认日期
     */
    showDefaultDate: boolean
    /**
     * 编辑状态是否能编辑
     */
    canEdit?: boolean
    /**
     * 编辑状态依赖于某个字段判断是否能编辑
     */
    editOn?: DependsOn[]
    /**
     * 新增状态是否能编辑
     */
    canInsert?: boolean
    /**
     * 是否存在关联
     */
    dependsOn?: DependsOn[]
    /**
     * 是否显示
     */
    canShow?: boolean
    /**
     * 下拉option
     */
    option: FormOptions
    /**
     * 校验
     */
    rules: Rules[]
    /**
     * 排序
     */
    sortNo?: number
    /**
     * tips
     */
    tips?: string
    /**
     * 类型 (text-文本；switch-开关；tag-标签;tree-树形；operation-按钮；uploadImage-上传图片)
     */
    type: string
    /**
     * name
     */
    name?: string
    /**
     * label
     */
    label?: string
    /**
     * 时间格式化
     */
    format?: string
    /**
     * 快捷选择日期时间段
     */
    shortcuts: string
    /**
     * 最小值
     */
    min: number
    /**
     * 最大值
     */
    max: number
    /**
     * 步长
     */
    step: number
    /**
     * 单位
     */
    unit: string
    /**
     * 单位下拉
     */
    units?: SelectVO[]
    /**
     * placeholder
     */
    placeholder: string
    /**
     * 宽度
     */
    width: string
    /**
     * 上传图片属性
     */
    imageOption?: ImageFileOptionVO
    /**
     * 上传文件属性
     */
    fileOption?: ImageFileOptionVO
    /**
     * 上传视频属性
     */
    videoOption?: ImageFileOptionVO
    /**
     * switch 属性
     */
    switchOption?: SwitchOptionVO
    /**
     * 是否长期
     */
    longTerm?: boolean
  }

  /**
   * 下拉项
   */
  interface SelectVO {
    name: string
    value: string
  }

  interface ImageFileOptionVO {
    /**
     * 提示信息
     */
    tips?: string
    /**
     * 上传文件类型 (file->文件；base64->base64)
     */
    type: string
    /**
     * 返回数据key
     */
    uploadData: string
    /**
     * 说明背景图
     */
    background: string
    /**
     * 业务线
     */
    businessLine: string
    /**
     * 上传数量
     */
    count: number
    /**
     * 请求地址
     */
    uri: string
    /**
     * 是否必填校验
     */
    required: Boolean
    /**
     * 目标字段
     */
    targetFields: FormRequestParamsVO[]
    /**
     * 文件类型
     */
    accept: string
    /**
     * 是否多选
     */
    multiple: boolean
    /**
     * 文件大小
     */
    size: number
  }

  interface SwitchOptionVO {
    /**
     * 目标字段
     */
    targetFields: FormRequestParamsVO[]
    /**
     * 请求地址
     */
    uri: string
  }

  /**
   * 依赖项,根据条件控制显示隐藏
   */
  interface DependsOn {
    /**
     * 依赖的key
     */
    field?: string
    /**
     * 依赖的key对应的值
     */
    when?: string
    /**
     * 来源（listData->列表中的数据；responseData->请求数据中的数据）
     */
    dependFrom?: string
    /**
     * 判断符号（greate->'>'  greatEq->'>=' less->'<' lessEq->'<=' eq->'=' in->'||'）
     */
    operator?: string
  }

  /**
   * 下拉option
   */
  interface FormOptions {
    /**
     * 下拉数据
     */
    data: SelectOptions[]
    /**
     * 临时保存的下拉数据,本地做过滤使用
     */
    filterData: SelectOptions[]
    /**
     * 是否多选
     */
    multiple?: boolean
    /**
     * 请求地址
     */
    uri: string
    /**
     * 请求方法
     */
    method: string
    /**
     * 获取默认值的uri
     */
    defaultUrl?: string
    /**
     * 请求参数
     */
    params?: Object | any

    multiParams?: FormRequestParamsVO[]
    /**
     * 依赖于当前字段（有值则判断当前字段是否为空，不为空再请求下拉）
     */
    dependOn: string
    /**
     * 依赖来源于哪里 （listData->列表中的数据 ； currentForm->当前表单中的数据）
     */
    dependFrom: string
    /**
     * 目标字段（用于选择下拉后，展示选中下拉中对应的key）
     */
    targetField: string

    targetFields: FormRequestParamsVO[]
    /**
     * Cascader中 是否严格的遵守父子节点不互相关联
     */
    checkStrictly: boolean

    /**
     * 当前选中的下拉对象请求后获取的对象
     */
    targetObj?: any
    /**
     * 是否需要缓存 false->不缓存 true->缓存
     */
    cache: boolean
    /**
     * 下拉空提示信息
     */
    emptyTip?: string
    /**
     * 是否需要刷新列表
     */
    refreshPage?: boolean
  }

  export interface SelectOptions {
    label: string
    value: any
    children?: SelectOptions[]
    type?: any
    desc?: string
    active?: boolean
    disabled?: boolean
  }

  /**
   * 跳转链接
   */
  export interface RouterLink {
    /**
     * 路由名称（router 中的 name e->"UserManagement")
     */
    name?: string
    /**
     * 参数数组（需要传递的参数名称 e-> ["name","age"]）
     */
    query?: Array
  }

  /**
   * meta项
   */
  export interface MetaVO {
    dataColumn: TableItem[]
    ext?: ExtVO | null
    hidden?: boolean
    /**
     * 图标
     */
    icon?: string
    /**
     * 背景色
     */
    background?: string
    title?: string
    type?: number
    /**
     * mapType = geoFence 显示地图
     */
    mapType?: string | null
    /**
     * 按钮（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
     */
    position?: string
    /**
     * 用于区分按钮 （add-新增；delete-删除；edit-编辑；singlePage-单页；。。。）
     */
    purpose?: string
    /**
     * 请求地址
     */
    uri?: string
    /**
     * 是否显示列表右侧操作列
     */
    operation?: boolean
    /**
     * 按钮下的表单
     */
    form?: BtnFormVO
    /**
     * 按钮id
     */
    menuId: string
    /**
     * 控制是否显示按钮
     */
    dependsOn: DependsOn[]
    /**
     * 跳转方式
     */
    jump?: JumpVO | null
    /**
     * 页面类型
     */
    pageType: string
  }

  interface ExtVO {
    /**
     * 按钮组顶部提示信息 富文本
     */
    instructions?: string
    /**
     * 是否展示左侧选择列
     */
    showHandleSelection?: boolean
    /**
     * 是否展示排序
     */
    showSort?: boolean
    /**
     * table是否展示全选复选框按钮
     */
    tableMultiSelect: boolean
    /**
     * 请求接口是否需要传storeData中的参数
     */
    needStore: boolean
    /**
     * 进入页面是否需要请求接口
     * true->请求 false->不请求，没值默认请求
     */
    needRequest?: boolean
    /**
     * 是否关闭当前弹窗（用在menuDialog中）
     */
    closeCurrentDialog?: boolean
    /**
     * 是否刷新外层列表（用在menuDialog中，关掉当前弹窗，刷新外层列表）
     */
    refreshParentList?: boolean
    /**
     * 是否页面顶部显示查询表单(默认不展示)
     */
    statForm?: boolean
    /**
     * 弹窗宽度
     */
    dialogWidth?: string
    /**
     * 是否全屏
     */
    fullScreen?: boolean
    /**
     * 下载链接地址
     */
    linkUrl?: string
    /**
     * 是否刷新列表
     */
    refreshPage?: boolean
    /**
     * 右侧提示信息
     */
    rightTip?: RightTipVO
    /**
     * 分页请求方法
     */
    pageMethod?: string
    /**
     * 分页条数
     */
    pageLimit?: number
    /**
     * 导出key  如果配置了，则导出时，会根据这个key去导出，否则默认id
     */
    exportKey?: string
  }

  interface RightTipVO {
    /**
     * 请求地址
     */
    uri: string
    /**
     * 请求方法
     */
    method: string
  }

  export interface RightTipDataVO {
    /**
     * 提示信息
     */
    timeoutMessage: string
    /**
     * 参数list
     */
    timeoutParam: Object
  }

  /**
   * 按钮下请求操作
   */
  export interface BtnFormVO {
    /**
     * 表单中的操作按钮
     */
    btns?: BtnRequestVO[]
    /**
     * 确认弹窗提示信息
     */
    confirmContent?: string
    /**
     * 确认按钮文字
     */
    confirmTitle?: string
    /**
     * 来源 （listData->列表中）
     */
    dependFrom?: string
    /**
     * 请求表单数据的请求地址
     */
    formUri?: string
    /**
     * 需要勾选列表项的最大数
     */
    max?: number
    /**
     * 需要勾选列表项的最小数
     */
    min?: number
    /**
     * 请求方法
     */
    method?: string
    /**
     * 请求数据需要传递的参数
     */
    params?: FormRequestParamsVO[]
    /**
     * 是否刷新当前列表
     */
    refreshPage: boolean
    /**
     * 是否更新菜单上的数字
     */
    refreshMenuCount: boolean
    /**
     * 需要请求的其他接口地址及参数
     */
    trigerUris: trigerUrisVO[]
    /**
     * 列表默认选中项（ids的数组）
     */
    listSelect: ListSelectVO[]
    /**
     * 点击按钮传递到页面顶部的查询条件
     */
    query?: QueryItemVO[]
    /**
     * 跳转的目标菜单
     */
    targetField?: string
    /**
     * 跳转目标菜单需要合并的formData
     */
    formData: FormRequestParamsVO[]
    /**
     * 跳转目标菜单需要全局存储的storeData
     */
    storeData: FormRequestParamsVO[]
    /**
     * menuId
     */
    menuId: string

    /**
     * title
     */
    title: string
    /**
     * 如果批量操作需要使用选中行中的某个字段（不是id)，需要使用这个
     */
    batchCommit?: BatchCommitVO
  }

  /**
   * 如果批量操作需要使用选中行中的某个字段（不是id)，需要使用这个
   */
  export interface BatchCommitVO {
    /**
     * row的name
     */
    name: string
    /**
     * 需要请求的参数
     */
    targetName: string
  }

  /**
   * 点击列表右侧按钮跳转传递的参数
   */
  export interface ListSelectVO {
    /**
     * 参数来源（listData->列表中的数据; queryForm->搜索条件中的数据）
     */
    dependFrom?: string
    /**
     * 字段名
     */
    name?: string
    /**
     * 值
     */
    value?: string
  }

  /**
   * 需要请求的其他接口地址及参数
   */
  export interface trigerUrisVO {
    /**
     * 请求方法
     */
    method: string
    /**
     * 请求地址
     */
    uri: string
    /**
     * 请求参数
     */
    params: QueryItemVO[]
  }

  /**
   * 请求表单数据的参数
   */
  export interface FormRequestParamsVO {
    /**
     * dependFrom对应位置的name
     */
    name?: string
    /**
     * 参数名称
     */
    targetName?: string
    /**
     * $#$# dependFrom对应位置的name的值
     */
    value?: string
    /**
     * 参数来源（listData->列表中的数据; queryForm->搜索条件中的数据）
     */
    dependFrom?: string
  }

  /**
   * 表单中按钮的请求操作
   */
  export interface BtnRequestVO {
    /**
     * 按钮名称
     */
    label?: string
    /**
     * 请求地址
     */
    uri?: string
    /**
     * 请求方法
     */
    method?: string

    /**
     * 下载类型
     */
    download?: string
    /**
     * 接口返回类型
     */
    responseType?: string
    /**
     * 按钮背景色
     */
    background: string
    /**
     * 判断条件，条件符合再显示
     */
    dependsOn: DependsOn[]
    /**
     * 请求数据参数
     */
    params: FormRequestParamsVO[]

    /**
     * 二次弹窗属性
     */
    secondDialog: SecondDialogVO

    purpose?: string
    /**
     * 是否关闭当前弹窗
     */
    closeDialog?: boolean
    /**
     * 跳转目标菜单
     */
    jump?: JumpVO
  }

  interface SecondDialogVO {
    /**
     * 是否根据返回的结果判断二次弹窗（有值弹，没值不弹）
     */
    result: boolean
    method: string
    uri: string
  }

  /**
   * 菜单项
   */
  export interface MenuVO {
    children?: MenuVO[]
    component?: string
    menuId?: ''
    meta: MetaVO
    name?: string
    columnName?: string | undefined
    title?: string

    parentId?: string
    tableId?: string
    path?: string
    permissions?: string
    redirect?: string
    sort?: number
    /**
     * 类型 1 菜单 2 按钮 3tab 4 目录
     */
    type?: string
  }
}

/**
 * 左侧栏列表数据
 */
export interface LeftSideBarItemVO {
  id?: string
}

/**
 * 表单项
 */
export interface FormColumn {
  /**
   * 文案
   */
  label?: string
  /**
   * key
   */
  name?: string
  /**
   * 类型
   */
  type?: string
  /**
   * 提示信息
   */
  tips?: string
  /**
   * 校验
   */
  rules?: Rules[]
  /**
   * 排序
   */
  sort?: number
  /**
   * meta
   */
  meta?: MetaVO
}

/**
 * 校验
 */
export interface Rules {
  /**
   * 是否必填
   */
  required?: boolean
  /**
   * 最小长度、最小值
   */
  min?: number
  /**
   * 最大长度、最大值
   */
  max?: number
  /**
   * 提示信息
   */
  message?: string
  /**
   * 正则校验(校验手机号：/^1[3|4|5|6|7|8|9][0-9]\d{8}$/)
   */
  pattern?: any
  /**
   * 触发时机（blur-失去焦点；change-切换；）
   */
  trigger?: string
  validator: (rule: any, value: any, callback: any) => void
}

/**
 * 表格合并
 */
export interface SpanMethodProps {
  row: User
  column: TableColumnCtx<User>
  rowIndex: number
  columnIndex: number
}

/**
 *  车辆信息
 */
export interface VehicleVO {
  /**
   * 车辆id
   */
  vehicleId?: string
  /**
   * 车牌号
   */
  vehicleNo?: string
}

/**
 *  司机信息
 */
export interface DriverVO {
  /**
   * 车辆id
   */
  driverId: string
  /**
   * 司机名称
   */
  realName: string
}

/**
 *  仓库信息
 */
export interface WarehouseVO {
  /**
   * 承运商仓库id
   */
  id: string
  /**
   * 承运商仓库名称
   */
  name: string
}

export {}

/**
 * 列属性 （left->左固定；right->又固定）
 */
interface attributesVO {
  fixed?: string
  /**
   * 复制
   */
  copy?: boolean
  /**
   * 排序
   */
  sort?: boolean
  /**
   * 说明
   */
  content?: string
  /**
   * 排序字段（有就取它，没有就默认取name)
   */
  sortColumn?: string
}

/**
 * 上传图片格式
 */
export interface UploadImageVO {
  /**
   * 图片地址
   */
  url: string
  /**
   * 名称
   */
  name?: string
}

/**
 * 统计组件
 */
export interface StatisticColumnVO {
  /**
   * label
   */
  label: string
  /**
   * name
   */
  name: string
  /**
   * 值
   */
  number: number
  /**
   * 单位
   */
  suffix: string
}

/**
 * 选择的左侧树形列表项数据
 */
export interface SelectLeftTreeRowsVO {
  /**
   * 菜单ID
   */
  menuId: string
  /**
   * 选择的左侧树形列表项数据
   */
  selectLeftTreeRow?: any
}

/**
 * 左侧属性列表查询条件
 */
export interface LeftSideQueryParams extends PageQuery {
  /**
   * 搜索的关键词
   */
  name: string
  /**
   * 启用禁用
   */
  enable: boolean | string
}

/**
 * 打印
 */
export interface PrintVO {
  titleList: string[]
  dataList: string[][]
}
