/*
 * @Author: llm
 * @Date: 2024-01-05 11:27:01
 * @LastEditors: llm
 * @LastEditTime: 2025-03-10 18:04:30
 * @Description:
 */
declare module 'lodash' {
  // 在这里添加你需要的类型声明
  export function chunk(array: any[], size?: number): any[][]
  export function compact(array: any[]): any[]
  export function cloneDeep(value: any)
  export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait?: number,
    options?: {
      leading?: boolean
      maxWait?: number
      trailing?: boolean
    },
  ): (...args: Parameters<T>) => ReturnType<T>
}
