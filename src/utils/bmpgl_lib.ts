/*
 * @Author: llm
 * @Date: 2023-07-12 16:11:59
 * @LastEditors: llm <EMAIL>
 * @LastEditTime: 2023-10-28 09:01:46
 * @Description: bmpgl_lib.ts
 *
 */

const ak: string = 'ZHr1DjwmXSKTV5Rg0m5WnIFvG44gFTsC' // 百度的地图密钥
declare const BMapGL: any
declare global {
  interface Window {
    init: () => void
  }
}

/**
 * 异步加载百度地图
 * @returns {Promise}
 * @constructor
 */
export const loadBaiDuMap = (): Promise<any> => {
  return new Promise(function (resolve, reject) {
    try {
      resolve({ BMapGL })
    } catch (err) {
      window.init = function () {
        resolve(BMapGL)
      }
      let script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = `https://api.map.baidu.com/api?v=3.0&type=webgl&ak=${ak}&callback=init`
      script.onerror = reject
      document.body.appendChild(script)
    }
  })
}
/**
 * 异步加载百度地图,以及绘制工具
 * @returns {Promise}
 * @constructor
 */
export const loadBaiDuDrawMap = (): Promise<any> => {
  return loadBaiDuMap().then((BMapGLLib: any) => {
    let loaded = false
    try {
      loaded = BMapGLLib && BMapGLLib.DrawingManager
    } catch (err) {
      loaded = false
    }
    if (!loaded) {
      // 批量加载所需脚本和样式
      const scripts = [
        'https://mapopen.cdn.bcebos.com/github/BMapGLLib/DrawingManager/src/DrawingManager.min.js',
        // "src/utils/TextIconOverlay_min.js",
        // "src/utils/MarkerClusterer_min.js",
        // "src/utils/InfoBox_min.js"
      ]
      const styles = ['https://mapopen.cdn.bcebos.com/github/BMapGLLib/DrawingManager/src/DrawingManager.min.css']
      const head = document.getElementsByTagName('head')[0]
      // 加载样式
      styles.forEach((style) => {
        let link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = style
        head.appendChild(link)
      })

      // 加载脚本
      const promises = scripts.map((script) => {
        return new Promise((resolve, reject) => {
          let scriptElement = document.createElement('script')
          scriptElement.type = 'text/javascript'
          scriptElement.onload = resolve
          scriptElement.onerror = reject
          scriptElement.src = script
          head.appendChild(scriptElement)
        })
      })
      // 使用 Promise.all 确保所有脚本加载完成
      return Promise.all(promises).then(() => BMapGLLib)
    } else {
      return BMapGLLib
    }
  })
}
