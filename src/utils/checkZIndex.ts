/*
 * @Author: llm
 * @Date: 2025-01-08
 * @LastEditors: llm
 * @LastEditTime: 2025-01-08
 * @Description: 检查页面元素的 z-index 值
 */

/**
 * 检查页面中所有重要元素的 z-index
 */
export function checkAllZIndex() {
  const elements = [
    { selector: '.el-dialog', name: '弹窗' },
    { selector: '.el-dialog__wrapper', name: '弹窗包装器' },
    { selector: '.el-overlay', name: '遮罩层' },
    { selector: '.el-tooltip__popper', name: 'Tooltip' },
    { selector: '.vxe-table', name: 'VXE Table' },
    { selector: '.el-drawer', name: '抽屉' },
    { selector: '.el-popover', name: '气泡框' },
    { selector: '.el-message', name: '消息' },
    { selector: '.el-notification', name: '通知' }
  ]

  console.log('=== Z-Index 检查报告 ===')
  
  elements.forEach(({ selector, name }) => {
    const els = document.querySelectorAll(selector)
    if (els.length > 0) {
      console.log(`\n${name} (${selector}):`)
      els.forEach((el, index) => {
        if (el instanceof HTMLElement) {
          const computedStyle = window.getComputedStyle(el)
          const zIndex = computedStyle.zIndex
          const position = computedStyle.position
          console.log(`  ${index + 1}. z-index: ${zIndex}, position: ${position}`)
        }
      })
    } else {
      console.log(`${name}: 未找到`)
    }
  })
  
  console.log('\n=== 检查完成 ===')
}

/**
 * 获取元素的实际 z-index 值
 */
export function getElementZIndex(element: HTMLElement): number {
  const computedStyle = window.getComputedStyle(element)
  const zIndex = computedStyle.zIndex
  return zIndex === 'auto' ? 0 : parseInt(zIndex) || 0
}

/**
 * 找到页面中最高的 z-index 值
 */
export function findHighestZIndex(): number {
  let highest = 0
  const allElements = document.querySelectorAll('*')
  
  allElements.forEach(el => {
    if (el instanceof HTMLElement) {
      const zIndex = getElementZIndex(el)
      if (zIndex > highest) {
        highest = zIndex
      }
    }
  })
  
  return highest
}

/**
 * 实时监控 z-index 变化
 */
export function monitorZIndex() {
  console.log('开始监控 z-index 变化...')
  
  const observer = new MutationObserver(() => {
    setTimeout(() => {
      const highest = findHighestZIndex()
      console.log(`当前最高 z-index: ${highest}`)
      
      // 检查 tooltip 的 z-index
      const tooltips = document.querySelectorAll('.el-tooltip__popper')
      if (tooltips.length > 0) {
        console.log(`当前 tooltip 数量: ${tooltips.length}`)
        tooltips.forEach((tooltip, index) => {
          if (tooltip instanceof HTMLElement) {
            const zIndex = getElementZIndex(tooltip)
            console.log(`  Tooltip ${index + 1}: z-index = ${zIndex}`)
          }
        })
      }
    }, 100)
  })
  
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['style', 'class']
  })
  
  return observer
}

// 在开发环境下添加到 window 对象
if (process.env.NODE_ENV === 'development') {
  (window as any).zIndexChecker = {
    checkAll: checkAllZIndex,
    findHighest: findHighestZIndex,
    monitor: monitorZIndex,
    getElementZIndex
  }
  
  console.log('Z-Index 检查工具已加载，使用 window.zIndexChecker 访问')
}
