/*
 * @Author: llm
 * @Date: 2023-07-04 12:17:22
 * @LastEditors: llm
 * @LastEditTime: 2025-07-17 10:40:11
 * @Description: 公共方法
 *
 */
import { AxiosResponse } from 'axios'
// 状态管理依赖
import { useFormStore } from '@/store/modules/form.js'
import { useSideBarStore } from '@/store/modules/sideBar'
// const { proxy }: any = getCurrentInstance();
const formStore = useFormStore()
const sideBarStore = useSideBarStore()
const localMap: any = {}
const { selectRows, selectLeftTreeRows } = storeToRefs(formStore)
import { getUserMenuPermission, switchStatus } from '@/api/auth'
import { CustomerVO } from '@/api/customerCenter/customerBaseData/types'
import { globalRequestApi, globalRequestUrlApi } from '@/api/planManagement'

/**
 * 获取当前菜单下的数据列
 * @param menuId 当前菜单id
 * @returns 当前菜单下的数据列
 */
export function getcurrentUserMenuColumnlist(menuId: string): any {
  return new Promise(async (resolve, reject) => {
    await getUserMenuPermission(menuId)
      .then((res) => {
        const { data } = res
        if (data) {
          resolve(data)
        }
        reject(`返回的数据data不应该为${data}`)
      })
      .catch((err) => {
        reject(err)
      })
  })
}
/**
 * 判断当前按钮是否有权限访问
 * @param permissionList 当前菜单下的权限列表
 * @param menuId 当前按钮menuId
 */
export const hasButtonPermission = (permissionList: MenuVO[], menuId: string) => {
  const hasPerm = permissionList?.some((perm: MenuVO) => perm.menuId?.includes(menuId))
  return hasPerm
}

/**
 * 初始化表单
 * @param tableItem
 */
export const resetFormGlobalFun = (tableItem: CustomerVO[]) => {
  const formData: CustomerVO = {}
  tableItem?.forEach((item: CustomerVO) => {
    if (item.name) {
      if (item.type === 'switch') {
        formData[item.name] = true
      } else if (item.type === 'number') {
        formData[item.name] = 0
      } else {
        formData[item.name] = ''
      }
    }
  })
  return formData
}

/**
 * 启用禁用开关
 * @param enable 当前状态
 * @param name 需要禁用的名称
 * @param id 当前id
 * @param uri 请求地址前缀
 * @param typeName 当前类型名称 （客户？4s店?...)
 */
export const switchChangeGlobalFun = (enable: boolean, id: string, uri: string) =>
  new Promise<void>((resolve, reject) => {
    const text = !enable ? '停用' : '启用'
    ElMessageBox.confirm(`确认要${text}` + '吗?', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        await switchStatus(id, uri)
        ElMessage.success(`${text}成功`)
        resolve()
      })
      .catch(() => {
        reject()
      })
  })
/**
 * 下载文件
 * @param res 文件流
 */
export const downloadFileGlobalFun = (res: AxiosResponse) =>
  new Promise<void>((resolve, reject) => {
    const blob = new Blob([res.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8',
    })
    const a = document.createElement('a')
    const href = window.URL.createObjectURL(blob) // 下载的链接
    a.href = href
    a.download = decodeURI(res.headers['content-disposition'].split(';')[1].split('=')[1]) // 获取后台设置的文件名称
    document.body.appendChild(a)
    a.click() // 点击导出
    document.body.removeChild(a) // 下载完成移除元素
    window.URL.revokeObjectURL(href) // 释放掉blob对象
    resolve()
  })
/**
 * 下载压缩文件
 * @param res 文件流
 */
export const downloadZipFileGlobalFun = (res: AxiosResponse) =>
  new Promise<void>((resolve, reject) => {
    const blob = new Blob([res.data], {
      type: 'application/zip;charset=utf-8',
    })
    const a = document.createElement('a')
    const href = window.URL.createObjectURL(blob) // 下载的链接
    a.href = href
    a.download = decodeURI(res.headers['content-disposition'].split(';')[1].split('=')[1]) // 获取后台设置的文件名称
    document.body.appendChild(a)
    a.click() // 点击导出
    document.body.removeChild(a) // 下载完成移除元素
    window.URL.revokeObjectURL(href) // 释放掉blob对象
    resolve()
  })

/**
 * 通过uri获取form表单下拉列表options
 * @param dataColumn
 * @param formData 表单项（用于表单中级联操作用）
 * @param dependOn 当前修改的依赖项下拉
 * @param type 请求类型，topQuerySelect->顶部搜索下拉 formSelect->表单下拉
 * @param key 当前key,如果存在则只更改对应的下拉项，用在远程模糊搜索
 * @param remoteQuery 远程模糊搜索{keyword:any}
 * @param row 当前行数据，用于模糊搜索回显
 * @returns  dataColumn
 */
export const getSelectOptions: any = async (
  dataColumn: TableItem[],
  formData?: any,
  dependOn?: string,
  type?: string,
  key?: string,
  remoteQuery: Object = {},
  row?: any,
) => {
  if (!dataColumn) return
  if (key) {
    const item = dataColumn.find((item) => item.form?.name === key)
    if (item) {
      const { localData, mergeParams } = (await getLocalData(item!, formData, key, remoteQuery)) as any
      item!.form!.option!.data = localData
      // //如果item.form?.option.defaultUri存在，获取默认值
      // nextTick(async () => {
      //   if (item!.form?.option!.defaultUrl) {
      //     let res = null
      //     if (!item!.form?.option?.method || item!.form?.option?.method === 'get' || item!.form?.option?.method === 'GET') {
      //       res = await globalRequestUrlApi(mergeParams, item!.form?.option?.method!, item!.form?.option?.defaultUrl!)
      //     } else {
      //       res = await globalRequestApi(mergeParams, item!.form?.option?.method!, item!.form?.option?.defaultUrl!)
      //     }
      //     if (res.data) {
      //       const { label, value } = res.data
      //       formData[label] = value
      //     }
      //   }
      // })
    }
  } else {
    // 遍历 dataColumn 中的每一项
    for (const item of dataColumn!) {
      // 检查类型是否为 "formSelect"
      if (type === 'formSelect') {
        // 检查表单类型是否为 "select" 或 "selectTree" 或 "cascader"，并且不与 dependOn 关联，且 insertEnable 为 true
        if (
          item.form?.name !== dependOn &&
          (item.insertEnable || item.updateEnable) &&
          (!item.form?.option?.dependOn ||
            (item.form?.option?.dependFrom === 'currentForm' && formData && formData[item.form?.option?.dependOn]) ||
            item.form?.option?.dependFrom === 'listData' ||
            item.form?.option?.dependFrom === 'parentListData' ||
            item.form?.option?.dependFrom === 'storeData')
        ) {
          // 没有配置uri，跳过，继续获取其他form表单数据项
          if (!item.form?.option?.uri) {
            continue
          }
          let tempQuery = {}
          if (row && item.form?.type === 'fuzzySelect') {
            tempQuery = {
              keyword: row[item.form?.name!.slice(0, -2) + 'Name'],
            }
          }
          const { localData, params } = (await getLocalData(item, formData, key!, tempQuery)) as any

          //如果存在目标字段，需要将对应表单字段进行数据填充 formData[item.form.name]=data[item.form.option.targetField]
          if (item.form?.option?.targetField) {
            formData[item.form.name!] = localData[item.form.option.targetField]
          } else if (item.form?.option?.targetFields) {
            let targetFields = item.form?.option?.targetFields
            targetFields.forEach((targetField) => {
              //类型是select 的并且 name 与targetField.name相同的，把data[targetField.targetName]赋值给item.form.option.data
              if (item.form?.type === 'select' || item.form?.type === 'selectTree' || item.form?.type === 'cascader') {
                if (item.form.name === targetField.name) {
                  if (!item.form!.option!.data) {
                    item.form!.option!.data = []
                  }
                  item.form!.option!.data = localData[targetField.targetName!]
                } else {
                  formData[targetField.name!] = localData[targetField.targetName!]
                }
              } else {
                // 其他类型，直接赋值
                formData[targetField.name!] = localData[targetField.targetName!]
              }
            })
          } else {
            item.form!.option!.data = localData
          }
        }
      } else if (type === 'topQuerySelect') {
        // 检查查询条件类型是否为 "select" 或 "selectTree" 或 "cascader"，并且不与 dependOn 关联，且 selectEnable 为 true
        const queryTypeList = ['select', 'selectTree', 'cascader']
        if (
          queryTypeList.includes(item.query?.type!) &&
          item.query?.name !== dependOn &&
          item.selectEnable &&
          (!item.query?.option?.dependOn ||
            (item.query?.option?.dependFrom === 'currentForm' && formData && formData[item.query?.option?.dependOn]) ||
            item.query?.option?.dependFrom === 'listData' ||
            item.query?.option?.dependFrom === 'parentListData' ||
            item.query?.option?.dependFrom === 'storeData')
        ) {
          if (!item.query?.option?.uri) {
            continue
          }
          // 封装下拉请求参数
          let params: any = {}
          // 下拉请求参数，配置dependon，需要遍历params进行参数替换（$#$#），否则直接取值
          if (item.query?.option?.dependOn) {
            for (const key in item.query.option.params) {
              composeOldFormParams(params, key, item, null, selectRows.value[0], formData)
            }
          } else {
            params = item.query?.option?.params ?? {}
          }
          item.query.option.multiParams?.map((_item) => {
            composeRequestParams(params, _item, null, sideBarStore.$state.storeDialogFormParams, selectRows.value[0], null, formData)
          })
          // 拼接storeData参数
          if (item.query?.option?.dependFrom === 'storeData') {
            params = Object.assign(params, sideBarStore.$state.storeDialogFormParams)
          }
          let res = null
          if (!item.query?.option?.method || item.query?.option?.method === 'get' || item.query?.option?.method === 'GET') {
            res = await globalRequestUrlApi(params, item.query?.option?.method!, item.query?.option?.uri!)
          } else {
            res = await globalRequestApi(params, item.query?.option?.method!, item.query?.option?.uri!)
          }
          const { data } = res
          item.query!.option!.data = data || []
        }
      }
    }
    //遍历dataColumn ，用sortNo从小到大进行排序
    dataColumn.sort((a, b) => {
      return a.sortNo - b.sortNo
    })
  }

  return dataColumn
}
export const getQuerySelectOptions: any = async (
  dataColumn: TableItem[],
  formData?: any,
  dependOn?: string,
  type?: string,
  key?: string,
  remoteQuery: Object = {},
  row?: any,
) => {
  if (!dataColumn) return
  if (key) {
    const item = dataColumn.find((item) => item.query?.name === key)
    if (item) {
      const { localData, mergeParams } = (await getQueryLocalData(item!, formData, key, remoteQuery)) as any
      item!.query!.option!.data = localData
      //如果item.form?.option.defaultUri存在，获取默认值
      nextTick(async () => {
        if (item!.query?.option!.defaultUrl) {
          let res = null
          if (!item!.query?.option?.method || item!.query?.option?.method === 'get' || item!.query?.option?.method === 'GET') {
            res = await globalRequestUrlApi(mergeParams, item!.query?.option?.method!, item!.query?.option?.defaultUrl!)
          } else {
            res = await globalRequestApi(mergeParams, item!.query?.option?.method!, item!.query?.option?.defaultUrl!)
          }
          if (res.data) {
            const { label, value } = res.data
            formData[label] = value
          }
        }
      })
    }
  } else {
    // 遍历 dataColumn 中的每一项
    for (const item of dataColumn!) {
      // 检查类型是否为 "formSelect"
      if (type === 'formSelect') {
        // 检查表单类型是否为 "select" 或 "selectTree" 或 "cascader"，并且不与 dependOn 关联，且 insertEnable 为 true
        if (
          item.query?.name !== dependOn &&
          (item.insertEnable || item.updateEnable) &&
          (!item.query?.option?.dependOn ||
            (item.query?.option?.dependFrom === 'currentForm' && formData && formData[item.query?.option?.dependOn]) ||
            item.query?.option?.dependFrom === 'listData' ||
            item.query?.option?.dependFrom === 'parentListData' ||
            item.query?.option?.dependFrom === 'storeData')
        ) {
          // 没有配置uri，跳过，继续获取其他form表单数据项
          if (!item.query?.option?.uri) {
            continue
          }
          let tempQuery = {}
          if (row && item.query?.type === 'fuzzySelect') {
            tempQuery = {
              keyword: row[item.query?.name!.slice(0, -2) + 'Name'],
            }
          }
          const { localData, params } = (await getQueryLocalData(item, formData, key!, tempQuery)) as any
          //如果存在目标字段，需要将对应表单字段进行数据填充 formData[item.form.name]=data[item.form.option.targetField]
          if (item.query?.option?.targetField) {
            formData[item.query.name!] = localData[item.query.option.targetField]
          } else if (item.query?.option?.targetFields) {
            let targetFields = item.query?.option?.targetFields
            targetFields.forEach((targetField) => {
              //类型是select 的并且 name 与targetField.name相同的，把data[targetField.targetName]赋值给item.form.option.data
              if (item.query?.type === 'select' || item.query?.type === 'selectTree' || item.query?.type === 'cascader') {
                if (item.query.name === targetField.name) {
                  if (!item.query!.option!.data) {
                    item.query!.option!.data = []
                  }
                  item.query!.option!.data = localData[targetField.targetName!]
                } else {
                  formData[targetField.name!] = localData[targetField.targetName!]
                }
              } else {
                formData[targetField.name!] = localData[targetField.targetName!]
              }
            })
          } else {
            item.query!.option!.data = localData
          }
          //如果item.form?.option.defaultUri存在，获取默认值
          nextTick(async () => {
            if (item.query?.option!.defaultUrl) {
              let res = null
              if (!item.query?.option?.method || item.query?.option?.method === 'get' || item.query?.option?.method === 'GET') {
                res = await globalRequestUrlApi(params, item.query?.option?.method!, item.query?.option?.defaultUrl!)
              } else {
                res = await globalRequestApi(params, item.query?.option?.method!, item.query?.option?.defaultUrl!)
              }
              if (res.data) {
                const { label, value } = res.data
                formData[label] = value
              }
            }
          })
        }
      } else if (type === 'topQuerySelect') {
        // 检查查询条件类型是否为 "select" 或 "selectTree" 或 "cascader"，并且不与 dependOn 关联，且 selectEnable 为 true
        const queryTypeList = ['select', 'selectTree', 'cascader']
        if (
          queryTypeList.includes(item.query?.type!) &&
          item.query?.name !== dependOn &&
          item.selectEnable &&
          (!item.query?.option?.dependOn ||
            (item.query?.option?.dependFrom === 'currentForm' && formData && formData[item.query?.option?.dependOn]) ||
            item.query?.option?.dependFrom === 'listData' ||
            item.query?.option?.dependFrom === 'parentListData' ||
            item.query?.option?.dependFrom === 'storeData')
        ) {
          if (!item.query?.option?.uri) {
            continue
          }
          // 封装下拉请求参数
          let params: any = {}
          // 下拉请求参数，配置dependon，需要遍历params进行参数替换（$#$#），否则直接取值
          if (item.query?.option?.dependOn) {
            for (const key in item.query.option.params) {
              composeOldFormParams(params, key, item, null, selectRows.value[0], formData)
            }
          } else {
            params = item.query?.option?.params ?? {}
          }
          item.query.option.multiParams?.map((_item) => {
            composeRequestParams(params, _item, null, sideBarStore.$state.storeDialogFormParams, selectRows.value[0], null, formData)
          })
          // 拼接storeData参数
          if (item.query?.option?.dependFrom === 'storeData') {
            params = Object.assign(params, sideBarStore.$state.storeDialogFormParams)
          }
          let res = null
          if (!item.query?.option?.method || item.query?.option?.method === 'get' || item.query?.option?.method === 'GET') {
            res = await globalRequestUrlApi(params, item.query?.option?.method!, item.query?.option?.uri!)
          } else {
            res = await globalRequestApi(params, item.query?.option?.method!, item.query?.option?.uri!)
          }
          const { data } = res
          item.query!.option!.data = data || []
        }
      }
    }
    //遍历dataColumn ，用sortNo从小到大进行排序
    dataColumn.sort((a, b) => {
      return a.sortNo - b.sortNo
    })
  }

  return dataColumn
}

export async function getLocalData(item: TableItem, formData: Object, _key: string, remoteQuery: Object) {
  return new Promise(async (resolve, reject) => {
    // 封装下拉请求参数
    let params: any = {}
    // 下拉请求参数，配置dependon，需要遍历params进行参数替换（$#$#），否则直接取值
    if (item.form?.option?.dependOn) {
      for (const key in item.form.option.params) {
        composeOldFormParams(params, key, item, null, selectRows.value[0], formData)
      }
    } else {
      params = item.form?.option?.params ?? {}
    }
    item.form!.option.multiParams?.map((_item) => {
      composeRequestParams(params, _item, null, sideBarStore.$state.storeDialogFormParams, selectRows.value[0], null, formData)
    })
    // 拼接storeData参数
    if (item.form?.option?.dependFrom === 'storeData') {
      params = Object.assign(params, sideBarStore.$state.storeDialogFormParams)
    }
    // 根据 item.form?.option?.uri 和 params， 生成唯一key，缓存getFormItemSelectOptionsApi的返回结果
    const key = item.form?.option?.uri + JSON.stringify(params)
    var localData: any = {}
    //如果item.form.option没有cache属性，获取有这个属性并且值为true,并且_key不存在（表示远程模糊搜索，不需要缓存）
    if (item.form!.option?.cache !== false && !_key) {
      for (let localMapKey in localMap) {
        if (localMapKey === key) {
          localData = localMap[key]
          break
        }
      }
    }
    const mergeParams = Object.assign(params, remoteQuery)
    if (JSON.stringify(localData) === '{}') {
      let res = null
      if (!item.form?.option?.method || item.form?.option?.method === 'get' || item.form?.option?.method === 'GET') {
        res = await globalRequestUrlApi(mergeParams, item.form?.option?.method!, item.form?.option?.uri!)
      } else {
        res = await globalRequestApi(mergeParams, item.form?.option?.method!, item.form?.option?.uri!)
      }
      const { data } = res
      localMap[key] = data || {}
      localData = data || {}
    }
    //如果item.form?.option.defaultUri存在，获取默认值
    nextTick(async () => {
      if (item.form?.option!.defaultUrl) {
        let res = null
        if (!item.form?.option?.method || item.form?.option?.method === 'get' || item.form?.option?.method === 'GET') {
          res = await globalRequestUrlApi(params, item.form?.option?.method!, item.form?.option?.defaultUrl!)
        } else {
          res = await globalRequestApi(params, item.form?.option?.method!, item.form?.option?.defaultUrl!)
        }
        if (res.data) {
          const { label, value } = res.data as { label: keyof typeof formData; value: any }
          formData[label] = value
        }
      }
    })
    resolve({ localData, mergeParams })
  })
}
export async function getCustomSelectGroupLocalData(item: SelectGroupItem, formData: Object, _key: string, remoteQuery: Object) {
  return new Promise(async (resolve, reject) => {
    let res = null
    if (!item.option?.method || item.option?.method === 'get' || item.option?.method === 'GET') {
      res = await globalRequestUrlApi(remoteQuery, item.option?.method!, item.option?.uri!)
    } else {
      res = await globalRequestApi(remoteQuery, item.option?.method!, item.option?.uri!)
    }
    const { data } = res
    //如果item.form?.option.uri存在，获取默认值
    nextTick(async () => {
      if (item.option!.uri) {
        let res = null
        if (!item.option?.method || item.option?.method === 'get' || item.option?.method === 'GET') {
          res = await globalRequestUrlApi(remoteQuery, item.option?.method!, item.option?.uri!)
        } else {
          res = await globalRequestApi(remoteQuery, item.option?.method!, item.option?.uri!)
        }
        if (res.data) {
          const { label, value } = res.data as { label: keyof typeof formData; value: any }
          formData[label] = value
        }
      }
    })
    console.log('%c [ data ] -> ', 'font-size:16px; background:#991c1c; color:#dd6060;', data)
    resolve({ data })
  })
}
async function getQueryLocalData(item: TableItem, formData: Object, _key: string, remoteQuery: Object) {
  return new Promise(async (resolve, reject) => {
    // 封装下拉请求参数
    let params: any = {}
    // 下拉请求参数，配置dependon，需要遍历params进行参数替换（$#$#），否则直接取值
    if (item.query?.option?.dependOn) {
      for (const key in item.query.option.params) {
        composeOldFormParams(params, key, item, null, selectRows.value[0], formData)
      }
    } else {
      params = item.query?.option?.params ?? {}
    }
    item.query!.option.multiParams?.map((_item) => {
      composeRequestParams(params, _item, null, sideBarStore.$state.storeDialogFormParams, selectRows.value[0], null, formData)
    })
    // 拼接storeData参数
    if (item.query?.option?.dependFrom === 'storeData') {
      params = Object.assign(params, sideBarStore.$state.storeDialogFormParams)
    }
    // 根据 item.form?.option?.uri 和 params， 生成唯一key，缓存getFormItemSelectOptionsApi的返回结果
    const key = item.query?.option?.uri + JSON.stringify(params)
    var localData: any = {}
    //如果item.form.option没有cache属性，获取有这个属性并且值为true,并且_key不存在（表示远程模糊搜索，不需要缓存）
    if (item.query!.option?.cache !== false && !_key) {
      for (let localMapKey in localMap) {
        if (localMapKey === key) {
          localData = localMap[key]
          break
        }
      }
    }
    const mergeParams = Object.assign(params, remoteQuery)
    if (JSON.stringify(localData) === '{}') {
      let res = null
      if (!item.query?.option?.method || item.query?.option?.method === 'get' || item.query?.option?.method === 'GET') {
        res = await globalRequestUrlApi(mergeParams, item.query?.option?.method!, item.query?.option?.uri!)
      } else {
        res = await globalRequestApi(mergeParams, item.query?.option?.method!, item.query?.option?.uri!)
      }
      const { data } = res
      localMap[key] = data || {}
      localData = data || {}
    }
    resolve({ localData, mergeParams })
  })
}
export function operatorCalculate(target: any, when: any, operator: string): boolean {
  if (!operator) {
    operator = 'eq'
  }
  if (operator === 'in') {
    return when.includes(target)
  } else if (operator === 'nin') {
    return !when.includes(target)
  } else if (operator === 'eq') {
    return target === when
  } else if (operator === 'neq') {
    return target !== when
  } else if (operator === 'greate' || operator === 'great') {
    return target > when
  } else if (operator === 'greatEq') {
    if (target === null || target === undefined) {
      return false
    }
    return target >= when
  } else if (operator === 'less') {
    return target < when
  } else if (operator === 'lessEq') {
    return target <= when
  } else if (operator === 'bool') {
    if (when === 'true') {
      return target
    } else if (when === 'false') {
      return !target
    }
  } else if (operator === 'isNull') {
    return target === null || target === undefined || target === '' || target === 'null' || target === 'undefined'
  } else if (operator === 'isNotNull') {
    return target !== null && target !== undefined && target !== '' && target !== 'null' && target !== 'undefined'
  }
  return target === when
}
export function composeCustomSelectGroupOldFormParams(resultData: any, key: any, item: any, storeData: any, rowData: any, formData: any): void {
  if (!item.option || !item.option.params) {
    return
  }
  if (item.option.params[key] === '$#$#') {
    //则取items第0项选中的值
  } else {
    //否则直接赋值
    resultData[key] = item.option.params[key]
  }
}
export function composeOldFormParams(resultData: any, key: any, item: any, storeData: any, rowData: any, formData: any): void {
  if (!item.form || !item.form.option) {
    return
  }
  if (item.form.option.params[key] === '$#$#') {
    if (item.form.option.dependFrom === 'listData') {
      //如果是多选，则params的值需要转换成字符串
      if (Array.isArray(rowData[item.form.option.dependOn!])) {
        resultData[key] = rowData[item.form.option.dependOn!].join(',')
      } else {
        resultData[key] = rowData[item.form.option.dependOn!]
      }
    } else if (item.form?.option.dependFrom === 'currentForm') {
      //如果是多选，则params的值需要转换成字符串
      if (Array.isArray(formData[item.form.option.dependOn!])) {
        resultData[key] = formData[item.form.option.dependOn!].join(',')
      } else {
        resultData[key] = formData[item.form.option.dependOn!]
      }
    } else if (item.form?.option.dependFrom === 'parentListData') {
      selectLeftTreeRows.value.map((_item, _index) => {
        if (_item.menuId === sideBarStore.$state.menuId) {
          resultData[key] = _item.selectLeftTreeRow[item.form!.option!.dependOn!]
        }
      })
    }
  } else {
    //否则直接赋值
    resultData[key] = item.form.option.params[key]
  }
}

export function composeRequestParams(resultData: any, item: any, menu: any, storeData: any, rowData: any, queryData: any, formData?: any): void {
  if (item?.value === '$#$#') {
    if (menu && menu.meta?.form?.dependFrom === 'listData') {
      resultData[item.targetName!] = rowData?.[item.name!]
    }
    if (storeData && item.dependFrom === 'storeData') {
      resultData[item.targetName!] = storeData[item.name!]
    } else if (rowData && rowData[item.name!] && item.dependFrom === 'listData') {
      if (Array.isArray(rowData[item.name!])) {
        resultData[item.targetName!] = rowData[item.name!].join(',')
      } else {
        resultData[item.targetName!] = rowData[item.name!]
      }
    } else if (queryData && item.dependFrom === 'queryForm') {
      resultData[item.targetName!] = queryData[item.name!]
    } else if (formData && formData[item.name!] && item.dependFrom === 'currentForm') {
      if (Array.isArray(formData[item.name!])) {
        resultData[item.targetName!] = formData[item.name!].join(',')
      } else {
        resultData[item.targetName!] = formData[item.name!]
      }
    } else if (item.dependFrom === 'parentListData') {
      selectLeftTreeRows.value.map((__item, _index) => {
        if (__item.menuId === sideBarStore.$state.menuId) {
          resultData[item.targetName!] = __item.selectLeftTreeRow[item.name!]
        }
      })
    }
  } else {
    resultData[item.targetName!] = item.value
  }
}
//重新计算，没有参数，默认获取顶部搜索条件作为参数
export function composeRequestQueryParams(resultData: any, item: any, menu: any, storeData: any, rowData: any, queryData: any, formData?: any): void {
  for (const key in queryData) {
    if (Object.prototype.hasOwnProperty.call(queryData, key)) {
      if (key !== 'page') {
        const element = queryData[key]
        resultData[key] = element
      }
    }
  }
}

export function composeRequestParamsMultiRow(resultData: any, item: FormRequestParamsVO, menu: any, storeData: any, rowDatas: any[], ids: Array<string>): void {
  if (item.name === 'ids') {
    resultData[item.name!] = ids
  } else {
    if (item.value === '$#$#') {
      if (menu && menu.meta?.form?.dependFrom === 'listData') {
        resultData[item.targetName!] = rowDatas.map((_item) => {
          return _item[item.name!]
        })
      }
      if (storeData && item.dependFrom === 'storeData') {
        resultData[item.targetName!] = storeData[item.name!]
      } else if (rowDatas && item.dependFrom === 'listData') {
        resultData[item.targetName!] = rowDatas.map((_item) => {
          return _item[item.name!]
        })
      }
    } else {
      resultData[item.targetName!] = item.value
    }
  }
}
