/*
 * @Author: llm
 * @Date: 2023-07-08 16:58:49
 * @LastEditors: llm
 * @LastEditTime: 2025-05-27 20:24:44
 * @Description: 工具方法
 *
 */
import xian from '@/assets/xian.png'
import blueXian from '@/assets/blue_xian.png'
import blue1Xian from '@/assets/blue1_xian.png'
import greenXian from '@/assets/green_xian.png'
import green1Xian from '@/assets/green1_xian.png'
import orangeXian from '@/assets/orange_xian.png'
import pinkXian from '@/assets/pink_xian.png'
import purpleXian from '@/assets/purple_xian.png'
import purple1Xian from '@/assets/purple1_xian.png'
import redXian from '@/assets/red_xian.png'
import yellowXian from '@/assets/yellow_xian.png'
import { number } from 'echarts'
/**
 * Check if an element has a class
 * @param {HTMLElement} ele
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele: HTMLElement, cls: string) {
  return !!ele.className.match(new RegExp(`(\\s|^)${cls}(\\s|$)`))
}

/**
 * Add class to element
 * @param {HTMLElement} ele
 * @param {string} cls
 */
export function addClass(ele: HTMLElement, cls: string) {
  if (!hasClass(ele, cls)) ele.className += ` ${cls}`
}

/**
 * Remove class from element
 * @param {HTMLElement} ele
 * @param {string} cls
 */
export function removeClass(ele: HTMLElement, cls: string) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp(`(\\s|^)${cls}(\\s|$)`)
    ele.className = ele.className.replace(reg, ' ')
  }
}

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path: string) {
  const isExternal = /^(https?:|http?:|mailto:|tel:)/.test(path)
  return isExternal
}
/**
 * 类型转换
 * type 1菜单 2按钮 3tab 4目录
 */
export function typeConversion(type: number | undefined) {
  switch (type) {
    case 1:
      return '菜单'
      break
    case 2:
      return '按钮'
      break
    case 3:
      return 'tab'
      break
    case 4:
      return '目录'
      break
    default:
      break
  }
}
/**
 * @description: 随机色
 * @return {*}
 */
export function color16() {
  const colors = [
    '#994499',
    '#109618',
    '#ff9900',
    '#990099',
    '#0099c6',
    '#dd4477',
    '#66aa00',
    '#316395',
    '#994499',
    '#22aa99',
    '#aaaa11',
    '#6633cc',
    '#e67300',
    '#651067',
    '#329262',
    '#5574a6',
    '#3b3eac',
    '#3b3eac',
  ]
  const randomNum = Math.floor(Math.random() * colors.length)
  return colors[randomNum]
}
/**
 * @description: 随机色
 * @return {*}
 */
export function colorRoute() {
  const colorsXian = [xian, blueXian, blue1Xian, greenXian, green1Xian, orangeXian, pinkXian, purpleXian, purple1Xian, redXian, yellowXian]
  const randomNum = Math.floor(Math.random() * colorsXian.length)
  return colorsXian[randomNum]
}
/**
 * @description: 国测局02坐标转百度坐标
 * @param {string}
 * @returns {Object}
 */
export function gcj02tobd09(gg_lon: any, gg_lat: any) {
  var X_PI = (Math.PI * 3000.0) / 180.0
  var x = gg_lon,
    y = gg_lat
  var z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * X_PI)
  var theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * X_PI)
  var bd_lon = z * Math.cos(theta) + 0.0065
  var bd_lat = z * Math.sin(theta) + 0.006
  return [bd_lon.toFixed(6), bd_lat.toFixed(6)]
}
/**
 * @param {string} 百度坐标转国测局02坐标
 * @returns {Object}
 */
export function bd09togcj02(bd_lon: any, bd_lat: any) {
  var x_pi = (3.14159265358979324 * 3000.0) / 180.0
  var x = bd_lon - 0.0065
  var y = bd_lat - 0.006
  var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi)
  var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi)
  var gg_lng = z * Math.cos(theta)
  var gg_lat = z * Math.sin(theta)
  return [gg_lng.toFixed(6), gg_lat.toFixed(6)]
}
//百度坐标转高德（传入经度、纬度）
export function bd_decrypt(bd_lng: any, bd_lat: any) {
  var X_PI = (Math.PI * 3000.0) / 180.0
  var x = bd_lng - 0.0065
  var y = bd_lat - 0.006
  var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * X_PI)
  var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI)
  var gg_lng = z * Math.cos(theta)
  var gg_lat = z * Math.sin(theta)
  return { lng: gg_lng, lat: gg_lat }
}
//高德坐标转百度（传入经度、纬度）
export function bd_encrypt(gg_lng: any, gg_lat: any) {
  var X_PI = (Math.PI * 3000.0) / 180.0
  var x = gg_lng,
    y = gg_lat
  var z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * X_PI)
  var theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * X_PI)
  var bd_lng = z * Math.cos(theta) + 0.0065
  var bd_lat = z * Math.sin(theta) + 0.006
  return {
    bd_lat: bd_lat,
    bd_lng: bd_lng,
  }
}
/**
 * @description: 获取多边形的中心点坐标
 * @param lnglatarr 多边形坐标点arr ['116.384336,39.914966', '116.403488,39.913863', '116.393478,39.908956', '116.379776,39.909839', '116.384336,39.914966']
 * @return {*} [116.3890827841781,39.9125183079364]
 */
export function calculateCenter(lnglatarr: any[]) {
  var total = lnglatarr.length
  var X = 0,
    Y = 0,
    Z = 0
  lnglatarr.forEach(function (lnglat: { split: (arg0: string) => number[] }) {
    var lng = (lnglat.split(',')[0] * Math.PI) / 180
    var lat = (lnglat.split(',')[1] * Math.PI) / 180
    var x, y, z
    x = Math.cos(lat) * Math.cos(lng)
    y = Math.cos(lat) * Math.sin(lng)
    z = Math.sin(lat)
    X += x
    Y += y
    Z += z
  })
  X = X / total
  Y = Y / total
  Z = Z / total

  var Lng = Math.atan2(Y, X)
  var Hyp = Math.sqrt(X * X + Y * Y)
  var Lat = Math.atan2(Z, Hyp)
  return {
    lng: ((Lng * 180) / Math.PI).toFixed(6),
    lat: ((Lat * 180) / Math.PI).toFixed(6),
  }
}
// 获取图片转base64
export function getBase64(file: any) {
  return new Promise(function (resolve, reject) {
    const reader = new FileReader()
    let imgResult: any = ''
    reader.readAsDataURL(file)
    reader.onload = function () {
      imgResult = reader.result
    }
    reader.onerror = function (error) {
      reject(error)
    }
    reader.onloadend = function () {
      resolve(imgResult)
    }
  })
}

// 计算图表Y轴最大值
export function calMax(arr: any) {
  const maxval = ref(0) //最大值
  let max = Math.max(...arr)
  if (max) {
    let maxint = Math.ceil(max / 9) // 不让最高的值超过最上面的刻度
    maxval.value = maxint * 10 // 让显示的刻度是整数
    // 为了防止Y轴分不开，不显示，小于5的给个最大值5
    if (maxval.value < 5) {
      maxval.value = 5
    }
  } else {
    maxval.value = 5
  }
  return maxval
}

// 计算图表Y轴最大值
export function calMaxNumber(maxNumber: any) {
  const maxval = ref(0) //最大值
  if (maxNumber) {
    // 为了防止Y轴分不开，不显示，小于5的给个最大值
    if (maxNumber < 5) {
      maxval.value = 4 // 极小值兜底处理[^3]
    } else if (maxNumber < 100) {
      maxval.value = Math.ceil(maxNumber)
    } else {
      maxval.value = Math.ceil(maxNumber / 10000) * 10000
    }
  } else {
    maxval.value = 4
  }
  return maxval.value
}

// 动态设置纵坐标轴单位
export function setYName(arr: any, str: string) {
  const max = Math.max(...arr)
  if (max >= 0 && max < 1000) {
    return str
  } else if (max >= 1000 && max < 10000) {
    return '千' + str
  } else if (max >= 10000) {
    return '万' + str
  }
}

// 计算最小值
export function calMin(arr: any) {
  const min = Math.min(...arr)
  const minint = Math.floor(min / 10)
  const minval = minint * 10 // 让显示的刻度是整数
  return minval
}

// 当月的月份
export function getNowMonth() {
  var date = new Date()
  var year = date.getFullYear()
  var month = date.getMonth() + 1
  var month2 = month > 9 ? month : '0' + month
  var nowMonth = year + '-' + month2
  return nowMonth
}

// 获取上月月份
export function getPreMonth() {
  var currentDate = new Date()
  var currentYear = currentDate.getFullYear()
  var currentMonth = currentDate.getMonth()
  var prevYear, prevMonth
  if (currentMonth === 0) {
    // 一月份情况
    prevYear = currentYear - 1
    prevMonth = 12
  } else {
    prevYear = currentYear
    prevMonth = currentMonth
  }
  var newMonth = currentMonth > 9 ? currentMonth : '0' + currentMonth
  var yearMonth = prevYear + '-' + newMonth
  return yearMonth
}
//获取当前日期，年月日时分秒，<10的前面拼0
export function getNowDate() {
  const date = new Date()
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const month2 = month > 9 ? month : '0' + month
  const day = date.getDate()
  const day2 = day > 9 ? day : '0' + day
  const hour = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  const minute = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  const second = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return `${year}-${month2}-${day2} ${hour}:${minute}:${second}`
}
//获取当前日期，年月日
export function getNowDateYMD() {
  const date = new Date()
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const month2 = month > 9 ? month : '0' + month
  const day = date.getDate()
  const day2 = day > 9 ? day : '0' + day
  return `${year}-${month2}-${day2}`
}
// 设置table高度为可视区域范围
export function getScollerHeight(height: number, paddingHeight: number) {
  const clientHeight = document.documentElement.clientHeight || document.body.clientHeight // 屏幕高度
  const topHeight = height // 上面内容区域高度
  // 100 是分页器高度；84 是顶部公共导航高度 ；90 是内边距
  const computedHeight = clientHeight - topHeight - (paddingHeight || paddingHeight == 0 ? paddingHeight : 90) - 84 - 100 + 'px'
  return computedHeight
}

// table column 自适应宽度
// 自适应表格列宽
export function flexColumnWidth(str: string, tableData: any[], flag = 'max') {
  // str为该列的字段名(传字符串);
  // tableData为该表格的数据源(传变量);
  // flag为可选值，可不传该参数,传参时可选'max'或'equal',默认为'max'
  // flag为'max'则设置列宽适配该列中最长的内容,flag为'equal'则设置列宽适配该列中第一行内容的长度。
  str = str + ''
  let columnContent = ''
  if (!tableData || !tableData.length || tableData.length === 0 || tableData === undefined) {
    return
  }
  if (!str || !str.length || str.length === 0 || str === undefined) {
    return
  }
  if (flag === 'equal') {
    // 获取该列中第一个不为空的数据(内容)
    for (let i = 0; i < tableData.length; i++) {
      if (tableData[i][str].length > 0) {
        columnContent = tableData[i][str]
        break
      }
    }
  } else {
    // 获取该列中最长的数据(内容)
    let index = 0
    for (let i = 0; i < tableData.length; i++) {
      if (tableData[i][str] === null) {
        return
      }
      const now_temp = tableData[i][str] + ''
      const max_temp = tableData[index][str] + ''
      if (now_temp.length > max_temp.length) {
        index = i
      }
    }
    columnContent = tableData[index][str]
  }
  // 以下分配的单位长度可根据实际需求进行调整
  let flexWidth = 0
  for (const char of columnContent) {
    //这里根据字体的大小来计算宽度，可根据实际需求进行调整
    //如果是纯中文flexWidth = 汉字数量 * 12
    if (char.charCodeAt(0) > 127 || char.charCodeAt(0) === 94) {
      flexWidth += 12
    } else {
      flexWidth += 6.5
    }
  }
  if (flexWidth < 80) {
    // 设置最小宽度
    flexWidth = 80
  }
  // if (flexWidth > 250) {
  //   // 设置最大宽度
  //   flexWidth = 250
  // }
  return flexWidth + 'px'
}
export const ShortcutsList = [
  //今日
  {
    text: '今日',
    value: () => {
      const end = new Date()
      const start = new Date()
      // 设置开始时间为00:00:00
      start.setHours(0, 0, 0, 0)
      // 设置结束时间为23:59:59
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  //昨日
  {
    text: '昨日',
    value: () => {
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24)
      // 设置开始时间为00:00:00
      start.setHours(0, 0, 0, 0)
      const end = new Date(start)
      // 设置结束时间为23:59:59
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  //本周
  {
    text: '本周',
    value: () => {
      const end = new Date()
      const start = new Date()
      // 设置到本周一
      const dayOfWeek = start.getDay()
      const diff = dayOfWeek === 0 ? -6 : 1 - dayOfWeek // 如果是周日，往前推6天；否则推到周一
      start.setDate(start.getDate() + diff)
      // 设置开始时间为00:00:00
      start.setHours(0, 0, 0, 0)
      // 设置结束时间为23:59:59
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  //上周
  {
    text: '上周',
    value: () => {
      const start = new Date()
      // 设置到本周一
      const dayOfWeek = start.getDay()
      const diff = dayOfWeek === 0 ? -6 : 1 - dayOfWeek
      start.setDate(start.getDate() + diff)
      // 再往前推一周到上周一
      start.setDate(start.getDate() - 7)
      // 设置开始时间为00:00:00
      start.setHours(0, 0, 0, 0)
      // end设置为上周日
      const end = new Date(start)
      end.setDate(start.getDate() + 6)
      // 设置结束时间为23:59:59
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  //最近7日
  {
    text: '最近7日',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 6) // 改为-6，包含今天共7天
      // 设置开始时间为00:00:00
      start.setHours(0, 0, 0, 0)
      // 设置结束时间为23:59:59
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  //当前月
  {
    text: '当前月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(1)
      // 设置开始时间为00:00:00
      start.setHours(0, 0, 0, 0)
      // 设置结束时间为23:59:59
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  //最近30天
  {
    text: '最近30天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 29) // 改为-29，包含今天共30天
      // 设置开始时间为00:00:00
      start.setHours(0, 0, 0, 0)
      // 设置结束时间为23:59:59
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      // 设置开始时间为00:00:00
      start.setHours(0, 0, 0, 0)
      // 设置结束时间为23:59:59
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  //最近半年
  {
    text: '最近半年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 6)
      // 设置开始时间为00:00:00
      start.setHours(0, 0, 0, 0)
      // 设置结束时间为23:59:59
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  //最近一年
  {
    text: '最近一年',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setFullYear(start.getFullYear() - 1)
      // 设置开始时间为00:00:00
      start.setHours(0, 0, 0, 0)
      // 设置结束时间为23:59:59
      end.setHours(23, 59, 59, 999)
      return [start, end]
    },
  },
  //长期
  // {
  //   text: '长期',
  //   value: () => {
  //     const start = new Date('9999-12-30')
  //     const end = new Date('9999-12-31')
  //     return [start, end]
  //   },
  // },
]
/**
 * Set the viewport of the map to include all specified markers.
 *
 * @param {Array} markers - Array of marker objects, each containing coordinates.
 * @param {string} coordinate - Key to access the coordinate string from each marker object.
 * @param {Object} _map - Baidu Map instance on which the viewport will be set.
 * @param {Object} _BMap - Baidu Map API object.
 */
export const setBdMapViewPort = (markers: any[], coordinate: string, _map: any, _BMap: any) => {
  // Array to store Baidu map points
  let points: any[] = []

  // Iterate over each marker and convert coordinates to Baidu map Points
  markers.map((item) => {
    const coords = item[coordinate].split(',')
    const bd_point = new _BMap.Point(Number(coords[0]), Number(coords[1]))
    points.push(bd_point)
  })

  // Set the map viewport to include all points
  _map.setViewport(points)
}
// 表头部重新渲染
export function renderHeader({ column, $index }: any) {
  // 新建一个 span
  const span = document.createElement('span')
  // 设置表头名称
  span.innerText = column.label
  // 临时插入 document
  document.body.appendChild(span)
  // 重点：获取 span 最小宽度，设置当前列，注意这里加了 20，字段较多时还是有挤压，且渲染后的 div 内左右 padding 都是 10，所以 +20 。（可能还有边距/边框等值，需要根据实际情况加上）
  column.minWidth = span.getBoundingClientRect().width + 30
  // 移除 document 中临时的 span
  document.body.removeChild(span)
  return column.label
}
// 定义文件类型接口
interface FileType {
  type: string
  extensions: string[]
}

// 定义文件类型配置
const FILE_TYPES: FileType[] = [
  {
    type: 'image',
    extensions: ['jpg', 'png', 'jpeg', 'image'],
  },
  {
    type: 'document',
    extensions: ['pdf', 'xls', 'xlsx'],
  },
  {
    type: 'video',
    extensions: ['mp4'],
  },
]

// 检查文件类型是否匹配
export const isFileType = (file: { type: string }, targetType: string): boolean => {
  const typeConfig = FILE_TYPES.find((config) => config.type === targetType)
  return typeConfig ? typeConfig.extensions.includes(file?.type?.toLowerCase() || '') : false
}
