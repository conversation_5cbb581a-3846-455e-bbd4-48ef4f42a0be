/*
 * @Author: llm
 * @Date: 2025-01-08
 * @LastEditors: llm
 * @LastEditTime: 2025-01-08
 * @Description: 快速修复 Tooltip 层级问题的一键解决方案
 */

/**
 * 一键修复所有 tooltip 层级问题
 * 这是最简单直接的解决方案
 */
export function quickFixTooltip() {
  const ULTRA_HIGH_Z_INDEX = 999999

  // 1. 添加全局 CSS 样式
  const addGlobalCSS = () => {
    const styleId = 'quick-tooltip-fix'
    let style = document.getElementById(styleId) as HTMLStyleElement

    if (!style) {
      style = document.createElement('style')
      style.id = styleId
      document.head.appendChild(style)
    }

    style.textContent = `
      /* 一键修复 tooltip 层级 */
      .el-tooltip__popper,
      .el-tooltip__popper[data-popper-placement],
      div[role="tooltip"].el-tooltip__popper,
      [class*="el-tooltip__popper"],
      .smart-tooltip-high-z-index {
        z-index: ${ULTRA_HIGH_Z_INDEX} !important;
      }

      /* 确保在任何容器中都有最高层级 */
      body .el-tooltip__popper,
      .el-dialog .el-tooltip__popper,
      .el-drawer .el-tooltip__popper,
      .vxe-table .el-tooltip__popper,
      .el-overlay .el-tooltip__popper {
        z-index: ${ULTRA_HIGH_Z_INDEX} !important;
      }
    `
  }

  // 2. 强制修复现有 tooltip
  const fixExistingTooltips = () => {
    const tooltips = document.querySelectorAll('.el-tooltip__popper, [role="tooltip"]')
    tooltips.forEach(tooltip => {
      if (tooltip instanceof HTMLElement) {
        tooltip.style.setProperty('z-index', ULTRA_HIGH_Z_INDEX.toString(), 'important')
      }
    })
  }

  // 3. 监听新 tooltip 的创建
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node instanceof Element) {
          // 检查是否是 tooltip
          if (node.matches('.el-tooltip__popper, [role="tooltip"]')) {
            (node as HTMLElement).style.setProperty('z-index', ULTRA_HIGH_Z_INDEX.toString(), 'important')
          }

          // 检查子元素中的 tooltip
          const tooltips = node.querySelectorAll('.el-tooltip__popper, [role="tooltip"]')
          tooltips.forEach(tooltip => {
            (tooltip as HTMLElement).style.setProperty('z-index', ULTRA_HIGH_Z_INDEX.toString(), 'important')
          })
        }
      })
    })
  })

  // 4. 执行修复
  addGlobalCSS()
  fixExistingTooltips()

  observer.observe(document.body, {
    childList: true,
    subtree: true
  })

  // 5. 定期检查（保险措施）
  const intervalId = setInterval(fixExistingTooltips, 1000)

  console.log('🚀 一键修复 Tooltip 层级问题已启动')
  console.log(`📊 使用 z-index: ${ULTRA_HIGH_Z_INDEX}`)

  return {
    observer,
    intervalId,
    stop: () => {
      observer.disconnect()
      clearInterval(intervalId)
      console.log('⏹️ Tooltip 修复已停止')
    }
  }
}

// 不自动执行修复，只提供手动调用接口
if (typeof window !== 'undefined') {
  // 添加到 window 对象供手动调用
  (window as any).quickFixTooltip = quickFixTooltip
}
