/*
 * @Author: llm
 * @Date: 2023-05-22 07:59:23
 * @LastEditors: llm
 * @LastEditTime: 2025-07-29 15:37:08
 * @Description: 请求拦截器
 *
 */
import axios, { InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { useUserStoreHook } from '@/store/modules/user'
import { Action } from 'element-plus'
import router from '@/router'
// 移除导入语句
// import { updateActivity, initTokenManager } from '@/utils/tokenManager'

// 移除初始化调用
// initTokenManager()

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 120 * 1000,
  headers: { 'Content-Type': 'application/json;charset=utf-8' },
})

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const userStore = useUserStoreHook()

    // 移除活动时间更新
    // updateActivity()

    if (userStore.TOKEN) {
      config.headers.Authorization = userStore.TOKEN
      // config.headers.companyId = '1751174375105175552';
      // config.headers.userId = '1751174375201644544';
    } else {
      config.headers.companyId = '1'
      config.headers.userId = '1485429613204058112'
    }
    config.headers.systemType = userStore.getSystemType() ?? '2'
    return config
  },
  (error: any) => Promise.reject(error),
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const { code, message } = response.data
    // 响应数据为二进制流处理(Excel导出)
    if (response.data instanceof ArrayBuffer || response.data instanceof Blob) {
      try {
        // 尝试将 ArrayBuffer 转换为字符串
        const decoder = new TextDecoder('utf-8')
        const text = decoder.decode(new Uint8Array(response.data as ArrayBuffer))
        // 尝试解析为 JSON，解析成功拦截报错
        const jsonData = JSON.parse(text)
        if (jsonData.code && jsonData.message) {
          if (jsonData.code === 10070) {
            ElMessageBox.alert(jsonData.message, '提示', {
              confirmButtonText: '前往下载',
              cancelButtonText: '取消',
              showCancelButton: true,
              closeOnPressEscape: false,
              showClose: false,
              closeOnClickModal: false,
              center: true,
              callback: (action: Action) => {
                if (action === 'confirm') {
                  //跳转到数据导出管理
                  router.push('/systemConfiguration/dataExportManagement')
                } else {
                  return
                }
              },
            })
          } else {
            ElMessage.error({
              dangerouslyUseHTMLString: true,
              message: jsonData.message || '系统出错',
              type: 'error',
            })
          }

          return
        }
      } catch (e) {
        // 如果解析失败，说明是二进制文件，直接下载
        return response
      }
    }
    if (code === 200) {
      return response.data
    } else if (code === 401) {
      ElMessageBox.confirm(message, '提示', {
        confirmButtonText: '去登录',
        dangerouslyUseHTMLString: true,
        showCancelButton: false,
        closeOnPressEscape: false,
        showClose: false,
        closeOnClickModal: false,
        center: true,
        type: 'warning',
      }).then(() => {
        localStorage.clear()
        window.location.href = '/'
      })
    } else if (code === 219 || code === 220 || code === 201) {
      //219是手动分配的时候，有计划发生了更新
      //220是分配的时候有取消配板操作，需要刷新页面重新分配
      //201是导入文件后，成功弹窗提示
      return response.data
    } else if (code === 11010) {
      //高速费管理-司机导入，导入文件错误提示弹窗展示list
      return response.data
    } else if (code === 5001) {
      //文件导入失败弹窗
      return response.data
    } else {
      ElMessage.error({
        dangerouslyUseHTMLString: true,
        message: message || '系统出错',
        type: 'error',
      })
    }
    // ElMessage.error(message || "系统出错");
    return Promise.reject(new Error(message || 'Error'))
  },
  (error: any) => {
    return Promise.reject(error.message)
  },
)

// 导出 axios 实例
export default service
