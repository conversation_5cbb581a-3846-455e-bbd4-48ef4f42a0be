/*
 * @Date: 2022-04-11 15:23:28
 * @LastEditors: llm
 * @LastEditTime: 2025-02-27 12:17:53
 * @desc:所有下拉选项
 * @FilePath     : /src/utils/selectJson.js
 */

/**
 * @description: 自有/外协下拉
 * @return {*} options
 */
export function belongCompanyOptions() {
  return [
    {
      value: true,
      text: '自有',
    },
    {
      value: false,
      text: '外协',
    },
  ]
}

/**
 * @description: 正常/异常下拉
 * @return {*} options
 */
export function normalAnomalyOptions() {
  return [
    {
      value: false,
      text: '正常',
    },
    {
      value: true,
      text: '异常',
    },
  ]
}

/**
 * @description: 油气切换列表
 * @return {*} options
 */
export function oilAndGasOptions() {
  return [
    {
      value: 1,
      text: '元/L',
    },
    {
      value: 2,
      text: '元/m3',
    },
  ]
}

/**
 * @description: 任务执行状态下拉
 * @return {*} options
 */
export function taskStatusOptions(type) {
  return [
    {
      value: -1,
      text: '--',
    },
    {
      value: 0,
      text: '待执行',
    },
    {
      value: 1,
      text: '执行中',
    },
    {
      value: 2,
      text: '执行结束',
    },
    {
      value: -2,
      text: type == 'order' ? '订单作废' : '任务作废',
    },
  ]
}

/**
 * @description: 单双程类型
 * @return {*} options
 */
export function tripTypeOptions() {
  return [
    {
      value: 0,
      label: '--',
    },
    {
      value: 1,
      label: '单程',
    },
    {
      value: 2,
      label: '双程',
    },
  ]
}

/**
 * @description: 单双程类型 -- 查询用
 * @return {*} options
 */
export function queryTripTypeOptions() {
  return [
    {
      value: null,
      label: '全部',
    },
    {
      value: 1,
      label: '单程',
    },
    {
      value: 2,
      label: '双程',
    },
  ]
}

/**
 * @description: 设备状态
 * @return {*} options
 */
export function obdTypeOptions() {
  return [
    {
      value: 0,
      label: '离线',
    },
    {
      value: 1,
      label: '静止',
    },
    {
      value: 2,
      label: '休眠',
    },
  ]
}

/**
 * @description: 指令状态
 * @return {*} options
 */
export function obdInstructOptions() {
  return [
    {
      value: 0,
      label: '超速设置',
    },
    {
      value: 1,
      label: '调度下发',
    },
    {
      value: 2,
      label: '无限回传',
    },
  ]
}

/**
 * @description: 排序类型
 * @return {*} options
 */
export function orderByColumnOptions() {
  return [
    {
      value: 'create_time',
      label: '创建时间',
    },
    {
      value: 'delivery_date',
      label: '送货日期',
    },
    {
      value: 'take_date',
      label: '提货日期',
    },
  ]
}
/**
 * @description: 排序类型2
 * @return {*} options
 */
export function sortingTypeOptions() {
  return [
    {
      value: 'task_no',
      label: '运单号',
    },
    {
      value: 'task_create_time',
      label: '开单时间',
    },
    {
      value: 'take_date',
      label: '提货日期',
    },
  ]
}
/**
 * @description: 排序方式
 * @return {*} options
 */
export function orderBySortOptions() {
  return [
    {
      value: 'asc',
      label: '升序',
    },
    {
      value: 'desc',
      label: '降序',
    },
  ]
}
/**
 * @description: 是否含有客户订单号选择
 * @return {*} options
 */
export function containOrderOptions() {
  return [
    {
      value: null,
      label: '全部',
    },
    {
      value: false,
      label: '是',
    },
    {
      value: true,
      label: '否',
    },
  ]
}
/**
 * @description: 是否含有事故
 * @return {*} options
 */
export function accidentOptions() {
  return [
    {
      value: '-1',
      label: '全部',
    },
    {
      value: '0',
      label: '无事故',
    },
    {
      value: '1',
      label: '有事故',
    },
  ]
}
/**
 * @description: 是否含有罚款
 * @return {*} options
 */
export function forfeitOptions() {
  return [
    {
      value: '-1',
      label: '全部',
    },
    {
      value: '0',
      label: '无罚款',
    },
    {
      value: '1',
      label: '有罚款',
    },
  ]
}

/**
 * @description: 订单类型
 * @return {*} options
 */
export function orderTypeOptions() {
  return [
    {
      value: 0,
      text: '任务',
    },
    {
      value: 1,
      text: '业务',
    },
  ]
}

/**
 * @description: 人员设定表头项类型
 * @return {*} options
 */
export function tableHeaderType() {
  return [
    {
      value: 1,
      text: '固定收入',
    },
    {
      value: 2,
      text: '变动收入',
    },
    {
      value: 3,
      text: '其他收入',
    },
    {
      value: 4,
      text: '固定扣除',
    },
    {
      value: 5,
      text: '变动扣除',
    },
    // {
    //   value: 6,
    //   text: '公司负担',
    // },
    {
      value: 7,
      text: '其他扣除',
    },
    {
      value: 8,
      text: '自动计算项',
    },
    {
      value: 9,
      text: '基础项',
    },
    {
      value: 10,
      text: '数据统计项',
    },
    // {
    //   value: 11,
    //   text: '数据统计定义项',
    // },
  ]
}

/**
 * @description: 货量单位
 * @return {*} options
 */
export function getGoodsQuantityUnitOptions() {
  const options = [
    {
      value: 1,
      text: '车',
    },
    {
      value: 2,
      text: '托盘',
    },
    {
      value: 3,
      text: '吨',
    },
    {
      value: 4,
      text: 'm³',
    },
    {
      value: 5,
      text: '桶',
    },
    {
      value: 6,
      text: 'L',
    },
    {
      value: 7,
      text: '箱',
    },
    {
      value: 8,
      text: '铁架',
    },
    {
      value: 9,
      text: '铁框',
    },
    {
      value: 10,
      text: '其他',
    },
  ]
  return options
}

/**
 * @description: 货量单位
 * @return {*} options
 */
export function getUnitList() {
  const options = [
    {
      value: 1,
      text: '元/车',
    },
    {
      value: 3,
      text: '元/吨',
    },
    {
      value: 4,
      text: '元/m³',
    },
    {
      value: 7,
      text: '元/柜',
    },
  ]
  return options
}

/**
 * @description: 补助基准
 * @return {*} options
 */
export function getBasisOfSubsidyOptions() {
  return [
    {
      value: 1,
      text: '按天',
    },
    {
      value: 2,
      text: '按小时',
    },
    {
      value: 3,
      text: '按半小时',
    },
    {
      value: 4,
      text: '按次',
    },
    {
      value: 5,
      text: '公里',
    },
    {
      value: 6,
      text: '按升',
    },
  ]
}

/**
 * @description: 补助方式
 * @return {*} options
 */
export function getStandardOfSubsidyOptions() {
  return [
    {
      value: 1,
      text: '夜班补贴',
    },
    {
      value: 2,
      text: '延时补贴',
    },
    {
      value: 3,
      text: '周末加班补贴',
    },
    {
      value: 4,
      text: '节假日加班补贴',
    },
    {
      value: 5,
      text: '连班补贴',
    },
    {
      value: 6,
      text: '里程补贴',
    },
    {
      value: 7,
      text: '高速补贴',
    },
    {
      value: 8,
      text: '油费补贴',
    },
    {
      value: 9,
      text: '长途补贴',
    },
    {
      value: 10,
      text: '其他补贴',
    },
    {
      value: 11,
      text: '趟数补贴',
    },
  ]
}

/**
 * @description: 业务时间
 * @return {*} options
 */
export function getBusinessTimeOptions() {
  return [
    {
      value: 1,
      text: '白班',
    },
    {
      value: 2,
      text: '夜班',
    },
  ]
}

/**
 * @description: 车辆状态
 * @return {*} options
 */
export function getVehicleStatusOptions() {
  const options = [
    {
      value: '',
      text: '全部状态',
    },
    {
      value: 0,
      text: '修车中',
    },
    {
      value: 1,
      text: '导航中',
    },
    {
      value: 2,
      text: '停车中',
    },
    {
      value: 3,
      text: '行驶中',
    },
    {
      value: 4,
      text: '北斗离线',
    },
    {
      value: 5,
      text: '疑似黑车',
    },
  ]
  return options
}

/**
 * @description: 用户策略
 * @return {*} options
 */
export function getStrategyOptions() {
  const options = [
    {
      value: 1,
      text: '时间优先',
    },
    {
      value: 2,
      text: '费用优先',
    },
  ]
  return options
}

/**
 * @description: 车牌颜色
 * @return {*} options
 */
export function getPlateColorOptions() {
  const options = [
    {
      value: 1,
      text: '蓝牌',
    },
    {
      value: 2,
      text: '黄牌',
    },
    {
      value: 3,
      text: '绿牌',
    },
  ]
  return options
}

/**
 * @description: 车型
 * @return {*} options
 */
export function getVehicleTypeOptions() {
  const options = [
    // {
    //   value: 1,
    //   text: "微型货车",
    // },
    {
      value: 2,
      text: '轻型货车',
    },
    {
      value: 3,
      text: '中型货车',
    },
    {
      value: 4,
      text: '重型货车',
    },
  ]
  return options
}

/**
 * @description: 排放标准
 * @return {*} options
 */
export function getEmissionStandardOptions() {
  const options = [
    { value: 4, text: '国四' },
    { value: 5, text: '国五' },
    { value: 6, text: '国六' },
  ]
  return options
}

/**
 * @description: 轴数
 * @return {*} options
 */
export function getAxesOptions() {
  const options = [
    {
      value: 2,
      text: '二轴',
    },
    {
      value: 3,
      text: '三轴',
    },
    {
      value: 4,
      text: '四轴',
    },
    {
      value: 5,
      text: '五轴',
    },
    {
      value: 6,
      text: '六轴及以上',
    },
  ]
  return options
}

/**
 * @description: 车辆品牌
 * @return {*} options
 */
export function getBrandOptions() {
  const options = [
    {
      value: 1,
      text: '一汽',
    },
    {
      value: 2,
      text: '东风',
    },
    {
      value: 3,
      text: '上汽',
    },
    {
      value: 4,
      text: '三一',
    },
    {
      value: 5,
      text: '大运',
    },
    {
      value: 6,
      text: '福田',
    },
    {
      value: 7,
      text: '重汽',
    },
    {
      value: 8,
      text: '陕汽',
    },
    {
      value: 9,
      text: '其他',
    },
  ]
  return options
}

/**
 * @description: 车辆类型
 * @return {*} options
 */
export function getVehicleClassOptions() {
  const options = [
    {
      value: 1,
      text: '中置轴',
    },
    {
      value: 2,
      text: '高栏',
    },
    {
      value: 3,
      text: '集装箱',
    },
    {
      value: 4,
      text: '高低板',
    },
    {
      value: 5,
      text: '厢式',
    },
    {
      value: 6,
      text: '平板',
    },
    {
      value: 7,
      text: '飞翼',
    },
    {
      value: 8,
      text: '低栏',
    },
    // {
    //   value: 9,
    //   text: "油罐车",
    // },
    // {
    //   value: 10,
    //   text: "大件",
    // },
  ]
  return options
}

/**
 * @description: 货车用途
 * @return {*} options
 */
export function getVehicleUsageOptions() {
  const options = [
    {
      value: '0',
      text: '单机',
    },
    {
      value: '1',
      text: '拖挂',
    },
  ]
  return options
}

/**
 * @description: 驾照类型
 * @return {*} options
 */
export function getDriverLicenseOptions() {
  const options = [
    {
      value: 'A本',
      text: 'A本',
    },
    {
      value: 'B本',
      text: 'B本',
    },
  ]
  return options
}

/**
 * @description: 车辆吨位下拉
 * @return {*} options
 */
export function getVehicleTonOptions() {
  const options = [
    { value: 1, text: '小于5t' },
    { value: 2, text: '5t-10t' },
    { value: 3, text: '10t-15t' },
    { value: 4, text: '15t-25t' },
    { value: 5, text: '大于25t' },
  ]
  return options
}

/**
 * @description: 路线要求下拉
 * @return {*} options
 */
export function getRouteRequireOptions() {
  const options = [
    { value: 1, text: '全程高速' },
    { value: 2, text: '下道+高速' },
    { value: 3, text: '全程下道' },
    { value: 4, text: '无所谓，便宜就好' },
  ]
  return options
}

/**
 * @description: 货物属性下拉
 * @return {*} options
 */
export function getCargoPropertiesOptions() {
  const options = [
    { value: 1, text: '重货' },
    { value: 2, text: '重抛' },
    { value: 3, text: '抛货' },
  ]
  return options
}

/**
 * @description: 车辆类别
 * @return {*} options
 */
export function getCarVehicleTypeName() {
  const options = [
    { value: 1, text: '自有车辆' },
    { value: 2, text: '固有车辆' },
    { value: 3, text: '授权车辆' },
  ]
  return options
}

/**
 * @description: 能耗类型
 * @return {*} options
 */
export function getPowerTypeList() {
  const options = [
    { value: 0, text: '汽油' },
    { value: 1, text: '柴油' },
    { value: 2, text: '燃气(LNG)' },
    { value: 3, text: '纯电动' },
    { value: 4, text: '插电混动' },
    { value: 5, text: '燃气(CNG)' },
  ]
  return options
}

/**
 * @description: 操作日志模块
 * @return {*}
 */
export function getModelOptions() {
  const options = [
    {
      value: '2',
      text: '组织架构',
    },
    {
      value: '4',
      text: '车辆列表',
    },
    {
      value: '5',
      text: '司机列表',
    },
    {
      value: '6',
      text: '车队设定',
    },
    {
      value: '7',
      text: '收藏点管理',
    },
    {
      value: '9',
      text: '自定义围栏',
    },
    {
      value: '10',
      text: '路线规划',
    },
    {
      value: '11',
      text: '在途跟踪',
    },
    {
      value: '12',
      text: '轨迹查询',
    },
    {
      value: '13',
      text: '路线共享',
    },
    {
      value: '15',
      text: '任务统计',
    },
    {
      value: '16',
      text: '成本统计',
    },
    {
      value: '17',
      text: '异常统计',
    },
    {
      value: '18',
      text: '导航数据统计',
    },
    {
      value: '19',
      text: '效果统计',
    },
    {
      value: '20',
      text: '可视化大屏',
    },
    {
      value: '22',
      text: '支付管理',
    },
    {
      value: '23',
      text: '订单管理',
    },
    {
      value: '25',
      text: '账号管理',
    },
    {
      value: '26',
      text: '任务管理',
    },
    {
      value: '27',
      text: '出险管理',
    },
    {
      value: '28',
      text: '消息中心',
    },
    // {
    //   value: "29",
    //   text: "AI成本测算",
    // },
    // {
    //   value: "30",
    //   text: "碳中和管理",
    // },
  ]
  return options
}
