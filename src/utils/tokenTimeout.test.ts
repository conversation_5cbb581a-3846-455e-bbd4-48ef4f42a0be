/*
 * @Author: llm
 * @Date: 2025-07-07
 * @Description: Token超时管理工具测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { 
  getTokenTimeout, 
  setTokenTimeout, 
  DEFAULT_TOKEN_TIMEOUT,
  TIMEOUT_OPTIONS 
} from './tokenTimeout'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

describe('tokenTimeout', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getTokenTimeout', () => {
    it('should return default timeout when no config exists', () => {
      localStorageMock.getItem.mockReturnValue(null)
      expect(getTokenTimeout()).toBe(DEFAULT_TOKEN_TIMEOUT)
    })

    it('should return configured timeout when config exists', () => {
      const customTimeout = 60 * 60 * 1000 // 1 hour
      localStorageMock.getItem.mockReturnValue(customTimeout.toString())
      expect(getTokenTimeout()).toBe(customTimeout)
    })
  })

  describe('setTokenTimeout', () => {
    it('should save timeout to localStorage', () => {
      const timeout = 15 * 60 * 1000 // 15 minutes
      setTokenTimeout(timeout)
      expect(localStorageMock.setItem).toHaveBeenCalledWith('tokenTimeout', timeout.toString())
    })
  })

  describe('TIMEOUT_OPTIONS', () => {
    it('should have correct timeout values', () => {
      expect(TIMEOUT_OPTIONS['15分钟']).toBe(15 * 60 * 1000)
      expect(TIMEOUT_OPTIONS['30分钟']).toBe(30 * 60 * 1000)
      expect(TIMEOUT_OPTIONS['1小时']).toBe(60 * 60 * 1000)
      expect(TIMEOUT_OPTIONS['2小时']).toBe(2 * 60 * 60 * 1000)
      expect(TIMEOUT_OPTIONS['4小时']).toBe(4 * 60 * 60 * 1000)
    })
  })
})
