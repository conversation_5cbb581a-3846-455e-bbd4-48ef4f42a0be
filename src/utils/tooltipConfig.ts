/*
 * @Author: llm
 * @Date: 2025-01-08
 * @LastEditors: llm
 * @LastEditTime: 2025-01-08
 * @Description: Tooltip 配置工具
 */

/**
 * 获取 VXE Table 中 tooltip 的 z-index
 * @returns z-index 值
 */
export function getTooltipZIndex(): number {
  // 检查是否在 VXE Table 环境中
  const vxeTableElement = document.querySelector('.vxe-table, .vxe-table-container')
  if (vxeTableElement) {
    // 如果在 VXE Table 中，返回高的 z-index
    return 9999
  }
  // 默认使用普通 z-index
  return 2000
}

/**
 * 获取 tooltip 的默认配置
 * @param content tooltip 内容
 * @param placement 位置
 * @returns tooltip 配置对象
 */
export function getTooltipConfig(content: string, placement: string = 'top') {
  return {
    content,
    placement,
    effect: 'dark',
    showAfter: 100,
    zIndex: getTooltipZIndex()
  }
}

/**
 * 动态设置 tooltip 的 z-index
 * 这个函数可以在组件挂载时调用，确保 tooltip 有正确的层级
 */
export function setTooltipZIndex() {
  // 立即更新一次
  updateTooltipZIndex()

  // 监听弹窗的打开和关闭
  const observer = new MutationObserver((mutations) => {
    let shouldUpdate = false

    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        // 检查是否有弹窗被添加或移除
        const addedNodes = Array.from(mutation.addedNodes)
        const removedNodes = Array.from(mutation.removedNodes)

        const hasDialog = [...addedNodes, ...removedNodes].some(node =>
          node instanceof Element && (
            node.classList.contains('el-dialog') ||
            node.classList.contains('el-drawer') ||
            node.classList.contains('el-popover') ||
            node.classList.contains('el-tooltip__popper')
          )
        )

        if (hasDialog) {
          shouldUpdate = true
        }
      }
    })

    if (shouldUpdate) {
      // 延迟更新，确保 DOM 已经完全渲染
      setTimeout(() => {
        updateTooltipZIndex()
      }, 10)
    }
  })

  // 开始观察 body 的变化
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['class', 'style']
  })

  // 定期检查并更新（作为备用方案）
  const intervalId = setInterval(() => {
    updateTooltipZIndex()
  }, 1000)

  return {
    observer,
    intervalId,
    disconnect: () => {
      observer.disconnect()
      clearInterval(intervalId)
    }
  }
}

/**
 * 更新 VXE Table 中 tooltip 的 z-index
 */
function updateTooltipZIndex() {
  // 只更新 VXE Table 容器内的 tooltip
  const vxeTableElements = document.querySelectorAll('.vxe-table, .vxe-table-container')

  vxeTableElements.forEach(vxeTable => {
    const tooltips = vxeTable.querySelectorAll('.el-tooltip__popper')
    const zIndex = 9999 // VXE Table 专用高层级

    tooltips.forEach(tooltip => {
      if (tooltip instanceof HTMLElement) {
        tooltip.style.zIndex = zIndex.toString()
        tooltip.style.setProperty('z-index', zIndex.toString(), 'important')
      }
    })
  })
}
