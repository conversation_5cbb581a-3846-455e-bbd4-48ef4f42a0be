/*
 * @Author: llm
 * @Date: 2025-01-08
 * @LastEditors: llm
 * @LastEditTime: 2025-01-08
 * @Description: Tooltip 调试工具
 */

/**
 * 检查页面中所有 tooltip 的 z-index 值
 */
export function checkTooltipZIndex() {
  const tooltips = document.querySelectorAll('.el-tooltip__popper')
  console.log(`找到 ${tooltips.length} 个 tooltip`)
  
  tooltips.forEach((tooltip, index) => {
    if (tooltip instanceof HTMLElement) {
      const computedStyle = window.getComputedStyle(tooltip)
      const zIndex = computedStyle.zIndex
      console.log(`Tooltip ${index + 1}: z-index = ${zIndex}`)
    }
  })
  
  return tooltips.length
}

/**
 * 检查弹窗的 z-index
 */
export function checkDialogZIndex() {
  const dialogs = document.querySelectorAll('.el-dialog')
  console.log(`找到 ${dialogs.length} 个弹窗`)
  
  dialogs.forEach((dialog, index) => {
    if (dialog instanceof HTMLElement) {
      const computedStyle = window.getComputedStyle(dialog)
      const zIndex = computedStyle.zIndex
      console.log(`Dialog ${index + 1}: z-index = ${zIndex}`)
    }
  })
  
  return dialogs.length
}

/**
 * 强制修复所有 tooltip 的 z-index
 */
export function forceFixTooltipZIndex() {
  const tooltips = document.querySelectorAll('.el-tooltip__popper')
  let fixed = 0
  
  tooltips.forEach((tooltip) => {
    if (tooltip instanceof HTMLElement) {
      tooltip.style.zIndex = '3000'
      fixed++
    }
  })
  
  console.log(`强制修复了 ${fixed} 个 tooltip 的 z-index`)
  return fixed
}

/**
 * 添加调试信息到页面
 */
export function addDebugInfo() {
  // 创建调试面板
  const debugPanel = document.createElement('div')
  debugPanel.id = 'tooltip-debug-panel'
  debugPanel.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 5px;
    z-index: 9999;
    font-family: monospace;
    font-size: 12px;
    max-width: 300px;
  `
  
  // 添加按钮
  const buttons = [
    { text: '检查 Tooltip', action: checkTooltipZIndex },
    { text: '检查弹窗', action: checkDialogZIndex },
    { text: '强制修复', action: forceFixTooltipZIndex },
    { text: '关闭', action: () => debugPanel.remove() }
  ]
  
  buttons.forEach(({ text, action }) => {
    const button = document.createElement('button')
    button.textContent = text
    button.style.cssText = `
      margin: 2px;
      padding: 5px 10px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
    `
    button.onclick = action
    debugPanel.appendChild(button)
  })
  
  // 添加到页面
  document.body.appendChild(debugPanel)
  
  console.log('Tooltip 调试面板已添加到页面右上角')
}

/**
 * 在控制台中运行的快速检查函数
 */
export function quickCheck() {
  console.log('=== Tooltip 层级快速检查 ===')
  checkDialogZIndex()
  checkTooltipZIndex()
  
  const hasDialog = document.querySelector('.el-dialog')
  if (hasDialog) {
    console.log('检测到弹窗存在，tooltip 应该使用 z-index: 3000')
  } else {
    console.log('未检测到弹窗，tooltip 使用默认 z-index')
  }
}

// 在开发环境下自动添加到 window 对象，方便调试
if (process.env.NODE_ENV === 'development') {
  (window as any).tooltipDebug = {
    check: quickCheck,
    checkTooltip: checkTooltipZIndex,
    checkDialog: checkDialogZIndex,
    fix: forceFixTooltipZIndex,
    addPanel: addDebugInfo
  }
  
  console.log('Tooltip 调试工具已加载，使用 window.tooltipDebug 访问')
}
