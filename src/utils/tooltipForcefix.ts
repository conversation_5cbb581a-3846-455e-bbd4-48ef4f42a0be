/*
 * @Author: llm
 * @Date: 2025-01-08
 * @LastEditors: llm
 * @LastEditTime: 2025-01-08
 * @Description: 强制修复 Tooltip 层级问题
 */

/**
 * 强制修复所有 tooltip 的 z-index
 * 这是最后的解决方案，确保 tooltip 始终在最上层
 */
export function forceFixTooltipZIndex() {
  const SUPER_HIGH_Z_INDEX = 99999

  // 1. 立即修复现有的 tooltip
  const fixExistingTooltips = () => {
    const tooltips = document.querySelectorAll('.el-tooltip__popper')
    tooltips.forEach(tooltip => {
      if (tooltip instanceof HTMLElement) {
        tooltip.style.setProperty('z-index', SUPER_HIGH_Z_INDEX.toString(), 'important')
      }
    })
  }

  // 2. 监听新创建的 tooltip
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node instanceof Element) {
            // 检查新添加的节点是否是 tooltip
            if (node.classList.contains('el-tooltip__popper')) {
              (node as HTMLElement).style.setProperty('z-index', SUPER_HIGH_Z_INDEX.toString(), 'important')
            }
            
            // 检查新添加节点的子元素中是否有 tooltip
            const tooltips = node.querySelectorAll('.el-tooltip__popper')
            tooltips.forEach(tooltip => {
              if (tooltip instanceof HTMLElement) {
                tooltip.style.setProperty('z-index', SUPER_HIGH_Z_INDEX.toString(), 'important')
              }
            })
          }
        })
      }
    })
  })

  // 3. 开始监听
  observer.observe(document.body, {
    childList: true,
    subtree: true
  })

  // 4. 立即修复一次
  fixExistingTooltips()

  // 5. 定期强制修复（备用方案）
  const intervalId = setInterval(() => {
    fixExistingTooltips()
  }, 500)

  // 6. 监听弹窗事件，立即修复
  document.addEventListener('DOMNodeInserted', () => {
    setTimeout(fixExistingTooltips, 10)
  })

  return {
    observer,
    intervalId,
    stop: () => {
      observer.disconnect()
      clearInterval(intervalId)
    }
  }
}

/**
 * 添加全局 CSS 规则来强制修复 tooltip 层级
 */
export function addGlobalTooltipCSS() {
  const style = document.createElement('style')
  style.id = 'tooltip-force-fix'
  style.innerHTML = `
    /* 强制修复 tooltip 层级 */
    .el-tooltip__popper,
    .el-tooltip__popper[data-popper-placement],
    div[role="tooltip"].el-tooltip__popper,
    [class*="el-tooltip__popper"] {
      z-index: 99999 !important;
    }
    
    /* 确保在任何容器中都有最高层级 */
    body .el-tooltip__popper,
    .el-dialog .el-tooltip__popper,
    .el-drawer .el-tooltip__popper,
    .vxe-table .el-tooltip__popper {
      z-index: 99999 !important;
    }
    
    /* 智能 tooltip 样式 */
    .smart-tooltip-high-z-index {
      z-index: 99999 !important;
    }
  `
  
  // 移除旧的样式（如果存在）
  const oldStyle = document.getElementById('tooltip-force-fix')
  if (oldStyle) {
    oldStyle.remove()
  }
  
  // 添加新样式到 head
  document.head.appendChild(style)
}

/**
 * 初始化强制修复
 */
export function initTooltipForceFix() {
  // 添加全局 CSS
  addGlobalTooltipCSS()
  
  // 启动动态修复
  const fixer = forceFixTooltipZIndex()
  
  console.log('🔧 Tooltip 强制修复已启动')
  
  return fixer
}

// 在开发环境下添加调试功能
if (process.env.NODE_ENV === 'development') {
  (window as any).tooltipForceFix = {
    init: initTooltipForceFix,
    fix: forceFixTooltipZIndex,
    addCSS: addGlobalTooltipCSS
  }
}
