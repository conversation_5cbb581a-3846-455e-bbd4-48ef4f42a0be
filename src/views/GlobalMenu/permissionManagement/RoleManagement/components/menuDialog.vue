<!--
 * @Author: llm
 * @Date: 2023-06-29 09:06:23
 * @LastEditors: llm
 * @LastEditTime: 2024-11-29 15:38:50
 * @Description: 菜单+按钮权限弹窗
 *
-->
<template>
  <el-dialog :draggable="true" v-model="menuDialogVisible.visible" :title="'【' + checkedRole.name + '】权限分配'" width="800px">
    <el-scrollbar v-loading="menuTreeLoading" max-height="600px">
      <el-checkbox v-model="deptNodeAll" @change="handleCheckedTreeNodeAll($event)">全选/全不选</el-checkbox>
      <el-tree
        ref="menuRef"
        node-key="menuId"
        show-checkbox
        empty-text="加载中，请稍候"
        :data="menuList"
        :highlight-current="true"
        :default-checked-keys="checkedRole.menuIds"
        :check-strictly="false"
        :default-expand-all="true"
        :expand-on-click-node="false"
        :props="defaultProps"
        :check-on-click-node="true"
      >
        <!-- <template #default="{ data }">
          {{ data.meta.title }} -->
        <!-- <el-tag v-if="data.meta.type === 1" type="success" style="margin-left: 10px" size="small">菜单</el-tag>
          <el-tag v-else-if="data.meta.type === 2" type="danger" style="margin-left: 10px" size="small">按钮</el-tag>
          <el-tag v-else-if="data.meta.type === 3" type="success" style="margin-left: 10px" size="small">Tab</el-tag>
          <el-tag v-else-if="data.meta.type === 4" type="success" style="margin-left: 10px" size="small">目录</el-tag> -->
        <!-- </template> -->
        <template #default="{ data }">
          <div :class="{ 'single-line': data.children && data.children.length === 0, 'multi-line': data.children && data.children.length > 0 }">
            {{ data.meta.title }}
          </div>
        </template>
      </el-tree>
    </el-scrollbar>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleRoleMenuSubmit">确 定</el-button>
        <el-button @click="closeMenuDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
  import { CheckedRole } from '@/api/authorityManagement/RoleManagement/types'
  import { PropType } from 'vue'
  const props = defineProps({
    /**
     * 加载中
     */
    menuTreeLoading: {
      require: true,
      type: Boolean,
      default: false,
    },
    /**
     * 弹窗属性
     */
    menuDialogVisible: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
    /**
     * 选中的角色信息
     */
    checkedRole: {
      require: true,
      type: Object as PropType<CheckedRole>,
      default: () => {
        return {}
      },
    },
    /**
     * 菜单列表
     */
    menuList: {
      require: true,
      type: Array as PropType<OptionType[]>,
      default: () => {
        return []
      },
    },
    /**
     * 选中的菜单
     */
    checkedIds: {
      require: true,
      type: Array<string>,
      default: () => {
        return []
      },
    },
  })

  const emit = defineEmits(['closeMenuDialog', 'selectMenuIds'])
  const deptExpand = ref(false)
  const deptNodeAll = ref(false)
  const menuRef = ref()
  const defaultProps = reactive({
    children: 'children',
    label: 'label',
  })
  watch(
    () => props.checkedRole.menuIds,
    (menuIds) => {
      nextTick(() => {
        menuIds?.forEach((menuId) => {
          menuRef.value!.setChecked(menuId, true, false)
        })
      })
    },
  )

  // 树权限（全选/全不选）
  function handleCheckedTreeNodeAll(value: any) {
    menuRef.value.setCheckedNodes(value ? props.menuList : [])
  }
  /**
   * 角色分配菜单提交
   */
  const handleRoleMenuSubmit = () => {
    const roleId = props.checkedRole.id
    if (roleId) {
      const checkedMenuIds = getMenuAllCheckedKeys()
      emit('selectMenuIds', checkedMenuIds, roleId)
    }
  }
  /**
   * 关闭弹窗
   */
  const closeMenuDialog = () => {
    emit('closeMenuDialog')
  }
  // 所有菜单节点数据
  const getMenuAllCheckedKeys = () => {
    // 目前被选中的菜单节点
    let checkedKeys = menuRef.value.getCheckedKeys()
    // 半选中的菜单节点
    let halfCheckedKeys = menuRef.value.getHalfCheckedKeys()
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)
    return checkedKeys
  }

  //设置操作按钮样式为行内样式
  const addClassToSecondLastLevel = (container: any) => {
    const allTreeNodes = container.querySelectorAll('.el-tree-node')

    allTreeNodes.forEach((node: any) => {
      if (node.parentElement!.classList.contains('el-tree')) {
        // 跳过.el-tree的直接子元素
        return
      }
      //如何下级元素包含
      const childrenContainer = node.querySelector('.el-tree-node__children')
      if (childrenContainer && node.parentElement !== container) {
        childrenContainer.classList.add('abc')

        const parentChildren = node.parentElement?.parentElement?.querySelector('.el-tree-node__children')
        if (parentChildren) {
          const parentContent = node.parentElement?.parentElement?.querySelector('.el-tree-node__content')
          const paddingLeft = getComputedStyle(parentContent!).paddingLeft
          const paddingLeftNum = parseFloat(paddingLeft)
          parentChildren.style.paddingLeft = `${paddingLeftNum + 18}px`
        }
      } else if (childrenContainer && node.parentElement === container) {
        // 如果是.el-tree的直接子元素，则不添加类abc
        return
      } else {
        // 如果没有子元素，则递归调用
        if (childrenContainer && childrenContainer.children.length > 0) {
          addClassToSecondLastLevel(childrenContainer)
        }
      }
    })
  }

  const setFlexForChildren = (container: HTMLElement) => {
    const treeContainer = document.querySelector('.el-tree')
    if (treeContainer) {
      addClassToSecondLastLevel(treeContainer)
    }
  }
  defineExpose({
    menuRef,
    setFlexForChildren,
  })
</script>
<style scoped lang="scss">
  :deep(.el-tree-node__children.abc) {
    display: flex !important;
    flex-flow: wrap;
  }
  :deep(.el-tree-node__children.abc .el-tree-node__content) {
    padding-left: 0 !important;
  }
  :deep(.el-tree-node__children.abc:nth-child(1) .el-tree-node .el-tree-node__content) {
    padding-left: 54px !important;
  }
  :deep(.el-tree) {
    max-width: 100% !important;
  }
  .single-line {
    display: flex; // 等于0的子节点在同一行排列
    flex-flow: wrap;
    margin-right: 10px; // 添加间距
  }
  .multi-line {
    width: 600px;
    display: block; // 大于0的子节点单独一行
  }
</style>
