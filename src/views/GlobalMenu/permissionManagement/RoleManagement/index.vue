<!--
 * @Author: llm
 * @Date: 2023-06-27 12:05:28
 * @LastEditors: llm
 * @LastEditTime: 2024-10-29 15:30:50
 * @Description: 角色管理
 *
-->

<template>
  <BasePage1></BasePage1>
</template>

<script setup lang="ts">
  import BasePage1 from '@/views/Pages/basePage1.vue'
  defineOptions({
    name: 'PermissionManagementRoleManagement',
    inheritAttrs: false,
  })
</script>

<style lang="scss" scoped></style>
