<!--
 * @Author: llm
 * @Date: 2024-10-26 17:39:07
 * @LastEditors: llm
 * @LastEditTime: 2024-10-28 17:07:28
 * @Description:
-->
<template>
  <BasePage1></BasePage1>
</template>

<script setup lang="ts">
  import BasePage1 from '@/views/Pages/basePage1.vue'
  defineOptions({
    name: 'PermissionManagementOrganizationManagement',
    inheritAttrs: false,
  })
</script>

<style lang="scss" scoped></style>
