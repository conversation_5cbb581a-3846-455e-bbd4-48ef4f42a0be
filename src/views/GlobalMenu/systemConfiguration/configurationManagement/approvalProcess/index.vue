<!--
 * @Author: llm
 * @Date: 2024-11-27 10:26:02
 * @LastEditors: llm
 * @LastEditTime: 2024-11-27 10:26:12
 * @Description:
-->
<template>
  <BasePage1></BasePage1>
</template>

<script setup lang="ts">
  import BasePage1 from '@/views/Pages/basePage1.vue'
  defineOptions({
    name: 'SystemConfigurationConfigurationManagementApprovalProcess',
    inheritAttrs: false,
  })
</script>

<style lang="scss" scoped></style>
