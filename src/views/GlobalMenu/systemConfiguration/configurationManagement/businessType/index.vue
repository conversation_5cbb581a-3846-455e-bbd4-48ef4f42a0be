<!--
 * @Author: llm
 * @Date: 2025-03-20 18:05:09
 * @LastEditors: llm
 * @LastEditTime: 2025-03-20 18:05:32
 * @Description: 业务类型
-->
<template>
  <GlobalPage :pageType="state.pageType" />
</template>
<script lang="ts" setup>
  import { getCurrentInstance, onBeforeMount, reactive } from 'vue'
  import GlobalPage from '@/components/GlobalPage/index.vue'
  defineOptions({
    name: 'SystemConfigurationConfigurationManagementBusinessType',
    inheritAttrs: false,
  })
  const { proxy }: any = getCurrentInstance()
  const state = reactive({
    pageType: '',
    pageName: '',
  })
  onBeforeMount(() => {
    state.pageType = proxy.$sideBarStore.$state.pageType
  })
</script>
