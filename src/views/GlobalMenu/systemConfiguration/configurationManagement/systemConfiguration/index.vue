<!--
 * @Author: llm
 * @Date: 2024-12-31 10:37:20
 * @LastEditors: llm
 * @LastEditTime: 2025-07-17 11:59:33
 * @Description: 系统设置
-->
<template>
  <div class="components-container" v-loading="state.showLoading" element-loading-text="加载中...">
    <div class="contentBox">
      <el-form ref="formDataRef" :inline="true" :model="state.dataList[index]" v-for="(item, index) in state.dataList" :key="index">
        <el-card style="width: 100%">
          <el-row>
            <el-col :span="24">
              <div class="titlebox">
                <h3>{{ item.category }}</h3>
              </div>
            </el-col>
            <el-col :span="24">
              <el-tag
                style="
                  margin-bottom: 10px;
                  display: block;
                  width: 100%;
                  height: auto;
                  line-height: 18px;
                  padding: 4px 10px;
                  word-wrap: break-word;
                  white-space: normal;
                "
                type="warning"
                >{{ item.instructions }}</el-tag
              >
            </el-col>
            <el-col :span="24" v-for="(event, eventIndex) in item.itemList" :key="eventIndex">
              <el-form-item v-if="event.configType === 3">
                <template #label>
                  <div class="flex items-center">
                    <div
                      v-if="event.configSetting.colorCode"
                      :style="{ background: event.configSetting.colorCode, width: '28px', height: '14px', marginRight: '6px', borderRadius: '1px' }"
                    ></div>
                    <span>{{ event.itemName }}</span>
                  </div>
                </template>
                <el-input-number
                  style="width: 100%"
                  v-if="event.configSetting"
                  v-model="event.configSetting.value"
                  :min="event.configSetting.min"
                  :max="event.configSetting.max"
                  :placeholder="event.configSetting.tips"
                />
              </el-form-item>
              <!-- 失效时间 -->
              <div v-if="event.configType === 5 && event.configJson">
                <el-form-item label="账密登录失效时间设置" label-width="160px" :prop="`itemList.${eventIndex}.configJson.passwordTimeoutEnable`">
                  <el-radio-group v-model="event.configJson.passwordTimeoutEnable">
                    <el-radio :value="true">是</el-radio>
                    <el-radio :value="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <div class="flex items-center">
                  <template v-if="event.configJson.passwordTimeoutEnable">
                    <el-form-item
                      label="失效时间"
                      label-width="90px"
                      :prop="`itemList.${eventIndex}.configJson.passwordTimeoutMinutes`"
                      :rules="[{ required: true, message: '请输入失效时间', trigger: 'blur' }]"
                    >
                      <template #label>
                        <span>失效时间<span class="required-field">*</span></span>
                      </template>
                      <el-input-number style="width: 100px" v-model="event.configJson.passwordTimeoutMinutes" :min="1" controls-position="right" />
                      <span class="ml-12px">分</span>
                    </el-form-item>
                  </template>
                </div>
                <div>
                  <el-form-item label="首次登录强制更改密码" label-width="160px" :prop="`itemList.${eventIndex}.configJson.firstLoginForceModifyPassword`">
                    <el-radio-group v-model="event.configJson.firstLoginForceModifyPassword">
                      <el-radio :value="true">是</el-radio>
                      <el-radio :value="false">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>

                <el-form-item label="密码输入错误强制锁定" label-width="160px" :prop="`itemList.${eventIndex}.configJson.loginPasswordErrorForceLock`">
                  <el-radio-group v-model="event.configJson.loginPasswordErrorForceLock">
                    <el-radio :value="true">是</el-radio>
                    <el-radio :value="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <div v-if="event.configJson.loginPasswordErrorForceLock">
                  <div>
                    <el-form-item
                      :prop="`itemList.${eventIndex}.configJson.wrongTimesLock`"
                      label-width="50px"
                      :rules="[{ required: true, message: '请输入锁定次数', trigger: 'blur' }]"
                    >
                      <template #label>
                        <span>输入</span>
                      </template>
                      <div class="flex items-center">
                        <el-input-number style="width: 100px; margin-right: 6px" v-model="event.configJson.wrongTimesLock" :min="1" controls-position="right" />
                        <el-text>次锁定</el-text>
                      </div>
                    </el-form-item>
                    <el-form-item
                      :prop="`itemList.${eventIndex}.configJson.wrongTimesFrozen`"
                      label-width="50px"
                      :rules="[{ required: true, message: '请输入封禁次数', trigger: 'blur' }]"
                    >
                      <template #label>
                        <span>输入</span>
                      </template>
                      <div class="flex items-center">
                        <el-input-number
                          style="width: 100px; margin-right: 6px"
                          v-model="event.configJson.wrongTimesFrozen"
                          :min="1"
                          controls-position="right"
                        />
                        <el-text>次封禁</el-text>
                      </div>
                    </el-form-item>
                  </div>
                  <div>
                    <el-form-item
                      label-width="110px"
                      :prop="`itemList.${eventIndex}.configJson.lockIntervalMinutes`"
                      :rules="[{ required: true, message: '请输入锁定间隔', trigger: 'blur' }]"
                    >
                      <template #label>
                        <span>每次锁定间隔</span>
                      </template>
                      <div class="flex items-center">
                        <div class="flex items-center">
                          <el-input-number
                            style="width: 100px; margin-right: 6px"
                            v-model="event.configJson.lockIntervalMinutes"
                            :min="1"
                            controls-position="right"
                          />
                          <el-text>分</el-text>
                        </div>
                      </div>
                    </el-form-item>
                  </div>
                  <div>
                    <el-form-item
                      label-width="140px"
                      :prop="`itemList.${eventIndex}.configJson.frozenIntervalMinutes`"
                      :rules="[{ required: true, message: '请输入最后锁定间隔', trigger: 'blur' }]"
                    >
                      <template #label>
                        <span>最后一次锁定间隔</span>
                      </template>
                      <div class="flex items-center">
                        <el-input-number
                          style="width: 100px; margin-right: 6px"
                          v-model="event.configJson.frozenIntervalMinutes"
                          :min="1"
                          controls-position="right"
                        />
                        <el-text>分</el-text>
                      </div>
                    </el-form-item>
                  </div>
                </div>
              </div>
              <!-- 工资计算配置 -->
              <div v-if="event.configType === 6 && event.configJson">
                <div class="mb-8px">工资计算配置</div>
                <el-form-item label="工资自动计算司机劳务费">
                  <el-radio-group v-model="event.configJson.autoComputeDriverLaborFee">
                    <el-radio :value="true">是</el-radio>
                    <el-radio :value="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <div>
                  <el-form-item label="劳务费归集时间" v-if="event.configJson.autoComputeDriverLaborFee">
                    <el-radio-group v-model="event.configJson.laborFeeBelongTime">
                      <el-radio :value="0">按发车时间</el-radio>
                      <el-radio :value="1">按交车时间</el-radio>
                      <el-radio :value="2">按调度时间</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
              </div>
              <!-- 单选/多个类别 -->
              <el-form-item :label="event.itemName" v-else>
                <el-radio-group v-model="event.itemType" v-if="event.configType === 4">
                  <el-radio :value="0">{{
                    event.itemOrder === 1
                      ? '每月1号（系统默认）'
                      : event.itemOrder === 2
                        ? '按下达时间/创建时间'
                        : event.itemOrder === 3
                          ? '按发生时间'
                          : event.itemOrder === 10
                            ? '按实际里程结算'
                            : event.itemOrder === 56
                              ? '按调度单审批通过结算'
                              : '是'
                  }}</el-radio>
                  <el-radio :value="1">{{
                    event.itemOrder === 1
                      ? '自定义'
                      : event.itemOrder === 2
                        ? '按装车时间/发车时间'
                        : event.itemOrder === 3
                          ? '按上报时间'
                          : event.itemOrder === 10
                            ? '按线路里程结算'
                            : event.itemOrder === 56
                              ? '按结算月审批通过结算'
                              : '否'
                  }}</el-radio>
                  <el-radio :value="2" v-if="event.itemOrder === 2">{{ '按交付时间/完成时间' }}</el-radio>
                </el-radio-group>
                <el-radio-group v-model="event.itemType" v-if="event.configType === 0">
                  <el-radio :value="0">是</el-radio>
                  <el-radio :value="1">否</el-radio>
                </el-radio-group>
                <template v-if="event.optionText && event.itemType === 1">
                  <el-form-item label="影响范围" label-width="120px" label-position="right">
                    <el-select v-model="event.itemRule" filterable placeholder="请选择" clearable style="width: 220px">
                      <el-option v-for="(option, _index) in event.optionText" :key="_index" :label="option.name" :value="option.name" />
                    </el-select>
                  </el-form-item>
                </template>
                <template v-if="event.multipleText && event.configType === 1">
                  <el-checkbox-group v-model="event.selectedIds">
                    <el-checkbox v-for="option in event.multipleText" :key="option.id" :label="option.name" :value="option.id" />
                  </el-checkbox-group>
                </template>
                <!-- 下拉多选 -->
                <template v-if="event.configType === 2">
                  <el-select
                    v-model="event.multipleText"
                    multiple
                    value-key="value"
                    :max-collapse-tags="2"
                    :collapse-tags="true"
                    filterable
                    placeholder="请选择客户"
                    clearable
                    style="width: 320px"
                  >
                    <el-option v-for="(option, _index) in event.multipleList" :key="option.value" :label="option.label" :value="option" />
                  </el-select>
                </template>
              </el-form-item>
              <el-tag
                v-if="event.tip"
                style="
                  display: block;
                  width: 100%;
                  height: auto;
                  line-height: 18px;
                  padding: 4px 10px;
                  margin-bottom: 14px;
                  word-wrap: break-word;
                  white-space: normal;
                "
                type="warning"
                >{{ event.tip }}</el-tag
              >
              <el-col :span="24" v-if="event.itemSubName && event.itemType === 0">
                <el-form-item :label="event.itemSubName">
                  <el-radio-group v-model="event.itemSubType">
                    <el-radio :value="0">同城市</el-radio>
                    <el-radio :value="1">距离</el-radio>
                  </el-radio-group>
                  <template v-if="event.itemSubType === 1">
                    <el-form-item label=" " label-width="20px" label-position="right">
                      <el-input v-model="event.itemSubRule" style="max-width: 200px" placeholder="Please input">
                        <template #append>km以内</template>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-form-item>
              </el-col>
            </el-col>
          </el-row>
          <div class="flex-row justify-end">
            <el-button type="primary" @click.stop="confirm(item)">确认</el-button>
            <el-button type="primary" v-if="item.historyShow" @click.stop="viewHistory(item.historyShowUrl)">查看修改历史</el-button>
          </div>
        </el-card>
      </el-form>
    </div>

    <!-- 修改历史 -->
    <el-dialog title="修改历史" v-model="state.showHistoryDialog">
      <el-table show-overflow-tooltip :border="true" :data="state.historyData">
        <!-- 序号 -->
        <el-table-column align="center" label="序号" type="index" width="60"></el-table-column>
        <el-table-column v-for="item in tableConfig.tableItem" align="center" :key="item.name" :label="item.label" :prop="item.name"> </el-table-column>
      </el-table>
      <pagination v-model:limit="state.limit" v-model:page="state.page" v-model:total="state.total" @pagination="_pagination" style="margin-top: 15px" />
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { getCurrentInstance, reactive, ref, onMounted } from 'vue'
  import { getSystemSettingApi, setItemSystemSettingApi, publicDataApi } from '@/api/GlobalMenu/index'
  import { ElMessage } from 'element-plus'

  defineOptions({
    name: 'SystemConfigurationConfigurationManagementSystemConfiguration',
    inheritAttrs: false,
  })

  const formDataRef = ref<any>()

  // 验证规则
  const rules = {
    // 针对公共验证规则
    itemList: {
      validator: (rule: any, value: any, callback: any) => {
        if (value && Array.isArray(value)) {
          const invalidItems = value.filter((item: any) => {
            if (item.configType === 3 && item.configSetting) {
              return item.configSetting.value === undefined || item.configSetting.value === null || item.configSetting.value === ''
            }
            return false
          })

          if (invalidItems.length > 0) {
            callback(new Error('请填写必填项'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
    },
  }

  // 定义 configSetting 的类型
  interface ConfigSetting {
    color: string
    colorCode: string
    value: number
    type: string
    max: number
    min: number
    name: string
    tips: string
  }
  export interface List {
    category: string
    instructions: string
    showOrder: number
    historyShow: boolean
    historyShowUrl: string
    itemList: Array<{
      itemName: string
      itemSubName: string
      itemSubType: any
      itemRule: any
      itemSubRule: any
      itemType: any
      itemOrder: number
      /**
       * 0-默认是/否 支持单选
       * 1-支持多选
       * 2-筛选项读取配置接口 query_uri 字段
       * 3-自定义字段设置
       * 4-单选/多个类别
       * 5-设置密码失效时间
       * 6-工资计算配置
       */
      configType: number
      queryUri: string
      tip: string | null
      configSetting: ConfigSetting
      optionText?: Array<{ name: string }>
      selectedIds?: Array<number | string>
      multipleList?: Array<{ label: string; value: string }>
      multipleText?: Array<{ id: number; name: string; selected: boolean }>
      configJson?: {
        salaryCalculationConfig?: number
        passwordTimeoutEnable?: boolean
        passwordTimeoutMinutes?: number
        firstLoginForceModifyPassword?: boolean
        loginPasswordErrorForceLock?: boolean
        wrongTimesLock?: number
        wrongTimesFrozen?: number
        lockIntervalMinutes?: number
        frozenIntervalMinutes?: number

        /**
         * 劳务费归集时间
         * 0 - 按发车时间
         * 1 - 按交车时间
         * 2 - 按调度时间
         */
        laborFeeBelongTime?: number
        /**
         * 工资自动计算司机劳务费
         */
        autoComputeDriverLaborFee?: boolean
      }
    }>
  }

  const tableConfig = {
    tableItem: [
      {
        name: 'oldContent',
        label: '修改前',
      },
      {
        name: 'newContent',
        label: '修改后',
      },
      {
        name: 'createTime',
        label: '修改时间',
        width: '140px',
      },
      {
        name: 'createUserName',
        label: '修改人',
        width: '120px',
      },
    ],
  }

  const { proxy }: any = getCurrentInstance()
  const state = reactive({
    showLoading: false,
    confirmLoading: false, //提交loading
    showHistoryDialog: false, // 查看修改历史
    dataList: [] as List[],
    multipleText: [] as any[],
    formData: {},
    historyData: [],
    total: 0,
    page: 1,
    limit: 50,
    uri: '',
  })

  onMounted(() => {
    getDataList()
  })

  // 初始化复选框选中的值
  const initializeSelectedIds = (dataList: List[]) => {
    dataList.forEach((item) => {
      item.itemList.forEach((event) => {
        if (event.multipleText) {
          // 初始化 selectedIds 属性
          event.selectedIds = event.multipleText.filter((option) => option.selected).map((option) => option.id)
        }
      })
    })
  }

  // 获取客户下拉列表
  const getCustomerListData = (dataList: List[]) => {
    dataList.forEach((item) => {
      item.itemList.forEach(async (event) => {
        if (event.configType === 2 && event.queryUri) {
          await publicDataApi({}, event.queryUri).then((response: any) => {
            if (response.code === 200) {
              event.multipleList = response.data
            }
          })
        }
      })
    })
  }

  // 分页
  const _pagination = async (params: any) => {
    state.page = params.page
    state.limit = params.limit
    await getHistoeyDataList()
  }

  // 查看历史
  const viewHistory = (url: any) => {
    state.page = 1
    state.limit = 50
    if (url) {
      state.uri = url
      getHistoeyDataList()
      state.showHistoryDialog = true
    }
  }

  // 查看修改历史
  const getHistoeyDataList = async () => {
    let params = {
      page: state.page,
      limit: state.limit,
    }
    await publicDataApi(params, state.uri).then((response: any) => {
      if (response.code === 200) {
        state.historyData = response.data.rows
        state.total = response.data.total
      } else {
        state.historyData = []
        state.total = 0
      }
    })
  }

  // 提交
  const confirm = (item: any) => {
    // 手动验证数据
    let valid = true

    // 手动验证需要的字段
    item.itemList.forEach((field: any) => {
      // 验证配置类型3（数字输入）的字段
      if (field.configType === 3 && field.configSetting) {
        const value = field.configSetting.value
        if (value === undefined || value === null || value === '') {
          valid = false
          ElMessage.error(`${field.itemName}不能为空`)
        }
      }

      // 验证配置类型5（密码相关设置）的字段
      if (field.configType === 5 && field.configJson) {
        const config = field.configJson

        if (config.passwordTimeoutEnable && (!config.passwordTimeoutMinutes || config.passwordTimeoutMinutes < 1)) {
          valid = false
          ElMessage.error('请输入有效的失效时间')
        }

        if (config.loginPasswordErrorForceLock) {
          if (!config.wrongTimesLock || config.wrongTimesLock < 1) {
            valid = false
            ElMessage.error('请输入有效的锁定次数')
          }
          if (!config.wrongTimesFrozen || config.wrongTimesFrozen < 1) {
            valid = false
            ElMessage.error('请输入有效的封禁次数')
          }
          if (!config.lockIntervalMinutes || config.lockIntervalMinutes < 1) {
            valid = false
            ElMessage.error('请输入有效的锁定间隔')
          }
          if (!config.frozenIntervalMinutes || config.frozenIntervalMinutes < 1) {
            valid = false
            ElMessage.error('请输入有效的最后锁定间隔')
          }
        }
      }
    })

    if (!valid) {
      return
    }

    // 验证通过，继续提交
    let params = {
      ...item,
    }
    let newParams = JSON.parse(JSON.stringify(params))
    // 判断有多选框的情况 处理数据
    newParams.itemList.forEach((item: any) => {
      if (item.configType === 1 && item.multipleText) {
        item.multipleText.forEach((option: any) => {
          option.selected = item.selectedIds.includes(option.id)
        })
      } else if (item.configType === 2) {
        delete item.multipleList
      } else if (item.configType === 5 || item.configType === 6) {
        item.configJson = JSON.stringify(item.configJson)
      }
    })

    // 保存数据
    state.confirmLoading = true
    setItemSystemSettingApi(newParams)
      .then((response: any) => {
        console.log(response)
        ElMessage.success(response.message)
        getDataList()
      })
      .catch(() => {})
      .finally(() => {
        state.confirmLoading = false
      })
  }

  // 获取配置审批数据
  const getDataList = () => {
    state.showLoading = true
    getSystemSettingApi({}).then((response: any) => {
      if (response.code === 200) {
        response.data.forEach((item: any) => {
          item.itemList.forEach((event: any) => {
            if (event.configType === 5 || event.configType === 6) {
              event.configJson = JSON.parse(event.configJson || '{}')
            }
          })
        })
        console.log(response.data)
        state.dataList = response.data

        getCustomerListData(state.dataList)
        initializeSelectedIds(response.data)
      }
      setTimeout(() => {
        state.showLoading = false
      }, 1000)
    })
  }
</script>

<style scoped lang="scss">
  .components-container {
    width: 100%;
    height: 100%;
  }

  .contentBox {
    padding: 20px;
    height: calc(100vh - 84px);
    overflow: scroll;
    position: relative;
  }

  .titlebox {
    padding: 10px;
    background: #f0f0f0;
    margin-bottom: 20px;
    border-radius: 4px;
    color: #1890ff;
  }

  .required-field {
    color: #f56c6c;
    margin-left: 4px;
  }

  .is-required {
    .el-input__wrapper {
      box-shadow: 0 0 0 1px #f56c6c inset;
    }
  }
</style>
