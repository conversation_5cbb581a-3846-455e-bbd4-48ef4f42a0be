<!--
 * @Author: llm
 * @Date: 2024-02-01 17:41:54
 * @LastEditors: llm
 * @LastEditTime: 2025-01-25 11:41:27
 * @Description: 显示表单对象样式
-->
<template>
  <div class="app-container">
    <el-form ref="formRef" :model="formData" label-width="80px" :style="{ width: '90%' }" class="form-inline">
      <template v-for="(item, index) in tableConfig.tableItem">
        <div>
          <!-- 如果 dependsOn 不存在 或者存在 并且dependsOn中的条件同时满足并且canShow=true 后不存在 再显示-->
          <el-form-item :label="item.label + ':'" size="large" :prop="item.name" :key="index" label-width="120px">
            <div v-if="item.type === 'uploadImage' && formData[item.name]">
              <div v-for="(_item, _index) in formData[item.name]">
                <el-image
                  style="width: 100px; height: 100px"
                  :src="_item"
                  :zoom-rate="1.2"
                  :preview-src-list="[_item]"
                  :max-scale="7"
                  :min-scale="0.2"
                  :initial-index="_index"
                  fit="cover"
                />
              </div>
            </div>
            <div v-else>{{ formData[item.name]! || '无' }}</div>
          </el-form-item>
        </div>
      </template>
    </el-form>
    <!-- 查看照片 -->
    <PicDialogComponent :image-list="imageList" ref="picDialogComponent" />
  </div>
</template>
<script setup lang="ts">
  import PicDialogComponent from '@/components/PicDialogComponent/index.vue' //查看照片
  import { getcurrentUserMenuColumnlist } from '@/utils/common'
  // 状态管理依赖
  import { useFormStore } from '@/store/modules/form.js'
  import { useSideBarStore } from '@/store/modules/sideBar'
  import { globalRequestApi, globalRequestUrlApi } from '@/api/planManagement'
  import { useSettingsStore } from '@/store/modules/settings'

  const sideBarStore = useSideBarStore()
  const formStore = useFormStore()
  const settingsStore = useSettingsStore()
  const { proxy }: any = getCurrentInstance()
  const { routerParams, defaultTableIds, mergeFormData, storeFormParams } = storeToRefs(formStore)
  //优先使用列表中的携带的参数，再使用地址栏参数
  const routeParams =
    proxy.$sideBarStore.$state.btnMenuQuery && JSON.stringify(proxy.$sideBarStore.$state.btnMenuQuery) != '{}'
      ? proxy.$sideBarStore.$state.btnMenuQuery
      : routerParams.value

  //查看图片组件
  const picDialogComponent = ref()
  // 表单数据
  const formData = ref<any>({})
  //图片地址
  const imageList = ref<string[]>([])
  /**
   * 请求地址前缀
   */
  const requestUri = ref<string>()
  /**
   * 加载状态
   */
  const loading = ref(false)
  /**
   * 列表配置项
   */
  const tableConfig = reactive<TableConfig>({
    showHandleSelection: true,
    showSort: true,
    tableItem: [],
    operation: {
      label: '操作',
      items: [],
      width: '120px',
    },
  })
  onBeforeMount(async () => {
    document.title = settingsStore.systemName
    // 动态设置菜单数据列
    const { children, meta }: { children: MenuVO[]; meta: MetaVO } = await getcurrentUserMenuColumnlist(
      proxy.$sideBarStore.$state.btnMenuId ? proxy.$sideBarStore.$state.btnMenuId : proxy.$sideBarStore.$state.menuId,
    )

    //获取form表单数据列
    const dataColumn: any = meta.dataColumn
    //遍历dataColumn,formData的key = item.name,formData[key] = ''
    dataColumn.forEach((item: any) => {
      formData.value[item.name] = ''
    })
    requestUri.value = meta.uri
    tableConfig.tableItem = dataColumn
    handleQuery()
  })
  onBeforeUnmount(() => {
    sideBarStore.$patch((state) => {
      state.btnMenuId = ''
    })
  })
  /**
   * 查询
   */

  function handleQuery() {
    loading.value = true
    let params = {}
    if (JSON.stringify(storeFormParams.value) !== '{}') {
      params = { ...storeFormParams.value }
    }
    globalRequestApi(params, 'post', requestUri.value!).then(async (res) => {
      for (let key in formData.value) {
        formData.value[key] = res.data[key]
      }
    })
  }
  const previewImage = (url: string) => {
    imageList.value = [url]
    picDialogComponent.value.picDialogVisible = true
  }
</script>
<style scoped lang="scss">
  :deep(.el-form-item__label),
  :deep(.el-form-item__content) {
    font-size: 18px;
  }
</style>
