<template>
  <div class="app-container" v-loading="pageLoading">
    <el-card shadow="never" style="margin-bottom: 20px">
      <template v-if="listTopOperation && listTopOperation!.length > 0" #header>
        <!-- 按钮组 -->
        <button-group-component
          ref="topButtonGroupComponent"
          :buttonPermissionGroup="listTopOperation"
          :downloadButtonLoading="downloadButtonLoading"
          :exportButtonLoading="exportButtonLoading"
          :ids="ids"
          :importButtonLoading="importButtonLoading"
          :printButtonLoading="printButtonLoading"
          :printObj="printObj"
          :statementId="mergeFormData.statementId"
          :transportPlanLoading="transportPlanLoading"
          @batchRead="batchRead"
          @refreshPlanList="refreshPlanList"
          @defaultHandle="defaultHandle"
          @downloadExcelTemplate="downloadExcelTemplate"
          @exportExcel="exportExcelFun"
          @handleDelete="handleDelete"
          @importExcel="importExcelFun"
          @transportPlanConfig="transportPlanConfigFun"
          @transportPlanStart="transportPlanStartFun"
          @transportPlanCompare="transportPlanCompareFun"
        />
      </template>
      <div
        v-show="
          topQueryConfig.tableItem.filter((item: any) => item.query).length > 0 || listTopOperation?.some((item) => item.meta?.purpose === 'definePageHeader')
        "
      >
        <div ref="content" :class="{ statusText: state.status }" :style="{ height: state.status ? state.textHeight : 'auto' }">
          <topQueryGroupComponent
            ref="topQueryGroupComponentRef"
            :query-permission-group="topQueryConfig.tableItem"
            @handleSearchQuery="handleSearchQuery"
            @queryParams="getQueryData"
          />
        </div>
        <div v-if="state.isShowMore" style="padding-bottom: 20px; color: #606266; text-align: center">
          <div class="more">
            <el-button :icon="state.status ? 'ArrowDown' : 'ArrowUp'" link type="" @click="moreClick">
              {{ state.status ? '展开更多' : '收起' }}
            </el-button>
          </div>
        </div>
      </div>
      <TableComponent
        ref="tableComponentRef"
        :buttonPermissionGroup="listRightOperation"
        :loading="loading"
        :showAllSelection="showAllSelection"
        :tableConfig="tableConfig"
        :tableData="tableData"
        :highlightCurrentRow="true"
        @batchDelete="batchDeleteFun"
        @cancel="cancel"
        @confirmDialog="confirmDialog"
        @defaultHandle="defaultHandleTable"
        @deleteItem="deleteItemFun"
        @editAuth="editAuth"
        @fileDownload="fileDownload"
        @handleStatusChange="handleStatusChange"
        @openMenuDialog="openMenuDialog"
        @showPic="showPic"
        @showRowMenuDialog="showRowMenuDialog"
        @showRowObjectDialog="showRowObjectDialog"
        @updateItem="defaultHandle"
        @updatePassword="updatePassword"
        @handleCellClick="showRowDetail"
        @viewPlanLimit="viewPlanLimitFun"
        @viewInfo="viewInfoFun"
      />
      <pagination v-model:limit="queryParams.limit" v-model:page="queryParams.page" v-model:total="total" @pagination="_pagination" />
    </el-card>

    <el-card shadow="never">
      <right-bottom-component
        ref="rightBottomRef"
        :bottomButtomPermissionGroup="bottomButtomPermissionGroup"
        :bottomTabPermissionGroup="bottomTabPermissionGroup"
        :requestUri="btnRequestUri ?? requestUri!"
        :showAllSelection="showAllSelection"
        @changeTab="changeTab"
        @defaultHandleTable="defaultHandleTable"
        @deleteItem="deleteItemFun"
        @batchDelete="bottomBatchSlectFun"
        @downloadExcelTemplate="downloadButtomTabExcelTemplate"
        @editBottomItem="editBottomItem"
        @showRowMenuDialog="showRowMenuDialog"
        @exportBottomTabListExcel="exportBottomTabListExcel"
        @handleQueryBottomList="handleQueryBottomList"
        @importButtomTabExcelFun="importButtomTabExcelFun"
        @samePlanCompare="samePlanCompareFun"
        @crossPlanCompare="crossPlanCompareFun"
      />
      <pagination
        v-model:limit="bottomListQueryParams.limit"
        v-model:page="bottomListQueryParams.page"
        v-model:total="rightBottomListTotal"
        @pagination="handleQueryBottomList"
      />
    </el-card>
    <!-- 新增or编辑组件 -->
    <form-dialog
      v-if="dialog.visible"
      ref="formDialogRef"
      :btnMenu="btnMenu"
      :currentRow="currentRow"
      :dataColumn="operationColumn ? operationColumn : tableConfig.tableItem"
      :dialog="dialog"
      :isEdit="isEdit"
      :refreshMenuCount="btnMenu?.meta?.form?.refreshMenuCount"
      :requestUri="btnRequestUri ?? requestUri"
      @clearFormColumn="clearFormColumn"
      @closeDialog="closeDialog"
      @formData="getFormData"
      @handleSubmit="handleSubmit"
    />
    <!-- 查看照片 -->
    <PicDialogComponent ref="picDialogComponent" :image-list="imageList" />
    <!-- 列表项详情弹窗 -->
    <el-dialog
      :draggable="true"
      v-model="detailDialogVisible.visible"
      :title="detailDialogVisible.title"
      align-center
      destroy-on-close
      :width="detailDialogVisible.dialogWidth ?? '80%'"
      @closed="closeDetailDialogVisible"
    >
      <el-scrollbar class="formClass" max-height="90vh">
        <BasePage1 />
      </el-scrollbar>
    </el-dialog>
    <!-- 对象数据弹窗 -->
    <el-dialog
      :draggable="true"
      v-model="objectDialogVisible.visible"
      :title="objectDialogVisible.title"
      align-center
      destroy-on-close
      width="50%"
      @closed="closeDetailDialogVisible"
    >
      <baseObjectPage />
    </el-dialog>
    <!-- 菜单弹窗 -->
    <menuDialog
      ref="menuDialogRef"
      :buttonPermissionGroup="menuDialogPermissionGroup"
      :checkedUser="userInfo"
      :current-menu-column-list="currentMemuColumnList"
      :menu-dialog-visible="menuDialogVisible"
      :menu-list="menuList"
      @getListMenuOptions="getcurrentLoginUserMenuColumnlist"
      @selectMenuColumn="selectMenuColumn"
    />
  </div>
</template>
<script lang="ts" setup>
  import BasePage1 from '@/views/Pages/basePage1.vue'
  import baseObjectPage from '@/views/Pages/baseObjectPage.vue'
  import buttonGroupComponent from '@/components/TopButtonGroupComponent/index.vue'
  import TableComponent from '@/components/TableComponent/index.vue'
  import pagination from '@/components/Pagination/index.vue'
  import formDialog from '@/components/FormDialogComponent/index.vue' //表单弹窗
  import PicDialogComponent from '@/components/PicDialogComponent/index.vue' //查看照片
  import { CustomTableHeaderVO, RoleForm, RolePageVO } from '@/api/authorityManagement/RoleManagement/types'
  import {
    composeRequestParams,
    composeRequestParamsMultiRow,
    downloadFileGlobalFun,
    getcurrentUserMenuColumnlist,
    getSelectOptions,
    resetFormGlobalFun,
    switchChangeGlobalFun,
  } from '@/utils/common'
  import rightBottomComponent from '@/views/customerCenter/components/rightBottomComponent.vue' //下部
  import {
    addItemApi,
    batchDeleteApi,
    bottomTableList,
    deleteItemApi,
    downloadTemplate,
    downloadTemplateUrl,
    exportExcel,
    getDetailById,
    getListPage,
    globalDownloadTemplate,
    globalExportExcel,
    importFileGlobalBtnUriFun,
    importFileGlobalFun,
    updateItemApi,
  } from '@/api/auth'
  import { FormColumn, UploadImageVO } from '@/types/global'
  import { boardApplyCancel, getMenuCount, globalRequestApi, globalRequestUrlApi } from '@/api/planManagement'
  import { taskStartPlanApi, getTaskSelectOption } from '@/api/transportPlan'
  // 状态管理依赖
  import { useFormStore } from '@/store/modules/form.js'
  import { useSideBarStore } from '@/store/modules/sideBar'
  import { useSettingsStore } from '@/store/modules/settings'
  // *****************************以下是个别操作按钮弹窗导入代码，需要根据业务需求修改**********************
  import { refreshCheckGetApi, refreshCheckPostApi } from '@/api/shipmentManagement'
  import { dayjs } from 'element-plus'
  import { CheckedMenuColumnVO, UserForm, UserPageVO } from '@/api/authorityManagement/CorporateAccountManagement/PersonalManagement/types'
  import {
    currentLoginUserMenuColumnlist,
    distributeMenuColumn,
    updateUserPassword,
    userMenulist,
  } from '@/api/authorityManagement/CorporateAccountManagement/PersonalManagement'
  //菜单弹窗数据start
  import menuDialog from '@/views/authorityManagement/CorporateAccountManagement/PersonalManagement/components/menuDialog.vue'
  import { typeConversion } from '@/utils'
  import bus from '@/utils/bus'
  import defaultSettings from '@/settings'
  // *****************************以上是个别操作按钮弹窗导入代码，需要根据业务需求修改**********************
  const sideBarStore = useSideBarStore()
  const settingsStore = useSettingsStore()
  const formStore = useFormStore()
  const router = useRouter()
  const query = reactive(router.currentRoute.value.query)
  const params = reactive(router.currentRoute.value.params)
  //如果地址栏有携带的参数并且不包含time，说明是工程外部跳转，则存到store中
  if (JSON.stringify(query) != '{}' && !Object.keys(query).includes('time')) {
    formStore.$patch((state) => {
      state.routerParams = query
    })
  }
  const { routerParams, defaultTableIds, mergeFormData, storeFormParams } = storeToRefs(formStore)
  const { mergeDialogFormParams } = storeToRefs(sideBarStore)
  const { proxy }: any = getCurrentInstance()
  //优先使用列表中的携带的参数，再使用地址栏参数
  const routeParams =
    proxy.$sideBarStore.$state.btnMenuQuery && JSON.stringify(proxy.$sideBarStore.$state.btnMenuQuery) != '{}'
      ? proxy.$sideBarStore.$state.btnMenuQuery
      : routerParams.value
  const content = ref()
  const tabIndexStatus = ref<boolean>(false)
  const topQueryGroupComponentRef = ref()
  const state = reactive({
    isShowMore: false, // 控制展开更多的显示与隐藏
    textHeight: '', // 框中内容的高度
    status: false, // 内容状态是否打开
  })
  const moreClick = () => {
    state.status = !state.status
  }
  //查看图片组件
  const picDialogComponent = ref()
  const formDialogRef = ref(ElForm)
  const tableComponentRef = ref()
  /**
   * 查看列表项详情弹窗
   */
  const detailDialogVisible = reactive<DialogOption>({
    visible: false,
    title: '详情',
    dialogWidth: '80%',
  })
  /**
   * 查看列表项对象弹窗
   */
  const objectDialogVisible = reactive<DialogOption>({
    visible: false,
    title: '详情',
  })
  /**
   * 查看图片弹窗
   */
  const picDialogVisible = ref<boolean>(false)
  /**
   * 当前菜单的uri
   */
  const currentMenuUri = ref<string>()
  /**
   * menu
   */
  const btnMenu = ref<MenuVO>()
  /**
   * 按钮组
   */
  const btnGroup = ref<MenuVO[]>([])
  /**
   * 选择的列表项数据
   */
  const selectTableColumn = ref<any[]>([])
  /**
   * 按钮下的表单
   */
  const operationColumn = ref<any>()
  /**
   * 按钮的请求地址前缀
   */
  const btnRequestUri = ref<string | null>()
  //图片地址
  const imageList = ref<string[]>([])
  /**
   * 导出加载动画
   */
  const exportButtonLoading = ref<boolean>()
  /**
   * 导入加载动画
   */
  const importButtonLoading = ref<boolean>()
  /**
   * 下载加载动画
   */
  const downloadButtonLoading = ref<boolean>()

  /**
   * 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   */
  const buttonPosition = ref<string>()
  /**
   * 请求地址前缀
   */
  const requestUri = ref<string>()
  /**
   * 加载状态
   */
  const loading = ref(false)
  /**
   * 全局pageLoading
   */
  const pageLoading = ref(false)
  /**
   * 选中的列表ids
   */
  const ids = ref<string[]>([])

  /**
   * 选中的当前行
   */
  const currentItem = ref<TableItem>()

  /**
   * 勾选需要删除的客户ids
   */
  const customerIds = ref<string[]>()

  /**
   * 总数
   */
  const total = ref(0)
  /**
   * 当前是否为编辑状态
   */
  const isEdit = ref(false)
  const listRightOperation = ref<MenuVO[]>() //列表右侧按钮权限
  const listTopOperation = ref<MenuVO[]>() //列表顶部按钮权限
  /**
   * 新增or编辑弹窗
   */
  const dialog = reactive<DialogOption>({
    visible: false,
  })

  /**
   * 右下区域
   */
  const rightBottomRef = ref()
  /**
   * 右下区域tab
   */
  const bottomTabPermissionGroup = ref<MenuVO[]>([])
  /**
   * 右下区域tab中的按钮组
   */
  const bottomButtomPermissionGroup = ref<MenuVO[]>([])
  /**
   * 选中的tab
   */
  const currentTab = ref<MenuVO>()
  /**
   * 当前tab的uri
   */
  const currentTabUri = ref<string>()

  const queryParams = reactive<any>({
    page: 1,
    limit: defaultSettings.globalLimit,
  })
  /**
   * 底部列表查询条件
   */
  const bottomListQueryParams = reactive<any>({
    page: 1,
    limit: defaultSettings.globalLimit,
  })
  /**
   * 右侧列表总条数
   */
  const rightBottomListTotal = ref<number>(0)

  // ------------------顶部按钮-------------------
  const planSetUpParamsDialogRef = ref() // 设置规划参数
  const comparisonPlansDialogRef = ref() //方案对比

  /**
   * 规划方案-按钮loading
   */
  const transportPlanLoading = ref<boolean>()

  // 刷新列表
  const refreshPlanList = () => {
    resetQuery() //刷新列表
  }
  //查看规划失败原因
  const viewInfoDialogRef = ref()

  // 设置规划参数
  const transportPlanConfigFun = (purpose: string, position?: string, menu?: MenuVO) => {
    planSetUpParamsDialogRef.value.dialogVisible.title = menu?.meta.title
    planSetUpParamsDialogRef.value.newMenuId = menu!.meta!.form?.menuId!
    planSetUpParamsDialogRef.value.state.currentId = routeParams.taskId //孝东-设置没有relationId 需要传 taskId
    planSetUpParamsDialogRef.value.state.origin = 'provinceLimit'
    planSetUpParamsDialogRef.value.state.showBtnGroup = true //显示按钮组
    planSetUpParamsDialogRef.value.dialogVisible.visible = true
  }
  // 规划方案
  const transportPlanStartFun = (purpose: string, position?: string, menu?: MenuVO) => {
    if (routeParams && routeParams.taskId) {
      transportPlanLoading.value = true
      taskStartPlanApi({ taskId: routeParams.taskId })
        .then((res) => {
          resetQuery() //刷新列表
          transportPlanLoading.value = false
        })
        .catch((err) => {
          transportPlanLoading.value = false
        })
    }
  }
  // 方案对比
  const transportPlanCompareFun = (purpose: string, position?: string, menu?: MenuVO) => {
    if (selectTableColumn.value && selectTableColumn.value.length > 1) {
      comparisonPlansDialogRef.value.dialogVisible.title = menu?.meta.title
      comparisonPlansDialogRef.value.dialogVisible.visible = true
      comparisonPlansDialogRef.value.state.tableData = selectTableColumn.value
      comparisonPlansDialogRef.value.state.type = 'topBtn'
    } else {
      ElMessage.warning('请至少选择2个需要对比的方案！')
    }
  }

  // 同方案对比
  const samePlanCompareFun = (purpose: string, position?: string, menu?: MenuVO, items?: any) => {
    if (items && items.length > 1) {
      comparisonPlansDialogRef.value.dialogVisible.title = menu?.meta.title
      comparisonPlansDialogRef.value.dialogVisible.visible = true
      comparisonPlansDialogRef.value.state.tableData = items
      comparisonPlansDialogRef.value.state.type = 'bottomBtn'
    } else {
      ElMessage.warning('请至少选择2个需要对比的方案！')
    }
  }
  // 跨方案对比
  const crossPlanCompareFun = (purpose: string, position?: string, menu?: MenuVO, items?: any) => {
    if (items && items.length == 1) {
      if (routeParams && routeParams.taskId) {
        getTaskSelectOption({ taskId: routeParams.taskId })
          .then((res) => {
            if (res && res.data) {
              comparisonPlansDialogRef.value.schemeListData = res.data
            } else {
              comparisonPlansDialogRef.value.schemeListData = []
            }
          })
          .catch((err) => {})
      }
      // 先清除
      comparisonPlansDialogRef.value.formData.ids = []
      comparisonPlansDialogRef.value.state.tableData = []
      comparisonPlansDialogRef.value.state.type = 'bottomSpanBtn'
      comparisonPlansDialogRef.value.newTaskId = routeParams.taskId
      comparisonPlansDialogRef.value.dialogVisible.title = menu?.meta.title
      comparisonPlansDialogRef.value.dialogVisible.visible = true
      comparisonPlansDialogRef.value.formData.month = items[0].month
    } else {
      ElMessage.warning('跨方案对比请选择1个对比方案！')
    }
  }

  // 点击查看约束调整
  const viewPlanLimitFun = (item: any, position: string, menu: MenuVO) => {
    planSetUpParamsDialogRef.value.dialogVisible.title = menu?.meta.title
    planSetUpParamsDialogRef.value.newMenuId = menu!.meta!.form?.menuId!
    planSetUpParamsDialogRef.value.state.origin = 'viewPlanLimit'
    planSetUpParamsDialogRef.value.state.currentId = item.id
    planSetUpParamsDialogRef.value.state.showBtnGroup = false //显示按钮组
    planSetUpParamsDialogRef.value.dialogVisible.visible = true
  }

  //查看规划失败弹窗
  const viewInfoFun = (row: any, position: string, menu: MenuVO) => {
    viewInfoDialogRef.value.dialogVisible.visible = true
    viewInfoDialogRef.value.dialogVisible.title = menu?.meta.title
    viewInfoDialogRef.value.state.currentItem = row
  }

  // ------------------顶部按钮-------------------

  //选中当前table行
  const showRowDetail = (row: any) => {
    currentItem.value = row
    //ids 赋值 为当前客户id
    customerIds.value = [row.id!]
    if (currentTab.value?.menuId) {
      getTabPermissionGroup(currentTab.value?.menuId)
    }
    if (bottomTabPermissionGroup.value.length > 0 && bottomTabPermissionGroup.value[0].meta?.uri && row.id) {
      //默认获取当前版本下的汇总
      getBottomTableList(row.id, currentTab.value?.meta ? currentTab.value?.meta?.uri! : bottomTabPermissionGroup.value[0].meta?.uri, true)
    }
  }

  /**
   * 切换tab
   * @param tabItem
   */
  const changeTab = (tabItem: MenuVO) => {
    //初始化page
    rightBottomRef.value.listQueryParams.page = 1
    rightBottomRef.value.resetQuery()
    currentTab.value = tabItem
    if (tabItem.menuId) {
      getTabPermissionGroup(tabItem.menuId)
    }
    if (tabItem.meta?.uri) {
      currentTabUri.value = tabItem.meta?.uri
      tabIndexStatus.value = true
      getBottomTableList(currentItem.value?.id!, tabItem.meta?.uri, true)
    }
  }

  /**
   * 获取table列表数据
   * @param relationId id
   * @param uri 请求地址
   * @param isResetQuery 是否重置查询条件
   */
  const getBottomTableList = (relationId: string, uri: string, isResetQuery: boolean = false) => {
    rightBottomRef.value.listLoading = true
    if (isResetQuery) {
      //循环bottomListQueryParams删除bottomListQueryParams 所有的参数
      for (let key in bottomListQueryParams) {
        if (key !== 'page' && key !== 'limit') {
          delete bottomListQueryParams[key]
        }
      }
      bottomListQueryParams.page = 1
    }
    bottomListQueryParams.relationId = relationId
    bottomTableList(bottomListQueryParams, uri)
      .then((res) => {
        const { data } = res
        rightBottomRef.value.tableData = data.rows
        rightBottomRef.value.total = rightBottomListTotal.value = data.total
        rightBottomRef.value.listLoading = false
      })
      .catch((err) => {
        rightBottomRef.value.listLoading = false
      })
  }

  /**
   * 切换tab 重新获取权限
   * @param menuId 菜单id
   */
  const getTabPermissionGroup = async (menuId: string) => {
    const { children, meta }: { children: MenuVO[]; meta: MetaVO } = await getcurrentUserMenuColumnlist(menuId)
    bottomButtomPermissionGroup.value = children
    currentTabUri.value = meta.uri

    //获取form表单数据列
    const dataColumn: FormColumn[] = await getSelectOptions(meta.dataColumn, null, '', 'topQuerySelect')
    rightBottomRef.value.tableConfig.tableItem = dataColumn
    rightBottomRef.value.tableConfig.isKey = !rightBottomRef.value.tableConfig.isKey
    dataColumn.forEach((item: TableItem, index: number) => {
      //由于服务端图片校验规则下发后是字符串，不能转换，所以需要处理
      if (item.form?.imageOption && item.form?.imageOption!.required) {
        //图片校验
        const validateImage = (rule: any, value: any, callback: any) => {
          //验证器
          if (!formDialogRef.value.formData[item.form?.name!]) {
            //为true代表图片在  false报错
            callback(new Error('请上传图片'))
          } else {
            callback()
          }
        }
        const imageRules = [{ required: true, validator: validateImage, trigger: 'change' }]
        item.form.rules = imageRules
      }
      //如果item.defaultValue存在，则给表单赋值
      if (item.query?.defaultValue) {
        queryParams[item.query?.name!] = topQueryGroupComponentRef.value.queryParams[item.query?.name!] = item.query?.defaultValue
      }
    })
    rightBottomRef.value.tableConfig.operation = meta.operation
    //遍历routeParams对象，queryParams[key] = routeParams[key]
    for (const key in routeParams) {
      if (Object.prototype.hasOwnProperty.call(routeParams, key)) {
        //如果属性中包含range字符串，则把首字母大写，然后拆分2个，分别拼上start 和 end ,并删除原有属性
        if (key!.indexOf('Range') !== -1 && (routeParams[key] as any)) {
          const startName = 'start' + key.charAt(0).toUpperCase() + key.slice(1)
          const endName = 'end' + key.charAt(0).toUpperCase() + key.slice(1)
          queryParams[startName] = routeParams[key]![0]
          queryParams[endName] = routeParams[key]![1]
          delete queryParams[key]
        } else {
          queryParams[key] = routeParams[key]
        }
        topQueryGroupComponentRef.value.queryParams[key] = routeParams[key]
        //初始化搜索条件
        // formStore.setSearchParams({});
      }
    }
  }

  /**
   * 分页查询底部列表数据
   */
  const handleQueryBottomList = (params: any) => {
    // bottomListQueryParams.limit = params.limit;
    // bottomListQueryParams.page = params.page;
    //遍历params
    for (const key in params) {
      if (params.hasOwnProperty(key)) {
        bottomListQueryParams[key] = params[key]
      }
    }
    getBottomTableList(currentItem.value?.id!, currentTab.value?.meta?.uri!)
  }

  /**
   * 下载导入模版
   */
  const downloadButtomTabExcelTemplate = useThrottleFn((purpose: string, position?: string, menu?: MenuVO) => {
    const requestApi = menu?.meta?.uri ? downloadTemplate : globalDownloadTemplate
    requestApi(menu?.meta?.uri ?? currentMenuUri.value!)
      .then(async (res) => {
        downloadButtonLoading.value = true
        await downloadFileGlobalFun(res)
        downloadButtonLoading.value = false
        ElMessage.success('操作成功')
      })
      .catch(() => {
        downloadButtonLoading.value = false
      })
  }, 3000)

  /**
   * 导出底部tab列表Excel
   * @param purpose 按钮类型
   * @param position 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   * @param menu 按钮信息
   */
  const exportBottomTabListExcel = useThrottleFn((purpose: string, position?: string, menu?: MenuVO) => {
    let requestApi = menu!.meta!.uri ? globalExportExcel : exportExcel
    requestApi(bottomListQueryParams, menu!.meta!.uri ?? currentMenuUri.value!)
      .then(async (res) => {
        exportButtonLoading.value = true
        await downloadFileGlobalFun(res)
        exportButtonLoading.value = false
        ElMessage.success('操作成功')
      })
      .catch(() => {
        exportButtonLoading.value = false
      })
  }, 3000)

  /**
   * 导入Excel
   * @param file 文件
   */
  const importButtomTabExcelFun = useThrottleFn((file: any, meta?: MetaVO) => {
    importButtonLoading.value = true
    const Api = meta?.uri ? importFileGlobalBtnUriFun : importFileGlobalFun
    Api(file, meta?.uri ?? currentTabUri.value!)
      .then(async (res) => {
        importButtonLoading.value = false
        ElMessage.success('导入成功')
        getBottomTableList(currentItem.value?.id!, currentTabUri.value!)
      })
      .catch(() => {
        importButtonLoading.value = false
      })
  }, 3000)

  /**
   * 修改底部列表项
   * @param row 当前行数据
   * @param position 按钮位置
   */
  const editBottomItem = async (row: TableItem, position: string) => {
    dialog.visible = true
    isEdit.value = true
    dialog.title = '修改'
    buttonPosition.value = position
    //初始化表单
    const initFormData = resetFormGlobalFun(rightBottomRef.value.tableConfig.tableItem!)
    let form: MenuVO = bottomButtomPermissionGroup.value.find((item: MenuVO) => item.meta?.purpose === 'topEdit') || ({} as MenuVO)
    if (form.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(form.meta!.dataColumn)
      requestUri.value = form.meta!.uri! ?? currentMenuUri.value!
    } else {
      requestUri.value = currentTabUri.value! ?? currentMenuUri.value!
      operationColumn.value = await refreshFormSelectOptions(rightBottomRef.value.tableConfig.tableItem)
    }
    nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, row)
    })
  }

  /**
   * meta
   */
  const metaInfo = ref<MetaVO>()
  /********************************自定义表头部分 start********************************/

  /**
   * 获取设置过的数据列
   */
  function getDefineColumns(): Promise<CustomTableHeaderVO[]> {
    return new Promise((resolve, reject) => {
      globalRequestApi({}, 'get', requestUri.value + '/table/header/user').then(async (res) => {
        const { data } = res
        resolve(data)
      })
    })
  }

  /********************************自定义表头部分 end********************************/

  /**
   * 列表配置项
   */
  const tableConfig = reactive<TableConfig>({
    showHandleSelection: true,
    showSort: true,
    tableItem: [],
    operation: {
      label: '操作',
      items: [],
      width: '120px',
    },
  })
  /**
   * 顶部搜索配置项
   */
  const topQueryConfig = reactive<TableConfig>({
    tableItem: [],
    operation: undefined,
  })
  const printButtonLoading = ref<boolean>(false)
  const showAllSelection = ref<boolean>(true)
  const topButtonGroupComponent = ref()

  const printObj = reactive({
    id: 'PrintView',
    popTitle: '',
    extraCss: 'https://cdn.bootcdn.net/ajax/libs/animate.css/4.1.1/animate.compat.css, https://cdn.bootcdn.net/ajax/libs/hover.css/2.3.1/css/hover-min.css',
    extraHead: '<meta http-equiv="Content-Language"content="zh-cn"/>',
    async previewBeforeOpenCallback(vue: any) {},
    beforeOpenCallback(vue: { printLoading: boolean }) {},
    openCallback(vue: { printLoading: boolean }) {
      vue.printLoading = false
    },
    closeCallback(vue: any) {},
    clickMounted() {},
  })

  /**
   * 列表数据
   */
  const tableData = ref<RolePageVO[]>()
  onBeforeMount(async () => {
    document.title = settingsStore.systemName
    // 动态设置菜单数据列
    const { children, meta }: { children: MenuVO[]; meta: MetaVO } = await getcurrentUserMenuColumnlist(
      proxy.$sideBarStore.$state.btnMenuId ? proxy.$sideBarStore.$state.btnMenuId : proxy.$sideBarStore.$state.menuId,
    )
    metaInfo.value = meta
    //控制多选复选框按钮显示隐藏
    showAllSelection.value = meta.ext?.tableMultiSelect ?? true
    //获取当前菜单分页条数
    queryParams.limit = meta.ext?.pageLimit || defaultSettings.globalLimit
    btnGroup.value = children
    //右侧按钮组
    listRightOperation.value = children.filter((item: MenuVO) => item.meta?.position === 'listRight')
    //顶部按钮组
    listTopOperation.value = children.filter((item: MenuVO) => item.meta?.position === 'listTop')

    //获取右下区域tab组
    bottomTabPermissionGroup.value = children.filter((item: MenuVO) => item.meta?.type === 3)

    //如果bottomTabPermissionGroup.length>0,取第一个tab的menuId,获取数据列
    if (bottomTabPermissionGroup.value.length > 0 && bottomTabPermissionGroup.value[0].menuId) {
      //默认当前tab为tab中的第一项
      currentTab.value = bottomTabPermissionGroup.value[0]
      await getTabPermissionGroup(bottomTabPermissionGroup.value[0].menuId)
    }

    //数据列权限分配弹窗中的操作按钮
    menuDialogPermissionGroup.value = listRightOperation.value?.filter((item: MenuVO) => item.meta?.purpose === 'editAuth')
    //获取form表单数据列
    const dataColumn: any = meta.dataColumn
    dataColumn.forEach((item: TableItem, index: number) => {
      //由于服务端图片校验规则下发后是字符串，不能转换，所以需要处理
      if (item.form?.imageOption && item.form?.imageOption!.required) {
        //图片校验
        const validateImage = (rule: any, value: any, callback: any) => {
          //验证器
          if (!formDialogRef.value.formData[item.form?.name!]) {
            //为true代表图片在  false报错
            callback(new Error('请上传图片'))
          } else {
            callback()
          }
        }
        const imageRules = [{ required: true, validator: validateImage, trigger: 'change' }]
        item.form.rules = imageRules
      }
      //如果item.defaultValue存在，则给表单赋值
      if (item.query?.defaultValue) {
        queryParams[item.query?.name!] = item.query?.defaultValue
        topQueryGroupComponentRef.value.queryParams[item.query?.name!] = item.query.option?.multiple
          ? item.query?.defaultValue.split(',')
          : item.query?.defaultValue
      }
      // 存在默认值
      if (item.query?.showDefaultDate && item.query?.format) {
        queryParams[item.query?.name!] = dayjs().format(item.query?.format)
        topQueryGroupComponentRef.value.queryParams[item.query?.name!] = dayjs().format(item.query?.format)
      }
    })
    requestUri.value = meta.uri

    //如果listTopOperation.value中存在purpose=customTableBusinessHeader或者financeReport,则获取自定义表头
    if (
      listTopOperation.value.findIndex((item: MenuVO) => item.meta?.purpose === 'customTableBusinessHeader') !== -1 ||
      listTopOperation.value.findIndex((item: MenuVO) => item.meta?.purpose === 'financeReport') !== -1
    ) {
      const defineColumns = await getDefineColumns()

      //tableConfig.tableItem = meta.dataColumn中name与defineColumns中columnName相同的项集合，并按照columnName顺序排列
      tableConfig.tableItem = dataColumn
        .filter((item: TableItem) => defineColumns.findIndex((defineItem: any) => defineItem.columnName === item.name) !== -1)
        .sort((a: TableItem, b: TableItem) => {
          return (
            defineColumns.findIndex((defineItem: any) => defineItem.columnName === a.name) -
            defineColumns.findIndex((defineItem: any) => defineItem.columnName === b.name)
          )
        })
    } else {
      tableConfig.tableItem = dataColumn
    }
    topQueryConfig.tableItem = dataColumn
    //如果meta.operation=false 则不展示列表右侧操作列
    if (!meta.operation) {
      tableConfig.operation = undefined
    }

    //遍历routeParams对象，queryParams[key] = routeParams[key]
    for (const key in routeParams) {
      if (Object.hasOwn(routeParams, key)) {
        //遍历dataColumn,如果key===item.query?.name,并且item.query?.type.indexOf('range') !== -1
        const columnItem = dataColumn.find((item: TableItem) => {
          return key === item.query?.name && item.query?.type.indexOf('range') !== -1
        })
        //如果属性中包含range字符串，则把首字母大写，然后拆分2个，分别拼上start 和 end ,并删除原有属性
        if (columnItem && routeParams[key]) {
          const startName = 'start' + key.charAt(0).toUpperCase() + key.slice(1)
          const endName = 'end' + key.charAt(0).toUpperCase() + key.slice(1)
          queryParams[startName] = routeParams[key].split(',')![0]
          queryParams[endName] = routeParams[key].split(',')![1]
        } else {
          queryParams[key] = routeParams[key]
        }
        const item = topQueryConfig.tableItem.find((item) => routeParams[key] && item.query?.name === key && item.query?.option?.multiple)
        if (item) {
          topQueryGroupComponentRef.value.queryParams[key] = routeParams[key] ? routeParams[key].split(',') : []
        } else if (columnItem) {
          topQueryGroupComponentRef.value.queryParams[key] = routeParams[key] ? routeParams[key].split(',') : []
        } else {
          topQueryGroupComponentRef.value.queryParams[key] = routeParams[key]
        }
        //初始化搜索条件
        // formStore.setSearchParams({});
      }
    }
    // 计算展开更多内容超出显示
    nextTick(() => {
      // 这里具体行数可依据需求自定义
      let lineHeight = 61 * 2
      state.textHeight = `${lineHeight}px`

      if (content.value.offsetHeight > lineHeight) {
        state.isShowMore = true
        state.status = true
      } else {
        state.isShowMore = false
        state.status = false
      }
    })
    nextTick(() => {
      handleQuery()
    })
  })
  onBeforeUnmount(() => {
    sideBarStore.$patch((state) => {
      state.btnMenuId = ''
    })
  })
  /**
   * 关闭弹窗并清除之前的全局查询条件
   */
  const closeDetailDialogVisible = () => {
    sideBarStore.$patch((state) => {
      state.btnMenuQuery = {}
    })
  }
  /**
   * 分页
   */
  const _pagination = () => {
    handleQuery()
  }

  /**
   * 查询
   */

  function handleQuery() {
    loading.value = true
    let newParams = JSON.parse(JSON.stringify(queryParams))
    getListPage(newParams, requestUri.value!)
      .then(async ({ data }) => {
        tableData.value = data.rows
        total.value = data.total
        if (data.rows.length > 0 && data.rows[0].id) {
          //默认customerIds为客户列表的第一条数据id
          customerIds.value = [data.rows[0].id]
          //默认获取当前第一条数据
          currentItem.value = data.rows[0]
          //默认将左侧菜单第一条数据存到pinia中，用于后面数据调用里面的属性
          formStore.$patch((state) => {
            state.selectLeftTreeRows.map((item) => {
              if (item.menuId === proxy.$sideBarStore.$state.menuId) {
                item.selectLeftTreeRow = data.rows[0]
              }
            })
          })
          if (bottomTabPermissionGroup.value.length > 0 && bottomTabPermissionGroup.value[0].meta?.uri) {
            // 定义状态
            if (!tabIndexStatus.value) {
              currentTabUri.value = bottomTabPermissionGroup.value[0].meta?.uri
            } else {
              tabIndexStatus.value = true
            }
            //默认获取当前客户下的用户列表
            getBottomTableList(data.rows[0].id, currentTabUri.value!)
          }
        } else {
          currentItem.value = {}
          rightBottomRef.value.tableData = []
          rightBottomRef.value.total = rightBottomListTotal.value = 0
          rightBottomRef.value.listLoading = false
        }
        //如果defaultTableIds有值，则需要勾选默认的列表项
        if (tableData.value && tableData.value.length > 0) {
          if (defaultTableIds.value.ids) {
            ids.value = defaultTableIds.value.ids
            const listNew = [] as any // 定义默认勾选的行数据
            //遍历tableData,找到id在defaultTableIds.value.ids存在的，并将行数据push到listNew中
            tableData.value.forEach((item) => defaultTableIds.value.ids.includes(item.id) && listNew.push(item))
            //清空列表所有选中项
            nextTick(() => {
              listNew.forEach((item: any) => {
                tableComponentRef.value.dataTableRef.toggleRowSelection(item, true)
              })
            })
          }
        }
        await refreshQuerySelectOptions()
      })
      .finally(() => {
        loading.value = false
        sideBarStore.$patch((state) => {
          // 弹窗关闭后，清空菜单id和菜单查询条件，防止下次打开时，数据有误，
          // 示例：列表菜单，点击详情，弹窗打开，弹窗数据再次页面跳转，导致列表数据有误（上汽大众 POC 送货单查询）
          state.btnMenuId = ''
          state.btnMenuQuery = {}
          // state.mergeDialogFormParams = {};
          // state.storeDialogFormParams = {};
        })
      })
  }

  /**
   * 更新查询条件下拉
   */
  const refreshQuerySelectOptions = async () => {
    //重新获取form表单数据列，刷新筛选下拉项
    topQueryConfig.tableItem = await getSelectOptions(metaInfo.value!.dataColumn, null, '', 'topQuerySelect')
  }
  /**
   * 更新表单下拉
   */
  const refreshFormSelectOptions = async (dataColumn: FormColumn[]) => {
    //重新获取form表单数据列，刷新筛选下拉项
    return new Promise(async (resolve, reject) => {
      const newDataColumn = getSelectOptions(dataColumn, null, '', 'formSelect')
      resolve(newDataColumn)
    })
  }

  /**
   * 重置查询
   */
  function resetQuery() {
    queryParams.page = 1
    handleQuery()
  }

  /**
   * 表单提交
   */
  async function handleSubmit(formData: RoleForm, request: BtnRequestVO, isRefreshMenuCount: boolean) {
    loading.value = true
    //如果mergeFormData有值，则合并到formData中
    if (JSON.stringify(mergeFormData.value) !== '{}') {
      formData = { ...formData, ...mergeFormData.value }
    }
    //如果storeFormParams有值，则合并到formData中
    if (JSON.stringify(storeFormParams.value) !== '{}') {
      formData = { ...formData, ...storeFormParams.value }
    }
    if (JSON.stringify(sideBarStore.$state.mergeDialogFormParams) !== '{}') {
      formData = { ...formData, ...sideBarStore.$state.mergeDialogFormParams }
    }
    if (JSON.stringify(sideBarStore.$state.storeDialogFormParams) !== '{}') {
      formData = { ...formData, ...sideBarStore.$state.storeDialogFormParams }
    }
    //如果是对象则使用公共请求
    if (request?.constructor === Object) {
      if (request.method === 'put' || request.method === 'PUT') {
        await updateItemApi(formData.id as string, formData, request.uri!)
          .then((res: any) => {
            ElMessage.success(res.message)
            resetQuery()
            closeDialog()
          })
          .finally(() => (loading.value = false))
      } else {
        await globalRequestApi(formData, request.method!, request.uri!)
          .then(async (res: any) => {
            //request.secondDialog.result=true->进行二次弹窗(运力指派用到)
            if (request.secondDialog && request.secondDialog.result && typeof res.data === 'object') {
              if (res.data.length > 0) {
                // 合并运单
              } else {
                await globalRequestApi(formData, request.secondDialog.method!, request.secondDialog.uri!).then(async (res: any) => {
                  ElMessage.success(res.message)
                  resetQuery()
                  closeDialog()
                })
              }
            } else {
              ElMessage.success(res.message)
              resetQuery()
              closeDialog()
            }
          })
          .finally(() => {
            loading.value = false
          })
      }
      //否则使用老方法
    } else {
      const _id = formData.id
      if (_id) {
        await updateItemApi(_id, formData, request as any)
          .then((res: any) => {
            Reflect.deleteProperty(formData, 'id')
            ElMessage.success(res.message)
            resetQuery()
            closeDialog()
          })
          .finally(() => (loading.value = false))
      } else {
        await addItemApi(formData, request as any)
          .then((res: any) => {
            ElMessage.success(res.message)
            resetQuery()
            closeDialog()
          })
          .finally(() => (loading.value = false))
      }
    }
    nextTick(() => {
      if (isRefreshMenuCount) refreshMenuCount()
    })
  }

  /**
   * 关闭弹窗
   */
  function closeDialog() {
    dialog.visible = false
    //刷新列表
    resetQuery()
  }

  /**
   * 重置表单
   */
  function resetForm() {
    formDialogRef.value.resetForm()
  }

  /**
   * 批量操作
   * @param arr 选中的列表项数据
   */
  const batchDeleteFun = (arr: any[]) => {
    //存储选中项到pinia
    formStore.$patch((state) => {
      state.selectRows = arr
    })
    selectTableColumn.value = arr
    ids.value = arr.map((item) => {
      return item.id
    })
  }

  // 下方table
  const bottomBatchSlectFun = (arr: any[]) => {}

  /**
   * 删除当前行
   * @param position
   * @param type
   * @param menu
   */
  const handleDelete = (position: string, type: string, menu: MenuVO) => {
    btnRequestUri.value = menu.meta?.uri
    let _ids = ''
    //如果批量操作需要使用选中行中的某个字段（不是id)，需要使用这个
    if (menu.meta?.form?.batchCommit) {
      _ids = selectTableColumn.value
        .map((item) => {
          return item[menu.meta?.form?.batchCommit?.name!]
        })
        .join(',')
    } else {
      _ids = ids.value.join(',')
    }
    if (_ids.length <= 0) {
      ElMessage.warning('请勾选')
      return
    }
    // const params: { [key: string]: any } = {};
    // params[menu.meta?.form?.batchCommit?.targetName!] = _ids;
    loading.value = true
    batchDeleteApi(_ids, btnRequestUri.value ?? requestUri.value!)
      .then((res: any) => {
        ElMessage.success(res.message)
        resetQuery()
      })
      .finally(() => (loading.value = false))
  }

  const deleteItemFun = (row: any) => {
    deleteItemApi(row.id!, requestUri.value!).then((res: any) => {
      ElMessage.success(res.message)
      resetQuery()
    })
  }
  /**
   * 修改状态
   */
  const handleStatusChange = async (row: { [key: string]: any }) => {
    await switchChangeGlobalFun(row.enable, row.vehicleNo, row.id, requestUri.value!, '车辆')
      .then(async () => {
        //修改成功后刷新列表
        await handleQuery()
      })
      .catch(() => {
        //取消切换，恢复状态
        row.enable = row.enable !== true
      })
  }
  /**
   * 导入Excel
   * @param file 文件
   */
  const importExcelFun = useThrottleFn(async (file: any, meta: MetaVO) => {
    //定义传递的参数
    let formDataParams = {} as any
    const _queryParams = await topQueryGroupComponentRef.value.searchQueryTemp()
    const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
    meta?.form?.params?.map((_item: any) => {
      composeRequestParams(formDataParams, _item, null, storeDataParams, null, _queryParams)
    })
    importButtonLoading.value = true
    let uri = meta?.uri ? meta.uri : requestUri.value!
    const api = meta?.uri ? importFileGlobalBtnUriFun : importFileGlobalFun
    api(file, uri, formDataParams)
      .then(async (res) => {
        ElMessage.success('导入成功')
        importButtonLoading.value = false
        handleQuery()
      })
      .catch(() => {
        importButtonLoading.value = false
      })
  }, 3000)
  /**
   * 导出Excel
   */
  const exportExcelFun = useThrottleFn((position: string, type: string, item: MenuVO) => {
    let requestApi = item.meta?.uri ? globalExportExcel : exportExcel
    requestApi(queryParams, item.meta?.uri ? item.meta?.uri : requestUri.value!)
      .then(async (res) => {
        exportButtonLoading.value = true
        await downloadFileGlobalFun(res)
        exportButtonLoading.value = false
        ElMessage.success('操作成功')
      })
      .catch((error) => {
        exportButtonLoading.value = false
      })
  }, 3000)
  /**
   * 下载导入模版
   */
  const downloadExcelTemplate = useThrottleFn((purpose: string, position?: string, menu?: MenuVO) => {
    const requestApi = menu?.meta?.uri ? downloadTemplate : globalDownloadTemplate
    requestApi(menu?.meta?.uri ? menu?.meta?.uri : requestUri.value!).then(async (res) => {
      exportButtonLoading.value = true
      await downloadFileGlobalFun(res)
      exportButtonLoading.value = false
      ElMessage.success('操作成功')
    })
  }, 3000)
  /**
   * 下载文件
   */
  const fileDownload = useThrottleFn((row: any, position: string, menu: MenuVO, purpose: string) => {
    //定义传递的参数
    let params = {} as any
    const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
    menu.meta?.form?.params?.map((_item: any) => {
      composeRequestParams(params, _item, menu, storeDataParams, row, queryParams)
    })
    // 文件
    if (purpose === 'fileDownload') {
      let api = menu.meta.form!.method === 'get' || menu.meta.form!.method === 'GET' ? globalRequestUrlApi : globalRequestApi
      api(params, menu?.meta?.form?.method!, menu?.meta?.form?.formUri!).then(async (res: any) => {
        // 根据res.data,进行逗号分割，依次下载每个文件
        if (res.data.indexOf(',') !== -1) {
          res.data.split(',').forEach((item: any) => {
            window.open(item)
          })
        } else {
          window.open(res.data)
        }
      })
    } else if (purpose === 'streamDownload') {
      //文件流
      let api = menu.meta.form!.method === 'get' || menu.meta.form!.method === 'GET' ? downloadTemplateUrl : downloadTemplate
      api(params, menu?.meta?.form?.method!, menu?.meta?.form?.formUri!).then(async (res: any) => {
        await downloadFileGlobalFun(res)
      })
    }
  }, 3000)
  /**
   * 筛选条件查询
   * @param searchParams 查询条件
   */
  const handleSearchQuery = (searchParams: any) => {
    //清除之前选中的ids
    ids.value = []
    selectTableColumn.value = []
    //遍历queryParams对象，删除所有属性
    for (const key in queryParams) {
      if (key !== 'page' && key !== 'limit') {
        Reflect.deleteProperty(queryParams, key)
      }
    }
    // queryParams.limit = 10;
    //遍历searchParams对象，将所有属性赋值给queryParams
    for (const key in searchParams) {
      queryParams[key] = searchParams[key]
    }

    //获取storeData中的参数并赋值给tempQueryParams
    if (proxy.$sideBarStore.$state.storeDialogFormParams) {
      for (const key in proxy.$sideBarStore.$state.storeDialogFormParams) {
        queryParams[key] = proxy.$sideBarStore.$state.storeDialogFormParams[key]
      }
    }
    handleQuery()
  }
  /**
   * 展示图片
   * @param row 当前行数据
   * @param name 图片字段名
   */
  const showPic = (row: any, name: string) => {
    if (row[name]) {
      imageList.value = row[name].split(',')
      picDialogComponent.value.picDialogVisible = true
    } else {
      ElMessage.warning('当前未上传证件')
    }
  }

  /**
   * 按钮默认操作
   * @param row 当前行数据
   * @param position 按钮位置
   * @param menu
   */
  const defaultHandle = async (row: any, position: string, menu: MenuVO) => {
    btnMenu.value = menu
    btnRequestUri.value = menu.meta!.uri
    isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1
    if (menu.meta?.form) {
      if (ids.value.length < menu.meta?.form?.min!) {
        ElMessage.warning(`至少选择${menu.meta?.form?.min}项`)
        return
      } else if (ids.value.length > menu.meta?.form?.max!) {
        ElMessage.warning(`最多选择${menu.meta?.form?.max}项`)
        return
      }
    }
    if (menu.meta?.form?.formUri) {
      //定义传递的参数
      let params = {} as any
      const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
      menu.meta?.form?.params?.map((_item) => {
        composeRequestParamsMultiRow(params, _item, menu, storeDataParams, selectTableColumn.value, ids.value)
      })
      globalRequestApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
        await globalBtnForm(res.data, menu.meta?.position!, menu)
      })
    } else {
      dialog.visible = true
      if (menu.meta!.dataColumn.length > 0) {
        operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
      } else {
        operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
      }
      dialog.title = menu.meta!.title
    }
  }
  /**
   * 当前选中的行数据
   */
  const currentRow = ref<TableItem>()
  /**
   *  列表右侧按钮默认操作
   * @param row
   * @param position 按钮位置
   * @param menu
   */
  const defaultHandleTable = async (row: TableItem, position: string, menu: MenuVO) => {
    currentRow.value = row
    btnMenu.value = menu
    btnRequestUri.value = menu.meta!.uri
    isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1
    //存储当前按钮下表单中的btns
    if (menu.meta?.form?.btns) {
      //存储选中项到pinia
      formStore.$patch((state) => {
        state.storeFormBtns = menu.meta?.form?.btns!
      })
    }
    if (menu.meta?.form?.formUri) {
      //定义传递的参数
      let params = {} as any
      const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
      menu.meta!.form!.params?.map((_item) => {
        composeRequestParams(params, _item, menu, storeDataParams, row, queryParams)
      })
      if (menu.meta?.form?.method === 'get' || menu.meta?.form?.method === 'GET') {
        globalRequestUrlApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      } else {
        globalRequestApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      }
    } else {
      if (menu.meta!.dataColumn.length > 0) {
        operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
      } else {
        operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
      }
      await globalBtnForm(row, position, menu)
    }
    //如果menu.meta?.form?.refreshPage==true ,则刷新当前列表
    if (menu.meta?.form?.refreshPage) handleQuery()
    //如果menu.meta?.form?.refreshMenuCount==true ,则更新菜单上的数字
    if (menu.meta?.form?.refreshMenuCount) refreshMenuCount()
    //如果menu.meta?.form?.trigerUris存在，遍历menu.meta?.form?.trigerUris，请求相应接口
    if (menu.meta?.form?.trigerUris && menu.meta?.form?.trigerUris.length > 0) {
      menu.meta?.form?.trigerUris?.map((item) => {
        //定义传递的参数
        let params = {} as any
        const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
        item.params?.map((_item) => {
          composeRequestParams(params, _item, menu, storeDataParams, row, queryParams)
        })
        if (item.method === 'get' || item.method === 'GET') {
          globalRequestUrlApi(params, item.method!, item.uri!).then(async (res) => {})
        } else {
          globalRequestApi(params, item.method!, item.uri!).then(async (res) => {})
        }
      })
    }
  }

  /**
   * 默认按钮操作
   * @param row
   * @param position
   * @param menu
   */
  const globalBtnForm = async (row: TableItem, position: string, menu: MenuVO) => {
    btnRequestUri.value = menu.meta?.form?.formUri ?? null
    if (menu.meta?.purpose === 'copy') {
      isEdit.value = false
    } else {
      isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1
    }
    buttonPosition.value = position //初始化表单
    const initFormData = resetFormGlobalFun(menu.meta?.dataColumn!)
    if (menu.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
    }
    // 根据按钮返回数据渲染当前表单，如果是下拉表单 或者是级联表单项，需要将返回数据设置到表单项的option.data上
    //遍历数组operationColumn.value，找到里面的form的type是select，将row中和form的name相同的项赋值他
    if (position === 'listTop' || position === 'listTabTop') {
      operationColumn.value.map((item: TableItem) => {
        if (item?.form?.type === 'select' || item?.form?.type === 'selectTree' || item?.form?.type === 'cascader') {
          if (row[item.form.name!]) {
            if (!item.form!.option!.data) {
              item.form!.option!.data = []
            }
            //如果row[item.form.name!] 类型是数组则赋值给item.form.option!.data
            if (Array.isArray(row[item.form.name!])) {
              item.form!.option!.data = row[item.form.name!]
            }
            delete row[item.form.name!]
          }
        }
      })
    }
    if (position === 'listRight' || position === 'listTabRight') {
      operationColumn.value.map((item: TableItem) => {
        if (item?.form?.type === 'select' || item?.form?.type === 'selectTree' || item?.form?.type === 'cascader') {
          if (row[item!.form.name!]) {
            if (!item!.form!.option!.data) {
              item!.form!.option!.data = []
            }
            // item!.form.option!.data = row[item!.form.name!];
            // delete row[item.form.name!];
          }
        }
      })
    }
    dialog.visible = true
    dialog.title = menu.meta?.title || '操作'
    const deepRow = JSON.parse(JSON.stringify(row))
    //如果是复制，并且operationColumn.value中的每项的form.canCopy为false，则将deepRow中的对应项的value设置为空
    if (menu.meta?.purpose === 'copy') {
      operationColumn.value.forEach((item: TableItem) => {
        if (item.form?.canCopy === false) {
          deepRow[item.form.name!] = null
        }
      })
      deepRow.id = undefined
    }
    await nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, deepRow)
    })
  }
  /**
   * 获取表单项，用于表单中级联操作
   * @param formData 表单项
   * @param dependOn 当前修改的依赖项下拉
   */
  const getQueryData = async (formData: any, dependOn: string) => {
    //获取form表单数据列
    const newOperationColumn = await getSelectOptions(metaInfo.value!.dataColumn, formData, dependOn, 'topQuerySelect')
  }
  /**
   * 获取表单项，用于表单中级联操作
   * @param formData 表单项
   * @param dependOn 当前修改的依赖项下拉
   */
  const getFormData = async (formData: any, dependOn: string) => {
    setTimeout(async () => {
      //获取form表单数据列
      const newOperationColumn = await getSelectOptions(operationColumn.value, formData, dependOn, 'formSelect')
    }, 200)
  }
  //弹窗显示菜单
  const showRowMenuDialog = (query: any, column: TableItem) => {
    detailDialogVisible.visible = true
    var title = column.label
    if (column.jump) {
      title = column.jump!.title ?? column.label
    }
    detailDialogVisible.title = title
  }
  //弹窗显示对象数据
  const showRowObjectDialog = (query: any, column: TableItem) => {
    objectDialogVisible.visible = true
    objectDialogVisible.title = column.label
  }

  /**
   * 初始化表单数据
   */
  const clearFormColumn = () => {
    operationColumn.value = []
    dialog.title = ''
  }
  /**
   * 取消
   */
  const cancel = (row: TableItem) => {
    boardApplyCancel(row.id!).then((res: any) => {
      ElMessage.success(res.message)
      handleQuery()
    })
  }
  /**
   * 批量删除
   */
  const batchRead = (row: TableItem, position: string, menu: MenuVO) => {
    btnRequestUri.value = menu.meta?.uri
    if (ids.value.length <= 0) {
      ElMessage.warning('请勾选')
      return
    }
    loading.value = true
    //定义传递的参数
    let params = {} as any
    menu.meta?.form?.params?.map((_item) => {
      if (menu.meta?.form?.dependFrom === 'listData') {
        if (_item.value === '$#$#') {
          params[_item.targetName!] = ids.value
        }
      }
    })
    globalRequestApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri ?? btnRequestUri.value!)
      .then(async (res: any) => {
        ElMessage.success(res.message)
        resetQuery()
      })
      .finally(() => (loading.value = false))
  }

  /**
   * 更新菜单上的数字
   */
  function refreshMenuCount() {
    getMenuCount().then((res) => {
      sideBarStore.$patch((state) => {
        state.menuCount = res.data as any
      })
    })
  }

  /**
   * 自定义提示弹窗（重新校验）
   */
  async function confirmDialog(row: TableItem, position: string, meta: MetaVO) {
    //定义传递的参数
    let params = {} as any
    const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
    meta?.form?.btns![0].params?.map((item) => {
      composeRequestParams(params, item, null, storeDataParams, row, queryParams)
    })
    if (meta.form?.btns![0].method === 'post' || meta.form?.btns![0].method === 'POST') {
      const { message } = await refreshCheckPostApi(meta.form.btns![0].uri!, params)
      ElMessage.success(message)
    } else if (meta.form?.btns![0].method === 'get' || meta.form?.btns![0].method === 'GET') {
      const { message } = await refreshCheckGetApi(meta.form.btns![0].uri!, params)
      ElMessage.success(message)
    }
    //刷新列表
    handleQuery()
  }

  const menuList = ref<MenuVO[]>()
  const userInfo = ref<UserPageVO>() //用户信息
  const currentMemuColumnList = ref<CheckedMenuColumnVO[]>() //当前选中的菜单下的数据列
  const menuDialogPermissionGroup = ref<MenuVO[]>() //数据列权限分配弹窗中的操作按钮
  const menuDialogRef = ref() //菜单弹窗
  /**
   * 菜单弹窗
   */
  const menuDialogVisible = reactive<DialogOption>({
    visible: false,
  })
  /**
   * 获取当前用户菜单数据
   */
  const getUserMenuList = (assignUserId?: string) => {
    userMenulist(assignUserId, requestUri.value!).then((res) => {
      menuList.value = getNewUserMenuList(res.data)
      menuDialogVisible.visible = true
    })
  }
  /**
   * 递归为菜单每项赋值title为meta中的title，columnName 为meta中的dataColumn中数组中的name
   * @param data 菜单项
   */
  const getNewUserMenuList = (data: MenuVO[]) => {
    data.forEach((item: MenuVO) => {
      item.title = item.meta?.title
      let columns: any[] = []
      item.meta?.dataColumn.forEach((i: any) => {
        columns.push(i.label)
      })
      item.columnName = columns.join(',') ? columns.join(',') : undefined
      item.type = typeConversion(item.meta?.type) //类型 用于区分菜单和目录
      if (item.children && item.children.length > 0) {
        getNewUserMenuList(item.children)
      }
    })
    return data
  }
  /**
   * 获取当前登录用户下的当前菜单下的数据列
   */
  const getcurrentLoginUserMenuColumnlist = (row: MenuVO) => {
    currentLoginUserMenuColumnlist(row.menuId, requestUri.value!)
      .then((res) => {
        const { data } = res
        //当前登录用户下的菜单数据列
        bus.emit('currentLoginUserMemuColumnList', data)
        //当前用户下的菜单数据列
        bus.emit('userMemuColumnList', row.meta?.dataColumn)
      })
      .catch((err) => {
        menuDialogRef.value.menuColumnLoading = false
      })
  }

  /**
   * 重置密码
   */
  function updatePassword(row: UserPageVO) {
    userInfo.value = row
    ElMessageBox.prompt('请输入用户「' + row.realName + '」的新密码', '重置密码', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })
      .then(({ value }) => {
        if (!value) {
          ElMessage.warning('请输入新密码')
          return false
        }
        updateUserPassword(row.id, value, requestUri.value!).then(() => {
          ElMessage.success('密码修改成功，新密码是：' + value)
        })
      })
      .catch(() => {})
  }

  /**
   * 获取用户当前菜单下的数据列
   */
  const selectMenuColumn = (selectMenuColumn: string) => {
    const params: CheckedMenuColumnVO = JSON.parse(selectMenuColumn)
    distributeMenuColumn(params, requestUri.value!).then((res) => {
      ElMessage.success('分配成功')
      menuDialogRef.value.menuColumnDialogVisible.visible = false
      getUserMenuList(userInfo.value?.id)
    })
  }
  /**
   * 分配权限
   */
  const openMenuDialog = (row: UserPageVO) => {
    getUserMenuList(row.id)
    userInfo.value = row
  }
  /**
   * 更新数据
   * @param row 当前行数据
   * @param position
   * @param menu
   */
  const editAuth = async (row: UserForm, position: string, menu: MenuVO) => {
    if (menu.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
    }
    dialog.visible = true
    if (row?.id) {
      dialog.title = '修改用户'
      isEdit.value = true
      const userInfo = await getUserDetail(row.id)
      userInfo.roles = userInfo.roleIds
      //初始化表单
      const initFormData = resetFormGlobalFun(tableConfig.tableItem!)
      nextTick(() => {
        Object.assign(formDialogRef.value.formData, initFormData)
        Object.assign(formDialogRef.value.formData, userInfo)
      })
    } else {
      dialog.title = '新增用户'
      isEdit.value = false
    }
  }

  /**
   * 获取用户信息
   */
  async function getUserDetail(id: string) {
    const { data } = await getDetailById(id, requestUri.value!)
    return data
  }

  //菜单弹窗数据end
</script>
<style lang="scss" scoped>
  .statusText {
    overflow: hidden;
  }
</style>
