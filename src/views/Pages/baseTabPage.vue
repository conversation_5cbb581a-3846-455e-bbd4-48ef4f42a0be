<template>
  <div class="app-container" v-loading="pageLoading">
    <el-card v-if="statisticColumn" class="top-query" shadow="never" style="padding-bottom: 20px; margin-bottom: 20px">
      <statisticComponent :statisticColumn="statisticColumn" :tableColumn="tableConfig.tableItem" />
    </el-card>
    <el-card>
      <div>
        <el-row>
          <el-col :span="24">
            <!-- 右侧区域 -->
            <right-bottom-component
              ref="rightBottomRef"
              :isShowBtnGroup="props.isShowBtnGroup"
              :bottomTabPermissionGroup="bottomTabPermissionGroup"
              :bottomButtomPermissionGroup="bottomButtomPermissionGroup"
              :requestUri="btnRequestUri ?? requestUri"
              :queryParams="queryParams"
              :showAllSelection="showAllSelection"
              :meta="currentTab?.meta"
              :rightTipDataObj="rightTipDataObj"
              @refreshPageTableColumn="refreshPageTableColumn"
              @changeTab="changeTab"
              @handleDeleteBottomListItem="handleDeleteBottomListItem"
              @handleQueryBottomList="handleQueryBottomList"
              @addItem="addBottomItem"
              @editBottomItem="editBottomItem"
              @deleteItem="deleteItemFun"
              @switchBottomListStatus="switchBottomListStatus"
              @exportBottomTabListExcel="exportBottomTabListExcel"
              @downloadExcelTemplate="downloadButtomTabExcelTemplate"
              @importButtomTabExcelFun="importButtomTabExcelFun"
              @showRowMenuDialog="showRowMenuDialog"
              @defaultHandleTable="defaultHandleTable"
              @handleSubmit="handleSubmit"
              @refresh="refresh"
              @vehicleChange="vehicleChange"
              @approvalOperation="approvalOperation"
              @showMenuDialog="showMenuDialog"
              @updatePassword="updatePassword"
              @confirmDialog="confirmDialog"
              @sortChange="sortChange"
              @handleMsgQuery="handleMsgQuery"
            />
          </el-col>
        </el-row>
        <pagination
          v-if="!rightBottomRef?.pageStyle"
          v-model:total="rightBottomListTotal"
          v-model:page="bottomListQueryParams.page"
          v-model:limit="bottomListQueryParams.limit"
          @pagination="handleQueryBottomList"
        />
      </div>
    </el-card>
    <!-- 新增弹窗 -->
    <form-dialog
      v-if="dialog.visible"
      ref="formDialogRef"
      :dialog="dialog"
      :isEdit="isEdit"
      :dataColumn="operationColumn && operationColumn.length > 0 ? operationColumn : rightBottomRef.tableConfig.tableItem"
      :requestUri="btnRequestUri ?? requestUri"
      :btnMenu="btnMenu"
      :loading="loading"
      @clearFormColumn="clearFormColumn"
      @closeDialog="closeDialog"
      @handleSubmit="handleFormSubmit"
      @formData="getFormData"
    />
    <!-- 列表项详情弹窗 -->
    <el-dialog
      :draggable="true"
      destroy-on-close
      v-model="detailDialogVisible.visible"
      :title="detailDialogVisible.title"
      :width="detailDialogVisible.dialogWidth ?? '80%'"
      :fullscreen="isFullscreen"
      @closed="closeDetailDialogVisible"
      align-center
    >
      <el-scrollbar max-height="90vh" class="formClass">
        <BasePage1 />
      </el-scrollbar>
    </el-dialog>
    <!--  下级审批操作 -->
    <ApprovalOperationDialogComponent
      ref="approvalOperationDialogRef"
      :dialogVisible="approvalOperationDialog"
      :btnMenu="btnMenu"
      @clearFormColumn="clearFormColumn"
      @closeDialog="approvalOperationDialog.visible = false"
      @handleSubmit="handleApprovalOperationSubmit"
    />
    <!-- 车辆变更弹窗 -->
    <VehicleChangeDialogComponent
      ref="exchangeVehcileComponentRef"
      :vehicleChangeParams="vehicleChangeParams"
      :dialogVisible="vehicleChangeDialog"
      @closeDialog="closeVehicleChangeDialog"
      @exchangeVehicleResult="exchangeVehicleResult"
    />
    <!-- 合并运单号弹窗 -->
    <MergeShipmentNoDialogComponent ref="mergeShipmentNoDialogRef" @closeDialog="closeDialog" />
    <!-- 导入失败公共组件 importCheck-->
    <ImportFailedComponent ref="importFailedRef" :dialog="importDialogVisible" />
    <!-- 导入成功公共组件 importSuccess-->
    <ImportSuccessComponent ref="importSuccessRef" :dialog="importSuccessDialogVisible" />
  </div>
</template>
<script setup lang="ts">
  import BasePage1 from '@/views/Pages/basePage1.vue'
  import rightBottomComponent from '@/views/customerCenter/components/rightBottomComponent.vue' //右下部
  import ApprovalOperationDialogComponent from '@/components/OtherFormDialogComponent/ApprovalOperationDialogComponent/index.vue' //下级审批操作弹窗
  import {
    downloadFileGlobalFun,
    getSelectOptions,
    getcurrentUserMenuColumnlist,
    resetFormGlobalFun,
    switchChangeGlobalFun,
    composeRequestParams,
  } from '@/utils/common'
  import { UserForm, UserPageVO } from '@/api/authorityManagement/CorporateAccountManagement/PersonalManagement/types'
  import VehicleChangeDialogComponent from '@/components/OtherFormDialogComponent/VehicleChangeDialogComponent/index.vue' //车辆变更弹窗
  import MergeShipmentNoDialogComponent from '@/components/OtherFormDialogComponent/MergeShipmentNoDialogComponent/index.vue' //合并运单号弹窗
  import {
    deleteItemApi,
    downloadTemplate,
    exportExcel,
    globalExportExcel,
    importFileGlobalFun,
    bottomTableList,
    batchDeleteApi,
    globalDownloadTemplate,
    bottomSinglePage,
    updateItemApi,
    addItemApi,
    bottomDynamicColumns,
  } from '@/api/auth'
  import { FormColumn, StatisticColumnVO, UploadImageVO } from '@/types/global'
  import { getMenuCount, globalRequestApi, globalRequestUrlApi, transferVehicle } from '@/api/planManagement'
  import { useSideBarStore } from '@/store/modules/sideBar'
  import { useSettingsStore } from '@/store/modules/settings'
  // 状态管理依赖
  import { useFormStore } from '@/store/modules/form.js'
  import { RoleForm } from '@/api/authorityManagement/RoleManagement/types'
  import formDialog from '@/components/FormDialogComponent/index.vue' //表单弹窗
  import { CustomerVO } from '@/api/customerCenter/customerBaseData/types'
  import { carrierListApi } from '@/api/InTransitManagement'
  import { approvalOperationApi, refreshCheckGetApi, refreshCheckPostApi } from '@/api/shipmentManagement'
  import { ExchangeVehicleParamsVo } from '@/api/planManagement/type'
  import { dayjs } from 'element-plus'
  import { updateUserPassword } from '@/api/authorityManagement/CorporateAccountManagement/PersonalManagement'
  import defaultSettings from '@/settings'
  import TableComponent from '@/components/TableComponent/index.vue'
  import ImportFailedComponent from '@/components/OtherFormDialogComponent/ImportFailedComponent/index.vue'
  import ImportSuccessComponent from '@/components/OtherFormDialogComponent/ImportSuccessComponent/index.vue'
  const settingsStore = useSettingsStore()
  const formStore = useFormStore()
  const { routerParams, defaultTableIds, mergeFormData, storeFormParams } = storeToRefs(formStore)
  const sideBarStore = useSideBarStore()
  const { proxy }: any = getCurrentInstance()
  //优先使用列表中的携带的参数，再使用地址栏参数
  const routeParams = proxy.$sideBarStore.$state.btnMenuQuery ? proxy.$sideBarStore.$state.btnMenuQuery : routerParams.value
  const props = defineProps({
    /**
     * 新的menuId
     */
    newMenuId: {
      require: true,
      type: String,
      default: '',
    },
    isShowBtnGroup: {
      require: true,
      type: Boolean,
      default: true,
    },
    currentId: {
      require: true,
      type: String,
      default: '',
    },
  })
  const state = reactive({
    isShowMore: false, // 控制展开更多的显示与隐藏
    textHeight: '', // 框中内容的高度
    status: false, // 内容状态是否打开
  })
  //运单号弹窗
  const mergeShipmentNoDialogRef = ref()
  /**
   * 提交按钮加载动画
   */
  const btnLoading = ref<boolean>(false)
  /**
   * 按钮下的表单
   */
  const operationColumn = ref<any>()
  /**
   * 下级审批
   */
  const approvalOperationDialogRef = ref<any>()
  /**
   * 查看列表项详情弹窗
   */
  const detailDialogVisible = reactive<DialogOption>({
    visible: false,
    title: '详情',
    dialogWidth: '80%',
  })
  /**
   * 按钮的请求地址前缀
   */
  const btnRequestUri = ref<string | null>()

  /**
   * 是否编辑状态
   */
  const isEdit = ref<boolean>()
  /**
   * 按钮组
   */
  const btnGroup = ref<MenuVO[]>([])
  /**
   * 客户列表查询条件
   */
  const queryParams = reactive<any>({ page: 1, limit: defaultSettings.globalLimit })
  /**
   * 右侧列表总条数
   */
  const rightBottomListTotal = ref<number>(0)
  /**
   * 底部列表查询条件
   */
  const bottomListQueryParams = reactive<any>({
    page: 1,
    limit: defaultSettings.globalLimit,
  })
  /**
   * form 表单弹窗
   */
  const formDialogRef = ref()
  /**
   * 车辆变更弹窗
   */
  const exchangeVehcileComponentRef = ref()
  /**
   * 右下区域tab
   */
  const bottomTabPermissionGroup = ref<MenuVO[]>([])
  /**
   * 右下区域tab中的按钮组
   */
  const bottomButtomPermissionGroup = ref<MenuVO[]>([])
  /**
   * 右下区域
   */
  const rightBottomRef = ref()
  /**
   * 选中的tab
   */
  const currentTab = ref<MenuVO>()
  /**
   * 选中的当前行
   */
  const currentItem = ref<TableItem>()
  /**
   * 用于显示在左侧栏中的列表名称
   */
  const listName = ''
  /**
   * 当前菜单的uri
   */
  const currentMenuUri = ref<string>()
  /**
   * 当前tab的uri
   */
  const currentTabUri = ref<string>()
  /**
   * 请求地址前缀
   */
  const requestUri = ref<string>('')
  /**
   * 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   */
  const buttonPosition = ref<string>()
  /**
   * 导出加载动画
   */
  const exportButtonLoading = ref<boolean>()
  /**
   * 导入加载动画
   */
  const importButtonLoading = ref<boolean>()
  /**
   * 下载加载动画
   */
  const downloadButtonLoading = ref<boolean>()
  /**
   * 全局pageLoading
   */
  const pageLoading = ref(false)
  /**
   * 弹窗
   */
  const dialog = reactive<DialogOption>({
    visible: false,
  })
  /**
   * 下级审批操作
   */
  const approvalOperationDialog = reactive<DialogOption>({
    visible: false,
  })
  /**
   * menu
   */
  const btnMenu = ref<MenuVO>()
  /**
   * meta
   */
  const metaInfo = ref<MetaVO>()
  /**
   * 统计组件数据
   */
  const statisticColumn = ref<StatisticColumnVO[]>()
  /**
   * 控制多选复选框按钮显示隐藏
   */
  const showAllSelection = ref<boolean>(true)
  /**
   * 是否全屏
   */
  const isFullscreen = ref<any>(true)
  /**
   * 列表配置项
   */
  const tableConfig = reactive<TableConfig>({
    showHandleSelection: true,
    showSort: true,
    tableItem: [],
    operation: {
      label: '操作',
      items: [],
      width: '120px',
    },
  })
  /**
   * 刷新数据列
   */
  const refreshPageTableColumn = async () => {
    pageLoading.value = true
    if (props.newMenuId) {
      // 首先获取菜单权限，获取相关tab项
      await getPermission(props.newMenuId)
    } else {
      // 首先获取菜单权限，获取相关tab项
      await getPermission(proxy.$sideBarStore.$state.menuId)
    }
    // 主动触发一次首个tab项
    await changeTab(currentTab.value!)
    pageLoading.value = false
  }
  onMounted(async () => {
    document.title = settingsStore.systemName
    //默认将左侧菜单第一条数据存到pinia中，初始化
    formStore.$patch((state) => {
      state.selectLeftTreeRows.map((item, index) => {
        if (state.selectLeftTreeRows[index].menuId === proxy.$sideBarStore.$state.menuId) {
          state.selectLeftTreeRows[index].selectLeftTreeRow = null
        }
      })
    })
    if (props.newMenuId) {
      // 首先获取菜单权限，获取相关tab项
      await getPermission(props.newMenuId)
    } else {
      // 首先获取菜单权限，获取相关tab项
      await getPermission(proxy.$sideBarStore.$state.menuId)
    }
    // 主动触发一次首个tab项
    await changeTab(currentTab.value!)
  })
  /**
   * 获取菜单下的权限
   * @param menuId 菜单id
   */
  const getPermission = async (menuId: string) => {
    return new Promise<void>(async (resolve, reject) => {
      const { children, meta }: { children: MenuVO[]; meta: MetaVO } = await getcurrentUserMenuColumnlist(menuId)
      metaInfo.value = meta
      //控制多选复选框按钮显示隐藏
      showAllSelection.value = meta.ext?.tableMultiSelect ?? true
      //获取当前菜单分页条数
      queryParams.limit = bottomListQueryParams.limit = meta.ext?.pageLimit || defaultSettings.globalLimit
      isFullscreen.value = meta.ext?.fullScreen === true
      btnGroup.value = children
      //获取右下区域tab组
      bottomTabPermissionGroup.value = children.filter((item: MenuVO) => item.meta?.type === 3)
      //如果bottomTabPermissionGroup.length>0,取第一个tab的menuId,获取数据列
      if (bottomTabPermissionGroup.value.length > 0 && bottomTabPermissionGroup.value[0].menuId) {
        //默认当前tab为tab中的第一项
        currentTab.value = bottomTabPermissionGroup.value[0]
        rightBottomRef.value.pageStyle = currentTab.value.meta?.purpose
      }
      //获取form表单数据列
      const dataColumn: any = await getSelectOptions(meta.dataColumn, null, '', 'topQuerySelect')
      currentMenuUri.value = meta.uri
      //如果meta.operation=false 则不展示列表右侧操作列
      if (!meta.operation) {
        tableConfig.operation = undefined
      }

      //遍历routeParams对象，queryParams[key] = routeParams[key]
      for (const key in routeParams) {
        if (Object.prototype.hasOwnProperty.call(routeParams, key)) {
          //如果属性中包含range字符串，则把首字母大写，然后拆分2个，分别拼上start 和 end ,并删除原有属性
          if (key!.indexOf('Range') !== -1 && (routeParams[key] as any)) {
            const startName = 'start' + key.charAt(0).toUpperCase() + key.slice(1)
            const endName = 'end' + key.charAt(0).toUpperCase() + key.slice(1)
            queryParams[startName] = routeParams[key]![0]
            queryParams[endName] = routeParams[key]![1]
            delete queryParams[key]
          } else {
            queryParams[key] = routeParams[key]
          }
          //初始化搜索条件
          // formStore.setSearchParams({});
        }
      }
      resolve()
    })
  }
  const rightTipObj = ref<RightTipVO>()
  const rightTipDataObj = ref<RightTipDataVO>()
  /**
   * 切换tab 重新获取权限
   * @param menuId 菜单id
   */
  const getTabPermissionGroup = async (menuId: string) => {
    return new Promise(async (resolve, reject) => {
      const { children, meta }: { children: MenuVO[]; meta: MetaVO } = await getcurrentUserMenuColumnlist(menuId)
      bottomButtomPermissionGroup.value = children
      currentTabUri.value = meta.uri
      showAllSelection.value = meta.ext?.tableMultiSelect ?? true
      //获取当前菜单分页条数
      queryParams.limit = bottomListQueryParams.limit = meta.ext?.pageLimit || defaultSettings.globalLimit
      if (meta.ext?.showHandleSelection === false) {
        rightBottomRef.value.tableConfig.showHandleSelection = false
      }
      if (meta.ext?.showSort === false) {
        rightBottomRef.value.tableConfig.showSort = false
      }
      //右侧消息提醒文字
      rightTipObj.value = meta.ext?.rightTip
      if (rightTipObj.value) {
        globalRequestUrlApi({ menuId }, rightTipObj.value.method, rightTipObj.value.uri).then((res: any) => {
          const data = res.data as RightTipDataVO
          rightTipDataObj.value = data
        })
      }

      if (!meta.operation) {
        tableConfig.operation = undefined
      }
      //获取form表单数据列
      const dataColumn: FormColumn[] = await getSelectOptions(meta.dataColumn, null, '', 'topQuerySelect')
      rightBottomRef.value.tableConfig.tableItem = dataColumn
      rightBottomRef.value.tableConfig.isKey = !rightBottomRef.value.tableConfig.isKey
      rightBottomRef.value.tableConfig.operation = meta.operation
      dataColumn.forEach((item: TableItem, index: number) => {
        //由于服务端图片校验规则下发后是字符串，不能转换，所以需要处理
        if (item.form?.imageOption && item.form?.imageOption!.required) {
          //图片校验
          const validateImage = (rule: any, value: any, callback: any) => {
            //验证器
            if (!formDialogRef.value.formData[item.form?.name!]) {
              //为true代表图片在  false报错
              callback(new Error('请上传图片'))
            } else {
              callback()
            }
          }
          const imageRules = [{ required: true, validator: validateImage, trigger: 'change' }]
          item.form.rules = imageRules
        }
        //如果item.defaultValue存在，则给表单赋值
        if (item.query?.defaultValue) {
          bottomListQueryParams[item.query?.name!] = item.query?.defaultValue
        }
        // 存在默认值
        if (item.query?.showDefaultDate && item.query?.format) {
          bottomListQueryParams[item.query?.name!] = dayjs().format(item.query?.format)
        }
      })
      //遍历routeParams对象，queryParams[key] = routeParams[key]
      for (const key in routeParams) {
        if (Object.prototype.hasOwnProperty.call(routeParams, key)) {
          //如果属性中包含range字符串，则把首字母大写，然后拆分2个，分别拼上start 和 end ,并删除原有属性
          if (key!.indexOf('Range') !== -1 && (routeParams[key] as any)) {
            const startName = 'start' + key.charAt(0).toUpperCase() + key.slice(1)
            const endName = 'end' + key.charAt(0).toUpperCase() + key.slice(1)
            queryParams[startName] = routeParams[key]![0]
            queryParams[endName] = routeParams[key]![1]
            delete queryParams[key]
          } else {
            queryParams[key] = routeParams[key]
          }
          //初始化搜索条件
          // formStore.setSearchParams({});
        }
      }
      resolve(bottomListQueryParams)
    })
  }
  /**
   * 更新表单下拉
   */
  const refreshFormSelectOptions = async (dataColumn: FormColumn[], row?: any) => {
    //重新获取form表单数据列，刷新筛选下拉项
    return new Promise(async (resolve, reject) => {
      const newDataColumn = getSelectOptions(dataColumn, null, '', 'formSelect', null, null, row)
      resolve(newDataColumn)
    })
  }
  /**
   * 重置表单
   */
  function resetForm() {
    if (formDialogRef.value) {
      formDialogRef.value.resetForm()
    }
    if (approvalOperationDialogRef.value) {
      approvalOperationDialogRef.value.resetForm()
    }
    //清空operationColumn
    if (rightBottomRef.value) {
      rightBottomRef.value.operationColumn = undefined
    }
  }
  /**
   * 启用禁用底部列表项状态
   */
  const switchBottomListStatus = async (row: UserForm) => {
    await switchChangeGlobalFun(row.enable!, row.name!, row.id, currentTabUri.value!, listName)
      .then(async () => {
        //修改成功后刷新列表
        getBottomTableList(currentTabUri.value!)
      })
      .catch(() => {
        //取消切换，恢复状态
        row.enable = row.enable !== true
      })
  }
  /**
   * 获取table列表数据
   * @param relationId id
   * @param uri 请求地址
   * @param isResetQuery 是否重置查询条件
   */
  const getBottomTableList = (uri: string, isResetQuery: boolean = false) => {
    rightBottomRef.value.listLoading = true
    if (isResetQuery) {
      rightBottomRef.value.tableConfig.tableItem.forEach((item: TableItem, index: number) => {
        //如果item.defaultValue存在，则给表单赋值
        if (item.query?.defaultValue) {
          bottomListQueryParams[item.query?.name!] = item.query?.defaultValue
        }
        //  循环bottomListQueryParams删除bottomListQueryParams 所有的参数
        for (let key in bottomListQueryParams) {
          if (!item.query?.defaultValue && item.query?.name === key) {
            delete bottomListQueryParams[key]
          }
        }
      })

      bottomListQueryParams.page = 1
      // bottomListQueryParams.limit = 20;
    }
    // 处理长安民生的二级弹窗tab切换需要relationId
    if (props.currentId) {
      bottomListQueryParams.relationId = props.currentId
    }
    //获取storeData中的参数并赋值给tempQueryParams
    if (proxy.$sideBarStore.$state.storeDialogFormParams) {
      for (const key in proxy.$sideBarStore.$state.storeDialogFormParams) {
        bottomListQueryParams[key] = proxy.$sideBarStore.$state.storeDialogFormParams[key]
      }
    }
    bottomTableList(bottomListQueryParams, uri)
      .then((res) => {
        const { data } = res
        try {
          rightBottomRef.value.tableConfig.tableItem.forEach((item: TableItem) => {
            if (item.type === 'uploadImage' && item.form?.imageOption!) {
              data.rows.map((_item: any) => {
                let arr: UploadImageVO[] = []
                _item[item.form?.name!]?.forEach((v: any) => {
                  arr.push({
                    url: v,
                  })
                })
                _item[item.form?.name!] = arr
              })
            }
          })
        } catch (error) {}

        rightBottomRef.value.tableData = data.rows

        rightBottomRef.value.total = rightBottomListTotal.value = data.total
        rightBottomRef.value.listLoading = false
        statisticColumn.value = data.statistics
      })
      .catch((err) => {
        rightBottomRef.value.listLoading = false
      })
  }
  /**
   * 获取tab下singlePage数据
   * @param relationId id
   * @param uri 请求地址
   * @param isResetQuery 是否重置查询条件
   */
  const getBottomSinglePage = (uri: string, isResetQuery: boolean = false) => {
    rightBottomRef.value.listLoading = true
    if (isResetQuery) {
      //循环bottomListQueryParams删除bottomListQueryParams 所有的参数
      for (let key in bottomListQueryParams) {
        delete bottomListQueryParams[key]
      }
    }
    bottomSinglePage(bottomListQueryParams, uri)
      .then((res) => {
        const { data } = res
        rightBottomRef.value.singlePageData = data
        rightBottomRef.value.listLoading = false
      })
      .catch((err) => {
        rightBottomRef.value.listLoading = false
      })
  }
  //获取tab下动态表头数据
  const getBottomDynamicColumns = (uri: string, isResetQuery: boolean = false) => {
    rightBottomRef.value.listLoading = true
    if (isResetQuery) {
      //循环bottomListQueryParams删除bottomListQueryParams 所有的参数
      for (let key in bottomListQueryParams) {
        delete bottomListQueryParams[key]
      }
    }
    bottomDynamicColumns(bottomListQueryParams, uri)
      .then((res) => {
        const { data } = res
        rightBottomRef.value.dynamicColumnsData = data
        rightBottomRef.value.listLoading = false
      })
      .catch((err) => {
        rightBottomRef.value.listLoading = false
      })
  }
  /**
   * 切换tab
   * @param tabItem
   * @param params
   * @param pageStyle tab下的页面样式 singlePage
   */
  const changeTab = async (tabItem: MenuVO, params?: any, pageStyle?: string) => {
    currentTab.value = tabItem
    if (tabItem.menuId) {
      await getTabPermissionGroup(tabItem.menuId)
    }
    if (rightBottomRef.value) {
      rightBottomRef.value.operationColumn = undefined
    }
    if (currentTab.value?.meta?.purpose === 'singlePage' || pageStyle === 'singlePage') {
      if (tabItem.meta?.uri) {
        btnRequestUri.value = tabItem.meta?.uri
        getBottomSinglePage(tabItem.meta?.uri, true)
      }
    } else if (currentTab.value?.meta?.purpose === 'dynamicColumns' || pageStyle === 'dynamicColumns') {
      if (tabItem.meta?.uri) {
        // btnRequestUri.value = tabItem.meta?.uri;
        // getBottomDynamicColumns(tabItem.meta?.uri, true);
      }
    } else {
      // //初始化page
      bottomListQueryParams.page = 1
      // bottomListQueryParams.limit = 20;
      rightBottomRef.value.listQueryParams.page = 1
      if (tabItem.meta?.ext?.needStore) {
        //将storeData中的参数拼接到bottomListQueryParams
        for (let key in formStore.storeFormParams) {
          if (key !== 'page' && key !== 'limit') {
            bottomListQueryParams[key] = formStore.storeFormParams[key]
          }
        }
      }
      rightBottomRef.value.resetQuery()
      if (tabItem.meta?.uri) {
        btnRequestUri.value = tabItem.meta?.uri
        getBottomTableList(tabItem.meta?.uri, true)
      }
    }
  }
  /**
   * 批量删除右下区域列表数据
   * @param ids
   */
  const handleDeleteBottomListItem = (ids: string) => {
    batchDeleteApi(ids, currentTab.value?.meta?.uri!).then(() => {
      ElMessage.success('删除成功')
      getBottomTableList(currentTab.value?.meta?.uri!)
    })
  }
  /**
   * 分页查询底部列表数据
   */
  const handleQueryBottomList = (params: any) => {
    // bottomListQueryParams.limit = params.limit;
    // bottomListQueryParams.page = params.page;
    //初始化bottomListQueryParams
    // for (const key in bottomListQueryParams) {
    //   if (key !== 'page' && key !== 'limit') {
    //     delete bottomListQueryParams[key]
    //   }
    // }
    //遍历params
    for (const key in params) {
      if (params.hasOwnProperty(key)) {
        bottomListQueryParams[key] = params[key]
      }
    }
    getBottomTableList(currentTab.value?.meta?.uri!)
  }
  const addBottomItem = async (position: string, type: string, btnItem: any) => {
    buttonPosition.value = position
    dialog.visible = true
    isEdit.value = false
    dialog.title = '新增'
    btnRequestUri.value = currentTabUri.value
    let form: MenuVO = btnGroup.value.find((item: MenuVO) => item.meta?.purpose === type) || ({} as MenuVO)
    operationColumn.value = rightBottomRef.value.tableConfig.tableItem
    if (btnItem.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(btnItem.meta!.dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(rightBottomRef.value.tableConfig.tableItem)
    }
    btnRequestUri.value = btnItem.meta!.uri ?? currentTabUri.value
  }
  /**
   * 修改底部列表项
   * @param row 当前行数据
   * @param position 按钮位置
   */
  const editBottomItem = async (row: TableItem, position: string) => {
    dialog.visible = true
    isEdit.value = true
    dialog.title = '修改'
    buttonPosition.value = position
    //初始化表单
    const initFormData = resetFormGlobalFun(rightBottomRef.value.tableConfig.tableItem!)
    let form: MenuVO = bottomButtomPermissionGroup.value.find((item: MenuVO) => item.meta?.purpose === 'topEdit') || ({} as MenuVO)
    if (form.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(form.meta!.dataColumn)
      btnRequestUri.value = form.meta!.uri
    } else {
      btnRequestUri.value = currentTabUri.value
      operationColumn.value = await refreshFormSelectOptions(rightBottomRef.value.tableConfig.tableItem)
    }
    nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, row)
    })
  }
  /**
   * 导出底部tab列表Excel
   * @param purpose 按钮类型
   * @param position 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   * @param menu 按钮信息
   */
  const exportBottomTabListExcel = useThrottleFn((params: any, purpose: string, position?: string, menu?: MenuVO) => {
    let requestApi = menu!.meta?.uri ? globalExportExcel : exportExcel
    requestApi(params, menu!.meta?.uri ?? currentTabUri.value!)
      .then(async (res) => {
        exportButtonLoading.value = true
        await downloadFileGlobalFun(res)
        exportButtonLoading.value = false
        ElMessage.success('操作成功')
      })
      .catch(() => {
        exportButtonLoading.value = false
      })
  }, 3000)
  /**
   * 下载导入模版
   */
  const downloadButtomTabExcelTemplate = useThrottleFn((purpose: string, position?: string, menu?: MenuVO) => {
    const api = menu?.meta?.uri ? downloadTemplate : globalDownloadTemplate
    api(menu?.meta?.uri ?? currentTabUri.value!)
      .then(async (res) => {
        downloadButtonLoading.value = true
        await downloadFileGlobalFun(res)
        downloadButtonLoading.value = false
        ElMessage.success('操作成功')
      })
      .catch(() => {
        downloadButtonLoading.value = false
      })
  }, 3000)
  // 导入失败弹窗
  const importDialogVisible = reactive<DialogOption>({
    title: '导入数据校验反馈',
    visible: false,
  })
  // 导入成功弹窗
  const importSuccessDialogVisible = reactive<DialogOption>({
    title: '导入数据校验反馈',
    visible: false,
  })
  const importFailedRef = ref<any>(null)
  const importSuccessRef = ref<any>(null)
  /**
   * 导入Excel
   * @param file 文件
   */
  const importButtomTabExcelFun = useThrottleFn((file: any, meta: MetaVO) => {
    importButtonLoading.value = true
    importFileGlobalFun(file, meta.uri ?? currentTabUri.value!)
      .then(async (res: any) => {
        if (res.code === 11010) {
          sideBarStore.$patch((state) => {
            state.btnMenuId = res.data.menuId
          })
          detailDialogVisible.visible = true
          detailDialogVisible.title = res.data.label
          return
        } else if (res.code === 5001) {
          ElMessage.error('导入失败')
          importDialogVisible.visible = true
          importFailedRef.value.state.data = res.data
          return
        } else if (res.code === 201) {
          importSuccessDialogVisible.visible = true
          importSuccessRef.value.state.data = res.data
        }
        ElMessage.success('导入成功')
        getBottomTableList(currentTabUri.value!)
      })
      .finally(() => {
        importButtonLoading.value = false
      })
  }, 3000)
  const deleteItemFun = (row: any, uri: string) => {
    deleteItemApi(row.id!, uri ?? btnRequestUri.value!).then((res: any) => {
      ElMessage.success(res.message)
      changeTab(currentTab.value!)
    })
  }
  //弹窗显示菜单
  const showRowMenuDialog = (query: any, column: TableItem) => {
    detailDialogVisible.visible = true
    var title = column.label
    if (column.jump) {
      title = column.jump!.title ?? column.label
      if (column.jump?.dialogWidth) {
        detailDialogVisible.dialogWidth = column.jump!.dialogWidth ?? '80%'
      }
    }
    detailDialogVisible.title = title
  }
  /**
   *  列表右侧按钮默认操作
   * @param position 按钮位置
   * @param type 按钮类型 或者是menuId
   * @param menu
   */
  const defaultHandleTable = async (row: TableItem, position: string, menu: MenuVO) => {
    btnMenu.value = menu
    btnRequestUri.value = menu.meta!.uri
    isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1
    if (menu.meta?.form?.formUri) {
      //定义传递的参数
      let params = {} as any
      const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
      menu.meta?.form?.params?.map((_item) => {
        composeRequestParams(params, _item, menu, storeDataParams, row, null)
      })
      if (menu.meta?.form?.method === 'get' || menu.meta?.form?.method === 'GET') {
        globalRequestUrlApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      } else {
        globalRequestApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      }
    } else {
      dialog.visible = true
      // if (menu.meta!.dataColumn.length > 0) {
      //   operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn, row)
      // } else {
      //   operationColumn.value = await refreshFormSelectOptions(rightBottomRef.value.tableConfig.tableItem, row)
      // }
      globalBtnForm(row, position, menu)
      dialog.title = menu.meta!.title
    }
    //如果menu.meta?.form?.refreshMenuCount==true ,则更新菜单上的数字
    if (menu.meta?.form?.refreshMenuCount) refreshMenuCount()
    //如果menu.meta?.form?.trigerUris存在，遍历menu.meta?.form?.trigerUris，请求相应接口
    if (menu.meta?.form?.trigerUris && menu.meta?.form?.trigerUris.length > 0) {
      menu.meta?.form?.trigerUris?.map((item) => {
        //定义传递的参数
        let params = {} as any
        item.params?.map((_item) => {
          composeRequestParams(params, _item, menu, null, row, null)
        })
        if (item.method === 'get' || item.method === 'GET') {
          globalRequestUrlApi(params, item.method!, item.uri!).then(async (res) => {})
        } else {
          globalRequestApi(params, item.method!, item.uri!).then(async (res) => {})
        }
      })
    }
  }
  const { searchParams } = toRaw(formStore)
  const showMenuDialog = (position: string, purpose: string, menu: MenuVO) => {
    //获取需要传递的formData中的属性保存到store中
    let mergeDialogFormParams: { [key: string]: any } = {}
    let storeDialogFormParams: { [key: string]: any } = {}
    const storeData = sideBarStore.$state.storeDialogFormParams || {}
    const storeFormData = sideBarStore.$state.mergeDialogFormParams || {}
    if (menu.meta.form?.storeData) {
      for (let item of menu.meta.form?.storeData!) {
        composeRequestParams(storeDialogFormParams, item, null, null, null, searchParams.value)
      }
    }
    let query: { [key: string]: any } = {}
    if (menu.meta.form?.query) {
      for (let item of menu.meta.form?.query!) {
        composeRequestParams(query, item, null, null, null, searchParams.value)
      }
    }
    // router.push({
    //   name: column.jump?.targetField,
    //   query: query,
    // });
    sideBarStore.$patch((state) => {
      state.btnMenuId = menu.meta.form?.menuId!
      state.btnMenuQuery = query
      state.mergeDialogFormParams = Object.assign(storeFormData, mergeDialogFormParams) //获取需要传递的formData中的属性保存到store中
      state.storeDialogFormParams = Object.assign(storeData, storeDialogFormParams) //获取需要传递的全局存储的属性保存到store中
    })
    detailDialogVisible.visible = true
    detailDialogVisible.title = menu.meta.form!.title
  }
  /**
   * 默认按钮操作
   * @param row
   * @param position
   * @param menu
   */
  const globalBtnForm = async (row: TableItem, position: string, menu: MenuVO) => {
    btnRequestUri.value = menu.meta?.form?.formUri ?? null
    if (menu.meta?.purpose === 'copy') {
      isEdit.value = false
    } else {
      isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1
    }
    buttonPosition.value = position //初始化表单
    const initFormData = resetFormGlobalFun(menu.meta?.dataColumn!)
    if (menu.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(rightBottomRef.value.tableConfig.tableItem)
    }
    // 根据按钮返回数据渲染当前表单，如果是下拉表单 或者是级联表单项，需要将返回数据设置到表单项的option.data上
    //遍历数组operationColumn.value，找到里面的form的type是select，将row中和form的name相同的项赋值他
    if (position === 'listTop' || position === 'listTabTop') {
      operationColumn.value.map((item: TableItem) => {
        if (item?.form?.type === 'select' || item?.form?.type === 'selectTree' || item?.form?.type === 'cascader') {
          if (row[item.form.name!]) {
            if (!item.form!.option!.data) {
              item.form!.option!.data = []
            }
            //如果row[item.form.name!] 类型是数组则赋值给item.form.option!.data
            if (Array.isArray(row[item.form.name!])) {
              item.form!.option!.data = row[item.form.name!]
            }
            delete row[item.form.name!]
          }
        }
      })
    }
    if (position === 'listRight' || position === 'listTabRight') {
      operationColumn.value.map((item: TableItem) => {
        if (item?.form?.type === 'select' || item?.form?.type === 'selectTree' || item?.form?.type === 'cascader') {
          // if (row[item!.form.name!]) {
          //   if (!item!.form!.option!.data) {
          //     item!.form!.option!.data = []
          //   }
          //   // item!.form.option!.data = row[item!.form.name!];
          //   // delete row[item.form.name!];
          // }
        }
      })
    }
    dialog.visible = true
    dialog.title = menu.meta?.title || '操作'
    const deepRow = JSON.parse(JSON.stringify(row))
    //如果是复制，并且operationColumn.value中的每项的form.canCopy为false，则将deepRow中的对应项的value设置为空
    if (menu.meta?.purpose === 'copy') {
      operationColumn.value.forEach((item: TableItem) => {
        if (item.form?.canCopy === false) {
          deepRow[item.form.name!] = null
        }
      })
      deepRow.id = undefined
    }
    await nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, deepRow)
    })
  }
  /**
   * 刷新singlePage
   */
  function refresh() {
    if (currentTabUri.value) {
      getBottomSinglePage(currentTabUri.value)
    }
  }
  /**
   * 更新菜单上的数字
   */
  function refreshMenuCount() {
    getMenuCount().then((res) => {
      sideBarStore.$patch((state) => {
        state.menuCount = res.data as any
      })
    })
  }
  /**
   * 表单提交
   */
  async function handleSubmit(formData: RoleForm, request: BtnRequestVO, isRefreshMenuCount: boolean) {
    btnLoading.value = true
    //如果mergeFormData有值，则合并到formData中
    if (JSON.stringify(mergeFormData.value) !== '{}') {
      formData = { ...formData, ...mergeFormData.value }
    }
    //如果mergeFormData有值，则合并到formData中
    if (JSON.stringify(storeFormParams.value) !== '{}') {
      formData = { ...formData, ...storeFormParams.value }
    }
    if (JSON.stringify(sideBarStore.$state.mergeDialogFormParams) !== '{}') {
      formData = { ...formData, ...sideBarStore.$state.mergeDialogFormParams }
    }
    if (JSON.stringify(sideBarStore.$state.storeDialogFormParams) !== '{}') {
      formData = { ...formData, ...sideBarStore.$state.storeDialogFormParams }
    }
    //如果是对象则使用公共请求
    if (request?.constructor === Object) {
      if (request.method === 'put' || request.method === 'PUT') {
        await updateItemApi(formData.id as string, formData, request.uri!)
          .then((res: any) => {
            ElMessage.success(res.message)
            closeDialog()
          })
          .finally(() => {
            btnLoading.value = false
            closeDialog()
          })
      } else {
        await globalRequestApi(formData, request.method!, request.uri!)
          .then(async (res: any) => {
            //request.secondDialog.result=true->进行二次弹窗(运力指派用到)
            if (request.secondDialog && request.secondDialog.result && typeof res.data === 'object') {
              if (res.data.length > 0) {
                // 合并运单
                mergeShipmentNoDialogRef.value.mergeShipmentNo = '' //初始化合并的运单
                mergeShipmentNoDialogRef.value.mergeShipmentNoVisible = true
                mergeShipmentNoDialogRef.value.mergeShipmentNoList = res.data
                mergeShipmentNoDialogRef.value.state.submitParams = formData
                mergeShipmentNoDialogRef.value.requestInfo.methods = request.secondDialog.method
                mergeShipmentNoDialogRef.value.requestInfo.uri = request.secondDialog.uri
              } else {
                await globalRequestApi(formData, request.secondDialog.method!, request.secondDialog.uri!).then(async (res: any) => {
                  ElMessage.success(res.message)
                  closeDialog()
                  if (mergeShipmentNoDialogRef.value) {
                    mergeShipmentNoDialogRef.value.mergeShipmentNoVisible = false
                    mergeShipmentNoDialogRef.value.state.submitParams = null
                    //刷新列表
                    getBottomTableList(currentTabUri.value!)
                  }
                })
              }
            } else {
              ElMessage.success(res.message)
              closeDialog()
            }
          })
          .finally(() => {
            btnLoading.value = false
          })
      }
      //否则使用老方法
    } else {
      await addItemApi(formData, request as any)
        .then((res: any) => {
          ElMessage.success(res.message)
          closeDialog()
          //如果是顶部按钮，则刷新客户列表
          if (buttonPosition.value === 'listTop') {
            getBottomTableList(currentTab.value?.meta?.uri!)
          } else {
            //如果是tab下的按钮，则刷新tab下的列表
            //默认获取当前客户下的用户列表
            getBottomTableList(currentTabUri.value!)
          }
          clearFormColumn()
        })
        .finally(() => (btnLoading.value = false))
    }
    nextTick(() => {
      if (isRefreshMenuCount) refreshMenuCount()
    })
  }
  /**
   * 初始化表单数据
   */
  const clearFormColumn = () => {
    operationColumn.value = []
    dialog.title = ''
  }
  /**
   * 关闭弹窗
   */
  function closeDialog() {
    dialog.visible = false
    approvalOperationDialog.visible = false
    if (rightBottomRef.value) {
      rightBottomRef.value.closeDialog()
    }
    resetForm()
  }
  /**
   * 获取表单项，用于表单中级联操作
   * @param formData 表单项
   * @param dependOn 当前修改的依赖项下拉
   */
  const getFormData = async (formData: any, dependOn: string) => {
    setTimeout(async () => {
      //获取form表单数据列
      const newOperationColumn = await getSelectOptions(operationColumn.value, formData, dependOn, 'formSelect')
    }, 200)
  }
  /**
   * 表单提交
   * @param formData 表单
   * @param uri 请求地址前缀
   */
  const loading = ref(false)
  const handleFormSubmit = useThrottleFn((formData: CustomerVO, request: any) => {
    loading.value = true
    //如果mergeFormData有值，则合并到formData中
    if (JSON.stringify(mergeFormData.value) !== '{}') {
      formData = { ...formData, ...mergeFormData.value }
    }
    //如果mergeFormData有值，则合并到formData中
    if (JSON.stringify(storeFormParams.value) !== '{}') {
      formData = { ...formData, ...storeFormParams.value }
    }
    if (JSON.stringify(sideBarStore.$state.mergeDialogFormParams) !== '{}') {
      formData = { ...formData, ...sideBarStore.$state.mergeDialogFormParams }
    }
    if (JSON.stringify(sideBarStore.$state.storeDialogFormParams) !== '{}') {
      formData = { ...formData, ...sideBarStore.$state.storeDialogFormParams }
    }
    const id = formData.id
    if (!formData.relationId) {
      formData.relationId = currentItem.value?.id
    }
    //如果是对象则使用公共请求
    if (request?.constructor === Object) {
      if (request.method === 'put' || request.method === 'PUT') {
        updateItemApi(formData.id as string, formData, request.uri!)
          .then((res: any) => {
            ElMessage.success(res.message)
            //如果是顶部按钮，则刷新客户列表
            if (buttonPosition.value === 'listTop') {
              getBottomTableList(currentTab.value?.meta?.uri!)
            } else {
              //如果是tab下的按钮，则刷新tab下的列表
              //默认获取当前客户下的列表
              getBottomTableList(currentTabUri.value!)
            }
            closeDialog()
          })
          .finally(() => (loading.value = false))
      } else {
        globalRequestApi(formData, request.method!, request.uri!)
          .then(async (res: any) => {
            ElMessage.success(res.message)
            //如果是顶部按钮，则刷新客户列表
            if (buttonPosition.value === 'listTop') {
              getBottomTableList(currentTab.value?.meta?.uri!)
            } else {
              //如果是tab下的按钮，则刷新tab下的列表
              //默认获取当前客户下的列表
              getBottomTableList(currentTabUri.value!)
            }
            closeDialog()
          })
          .finally(() => {
            loading.value = false
          })
      }
    } else if (id) {
      updateItemApi(id, formData, request || currentTabUri.value!)
        .then(() => {
          Reflect.deleteProperty(formData, 'id')
          ElMessage.success('修改成功')
          formDialogRef.value.resetForm()
          closeDialog()
          //如果是顶部按钮，则刷新客户列表
          if (buttonPosition.value === 'listTop') {
            getBottomTableList(currentTab.value?.meta?.uri!)
          } else {
            //如果是tab下的按钮，则刷新tab下的列表
            //默认获取当前客户下的列表
            getBottomTableList(currentTabUri.value!)
          }
          clearFormColumn()
        })
        .finally(() => {
          loading.value = false
        })
    } else {
      addItemApi(formData, request || currentTabUri.value!)
        .then(() => {
          ElMessage.success('新增成功')
          formDialogRef.value.resetForm()
          closeDialog()
          //如果是顶部按钮，则刷新客户列表
          if (buttonPosition.value === 'listTop') {
            getBottomTableList(currentTab.value?.meta?.uri!)
          } else {
            //如果是tab下的按钮，则刷新tab下的列表
            //默认获取当前客户下的用户列表
            getBottomTableList(currentTabUri.value!)
          }
          clearFormColumn()
        })
        .finally(() => {
          loading.value = false
        })
    }
  }, 3000)
  //下级审批操作
  /**
   *
   * @param row
   * @param position
   * @param menu
   */
  const approvalOperation = async (row: TableItem, position: string, menu: MenuVO) => {
    btnMenu.value = menu
    approvalOperationApi({ currentNo: row.currentNo }).then((res: any) => {
      nextTick(() => {
        Object.assign(approvalOperationDialogRef.value.formData, res.data)

        approvalOperationDialog.visible = true
        approvalOperationDialog.title = menu.meta?.title || '操作'
      })
    })
  }
  const handleApprovalOperationSubmit = async (formData: RoleForm, request: BtnRequestVO, isRefreshMenuCount: boolean) => {
    btnLoading.value = true
    //如果mergeFormData有值，则合并到formData中
    if (JSON.stringify(mergeFormData.value) !== '{}') {
      formData = { ...formData, ...mergeFormData.value }
    }
    //如果mergeFormData有值，则合并到formData中
    if (JSON.stringify(storeFormParams.value) !== '{}') {
      formData = { ...formData, ...storeFormParams.value }
    }
    if (JSON.stringify(sideBarStore.$state.mergeDialogFormParams) !== '{}') {
      formData = { ...formData, ...sideBarStore.$state.mergeDialogFormParams }
    }
    if (JSON.stringify(sideBarStore.$state.storeDialogFormParams) !== '{}') {
      formData = { ...formData, ...sideBarStore.$state.storeDialogFormParams }
    }
    //如果是对象则使用公共请求
    if (request?.constructor === Object) {
      await globalRequestApi(formData, request.method!, request.uri!)
        .then(async (res: any) => {
          ElMessage.success(res.message)
          closeDialog()
        })
        .finally(() => {
          btnLoading.value = false
        })
      //否则使用老方法
    } else {
      await addItemApi(formData, request as any)
        .then((res: any) => {
          ElMessage.success(res.message)
          closeDialog()
        })
        .finally(() => (btnLoading.value = false))
    }
    nextTick(() => {
      getBottomTableList(currentTab.value?.meta?.uri!)
      if (isRefreshMenuCount) refreshMenuCount()
    })
  }
  //车辆变更弹窗
  const vehicleChangeDialog = reactive<DialogOption>({
    title: '车辆变更',
    visible: false,
  })
  const vehicleChangeParams = ref({
    transportType: '',
    shipmentType: '',
  })
  /**
   * 车辆变更弹窗
   * @param transportType 发运方式  3-公路 6-铁路 7-水路 9-分拨
   * @param shipmentType 运单类型 1-短驳 2-干线倒板 3-干线直发  100-以上三个都要
   */
  function vehicleChange(position: string, type: string, menu: MenuVO, transportType: string, shipmentType: string) {
    vehicleChangeParams.value.transportType = transportType
    vehicleChangeParams.value.shipmentType = shipmentType
    vehicleChangeDialog.visible = true
  }
  /**
   * 关闭车辆变更弹窗
   */
  function closeVehicleChangeDialog() {
    vehicleChangeDialog.visible = false
  }
  /**
   * 提交换车
   * @param data 表单数据
   * @param tabActive 选中的tab 0 同承运商 1不同承运商
   */
  const exchangeVehicleResult = (data: ExchangeVehicleParamsVo, tabActive: number) => {
    let params: ExchangeVehicleParamsVo = JSON.parse(JSON.stringify(data))
    params.body!.forEach((v) => {
      delete v.addressType
    })
    transferVehicle(params!).then((res) => {
      exchangeVehcileComponentRef.value!.formData = {
        body: [
          {
            addressType: 1,
          },
        ],
        operationType: 1, //操作类型1-倒板 2-换车
        source: 1, //申请来源：0-调度 1-司机
        origin: {
          vehicleId: '',
          driverId: '',
        },
        shipmentNo: '',
      }
      vehicleChangeDialog.visible = false
      ElMessage.success('提交成功')
      //刷新列表
      getBottomTableList(currentTabUri.value!)
    })
  }
  /**
   * 重置密码
   */
  function updatePassword(row: any) {
    // userInfo.value = row;
    ElMessageBox.prompt('请输入用户「' + row.driverRealName + '」的新密码', '重置密码', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })
      .then(({ value }) => {
        if (!value) {
          ElMessage.warning('请输入新密码')
          return false
        }
        updateUserPassword(row.id, value, currentTabUri.value!).then(() => {
          ElMessage.success('密码修改成功，新密码是：' + value)
        })
      })
      .catch(() => {})
  }
  /**
   * 自定义提示弹窗（重新校验）
   */
  async function confirmDialog(row: TableItem, position: string, meta: MetaVO) {
    //定义传递的参数
    let params = {} as any
    const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
    meta?.form?.btns![0].params?.map((item) => {
      composeRequestParams(params, item, null, storeDataParams, row, queryParams)
    })
    if (meta.form?.btns![0].method === 'post' || meta.form?.btns![0].method === 'POST') {
      const { message } = await refreshCheckPostApi(meta.form.btns![0].uri!, params)
      ElMessage.success(message)
    } else if (meta.form?.btns![0].method === 'get' || meta.form?.btns![0].method === 'GET') {
      const { message } = await refreshCheckGetApi(meta.form.btns![0].uri!, params)
      ElMessage.success(message)
    }
    //刷新列表
    getBottomTableList(currentTabUri.value!)
  }
  /**
   * 关闭弹窗并清除之前的全局查询条件
   */
  const closeDetailDialogVisible = () => {
    const meta = proxy.$sideBarStore.$state.meta

    //如果menu.meta.ext.refreshPage===true ,则刷新列表页
    if (meta && meta.ext?.refreshPage) {
      //刷新列表
      getBottomTableList(currentTabUri.value!)
    }
    sideBarStore.$patch((state) => {
      state.btnMenuQuery = {}
      state.mergeDialogFormParams = {}
      state.storeDialogFormParams = {}
    })
    isFullscreen.value = false
  }

  const sortChange = (data: { column: string; direction: string }[]) => {
    //查询列表
    //&customerOrderBy=notInvoicedMount desc以这种形式频道拼到请求参数后
    const customerOrderBy = data.map((item) => {
      return item.column + ' ' + item.direction
    })
    bottomListQueryParams.customerOrderBy = customerOrderBy.join(',')
    //刷新列表
    getBottomTableList(currentTabUri.value!)
  }
  /**
   * 右侧消息提醒文字
   */
  const handleMsgQuery = () => {
    //遍历rightTipDataObj.value.timeoutParam对象，将对象中的每项拼到查询参数上，重新查询列表
    const timeoutParam = rightTipDataObj.value!.timeoutParam
    const timeoutParamList = Object.keys(timeoutParam as Record<string, any>)
    timeoutParamList.forEach((item) => {
      bottomListQueryParams[item] = (timeoutParam as Record<string, any>)[item]
    })
    //刷新列表
    getBottomTableList(currentTabUri.value!)
  }
  defineExpose({
    changeTab,
    currentTab,
  })
</script>
<style scoped lang="scss">
  .statusText {
    overflow: hidden;
  }
</style>
