<!--
 * @Author: llm 
 * @Date: 2023-06-30 16:51:34
 * @LastEditors: llm 
 * @LastEditTime: 2023-07-21 09:48:56
 * @Description: 部门树组件
 * 
-->
<template>
  <el-card shadow="never">
    <div class="flex-row items-center">
      <el-input v-model="searchDeptName" placeholder="部门名称" clearable>
        <template #prefix>
          <i-ep-search />
        </template>
      </el-input>
      <el-tooltip effect="light" content="重置" placement="top">
        <el-icon color="#999999" style="margin-left: 10px; cursor: pointer" @click="refresh"><Refresh /></el-icon>
      </el-tooltip>
    </div>

    <el-tree
      ref="deptTreeRef"
      class="mt-2"
      :data="deptList"
      :highlight-current="true"
      node-key="deptId"
      :props="{ children: 'children', label: 'deptName', disabled: '' }"
      :expand-on-click-node="false"
      :filter-node-method="handleDeptFilter"
      default-expand-all
      @node-click="handleDeptNodeClick"
      v-loading="deptTreeLoading"
    ></el-tree>
  </el-card>
</template>
<script setup lang="ts">
  import { DeptVO } from '@/api/authorityManagement/CorporateAccountManagement/DeptManagement/types'
  const emit = defineEmits(['handleQuery', 'refresh'])
  const searchDeptName = ref()
  const deptTreeRef = ref() // 部门树
  const deptTreeLoading = ref(false) // 加载部门树数据
  defineProps({
    deptList: {
      require: true,
      type: Array as PropType<DeptVO[]>,
      default: () => {
        return []
      },
    },
  })
  watchEffect(
    () => {
      deptTreeRef.value.filter(searchDeptName.value)
    },
    {
      flush: 'post', // watchEffect会在DOM挂载或者更新之前就会触发，此属性控制在DOM元素更新后运行
    },
  )
  /**
   * 部门筛选
   */
  function handleDeptFilter(value: string, data: any) {
    if (!value) {
      return true
    }
    return data.deptName.indexOf(value) !== -1
  }
  /**
   * 部门树节点
   */
  function handleDeptNodeClick(data: DeptVO) {
    emit('handleQuery', data.deptId)
  }
  function refresh() {
    deptTreeRef.value.setCurrentKey(null)
    emit('refresh')
  }
  defineExpose({
    deptTreeLoading, //加载部门树
  })
</script>
