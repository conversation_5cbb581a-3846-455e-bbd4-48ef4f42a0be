<!--
 * @Author: llm
 * @Date: 2023-06-29 09:06:23
 * @LastEditors: llm
 * @LastEditTime: 2024-10-29 17:10:28
 * @Description: 菜单权限弹窗
 *
-->
<template>
  <el-dialog :draggable="true" v-model="menuDialogVisible.visible" :fullscreen="true" :show-close="false" width="800px">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <div :id="titleId" :class="titleClass">
          {{ '【' + checkedUser.name + '】菜单列表项' }}
        </div>
        <el-button type="danger" @click="close">
          <el-icon class="el-icon--left"><CircleCloseFilled /></el-icon>
          关闭
        </el-button>
      </div>
    </template>
    <menuTableColumnDialog
      :menuColumnDialogVisible="menuColumnDialogVisible"
      :loading="menuColumnLoading"
      :checked-menu="checkedMenu"
      :checked-user="checkedUser"
      @selectMenuColumn="selectMenuColumn"
      @closeMenuTableColumnDialog="closeMenuTableColumnDialog"
    />
    <el-scrollbar v-loading="loading" max-height="85vh">
      <TableComponent
        ref="tableComponentRef"
        :loading="loading"
        :tableData="menuList"
        :tableConfig="tableConfig"
        :tree-props="{
          children: 'children',
          hasChildren: 'hasChildren',
        }"
        :defaultExpandAll="true"
        :buttonPermissionGroup="props.buttonPermissionGroup"
        row-key="menuId"
        @editAuth="updateItem"
        @defaultHandle="updateItem"
      />
    </el-scrollbar>
  </el-dialog>
</template>
<script setup lang="ts">
  import { CheckedMenuColumnVO, UserPageVO } from '@/api/authorityManagement/CorporateAccountManagement/PersonalManagement/types'
  import menuTableColumnDialog from './menuTableColumnDialog.vue'
  import { PropType } from 'vue'
  const props = defineProps({
    /**
     * 加载中
     */
    loading: {
      require: true,
      type: Boolean,
      default: false,
    },
    /**
     * 弹窗属性
     */
    menuDialogVisible: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
    /**
     * 选中的角色信息
     */
    checkedUser: {
      require: true,
      type: Object as PropType<UserPageVO>,
      default: () => {
        return {}
      },
    },
    /**
     * 菜单列表
     */
    menuList: {
      require: true,
      type: Array as PropType<MenuVO[]>,
      default: () => {
        return []
      },
    },
    /**
     * 按钮组权限
     */
    buttonPermissionGroup: {
      require: true,
      type: Array as PropType<MenuVO[]>,
      default: () => {
        return [
          {
            menuId: '88010100002002',
            parentId: '88010100000000',
            tableId: null,
            type: 2,
            name: null,
            path: null,
            component: null,
            redirect: null,
            meta: {
              menuId: '88010100002002',
              parentId: '88010100000000',
              type: 2,
              title: '编辑',
              keepAlive: true,
              hidden: null,
              position: 'listRight',
              purpose: 'editAuth',
              uri: null,
              remark: null,
              operation: true,
              icon: 'Edit',
              background: '#1c94fa',
              mapType: null,
              dataColumn: [],
              form: null,
              dependsOn: null,
              jump: null,
              ext: null,
            },
            children: [],
          },
        ]
      },
    },
  })
  const menuColumnDialogVisible = reactive<DialogOption>({
    visible: false,
  })
  /**
   * 加载数据列loading
   */
  const menuColumnLoading = ref(false)
  const tableComponentRef = ref()

  const checkedMenu = ref<MenuVO>()
  const emit = defineEmits([
    'getListMenuOptions', //获取当前菜单下的数据列
    'selectMenuColumn', //选择的数据列
  ])
  /**
   * 列表配置项
   */
  const tableConfig = reactive<TableConfig>({
    showHandleSelection: true,
    showSort: true,
    tableItem: [
      {
        name: 'title',
        label: '菜单名称',
        align: 'left',
        type: 'tree',
        width: '250px',
        listEnable: true,
      },
      {
        listEnable: true,
        name: 'type',
        label: '类型',
        align: 'center',
        type: 'tag',
        tagNames: 'type',
        width: '100px',
      },
      {
        listEnable: true,
        name: 'columnName',
        label: '列表项',
        align: 'left',
        type: 'tag',
      },
    ],
    operation: {
      label: '操作',
      items: [],
      width: '100px',
    },
  })

  /**
   * 更改当前菜单数据列
   */
  const updateItem = (row: MenuVO) => {
    emit('getListMenuOptions', row)
    checkedMenu.value = row
    menuColumnDialogVisible.visible = true
  }
  /**
   * 关闭分配数据列弹窗
   */
  const closeMenuTableColumnDialog = () => {
    menuColumnDialogVisible.visible = false
  }
  /**
   * 选择的数据列
   */
  const selectMenuColumn = (menuColumn: CheckedMenuColumnVO) => {
    emit('selectMenuColumn', menuColumn)
  }
  defineExpose({
    menuColumnDialogVisible,
    menuColumnLoading,
  })
</script>
<style scoped lang="scss">
  .my-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
</style>
