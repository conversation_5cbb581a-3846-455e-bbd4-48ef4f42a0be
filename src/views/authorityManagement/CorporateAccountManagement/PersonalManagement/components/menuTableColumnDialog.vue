<!--
 * @Author: llm
 * @Date: 2023-06-29 09:06:23
 * @LastEditors: llm
 * @LastEditTime: 2023-10-17 10:19:15
 * @Description: 菜单+数据列权限弹窗
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <el-dialog :draggable="true" v-model="menuColumnDialogVisible.visible" :title="'【' + checkedMenu.title + '】列表项分配'" width="800px">
    <el-scrollbar v-loading="loading" max-height="600px">
      <el-checkbox label="全选" v-model="checkAll" @change="handleCheckAllChange"></el-checkbox>
      <el-checkbox label="反选" v-model="checkedAll" @change="handleCheckedAllChange"></el-checkbox>
      <el-divider />
      <el-checkbox-group v-model="columnList" v-if="menuColumnList.length > 0">
        <el-checkbox :label="item" v-for="(item, index) in menuColumnList" :key="index">
          {{ item.label }}
        </el-checkbox>
      </el-checkbox-group>
      <el-empty :description="'当前菜单【' + checkedMenu.title + '】未分配任何列表项'" v-else />
    </el-scrollbar>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleMenuColumnSubmit">确 定</el-button>
        <el-button @click="closeMenuDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
  import { CheckedMenuColumnItemVO, MenuColumnVO, UserPageVO } from '@/api/authorityManagement/CorporateAccountManagement/PersonalManagement/types'
  import { CheckedRole } from '@/api/authorityManagement/RoleManagement/types'
  import { PropType } from 'vue'
  import bus from '@/utils/bus'
  const props = defineProps({
    /**
     * 加载中
     */
    loading: {
      require: true,
      type: Boolean,
      default: false,
    },
    /**
     * 弹窗属性
     */
    menuColumnDialogVisible: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
    /**
     * 选中的菜单信息
     */
    checkedMenu: {
      require: true,
      type: Object as PropType<MenuVO>,
      default: () => {
        return {}
      },
    },
    /**
     * 选中的用户信息
     */
    checkedUser: {
      require: true,
      type: Object as PropType<UserPageVO>,
      default: () => {
        return {}
      },
    },
  })

  const emit = defineEmits(['closeMenuTableColumnDialog', 'selectMenuColumn'])
  const columnList = ref() //当前用户选中菜单的数据列
  const checkAll = ref(false) //全选
  const checkedAll = ref(false) //反选
  const menuColumnList = ref<MenuColumnVO[]>([]) //当前登录用户下的当前菜单数据列
  /**
   * 监听当前登录用户选中的菜单下的数据列
   */
  bus.on('currentLoginUserMemuColumnList', (e: any) => {
    menuColumnList.value = e
  })
  /**
   * 监听当前菜单下的数据列
   */
  bus.on('userMemuColumnList', (e: any) => {
    columnList.value = e
  })
  watch(
    () => columnList.value,
    (newVal, oldVal) => {
      if (newVal.length === menuColumnList.value.length) {
        checkAll.value = true
      } else {
        checkAll.value = false
      }
    },
  )
  //全选
  const handleCheckAllChange = (e: any) => {
    if (e) {
      columnList.value = menuColumnList.value
    } else {
      columnList.value = []
    }
  }
  //反选
  const handleCheckedAllChange = (e: any) => {
    let diffArray: CheckedMenuColumnItemVO[] = []
    diffArray = menuColumnList.value.filter((item) => !columnList.value.some((_item: MenuColumnVO) => _item.columnId === item.columnId))
    columnList.value = diffArray
  }
  /**
   * 菜单分配数据列提交
   */
  const handleMenuColumnSubmit = () => {
    let columns: CheckedMenuColumnItemVO[] = []
    //找出2个数组中的相同项
    for (let i = 0; i < menuColumnList.value.length; i++) {
      for (let j = 0; j < columnList.value.length; j++) {
        if (isEqual(menuColumnList.value[i], columnList.value[j])) {
          columns.push({
            columnId: menuColumnList.value[i].columnId,
            sortNo: menuColumnList.value[i].sortNo,
            label: menuColumnList.value[i].label,
          })
          break
        }
      }
    }
    const menuColumn = {
      roleId: props.checkedUser.id,
      menuId: props.checkedMenu.menuId,
      tableId: props.checkedMenu.tableId,
      columns,
    }
    emit('selectMenuColumn', JSON.stringify(menuColumn))
  }
  /**
   * 判断2个对象是否相同
   * @param obj1
   * @param obj2
   */
  function isEqual(obj1: MenuColumnVO, obj2: MenuColumnVO) {
    // 根据你的需求来比较两个对象的属性值是否相等
    // 这里假设对象的属性值为字符串类型
    return obj1.columnId === obj2.columnId
  }
  /**
   * 关闭弹窗
   */
  const closeMenuDialog = () => {
    emit('closeMenuTableColumnDialog')
  }
</script>
<style scoped lang="scss">
  :deep(.el-divider--horizontal) {
    margin: 4px 0;
  }
</style>
