<!--
 * @Author: llm
 * @Date: 2023-06-29 09:06:23
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-07-24 18:07:37
 * @Description: 重置密码弹窗
 *
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <el-dialog :draggable="true" v-model="passwordDialogVisible.visible" :close-on-click-modal="false" :show-close="false" width="800px">
    <el-scrollbar max-height="600px"></el-scrollbar>
  </el-dialog>
</template>
<script setup lang="ts">
  const props = defineProps({
    /**
     * 弹窗属性
     */
    passwordDialogVisible: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
  })
  const password = ref<string>() //新密码
  const emit = defineEmits([
    'resetPassword', //新密码
  ])
</script>
<style scoped lang="scss">
  .my-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
</style>
