<!--
 * @Author: llm 
 * @Date: 2023-06-30 12:16:47
 * @LastEditors: llm
 * @LastEditTime: 2025-02-14 11:10:01
 * @Description: 顶部搜索组件
 * 
-->
<template>
  <div class="search-container">
    <el-form ref="queryFormRef" :model="queryParams" :inline="true">
      <el-form-item label="关键字" prop="deptName">
        <el-input v-model="queryParams.deptName" placeholder="部门名称" @keyup.enter="handleQuery" clearable />
      </el-form-item>

      <el-form-item label="部门状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="部门状态" clearable>
          <el-option :value="0" label="启用" />
          <el-option :value="1" label="禁用" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button class="filter-item" type="primary" @click="handleQuery">
          <i-ep-search />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <i-ep-refresh />
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup lang="ts">
  import { DeptQuery } from '@/api/authorityManagement/CorporateAccountManagement/DeptManagement/types'
  import defaultSettings from '@/settings'
  const emit = defineEmits(['handleQuery'])
  const queryFormRef = ref(ElForm)
  const queryParams = reactive<any>({ page: 1, limit: defaultSettings.globalLimit })
  const handleQuery = () => {
    emit('handleQuery', queryParams)
  }
  /**
   * 重置查询
   */
  function resetQuery() {
    queryFormRef.value.resetFields()
    // emit("resetQuery");
    handleQuery()
  }
  defineExpose({
    resetQuery,
  })
</script>
