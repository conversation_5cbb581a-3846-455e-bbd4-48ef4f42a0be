<!--
 * @Author: llm
 * @Date: 2023-06-29 09:06:23
 * @LastEditors: llm
 * @LastEditTime: 2023-07-20 14:26:45
 * @Description: 菜单+按钮权限弹窗
 *
-->
<template>
  <el-dialog :draggable="true" v-model="menuDialogVisible.visible" :title="'【' + checkedRole.name + '】权限分配'" width="800px">
    <el-scrollbar v-loading="menuTreeLoading" max-height="600px">
      <el-tree
        ref="menuRef"
        node-key="menuId"
        show-checkbox
        :data="menuList"
        :highlight-current="true"
        :default-checked-keys="checkedRole.menuIds"
        check-strictly
        :default-expand-all="true"
        :expand-on-click-node="false"
        @check-change="handleCheckChange"
      >
        <template #default="{ data }">
          {{ data.meta.title }}
          <el-tag v-if="data.meta.type === 1" type="success" style="margin-left: 10px" size="small">菜单</el-tag>
          <el-tag v-else-if="data.meta.type === 2" type="danger" style="margin-left: 10px" size="small">按钮</el-tag>
          <el-tag v-else-if="data.meta.type === 3" type="success" style="margin-left: 10px" size="small">Tab</el-tag>
          <el-tag v-else-if="data.meta.type === 4" type="success" style="margin-left: 10px" size="small">目录</el-tag>
        </template>
      </el-tree>
    </el-scrollbar>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleRoleMenuSubmit">确 定</el-button>
        <el-button @click="closeMenuDialog">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
  import { CheckedRole } from '@/api/authorityManagement/RoleManagement/types'
  import { PropType } from 'vue'
  const props = defineProps({
    /**
     * 加载中
     */
    menuTreeLoading: {
      require: true,
      type: Boolean,
      default: false,
    },
    /**
     * 弹窗属性
     */
    menuDialogVisible: {
      require: true,
      type: Object as PropType<DialogOption>,
      default: () => {
        return {}
      },
    },
    /**
     * 选中的角色信息
     */
    checkedRole: {
      require: true,
      type: Object as PropType<CheckedRole>,
      default: () => {
        return {}
      },
    },
    /**
     * 菜单列表
     */
    menuList: {
      require: true,
      type: Array as PropType<OptionType[]>,
      default: () => {
        return []
      },
    },
    /**
     * 选中的菜单
     */
    checkedIds: {
      require: true,
      type: Array<string>,
      default: () => {
        return []
      },
    },
  })

  const emit = defineEmits(['closeMenuDialog', 'selectMenuIds'])
  const menuRef = ref()

  watch(
    () => props.checkedRole.menuIds,
    (menuIds) => {
      nextTick(() => {
        menuIds?.forEach((menuId) => {
          menuRef.value!.setChecked(menuId, true, false)
        })
      })
    },
  )
  /**
   * 角色分配菜单提交
   */
  const handleRoleMenuSubmit = () => {
    const roleId = props.checkedRole.id
    if (roleId) {
      const checkedMenuIds: string[] = menuRef.value.getCheckedNodes().map((node: any) => node.menuId)
      emit('selectMenuIds', checkedMenuIds, roleId)
    }
  }
  /**
   * 关闭弹窗
   */
  const closeMenuDialog = () => {
    emit('closeMenuDialog')
  }
  /**
   * 选中子节点同时选中
   * @param data 选中的菜单项
   * @param checked
   */
  const handleCheckChange = (data: any, checked: boolean) => {
    if (checked) {
      // 选中父节点
      menuRef.value.setChecked(data.parentId, true)
    }
  }
  defineExpose({
    menuRef,
  })
</script>
