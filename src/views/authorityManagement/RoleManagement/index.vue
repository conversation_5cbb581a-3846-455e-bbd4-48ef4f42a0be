<!--
 * @Author: llm
 * @Date: 2023-06-27 12:05:28
 * @LastEditors: llm
 * @LastEditTime: 2025-02-14 11:10:17
 * @Description: 角色管理
 *
-->

<template>
  <div class="app-container">
    <el-card shadow="never" style="margin-bottom: 20px" class="top-query">
      <topQueryGroupComponent
        ref="topQueryGroupComponentRef"
        :query-permission-group="topQueryConfig.tableItem"
        @handleSearchQuery="handleSearchQuery"
        @queryParams="getQueryData"
      />
    </el-card>
    <el-card shadow="never">
      <template #header>
        <!-- 按钮组 -->
        <button-group-component
          ref="topButtonGroupComponent"
          :ids="ids"
          :buttonPermissionGroup="listTopOperation"
          @addItem="addItem"
          @handleDelete="handleDelete"
          @defaultHandle="defaultHandle"
        />
      </template>
      <TableComponent
        :loading="loading"
        :tableData="tableData"
        :tableConfig="tableConfig"
        :buttonPermissionGroup="listRightOperation"
        @batchDelete="batchDeleteFun"
        @deleteItem="deleteItemFun"
        @updateItem="updateItem"
        @openMenuDialog="openMenuDialog"
        @handleStatusChange="handleStatusChange"
        @defaultHandle="globalBtnForm"
      />
      <pagination
        v-if="total >= queryParams.limit"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="_pagination"
      />
      <!-- 新增or编辑组件 -->
      <form-dialog
        v-if="dialog.visible"
        ref="formDialogRef"
        :dialog="dialog"
        :isEdit="isEdit"
        :dataColumn="tableConfig.tableItem"
        :requestUri="requestUri"
        @closeDialog="closeDialog"
        @handleSubmit="handleSubmit"
      />
      <!-- 菜单 弹窗 -->
      <menu-dialog
        ref="menuDialogRef"
        :menu-tree-loading="menuTreeLoading"
        :menuList="menuList"
        :checkedRole="checkedRole"
        :menuDialogVisible="menuDialogVisible"
        @closeMenuDialog="closeMenuDialog"
        @selectMenuIds="selectMenuIds"
      />
    </el-card>
  </div>
</template>
<script setup lang="ts">
  defineOptions({
    name: 'RoleManagement',
    inheritAttrs: false,
  })
  import { listMenuOptions, setMenus } from '@/api/authorityManagement/RoleManagement'
  import buttonGroupComponent from '@/components/TopButtonGroupComponent/index.vue'
  import formDialog from '@/components/FormDialogComponent/index.vue' //表单弹窗
  import menuDialog from './components/menuDialog.vue'
  import { RolePageVO, RoleForm, CheckedRole } from '@/api/authorityManagement/RoleManagement/types'

  import { getSelectOptions, getcurrentUserMenuColumnlist, resetFormGlobalFun, switchChangeGlobalFun, composeRequestParamsMultiRow } from '@/utils/common'
  import { addItemApi, deleteItemApi, getListPage, batchDeleteApi, updateItemApi } from '@/api/auth'
  import { FormColumn } from '@/types/global'
  import { globalRequestApi, globalRequestUrlApi } from '@/api/planManagement'
  // 状态管理依赖
  import { useFormStore } from '@/store/modules/form.js'
  import defaultSettings from '@/settings'

  const formStore = useFormStore()
  const { routerParams } = storeToRefs(formStore)
  const { proxy }: any = getCurrentInstance()

  //优先使用列表中的携带的参数，再使用地址栏参数
  const routeParams = proxy.$sideBarStore.$state.btnMenuQuery ? proxy.$sideBarStore.$state.btnMenuQuery : routerParams.value
  const topQueryGroupComponentRef = ref()
  const formDialogRef = ref(ElForm)
  const menuDialogRef = ref()

  /**
   * 当前是否为编辑状态
   */
  const isEdit = ref(false)
  /**
   * 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   */
  const buttonPosition = ref<string>()
  /**
   * 请求地址前缀
   */
  const requestUri = ref<string>()
  /**
   * 加载状态
   */
  const loading = ref(false)
  /**
   * 菜单树加载状态
   */
  const menuTreeLoading = ref(false)
  /**
   * 删除的列表ids
   */
  const ids = ref<string[]>([])
  /**
   * 总数
   */
  const total = ref(0)
  /**
   * 选中的角色
   */
  const checkedRole = ref<CheckedRole>({})
  /**
   * 菜单列表
   */
  const menuList = ref<OptionType[]>([])
  const listRightOperation = ref<MenuVO[]>() //列表右侧按钮权限
  const listTopOperation = ref<MenuVO[]>() //列表顶部按钮权限
  /**
   * 新增or编辑弹窗
   */
  const dialog = reactive<DialogOption>({
    visible: false,
  })
  /**
   * 菜单弹窗
   */
  const menuDialogVisible = reactive<DialogOption>({
    visible: false,
  })
  /**
   * meta
   */
  const metaInfo = ref<MetaVO>()
  /**
   * 按钮下的表单
   */
  const operationColumn = ref<any>()
  /**
   * 按钮的请求地址前缀
   */
  const btnRequestUri = ref<string | null>()
  /**
   * menu
   */
  const btnMenu = ref<MenuVO>()
  /**
   * 选择的列表项数据
   */
  const selectTableColumn = ref<any[]>([])
  /**
   * 列表配置项
   */
  const tableConfig = reactive<TableConfig>({
    showHandleSelection: true,
    showSort: true,
    tableItem: [],
    operation: {
      label: '操作',
      items: [],
      width: '120px',
    },
  })
  /**
   * 顶部搜索配置项
   */
  const topQueryConfig = reactive<TableConfig>({ tableItem: [] })
  /**
   * 查询参数
   */
  const queryParams = reactive<any>({
    page: 1,
    limit: defaultSettings.globalLimit,
  })
  /**
   * 列表数据
   */
  const tableData = ref<RolePageVO[]>()
  onBeforeMount(async () => {
    // 动态设置菜单数据列
    const {
      children,
      meta,
    }: {
      children: MenuVO[]
      meta: MetaVO
    } = await getcurrentUserMenuColumnlist(proxy.$sideBarStore.$state.menuId)
    metaInfo.value = meta
    //右侧按钮组
    listRightOperation.value = children.filter((item: MenuVO) => item.meta?.position === 'listRight')
    //顶部按钮组
    listTopOperation.value = children.filter((item: MenuVO) => item.meta?.position === 'listTop')
    //获取form表单数据列
    const dataColumn: any = await getSelectOptions(meta.dataColumn)
    requestUri.value = meta.uri
    topQueryConfig.tableItem = tableConfig.tableItem = dataColumn
    //如果meta.operation=false 则不展示列表右侧操作列
    if (!meta.operation) {
      tableConfig.operation = undefined
    }
    //遍历routeParams对象，queryParams[key] = routeParams[key]

    for (const key in routeParams) {
      if (Object.prototype.hasOwnProperty.call(routeParams, key)) {
        //如果属性中包含range字符串，则把首字母大写，然后拆分2个，分别拼上start 和 end ,并删除原有属性
        if (key!.indexOf('Range') !== -1 && (routeParams[key] as any)) {
          const startName = 'start' + key.charAt(0).toUpperCase() + key.slice(1)
          const endName = 'end' + key.charAt(0).toUpperCase() + key.slice(1)
          queryParams[startName] = routeParams[key]![0]
          queryParams[endName] = routeParams[key]![1]
          delete queryParams[key]
        } else {
          queryParams[key] = routeParams[key]
        }
        const item = topQueryConfig.tableItem.find((item) => item.query?.name === key && item.query?.option?.multiple)
        if (item) {
          topQueryGroupComponentRef.value.queryParams[key] = routeParams[key] ? routeParams[key].split(',') : []
        } else {
          topQueryGroupComponentRef.value.queryParams[key] = routeParams[key]
        }
        //初始化搜索条件
        // formStore.setSearchParams({});
      }
    }
    handleQuery()
  })
  /**
   * 获取表单项，用于表单中级联操作
   * @param formData 表单项
   * @param dependOn 当前修改的依赖项下拉
   */
  const getQueryData = async (formData: any, dependOn: string) => {
    //获取form表单数据列
    const newOperationColumn = await getSelectOptions(metaInfo.value!.dataColumn, formData, dependOn, 'topQuerySelect')
  }
  /**
   * 分页
   */
  const _pagination = () => {
    handleQuery()
  }

  /**
   * 查询
   */
  function handleQuery() {
    loading.value = true
    getListPage(queryParams, requestUri.value!)
      .then(({ data }) => {
        tableData.value = data.rows
        total.value = data.total
        refreshQuerySelectOptions()
      })
      .finally(() => {
        loading.value = false
      })
  }

  /**
   * 更新查询条件下拉
   */
  const refreshQuerySelectOptions = async () => {
    //重新获取form表单数据列，刷新筛选下拉项
    const dataColumn: any = await getSelectOptions(metaInfo.value!.dataColumn, null, '', 'topQuerySelect')
    topQueryConfig.tableItem = dataColumn
  }
  /**
   * 更新表单下拉
   */
  const refreshFormSelectOptions = async (dataColumn: FormColumn[]) => {
    //重新获取form表单数据列，刷新筛选下拉项
    tableConfig.tableItem = await getSelectOptions(dataColumn, null, '', 'formSelect')
  }

  /**
   * 重置查询
   */
  function resetQuery() {
    queryParams.page = 1
    handleQuery()
  }

  /**
   * 角色表单提交
   */
  const handleSubmit = async (formData: RoleForm) => {
    loading.value = true
    const _id = formData.id
    if (_id) {
      updateItemApi(_id, formData, requestUri.value!)
        .then(async () => {
          ElMessage.success('修改成功')
          resetQuery()
          closeDialog()
        })
        .finally(() => (loading.value = false))
    } else {
      addItemApi(formData, requestUri.value!)
        .then(async () => {
          ElMessage.success('新增成功')
          resetQuery()
          closeDialog()
        })
        .finally(() => (loading.value = false))
    }
  }
  /**
   * 修改状态
   */
  const handleStatusChange = async (row: { [key: string]: any }) => {
    await switchChangeGlobalFun(row.enable, row.roleName, row.id, requestUri.value!, '角色')
      .then(async () => {
        //修改成功后刷新列表
        await handleQuery()
      })
      .catch(() => {
        //取消切换，恢复状态
        row.enable = row.enable !== true
      })
  }

  /**
   * 关闭弹窗
   */
  function closeDialog() {
    dialog.visible = false
  }

  /**
   * 关闭菜单弹窗
   */
  function closeMenuDialog() {
    menuDialogVisible.visible = false
  }

  /**
   * 重置表单
   */
  function resetForm() {
    formDialogRef.value.resetForm()
  }

  /**
   * 批量删除
   * @param arr 选中的列表项数据
   */
  const batchDeleteFun = (arr: any[]) => {
    ids.value = arr.map((item) => {
      return item.id
    })
  }
  /**
   * 删除当前行
   * @param row 当前行数据
   */
  const handleDelete = (id?: string[]) => {
    const _ids = [id || ids.value].join(',')
    if (_ids.length <= 0) {
      ElMessage.warning('请勾选删除项')
      return
    }
    loading.value = true
    batchDeleteApi(_ids, requestUri.value!)
      .then(() => {
        ElMessage.success('删除成功')
        resetQuery()
      })
      .finally(() => (loading.value = false))
  }

  const deleteItemFun = (row: any) => {
    deleteItemApi(row.id!, requestUri.value!).then(() => {
      ElMessage.success('删除成功')
      resetQuery()
    })
  }
  /**
   * 新增
   * @param position 按钮位置
   */
  const addItem = (position: string) => {
    buttonPosition.value = position
    dialog.visible = true
    isEdit.value = false
    dialog.title = '新增'
    refreshFormSelectOptions(tableConfig.tableItem)
  }
  /**
   * 更新数据
   * @param row 当前行数据
   */
  const updateItem = (row: TableItem, position: string) => {
    dialog.visible = true
    isEdit.value = true
    dialog.title = '修改'
    buttonPosition.value = position
    //初始化表单
    const initFormData = resetFormGlobalFun(tableConfig.tableItem!)
    nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, row)
    })
    refreshFormSelectOptions(tableConfig.tableItem)
  }
  /**
   * 分配权限
   * @param row 当前行数据
   */
  const openMenuDialog = (row: any) => {
    menuDialogVisible.visible = true
    checkedRole.value.id = row.id
    checkedRole.value.roleName = row.roleName
    checkedRole.value.menuIds = row.menuIds
    getListMenuOptions()
  }
  /**
   * 获取菜单下拉树形列表(包含按钮)
   */
  const getListMenuOptions = () => {
    menuTreeLoading.value = true
    listMenuOptions()
      .then((res) => {
        const { data } = res
        data.forEach((value) => {
          menuDialogRef.value.menuRef.setChecked(value, true, false)
        })
        menuList.value = data
        menuTreeLoading.value = false
      })
      .catch((err) => {
        menuTreeLoading.value = false
      })
  }
  /**
   * 获取当前角色选中的菜单
   */
  const selectMenuIds = (menuIds: string[], roleId: string) => {
    const params = {
      roleId,
      menuIds,
    }
    loading.value = true
    setMenus(params)
      .then((res) => {
        ElMessage.success('分配成功')
        menuDialogVisible.visible = false
        resetQuery()
      })
      .finally(() => {
        loading.value = false
        checkedRole.value.menuIds = []
      })
  }
  /**
   * 筛选条件查询
   * @param searchParams 查询条件
   */
  const handleSearchQuery = (searchParams: any) => {
    //遍历queryParams对象，删除所有属性
    for (const key in queryParams) {
      queryParams.limit = defaultSettings.globalLimit
    }
    for (const key in searchParams) {
      queryParams[key] = searchParams[key]
    }
    handleQuery()
  }
  /**
   * 按钮默认操作
   * @param position 按钮位置
   * @param type 按钮类型 或者是menuId
   * @param menu
   */
  const defaultHandle = async (position: string, type: string, menu: MenuVO) => {
    btnMenu.value = menu
    btnRequestUri.value = menu.meta!.uri
    isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1
    if (menu.meta?.form) {
      if (ids.value.length < menu.meta?.form?.min!) {
        ElMessage.warning(`至少选择${menu.meta?.form?.min}项`)
        return
      } else if (ids.value.length > menu.meta?.form?.max!) {
        ElMessage.warning(`最多选择${menu.meta?.form?.max}项`)
        return
      }
    }
    if (menu.meta?.form?.formUri) {
      //定义传递的参数
      let params = {} as any
      const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
      menu.meta?.form?.params?.map((_item) => {
        composeRequestParamsMultiRow(params, _item, menu, storeDataParams, selectTableColumn.value, ids.value)
      })
      if (menu.meta?.form?.method === 'get' || menu.meta?.form?.method === 'GET') {
        globalRequestUrlApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      } else {
        globalRequestApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      }
    } else {
      dialog.visible = true
      if (menu.meta!.dataColumn.length > 0) {
        operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
      } else {
        operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
      }
      dialog.title = menu.meta!.title
    }
  }

  /**
   * 默认按钮操作
   * @param row
   * @param position
   * @param menu
   */
  const globalBtnForm = async (row: TableItem, position: string, menu: MenuVO) => {
    btnRequestUri.value = menu.meta?.form?.formUri ?? null
    isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1
    buttonPosition.value = position
    //初始化表单
    const initFormData = resetFormGlobalFun(menu.meta?.dataColumn!)
    if (menu.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
    }
    dialog.visible = true
    dialog.title = menu.meta?.title || '操作'
    await nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, row)
    })
  }
</script>
