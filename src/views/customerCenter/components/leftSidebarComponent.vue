<!--
 * @Author: llm 
 * @Date: 2023-07-05 10:10:51
 * @LastEditors: llm
 * @LastEditTime: 2025-02-14 11:10:38
 * @Description: 左侧栏
 * 
-->
<template>
  <div class="app-padding" style="padding-top: 0">
    <div>
      <div class="flex-row items-center">
        <el-input v-model="searchName" placeholder="模糊搜索" clearable>
          <template #append>
            <el-select v-model="status" placeholder="选择状态" @change="change" style="width: 80px">
              <el-option v-for="(item, index) in options" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </template>
        </el-input>
      </div>
      <!-- <el-text>{{ props.listName }}列表</el-text> -->
      <div class="list">
        <el-tree
          ref="treeRef"
          class="mt-2"
          node-key="id"
          default-expand-all
          v-loading="listTreeLoading"
          :data="props.leftSideBarList"
          :highlight-current="true"
          :default-checked-keys="props.checkedItem"
          :props="{ label: 'name' }"
          :expand-on-click-node="false"
          :filter-node-method="handleDeptFilter"
          @node-click="handleDeptNodeClick"
        >
          <template #default="{ node, data }">
            <el-tooltip effect="light" :content="node.label" placement="right">
              <div class="single-line custom-tree-node">{{ node.label }}</div>
            </el-tooltip>
          </template>
        </el-tree>
      </div>
      <!-- <pagination
        v-if="props.leftSideBarListTotal > 0"
        v-model:total="props.leftSideBarListTotal"
        v-model:page="props.queryParams.page"
        v-model:limit="props.queryParams.limit"
        :small="true"
        :background="false"
        :pager-count="3"
        layout="prev, pager, next"
        @pagination="_handleQuery"
      /> -->
    </div>
  </div>
</template>
<script setup lang="ts">
  import { CustomerVO } from '@/api/customerCenter/customerBaseData/types'
  import { LeftSideBarItemVO } from '@/types/global'
  // 状态管理依赖
  import { useFormStore } from '@/store/modules/form.js'
  import { useSideBarStore } from '@/store/modules/sideBar'
  import defaultSettings from '@/settings'
  const formStore = useFormStore()
  const sideBarStore = useSideBarStore()
  const searchName = ref('')
  const status = ref<Boolean | string>('')

  const emit = defineEmits([
    'getItem', //获取当前选中的
    'changeStatus', //状态查询
    'handleQueryLeftSideBarList', //查询左侧栏列表
    'leftSideBarListNameSearch', //左侧列表名称模糊搜索
  ])
  const listTreeLoading = ref(false) //加载动画
  const treeRef = ref()
  const props = defineProps({
    /**
     * 列表名称
     */
    listName: {
      required: true,
      type: String,
      default: '',
    },
    /**
     * 数据列表
     */
    leftSideBarList: {
      required: true,
      type: Array as PropType<CustomerVO[]>,
      default: [],
    },
    /**
     * 总条数
     */
    leftSideBarListTotal: {
      required: true,
      type: Number,
      default: 0,
    },
    /**
     * 分页参数
     */
    queryParams: {
      required: true,
      type: Object as PropType<PageQuery>,
      default: () => {
        return {
          page: 1,
          limit: defaultSettings.globalLimit,
        }
      },
    },
    /**
     * 选中的列表项
     */
    checkedItem: {
      required: true,
      type: Array as PropType<string[]>,
      default: () => {
        return []
      },
    },
  })
  watch(
    () => props.leftSideBarList,
    (val) => {
      if (val.length > 0) {
        nextTick(() => {
          treeRef.value!.setCurrentKey(val[0].id, true)
        })
      }
    },
    {
      deep: true,
    },
  )
  const options = [
    {
      value: '',
      label: '全部',
    },
    {
      value: true,
      label: '启用',
    },
    {
      value: false,
      label: '禁用',
    },
  ]
  watch(
    () => searchName.value,
    () => {
      emit('leftSideBarListNameSearch', searchName.value)
    },
  )
  /**
   * 列表筛选
   */
  function handleDeptFilter(value: string, data: any) {
    if (!value) {
      return true
    }
    return data.name.indexOf(value) !== -1
  }
  /**
   * 列表节点
   */
  function handleDeptNodeClick(row: LeftSideBarItemVO) {
    //默认将左侧菜单第一条数据存到pinia中，用于后面数据调用里面的属性
    formStore.$patch((state) => {
      //判断sideBarStore.menuId 在state.selectLeftTreeRows 中是否存在,如果存在则替换selectLeftTreeRow = null,否则state.selectLeftTreeRows新增一条数据{sideBarStore.menuId:row}
      if (state.selectLeftTreeRows.some((item) => item.menuId === sideBarStore.$state.menuId)) {
        state.selectLeftTreeRows = state.selectLeftTreeRows.map((_item) => {
          if (_item.menuId === sideBarStore.$state.menuId) {
            _item.selectLeftTreeRow = row
          }
          return _item
        })
      }
    })
    emit('getItem', row)
  }
  /**
   * 状态查询
   */
  const change = (e: boolean) => {
    emit('changeStatus', e)
  }
  /**
   * 分页查询
   */
  const _handleQuery = () => {
    emit('handleQueryLeftSideBarList', props.queryParams)
  }
  defineExpose({
    listTreeLoading,
  })
</script>
<style scoped lang="scss">
  .custom-tree-node {
    padding-right: 10px;
  }

  .list {
    min-height: calc(100vh - 550px);
    max-height: calc(450px);
    overflow-y: scroll;
  }
</style>
