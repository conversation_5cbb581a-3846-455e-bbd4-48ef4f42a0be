<!--
 * @Author: llm
 * @Date: 2023-07-05 10:19:20
 * @LastEditors: llm
 * @LastEditTime: 2025-07-25 12:20:39
 * @Description: 右侧下部区域
 *
-->
<template>
  <div style="position: relative">
    <!-- tab切换 -->
    <div style="position: absolute; top: 0; left: 0; right: 0; width: 100%">
      <el-tabs v-model="menuId" class="demo-tabs mt-[12px]" @tab-click="handleClick" type="card">
        <el-tab-pane v-for="(item, index) in tabList" :label="item.meta?.title" :name="item.menuId" :key="index" />
      </el-tabs>
    </div>
    <!-- 筛选条件 -->
    <topQueryGroupComponent
      style="margin-top: 65px"
      ref="topQueryGroupComponentRef"
      :query-permission-group="tableConfig.tableItem"
      @handleSearchQuery="handleSearchQuery"
      @queryParams="getQueryData"
      v-show="existSelectDataColumn!.length > 0"
      @refreshPageTableColumn="refreshPageTableColumn"
    />
    <div :style="{ paddingTop: existSelectDataColumn!.length > 0 ? '125px' : '70px' }">
      <div class="mb-10px" v-if="meta?.ext?.instructions">
        <div v-html="meta?.ext?.instructions"></div>
      </div>
      <div class="flex items-center justify-between">
        <div ref="statisticRef" v-if="props.isShowBtnGroup" class="mb-10px">
          <!-- 按钮组 -->
          <button-group-component
            :ids="ids"
            :buttonPermissionGroup="bottomListTopOperation"
            @addItem="addItem"
            @handleDelete="handleDelete"
            @defaultHandle="defaultHandle"
            @exportExcel="exportExcelFun"
            @downloadExcelTemplate="downloadExcelTemplate"
            @importExcel="importExcelFun"
            @vehicleChange="vehicleChange"
            @addSubLine="addSubLine"
            @samePlanCompare="samePlanCompare"
            @crossPlanCompare="crossPlanCompare"
            @resetQuery="resetTopQuery"
            @searchQuery="searchTopQuery"
            @showMenuDialog="showMenuDialog"
            @topBtnConfirmDialog="topBtnConfirmDialog"
          />
        </div>
        <div class="cursor" @click="handleMsgQuery" v-html="rightTipDataObj?.timeoutMessage || ''"></div>
      </div>

      <TableComponent
        :loading="listLoading"
        :tableData="tableData"
        :tableConfig="tableConfig"
        :height="tableHeight"
        :buttonPermissionGroup="bottomListRightOperation"
        :topButtonPermissionGroup="topButtonPermissionGroup"
        :showAllSelection="props.showAllSelection"
        :requestUri="requestUri"
        :pageSummary="pageSummary"
        :totalSummary="totalSummary"
        @edit-by-id="getDialogDetailById"
        @copy="copyForm"
        @refreshPageTableColumn="refreshPageTableColumn"
        @batchDelete="batchDelete"
        @deleteItem="deleteItemFun"
        @updateItem="updateItem"
        @handleStatusChange="handleStatusChange"
        @showPic="showPic"
        @approvalOperation="approvalOperation"
        @defaultHandle="defaultHandleTable"
        @showRowMenuDialog="showRowMenuDialog"
        @updatePassword="updatePassword"
        @confirmDialog="confirmDialog"
        @sortChange="sortChange"
        v-if="(pageStyle && pageStyle !== 'singlePage' && pageStyle !== 'configurationSinglePage' && pageStyle !== 'dynamicColumns') || !pageStyle"
      />
    </div>
    <!-- 提醒设置中用到的，显示表单信息 -->
    <div v-if="pageStyle && pageStyle === 'singlePage'">
      <FormComponent
        ref="formDialogRef"
        :isEdit="isEdit"
        :dataColumn="operationColumn ? operationColumn : tableConfig.tableItem"
        :requestUri="btnRequestUri ?? requestUri"
        :refreshMenuCount="btnMenu?.meta?.form?.refreshMenuCount"
        :btnMenu="btnMenu"
        :formData="singlePageFormData"
        @clearFormColumn="clearFormColumn"
        @handleSubmit="handleSubmit"
        @formData="getFormData"
        @refresh="refresh"
      />
    </div>
    <!-- 待配板划分设置 -->
    <div v-if="pageStyle && pageStyle === 'configurationSinglePage'">
      <ConfigurationFormComponent
        ref="configurationDialogRef"
        v-loading="listLoading"
        element-loading-text="加载中..."
        :requestUri="btnRequestUri ?? requestUri"
        :formData="props.detailData"
        @handleSubmit="configurationSubmit"
        @refresh="refresh"
      />
    </div>
    <!-- 自定义表头-驾驶员管理-工资设置 -->
    <div v-if="pageStyle && pageStyle === 'dynamicColumns'">
      <GeneralPersonalTable />
    </div>
    <form-dialog
      v-if="dialog.visible"
      ref="formDialogRef"
      :dialog="dialog"
      :isEdit="isEdit"
      :dataColumn="operationColumn ? operationColumn : tableConfig.tableItem"
      :requestUri="btnRequestUri ?? requestUri"
      :refreshMenuCount="btnMenu?.meta?.form?.refreshMenuCount"
      :btnMenu="btnMenu"
      :currentRow="currentRow"
      @clearFormColumn="clearFormColumn"
      @closeDialog="closeDialog"
      @handleSubmit="handleSubmit"
      @formData="getFormData"
    />

    <!-- <pagination v-if="total > 0" v-model:total="total" v-model:page="listQueryParams.page" v-model:limit="listQueryParams.limit" @pagination="_handleQuery" /> -->
    <!-- 查看照片 -->
    <PicDialogComponent :image-list="imageList" ref="picDialogComponent" />
  </div>
</template>
<script setup lang="ts">
  import TableComponent from '@/components/TableComponent/index.vue'
  import buttonGroupComponent from '@/components/TopButtonGroupComponent/index.vue'
  import FormComponent from '@/components/FormComponent/index.vue'
  import ConfigurationFormComponent from '@/components/ConfigurationFormComponent/index.vue'
  import PicDialogComponent from '@/components/PicDialogComponent/index.vue' //查看照片
  import { UserQuery, UserTableItem } from '@/api/customerCenter/customerBaseData/types'
  import type { TabsPaneContext } from 'element-plus'
  import { PropType } from 'vue'
  import {
    composeRequestParams,
    composeRequestParamsMultiRow,
    composeRequestQueryParams,
    downloadFileGlobalFun,
    getSelectOptions,
    resetFormGlobalFun,
  } from '@/utils/common'
  import { RoleForm } from '@/api/authorityManagement/RoleManagement/types'
  import { globalRequestApi, globalRequestUrlApi } from '@/api/planManagement'
  import { FormColumn } from '@/types/global'
  import { useFormStore } from '@/store/modules/form'
  import formDialog from '@/components/FormDialogComponent/index.vue' //表单弹窗
  import GeneralPersonalTable from '@/views/outbound/basicData/fleetData/driverManagement/components/generalPersonalTable/index.vue' //自有车队管理-驾驶员管理-工资设置
  import { UserPageVO } from '@/api/authorityManagement/CorporateAccountManagement/PersonalManagement/types'
  import defaultSettings from '@/settings'
  import { useSideBarStore } from '@/store/modules/sideBar'
  import { postFormApi, refreshCheckGetApi, refreshCheckPostApi } from '@/api/shipmentManagement'
  import VxeTableComponent from '@/components/VxeTableComponent/index.vue'
  const bottomListRightOperation = ref<MenuVO[]>([]) //右下区域tab中的列表按钮组
  const bottomListTopOperation = ref<MenuVO[]>([]) //右下区域tab中的顶部按钮组
  const formStore = useFormStore()
  const tabList = ref<MenuVO[]>([])
  const menuId = ref<string>('')
  const loading = ref<boolean>(false)
  const existSelectDataColumn = ref<any[]>()
  const { proxy }: any = getCurrentInstance()
  const dialog = reactive<DialogOption>({
    visible: false,
  })
  const topQueryGroupComponentRef = ref()
  const emit = defineEmits([
    /**
     * 刷新列表
     */
    'refreshRightBottomList',
    /**
     * 切换底部tab
     */
    'changeTab',
    /**
     * 批量删除右下区域列表
     */
    'handleDeleteBottomListItem',
    /**
     * 底部列表分页查询
     */
    'handleQueryBottomList',
    /**
     * 新增
     */
    'addItem',
    /**
     * 编辑
     */
    'editBottomItem',
    /**
     * 启用禁用
     */
    'switchBottomListStatus',
    /**
     * 导出底部列表Excel
     */
    'exportBottomTabListExcel',
    /**
     * 导出模版
     */
    'downloadExcelTemplate',
    /**
     * 导入Excel
     */
    'importButtomTabExcelFun',
    /**
     * 删除单行
     */
    'deleteItem',
    /**
     * 显示列表项详情
     */
    'showRowMenuDialog',
    /**
     * 列表默认操作
     */
    'defaultHandleTable',
    /**
     * 底部列表查询条件
     */
    'handleQueryBottomList',
    /**
     * 提交表单
     */
    'handleSubmit',
    /**
     * 刷新
     */
    'refresh',
    /**
     * 下级审批操作
     */
    'approvalOperation',
    /**
     * 车辆变更
     */
    'vehicleChange',
    /**
     * 新增联运线路弹窗
     */
    'addSubLine',
    'samePlanCompare', //同方案对比
    'crossPlanCompare', //跨方案对比
    'showMenuDialog', //显示弹窗菜单
    'updatePassword', //修改密码
    'refreshPageTableColumn', //刷新数据列
    'confirmDialog', //自定义提示弹窗（重新校验）
    'sort-change', // 添加排序事件
    'handleMsgQuery', // 右侧消息提醒文字
  ])
  /**
   * 对dataColumn进行排序展示
   */

  /**
   * meta
   */
  const metaInfo = ref<MetaVO>()
  /**
   * 获取表单项，用于表单中级联操作
   * @param formData 表单项
   * @param dependOn 当前修改的依赖项下拉
   */
  const getQueryData = async (formData: any, dependOn: string) => {
    //获取form表单数据列
    const newOperationColumn = await getSelectOptions(metaInfo.value!.dataColumn, formData, dependOn, 'topQuerySelect')
  }
  /**
   * 当前选中的行数据
   */
  const currentRow = ref<TableItem>()

  /**
   * 关闭弹窗
   */
  function closeDialog() {
    dialog.visible = false
  }

  /**
   * 列表查询条件
   */
  const queryParams = reactive<any>({ page: 1, limit: defaultSettings.globalLimit })
  /**
   * 筛选条件查询
   * @param searchParams 查询条件
   * @param status 状态 不重新请求-noRequest  重新请求-request
   * @returns
   */
  const handleSearchQuery = (searchParams: any, status: string) => {
    //清除之前选中的ids
    ids.value = []
    selectTableColumn.value = []
    //遍历routeParams对象，queryParams[key] = routeParams[key]
    //初始化queryParams，只保留page和limit
    for (const key in queryParams) {
      if (key !== 'page' && key !== 'limit') {
        delete queryParams[key]
      }
    }
    for (const key in searchParams) {
      if (Object.prototype.hasOwnProperty.call(searchParams, key)) {
        //如果属性中包含range字符串，则把首字母大写，然后拆分2个，分别拼上start 和 end ,并删除原有属性
        if (key!.indexOf('Range') !== -1 && (searchParams[key] as any)) {
          const startName = 'start' + key.charAt(0).toUpperCase() + key.slice(1)
          const endName = 'end' + key.charAt(0).toUpperCase() + key.slice(1)
          queryParams[startName] = searchParams[key]![0]
          queryParams[endName] = searchParams[key]![1]
          delete queryParams[key]
        } else {
          queryParams[key] = searchParams[key]
        }
        // 重置了查询项，导致multi错误，暂时注释
        // topQueryGroupComponentRef.value.queryParams[key] = queryParams[key]
      }
    }
    if (status === 'request') {
      emit('handleQueryBottomList', queryParams)
    }
    // getList();
  }
  /**
   * 查询参数
   */
  const listQueryParams = reactive<UserQuery>({
    page: 1,
    limit: defaultSettings.globalLimit,
  })
  const listLoading = ref<boolean>(false) //列表加载动画
  /**
   * 总数
   */
  const total = ref(0)
  /**
   * 删除的列表ids
   */
  const ids = ref<string[]>([])
  //图片地址
  const imageList = ref<string[]>([])
  //查看图片组件
  const picDialogComponent = ref()
  /**
   * 页面类型
   * singlePage：单页模式
   * dynamicColumns: 动态列
   */
  const pageStyle = ref<string>('')
  /**
   * @description: singlePageData
   * @return {*}
   */
  const singlePageData = ref<any>()
  const formDialogRef = ref<any>()
  //获取单页模式数据
  const singlePageFormData = computed(() => {
    if (formDialogRef.value && singlePageData.value) {
      formDialogRef.value.formData = singlePageData.value
    }
    return
  })

  const configurationDialogRef = ref<any>()
  const configFromInfo = ref<any>()
  const tableHeight = ref<any>()
  const statisticRef = ref<any>()
  /**
   * 列表配置项
   */
  const tableConfig = reactive<TableConfig>({
    showHandleSelection: true,
    showSort: true,
    tableItem: [],
    isKey: false, //更新数据列
    operation: {
      label: '操作',
      items: [],
      width: '120px',
    },
  })

  const props = defineProps({
    meta: {
      required: false,
      type: Object as PropType<MetaVO> | undefined,
    },
    /**
     * 底部tab按钮组权限
     */
    bottomTabPermissionGroup: {
      required: true,
      type: Array as PropType<MenuVO[]>,
      default: () => {
        return []
      },
    },
    /**
     * 顶部按钮组权限
     */
    topButtonPermissionGroup: {
      required: false,
      type: Array as PropType<MenuVO[]>,
      default: () => {
        return []
      },
    },
    /**
     * 底部tab下的按钮组权限
     */
    bottomButtomPermissionGroup: {
      required: true,
      type: Array as PropType<MenuVO[]>,
      default: () => {
        return []
      },
    },
    queryParams: {
      required: false,
      type: Object as PropType<UserQuery>,
    },
    /**
     * 请求地址
     */
    requestUri: {
      required: true,
      type: String,
    },
    /**
     * 是否展示全选复选框
     */
    showAllSelection: {
      require: true,
      type: Boolean,
      default: true,
    },
    /**
     * 带配板
     */
    detailData: {
      require: true,
      type: Object as PropType<any>,
      default: () => {
        return {}
      },
    },
    /**
     * 是否显示按钮组
     */
    isShowBtnGroup: {
      require: true,
      type: Boolean,
      default: true,
    },
    /**
     * 右侧消息提醒文字
     */
    rightTipDataObj: {
      require: true,
      type: Object as PropType<RightTipDataVO>,
    },
  })

  watch(
    () => tableConfig.tableItem,
    (dataColumn: TableItem[]) => {
      existSelectDataColumn.value = dataColumn.filter((item) => item.query && item.selectEnable)
    },
    { deep: true, immediate: true },
  )
  watch(
    () => props.bottomTabPermissionGroup,
    (arr) => {
      tabList.value = arr
      if (tabList.value.length > 0 && tabList.value[0].menuId) {
        menuId.value = tabList.value[0].menuId
      }
    },
    { deep: true },
  )
  watch(
    () => props.bottomButtomPermissionGroup,
    (arr) => {
      //右侧按钮组
      bottomListRightOperation.value = arr.filter((item: MenuVO) => item.meta?.position === 'listTabRight')
      //顶部按钮组
      bottomListTopOperation.value = arr.filter((item: MenuVO) => item.meta?.position === 'listTabTop')
    },
    { deep: true },
  )

  // 监听内容高度的变化
  onMounted(() => {
    nextTick(() => {
      tableHeight.value = document.body.clientHeight - (statisticRef.value ? statisticRef.value?.offsetHeight : 0) - 340 + 'px'
    })
  })

  function resetTopQuery() {
    topQueryGroupComponentRef.value.resetQuery()
  }

  function searchTopQuery() {
    topQueryGroupComponentRef.value.handleQuery()
  }
  //重置搜索条件
  const resetQuery = () => {
    topQueryGroupComponentRef.value && topQueryGroupComponentRef.value.resetParams()
  }
  /**
   * 分页
   */
  const _handleQuery = () => {
    emit('handleQueryBottomList', listQueryParams)
  }
  /**
   * 批量删除
   * @param arr 选中的列表项数据
   */
  const batchDelete = (arr: any[]) => {
    //存储选中项到pinia
    formStore.$patch((state) => {
      state.selectRows = arr
    })
    ids.value = arr.map((item) => {
      return item.id
    })
    selectTableColumn.value = arr
  }
  /**
   * 展示图片
   * @param row 当前行数据
   * @param name 图片字段名
   */
  const showPic = (row: any, name: string) => {
    if (row[name]) {
      imageList.value = row[name].split(',')
      picDialogComponent.value.picDialogVisible = true
    } else {
      ElMessage.warning('当前未上传证件')
    }
  }
  /**
   * 新增
   * @param position 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   * @param type
   * @param item
   */
  const addItem = (position: string, type: string, item: MenuVO) => {
    console.log(item)
    emit('addItem', position, type, item)
  }
  /**
   * 更新数据
   * @param row 当前行数据
   * @param position  按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   */
  const updateItem = (row: TableItem, position: string) => {
    emit('editBottomItem', row, position)
  }
  /**
   * 修改用户状态
   */
  const handleStatusChange = async (row: { [key: string]: any }) => {
    emit('switchBottomListStatus', row)
  }
  /**
   * 列表数据
   */
  const tableData = ref<any[]>()
  const pageSummary = ref<any[]>()
  const totalSummary = ref<any[]>()
  const handleClick = (tab: TabsPaneContext, event: Event) => {
    if (tab.index) {
      tableData.value = []
      // 获取配置详情
      let newConfigDetail = sessionStorage.getItem('configDetail') ? JSON.parse(sessionStorage.getItem('configDetail') ?? '') : ''
      btnRequestUri.value = tabList.value[parseInt(tab.index)].meta?.uri
      // 判断是否有配置详情 并且配置详情和当前配置详情是否一致
      if (
        newConfigDetail &&
        configurationDialogRef.value &&
        JSON.stringify(configurationDialogRef.value.state.formData) !== '{}' &&
        parseInt(tab.index) !== tabList.value.length - 1
      ) {
        // 装载位判断
        let isMessage = configurationDialogRef.value.state.formData.standardLoadCapacity == newConfigDetail?.standardLoadCapacity
        // 遍历数组并比较totalVinId1 和 totalVinId2
        // 车型及数量匹配
        let isContrast = contrastArrFun(newConfigDetail?.list, configurationDialogRef.value.state.formData.list)
        // 如果有其中一个改变了就提示
        if (!isMessage || !isContrast) {
          ElMessageBox.confirm('当前配置有修改，是否忽略？', '提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              sessionStorage.setItem('configDetail', '')
              pageStyle.value = tabList.value[parseInt(tab.index as string)].meta?.purpose! //赋值展示tab下不同页面样式
              emit('changeTab', tabList.value[parseInt(tab.index as string)], listQueryParams, pageStyle.value)
            })
            .catch(() => {
              menuId.value = tabList.value[tabList.value.length - 1]?.menuId ?? ''
            })
        } else {
          sessionStorage.setItem('configDetail', '')
          pageStyle.value = tabList.value[parseInt(tab.index)].meta?.purpose! //赋值展示tab下不同页面样式
          emit('changeTab', tabList.value[parseInt(tab.index)], listQueryParams, pageStyle.value)
        }
      } else {
        // 如果一致
        if (btnRequestUri.value && btnRequestUri.value === 'tms/company/switch/config/loadVin/detailById') {
          pageStyle.value = 'configurationSinglePage'
          emit('changeTab', tabList.value[parseInt(tab.index)], listQueryParams, pageStyle.value)
        } else {
          sessionStorage.setItem('configDetail', '')
          pageStyle.value = tabList.value[parseInt(tab.index)].meta?.purpose! //赋值展示tab下不同页面样式
          emit('changeTab', tabList.value[parseInt(tab.index)], listQueryParams, pageStyle.value)
        }
      }
      // emit('changeTab', tabList.value[parseInt(tab.index)], listQueryParams, pageStyle.value);
    }
  }
  /**
   * 根据行id查详情
   * @param row 当前行数据
   * @param position 按钮位置
   * @param menu
   */
  const getDialogDetailById = async (row: any, position: string, menu: MenuVO) => {
    btnMenu.value = menu
    //定义传递的参数
    let params = {} as any
    const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
    menu.meta?.form?.params?.map((_item) => {
      composeRequestParamsMultiRow(params, _item, menu, storeDataParams, selectTableColumn.value, ids.value)
    })
    globalRequestUrlApi(params, 'get', props.requestUri! + '/' + row.id).then(async (res) => {
      await globalBtnForm(res.data, menu.meta?.position!, menu)
    })
  }
  const contrastArrFun = (arr1: any[], arr2: any[]) => {
    if (arr1.length !== arr2.length) {
      return false
    }
    for (let i = 0; i < arr1.length; i++) {
      if (
        arr1[i].totalVinId1 !== arr2[i].totalVinId1 ||
        arr1[i].totalVinId2 !== arr2[i].totalVinId2 ||
        arr1[i].vehicleModelId1 !== arr2[i].vehicleModelId1 ||
        arr1[i].vehicleModelId2 !== arr2[i].vehicleModelId2
      ) {
        return false // 如果找到不同的值，返回false
      }
    }
    return true
  }
  /**
   * 批量删除
   * @param handleIds 需要删除的ids字符串
   */
  const handleDelete = () => {
    const handleIds = ids.value.join(',')
    if (!handleIds) {
      ElMessage.warning('请勾选删除项')
      return
    }
    emit('handleDeleteBottomListItem', handleIds)
  }
  /**
   * 导出Excel
   * @param purpose 按钮类型
   * @param position 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   * @param menu 按钮信息
   *
   */
  const exportExcelFun = useThrottleFn((purpose: string, position?: string, menu?: MenuVO) => {
    const params = {
      ...listQueryParams,
      ids: ids.value.join(','),
    }
    emit('exportBottomTabListExcel', params, position, purpose, menu)
  }, 3000)
  /**
   * 导出模版
   * @param purpose 按钮类型
   * @param position 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   * @param menu 按钮信息
   */
  const downloadExcelTemplate = useThrottleFn((purpose: string, position?: string, menu?: MenuVO) => {
    emit('downloadExcelTemplate', position, purpose, menu)
  }, 3000)
  /**
   * 导入Excel
   * @param file 文件
   */
  const importExcelFun = useThrottleFn((file: any, meta: MetaVO) => {
    emit('importButtomTabExcelFun', file.raw || file, meta)
  }, 3000)
  /**
   * 删除单行
   * @param row
   */
  const deleteItemFun = (row: any) => {
    emit('deleteItem', row, btnRequestUri.value!)
  }
  //弹窗显示菜单
  const showRowMenuDialog = (query: any, column: TableItem) => {
    emit('showRowMenuDialog', query, column)
  }
  //默认操作
  const defaultHandleTable = (row: TableItem, position: string, menu: MenuVO) => {
    emit('defaultHandleTable', row, position, menu)
  }
  //下级审批操作
  const approvalOperation = (row: TableItem, position: string, menu: MenuVO) => {
    emit('approvalOperation', row, position, menu)
  }

  //同方案对比
  const samePlanCompare = (row: TableItem, position: string, menu: MenuVO) => {
    emit('samePlanCompare', row, position, menu, selectTableColumn.value)
  }
  // 跨方案对比
  const crossPlanCompare = (row: TableItem, position: string, menu: MenuVO) => {
    emit('crossPlanCompare', row, position, menu, selectTableColumn.value)
  }
  /***********************************************************************/
  /**
   * 当前是否为编辑状态
   */
  const isEdit = ref(false)
  /**
   * 按钮下的表单
   */
  const operationColumn = ref<any>()
  /**
   * 按钮的请求地址前缀
   */
  const btnRequestUri = ref<string | null>()
  /**
   * 初始化表单数据
   */
  const clearFormColumn = () => {
    operationColumn.value = undefined
    dialog.title = ''
  }
  /**
   * menu
   */
  const btnMenu = ref<MenuVO>()
  /**
   * 获取表单项，用于表单中级联操作
   * @param formData 表单项
   * @param dependOn 当前修改的依赖项下拉
   */
  const getFormData = async (formData: any, dependOn: string) => {
    setTimeout(async () => {
      //获取form表单数据列
      const newOperationColumn = await getSelectOptions(operationColumn.value, formData, dependOn, 'formSelect')
    }, 200)
  }
  const handleSubmit = (formData: RoleForm, request: BtnRequestVO, isRefreshMenuCount: boolean) => {
    emit('handleSubmit', formData, request, isRefreshMenuCount)
  }
  /**
   * 刷新
   */
  const refresh = () => {
    emit('refresh')
  }

  // 带配板配置表单提交
  const configurationSubmit = async (formData: any, request: BtnRequestVO, isRefreshMenuCount: boolean) => {}

  /**
   * 选择的列表项数据
   */
  const selectTableColumn = ref<any[]>([])
  /**
   * 按钮默认操作
   * @param position 按钮位置
   * @param type 按钮类型 或者是menuId
   * @param menu
   */
  const defaultHandle = async (position: string, type: string, menu: MenuVO) => {
    btnMenu.value = menu
    if (menu.meta!.uri) {
      btnRequestUri.value = menu.meta!.uri
    }
    isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1
    if (menu.meta?.form) {
      if (ids.value.length < menu.meta?.form?.min!) {
        ElMessage.warning(`至少选择${menu.meta?.form?.min}项`)
        return
      } else if (ids.value.length > menu.meta?.form?.max!) {
        ElMessage.warning(`最多选择${menu.meta?.form?.max}项`)
        return
      }
    }
    if (menu.meta?.form?.formUri) {
      //定义传递的参数
      let params = {} as any
      const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
      menu.meta?.form?.params?.map((_item) => {
        composeRequestParamsMultiRow(params, _item, menu, storeDataParams, selectTableColumn.value, ids.value)
      })
      if (menu.meta?.form?.method === 'get' || menu.meta?.form?.method === 'GET') {
        globalRequestUrlApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      } else {
        globalRequestApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      }
    } else {
      dialog.visible = true
      if (menu.meta!.dataColumn.length > 0) {
        operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
      } else {
        operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
      }
      dialog.title = menu.meta!.title
    }
  }
  /**
   * 更新表单下拉
   */
  const refreshFormSelectOptions = async (dataColumn: FormColumn[]) => {
    //重新获取form表单数据列，刷新筛选下拉项
    return new Promise(async (resolve, reject) => {
      const newDataColumn = getSelectOptions(dataColumn, null, '', 'formSelect')
      resolve(newDataColumn)
    })
  }
  /**
   * 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   */
  const buttonPosition = ref<string>()
  /**
   * 默认按钮操作
   * @param row
   * @param position
   * @param menu
   */
  const globalBtnForm = async (row: TableItem, position: string, menu: MenuVO) => {
    btnRequestUri.value = menu.meta?.form?.formUri ?? null
    if (menu.meta?.purpose === 'copy') {
      isEdit.value = false
    } else {
      isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1
    }
    buttonPosition.value = position //初始化表单
    const initFormData = resetFormGlobalFun(menu.meta?.dataColumn!)
    if (menu.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
    }
    // 根据按钮返回数据渲染当前表单，如果是下拉表单 或者是级联表单项，需要将返回数据设置到表单项的option.data上
    //遍历数组operationColumn.value，找到里面的form的type是select，将row中和form的name相同的项赋值他
    if (position === 'listTop' || position === 'listTabTop') {
      operationColumn.value.map((item: TableItem) => {
        if (item?.form?.type === 'select' || item?.form?.type === 'selectTree' || item?.form?.type === 'cascader') {
          if (row[item.form.name!]) {
            if (!item.form!.option!.data) {
              item.form!.option!.data = []
            }
            //如果row[item.form.name!] 类型是数组则赋值给item.form.option!.data
            if (Array.isArray(row[item.form.name!])) {
              item.form!.option!.data = row[item.form.name!]
            }
            delete row[item.form.name!]
          }
        }
      })
    }
    if (position === 'listRight') {
      operationColumn.value.map((item: TableItem) => {
        if (item?.form?.type === 'select' || item?.form?.type === 'selectTree' || item?.form?.type === 'cascader') {
          if (row[item!.form.name!]) {
            if (!item!.form!.option!.data) {
              item!.form!.option!.data = []
            }
            // item!.form.option!.data = row[item!.form.name!];
            // delete row[item.form.name!];
          }
        }
      })
    }
    dialog.visible = true
    dialog.title = menu.meta?.title || '操作'
    const deepRow = JSON.parse(JSON.stringify(row))
    //如果是复制，并且operationColumn.value中的每项的form.canCopy为false，则将deepRow中的对应项的value设置为空
    if (menu.meta?.purpose === 'copy') {
      operationColumn.value.forEach((item: TableItem) => {
        if (item.form?.canCopy === false) {
          deepRow[item.form.name!] = null
        }
      })
      deepRow.id = undefined
    }
    await nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, deepRow)
    })
  }

  /**
   * 车辆变更弹窗
   * @param position
   * @param type
   * @param menu
   * @param transportType 发运方式  3-公路 6-铁路 7-水路 9-分拨
   * @param shipmentType 运单类型 1-短驳 2-干线倒板 3-干线直发  100-以上三个都要
   */
  function vehicleChange(position: string, type: string, menu: MenuVO, transportType: string, shipmentType: string) {
    emit('vehicleChange', position, type, menu, transportType, shipmentType)
  }
  const showMenuDialog = async (position: string, purpose: string, menu: MenuVO) => {
    emit('showMenuDialog', position, purpose, menu)
  }
  //新增联运线路
  function addSubLine() {
    emit('addSubLine')
  }
  /*
   * 重置密码
   */
  function updatePassword(row: UserPageVO) {
    emit('updatePassword', row)
  }
  const refreshPageTableColumn = async () => {
    emit('refreshPageTableColumn')
  }
  const confirmDialog = async (row: TableItem, position: string, meta: MetaVO) => {
    emit('confirmDialog', row, position, meta)
  }
  /**
   * 复制表单
   */
  const copyForm = async (row: any, position: string, menu: MenuVO) => {
    currentRow.value = row
    btnMenu.value = menu
    btnRequestUri.value = menu.meta!.uri
    isEdit.value = false
    //存储当前按钮下表单中的btns
    if (menu.meta?.form?.btns) {
      //存储选中项到pinia
      formStore.$patch((state) => {
        state.storeFormBtns = menu.meta?.form?.btns!
      })
    }
    if (menu.meta?.form?.formUri) {
      //定义传递的参数
      let params = {} as any
      const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
      menu.meta!.form!.params?.map((_item) => {
        composeRequestParams(params, _item, menu, storeDataParams, row, queryParams)
      })
      if (menu.meta?.form?.method === 'get' || menu.meta?.form?.method === 'GET') {
        globalRequestUrlApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      } else {
        globalRequestApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      }
    } else {
      if (menu.meta!.dataColumn.length > 0) {
        operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
      } else {
        operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
      }
      await globalBtnForm(row, position, menu)
    }
    //如果menu.meta?.form?.refreshPage==true ,则刷新当前列表
    if (menu.meta?.form?.refreshPage) refreshPageTableColumn()
    //如果menu.meta?.form?.trigerUris存在，遍历menu.meta?.form?.trigerUris，请求相应接口
    if (menu.meta?.form?.trigerUris && menu.meta?.form?.trigerUris.length > 0) {
      menu.meta?.form?.trigerUris?.map((item) => {
        //定义传递的参数
        let params = {} as any
        const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
        item.params?.map((_item) => {
          composeRequestParams(params, _item, menu, storeDataParams, row, queryParams)
        })
        if (item.method === 'get' || item.method === 'GET') {
          globalRequestUrlApi(params, item.method!, item.uri!).then(async (res) => {})
        } else {
          globalRequestApi(params, item.method!, item.uri!).then(async (res) => {})
        }
      })
    }
  }
  const sideBarStore = useSideBarStore()
  //弹窗确认
  const topBtnConfirmDialog = async (row: TableItem, position: string, menu: MenuVO) => {
    if (menu.meta?.form?.btns![0].params) {
      if (ids.value.length <= 0) {
        ElMessage.warning('请勾选')
        return
      }
    }
    let params = {} as any
    await confirmDialogParams(params, row, position, menu)
    let res = {} as any
    try {
      if (menu.meta.form?.btns![0].method === 'post' || menu.meta.form?.btns![0].method === 'POST') {
        res = await refreshCheckPostApi(menu.meta.form.btns![0].uri!, params)
      } else if (menu.meta.form?.btns![0].method === 'get' || menu.meta.form?.btns![0].method === 'GET') {
        res = await refreshCheckGetApi(menu.meta.form.btns![0].uri!, params)
      } else if (menu.meta.form?.btns![0].method === 'postForm') {
        const _params = {
          ...params,
        }
        res = await postFormApi(menu.meta.form.btns![0].uri!, menu.meta.form.btns![0].responseType, _params)
      }
      const download = menu.meta.form?.btns![0].download
      if (download) {
        if (download === 'file') {
          if (res.data.indexOf(',') !== -1) {
            res.data.split(',').forEach((item: any) => {
              window.open(item)
            })
          } else {
            window.open(res.data)
          }
        } else if (download === 'stream') {
          await downloadFileGlobalFun(res)
        }
      }
      //如果存在jump,则根据jump中的targetField跳转到对应的菜单页面，jump.params是需要携带的参数
      else if (menu.meta.form?.btns![0].jump) {
        const { data } = res
        //跳转到menu.meta.form?.btns![0].jump.targetField对应的菜单页面
        const jumpType = menu.meta.form?.btns![0].jump?.jumpType
        // 跳转页面需要，暂时还没有
        // if (jumpType) {
        //   switch (jumpType) {
        //     case 'editGenerateStatement':
        //       editGenerateStatement(data)
        //       break
        //   }
        // }
      } else {
        ElMessage.success(res.message)
      }
      _handleQuery()
    } catch (error) {
      loading.value = false
    }
  }
  const confirmDialogParams = async (params: any, row: TableItem, position: string, menu: MenuVO) => {
    const _queryParams = await topQueryGroupComponentRef.value.searchQueryTemp()
    if (menu.meta?.form?.btns![0].params) {
      //定义传递的参数
      menu.meta?.form?.btns![0].params?.map((_item) => {
        if (_item.dependFrom === 'listData') {
          composeRequestParamsMultiRow(params, _item, menu, sideBarStore.$state.storeDialogFormParams, selectTableColumn.value, ids.value)
        } else {
          composeRequestParams(params, _item, menu, sideBarStore.$state.storeDialogFormParams, null, _queryParams)
        }
      })
    }
    composeRequestQueryParams(params, null, menu, sideBarStore.$state.storeDialogFormParams, null, _queryParams)
  }
  const sortChange = (data: { column: string; direction: string }[]) => {
    emit('sort-change', data) //调用后端查询接口
  }
  const handleMsgQuery = () => {
    emit('handleMsgQuery')
  }
  defineExpose({
    tableData,
    pageSummary,
    totalSummary,
    total,
    tableConfig,
    listLoading,
    listQueryParams,
    resetQuery,
    pageStyle,
    singlePageData,
    closeDialog,
    operationColumn,
  })
</script>
