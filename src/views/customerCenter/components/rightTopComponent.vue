<!--
 * @Author: llm
 * @Date: 2023-07-05 10:19:20
 * @LastEditors: llm
 * @LastEditTime: 2023-07-29 10:10:42
 * @Description: 右侧上部区域
 *
-->
<template>
  <div>
    <el-descriptions class="margin-top" :border="true" :column="3">
      <template v-for="(item, index) in tableConfig.tableItem">
        <el-descriptions-item :label="item.label" :key="index" style="min-width: 100px" v-if="item.listEnable">
          <template v-if="item.name">
            <template v-if="item.type === 'tag'">
              <el-tag v-for="(v, i) in props.info[item.name]?.split(',')" type="success" :key="i" style="margin: 5px">{{ v }}</el-tag>
            </template>
            <el-switch
              v-else-if="item.type === 'switch'"
              inline-prompt
              active-text="启用"
              inactive-text="禁用"
              v-model="props.info[item.name]"
              :active-value="true"
              @change="handleStatusChange(props.info)"
            />
            <el-text v-else>{{ props.info[item.name] }}</el-text>
          </template>
        </el-descriptions-item>
      </template>
    </el-descriptions>
  </div>
</template>
<script setup lang="ts">
  import { CustomerVO } from '@/api/customerCenter/customerBaseData/types'
  import { PropType } from 'vue'
  const emit = defineEmits([
    'handleStatusChange', //切换状态
  ])
  const props = defineProps({
    /**
     * 列表项
     */
    descriptionsConfig: {
      required: true,
      type: Array as PropType<TableItem[]>,
      default: () => {
        return []
      },
    },
    info: {
      required: true,
      type: Object as PropType<TableItem>,
      default: () => {
        return {}
      },
    },
  })
  const info = ref<CustomerVO>({})
  const descriptionsConfig: TableItem[] = []
  const tableConfig = reactive<TableConfig>({
    showHandleSelection: true,
    showSort: true,
    tableItem: [],
    operation: {
      label: '操作',
      items: [],
      width: '120px',
    },
  })
  /**
   * 修改状态
   */
  function handleStatusChange(row: { [key: string]: any }) {
    //更新状态
    emit('handleStatusChange', row)
  }
  defineExpose({
    descriptionsConfig,
    tableConfig,
  })
</script>
