<!--  线 + 柱混合图 -->
<template>
  <div :id="'bar' + props.id" :style="{ height: '2.4rem', width: '5rem' }" />
</template>

<script setup lang="ts">
  import * as echarts from 'echarts'

  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
  })

  const options = {
    grid: {
      left: '2%',
      right: '2%',
      bottom: '20%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
    },
    legend: {
      x: 'right',
      y: '0px',
      padding: [0, 20, 0, 0],
      data: ['收入', '利润增长率'],
      textStyle: {
        color: '#999',
      },
    },
    xAxis: [
      {
        type: 'category',
        data: ['浙江', '北京', '上海', '广东', '深圳'],
        axisPointer: {
          type: 'shadow',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        min: 0,
        max: 10000,
        interval: 2000,
        axisLabel: {
          formatter: '{value} ',
        },
      },
      {
        type: 'value',
        min: 0,
        max: 100,
        interval: 20,
        axisLabel: {
          formatter: '{value}%',
        },
      },
    ],
    series: [
      {
        name: '收入',
        type: 'bar',
        data: [7000, 7100, 7200, 7300, 7400],
        barWidth: 20,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' },
          ]),
        },
      },
      {
        name: '利润增长率',
        type: 'line',
        yAxisIndex: 1,
        data: [60, 65, 70, 75, 80],
        itemStyle: {
          color: '#67C23A',
        },
      },
    ],
  }

  onMounted(() => {
    // 图表初始化
    const chart = echarts.init(document.getElementById('bar' + props.id) as HTMLDivElement)
    chart.setOption(options)

    // 大小自适应
    window.addEventListener('resize', () => {
      chart.resize()
    })
  })
</script>

<style scoped></style>
