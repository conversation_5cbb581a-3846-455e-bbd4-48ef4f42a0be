<!--  柱状图 ————维修金额 -->
<template>
  <div ref="weixiuChartRef" :style="{ height: props.height, width: props.width }" />
</template>

<script setup lang="ts">
  import * as echarts from 'echarts'
  import type { EChartsOption, SeriesOption } from 'echarts'
  import { onMounted } from 'vue'
  import { debounce } from 'lodash'

  const props = defineProps({
    data: {
      type: Object as PropType<{
        s1: 0
        s3: 0
        id: string // 添加id属性
        list: {
          id: number
          name: string
          v1: number
          v2: number
          v3: number
          s1: number
          s2: number
          s3: number
        }[]
      }>,
      default: () => ({
        id: '', // 设置默认值
        list: [],
        s1: 0,
        s3: 0,
      }),
    },
    height: {
      type: String,
      default: '2.4rem',
    },
    width: {
      type: String,
      default: '5rem',
    },
    barWidth: {
      type: Number,
      default: 6,
    },
  })
  const weixiuChartRef = ref()
  const series = ref([] as SeriesOption[])
  const options = ref({})

  watch(
    () => props.data,
    (data) => {
      console.log('维修: ', data)
      nextTick(() => {
        initChartsData(data)
      })
    },
    { deep: true, immediate: true },
  )

  let myChart: echarts.ECharts | null = null
  let resizeObserver: ResizeObserver | null = null

  onMounted(() => {
    // 使用 ResizeObserver 替代 window.resize
    resizeObserver = new ResizeObserver(() => {
      if (myChart) {
        myChart.resize()
      }
    })
    if (weixiuChartRef.value) {
      resizeObserver.observe(weixiuChartRef.value)
    }
  })

  onUnmounted(() => {
    if (myChart) {
      myChart.dispose()
      myChart = null
    }
    if (resizeObserver) {
      resizeObserver.disconnect()
      resizeObserver = null
    }
  })

  const initChartsData = (data: any) => {
    if (!weixiuChartRef.value) return
    if (myChart) {
      myChart.dispose()
    }
    myChart = echarts.init(weixiuChartRef.value)
    if (!myChart) return

    // 动态计算最大值
    let adjustedMax = 4
    if (data.s3 < 5) {
      adjustedMax = 4 // 极小值兜底处理
    } else if (data.s3 <= data.s3 * 1.2) {
      adjustedMax = Math.ceil((data.s3 * 1.2) / 10) * 10
    }
    series.value = [
      {
        data: data.list.map((item: any) => item.v1),
        type: 'bar',
        name: '维修金额',
        barWidth: props.barWidth,
        label: {
          show: true, // 开启标签显示
          position: 'top', // 标签位置在柱顶部
          formatter: function (params: any) {
            if (params.value === 0 || params.value === null) return ''
            return params.value // 非零值正常显示
          }, // 显示数据值（{c} 表示原始数值
          color: '#3ED5F7', // 字体颜色‌
          fontSize: 10, // 字体大小
          distance: 2, // 标签与柱顶部的距离
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0.2, 1, 1, [
            {
              offset: 0,
              color: '#48D6EF', // 渐变色起点颜色
            },
            {
              offset: 1,
              color: '#338FEA', // 渐变色终点颜色
            },
          ]),
        },
      },
    ]
    options.value = {
      grid: {
        top: '18%',
        left: '5%',
        right: '5%',
        bottom: '10%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        }, // 提示框组件
        confine: true, // 防止超出容器
        backgroundColor: 'rgba(13, 64, 71, 1)',
        borderColor: 'rgba(143, 225, 252, 0.60)',
        padding: 8,
        textStyle: {
          color: '#fff',
        },
        formatter: function (params: any) {
          var relVal = params[0].name + (data.queryType == 'month' ? '日' : data.queryType == 'year' ? '' : '')
          for (var i = 0, l = params.length; i < l; i++) {
            relVal += '<br/>' + params[i].marker + params[i].seriesName + '：' + (params[i].value || params[i].value == 0 ? params[i].value + data.unit : ' -')
          }
          return relVal
        },
      },
      legend: {
        right: '20px',
        top: '5px',
        padding: [0, 0, 0, 0],
        data: ['维修金额'],
        textStyle: {
          color: '#999',
        },
        itemWidth: 16,
        itemHeight: 10,
      },
      xAxis: {
        type: 'category',
        data: data.list.map((item: any) => item.name),
        axisPointer: {
          type: 'shadow',
        },
      },
      yAxis: [
        {
          type: 'value',
          name: data.unit,
          nameTextStyle: {
            // 设置名称样式
            color: '#999', // 文字颜色
            fontStyle: 'normal', // 字体风格
            fontWeight: 'bold', // 字体粗细
            fontSize: '0.14rem', // 字体大小
            padding: [0, 0, -4, -30], // 文字与轴线之间的距离
          },
          min: 0,
          max: Math.round(adjustedMax),
          interval: Math.round(adjustedMax / 4), //平均值
          splitNumber: 4, //平均分几份
          axisLabel: {
            formatter: '{value} ',
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.15)',
              type: 'dashed', // dashed 虚线  dotted 实线
            },
          },
          axisPointer: {
            show: false,
          },
        },
      ],
      series: series.value,
    }
    myChart.setOption(options.value)
  }
</script>

<style scoped></style>
