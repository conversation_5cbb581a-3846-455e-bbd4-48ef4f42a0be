<!--  柱状图 ————客户扣款 -->
<template>
  <div ref="kehuChartRef" :style="{ height: props.height, width: props.width }" />
</template>

<script setup lang="ts">
  import * as echarts from 'echarts'
  import type { EChartsOption, SeriesOption } from 'echarts'
  import { onMounted } from 'vue'
  import { debounce } from 'lodash'

  const props = defineProps({
    data: {
      type: Object as PropType<{
        s1: 0
        s3: 0
        id: string // 添加id属性
        list: {
          id: number
          name: string
          v1: number
          v2: number
          v3: number
          s1: number
          s2: number
          s3: number
        }[]
      }>,
      default: () => ({
        id: '', // 设置默认值
        list: [],
        s1: 0,
        s3: 0,
      }),
    },
    height: {
      type: String,
      default: '2.4rem',
    },
    width: {
      type: String,
      default: '5rem',
    },
    barWidth: {
      type: Number,
      default: 6,
    },
  })

  const kehuChartRef = ref()
  const series = ref([] as SeriesOption[])
  const options = ref({})

  watch(
    () => props.data,
    (data) => {
      console.log('kehu: ', data)
      nextTick(() => {
        initChartsData(data)
      })
    },
    { deep: true, immediate: true },
  )

  let myChart: echarts.ECharts | null = null
  let resizeObserver: ResizeObserver | null = null

  onMounted(() => {
    // 使用 ResizeObserver 替代 window.resize
    resizeObserver = new ResizeObserver(() => {
      if (myChart) {
        myChart.resize()
      }
    })
    if (kehuChartRef.value) {
      resizeObserver.observe(kehuChartRef.value)
    }
  })

  onUnmounted(() => {
    if (myChart) {
      myChart.dispose()
      myChart = null
    }
    if (resizeObserver) {
      resizeObserver.disconnect()
      resizeObserver = null
    }
  })

  const initChartsData = (data: any) => {
    if (!kehuChartRef.value) return
    if (myChart) {
      myChart.dispose()
    }
    myChart = echarts.init(kehuChartRef.value)
    if (!myChart) return

    // 动态计算最大值
    let adjustedMax = 4
    if (data.s3 < 5) {
      adjustedMax = 4 // 极小值兜底处理
    } else if (data.s3 <= data.s3 * 1.2) {
      adjustedMax = Math.ceil((data.s3 * 1.2) / 10) * 10
    }
    // if (data.s3 < 5) {
    //   adjustedMax = 4 // 极小值兜底处理
    // } else if (data.s3 < 100) {
    //   adjustedMax = Math.ceil(data.s3)
    // } else if (data.s3 < 1000) {
    //   adjustedMax = Math.ceil(data.s3 / 1000) * 1000
    // } else {
    //   adjustedMax = Math.ceil(data.s3 / 10000) * 10000
    // }
    series.value = [
      {
        data: data.list.map((item: any) => item.v1),
        type: 'custom',
        name: '客户扣款',
        renderItem: function (params: any, api: any) {
          const value = api.value(0)
          const xPos = api.coord([api.value(1), 0])[0]
          const yPos = api.coord([0, api.value(0)])[1]
          const barWidth = api.size([1, 0])[0] * 0.6

          const points = [
            [xPos - barWidth / 2, api.coord([0, 0])[1]],
            [xPos + barWidth / 2, api.coord([0, 0])[1]],
            [xPos, yPos],
          ]
          return {
            type: 'polygon',
            shape: { points },
            style: {
              fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#E519D2' },
                { offset: 1, color: 'rgba(229,25,210,0.1)' },
              ]),
            },
          }
        },
        encode: {
          x: 1,
          y: 0,
        },
        // barWidth: props.barWidth,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 1, 1, [
            {
              offset: 0,
              color: '#E519D2', // 渐变色起点颜色
            },
            {
              offset: 1,
              color: 'rgba(229,25,210,0.1)', // 渐变色终点颜色
            },
          ]),
        },
      },
    ]
    options.value = {
      grid: {
        top: '18%',
        left: '5%',
        right: '5%',
        bottom: '10%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
        confine: true, // 防止超出容器
        backgroundColor: 'rgba(13, 64, 71, 1)',
        borderColor: 'rgba(143, 225, 252, 0.60)',
        padding: 8,
        textStyle: {
          color: '#fff',
        },
        formatter: function (params: any) {
          var relVal = params[0].name + (data.queryType == 'month' ? '日' : data.queryType == 'year' ? '' : '')
          for (var i = 0, l = params.length; i < l; i++) {
            relVal += '<br/>' + params[i].marker + params[i].seriesName + '：' + (params[i].value || params[i].value == 0 ? params[i].value + data.unit : ' -')
          }
          return relVal
        },
      },
      legend: {
        right: '20px',
        top: '2px',
        padding: [0, 0, 0, 0],
        data: ['客户扣款'],
        textStyle: {
          color: '#999',
        },
        itemWidth: 16,
        itemHeight: 10,
      },
      xAxis: {
        type: 'category',
        data: data.list.map((item: any) => item.name),
        axisPointer: {
          type: 'shadow',
        },
      },
      yAxis: [
        {
          type: 'value',
          name: data.unit,
          nameTextStyle: {
            // 设置名称样式
            color: '#999', // 文字颜色
            fontStyle: 'normal', // 字体风格
            fontWeight: 'bold', // 字体粗细
            fontSize: '0.14rem', // 字体大小
            padding: [0, 0, -4, -30], // 文字与轴线之间的距离
          },
          min: 0,
          max: Math.round(adjustedMax),
          interval: Math.round(adjustedMax / 4), //平均值
          splitNumber: 4, //平均分几份
          axisLabel: {
            formatter: '{value} ',
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.15)',
              type: 'dashed', // dashed 虚线  dotted 实线
            },
          },
          axisPointer: {
            show: false,
          },
        },
      ],
      series: series.value,
    }
    myChart.setOption(options.value)
  }
</script>

<style scoped></style>
