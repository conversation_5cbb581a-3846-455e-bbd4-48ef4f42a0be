<!--  堆叠柱 + 线 图 混合 ————总里程数 -->
<template>
  <div style="position: relative">
    <div ref="zonglichengChartRef" :style="{ height: props.height, width: props.width }" />
    <div
      style="
        position: absolute;
        top: -0.2rem;
        right: 0.2rem;
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: 400;
        font-size: 0.1rem;
        color: #ffffff;
        line-height: 15px;
        text-align: left;
        font-style: normal;
        display: flex;
      "
    >
      <div>放空率：</div>
      <div style="font-size: 0.18rem; margin-top: -0.04rem">{{ data.s1 }}%</div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import * as echarts from 'echarts'
  import type { EChartsOption, SeriesOption } from 'echarts'
  import { onMounted } from 'vue'
  import { debounce } from 'lodash'

  const props = defineProps({
    data: {
      type: Object as PropType<{
        id: string // 添加id属性
        s3: 0
        s1: 0
        list: {
          id: number
          name: string
          v1: number
          v2: number
          v3: number
          s1: number
          s2: number
          s3: number
        }[]
      }>,
      default: () => ({
        id: '', // 设置默认值
        list: [],
        s3: 0,
      }),
    },
    height: {
      type: String,
      default: '2.4rem',
    },
    width: {
      type: String,
      default: '5rem',
    },
    barWidth: {
      type: Number,
      default: 6,
    },
  })

  const zonglichengChartRef = ref()
  const series = ref([] as SeriesOption[])
  const options = ref({})

  watch(
    () => props.data,
    (data) => {
      console.log('zonglicheng: ', data)
      nextTick(() => {
        initChartsData(data)
      })
    },
    { deep: true, immediate: true },
  )

  let myChart: echarts.ECharts | null = null
  let resizeObserver: ResizeObserver | null = null

  onMounted(() => {
    // 使用 ResizeObserver 替代 window.resize
    resizeObserver = new ResizeObserver(() => {
      if (myChart) {
        myChart.resize()
      }
    })
    if (zonglichengChartRef.value) {
      resizeObserver.observe(zonglichengChartRef.value)
    }
  })

  onUnmounted(() => {
    if (myChart) {
      myChart.dispose()
      myChart = null
    }
    if (resizeObserver) {
      resizeObserver.disconnect()
      resizeObserver = null
    }
  })

  const initChartsData = (data: any) => {
    if (!zonglichengChartRef.value) return
    if (myChart) {
      myChart.dispose()
    }
    myChart = echarts.init(zonglichengChartRef.value)
    if (!myChart) return

    // 动态计算最大值
    // let adjustedMax
    // if (data.s3 < 5) {
    //   adjustedMax = 4 // 极小值兜底处理
    // } else if (data.s3 < 100) {
    //   adjustedMax = Math.ceil(data.s3)
    // } else if (data.s3 < 1000) {
    //   adjustedMax = Math.ceil(data.s3 / 1000) * 1000
    // } else {
    //   adjustedMax = Math.ceil(data.s3 / 10000) * 10000
    // }
    let adjustedMax = 4
    if (data.s3 < 5) {
      adjustedMax = 4 // 极小值兜底处理
    } else if (data.s3 <= data.s3 * 1.2) {
      adjustedMax = Math.ceil((data.s3 * 1.2) / 10) * 10
    }

    series.value = [
      {
        name: '累计',
        type: 'line',
        symbol: 'none', // 去掉折线点
        smooth: true, // 开启平滑效果
        lineStyle: {
          width: 2,
        },
        yAxisIndex: 0,
        data: data.list.map((item: any) => item.v3),
        itemStyle: {
          color: '#3F97FD',
        },
      },
      {
        data: data.list.map((item: any) => item.v1),
        type: 'bar',
        stack: 'a',
        name: '满载里程',
        barWidth: props.barWidth,
        yAxisIndex: 0,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 1, 1, [
            {
              offset: 0.3,
              color: '#3CDDEB', // 渐变色起点颜色
            },
            {
              offset: 1,
              color: 'rgba(60,221,235,0)', // 渐变色终点颜色
            },
          ]),
        },
      },
      {
        data: data.list.map((item: any) => item.v2),
        type: 'bar',
        stack: 'a',
        name: '空载里程',
        barWidth: props.barWidth,
        yAxisIndex: 0,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 1, 1, [
            {
              offset: 0.3,
              color: '#7B2DFF', // 渐变色起点颜色
            },
            {
              offset: 1,
              color: 'rgba(123,45,255,0.36)', // 渐变色终点颜色
            },
          ]),
        },
      },
    ]
    options.value = {
      grid: {
        top: '18%',
        left: '5%',
        right: '5%',
        bottom: '10%',
        containLabel: true,
      },

      // title: {
      //   text: `{a|放空率：}{name|${data.s1}%}`,
      //   left: '5%',
      //   top: -2, // 直接使用像素值
      //   textStyle: {
      //     rich: {
      //       a: {
      //         fontSize: 12,
      //         color: '#999',
      //         fontWeight: '400',
      //         align: 'center',
      //       },
      //       name: {
      //         fontSize: 16,
      //         color: '#fff',
      //         align: 'center',
      //         fontWeight: '500',
      //       },
      //     },
      //     color: '#ffffff',
      //     fontSize: '12px',
      //   },
      // },

      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
        confine: true, // 防止超出容器
        backgroundColor: 'rgba(13, 64, 71, 1)',
        borderColor: 'rgba(143, 225, 252, 0.60)',
        padding: 8,
        textStyle: {
          color: '#fff',
        },
        formatter: function (params: any) {
          var relVal = params[0].name + (data.queryType == 'month' ? '日' : data.queryType == 'year' ? '' : '')
          for (var i = 0, l = params.length; i < l; i++) {
            relVal += '<br/>' + params[i].marker + params[i].seriesName + '：' + (params[i].value || params[i].value == 0 ? params[i].value + data.unit : ' -')
          }
          return relVal
        },
      },
      legend: {
        right: '20px',
        top: '2px',
        padding: [0, 0, 0, 0],
        data: ['累计', '空载里程', '满载里程'],
        textStyle: {
          color: '#999',
        },
        itemWidth: 16,
        itemHeight: 10,
      },
      xAxis: {
        type: 'category',
        data: data.list.map((item: any) => item.name),
        axisPointer: {
          type: 'shadow',
        },
      },
      yAxis: [
        {
          type: 'value',
          name: data.unit,
          nameTextStyle: {
            // 设置名称样式
            color: '#999', // 文字颜色
            fontStyle: 'normal', // 字体风格
            fontWeight: 'bold', // 字体粗细
            fontSize: '0.14rem', // 字体大小
            padding: [0, 0, -4, -30], // 文字与轴线之间的距离
          },
          min: 0,
          max: Math.round(adjustedMax),
          interval: Math.round(adjustedMax / 4), //平均值
          splitNumber: 4, //平均分几份
          axisLabel: {
            formatter: '{value} ',
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.15)',
              type: 'dashed', // dashed 虚线  dotted 实线
            },
          },
          axisPointer: {
            show: false,
          },
        },
      ],
      series: series.value,
    }
    myChart.setOption(options.value)
  }
</script>

<style scoped></style>
