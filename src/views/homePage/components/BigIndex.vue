<!--
 * @Author: llm 
 * @Date: 2023-09-06 10:21:44
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-21 20:59:46
 * @Description: 首页工作台
 * 
-->
<template>
  <div class="content">
    <!-- 头部 -->
    <div class="flex flex-row items-center justify-between">
      <div class="flex flex-row items-center">
        <slot name="left" />
      </div>
      <slot name="title" />
      <img src="../assets/gongzuotai.png" alt="title" class="title" />
      <div class="flex items-center justify-end">
        <img v-if="props.systemSettings" @click="openOptions" style="width: 20px; height: 20px; cursor: pointer" src="@/assets/Settings.png" alt="" srcset="" />
        <img
          @click="offBigScreen"
          style="width: 18px; height: 18px; margin-left: 20px; cursor: pointer; color: red"
          src="../assets/suoxiao1.png"
          alt=""
          srcset=""
        />
      </div>
    </div>

    <div class="h[100vh]" v-show="showLoading">
      <dv-loading>
        <div class="color-white">正在初始化...</div>
      </dv-loading>
    </div>
    <div style="padding: 0 20px" v-show="!showLoading">
      <!-- 上部分 -->
      <firstPosition :data="pieDataInfo" height="2rem" @topChangeTab="topChangeTab" />
      <!-- 下部分 左中右 -->
      <lastPosition :commonFunctionList="commonFunctionList" :toDoListDataList="toDoListDataList" :defaultDataInfo="defaultDataInfo" />
    </div>
  </div>
</template>
<script setup lang="ts">
  // 适配flex
  import '@/utils/flexible.js'
  import firstPosition from '@/views/homePage/components/firstPosition.vue'
  import lastPosition from '@/views/homePage/components/lastPosition.vue'
  import { getBigScreenCommonFunctionApi, getBigScreenToDoListApi, getBigScreenDefaultDataApi, getStatisticsDataApi } from '@/api/bigScreen/index.js'
  defineOptions({
    name: 'HomePage',
  })
  const showLoading = ref(false)
  const pieDataInfo = ref({} as any) // 饼图数据
  const commonFunctionList = ref([] as any) // 常用功能列表
  const toDoListDataList = ref([] as any) // 待办事项列表
  const defaultDataInfo = ref({} as any) // 7选4的默认数据
  const period = ref('year') // 时间周期

  const props = defineProps({
    openBigScreenDialog: {
      type: Boolean,
    },
    newData: {
      type: Boolean,
      default: false,
    },
    systemSettings: {
      type: Boolean,
      default: true,
    },
  })

  const emit = defineEmits(['update:openBigScreenDialog', 'openOptions'])
  onMounted(() => {
    initData() // 初始化数据
  })

  // 确认更新数据
  const updateDataStatus = (data: any) => {
    initData() // 初始化数据
  }

  const openOptions = () => {
    emit('openOptions')
  }

  const offBigScreen = () => {
    emit('update:openBigScreenDialog', false)
  }

  defineExpose({
    offBigScreen,
  })

  watch(
    props,
    (newVal) => {
      if (!newVal.newData) {
        initData()
      }
    },
    { deep: true },
  )

  // 初始化数据
  const initData = () => {
    showLoading.value = true
    // 常用功能列表 待办事项 7选4的默认数据
    Promise.all([getCommonFunctionList(), getToDoListData(), getDefaultDataList(), getTopPieData()]).then(() => {
      showLoading.value = false
    })
  }

  // 获取常用功能列表
  const getCommonFunctionList = () => {
    return new Promise<void>((resolve, reject) => {
      getBigScreenCommonFunctionApi({})
        .then((res: any) => {
          const { data } = res

          commonFunctionList.value = data

          resolve(data)
        })
        .catch((err: any) => {})
    })
  }

  // 获取常用功能列表
  const getToDoListData = () => {
    return new Promise<void>((resolve, reject) => {
      getBigScreenToDoListApi({})
        .then((res: any) => {
          const { data } = res

          toDoListDataList.value = data
          resolve(data)
        })
        .catch((err: any) => {})
    })
  }
  // 获取7选4的默认数据
  const getDefaultDataList = () => {
    return new Promise<void>((resolve, reject) => {
      getBigScreenDefaultDataApi({})
        .then((res: any) => {
          const { data } = res
          defaultDataInfo.value = data
          resolve(data)
        })
        .catch((err: any) => {})
    })
  }

  // 获取顶部饼图数据
  const getTopPieData = () => {
    return new Promise<void>((resolve, reject) => {
      getStatisticsDataApi({
        period: period.value,
      })
        .then((res: any) => {
          const { data } = res

          pieDataInfo.value = data

          resolve(data)
        })
        .catch((err: any) => {})
    })
  }

  const topChangeTab = (val: any) => {
    period.value = val
    getTopPieData()
  }

  onMounted(() => {})
</script>

<style lang="scss" scoped>
  .content {
    background-color: #06182a;
    width: 100%;
    height: calc(100vh - 84px);
  }

  .title {
    width: 5rem;
    height: 0.55rem;
  }
</style>
