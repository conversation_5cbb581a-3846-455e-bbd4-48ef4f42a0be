<template>
  <div class="self_defining_data">
    <div class="top_text1">当前展示（仅可选择6个）</div>
    <VueDraggable id="present_data" class="present_data" v-model="presentItems" :animation="150" ghost-class="ghost" group="dataItems">
      <div
        class="form"
        v-for="(item, index) in presentItems"
        :key="item.menuId"
        :style="{
          border: presentactive === index ? '0.00001rem solid red' : '0.00001rem solid rgba(55, 183, 255, 0.2)',
          boxShadow: presentactive === index ? '0 0 5px rgba(255,0,0,0.5)' : 'none',
        }"
        @click="presentItemsActive(index)"
      >
        <img class="yichu" src="@/assets/yichu.png" alt="" @click.stop="yichu(item.topMenuId, item.menuId, index)" />
        <img v-if="item.icon" :src="item.icon" alt="" />
        <div class="name" v-if="item.title">{{ item.title }}</div>
      </div>
      <div class="form" v-for="item in 6 - presentItems.length">
        <span>+</span>
      </div>
    </VueDraggable>

    <div class="top_text2">待选区（选择上下可交换）</div>
    <div style="max-height: 3.5rem; width: 100%; margin-top: 1.3rem; overflow: auto; scrollbar-width: none; -ms-overflow-style: none">
      <div class="wait_data" v-for="item in waitItems" :key="item.menuId">
        <div class="text1">{{ item.title }}</div>
        <div id="wait_data1" class="wait_data1">
          <div
            class="form"
            v-for="(subItem, subIndex) in item.children"
            :key="subItem.menuId"
            :style="{
              border:
                waitactive?.parentId === item.menuId && waitactive?.childIndex === subIndex
                  ? '0.00001rem solid red'
                  : '0.00001rem solid rgba(55, 183, 255, 0.2)',
              boxShadow: waitactive?.parentId === item.menuId && waitactive?.childIndex === subIndex ? '0 0 5px rgba(255,0,0,0.5)' : 'none',
            }"
            @click="waitItemsActive(subItem, subIndex)"
          >
            <img v-if="subItem.icon" :src="subItem.icon" alt="" />
            <div class="name" v-if="subItem.title">{{ subItem.title }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import '@/utils/flexible.js'
  import { postBigScreenCommonFunctionApi } from '@/api/bigScreen'
  import { VueDraggable } from 'vue-draggable-plus'

  const { CommonlyUsedFunctionList } = defineProps({
    CommonlyUsedFunctionList: {
      type: Object as PropType<{
        setList: any[]
        allList: any[]
      }>,
      required: true,
    },
  })

  const presentItems = ref<any>(CommonlyUsedFunctionList.setList)

  const waitItems = ref<any>(CommonlyUsedFunctionList.allList)

  const presentactive = ref<any>()

  const waitactive = ref<any>()

  const onAdd = (event: any) => {
    //   const { newIndex, oldIndex, from, to } = event
    //   if (from.id === 'wait_data1' && to.id === 'present_data') {
    //     setTimeout(() => {
    //       // // 先检查是否已达到最大数量
    //       // if (presentItems._rawValue.length >= 6) {
    //       //   ElMessage.warning('最多只能选择6个')
    //       //   return
    //       // }
    //       // 找到被拖拽的子项
    //       const parentIndex = waitItems.value.findIndex((parent: any) => parent.children?.some((child: any) => child.menuId === event.item.dataset.id))
    //       if (parentIndex !== -1) {
    //         const parent = waitItems.value[parentIndex]
    //         const childIndex = parent.children.findIndex((child: any) => child.menuId === event.item.dataset.id)
    //         if (childIndex !== -1) {
    //           // 复制被拖拽的子项
    //           const draggedItem = { ...parent.children[childIndex] }
    //           // 添加到当前展示区
    //           presentItems.value = [...presentItems.value, draggedItem]
    //           // 从原父级的children中移除
    //           parent.children.splice(childIndex, 1)
    //         }
    //       }
    //     }, 0)
    //   }
  }

  const presentItemsActive = (index: any) => {
    presentactive.value = index

    if (presentactive.value !== undefined && waitactive.value !== undefined) {
      const highlightedPresentItem = presentItems.value[presentactive.value]
      const presentItemParent = waitItems.value.find((item: any) => item.menuId === highlightedPresentItem.topMenuId)

      const highlightedWaitItemParent = waitItems.value.find((item: any) => item.menuId === waitactive.value.parentId)

      if (presentItemParent && highlightedWaitItemParent) {
        const highlightedWaitItem = highlightedWaitItemParent.children[waitactive.value.childIndex]
        presentItemParent.children.push(highlightedPresentItem)
        presentItems.value.splice(presentactive.value, 1)
        presentItems.value.push(highlightedWaitItem)
        highlightedWaitItemParent.children.splice(waitactive.value.childIndex, 1)
        presentactive.value = undefined
        waitactive.value = undefined
      }
    }
  }

  const waitItemsActive = (subItem: any, index: any) => {
    const parent = waitItems.value.find((item: any) => item.menuId === subItem.topMenuId)

    if (presentItems.value.length < 6) {
      presentItems.value.push(subItem)
      const parentIndex = waitItems.value.findIndex((item: any) => item.menuId === subItem.topMenuId)

      if (parentIndex !== -1) {
        const childIndex = waitItems.value[parentIndex].children.findIndex((child: any) => child.menuId === subItem.menuId)

        if (childIndex !== -1) {
          waitItems.value[parentIndex].children.splice(childIndex, 1)
          presentactive.value = undefined
          waitactive.value = undefined
        }
      }
    } else if (parent && subItem.topMenuId === parent.menuId) {
      waitactive.value = {
        parentId: parent.menuId,
        childIndex: index,
      }
      if (presentactive.value !== undefined && waitactive.value !== undefined) {
        const highlightedPresentItem = presentItems.value[presentactive.value]
        const presentItemParent = waitItems.value.find((item: any) => item.menuId === highlightedPresentItem.topMenuId)

        if (presentItemParent) {
          presentItemParent.children.push(highlightedPresentItem)
          presentItems.value.splice(presentactive.value, 1)

          presentItems.value.push(subItem)
          const childIndex = parent.children.findIndex((child: any) => child.menuId === subItem.menuId)
          if (childIndex !== -1) {
            parent.children.splice(childIndex, 1)
          }
          presentactive.value = undefined
          waitactive.value = undefined
        }
      }
    }
  }

  const yichu = (topMenuId: any, menuId: any, index: any) => {
    const index1 = waitItems.value.findIndex((item: { menuId: any }) => item.menuId === topMenuId)
    const index2 = presentItems.value.find((item: { menuId: any }) => item.menuId === menuId)
    waitItems.value[index1].children.push(index2)
    presentItems.value.splice(index, 1)
    //
    presentactive.value = undefined
  }

  const saveCommonFunction = async () => {
    if (presentItems.value.length !== 6) {
      ElMessage.error('请选择6个')
      return false
    }
    const ids = presentItems.value.map((item: { menuId: any }) => item.menuId)
    const params = {
      ids: ids,
    }

    await postBigScreenCommonFunctionApi(params)

    return true
  }

  defineExpose({
    saveCommonFunction,
    presentItems,
  })
</script>

<style lang="scss" scoped>
  .ghost {
    opacity: 0.5;
    background: rgba(55, 183, 255, 0.2);
  }

  .self_defining_data {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .top_text1 {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width: 17.4375rem;
      height: 5%;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      font-size: 0.24rem;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      margin-top: 0.3rem;
    }

    .present_data {
      width: 100%;
      height: 20%;
      display: flex;
      align-items: center;

      .form {
        margin-top: 1.2rem;
        width: 1.5rem;
        height: 1.2rem;
        margin-left: 0.23rem;
        border: 0.00001rem solid rgba(55, 183, 255, 0.2);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .yichu {
          position: absolute;
          width: 0.2rem;
          height: 0.2rem;
          top: 1.13rem;
          margin-left: 1.5rem;
          z-index: 999;
          cursor: pointer;
        }

        img {
          width: 0.8rem;
          height: 0.5rem;
        }

        .name {
          width: 1rem;
          font-size: 0.15rem;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 0.1rem;
          color: #ffffff;
          border: none;
        }

        span {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: bold;
          color: #37b7ff;
          font-size: 0.5rem;
          text-align: left;
          font-style: normal;
          cursor: pointer;
        }
      }
    }

    .top_text2 {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width: 17.4375rem;
      height: 5%;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      font-size: 0.24rem;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      margin-top: 3rem;
    }

    .wait_data {
      width: 100%;
      align-content: flex-start;

      .text1 {
        width: 5rem;
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: 400;
        font-size: 0.2rem;
        color: #ffffff;
        text-align: left;
        font-style: normal;
        margin-left: 0.25rem;
        margin-top: 0.2rem;
      }

      .wait_data1 {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        margin-top: 0.1rem;

        .form {
          width: 1.5rem;
          height: 1.2rem;
          margin-left: 0.23rem;
          margin-bottom: 0.3rem;
          border: 0.00001rem solid rgba(55, 183, 255, 0.2);
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          img {
            width: 0.8rem;
            height: 0.5rem;
          }

          .name {
            width: 1rem;
            font-size: 0.15rem;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 0.1rem;
            color: #ffffff;
            border: none;
          }

          span {
            font-family: SourceHanSansCN, SourceHanSansCN;
            font-weight: bold;
            color: #37b7ff;
            font-size: 0.5rem;
            text-align: left;
            font-style: normal;
            cursor: pointer;
          }
        }
      }

      .wait_data2 {
        margin-top: -1rem;
      }
    }
  }
</style>
