<template>
  <div class="shuju">
    <ul>
      <li v-for="item in data.list" :class="{ active: data.active === item.id }" @click="tabData(item.id)">{{ item.name }}</li>
    </ul>
  </div>
  <img src="@/assets/zuoshangjiao.png" alt="" class="zuoshang" />
  <img src="@/assets/youshangjiao.png" alt="" class="youshang" />
  <img src="@/assets/zuoxiajiao.png" alt="" class="zuoxia" />
  <img src="@/assets/youxiajiao.png" alt="" class="youxia" />
  <SelfDefiningData ref="SelfDefiningDataRef" v-if="data.active === 1" :active="data.active" style="z-index: 2; position: relative" />
  <SelfDefiningIcon ref="selfDefiningIconRef" v-if="data.active === 2" :SelfDefiningIconList="SelfDefiningIconList" style="z-index: 2; position: relative" />
  <CommonlyUsedFunction
    ref="commonFuncRef"
    v-if="data.active === 3"
    :CommonlyUsedFunctionList="CommonlyUsedFunctionList"
    style="z-index: 2; position: relative"
  />

  <div class="bottom_button">
    <div class="save" @click="callChildAdd">保存</div>
    <div class="close" @click="closeDialog">关闭</div>
  </div>
</template>

<script lang="ts" setup>
  import SelfDefiningData from './SelfDefiningData.vue'
  import SelfDefiningIcon from './SelfDefiningIcon.vue'
  import CommonlyUsedFunction from './CommonlyUsedFunction.vue'
  import { getBigScreenCommonAllFunctionApi, getBigScreenSelfDefiningIconApi } from '@/api/bigScreen/index.js'
  import '@/utils/flexible.js'
  const props = defineProps({
    optionDialog: {
      type: Boolean,
    },
  })

  const commonFuncRef = ref()
  const selfDefiningIconRef = ref()
  const SelfDefiningDataRef = ref()

  //向上传递的数据
  const CommonlyUsedFunctionList = ref()
  const SelfDefiningIconList = ref()
  const SelfDefiningDataList = ref()

  const showLoading = ref(false)

  const data = reactive({
    list: [
      {
        name: '自定义数据',
        id: 1,
      },
      {
        name: '自定义图表',
        id: 2,
      },
      {
        name: '常用功能',
        id: 3,
      },
    ],
    active: 0,
  })

  onMounted(() => {
    initData() // 初始化数据
  })

  // 初始化数据
  const initData = () => {
    showLoading.value = true
    data.active = 1
    // 获取数据
    Promise.all([getBigScreenCommonAllFunction(), getBigScreenSelfDefiningIcon()]).then(() => {
      showLoading.value = false
    })
  }

  //获取常用列表
  const getBigScreenCommonAllFunction = () => {
    getBigScreenCommonAllFunctionApi({})
      .then((res: any) => {
        if (res.code === 200) {
          CommonlyUsedFunctionList.value = res.data //已选常用列表
        }
      })
      .catch((err: any) => {})
  }

  const getBigScreenSelfDefiningIcon = () => {
    //获取自定义图标数据
    getBigScreenSelfDefiningIconApi({})
      .then((res: any) => {
        if (res.code === 200) {
          SelfDefiningIconList.value = res.data
        }
      })
      .catch((err: any) => {})
  }

  const callChildAdd = async () => {
    if (commonFuncRef.value) {
      CommonlyUsedFunctionList.value = commonFuncRef.value.presentItems
      const success = await commonFuncRef.value.saveCommonFunction()
      if (success) {
        ElMessage({
          message: '保存成功',
          type: 'success',
        })
        closeDialog()
        emit('updateData', true)
      }
    } else if (selfDefiningIconRef.value) {
      SelfDefiningIconList.value = selfDefiningIconRef.value.presentItems
      const success = await selfDefiningIconRef.value.saveSelfDefiningIcon()
      if (success) {
        ElMessage({
          message: '保存成功',
          type: 'success',
        })
        closeDialog()
        emit('updateData', true)
      }
    } else if (SelfDefiningDataRef.value) {
      SelfDefiningDataList.value = SelfDefiningDataRef.value.presentItems
      const success = await SelfDefiningDataRef.value.saveSelfDefiningData()
      if (success) {
        ElMessage({
          message: '保存成功',
          type: 'success',
        })
        closeDialog()
        emit('updateData', true)
      }
    }
  }

  const closeDialog = () => {
    data.active = 0
    emit('update:optionDialog', false)
  }

  const emit = defineEmits(['update:optionDialog', 'updateData'])

  const tabData = (id: any) => {
    data.active = id
  }

  defineExpose({
    CommonlyUsedFunctionList,
    SelfDefiningIconList,
    SelfDefiningDataList,
    initData,
    data,
  })
</script>

<style lang="scss" scoped>
  .shuju {
    border: 1px solid rgba(55, 183, 255, 0.2);
    position: absolute;
    top: -1px;
    left: -115px;
    color: #ffffff;

    ul {
      background-color: #010033;
      display: flex;
      flex-flow: column;
      justify-content: center;
      align-items: center;

      li {
        width: 100%;
        padding: 15px 22px;
        display: flex;
        flex-flow: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        color: #ffffff;
        z-index: 999;

        &.active {
          background-color: #184184;
        }
      }
    }
  }

  img {
    position: absolute;
    z-index: 1;
  }

  .zuoshang {
    top: 0;
    left: 0;
  }
  .youshang {
    top: 0;
    right: 0;
  }
  .zuoxia {
    left: 0;
    bottom: 0;
  }
  .youxia {
    right: 0;
    bottom: 0;
  }

  .box {
    position: absolute;
    width: 53px;
    height: 53px;
    background: linear-gradient(135deg, #00a3ff 0%, rgba(0, 163, 255, 0) 50%);
  }

  .bottom_button {
    width: 14rem;
    height: 1rem;
    margin-top: -6%;
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 0;
    z-index: 999;

    .save {
      width: 1.2rem;
      height: 0.5rem;
      background: #00a3ff;
      border-radius: 2px;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      font-size: 0.16rem;
      color: #ffffff;
      font-style: normal;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 3%;
      cursor: pointer;
    }

    .close {
      width: 1.2rem;
      height: 0.5rem;
      border-radius: 2px;
      border: 1px solid #3f97fd;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 400;
      font-size: 0.16rem;
      color: #ffffff;
      font-style: normal;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }
</style>
