<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-04-07 17:56:59
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-21 21:00:20
 * @Description: 
-->
<template>
  <div class="header">
    <div class="flex flex-row items-center justify-between">
      <div class="flex flex-row items-center">
        <slot name="left" />
      </div>
      <slot name="title" />
      <div class="flex items-center justify-end" style="padding-top: 6px">
        <img v-if="props.systemSettings" @click="openOptions" style="width: 20px; height: 20px; cursor: pointer" src="@/assets/Settings.png" alt="" srcset="" />
        <img @click="openBigScreen" style="width: 16px; height: 16px; margin-left: 20px; cursor: pointer" src="../assets/fangda.png" alt="" srcset="" />
      </div>
    </div>
    <el-dialog custom-class="option-dialog" v-model="optionDialog" :close-on-click-modal="false" :show-close="false" draggable>
      <CustomizingVisualizations v-model:optionDialog="optionDialog" @updateData="updateData" ref="CustomizingVisualizationsRef" />
    </el-dialog>

    <el-dialog
      custom-class="big_screen"
      v-model="openBigScreenDialog"
      fullscreen
      :close-on-click-modal="false"
      :show-close="false"
      style="width: 100%; height: 100%"
    >
      <BigIndex v-model:openBigScreenDialog="openBigScreenDialog" @openOptions="openOptions" :newData="newData" :systemSettings="props.systemSettings" />
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
  import CustomizingVisualizations from './CustomizingVisualizations.vue'
  import BigIndex from './BigIndex.vue'

  const emit = defineEmits(['updateDataStatus'])

  const props = defineProps({
    brandList: {
      type: Array,
      default: () => [],
    },
    systemSettings: {
      type: Boolean,
      default: true,
    },
  })
  const optionDialog = ref(false)
  const openBigScreenDialog = ref(false)
  const CustomizingVisualizationsRef = ref()

  //向上传递的数据
  const CommonlyUsedFunctionList = ref([])
  const SelfDefiningDataList = ref([])
  const SelfDefiningIconList = ref([])
  const newData = ref()

  const openOptions = () => {
    CustomizingVisualizationsRef.value?.initData()
    optionDialog.value = true
    newData.value = optionDialog.value
  }

  const openBigScreen = () => {
    openBigScreenDialog.value = true
  }

  const updateData = (data: any) => {
    newData.value = optionDialog.value
    emit('updateDataStatus', data)
  }

  // 监听optionDialog变化
  watch(optionDialog, (newVal) => {
    CommonlyUsedFunctionList.value = CustomizingVisualizationsRef.value?.CommonlyUsedFunctionList
    SelfDefiningDataList.value = CustomizingVisualizationsRef.value?.SelfDefiningDataList
    SelfDefiningIconList.value = CustomizingVisualizationsRef.value?.SelfDefiningIconList
  })

  onMounted(() => {})

  //退出页面
  onUnmounted(() => {})

  defineExpose({
    CommonlyUsedFunctionList,
    SelfDefiningDataList,
    SelfDefiningIconList,
  })
</script>
<style scoped lang="scss">
  .header {
    width: 100%;
    padding: 0 20px;

    :deep(.el-dialog) {
      width: 14.4rem;
      height: 9rem;
      background-color: #06182a !important;
      border: 1px solid rgba(55, 183, 255, 0.2);
    }
  }
  :deep(.el-dialog__header) {
    padding: 0 !important;
  }

  :deep(.is-fullscreen) {
    padding: 0 0.2rem !important;
  }
</style>
