<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-03-11 20:48:35
 * @LastEditors: z<PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-22 09:27:48
 * @Description: 
-->
<!-- 饼图 -->
<template>
  <div class="pie-chart">
    <div class="pieChartContent" ref="pieRef" :style="{ height: props.height, width: props.width }" />
    <div v-if="props.data.greenRatio" style="position: absolute; top: -0.12rem; left: 50%; transform: translate(-50%, 0%)">
      <span style="font-size: 0.1rem; color: #fff">{{ props.data.greenRatioLabel + ':' }}</span>
      <span style="font-size: 0.2rem; color: #fff; margin-left: 4px; font-weight: 500; font-family: jdFont, serif">{{ props.data.greenRatio }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import * as echarts from 'echarts'

  const props = defineProps({
    data: {
      type: Object as any,
      default: () => {},
    },
    width: {
      type: String,
      default: '1.3rem',
    },
    height: {
      type: String,
      default: '1.3rem',
    },
  })
  const pieRef = ref()
  let myChart: echarts.ECharts | null = null
  let resizeObserver: ResizeObserver | null = null

  onMounted(() => {
    if (props.data) {
      initChart(props.data)
    }
    // 使用 ResizeObserver 替代 window.resize
    resizeObserver = new ResizeObserver(() => {
      if (myChart) {
        myChart.resize()
      }
    })
    if (pieRef.value) {
      resizeObserver.observe(pieRef.value)
    }
  })

  onUnmounted(() => {
    if (myChart) {
      myChart.dispose()
      myChart = null
    }
    if (resizeObserver) {
      resizeObserver.disconnect()
      resizeObserver = null
    }
  })

  const initChart = (data: any) => {
    if (!pieRef.value) return

    if (myChart) {
      myChart.dispose()
    }

    myChart = echarts.init(pieRef.value)
    if (!myChart) return

    var seriesData: string | any[] = []
    var externalSeriesData = []
    if (data.data.length === 0) {
      data.isShowLabel = false
      seriesData = [
        {
          value: 0, // 数值为0
          itemStyle: {
            color: 'rgba(0, 0, 0, 0)',
          },
        },
      ]
      externalSeriesData = [
        {
          value: 0, // 数值为0
          itemStyle: {
            color: 'rgba(0, 0, 0, 0)',
          },
        },
      ]
    } else {
      data.isShowLabel = true
      seriesData = data.data.map((item: any) => {
        return {
          value: item.number,
          name: item.label,
          itemStyle: {
            color: item.color,
            color1: item.outLineColor,
          },
        }
      })
      externalSeriesData = data.data.map((item: any) => {
        return {
          value: item.number,
          name: item.label,
          itemStyle: {
            color: item.outLineColor,
          },
        }
      })
    }

    const options = {
      title: {
        text: data.total, //主标题
        subtext: data.name, // 副标题文本
        top: props.height == '2rem' ? '38%' : '35%',
        left: 'center',
        itemGap: 4, // 主标题和副标题之间的间距
        triggerEvent: true,
        textStyle: {
          color: '#fff',
          fontWeight: 'bold',
          fontSize: props.height == '2rem' ? '0.2rem' : '0.16rem',
          fontFamily: `jdFont, serif`,
        },
        subtextStyle: {
          color: '#fff', // 副标题颜色
          fontWeight: '500',
          fontSize: props.height == '2rem' ? '0.15rem' : '0.1rem', // 副标题字体大小
        },
      },
      tooltip: {
        show: data.isShowLabel,
        trigger: 'item',
        position: 'bottom', // 提示框浮层的位置
        confine: false, // 防止超出容器
        backgroundColor: 'rgba(13, 64, 71, 1)',
        borderColor: 'rgba(143, 225, 252, 0.60)',
        padding: 8,
        textStyle: {
          color: '#fff',
          fontSize: '0.1rem',
        },
        // formatter: function (params: any) {
        //   console.log('params: ', params)
        //   var relVal = params.seriesName
        //   console.log('seriesData: ', seriesData)
        //   for (var i = 0, l = seriesData.length; i < l; i++) {
        //     relVal +=
        //       '<br/>' + params.marker + seriesData[i].name + '：' + (seriesData[i].value || seriesData[i].value == 0 ? seriesData[i].value + data.unit : ' -')
        //   }

        //   return relVal
        // },
        // formatter: '{a} <br/>{b} : {c} ({d}%)', // 提示框浮层的具体内容格式
        formatter: function (params: any) {
          var relVal = params.seriesName
          for (var i = 0, l = seriesData.length; i < l; i++) {
            var color = seriesData[i].itemStyle ? seriesData[i].itemStyle.color1 : params.data.itemStyle.color
            var marker = '<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:' + color + ';"></span>'
            relVal += '<br/>' + marker + seriesData[i].name + '：' + (seriesData[i].value || seriesData[i].value === 0 ? seriesData[i].value + data.unit : ' -')
          }
          return relVal
        },
      },
      series: [
        {
          name: data.name,
          type: 'pie',
          radius: props.height == '2rem' ? ['48%', '68%'] : ['55%', '70%'],
          center: ['50%', '50%'], // 圆心位置
          hoverAnimation: true, // 悬停动画
          animationDuration: 400,
          label: {
            show: false, // data.isShowLabel, // 当数据长度大于0时显示标签,
            align: 'left', // 'left' | 'center' | 'right'
            position: 'outside', // 'top' | 'bottom' | 'inside' | 'insideLeft' | 'insideRight' | 'insideTop' | 'insideBottom' | 'insideTopLeft' | 'insideBottomLeft' | 'insideTopRight' | 'insideBottomRight'
            alignTo: 'labelLine', // 'edge' | 'labelLine'
            distanceToLabelLine: 8, // 标签距离轴线的距离
            overflow: 'truncate', // 文本溢出时的处理方式 'none' | 'truncate' | 'break' | 'breakAll'
            ellipsis: 'truncate', //'...', // 溢出时显示省略符号
            width: 40, // 标签最大宽度（超出后触发溢出处理）
            avoidLabelOverlap: true, // 自动防止标签重叠（饼图/环形图专用）
            formatter: '{total|{b}}\n{name|{c}}',
            rich: {
              total: {
                fontSize: '0.11rem',
                color: '#fff',
                fontWeight: '500',
                align: 'center',
              },
              name: {
                fontSize: '0.1rem',
                color: '#fff',
                align: 'center',
                fontWeight: '500',
              },
            },
          },
          // labelLine: {
          //   length: 8,
          //   length2: 1,
          //   maxSurfaceAngle: 0, // 最大表面角度
          // },
          // labelLayout: function (params: any) {
          //   if (!data.isShowLabel) return
          //   const isLeft = params.labelRect.x < myChart!.getWidth() / 2
          //   const points = params.labelLinePoints
          //   points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width
          //   return {
          //     labelLinePoints: points,
          //   }
          // },
          emphasis: {
            scale: true,
            scaleSize: 1.3, // 悬浮放大比例
            focus: 'none', // 避免聚焦时其他元素缩小
          },
          z: 3,
          data: seriesData,
        },
        {
          name: data.name,
          type: 'pie',
          radius: props.height == '2rem' ? ['68%', '71%'] : ['71%', '74%'],
          clockWise: true, // 饼图的绘制顺序是否为顺时针
          hoverAnimation: true, // 鼠标移入饼图是否高亮
          label: {
            show: false,
          },
          emphasis: {
            scale: true,
            scaleSize: 1.2, // 悬浮放大比例
            focus: 'none', // 避免聚焦时其他元素缩小
          },
          itemStyle: {
            color: 'transparent', // 中心透明
            borderWidth: 0,
          },
          z: 1,
          data: externalSeriesData,
        },
      ],
    }
    // 绘制图表
    myChart.setOption(options)
  }

  //监听传过来的数据
  watch(
    () => props.data,
    (val) => {
      nextTick(() => {
        initChart(val)
      })
    },
    {
      deep: true,
      immediate: true,
    },
  )
</script>

<style scoped>
  .pie-chart {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: relative;
  }
  .pieChartContent {
    background: url('../assets/pie_bg.png') no-repeat center;
    background-size: 100% 100%;
    width: 100%;
    height: 100%;
  }
</style>
