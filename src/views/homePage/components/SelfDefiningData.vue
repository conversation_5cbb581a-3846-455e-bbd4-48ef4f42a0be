<template>
  <div class="self_defining_data">
    <div class="top_text1">当前展示（仅可选择6个）</div>
    <VueDraggable id="present_data" class="present_data" v-model="presentItems" :animation="150" ghost-class="ghost" group="dataItems" @add="onAdd">
      <div
        v-for="(item, index) in presentItems"
        :key="item.templateId"
        @click="presentItemsActive(index)"
        :style="{
          border: presentactive === index ? '0.00001rem solid red' : '0.00001rem solid rgba(55, 183, 255, 0.2)',
          boxShadow: presentactive === index ? '0 0 5px rgba(255,0,0,0.5)' : 'none',
        }"
      >
        <img class="yichu" src="@/assets/yichu.png" alt="" @click.stop="yichu(item.id)" />
        <img v-if="item.icon" :src="item.icon" alt="" />
      </div>
      <div v-for="item in 6 - presentItems.length" class="item">
        <span>+</span>
      </div>
    </VueDraggable>

    <div class="top_text2">待选区（选择上下可交换）</div>
    <div style="max-height: 3.7rem; width: 100%; margin-top: 0.8rem; overflow: auto; scrollbar-width: none; -ms-overflow-style: none">
      <div class="wait_data">
        <div
          class="wait_data1"
          v-for="(item, index) in filteredWaitItems"
          :key="item.templateId"
          @click="waitItemsActive(index)"
          :style="{
            border: waitactive === index ? '0.00001rem solid red' : '0.00001rem solid rgba(55, 183, 255, 0.2)',
            boxShadow: waitactive === index ? '0 0 5px rgba(255,0,0,0.5)' : 'none',
          }"
        >
          <div v-if="index < 6" class="item">
            <img v-if="item.icon" :src="item.icon" alt="" />
            <span v-if="item.showPlus">+</span>
          </div>
          <div v-if="index >= 6" class="item">
            <img v-if="item.icon" :src="item.icon" alt="" />
            <span v-if="item.showPlus">+</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { VueDraggable } from 'vue-draggable-plus'
  import { ref, inject } from 'vue'
  import { postBigScreenSelfDefiningDataApi } from '@/api/bigScreen/index'
  import { getBigScreenSelfDefiningDataApi, getBigScreenSelfDefiningDataAllApi } from '@/api/bigScreen/index.js'
  import '@/utils/flexible.js'

  const { active } = defineProps({
    active: {
      type: Number,
    },
  })

  console.log(active)

  const presentItems = ref<any>([])

  const waitItems = ref<any>([])

  const filteredWaitItems = ref<any[]>([])

  const presentactive = ref<any>()

  const waitactive = ref<any>()

  // const onAdd = () => {
  //   if (presentItems.value.length > 4) {
  //     const lastItem = presentItems.value.pop()

  //     waitItems.value.push(lastItem)
  //   }
  // }

  // const onAdd = (event: any) => {
  //   //
  //   const { newIndex, oldIndex, data } = event
  //
  //
  //
  //   if (presentItems.value.length > 4) {
  //     const lastItem = presentItems.value.splice(newIndex + 1, 1)
  //

  //     waitItems.value.splice(oldIndex, 0, lastItem)
  //   }
  // }

  const swapFunction = () => {
    if (presentactive.value != undefined && waitactive.value !== undefined) {
      const pactive = presentItems.value[presentactive.value]
      const wactive = filteredWaitItems.value[waitactive.value]
      presentItems.value.splice(presentactive.value, 1, wactive)
      filteredWaitItems.value.splice(waitactive.value, 1, pactive)
      presentactive.value = undefined
      waitactive.value = undefined
    }
  }

  const presentItemsActive = (index: any) => {
    presentactive.value = index
    swapFunction()
  }

  const waitItemsActive = (index: any) => {
    if (presentItems.value.length < 6) {
      const selectedItem = filteredWaitItems.value[index]

      presentItems.value.push(selectedItem)
      presentactive.value = undefined
      waitactive.value = undefined
    } else {
      waitactive.value = index
      swapFunction()
    }
  }

  watch(
    [presentItems, waitItems],
    ([newPresentItems, newWaitItems]) => {
      const presentIds = new Set(newPresentItems.map((item: { templateId: any }) => item.templateId))
      filteredWaitItems.value = newWaitItems.filter((item: { templateId: unknown }) => !presentIds.has(item.templateId))
    },
    { immediate: true, deep: true },
  )
  const yichu = (id: any) => {
    const index = presentItems.value.findIndex((item: { id: any }) => item.id === id)

    presentItems.value.splice(index, 1)
    presentactive.value = undefined
    waitactive.value = undefined
  }

  const getBigScreenSelfDefiningData = () => {
    //获取自定义数据
    getBigScreenSelfDefiningDataApi({})
      .then((res: any) => {
        if (res.code === 200) {
          presentItems.value = res.data
        }
      })
      .catch((err: any) => {})
  }

  const getBigScreenSelfDefiningDataAll = () => {
    //获取所有自定义数据
    getBigScreenSelfDefiningDataAllApi({})
      .then((res: any) => {
        if (res.code === 200) {
          waitItems.value = res.data
        }
      })
      .catch((err: any) => {})
  }

  const onAdd = () => {
    if (presentItems.value.length > 6) {
      presentItems.value.pop()
    }
  }

  const saveSelfDefiningData = async () => {
    if (presentItems.value.length !== 6) {
      ElMessage.error('请选择6个')
      return false
    }
    const params = presentItems.value.map((item: any, index: any) => ({
      templateId: item.templateId,
      sortNo: index + 1,
    }))
    await postBigScreenSelfDefiningDataApi(params)
    return true
  }

  watch(
    () => active,
    (newVal) => {
      if (newVal === 1) {
        getBigScreenSelfDefiningData()
        getBigScreenSelfDefiningDataAll()
      }
    },
    { immediate: true },
  )

  onMounted(() => {
    getBigScreenSelfDefiningData(), getBigScreenSelfDefiningDataAll()
  })

  defineExpose({
    saveSelfDefiningData,
    presentItems,
  })
</script>

<style lang="scss" scoped>
  .self_defining_data {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .top_text1 {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width: 17.4375rem;
      height: 5%;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      font-size: 0.24rem;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      margin-top: 0.3rem;
    }

    .present_data {
      width: 100%;
      height: 20%;
      display: flex;
      // justify-content: space-evenly;
      align-items: center;

      div {
        width: 1.6rem;
        height: 1.6rem;
        border: 0.00001rem solid rgba(55, 183, 255, 0.2);
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        margin: 0.356rem;
        margin-top: 1rem;

        .yichu {
          position: absolute;
          width: 0.2rem;
          height: 0.2rem;
          top: -0.1rem;
          margin-left: 1.6rem;
          cursor: pointer;
        }

        img {
          width: 100%;
          height: 100%;
        }

        span {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: bold;
          color: #37b7ff;
          font-size: 0.5rem;
          text-align: left;
          font-style: normal;
          cursor: pointer;
        }
      }
    }

    .top_text2 {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width: 17.4375rem;
      height: 5%;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      font-size: 0.24rem;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      margin-top: 3rem;
    }

    .wait_data {
      width: 100%;
      height: 47%;
      display: flex;
      padding-left: 0.05rem;
      flex-wrap: wrap;
      justify-content: flex-start;

      .wait_data1 {
        width: 1.6rem;
        height: 1.6rem;
        border: 0.00001rem solid rgba(55, 183, 255, 0.2);
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        margin: 0.1rem 0.35rem;

        img {
          width: 100%;
          height: 100%;
        }

        span {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: bold;
          font-size: 0.5rem;
          color: #37b7ff;
          text-align: left;
          font-style: normal;
          cursor: pointer;
        }
      }
    }
  }
</style>
