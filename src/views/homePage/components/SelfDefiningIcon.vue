<template>
  <div class="self_defining_data">
    <div class="top_text1">当前展示（仅可选择4个）</div>
    <VueDraggable class="present_data" v-model="presentItems" ghost-class="ghost" group="dataItems" :animation="150" @add="onAdd">
      <div
        class="form"
        v-for="(item, index) in presentItems"
        :key="item.id"
        :style="{
          border: presentactive === index ? '0.00001rem solid red' : '0.00001rem solid rgba(55, 183, 255, 0.2)',
          boxShadow: presentactive === index ? '0 0 5px rgba(255,0,0,0.5)' : 'none',
        }"
        @click="presentItemsActive(index)"
      >
        <img class="yichu" src="@/assets/yichu.png" alt="" @click.stop="yichu(item.id)" />
        <img v-if="item.icon" :src="item.icon" alt="" />
        <div class="iconName">{{ iconName[index] }}</div>
      </div>
      <div class="form" v-for="item in 4 - presentItems.length">
        <span>+</span>
      </div>
    </VueDraggable>

    <div class="top_text2">待选区（选择上下可交换）</div>
    <div style="max-height: 2.4rem; width: 100%; margin-top: 1.5rem; overflow: auto; scrollbar-width: none; -ms-overflow-style: none">
      <div class="present_data1" style="margin-top: 0.2rem" :animation="150">
        <div
          v-for="(item, index) in waitItems"
          :key="item.id"
          :style="{
            border: waitactive === index ? '0.00001rem solid red' : '0.00001rem solid rgba(55, 183, 255, 0.2)',
            boxShadow: waitactive === index ? '0 0 5px rgba(255,0,0,0.5)' : 'none',
          }"
          @click="waitItemsActive(index)"
        >
          <img v-if="item.icon" :src="item.icon" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import '@/utils/flexible.js'
  import { postBigScreenSelfDefiningIconApi } from '@/api/bigScreen'
  import { VueDraggable } from 'vue-draggable-plus'

  const { SelfDefiningIconList } = defineProps({
    SelfDefiningIconList: {
      type: Object as PropType<{
        setList: any[]
        allList: any[]
      }>,
      required: true,
    },
  })

  const presentItems = ref<any>(SelfDefiningIconList.setList)

  const waitItems = ref<any>(SelfDefiningIconList.allList)

  const iconName = ref(['左一', '左二', '右一', '右二'])

  const presentactive = ref<any>()

  const waitactive = ref<any>()

  // const onAdd = () => {
  //   if (presentItems.value.length > 4) {
  //     const lastItem = presentItems.value.pop()

  //     waitItems.value.push(lastItem)
  //   }
  // }

  const onAdd = () => {
    presentactive.value = undefined
    waitactive.value = undefined
  }

  // const onAdd = (event: any) => {
  //   //
  //   const { newIndex, oldIndex, data } = event
  //
  //
  //
  //   if (presentItems.value.length > 4) {
  //     const lastItem = presentItems.value.splice(newIndex + 1, 1)
  //

  //     waitItems.value.splice(oldIndex, 0, lastItem)
  //   }
  // }

  const swapFunction = () => {
    if (presentactive.value != undefined && waitactive.value !== undefined) {
      const pactive = presentItems.value[presentactive.value]
      const wactive = waitItems.value[waitactive.value]
      presentItems.value.splice(presentactive.value, 1, wactive)
      waitItems.value.splice(waitactive.value, 1, pactive)
      presentactive.value = undefined
      waitactive.value = undefined
    }
  }

  const presentItemsActive = (index: any) => {
    presentactive.value = index
    swapFunction()
  }

  const waitItemsActive = (index: any) => {
    if (presentItems.value.length < 4) {
      const selectedItem = waitItems.value[index]
      presentItems.value.push(selectedItem)
      waitItems.value.splice(index, 1)
      presentactive.value = undefined
      waitactive.value = undefined
    } else {
      waitactive.value = index
      swapFunction()
    }
  }

  const saveSelfDefiningIcon = async () => {
    if (presentItems.value.length !== 4) {
      ElMessage.error('请选择4个')
      return false
    }
    const ids = presentItems.value.map((item: { id: any }) => item.id)
    const params = {
      ids: ids,
    }
    await postBigScreenSelfDefiningIconApi(params)
    return true
  }

  const yichu = (id: any) => {
    const index = presentItems.value.findIndex((item: { id: any }) => item.id === id)
    if (index !== -1) {
      const [presentRemovedItem] = presentItems.value.splice(index, 1)
      waitItems.value.push(presentRemovedItem)
      presentactive.value = undefined
      waitactive.value = undefined
    }
  }

  defineExpose({
    saveSelfDefiningIcon,
    presentItems,
  })
</script>

<style lang="scss" scoped>
  .ghost {
    opacity: 0.5;
    background: rgba(55, 183, 255, 0.2);
  }
  .self_defining_data {
    width: 100%;
    height: 100%;
    padding: 0 0.3rem;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .top_text1 {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width: 17.4375rem;
      height: 5%;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      font-size: 0.24rem;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      margin-top: 0.3rem;
    }
    .present_data {
      width: 100%;
      height: 25%;
      display: flex;
      align-items: center;

      .form {
        width: 23%;
        height: 1.8rem;
        border: 0.00001rem solid rgba(55, 183, 255, 0.2);
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        margin: 1.2rem 0.13rem 0;
        padding: 0.1rem;

        .yichu {
          position: absolute;
          width: 0.2rem;
          height: 0.2rem;
          top: -0.1rem;
          margin-left: 3.1rem;
          cursor: pointer;
        }

        img {
          width: 100%;
          height: 100%;
        }

        .iconName {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: bold;
          font-size: 0.15rem;
          color: #ffffff;
          text-align: left;
          font-style: normal;
          position: absolute;
          top: 1.9rem;
          z-index: 111;
        }

        span {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: bold;
          color: #37b7ff;
          font-size: 0.5rem;
          text-align: left;
          font-style: normal;
          cursor: pointer;
        }
      }
    }

    .top_text2 {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      width: 17.4375rem;
      height: 5%;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      font-size: 0.24rem;
      color: #ffffff;
      text-align: center;
      font-style: normal;
      margin-top: 3.9rem;
    }
    .present_data1 {
      width: 100%;
      height: 25%;
      display: flex;
      flex-wrap: wrap;

      div {
        width: 23%;
        height: 1.8rem;
        border: 0.00001rem solid rgba(55, 183, 255, 0.2);
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 0.13rem 0.4rem;
        padding: 0.1rem;

        img {
          width: 100%;
          height: 100%;
        }

        span {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: bold;
          color: #37b7ff;
          font-size: 0.5rem;
          text-align: left;
          font-style: normal;
          cursor: pointer;
        }
      }
    }
  }
</style>
