<!--  堆叠柱状图————订单数 -->
<template>
  <div ref="dingdanChartRef" :style="{ height: props.height, width: props.width }" />
</template>

<script setup lang="ts">
  import * as echarts from 'echarts'
  import type { EChartsOption, SeriesOption } from 'echarts'
  import { onMounted } from 'vue'
  import { debounce } from 'lodash'

  const props = defineProps({
    data: {
      type: Object as PropType<{
        id: string // 添加id属性
        s3: 0
        list: {
          id: number
          name: string
          v1: number
          v2: number
          v3: number
          s1: number
          s2: number
          s3: number
        }[]
      }>,
      default: () => ({
        id: '', // 设置默认值
        list: [],
        s3: 0,
      }),
    },
    height: {
      type: String,
      default: '2.4rem',
    },
    width: {
      type: String,
      default: '5rem',
    },
    barWidth: {
      type: Number,
      default: 6,
    },
  })

  const dingdanChartRef = ref()
  const series = ref([] as SeriesOption[])
  const options = ref({})

  let myChart: echarts.ECharts | null = null
  let resizeObserver: ResizeObserver | null = null

  watch(
    () => props.data,
    (data) => {
      console.log('dingdan: ', data)
      nextTick(() => {
        initChartsData(data)
      })
    },
    { deep: true, immediate: true },
  )

  onMounted(() => {
    // 使用 ResizeObserver 替代 window.resize
    resizeObserver = new ResizeObserver(() => {
      if (myChart) {
        myChart.resize()
      }
    })
    if (dingdanChartRef.value) {
      resizeObserver.observe(dingdanChartRef.value)
    }
  })

  onUnmounted(() => {
    if (myChart) {
      myChart.dispose()
      myChart = null
    }
    if (resizeObserver) {
      resizeObserver.disconnect()
      resizeObserver = null
    }
  })

  const initChartsData = (data: any) => {
    if (!dingdanChartRef.value) return

    if (myChart) {
      myChart.dispose()
    }

    myChart = echarts.init(dingdanChartRef.value)
    if (!myChart) return

    // 动态计算最大值
    let adjustedMax = 4
    if (data.s3 < 5) {
      adjustedMax = 4 // 极小值兜底处理
    } else if (data.s3 <= data.s3 * 1.2) {
      adjustedMax = Math.ceil((data.s3 * 1.2) / 10) * 10
    }
    // if (data.s3 < 5) {
    //   adjustedMax = 4 // 极小值兜底处理
    // } else if (data.s3 <= 100) {
    //   adjustedMax = Math.ceil(data.s3 / 100) * 100
    // } else if (100 < data.s3 && data.s3 <= 500) {
    //   adjustedMax = Math.ceil(data.s3 / 500) * 500
    // } else if (500 < data.s3 && data.s3 <= 1000) {
    //   adjustedMax = Math.ceil(data.s3 / 1000) * 1000
    // } else {
    //   adjustedMax = Math.ceil(data.s3 / 10000) * 10000
    // }
    series.value = [
      {
        data: data.list.map((item: any) => item.v2),
        type: 'bar',
        stack: 'a',
        name: '运输中',
        barWidth: props.barWidth,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 1, 1, [
            {
              offset: 0,
              color: '#0084FF', // 渐变色起点颜色
            },
            {
              offset: 1,
              color: 'rgba(0, 132, 255, 0.36)', // 渐变色终点颜色
            },
          ]),
        },
      },
      {
        data: data.list.map((item: any) => item.v1),
        type: 'bar',
        stack: 'a',
        name: '待发运',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 1, 1, [
            {
              offset: 0,
              color: '#00F954', // 渐变色起点颜色
            },
            {
              offset: 1,
              color: 'rgba(0,249,84,0.36)', // 渐变色终点颜色
            },
          ]),
        },
      },
      {
        data: data.list.map((item: any) => item.v3),
        type: 'bar',
        stack: 'a',
        name: '已交车',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(1, 0, 1, 1, [
            {
              offset: 0,
              color: '#7B2DFF', // 渐变色起点颜色
            },
            {
              offset: 1,
              color: 'rgba(123,45,255,0.36)', // 渐变色终点颜色
            },
          ]),
        },
      },
    ]
    options.value = {
      grid: {
        top: '18%',
        left: '5%',
        right: '5%',
        bottom: '10%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
        confine: true, // 防止超出容器
        backgroundColor: 'rgba(13, 64, 71, 1)',
        borderColor: 'rgba(143, 225, 252, 0.60)',
        padding: 8,
        textStyle: {
          color: '#fff',
        },
        formatter: function (params: any) {
          var relVal = params[0].name + (data.queryType == 'month' ? '日' : data.queryType == 'year' ? '' : '')
          for (var i = 0, l = params.length; i < l; i++) {
            relVal += '<br/>' + params[i].marker + params[i].seriesName + '：' + (params[i].value || params[i].value == 0 ? params[i].value + data.unit : ' -')
          }
          return relVal
        },
      },
      legend: {
        right: '20px',
        top: '5px',
        padding: [0, 0, 0, 0],
        data: ['运输中', '待发运', '已交车'],
        textStyle: {
          color: '#999',
        },
        itemWidth: 16,
        itemHeight: 10,
      },
      xAxis: {
        type: 'category',
        data: data.list.map((item: any) => item.name),
        splitLine: {
          show: false,
        },
        axisPointer: {
          type: 'shadow',
        },
      },
      yAxis: [
        {
          type: 'value',
          name: data.unit,
          nameTextStyle: {
            // 设置名称样式
            color: '#999', // 文字颜色
            fontStyle: 'normal', // 字体风格
            fontWeight: 'bold', // 字体粗细
            fontSize: '0.14rem', // 字体大小
            padding: [0, 0, -4, -30], // 文字与轴线之间的距离
          },
          min: 0,
          max: Math.round(adjustedMax),
          interval: Math.round(adjustedMax / 4),
          // max: data.s3 && data.s3 > 4 ? Math.round(data.s3 * 1.5) : 4,
          // interval: Math.round((data.s3 * 1.5) / 4) && Math.round((data.s3 * 1.5) / 4) > 4 ? Math.round((data.s3 * 1.5) / 4) : 1, //平均值
          splitNumber: 4, //平均分几份
          axisLabel: {
            formatter: '{value} ',
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.15)',
              type: 'dashed', // dashed 虚线  dotted 实线
            },
          },
          axisPointer: {
            show: false,
          },
        },
      ],
      series: series.value,
    }
    myChart.setOption(options.value)
  }
</script>

<style scoped></style>
