<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-03-19 17:16:45
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-21 19:50:22
 * @Description: 顶部圆形统计
-->
<template>
  <div class="pie-chart-box">
    <div class="radio-group-box">
      <el-radio-group v-model="tabPosition" style="margin: 0 0 0.1rem 0.15rem" fill="rgba(0,132,255,0.51)" text-color="#fff" size="small" @change="changeTab">
        <el-radio-button value="year">本年</el-radio-button>
        <el-radio-button value="month">本月</el-radio-button>
        <el-radio-button value="day">本日</el-radio-button>
      </el-radio-group>
    </div>
    <el-row :gutter="20">
      <el-col :span="4" v-for="(item, index) in props.data" :key="index">
        <div style="display: flex; align-items: center; justify-content: center">
          <PieChart :data="item" :height="props.height" :width="props.height" />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, watch, nextTick, onUnmounted } from 'vue'
  import PieChart from './PieChart.vue'

  // 添加防抖函数
  const debounce = (fn: Function, delay: number) => {
    let timer: number | null = null
    return function (this: any, ...args: any[]) {
      if (timer) clearTimeout(timer)
      timer = window.setTimeout(() => {
        fn.apply(this, args)
      }, delay)
    }
  }

  const props = defineProps({
    data: {
      type: Object as any,
      default: () => {},
    },
    height: {
      type: String,
      default: '1.3rem',
    },
  })
  const dataInfo = reactive({
    legends: [] as any,
    counts: [] as any,
    ratio: {} as any,
    inventory: {} as any,
    age: {} as any,
  })
  const tabPosition = ref('year')
  const $emits = defineEmits<{
    (e: 'topChangeTab', modelValue: string): void
  }>()
  let resizeTimer: number | null = null

  watch(
    () => props.data,
    (val) => {
      dataInfo.legends = val.legends
      dataInfo.counts = val.counts
      dataInfo.ratio = val.ratio
      dataInfo.inventory = val.inventory
      dataInfo.age = val.age
    },
  )

  onMounted(() => {
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }
  })

  const handleResize = () => {
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }
    resizeTimer = window.setTimeout(() => {
      nextTick(() => {
        // 触发图表重新渲染
        if (props.data) {
          dataInfo.legends = [...props.data.legends]
          dataInfo.counts = [...props.data.counts]
          dataInfo.ratio = { ...props.data.ratio }
          dataInfo.inventory = { ...props.data.inventory }
          dataInfo.age = { ...props.data.age }
        }
      })
    }, 50)
  }

  const changeTab = (val: any) => {
    $emits('topChangeTab', val)
  }
</script>

<style lang="scss" scoped>
  .pie-chart-box {
    width: 100%;
    height: 100%;
    margin-top: 10px;
    // border-radius: 6px;
    // border: 1px solid #1b6bd2;
    position: relative;
  }
  .radio-group-box {
    position: absolute;
    top: -30px;
    left: 0px;
  }

  .radio-group-box :deep(.el-radio-button__inner) {
    background: transparent;
    border-color: rgba(55, 183, 255, 0.2);
  }
</style>
