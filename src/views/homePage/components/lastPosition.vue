<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-03-19 17:16:45
 * @LastEditors: llm
 * @LastEditTime: 2025-05-12 18:21:44
 * @Description: 左中右
-->
<template>
  <div class="contentBox">
    <el-row :gutter="20">
      <el-col :span="6">
        <div class="leftInfo">
          <div class="leftInfoTop">
            <!-- 常用功能 -->
            <div class="titleStyle">
              <div class="flex flex-column items-center justify-between">
                <img class="leftImg" src="../assets/changyonggongneng.png" alt="" />
              </div>
              <div class="baseBox">
                <el-row :gutter="20" type="flex" justify="center" align="middle">
                  <el-col :span="8" v-for="(item, index) in props.commonFunctionList" :key="index">
                    <div class="itemStyle" @click="commonFunctionClick(item)">
                      <img class="value" :src="item.icon" alt="" />
                      <p class="text">{{ item.title }}</p>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
          <!-- 左上 -->
          <div class="leftInfoCenter" v-if="JSON.stringify(leftTopData) != '{}'">
            <div class="titleStyle">
              <div class="flex flex-column items-center justify-between">
                <img class="leftImg" :src="leftTopData.icon" alt="" />
                <img class="rightImg" src="../assets/fangda.png" alt="" @click="showBigScreenInfo(leftTopData)" />
              </div>
              <div class="barClass">
                <div class="radio-group-box">
                  <el-radio-group
                    v-model="leftTopData.queryType"
                    style="padding: 0.15rem 0 0 0.1rem"
                    fill="rgba(0,132,255,0.51)"
                    text-color="#fff"
                    size="small"
                    @change="changeTab($event, leftTopData, '1')"
                  >
                    <el-radio-button value="year">年度</el-radio-button>
                    <el-radio-button value="month">月度</el-radio-button>
                  </el-radio-group>
                </div>
                <StackedBarChart v-if="leftTopData.id == 1" :data="leftTopData" :height="props.height" width="100%" />
                <StackedBarChart1 v-if="leftTopData.id == 2" :data="leftTopData" :height="props.height" width="100%" />
                <BarChart1 v-if="leftTopData.id == 6" :data="leftTopData" :height="props.height" width="100%" />
                <BarChart2 v-if="leftTopData.id == 5" :data="leftTopData" :height="props.height" width="100%" />
                <LineChart v-if="leftTopData.id == 7" :data="leftTopData" :height="props.height" width="100%" />
                <BarGraphChart1 v-if="leftTopData.id == 3" :data="leftTopData" :height="props.height" width="100%" />
                <BarGraphChart v-if="leftTopData.id == 4" :data="leftTopData" :height="props.height" width="100%" />
              </div>
            </div>
          </div>
          <!-- 左下 -->
          <div class="leftInfoBottom" v-if="JSON.stringify(leftBottomData) != '{}'">
            <div class="titleStyle">
              <div class="flex flex-column items-center justify-between">
                <img class="leftImg" :src="leftBottomData.icon" alt="" />
                <img class="rightImg" src="../assets/fangda.png" alt="" @click="showBigScreenInfo(leftBottomData)" />
              </div>
              <div class="barClass">
                <div class="radio-group-box">
                  <el-radio-group
                    v-model="leftBottomData.queryType"
                    style="padding: 0.15rem 0 0 0.1rem"
                    fill="rgba(0,132,255,0.51)"
                    text-color="#fff"
                    size="small"
                    @change="changeTab($event, leftBottomData, '2')"
                  >
                    <el-radio-button value="year">年度</el-radio-button>
                    <el-radio-button value="month">月度</el-radio-button>
                  </el-radio-group>
                </div>
                <StackedBarChart v-if="leftBottomData.id == 1" :data="leftBottomData" :height="props.height" width="100%" />
                <StackedBarChart1 v-if="leftBottomData.id == 2" :data="leftBottomData" :height="props.height" width="100%" />
                <BarChart1 v-if="leftBottomData.id == 6" :data="leftBottomData" :height="props.height" width="100%" />
                <BarChart2 v-if="leftBottomData.id == 5" :data="leftBottomData" :height="props.height" width="100%" />
                <LineChart v-if="leftBottomData.id == 7" :data="leftBottomData" :height="props.height" width="100%" />
                <BarGraphChart1 v-if="leftBottomData.id == 3" :data="leftBottomData" :height="props.height" width="100%" />
                <BarGraphChart v-if="leftBottomData.id == 4" :data="leftBottomData" :height="props.height" width="100%" />
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="centerInfo">
          <CenterMap />
        </div>
      </el-col>
      <el-col :span="6">
        <div class="rightInfo">
          <div class="rightInfoTop">
            <!-- 代办事项 -->
            <div class="titleStyle">
              <div class="flex flex-column items-center justify-between">
                <img class="leftImg" src="../assets/daibanshixiang.png" alt="" />
              </div>
              <div class="todoListBox">
                <el-row :gutter="20" type="flex" justify="center" align="middle">
                  <el-col :span="12" v-for="(item, index) in props.toDoListDataList" :key="index">
                    <el-popover
                      v-if="item.children && item.children.length > 0"
                      placement="left"
                      :width="400"
                      effect="dark"
                      trigger="hover"
                      :popper-style="{
                        padding: '0px',
                        border: 'none',
                      }"
                    >
                      <template #reference>
                        <div class="itemStyle">
                          <p class="value">{{ item.value }}</p>
                          <p class="text">{{ item.name }}</p>
                        </div>
                      </template>
                      <div :class="item.id === 1 ? 'todoListContent active1' : 'todoListContent active2'">
                        <el-row :gutter="20" type="flex" justify="center" align="middle">
                          <el-col :span="item.id === 1 ? 12 : 6" v-for="(event, index) in item.children" :key="index">
                            <div class="itemStyle" @click="toDoListClick(event)">
                              <el-badge
                                value="+"
                                v-if="event.value > 99 && item.id === 4"
                                :badge-style="{ width: '12px', height: '12px', fontSize: '14px', padding: '0 0 3px 0px' }"
                                :offset="[8, 8]"
                              >
                                <p class="value">{{ event.value }}</p>
                              </el-badge>
                              <p class="value" style="font-family: jdFont, serif" v-else>{{ event.value }}</p>
                              <p class="text">{{ event.name }}</p>
                            </div>
                          </el-col>
                        </el-row>
                      </div>
                    </el-popover>
                    <div v-else class="itemStyle" @click="toDoListClick(item)">
                      <p class="value">{{ item.value }}</p>
                      <p class="text">{{ item.name }}</p>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>

          <!-- 右上 -->
          <div class="rightInfoCenter" v-if="JSON.stringify(rightTopData) != '{}'">
            <div class="titleStyle">
              <div class="flex flex-column items-center justify-between">
                <img class="leftImg" :src="rightTopData.icon" alt="" />
                <img class="rightImg" src="../assets/fangda.png" alt="" @click="showBigScreenInfo(rightTopData)" />
              </div>
              <div class="barClass">
                <div class="radio-group-box">
                  <el-radio-group
                    v-model="rightTopData.queryType"
                    style="padding: 0.15rem 0 0 0.1rem"
                    fill="rgba(0,132,255,0.51)"
                    text-color="#fff"
                    size="small"
                    @change="changeTab($event, rightTopData, '3')"
                  >
                    <el-radio-button value="year">年度</el-radio-button>
                    <el-radio-button value="month">月度</el-radio-button>
                  </el-radio-group>
                </div>
                <StackedBarChart v-if="rightTopData.id == 1" :data="rightTopData" :height="props.height" width="100%" />
                <StackedBarChart1 v-if="rightTopData.id == 2" :data="rightTopData" :height="props.height" width="100%" />
                <BarChart1 v-if="rightTopData.id == 6" :data="rightTopData" :height="props.height" width="100%" />
                <BarChart2 v-if="rightTopData.id == 5" :data="rightTopData" :height="props.height" width="100%" />
                <LineChart v-if="rightTopData.id == 7" :data="rightTopData" :height="props.height" width="100%" />
                <BarGraphChart1 v-if="rightTopData.id == 3" :data="rightTopData" :height="props.height" width="100%" />
                <BarGraphChart v-if="rightTopData.id == 4" :data="rightTopData" :height="props.height" width="100%" />
              </div>
            </div>
          </div>

          <!-- 右下 -->
          <div class="rightInfoBottom" v-if="JSON.stringify(rightBottomData) != '{}'">
            <div class="titleStyle">
              <div class="flex flex-column items-center justify-between">
                <img class="leftImg" :src="rightBottomData.icon" alt="" />
                <img class="rightImg" src="../assets/fangda.png" alt="" @click="showBigScreenInfo(rightBottomData)" />
              </div>
              <div class="barClass">
                <div class="radio-group-box">
                  <el-radio-group
                    v-model="rightBottomData.queryType"
                    style="padding: 0.15rem 0 0 0.1rem"
                    fill="rgba(0,132,255,0.51)"
                    text-color="#fff"
                    size="small"
                    @change="changeTab($event, rightBottomData, '4')"
                  >
                    <el-radio-button value="year">年度</el-radio-button>
                    <el-radio-button value="month">月度</el-radio-button>
                  </el-radio-group>
                </div>
                <StackedBarChart v-if="rightBottomData.id == 1" :data="rightBottomData" :height="props.height" width="100%" />
                <StackedBarChart1 v-if="rightBottomData.id == 2" :data="rightBottomData" :height="props.height" width="100%" />
                <BarChart1 v-if="rightBottomData.id == 6" :data="rightBottomData" :height="props.height" width="100%" />
                <BarChart2 v-if="rightBottomData.id == 5" :data="rightBottomData" :height="props.height" width="100%" />
                <LineChart v-if="rightBottomData.id == 7" :data="rightBottomData" :height="props.height" width="100%" />
                <BarGraphChart1 v-if="rightBottomData.id == 3" :data="rightBottomData" :height="props.height" width="100%" />
                <BarGraphChart v-if="rightBottomData.id == 4" :data="rightBottomData" :height="props.height" width="100%" />
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 放大图例 -->
    <el-dialog v-model="isShowBigScreenContent" :close-on-click-modal="false" :before-close="bigScreenClose" draggable>
      <div class="bigScreenInfo" v-if="JSON.stringify(newChartData) != '{}'">
        <div class="titleStyle">
          <div class="flex flex-column items-center justify-between">
            <img class="title" :src="newChartData.icon" alt="" />
          </div>
          <div class="bigChartsClass">
            <div class="radio-group-box">
              <el-radio-group
                v-model="newChartData.queryType"
                style="padding: 0.15rem 0 0 0.1rem"
                fill="rgba(0,132,255,0.51)"
                text-color="#fff"
                size="small"
                @change="changeTab($event, newChartData, '5')"
              >
                <el-radio-button value="year">年度</el-radio-button>
                <el-radio-button value="month">月度</el-radio-button>
              </el-radio-group>
            </div>
            <StackedBarChart v-if="newChartData.id == 1" :data="newChartData" height="38vh" width="100%" :barWidth="Number(12)" />
            <StackedBarChart1 v-if="newChartData.id == 2" :data="newChartData" height="38vh" width="100%" :barWidth="Number(12)" />
            <BarChart1 v-if="newChartData.id == 6" :data="newChartData" height="38vh" width="100%" :barWidth="Number(12)" />
            <BarChart2 v-if="newChartData.id == 5" :data="newChartData" height="38vh" width="100%" :barWidth="Number(12)" />
            <LineChart v-if="newChartData.id == 7" :data="newChartData" height="38vh" width="100%" />
            <BarGraphChart1 v-if="newChartData.id == 3" :data="newChartData" height="38vh" width="100%" :barWidth="Number(12)" />
            <BarGraphChart v-if="newChartData.id == 4" :data="newChartData" height="38vh" width="100%" :barWidth="Number(12)" />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import BarChart1 from './BarChart1.vue' //维修金额
  import BarChart2 from './BarChart2.vue' //客户扣款
  import StackedBarChart from './StackedBarChart.vue' //订单数
  import StackedBarChart1 from './StackedBarChart1.vue' //发运量
  import BarGraphChart from './BarGraphChart.vue' //质损数
  import BarGraphChart1 from './BarGraphChart1.vue' //总里程数
  import LineChart from './LineChart.vue' //营业收入
  import CenterMap from './mapCenter/CenterMap.vue' //地图
  import router from '@/router'
  import { useFormStore } from '@/store/modules/form'
  import { getDataSingleInfoApi } from '@/api/bigScreen/index.js'

  const formStore = useFormStore()
  const isShowBigScreenContent = ref(false) //放大图例
  const props = defineProps({
    // 待办事项列表
    toDoListDataList: {
      type: Array as any,
      default: () => [],
    },
    // 常用功能列表
    commonFunctionList: {
      type: Array as any,
      default: () => [],
    },
    // 默认数据
    defaultDataInfo: {
      type: Object as any,
      default: () => {},
    },
    height: {
      type: String,
      default: '2.1rem',
    },
  })

  const leftTopData = ref({} as any) // 左上数据
  const leftBottomData = ref({} as any) // 左下数据
  const rightTopData = ref({} as any) // 右上侧数据
  const rightBottomData = ref({} as any) // 右下侧数据
  const newChartData = ref({} as any) // 放大图例数据

  watch(
    () => props.defaultDataInfo,
    (data) => {
      leftTopData.value = data.leftOne
      leftBottomData.value = data.leftTwo
      rightTopData.value = data.rightOne
      rightBottomData.value = data.rightTwo
    },
    {
      deep: true,
      // immediate: true,
    },
  )

  let resizeTimer: number | null = null
  const handleResize = () => {
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }
    resizeTimer = window.setTimeout(() => {
      nextTick(() => {
        // 触发图表重新渲染
        if (props.defaultDataInfo) {
          leftTopData.value = props.defaultDataInfo.leftOne
          leftBottomData.value = props.defaultDataInfo.leftTwo
          rightTopData.value = props.defaultDataInfo.rightOne
          rightBottomData.value = props.defaultDataInfo.rightTwo
        }
      })
    }, 100)
  }

  onMounted(() => {
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }
  })

  // 放大查看大屏信息
  const showBigScreenInfo = (item: any) => {
    newChartData.value = JSON.parse(JSON.stringify(item))
    isShowBigScreenContent.value = true
  }

  // 关闭大屏信息
  const bigScreenClose = () => {
    isShowBigScreenContent.value = false
  }

  // 切换年月
  const changeTab = (val: any, item: any, type: string) => {
    getOtherChartsDataInfo(val, item.id, type)
  }

  // 常用功能点击跳转
  const commonFunctionClick = (item: any) => {
    //不刷新查询条件
    formStore.setIsRefreshTopQueryParams(false)
    // 跳转页面
    router.push({
      path: item.redirect,
      query: {
        time: new Date().getTime(), //获取当前时间戳,防止不刷新页面
      },
    })
  }

  // 代办事项点击跳转
  const toDoListClick = (item: any) => {
    //不刷新查询条件
    formStore.setIsRefreshTopQueryParams(false)
    if (item.redirect) {
      let queryParams = item.redirect.split('?')[1]
      if (queryParams) {
        let newParams = queryParams.split('&')
        let obj = {} as any
        newParams.forEach((item: any) => {
          let key = item.split('=')[0]
          let value = item.split('=')[1]
          obj[key] = value
        })
        // 跳转页面
        router.push({
          path: item.redirect,
          query: obj,
        })
      } else {
        // 跳转页面
        router.push({
          path: item.redirect,
          query: {
            time: new Date().getTime(), //获取当前时间戳,防止不刷新页面
          },
        })
      }
    }
  }

  // 切换后的数据
  const getOtherChartsDataInfo = (timeType: any, id: any, type: string) => {
    getDataSingleInfoApi({
      id: id,
      queryType: timeType,
    })
      .then((res: any) => {
        const { data } = res
        switch (type) {
          case '1':
            leftTopData.value = data
            break
          case '2':
            leftBottomData.value = data
            break
          case '3':
            rightTopData.value = data
            break
          case '4':
            rightBottomData.value = data
            break
          case '5':
            newChartData.value = JSON.parse(JSON.stringify(data))
            break
          default:
        }
      })
      .catch((err: any) => {})
  }
</script>

<style scoped>
  .contentBox {
    width: 100%;
    padding: 0 10px 10px;
  }
  .leftInfo {
    width: 100%;
    height: 100%;
  }
  .centerInfo {
    width: 100%;
    /* height: 100%; */
    height: 9.16rem;
    border-radius: 6px;
  }
  .rightInfo {
    width: 100%;
    height: 100%;
  }
  .leftInfo .leftInfoTop,
  .leftInfo .leftInfoCenter,
  .leftInfo .leftInfoBottom,
  .rightInfo .rightInfoTop,
  .rightInfo.rightInfoCenter,
  .rightInfo.rightInfoBottom {
    width: 100%;
    /* height: 2.5rem; */
  }

  .titleStyle .leftImg {
    width: 70%;
    height: 0.52rem;
  }

  .titleStyle .rightImg {
    width: 0.2rem;
    height: 0.2rem;
    cursor: pointer;
  }

  .baseBox {
    width: 100%;
    height: 2.4rem;
    background: url('../assets/changyonggongnengbg.png') no-repeat center;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .baseBox .itemStyle {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 1.1rem;
  }

  .baseBox .itemStyle .value {
    width: 0.6rem;
    height: 0.4rem;
  }

  .baseBox .itemStyle .text {
    font-size: 0.16rem;
    color: #ffffff;
    margin-top: 0.04rem;
  }

  .todoListBox {
    width: 100%;
    height: 2.4rem;
    background: url('../assets/daibanshixiangbg.png') no-repeat center;
    background-size: 100% 100%;
    padding: 0 5%;
  }

  .todoListBox .itemStyle {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 1.2rem;
  }

  .todoListBox .itemStyle .value {
    font-size: 0.24rem;
    font-weight: bold;
    color: #ffffff;
    font-family: jdFont, serif;
  }

  .todoListBox .itemStyle .text {
    font-size: 0.18rem;
    color: #ffffff;
  }

  .todoListContent {
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    padding: 0 5%;
  }

  .active1 {
    background: url('../assets/shenpizu.png') no-repeat center;
    background-size: 100% 100%;
  }
  .active2 {
    background: url('../assets/feiyongzu.png') no-repeat center;
    background-size: 100% 100%;
  }

  .todoListContent .itemStyle {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0.15rem 0;
  }
  .todoListContent .itemStyle .value {
    font-size: 0.24rem;
    font-weight: bold;
    color: #ffffff;
  }

  .todoListContent .itemStyle .text {
    font-size: 0.14rem;
    color: #ffffff;
  }

  .barClass {
    background: url('../assets/changyonggongnengbg.png') no-repeat center;
    background-size: 100% 100%;
    height: 2.6rem;
    width: 100%;
  }

  .barClass :deep(.el-radio-button__inner) {
    background: transparent;
    border-color: rgba(55, 183, 255, 0.2);
  }

  /* 放大展示弹窗 */
  :deep(.el-dialog) {
    height: 55vh;
    background-color: #06182a !important;
  }
  :deep(.el-dialog__header .el-dialog__close) {
    color: #ffffff !important;
  }
  .bigScreenInfo .bigContent {
    width: 100%;
    height: 100%;
  }
  .bigChartsClass {
    background: url('../assets/changyonggongnengbg.png') no-repeat center;
    background-size: 100% 100%;
    height: 45vh;
    width: 100%;
  }
  .titleStyle .title {
    width: auto;
    height: 0.6rem;
  }

  .bigChartsClass .radio-group-box {
    padding: 20px 0 0 20px;
  }

  .radio-group-box :deep(.el-radio-button__inner) {
    background: transparent;
    border-color: rgba(55, 183, 255, 0.2);
  }
</style>
