<!--
 * @Author: llm
 * @Date: 2024-11-28 15:10:50
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-21 20:04:06
 * @Description: 运力雷达
-->
<template>
  <div class="map-container">
    <div class="map-container-header">
      <div class="map-container-header-top flex items-center justify-around">
        <div class="map-container-header-left w-110px mr-2"></div>
        <div class="map-container-header-center flex flex-col items-center">
          <div class="map-container-header-center-top flex items-center">
            <img src="@/views/homePage/assets/map_icon_1.png" alt="" class="w-19px h-54px mr-6px" />
            <div class="map-container-header-center-top-text">
              <span class="text-20px font-bold text-white mb-4px" style="font-family: jdFont, serif; font-size: 0.23rem">{{
                state.summaryData.totalVehicleCount || 0
              }}</span>
              <span class="text-11px text-white ml-4px mb-2px">车辆总数</span>
            </div>
            <div class="map-container-header-center-top-text ml-10px">
              <span class="text-20px font-bold text-#FF0000 mb-4px" style="font-family: jdFont, serif; font-size: 0.23rem">{{
                state.summaryData.abnormalVehicleCount || 0
              }}</span>
              <span class="text-11px text-white ml-4px mb-2px">在途异常</span>
            </div>
            <img src="@/views/homePage/assets/map_icon_2.png" alt="" class="w-19px h-54px ml-6px" />
          </div>
        </div>
        <div class="map-container-header-right ml-2" @click="goDetail">详情</div>
      </div>
      <div class="map-container-header-center-bottom">
        <div class="map-container-header-center-bottom-text text-center">
          <span class="text-18px text-#D8F0FF" style="font-size: 0.2rem">累计总订单量</span>
        </div>
        <div class="map-container-header-center-bottom-count text-#D8F0FF">{{ state.summaryData.totalVinCount || 0 }}</div>
      </div>
    </div>
    <!-- 地图 -->
    <baidu-map class="bm-view" ref="mapContainer" :zoom="5" :scroll-wheel-zoom="true" :center="{ lng: 108.93, lat: 34.34 }" :map-click="false" @init="handler">
      <bml-marker-clusterer :averageCenter="true" v-if="state.markers.length > 0">
        <bm-marker
          v-for="(marker, index) in state.markers"
          :icon="{ url: marker.icon, size: { width: 20, height: 32 }, opts: { imageSize: { width: 20, height: 32 } } }"
          :position="{ lng: parseFloat(marker.bd_currentCoordinate.bd_lng), lat: parseFloat(marker.bd_currentCoordinate.bd_lat) }"
          @click="shouye ? infoWindowOpen(marker, index) : false"
        >
        </bm-marker>
      </bml-marker-clusterer>
      <!-- <bm-polygon v-for="(item, index) in state.polygonFenceList" :key="index" :path="item" stroke-color="blue"
        :stroke-opacity="0.5" :stroke-weight="2" :editing="false" />
      <bm-circle v-for="(item, index) in state.pointFenceList" :key="index" :center="{ lng: item.lng, lat: item.lat }"
        :radius="item.radius" stroke-color="blue" :stroke-opacity="0.5" :stroke-weight="2" :editing="false"></bm-circle> -->
    </baidu-map>
  </div>

  <!-- windowinfo展示 -->
  <MapWindowInfo
    :dataInfo="state.currentInfo"
    :isShowHide="state.showInfoWindow"
    :popupPixel="state.popupPixel"
    @closeInformation="closeInformation"
  ></MapWindowInfo>
</template>
<script setup>
  import {
    getFleetTransportCapacitySummaryQueryApi,
    getFleetTransportCapacityAllVehicleApi,
    getFleetTransportCapacityFenceQueryApi,
    getVehicleDispatchInfoApi,
  } from '@/api/transitManagement/transportRadar.js'
  import router from '@/router'
  import { bd_encrypt, gcj02tobd09, setBdMapViewPort } from '@/utils/index'
  import { useFormStore } from '@/store/modules/form'
  import MapWindowInfo from '@/components/MapWindowInfo/index.vue'
  import { darkStyleJson } from './themeStyle'
  const formStore = useFormStore()

  defineOptions({
    name: 'OutboundTransportInTransitTransportRadar',
    inheritAttrs: false,
  })

  const mapContainer = ref(null)
  const state = reactive({
    map: null,
    BMap: null,
    summaryData: {},
    hideRightArea: true,
    searchValue: '',
    selectValue: '1',
    polygonFenceList: [],
    pointFenceList: [],
    markers: [],
    mapLoading: false,
    showInfoWindow: false,
    moreVehicleVisible: false,
    infoWindow: null, // 信息窗口
    circleObj: null,
    newPath: null,
    currentInfo: {},
    popupPixel: {},
  })

  const shouye = ref(false)

  const handler = ({ BMap, map }) => {
    state.map = map
    state.BMap = BMap
    // // 设置深色主题（使用内置的暗色样式）
    // map.setMapStyle({ style: 'dark' })
    // 应用自定义深色样式
    map.getContainer().style.backgroundColor = '#091934'
    map.setMapStyle({ styleJson: darkStyleJson })
    drawBoundary()
    // 设置中心点坐标(经纬度)
    setTimeout(() => {
      var point = new BMap.Point(108.93, 34.34)
      map.centerAndZoom(point, 5)
    }, 500)
    searchAllVehicle()

    // // 监听地图移动事件
    // map.addEventListener('moveend', addMarkerTip)
    // // 监听地图缩放事件
    // map.addEventListener('zoomend', addMarkerTip)
  }
  // 窗口大小变化时重新调整
  window.addEventListener('resize', function () {
    adjustMapView()
  })
  onMounted(() => {
    getFleetTransportCapacitySummaryQuery()

    // 自定义关闭按钮的功能
    window.closeInfoWindow = () => {
      if (state.map && state.infoWindow) {
        if (state.infoWindow.isOpen()) {
          state.map.closeInfoWindow()
        }
      }
    }
  })
  const getSummaries = (param) => {
    const { columns, data } = param
    const sums = []

    columns.forEach((column, index) => {
      if (index === 0) {
        sums[index] = '合计'
        return
      }
      const values = data.map((item) => Number(item[column.property]))
      if (!values.every((value) => isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr)
          if (!isNaN(value)) {
            return prev + curr
          } else {
            return prev
          }
        }, 0)}${
          index === 1
            ? ''
            : `（${data.reduce((prev, curr) => {
                const vinValue = Number(curr[`${column.property.replace('VehicleCount', 'VinCount')}`])
                if (!isNaN(vinValue)) {
                  return prev + vinValue
                } else {
                  return prev
                }
              }, 0)}）`
        } `
      } else {
        sums[index] = ''
      }
    })

    return sums
  }
  const viewAddress = (row, key) => {
    handleSearch(row[key], 'clearSearchValue')
    state.moreVehicleVisible = false
  }
  const infoWindowOpen = (marker, index) => {
    let params = {
      vehicleId: state.markers[index].vehicleWholeId,
      vehicleNo: state.markers[index].vehicleNo,
    }
    // 获取信息
    getVehicleDispatchInfoApi(params)
      .then((res) => {
        if (res.code === 200) {
          state.currentInfo = res.data
          state.currentInfo.lng = state.markers[index].lng
          state.currentInfo.lat = state.markers[index].lat
          // addMarkerTip()
          state.showInfoWindow = true
        } else {
          state.currentInfo = {}
          state.showInfoWindow = false
        }
      })
      .catch((err) => {})
  }
  // 添加信息窗口
  function addMarkerTip() {
    if (JSON.stringify(state.currentInfo) != '{}') {
      // 获取地图容器的 DOM 元素
      const mapContainerDom = document.querySelector('.bm-view')
      // 获取地图容器的位置和大小
      const mapRect = mapContainerDom.getBoundingClientRect()
      // 获取标记点的经纬度
      const position = new state.BMap.Point(parseFloat(state.currentInfo.lng), parseFloat(state.currentInfo.lat))
      // 将经纬度转换为像素坐标
      const pixel = state.map.pointToPixel(position)
      // 计算弹窗的位置
      // state.popupLeft = mapRect.left + pixel.x
      // state.popupTop = mapRect.top + pixel.y

      // 计算水平居中的偏移量
      const offsetX = 450
      // 计算垂直偏移量，让弹窗显示在 marker 点下方
      const offsetY = 12

      // 计算弹窗的位置
      state.popupPixel = {
        x: mapRect.left + pixel.x - offsetX,
        y: mapRect.top + pixel.y + offsetY,
      }

      // state.popupPixel = {
      //   x: mapRect.left + pixel.x - 460,
      //   y: mapRect.top + pixel.y + 10,
      // }
    }

    // if (state.map && state.infoWindow) {
    //   if (state.infoWindow.isOpen()) {
    //     state.map.closeInfoWindow()
    //   }
    // }
    // var opts = {
    //   width: 260, // 信息窗口宽度
    //   enableCloseOnClick: false, // 点击地图时不关闭
    // }
    // let positionMarker = item.bd_currentCoordinate
    // var content = ''
    // //如果是车辆marker点
    // if (item.vehicleNo) {
    //   content = `
    //   <div class="statisticsInfo">
    //   <div class="customizeBox">
    //     <h4>车辆信息</h4>
    //     <button class="close-btn" onclick="closeInfoWindow()">X</button>
    //   </div>
    //   <div class="buttonBox">
    //   <el-tabs v-model="${state.newTypeName || 0}" class="tabChangeClass" type="border-card" @tab-change="handleChange">
    //     <el-tab-pane label="车辆信息" name="vehicle">
    //       <div class="vehicleInfo">
    //         <el-row>
    //           <el-col :span="12" class="colItem">
    //             <span class="lableText">承运商：</span>
    //             <span>${item.carrierName || '--'}</span>
    //           </el-col>
    //           <el-col :span="12" class="colItem">
    //             <span class="lableText">车牌号：</span>
    //             <span>${item.vehicleNo || '--'}</span>
    //           </el-col>
    //           <el-col :span="12" class="colItem">
    //             <span class="lableText">行驶里程：</span>
    //             <span>${item.navTotalMileage || '--'}</span>
    //           </el-col>
    //           <el-col :span="12" class="colItem">
    //             <span class="lableText">驾驶时长：</span>
    //             <span>${item.drivingTotalTime || '--'}</span>
    //           </el-col>
    //           <el-col :span="12" class="colItem">
    //             <span class="lableText">停车时长：</span>
    //             <span>${item.parkingTotalTime || '--'}</span>
    //           </el-col>
    //           <el-col :span="12" class="colItem">
    //             <span class="lableText">平均速度：</span>
    //             <span>${item.averageSpeed || '--'}</span>
    //           </el-col>
    //         </el-row>
    //       </div>
    //     </el-tab-pane>
    //   </el-tabs>
    // </div>
    // </div>
    //         `
    // } else {
    //   content = `
    //         <div class="titleBox">
    //           <h4>围栏信息</h4>
    //           <button class="close-btn" onclick="closeInfoWindow()">X</button>
    //         </div>
    //         <div class="contentBox">
    //           <span class="title">围栏名称: </span><span class="contentInfo">${item.name}</span><br>
    //           <span class="title">围栏地址: </span><span class="contentInfo">${item.address}</span><br>
    //         </div>
    //         `
    //   clearAllOverlays()
    //   if (item.radius) {
    //     circleData(item)
    //   } else {
    //     areasData(item)
    //   }
    // }
    // state.infoWindow = new BMap.InfoWindow(content, opts) // 创建信息窗口对象
    // var newPoint = new BMap.Point(positionMarker.bd_lng, positionMarker.bd_lat)
    // state.map.openInfoWindow(state.infoWindow, newPoint) //开启信息窗口
  }

  // 展开隐藏信息窗
  const closeInformation = (value) => {
    state.showInfoWindow = value
  }

  const clearAllOverlays = () => {
    // 清空所有圆形围栏
    if (state.circleObj) {
      state.map.removeOverlay(state.circleObj)
      state.circleObj = null
    }
    // 清空所有多边形围栏
    if (state.newPath && state.newPath.length > 0) {
      for (let i = 0; i < state.newPath.length; i++) {
        state.map.removeOverlay(state.newPath[i])
      }
    }
    //关闭窗体
    if (state.infoWindow && state.infoWindow.isOpen()) {
      state.map.closeInfoWindow()
    }
  }
  const getFleetTransportCapacitySummaryQuery = async () => {
    const { data } = await getFleetTransportCapacitySummaryQueryApi({})
    state.summaryData = data
  }
  const handleSearch = async (params, type) => {
    if (params) {
      clearAllOverlays()
      if (type && type === 'clearSearchValue') {
        state.searchValue = ''
      }
      if (state.selectValue === '1') {
        // 搜车牌号
        const data = await getFleetTransportCapacityAllVehicle({ vehicleNos: params })
        if (data.length === 0) {
          ElMessage.error('未查询到当前车辆')
        }
        state.markers = data
      } else {
        state.markers = []
        // 搜围栏
        const { data } = await getFleetTransportCapacityFenceQueryApi({ name: params })
        if (data.length === 0) {
          ElMessage.error('未查询到当前围栏')
        }
        state.markers = data.map((item) => {
          const bd_point = bd_encrypt(item.coordinate.split(',')[0], item.coordinate.split(',')[1])
          item.bd_currentCoordinate = {
            bd_lng: bd_point.bd_lng,
            bd_lat: bd_point.bd_lat,
          }
          item.icon = 'https://webapi.amap.com/theme/v1.3/markers/b/mark_bs.png'
          // 多边形围栏坐标转换
          if (item.quyu) {
            const quyu = item.quyu.split(';')
            if (quyu && quyu.length > 0) {
              item.quyu = quyu.map((qyItem) => {
                let quyuPoint = gcj02tobd09(qyItem.split(',')[0], qyItem.split(',')[1])
                return (qyItem = {
                  lng: quyuPoint[0],
                  lat: quyuPoint[1],
                })
              })
            }
          }
          return item
        })
        setBdMapViewPort(state.markers, 'coordinate', state.map, state.BMap)
      }
    }
  }
  // 回显圆形数据
  const circleData = (valueObj) => {
    if (JSON.stringify(valueObj) != '{}') {
      var newPoint = new BMap.Point(Number(valueObj.bd_currentCoordinate.bd_lng), Number(valueObj.bd_currentCoordinate.bd_lat))
      // this.circlePath.center = newPoint;
      // this.circlePath.radius = valueObj.radius;
      let options = {
        strokeColor: '#FF33FF',
        fillColor: '#1791fc',
        fillOpacity: 0.5, // 透明度
        strokeStyle: 'dashed',
        strokeWeight: 3,
        strokeOpacity: 1,
        enableEditing: false,
      }
      if (state.circleObj && state.map) {
        state.map.removeOverlay(state.circleObj)
      }
      state.circleObj = new BMap.Circle(newPoint, valueObj.radius, options) // 创建圆形覆盖物
      state.map.addOverlay(state.circleObj) // 添加到地图
      // 获取圆形覆盖物的视图边界
      var bounds = state.circleObj.getBounds()
      // 获取圆形的四个边界点：左上角 (southWest) 和右下角 (northEast)
      const sw = bounds.getSouthWest() // 左下角
      const ne = bounds.getNorthEast() // 右上角
      // 使用 setViewport 调整地图视野，确保圆形完全显示
      state.map.setViewport([sw, ne]) // 设置地图视野区域
    }
  }
  // 回显多边形
  const areasData = (data) => {
    let path = []
    if (data.quyu.length <= 0) {
      return
    }
    for (let index = 0; index < data.quyu.length; index++) {
      let currentPoint = new BMap.Point(Number(data.quyu[index].lng), Number(data.quyu[index].lat))
      path.push(currentPoint)
    }
    let options = {
      strokeColor: '#FF33FF',
      fillColor: '#1791fc',
      fillOpacity: 0.5, // 透明度
      strokeStyle: 'dashed',
      strokeWeight: 3,
      strokeOpacity: 1,
      enableEditing: false,
    }
    if (state.map) {
      state.map.removeOverlay(state.newPath)
    }
    state.newPath = new BMap.Polygon(path, options) // 创建多边形覆盖物
    state.map.addOverlay(state.newPath) // 添加到地图
    state.map.setViewport(path)
  }

  //查询所有在途车辆
  const searchAllVehicle = async () => {
    const params = {
      vehicleNos: undefined,
    }
    state.mapLoading = true
    state.markers = []
    state.searchValue = ''
    clearAllOverlays()
    const data = await getFleetTransportCapacityAllVehicle(params)
    state.markers = data
    // setBdMapViewPort(data, 'currentCoordinate', state.map, state.BMap)
    state.mapLoading = false
  }
  const getFleetTransportCapacityAllVehicle = async (params) => {
    const { data } = await getFleetTransportCapacityAllVehicleApi(params)
    const ownVehicles = data.filter((item) => item.currentCoordinate)
    ownVehicles.map((item) => {
      const bd_point = bd_encrypt(item.currentCoordinate.split(',')[0], item.currentCoordinate.split(',')[1])
      item.bd_currentCoordinate = {
        bd_lng: bd_point.bd_lng,
        bd_lat: bd_point.bd_lat,
      }
      item.icon = 'https://webapi.amap.com/theme/v1.3/markers/b/mark_bs.png'
    })
    return ownVehicles
  }
  const linkToOrder = (row) => {
    //不刷新查询条件
    formStore.setIsRefreshTopQueryParams(false)
    formStore.$patch((state) => {
      state.routerParams = {
        orderStatus: '1',
        childCompanyName: row.childCompanyName,
      }
    })
    router.push({
      path: '/outboundDispatchManagement/orderManagement',
      query: {
        time: new Date().getTime(), //获取当前时间戳,防止不刷新页面·
      },
    })
  }
  function drawBoundary() {
    /*画遮蔽层的相关方法
     *思路: 首先在中国地图最外画一圈，圈住理论上所有的中国领土，然后再将每个闭合区域合并进来，并全部连到西北角。
     *      这样就做出了一个经过多次西北角的闭合多边形*/
    //定义中国东南西北端点，作为第一层
    //向数组中添加一次闭合多边形，并将西北角再加一次作为之后画闭合区域的起点
    var pStart = new BMap.Point(180, 90)
    var pEnd = new BMap.Point(0, -90)
    var pArray = [
      new BMap.Point(pStart.lng, pStart.lat),
      new BMap.Point(pEnd.lng, pStart.lat),
      new BMap.Point(pEnd.lng, pEnd.lat),
      new BMap.Point(pStart.lng, pEnd.lat),
    ]
    //循环添加各闭合区域
    pArray.push(new BMap.Point(135.077218, 48.544352))
    pArray.push(new BMap.Point(134.92218, 48.584352))
    pArray.push(new BMap.Point(134.827218, 48.534352))
    pArray.push(new BMap.Point(134.727669, 48.495377))
    pArray.push(new BMap.Point(134.304531, 48.394091))
    pArray.push(new BMap.Point(133.513447, 48.177476))
    pArray.push(new BMap.Point(132.832747, 48.054205))
    pArray.push(new BMap.Point(132.519993, 47.789172))
    pArray.push(new BMap.Point(131.765704, 47.813962))
    pArray.push(new BMap.Point(131.103402, 47.776772))
    pArray.push(new BMap.Point(130.919429, 48.331824))
    pArray.push(new BMap.Point(130.77225, 48.868729))
    pArray.push(new BMap.Point(129.907577, 49.351849))
    pArray.push(new BMap.Point(128.73015, 49.699156))
    pArray.push(new BMap.Point(127.791888, 49.85404))
    pArray.push(new BMap.Point(127.791888, 50.492084))
    pArray.push(new BMap.Point(126.927215, 51.616759))
    pArray.push(new BMap.Point(126.467283, 52.579818))
    pArray.push(new BMap.Point(125.952158, 53.059077))
    pArray.push(new BMap.Point(124.701142, 53.313247))
    pArray.push(new BMap.Point(123.56051, 53.664362))
    pArray.push(new BMap.Point(121.555204, 53.46722))
    pArray.push(new BMap.Point(120.340983, 53.125528))
    pArray.push(new BMap.Point(119.95464, 52.579818))
    pArray.push(new BMap.Point(120.616942, 52.523746))
    pArray.push(new BMap.Point(120.506559, 52.095236))
    pArray.push(new BMap.Point(119.862653, 51.616759))
    pArray.push(new BMap.Point(119.365926, 50.959196))
    pArray.push(new BMap.Point(119.089967, 50.362806))
    pArray.push(new BMap.Point(119.108364, 50.05583))
    pArray.push(new BMap.Point(118.133307, 49.925357))
    pArray.push(new BMap.Point(117.471005, 49.794528))
    pArray.push(new BMap.Point(116.808702, 49.889712))
    pArray.push(new BMap.Point(116.385564, 49.758785))
    pArray.push(new BMap.Point(115.962426, 48.953617))
    pArray.push(new BMap.Point(115.520891, 48.147476))
    pArray.push(new BMap.Point(115.796851, 47.677465))
    pArray.push(new BMap.Point(116.27518, 47.652609))
    pArray.push(new BMap.Point(117.103059, 47.652609))
    pArray.push(new BMap.Point(118.004526, 47.801568))
    pArray.push(new BMap.Point(118.887596, 47.577968))
    pArray.push(new BMap.Point(119.402721, 47.127871))
    pArray.push(new BMap.Point(119.402721, 46.800397))
    pArray.push(new BMap.Point(118.464459, 46.825659))
    pArray.push(new BMap.Point(117.103059, 46.648575))
    pArray.push(new BMap.Point(115.980824, 46.088213))
    pArray.push(new BMap.Point(115.226534, 45.702829))
    pArray.push(new BMap.Point(114.159491, 45.275796))
    pArray.push(new BMap.Point(112.761297, 45.171782))
    pArray.push(new BMap.Point(111.639061, 45.132727))
    pArray.push(new BMap.Point(111.436691, 44.55683))
    pArray.push(new BMap.Point(111.51028, 44.001703))
    pArray.push(new BMap.Point(110.682402, 43.387647))
    pArray.push(new BMap.Point(108.897864, 42.658724))
    pArray.push(new BMap.Point(106.892559, 42.522781))
    pArray.push(new BMap.Point(103.82021, 42.140555))
    pArray.push(new BMap.Point(102.422016, 42.536389))
    pArray.push(new BMap.Point(101.336575, 42.82146))
    pArray.push(new BMap.Point(99.478448, 42.929712))
    pArray.push(new BMap.Point(97.601924, 42.997272))
    pArray.push(new BMap.Point(96.019756, 43.815487))
    pArray.push(new BMap.Point(92.72664, 45.288784))
    pArray.push(new BMap.Point(91.144473, 45.599605))
    pArray.push(new BMap.Point(91.457227, 46.483616))
    pArray.push(new BMap.Point(90.794924, 47.553064))
    pArray.push(new BMap.Point(89.562305, 48.221295))
    pArray.push(new BMap.Point(88.2377, 48.953617))
    pArray.push(new BMap.Point(87.722576, 49.279683))
    pArray.push(new BMap.Point(87.097067, 49.255604))
    pArray.push(new BMap.Point(86.60034, 49.122957))
    pArray.push(new BMap.Point(86.177203, 48.710696))
    pArray.push(new BMap.Point(85.533297, 48.344091))
    pArray.push(new BMap.Point(85.404516, 47.875888))
    pArray.push(new BMap.Point(85.349324, 47.390897))
    pArray.push(new BMap.Point(84.926186, 47.215692))
    pArray.push(new BMap.Point(83.233635, 47.315881))
    pArray.push(new BMap.Point(82.865689, 47.328391))
    pArray.push(new BMap.Point(82.258578, 45.844449))
    pArray.push(new BMap.Point(82.368962, 45.366651))
    pArray.push(new BMap.Point(82.093003, 45.30177))
    pArray.push(new BMap.Point(80.989165, 45.275796))
    pArray.push(new BMap.Point(79.903724, 45.015402))
    pArray.push(new BMap.Point(80.326862, 44.332772))
    pArray.push(new BMap.Point(80.510835, 43.642047))
    pArray.push(new BMap.Point(80.621219, 43.186043))
    pArray.push(new BMap.Point(80.27167, 43.010775))
    pArray.push(new BMap.Point(79.885327, 42.304653))
    pArray.push(new BMap.Point(79.259819, 41.838593))
    pArray.push(new BMap.Point(78.487133, 41.576647))
    pArray.push(new BMap.Point(77.916816, 41.341363))
    pArray.push(new BMap.Point(77.272911, 41.16086))
    pArray.push(new BMap.Point(76.739389, 41.02167))
    pArray.push(new BMap.Point(76.26106, 40.546202))
    pArray.push(new BMap.Point(75.672346, 40.75639))
    pArray.push(new BMap.Point(74.881262, 40.630357))
    pArray.push(new BMap.Point(74.255754, 40.293095))
    pArray.push(new BMap.Point(73.777425, 39.939968))
    pArray.push(new BMap.Point(73.74063, 39.556517))
    pArray.push(new BMap.Point(73.53826, 39.34256))
    pArray.push(new BMap.Point(73.685438, 38.725549))
    pArray.push(new BMap.Point(74.034987, 38.407771))
    pArray.push(new BMap.Point(74.458125, 38.335352))
    pArray.push(new BMap.Point(74.734084, 38.074036))
    pArray.push(new BMap.Point(74.844468, 37.577865))
    pArray.push(new BMap.Point(74.678892, 37.21089))
    pArray.push(new BMap.Point(74.6237, 36.975076))
    pArray.push(new BMap.Point(75.414784, 36.501232))
    pArray.push(new BMap.Point(75.801127, 35.934721))
    pArray.push(new BMap.Point(76.518622, 35.379154))
    pArray.push(new BMap.Point(77.309706, 35.137703))
    pArray.push(new BMap.Point(77.972008, 34.758986))
    pArray.push(new BMap.Point(78.376749, 34.241106))
    pArray.push(new BMap.Point(78.523927, 33.473647))
    pArray.push(new BMap.Point(78.7079, 32.978834))
    pArray.push(new BMap.Point(78.450338, 32.745921))
    pArray.push(new BMap.Point(78.30316, 32.340745))
    pArray.push(new BMap.Point(78.431941, 32.04349))
    pArray.push(new BMap.Point(78.671106, 31.572152))
    pArray.push(new BMap.Point(78.855079, 31.145879))
    pArray.push(new BMap.Point(79.425395, 30.797108))
    pArray.push(new BMap.Point(80.087697, 30.447053))
    pArray.push(new BMap.Point(81.301919, 29.855455))
    pArray.push(new BMap.Point(81.90903, 30.0157))
    pArray.push(new BMap.Point(82.7921, 29.485907))
    pArray.push(new BMap.Point(84.539843, 28.661613))
    pArray.push(new BMap.Point(85.71727, 28.124721))
    pArray.push(new BMap.Point(86.821108, 27.732537))
    pArray.push(new BMap.Point(87.998535, 27.69979))
    pArray.push(new BMap.Point(88.568851, 27.716165))
    pArray.push(new BMap.Point(88.863208, 27.108656))
    pArray.push(new BMap.Point(89.580703, 27.190949))
    pArray.push(new BMap.Point(89.654292, 27.765274))
    pArray.push(new BMap.Point(90.923705, 27.650651))
    pArray.push(new BMap.Point(91.751584, 27.223849))
    pArray.push(new BMap.Point(92.04594, 26.778874))
    pArray.push(new BMap.Point(92.965805, 26.646689))
    pArray.push(new BMap.Point(93.830478, 26.960375))
    pArray.push(new BMap.Point(94.860727, 27.453873))
    pArray.push(new BMap.Point(96.185332, 27.798001))
    pArray.push(new BMap.Point(97.123594, 27.503101))
    pArray.push(new BMap.Point(97.620321, 27.896122))
    pArray.push(new BMap.Point(97.675513, 28.059457))
    pArray.push(new BMap.Point(98.080254, 27.306056))
    pArray.push(new BMap.Point(98.595378, 27.009824))
    pArray.push(new BMap.Point(98.393008, 26.066566))
    pArray.push(new BMap.Point(97.804294, 25.483523))
    pArray.push(new BMap.Point(97.528335, 24.847254))
    pArray.push(new BMap.Point(97.417951, 24.10637))
    pArray.push(new BMap.Point(97.804294, 23.717348))
    pArray.push(new BMap.Point(98.595378, 23.886634))
    pArray.push(new BMap.Point(98.834543, 23.123105))
    pArray.push(new BMap.Point(99.239283, 22.697005))
    pArray.push(new BMap.Point(99.165694, 22.303805))
    pArray.push(new BMap.Point(99.386462, 21.857966))
    pArray.push(new BMap.Point(100.251135, 21.445169))
    pArray.push(new BMap.Point(100.839848, 21.290063))
    pArray.push(new BMap.Point(101.704521, 21.031186))
    pArray.push(new BMap.Point(102.05407, 21.152053))
    pArray.push(new BMap.Point(101.998878, 21.582901))
    pArray.push(new BMap.Point(101.962083, 22.132497))
    pArray.push(new BMap.Point(102.587591, 22.355156))
    pArray.push(new BMap.Point(103.599443, 22.338041))
    pArray.push(new BMap.Point(104.482513, 22.560368))
    pArray.push(new BMap.Point(105.383981, 22.799392))
    pArray.push(new BMap.Point(106.083078, 22.59454))
    pArray.push(new BMap.Point(106.469421, 22.286683))
    pArray.push(new BMap.Point(106.874162, 21.754879))
    pArray.push(new BMap.Point(107.315697, 21.514051))
    pArray.push(new BMap.Point(107.812424, 21.410715))
    pArray.push(new BMap.Point(107.775629, 21.134792))
    pArray.push(new BMap.Point(106.929353, 20.269201))
    pArray.push(new BMap.Point(106.175064, 19.17158))
    pArray.push(new BMap.Point(106.377435, 18.470789))
    pArray.push(new BMap.Point(107.297299, 17.23746))
    pArray.push(new BMap.Point(109.008248, 15.675143))
    pArray.push(new BMap.Point(109.688948, 13.705222))
    pArray.push(new BMap.Point(109.652153, 11.664031))
    pArray.push(new BMap.Point(108.750686, 9.571001))
    pArray.push(new BMap.Point(108.198767, 6.876803))
    pArray.push(new BMap.Point(108.493124, 5.090099))
    pArray.push(new BMap.Point(109.817729, 3.612656))
    pArray.push(new BMap.Point(111.10554, 3.298351))
    pArray.push(new BMap.Point(114.71141, 5.514272))
    pArray.push(new BMap.Point(116.256783, 7.556636))
    pArray.push(new BMap.Point(118.758815, 10.883133))
    pArray.push(new BMap.Point(119.531502, 13.669242))
    pArray.push(new BMap.Point(119.494707, 16.617614))
    pArray.push(new BMap.Point(120.414572, 18.961654))
    pArray.push(new BMap.Point(121.51841, 20.633358))
    pArray.push(new BMap.Point(122.751029, 22.303805))
    pArray.push(new BMap.Point(123.247756, 23.378111))
    pArray.push(new BMap.Point(124.811526, 25.68375))
    pArray.push(new BMap.Point(126.577667, 25.900278))
    pArray.push(new BMap.Point(127.479134, 26.67975))
    pArray.push(new BMap.Point(128.454191, 28.189945))
    pArray.push(new BMap.Point(128.766945, 29.93561))
    pArray.push(new BMap.Point(128.73015, 31.650877))
    pArray.push(new BMap.Point(127.957464, 32.153119))
    pArray.push(new BMap.Point(127.221572, 32.745921))
    pArray.push(new BMap.Point(127.019202, 33.596907))
    pArray.push(new BMap.Point(125.988953, 33.827543))
    pArray.push(new BMap.Point(125.731391, 34.546135))
    pArray.push(new BMap.Point(125.878569, 35.454458))
    pArray.push(new BMap.Point(125.731391, 36.634799))
    pArray.push(new BMap.Point(125.80498, 37.51927))
    pArray.push(new BMap.Point(124.425183, 37.972159))
    pArray.push(new BMap.Point(124.498772, 38.58128))
    pArray.push(new BMap.Point(125.013896, 39.242487))
    pArray.push(new BMap.Point(124.590758, 39.471014))
    pArray.push(new BMap.Point(124.296402, 39.840762))
    pArray.push(new BMap.Point(124.388388, 40.081441))
    pArray.push(new BMap.Point(124.940307, 40.335346))
    pArray.push(new BMap.Point(125.731391, 40.630357))
    pArray.push(new BMap.Point(126.448885, 40.96591))
    pArray.push(new BMap.Point(126.798434, 41.493704))
    pArray.push(new BMap.Point(127.111188, 41.410654))
    pArray.push(new BMap.Point(127.883875, 41.271998))
    pArray.push(new BMap.Point(128.490985, 41.452192))
    pArray.push(new BMap.Point(128.307012, 41.879854))
    pArray.push(new BMap.Point(128.950918, 41.921089))
    pArray.push(new BMap.Point(129.484439, 42.12686))
    pArray.push(new BMap.Point(129.999564, 42.549994))
    pArray.push(new BMap.Point(130.073153, 42.807915))
    pArray.push(new BMap.Point(130.404304, 42.495557))
    pArray.push(new BMap.Point(130.77225, 42.359256))
    pArray.push(new BMap.Point(130.698661, 42.726583))
    pArray.push(new BMap.Point(131.195388, 42.848541))
    pArray.push(new BMap.Point(131.360964, 43.494895))
    pArray.push(new BMap.Point(131.342566, 44.491021))
    pArray.push(new BMap.Point(131.820896, 45.002351))
    pArray.push(new BMap.Point(132.998323, 44.976239))
    pArray.push(new BMap.Point(133.623831, 45.599605))
    pArray.push(new BMap.Point(134.102161, 46.394582))
    pArray.push(new BMap.Point(134.37812, 47.228226))
    pArray.push(new BMap.Point(134.874847, 47.851127))
    pArray.push(new BMap.Point(134.985231, 48.233588))
    pArray.push(new BMap.Point(135.13241, 48.454352))
    pArray.push(new BMap.Point(135.077218, 48.474352))

    //添加遮蔽层
    var plyall = new BMap.Polygon(pArray, { strokeOpacity: 1, strokeColor: '#091934', strokeWeight: 1, fillColor: '#091934', fillOpacity: 1 }) //建立多边形覆盖物
    state.map.addOverlay(plyall)

    pStart = new BMap.Point(180, 90)
    pEnd = new BMap.Point(0, -90)
    pArray = [
      new BMap.Point(135.077218, 48.454352),
      new BMap.Point(pStart.lng, pStart.lat),
      new BMap.Point(pStart.lng, pEnd.lat),
      new BMap.Point(135.077218, 48.454352),
    ]
    var sanjiaoxing = new BMap.Polygon(pArray, { strokeOpacity: 1, strokeColor: '#091934', strokeWeight: 1, fillColor: '#091934', fillOpacity: 1 }) //建立多边形覆盖物
    state.map.addOverlay(sanjiaoxing)
  }
  function adjustMapView() {
    var chinaBounds = new BMap.Bounds(new BMap.Point(73.66, 18.16), new BMap.Point(135.05, 53.55))
    state.map.centerAndZoom(chinaBounds.getCenter(), state.map.getViewport(chinaBounds).zoom + 1)
  }
  const goDetail = () => {
    router.push('/transportInTransit/transportRadar')
  }
</script>
<style lang="scss" scoped>
  .bm-view {
    height: calc(100% - 1.9rem);
    border-radius: 4px;
    padding: 0;
    margin: 0;
    background: transparent !important;
    // opacity: 0.8;
    transition-delay: 2s;
    // transform: translateZ(0); /* 启用GPU层 */
    // backface-visibility: hidden; /* 防止空白帧 */
  }

  .center {
    position: absolute;
    left: 50%;
    top: 140px;
    transform: translate(-50%, -50%);
    z-index: 9;
  }

  :deep(.el-divider--horizontal) {
    margin: 20px 0 10px !important;
  }

  .vertical-text {
    writing-mode: vertical-rl;
    /* 从右到左的垂直书写模式 */
    text-orientation: upright;
    /* 文字方向保持直立 */
  }

  .btn {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url('@/assets/images/radar/btn-bg.png') no-repeat;
    background-size: 100% 100%;
    font-size: 14px;
    font-weight: bold;
    color: #00417e;
    cursor: pointer;
  }

  .btn1 {
    width: 180px;
    height: 39px;
  }

  .btn2 {
    width: 130px;
    height: 39px;
  }
  .map-container {
    position: relative;
    height: 100%;
  }
  .map-container-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 1.9rem;
  }
  .map-container-header-top {
    height: 0.8rem;
    width: 100%;
  }
  .map-container-header-center-bottom {
    width: 4rem;
    height: 0.9rem;
    background: url('@/views/homePage/assets/map_icon_5.png') no-repeat;
    background-size: 100% 100%;
  }
  .map-container-header-center-top-text {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.8rem;
    height: 0.5rem;
    background: url('@/views/homePage/assets/map_icon_3.png') no-repeat;
    background-size: 100% 100%;
  }
  .map-container-header-center-bottom-count {
    font-family: electronicFont, serif;
    font-size: 0.45rem;
    text-align: center;
    padding: 0 0.1rem;
    height: 0.5rem;
    line-height: 0.5rem;
  }
  .map-container-header-center-bottom-text {
    font-family: YouSheBiaoTiHei;
    text-align: center;
  }
  .map-container-header-right {
    width: 1.2rem;
    height: 0.3rem;
    line-height: 0.3rem;
    background: url('@/views/homePage/assets/map_icon_4.png') no-repeat;
    background-size: 100% 100%;
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: bold;
    font-size: 0.15rem;
    color: #00a3ff;
    text-align: center;
    font-style: normal;
    cursor: pointer;
  }
</style>

<style scoped>
  .bm-view :deep(.BMap_pop div:nth-child(9)) {
    top: 28px !important;
  }
  .customizeBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #228b22;
    font-weight: 500;
    color: #fff;
    padding: 5px 10px;
    font-size: 16px;
  }
  .customizeBox .close-btn {
    border: none;
    padding: 0 4px;
    cursor: pointer;
    background: transparent;
    color: #fff;
  }
</style>
