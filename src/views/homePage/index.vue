<!--
 * @Author: llm
 * @Date: 2023-09-06 10:21:44
 * @LastEditors: z<PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-21 20:57:37
 * @Description: 首页工作台
 *
-->
<template>
  <div v-if="systemName === 'VTMS'" class="content">
    <!-- 头部 -->
    <Header ref="headerRef" @updateDataStatus="updateDataStatus" :systemSettings="systemSettings.value">
      <template v-slot:title>
        <img src="../homePage/assets/gongzuotai.png" alt="title" class="title" />
      </template>
    </Header>
    <div style="height: calc(100vh - 184px)" v-show="showLoading">
      <dv-loading>
        <div class="color-white">正在初始化...</div>
      </dv-loading>
    </div>
    <div style="padding: 0 20px" v-show="!showLoading">
      <!-- 上部分 -->
      <firstPosition :data="pieDataInfo" height="1.3rem" @topChangeTab="topChangeTab" />
      <!-- 下部分 左中右 -->
      <lastPosition :commonFunctionList="commonFunctionList" :toDoListDataList="toDoListDataList" :defaultDataInfo="defaultDataInfo" />
    </div>
  </div>
  <HomePage_ v-else></HomePage_>
</template>
<script setup lang="ts">
  // 适配flex
  import '@/utils/flexible.js'
  import Header from './components/Header.vue'
  import firstPosition from './components/firstPosition.vue'
  import lastPosition from './components/lastPosition.vue'
  import { getBigScreenCommonFunctionApi, getBigScreenToDoListApi, getBigScreenDefaultDataApi, getStatisticsDataApi } from '@/api/bigScreen/index.js'
  import { useSystemNameStore } from '@/store/modules/systemName'
  import HomePage_ from './index_.vue'
  defineOptions({
    name: 'HomePage',
  })
  const showLoading = ref(false)
  const pieDataInfo = ref({} as any) // 饼图数据
  const commonFunctionList = ref([] as any) // 常用功能列表
  const toDoListDataList = ref([] as any) // 待办事项列表
  const defaultDataInfo = ref({} as any) // 7选4的默认数据
  const period = ref('year') // 时间周期
  const headerRef = ref()
  const systemSettings = ref<any>(true)

  let systemName = ref<string>('')

  onMounted(() => {
    const systemNameStore = useSystemNameStore()
    systemName.value = systemNameStore.systemName as string
    initData() // 初始化数据
    systemSettings.value = localStorage.getItem('systemSettings')
  })

  // 确认更新数据
  const updateDataStatus = (data: any) => {
    initData() // 初始化数据
  }

  // 初始化数据
  const initData = () => {
    showLoading.value = true
    // 常用功能列表 待办事项 7选4的默认数据
    Promise.all([getCommonFunctionList(), getToDoListData(), getDefaultDataList(), getTopPieData()]).then(() => {
      showLoading.value = false
    })
  }

  // 获取常用功能列表
  const getCommonFunctionList = () => {
    return new Promise<void>((resolve, reject) => {
      getBigScreenCommonFunctionApi({})
        .then((res: any) => {
          const { data } = res
          commonFunctionList.value = data

          resolve(data)
        })
        .catch((err: any) => {})
    })
  }

  // 获取常用功能列表
  const getToDoListData = () => {
    return new Promise<void>((resolve, reject) => {
      getBigScreenToDoListApi({})
        .then((res: any) => {
          const { data } = res
          toDoListDataList.value = data
          resolve(data)
        })
        .catch((err: any) => {})
    })
  }
  // 获取7选4的默认数据
  const getDefaultDataList = () => {
    return new Promise<void>((resolve, reject) => {
      getBigScreenDefaultDataApi({})
        .then((res: any) => {
          const { data } = res
          defaultDataInfo.value = data
          resolve(data)
        })
        .catch((err: any) => {})
    })
  }

  // 获取顶部饼图数据
  const getTopPieData = () => {
    return new Promise<void>((resolve, reject) => {
      getStatisticsDataApi({
        period: period.value,
      })
        .then((res: any) => {
          const { data } = res
          pieDataInfo.value = data

          resolve(data)
        })
        .catch((err: any) => {})
    })
  }

  const topChangeTab = (val: any) => {
    period.value = val
    getTopPieData()
  }
</script>

<style lang="scss" scoped>
  .content {
    // background-color: #06182a;
    background: url('./assets/home_bg.png') no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: calc(100vh - 84px);
    overflow: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .title {
    width: 4rem;
    height: 0.45rem;
  }
</style>
