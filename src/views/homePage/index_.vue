<!--
 * @Author: llm 
 * @Date: 2023-09-06 10:21:44
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-18 10:56:51
 * @Description: 
 * 
-->
<template>
  <div class="bg1 flex-row justify-center items-center">
    <div class="to-animate title fadeInUp animated">展望未来 共启愿景</div>
  </div>
</template>
<script>
  export default {
    data() {
      return {}
    },

    mounted() {},
  }
</script>
<style lang="scss" scoped>
  .pic {
    position: absolute;
    top: 20px;
    right: 20px;

    img {
      width: 400px;
      height: 100%;
    }
  }

  .bg {
    width: 100%;
    height: calc(100vh - 84px);
    background-image: url('./assets/bg1.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .bg1 {
    display: flex;
    width: 100%;
    height: calc(100vh - 84px);
    background-image: url('./assets/full_image_2.jpg');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .title {
    font-size: 74px;
    color: #fff;
    letter-spacing: 15px;
  }

  .fadeInUp {
    animation-name: fadeInUp;
    animation-name: fadeInUp;
  }

  .animated {
    animation-duration: 1.5s;
    animation-duration: 1.5s;
    animation-fill-mode: both;
    animation-fill-mode: both;
  }

  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translate3d(0, 100px, 0);
      transform: translate3d(0, 100px, 0);
    }

    100% {
      opacity: 1;
      transform: none;
      transform: none;
    }
  }

  .to-animate {
    opacity: 0;
  }
</style>
