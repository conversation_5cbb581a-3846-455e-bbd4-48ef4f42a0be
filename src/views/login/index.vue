<template>
  <div class="login-container">
    <img class="login_logo" src="@/assets/login_logo.jpg" alt="" />
    <el-form ref="loginFormRef" :model="loginData" :rules="loginRules" class="login-form">
      <div class="flex text-black items-center py-4">
        <span class="text-2xl flex-1 text-center">欢迎登录</span>
        <!-- <lang-select class="text-white" /> -->
      </div>

      <el-form-item prop="username" style="margin-bottom: 33px">
        <div class="p-2 text-black">
          <svg-icons icon-class="user" />
        </div>
        <!-- <el-input ref="username" style="width: 100%;height:40px" v-model="loginData.username" class="flex-1"
          size="default" :placeholder="$t('login.username')" name="username" /> -->
        <el-input
          ref="username"
          style="width: 100%; height: 40px"
          v-model="loginData.username"
          class="flex-1"
          size="default"
          :placeholder="$t('login.username')"
          name="username"
          :prefix-icon="UserFilled"
        />
      </el-form-item>

      <el-tooltip :disabled="!isCapslock" :trigger-keys="['CapsLock']" content="大写已打开" placement="right">
        <el-form-item prop="password" style="margin-bottom: 33px">
          <span class="p-2 text-black">
            <svg-icons icon-class="password" />
          </span>
          <el-input
            v-model="loginData.password"
            style="width: 100%; height: 40px"
            class="flex-1"
            placeholder="密码"
            :type="passwordVisible === false ? 'password' : 'input'"
            size="default"
            name="password"
            :prefix-icon="Lock"
            show-password
            @keydown="checkCapslock"
            @keyup="checkCapslock"
            @keyup.enter="handleLogin"
          />
          <span class="mr-2" @click="passwordVisible = !passwordVisible">
            <svg-icons :icon-class="passwordVisible === false ? 'eye' : 'eye-open'" class="text-black cursor-pointer" />
          </span>
        </el-form-item>
      </el-tooltip>

      <!-- 验证码 -->
      <el-form-item prop="captcha" style="width: calc(100% - 130px); margin-bottom: 33px">
        <span class="p-2 text-black">
          <svg-icons icon-class="verify_code" />
        </span>
        <el-input
          v-model="loginData.captcha"
          style="width: calc(100% - 130px); height: 40px"
          auto-complete="off"
          :placeholder="$t('login.captcha')"
          class="w-[60%]!"
          @keyup.enter="handleLogin"
          @input="updateButtonState"
          @focus="showWerificationCode = true"
          @blur="showWerificationCode = false"
        >
          <template #prefix>
            <svg t="1743584205139" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2768" width="14" height="14">
              <path
                d="M895.744 273.28a59.2 59.2 0 0 0-46.72-51.936A1103.36 1103.36 0 0 1 697.92 186.56a561.28 561.28 0 0 1-135.008-78.912 61.76 61.76 0 0 0-72.16 0 354.112 354.112 0 0 1-136.032 78.4 507.264 507.264 0 0 1-146.944 36.32 55.04 55.04 0 0 0-46.208 51.936S160 404.64 160 519.36C160 727.04 405.056 928 527.616 928c122.528 0 330.24-141.76 363.456-405.504 8.32-155.776 2.56-248.704 2.56-248.704l2.112-0.544z m-133.44 160.416l-256 241.44c-11.52 10.976-29.12 12.704-42.56 4.16l-6.72-5.728-141.248-146.912a35.264 35.264 0 0 1 51.904-47.776l116.32 122.016 230.528-218.08a35.264 35.264 0 1 1 47.776 51.936v-1.056z"
                :fill="showWerificationCode ? '#303133' : '#a8abb2'"
                p-id="2769"
              ></path>
            </svg>
          </template>
        </el-input>

        <div class="captcha">
          <img :src="captchaBase64" @click="getCaptcha" v-if="captchaBase64" />
        </div>
      </el-form-item>

      <el-button
        size="large"
        style="width: 100%; height: 60px; font-size: 24px"
        :loading="loading"
        type="primary"
        :class="[{ 'bright-button': isCaptchaFilled, 'dim-button': !isCaptchaFilled }]"
        @click.prevent="handleLogin"
        >{{ $t('login.login') }}</el-button
      >
      <div class="bottomText">为了更好地使用体验，请使用最新版的谷歌浏览器</div>
    </el-form>
    <div class="copyright">
      <div class="info">
        <div><el-text style="color: #ccc">售后电话：022-25329883</el-text></div>
        <div>
          <el-text class="flex-row items-center" style="color: #ccc">
            ICP证：
            <a class="icp" href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">津ICP备2021001986号</a>
          </el-text>
        </div>

        <div>
          <el-text style="color: #ccc">@版权所有：文诚恒远（天津）供应链管理服务有限公司</el-text>
        </div>
      </div>
    </div>

    <el-dialog v-model="systemDialogVisible" align-center width="30%" :show-close="false" style="width: 100px; height: 100px; position: relative">
      <div class="loginDialog">
        <div class="close_dialog" @click="systemDialogVisible = false">×</div>
        <div style="margin-bottom: 15px">
          <img src="@/assets/logo.jpg" alt="" />
        </div>
        <div>
          <img src="@/assets/logoName.jpg" alt="" />
        </div>
        <div class="loginText">请选择：</div>
        <div class="selectLogin">
          <div
            v-for="system in systemsList"
            :key="system.systemName"
            class="selectItem"
            :class="{ selected: selectedSystem === system.systemName }"
            @click="selectSystem(system.systemName)"
          >
            <img v-if="system.systemName === 'VTMS' && selectedSystem === system.systemName" src="@/assets/vtms1.jpg" alt="" />
            <img v-if="system.systemName === 'VTMS' && selectedSystem !== system.systemName" src="@/assets/vtms.jpg" alt="" />
            <img v-if="system.systemName === 'VWMS' && selectedSystem === system.systemName" src="@/assets/vwms1.png" alt="" />
            <img v-if="system.systemName === 'VWMS' && selectedSystem !== system.systemName" src="@/assets/vwms.png" alt="" />
            <div class="selectText" :style="{ color: selectedSystem === system.systemName ? '#3F97FD' : '#D1D3D7' }">
              {{ system.systemName }}
            </div>
          </div>
        </div>

        <div style="width: 100%; display: flex; justify-content: center; margin-top: 20px">
          <el-button
            type="primary"
            style="width: 117px; height: 46px; background: #b9daff; border-radius: 10px; border: none"
            :style="{
              background: currentSystem?.systemName ? '#3F97FD' : '#B9DAFF',
              color: '#fff',
            }"
            @click="loginSystem"
            >确定</el-button
          >
        </div>
      </div>
    </el-dialog>

    <el-dialog v-model="firstDialogVisible" align-center width="30%" :show-close="false" style="width: 100px; height: 100px; position: relative">
      <div class="loginDialog" style="height:auto;">
        <div class="close_dialog" @click="firstDialogVisible = false">×</div>

        <div class="content">
          <div class="title">请更改密码</div>
           <div class="tips">
           <div> 密码修改提示：</div>密码长度为:8-16位，必须包含大写字母+小写字母+数字。
           </div>

          <el-form ref="PasswordFormRef" :model="PasswordFormData" :rules="PasswordFormRules">
            <el-form-item prop="password" style="margin-bottom: 33px">
          <span style="display:flex;align-items: center;gap: 10px; font-size: 14px;font-weight: 600;" class="p-2 text-black">
          <span>新密码&emsp;&nbsp;</span>  <svg-icons icon-class="password" />
          </span>
          <el-input
            v-model="PasswordFormData.password"
            style="width: 100%; height: 40px"
            class="flex-1"
            placeholder="密码"
            :type="passwordVisible === false ? 'password' : 'input'"
            size="default"
            name="password"
            :prefix-icon="Lock"
            show-password
            @keyup="checkCapslock"
            @keyup.enter="handleLogin"
          />
          <span class="mr-2" @click="passwordVisible = !passwordVisible">
            <svg-icons :icon-class="passwordVisible === false ? 'eye' : 'eye-open'" class="text-black cursor-pointer" />
          </span>
        </el-form-item>

        <el-form-item  prop="confirmPassword" style="margin-bottom: 33px">
          <span style="display: flex;align-items: center;gap: 10px;font-size: 14px;font-weight: 600;" class="p-2 text-black">
            <span>确认密码</span>
            <svg-icons icon-class="password" />
          </span>
          <el-input
            v-model="PasswordFormData.confirmPassword"
            style="width: 100%; height: 40px"
            class="flex-1"
            placeholder="密码"
            :type="passwordVisible === false ? 'password' : 'input'"
            size="default"
            name="password"
            :prefix-icon="Lock"
            show-password
            @keyup="checkCapslock"
            @keyup.enter="handleLogin"
          />
          <span class="mr-2" @click="passwordVisible = !passwordVisible">
            <svg-icons :icon-class="passwordVisible === false ? 'eye' : 'eye-open'" class="text-black cursor-pointer" />
          </span>
        </el-form-item>
    </el-form>
        </div>

        <div style="width: 100%; display: flex; justify-content: center; margin-top: 20px">
          <el-button
            type="primary"
            style="width: 117px; height: 46px;  border-radius: 10px; border: none"
            @click="ChangePassword"
            >确定</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import router from '@/router'
  import LangSelect from '@/components/LangSelect/index.vue'
  import { getCaptchaApi, getUserMenuList,changePasswordApi } from '@/api/auth'
  import { UserFilled, Lock } from '@element-plus/icons-vue'
  // 状态管理依赖
  import { useUserStore } from '@/store/modules/user.js'
  import { useSettingsStore } from '@/store/modules/settings'
  import { useAppStore } from '@/store/modules/app'
  // API依赖
  import { LocationQuery, LocationQueryValue, useRoute } from 'vue-router'
  import { LoginData, SystemsVO } from '@/api/auth/types'
  import { ElForm } from 'element-plus'
  import { ref, onMounted, onUnmounted } from 'vue'
  const appStore = useAppStore()
  const menuList = useStorage('menuList', Array<any>)
  const userStore = useUserStore()
  const settingsStore = useSettingsStore()
  const route = useRoute()
  const captchaBase64 = ref() // 验证码图片Base64字符串
  const selectedSystem = ref('')
  const PasswordFormRef = ref(ElForm)
  const PasswordFormData = ref({
    password: '',
    confirmPassword: '',
  })
  
  const validatePassword = (rule: any, value: any, callback: any) => {
    if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,16}$/.test(value)) {
      callback(new Error('密码长度为8-16位，必须包含大写字母+小写字母+数字'))
    } else {
      callback()
    } 
  }

  // 判断两次密码是否一致
const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value !== PasswordFormData.value.password) {
    callback(new Error('两次密码不一致'))
  } else {
    callback()
  }
}

  const PasswordFormRules = ref({
    password: [{ required: true, trigger: 'blur', message: '请输入新密码' },{ validator: validatePassword, trigger: 'blur' }],
    confirmPassword: [{ required: true, trigger: 'blur', message: '请输入确认密码' },{ validator: validatePassword, trigger: 'blur' },{ validator: validateConfirmPassword, trigger: 'blur' }],
  })



  const ChangePassword = () => {
    PasswordFormRef.value.validate((valid: boolean) => {
      if (valid) {
        changePasswordApi({password: PasswordFormData.value.password}).then(res=>{
          if(res){
            ElMessage.success('密码修改成功，请重新登录')
            firstDialogVisible.value = false
          }else{
            ElMessage.success('密码修改失败')
          }
          
        })
        
      }
    })
  }
  /**
   * 按钮loading
   */
  const loading = ref(false)
  /**
   * 是否大写锁定
   */
  const isCapslock = ref(false)
  /**
   * 密码是否可见
   */
  const passwordVisible = ref(false)
  /**
   * 登录表单引用
   */
  const loginFormRef = ref(ElForm)
  const loginData = ref<LoginData>({
    username: '',
    password: '',
    captcha: '',
    checkKey: '',
  })
  const currentSystem = ref<any>()
  const systemDialogVisible = ref(false)
  const firstDialogVisible = ref(false)
  const showWerificationCode = ref(false)
  //系统列表
  const systemsList = ref<any[]>()
  const loginRules = {
    username: [{ required: true, trigger: 'blur', message: '请输入用户名' }],
    password: [{ required: true, trigger: 'blur', validator: passwordValidator }],
    captcha: [{ required: true, trigger: 'blur', message: '请输入验证码' }],
  }

  /**
   * 密码校验器
   */
  function passwordValidator(rule: any, value: any, callback: any) {
    if (value.length < 6) {
      callback(new Error('密码长度不能少于6位'))
    } else {
      callback()
    }
  }
  // 获取验证码
  async function getCaptcha() {
    //loginData.value.checkKey = 当前时间戳
    loginData.value.checkKey = new Date().getTime().toString()
    const { data } = await getCaptchaApi(loginData.value.checkKey)
    captchaBase64.value = data
  }
  /**
   * 检测大写是否开启
   */
  function checkCapslock(e: Event) {
    const event = e as KeyboardEvent;
    
    // 直接检测 CapsLock 状态
    if (event.getModifierState) {
      const capsLockState = event.getModifierState('CapsLock');
      if (isCapslock.value !== capsLockState) {
        isCapslock.value = capsLockState;
      }
    } else {
      // 降级方案：使用 keyCode 检测
      const keyCode = event.keyCode || event.which;
      const shiftKey = event.shiftKey;

      if (keyCode >= 65 && keyCode <= 90 && !shiftKey) {
        isCapslock.value = true;
      } else if (keyCode >= 97 && keyCode <= 122 && shiftKey) {
        isCapslock.value = true;
      } else {
        isCapslock.value = false;
      }
    }
  }

  // 全局检测 CapsLock 状态
  function checkGlobalCapslock(e: KeyboardEvent) {
    // 检查是否是 CapsLock 键
    if (e.code === 'CapsLock' || e.getModifierState) {
      isCapslock.value = e.getModifierState('CapsLock');
    }
  }

  // 组件挂载时添加全局事件监听
  onMounted(() => {
    // 初始检测
    if (window.event && (window.event as KeyboardEvent).getModifierState) {
      isCapslock.value = (window.event as KeyboardEvent).getModifierState('CapsLock');
    }
    
    // 添加全局事件监听，同时监听按下和释放
    window.addEventListener('keydown', checkGlobalCapslock);
    window.addEventListener('keyup', checkGlobalCapslock);
  });

  // 组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('keydown', checkGlobalCapslock);
    window.removeEventListener('keyup', checkGlobalCapslock);
  });

  const isCaptchaFilled = ref(false)

  function updateButtonState() {
    if (loginData.value.captcha !== '') {
      isCaptchaFilled.value = true
    } else {
      isCaptchaFilled.value = false
    }
  }

  function selectSystem(systemName: string) {
    selectedSystem.value = systemName
    currentSystem.value = systemsList.value?.find((item) => item.systemName === systemName)
  }

  /**
   * 登录
   */
  function handleLogin() {
    loginFormRef.value.validate((valid: boolean) => {
      if (valid) {
        loading.value = true
        userStore
          .login(loginData.value)
          .then(async (res: any) => {
            const { systems, admin,needChangePassword } = res
            if(needChangePassword){
              firstDialogVisible.value = true
            }else{
            localStorage.setItem('systemSettings', admin)
            systemsList.value = systems

            userStore.setSystemList(systems)
            if (systems.length > 1) {
              systemDialogVisible.value = true
            } else {
              currentSystem.value = systems[0]
              settingsStore.changeSetting({ key: 'systemName', value: currentSystem.value!.systemName })
              document.title = settingsStore.systemName
              userStore.setSystemType(currentSystem.value!.systemType)
              const menus = (await getUserMenuListFun()) as any
              menuList.value = menus.data
              await goHomePage()
            }
            }
          })
          .catch(() => {
            getCaptcha()
          })
          .finally(() => {
            loading.value = false
          })
      }
    })
  }
  const goHomePage = () => {
    return new Promise<void>((resolve, reject) => {
      const query: LocationQuery = route.query

      const redirect = (query.redirect as LocationQueryValue) ?? '/'

      const otherQueryParams = Object.keys(query).reduce((acc: any, cur: string) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
      router.push({ path: redirect, query: otherQueryParams })
      resolve()
    })
  }
  //登录选择的系统
  async function loginSystem() {
    if (!currentSystem.value || !currentSystem.value.systemName) {
      ElMessage.error('请选择需要登录的系统！')
      return
    }
    settingsStore.changeSetting({ key: 'systemName', value: currentSystem.value!.systemName })

    document.title = settingsStore.systemName
    userStore.setSystemType(currentSystem.value!.systemType)
    const menus = (await getUserMenuListFun()) as any
    menuList.value = menus.data
    // userStore.setMenuList(menuList);

    appStore.changeLanguage('zh-cn')
    await goHomePage()
  }
  function getUserMenuListFun() {
    return new Promise((resolve, reject) => {
      const menuList = getUserMenuList()
      resolve(menuList)
    })
  }
  onMounted(() => {
    getCaptcha()
  })
</script>

<style lang="scss" scoped>
  .login-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-height: 100%;
    overflow: hidden;
    background: url('@/assets/login_bg.png') no-repeat;
    background-size: 100% 100%;
    transition: all 0.3s linear;

    .login_logo {
      position: absolute;
      width: 37%;
      left: 10%;
      top: 26%;
      background-color: transparent;
      mix-blend-mode: multiply;
    }

    .inner {
      position: fixed;
      inset: 0;
      z-index: 1;
    }

    .login-form {
      position: relative;
      z-index: 2;
      width: 600px;
      height: 20%;
      max-width: 100%;
      padding: 40px 50px 30px;
      overflow: hidden;
      background-color: #fff;
      border-radius: 22px;
      box-shadow: 0 1px 50px 0 rgb(220 228 236 / 59%);
      margin-left: 50%;

      .captcha {
        position: absolute;
        top: 0;
        right: -130px;
        height: 100%;

        img {
          width: auto;
          height: 100%;
          cursor: pointer;
        }
      }
    }

    .bottomText {
      width: 100%;
      text-align: center;
      margin: 10px 0;
      height: 16px;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 400;
      font-size: 14px;
      color: #85bdff;
      line-height: 24px;
      font-style: normal;
      margin-top: 20px;
    }

    .text-center {
      font-size: 42px;
      margin-bottom: 30px;
      font-weight: 700;
    }

    .copyright {
      position: fixed;
      right: 0;
      bottom: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100px;
      color: #fff;
      background-color: #444866;

      .info {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .icp {
          cursor: pointer;

          &:hover {
            color: var(--el-color-primary);
            text-decoration: underline;
          }
        }
      }
    }
  }

  .el-form-item {
    background: rgb(0 0 0 / 10%);
    border: 1px solid rgb(255 255 255 / 10%);
    background-color: #f3f5f9 !important;
    border-radius: 10px;
    :deep(.el-input__wrapper) {
      background-color: #f3f5f9 !important;
    }
  }

  .el-input {
    background: transparent;

    :deep(.el-input__wrapper) {
      box-shadow: none !important;
      border: none !important;

      &.is-focus {
        box-shadow: none !important;
        border: none !important;
      }
    }

    .password-input :deep(.el-input__prefix) {
      position: relative;
    }
    :deep(.el-input__prefix)::after {
      content: '|';
      position: absolute;
      left: 22px;
      top: 47%;
      transform: translateY(-50%);
    }
    :deep(.el-input__wrapper) {
      padding: 0;
      background: transparent;
      box-shadow: none;

      .el-input__inner {
        color: var(--el-color-primary-dark);
        background: transparent;
        border: 0;
        border-radius: 0;
        // caret-color: #fff;

        &:-webkit-autofill {
          box-shadow: 0 0 0 1000px transparent inset !important;
          -webkit-text-fill-color: var(--el-color-primary-dark) !important;
        }

        // 设置输入框自动填充的延迟属性
        &:-webkit-autofill,
        &:-webkit-autofill:hover,
        &:-webkit-autofill:focus,
        &:-webkit-autofill:active {
          transition:
            color 99999s ease-out,
            background-color 99999s ease-out;
          transition-delay: 99999s;
        }
      }
    }
  }

  .flex-1 :deep(.el-input__inner) {
    padding-left: 15px !important;
  }

  .w-\[60\%\]\! :deep(.el-input__inner) {
    padding-left: 15px !important;
  }

  .el-input {
    background: transparent;

    &:focus-within {
      :deep(.el-input__prefix),
      :deep(.el-input__suffix) {
        .el-icon {
          color: var(--el-text-color-primary); // 聚焦时黑色
        }
      }
    }

    :deep(.el-input__prefix),
    :deep(.el-input__suffix) {
      .el-icon {
        color: var(--el-text-color-placeholder); // 默认灰色
        transition: color 0.3s ease; // 添加过渡效果
      }
    }
  }

  .bright-button {
    border-radius: 10px;
  }

  .dim-button {
    opacity: 0.7;
    border-radius: 10px;
    background-color: var(--el-color-primary-light-3);
    border: none !important;
    box-shadow: none !important;

    &:hover,
    &:focus {
      border: none !important;
      box-shadow: none !important;
    }
  }

  .loginDialog {
    width: 616px;
    height: 602px;
    position: absolute;
    top: -301px;
    left: -308px;
    border-radius: 20px;
    padding: 78px 64px;
    background: url('@/assets/loginborder.jpg') no-repeat;

    .content{
      width: 100%;
      padding: 10px;
      .title{
        font-size: 30px;
        font-weight: bold;
        color: #333333;
        text-align: left;
        margin-bottom: 10px;
      }
      .tips{
        font-size: 14px;
        color: #127af0;
        text-align: left;
        padding: 10px;
        background-color: #F4F5F9;
        border-radius: 10px;
        margin-bottom: 20px;
      }
    }

    .close_dialog {
      font-size: 26px;
      padding: 8px 8px;
      color: #999999;
      position: absolute;
      top: 0%;
      right: 0%;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 15px;
      margin-right: 30px;
      margin-top: 30px;
      cursor: pointer;


      &:hover {
        background-color: #efefef;
        /* 鼠标悬停时背景变黑 */
      }
    }

    img {
      background-color: transparent;
      mix-blend-mode: multiply;
    }

    .loginText {
      width: 72px;
      height: 18px;
      margin-top: 47px;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      line-height: 27px;
      text-align: center;
      font-style: normal;
      margin-bottom: 21px;
    }

    .selectLogin {
      display: flex;
      justify-content: space-between;

      .selectItem {
        width: 230px;
        height: 193px;
        background: #f3f9ff;
        border-radius: 19px;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;

        img {
          margin-top: 50px;
        }

        .selectText {
          width: 100px;
          height: 26px;
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: bold;
          font-size: 26px;
          line-height: 39px;
          text-align: center;
          font-style: normal;
          margin-top: 20px;
        }

        &.selected {
          background: #e6f1ff;
        }
      }
    }
  }
</style>
