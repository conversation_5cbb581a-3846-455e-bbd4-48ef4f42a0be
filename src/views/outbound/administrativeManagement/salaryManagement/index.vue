<template>
  <div v-loading="pageLoading" :element-loading-text="pageLoadingText" class="app-container">
    <div ref="topQueryCardRef">
      <!-- <el-card
        v-show="topQueryConfig.tableItem.filter((item: any) => item.query).length > 0"
        shadow="never"
      > -->
      <div
        v-show="
          topQueryConfig.tableItem.filter((item: any) => item.query && item.selectEnable).length > 0 ||
          listTopOperation?.some((item) => item.meta?.purpose === 'definePageHeader')
        "
        ref="content"
        style="position: relative"
      >
        <topQueryGroupComponent
          ref="topQueryGroupComponentRef"
          :query-permission-group="topQueryConfig.tableItem"
          :request-uri="requestUri"
          :show-search="showSearch"
          :top-button-permission-group="listTopOperation"
          @handle-search-query="handleSearchQuery"
          @query-params="getQueryData"
          @refresh-page="resetQuery"
          @refresh-page-table-column="refreshPageTableColumn"
        />
      </div>
      <div v-if="metaInfo?.ext?.instructions" class="mb-10px">
        <div v-html="metaInfo?.ext?.instructions" />
      </div>
      <!-- <div v-if="state.isShowMore" style="padding-bottom: 20px; color: #606266; text-align: center">
        <div class="more">
          <el-button :icon="state.status ? 'ArrowDown' : 'ArrowUp'" link type="" @click="moreClick">
            {{ state.status ? '展开更多' : '收起' }}
          </el-button>
        </div>
      </div> -->
      <!-- </el-card> -->
    </div>
    <div :style="{ paddingTop: topQueryConfig.tableItem.filter((item: any) => item.query && item.selectEnable).length > 0 ? '60px' : '0px' }">
      <el-card v-if="statisticColumn" ref="statisticRef" class="top-query" shadow="never" style="padding-bottom: 20px; margin-bottom: 20px">
        <statisticComponent :statistic-column="statisticColumn" :table-column="tableConfig.tableItem" />
      </el-card>
      <el-card shadow="never">
        <template v-if="listTopOperation && listTopOperation!.length > 0" #header>
          <div class="flex items-center justify-between">
            <!-- 按钮组 -->
            <div ref="topButtonGroupComponentRef">
              <button-group-component
                ref="topButtonGroupComponent"
                :button-permission-group="listTopOperation"
                :custom-template-loading="customTemplateLoading"
                :download-button-loading="downloadButtonLoading"
                :export-button-loading="exportButtonLoading"
                :import-custom-excel-button-loading="importCustomExcelButtonLoading"
                :ids="ids"
                :import-button-loading="importButtonLoading"
                :meta-info="metaInfo"
                :print-button-loading="printButtonLoading"
                :print-obj="printObj"
                :statement-id="mergeFormData.statementId"
                @appointment="appointment"
                @meeting="addMeeting"
                @quickChangeVehicle="quickChangeVehicle"
                @recompute="recompute"
                @add-examine-project="addExamineProject"
                @add-examine-template="addExamineTemplate"
                @add-safe-standard="addSafeStandard"
                @batch-read="batchRead"
                @batch-shipment-confirm="batchShipmentConfirm"
                @default-handle="defaultHandle"
                @download-excel-template="downloadExcelTemplate"
                @export-excel="exportExcelFun"
                @handle-delete="handleDelete"
                @import-excel="importExcelFun"
                @import-custom-excel="importCustomExcel"
                @lane-appointment="appointmentVehicleShipmentInfo"
                @print-bill-table="printBillTable"
                @print-table="printTable"
                @rail-waterway-lane-appointment="railWaterwayAppointmentVehicleShipmentInfo"
                @set-custom-table-header="setCustomTableHeader"
                @set-custom-table-template-header="setCustomTableTemplateHeader"
                @shipment-confirm="shipmentConfirm"
                @update-statement="updateStatement"
                @vehicle-change="vehicleChange"
                @vehicle-flip-board="vehicleFlipBoard"
                @add-ykp="addYkp"
                @tree-expand="toggleExpandAll"
                @show-menu-dialog="showMenuDialog"
                @reset-query="resetTopQuery"
                @search-query="searchTopQuery"
                @out-fleet-audit-setting-add="outFleetAuditSettingAdd"
                @batch-schedule="batchSchedule"
                @empty-dispatch="emptyDispatch"
                @top-btn-confirm-dialog="topBtnConfirmDialog"
                @generate-settlement="generateSettlement"
                @generate-statement="generateStatement"
                @generate-repair-statement="generateRepairStatement"
                @generate-subsidy-statement="generateSubsidyStatement"
                @add-quality-loss="addQualityLoss"
                @add-fine="addFine"
                @alter-load-time="alterLoadTime"
                @alter-actual-drop-time="alterActualDropTime"
                @add-fleet-report="addFleetReport"
                @add-supplier-repair="addSupplierRepair"
                @order-template-config="orderTemplateConfig"
                @download-custom-excel-template="downloadCustomExcelTemplate"
                @add-inspection-configuration="addInspectionConfiguration"
                @add-order-management="addOrderManagement"
                @scattered-receive-settlement="scatteredReceiveSettlement"
                @scattered-receive-payment="scatteredReceivePayment"
                @vehicle-history-mileage-edit="vehicleHistoryMileageEdit"
                @oil-loan-add="oilLoanAdd"
                @etc-loan-add="etcLoanAdd"
                @add-on-route-maintenance="addOnRouteMaintenance"
              />
            </div>
            <div class="cursor" @click="handleMsgQuery" v-html="rightTipDataObj?.timeoutMessage || ''" />
          </div>
        </template>
        <div>
          <table-component
            ref="tableComponentRef"
            :button-permission-group="listRightOperation"
            :height="state.tableHeight"
            :loading="loading"
            :is-expand-all="isExpandAll"
            :page-summary="pageSummary"
            :queryParams="queryParams"
            :request-uri="requestUri"
            :table-border="tableBorder"
            :table-config="tableConfig"
            :table-data="tableData"
            :top-button-permission-group="listTopOperation"
            :total-summary="totalSummary"
            row-key="id"
            @cancel="cancel"
            @copy="copyForm"
            @meeting="meetingDetail"
            @publish="publish"
            @score="score"
            @refresh-page-table-column="refreshPageTableColumn"
            @batch-delete="batchDeleteFun"
            @confirm-dialog="confirmDialog"
            @custom-table-template-header="customTableTemplateHeader"
            @default-handle="defaultHandleTable"
            @delete-item="deleteItemFun"
            @edit-auth="editAuth"
            @edit-examine-project="editExamineProject"
            @edit-examine-template="editExamineTemplate"
            @edit-inspection-warehouse="editInspectionWarehouse"
            @edit-safe-standard="editSafeStandard"
            @file-download="fileDownload"
            @handle-status-change="handleStatusChange"
            @open-menu-dialog="openMenuDialog"
            @open-data-column-menu-dialog="openDataColumnMenuDialog"
            @show-can-allocate-detail="showCanAllocateDetail"
            @show-pic="showPic"
            @show-row-menu-dialog="showRowMenuDialog"
            @show-row-object-dialog="showRowObjectDialog"
            @to-line-schedule="toLineSchedule"
            @to-track-map="toTrackMap"
            @update-item="defaultHandle"
            @edit-by-id="getDialogDetailById"
            @update-password="updatePassword"
            @view-safe-standard="viewSafeStandard"
            @view-ykp="viewYkp"
            @transport-plan-detail="transportPlanDetailFun"
            @out-fleet-audit-setting-edit="outFleetAuditSettingEdit"
            @out-fleet-audit-setting-copy="outFleetAuditSettingCopy"
            @edit-subsidy="editSubsidy"
            @edit-dispatch="editDispatch"
            @edit-generate-settlement="editGenerateSettlement"
            @view-generate-sett="viewGenerateSett"
            @edit-generate-statement="editGenerateStatement"
            @edit-repair-statement="editRepairStatement"
            @view-generate-statement="viewGenerateStatement"
            @view-repair-generate-statement="viewRepairGenerateStatement"
            @edit-generate-subsidy-statement="editGenerateSubsidyStatement"
            @view-generate-subsidy-statement="viewGenerateSubsidyStatement"
            @edit-open-invoice-managment="editOpenInvoiceManagment"
            @edit-open-invoice-repair-management="editOpenInvoiceRepairManagement"
            @edit-open-invoice-outsourcing-management="editOpenInvoiceOutsourcingManagement"
            @edit-fine="editFine"
            @edit-quality-loss="editQualityLoss"
            @edit-outsourcing-payment="editOutsourcingPayment"
            @edit-subsidy-payment="editSubsidyPayment"
            @payment-part="paymentPart"
            @resignation-employee-management="resignationEmployeeManagement"
            @edit-fleet-report="editFleetReport"
            @view-fleet-report="viewFleetReport"
            @edit-supplier-repair="editSupplierRepair"
            @adjust-cost-supplier-repair="adjustCostSupplierRepair"
            @edit-employee-management="editEmployeeManagement"
            @dynamic-audit-detail="dynamicAuditDetail"
            @column-dynamic-audit-detail="columnDynamicAuditDetail"
            @un-bind-tire="unBindTire"
            @modify-customer-income="modifyCustomerIncome"
            @compare-modify-customer-income="compareModifyCustomerIncome"
            @modify-carrier-payment="modifyCustomerIncome"
            @edit-inspection-configuration="editInspectionConfiguration"
            @copy-inspection-configuration="copyInspectionConfiguration"
            @edit-order-management="editOrderManagement"
            @formula-customer-contract="formulaCustomerContract"
            @formula-outsourcing-contract="formulaOutsourcingContract"
            @show-row-menu-form-dialog="showRowMenuFormDialog"
            @edit-settlement-amount="editSettlementAmount"
            @view-reconciliation-payment="viewReconciliationPayment"
            @reset-query="resetQuery"
            @sort-change="sortChange"
            @oil-loan-edit="oilLoanEdit"
            @etc-loan-edit="etcLoanEdit"
            @view-dispatch="viewDispatch"
            @table-detail-dialog="tableDetailDialog"
            @edit-on-route-maintenance="editOnRouteMaintenance"
            @view-on-route-maintenance="viewOnRouteMaintenance"
          />
        </div>
        <pagination v-model:limit="queryParams.limit" v-model:page="queryParams.page" v-model:total="total" @pagination="_pagination" />
        <!-- 新增or编辑组件 -->
        <form-dialog
          v-if="dialog.visible"
          ref="formDialogRef"
          :btn-menu="btnMenu"
          :current-row="currentRow"
          :data-column="operationColumn ? operationColumn : tableConfig.tableItem"
          :dialog="dialog"
          :ids="ids"
          :is-copy="isCopy"
          :is-edit="isEdit"
          :loading="loading"
          :refresh-menu-count="btnMenu?.meta?.form?.refreshMenuCount"
          :request-uri="btnRequestUri ?? requestUri"
          @clear-form-column="clearFormColumn"
          @close-dialog="closeDialog"
          @form-data="getFormData"
          @handle-submit="handleSubmit"
        />
      </el-card>
    </div>
    <!-- 查看照片 -->
    <PicDialogComponent ref="picDialogComponent" :image-list="imageList" />
    <!-- 列表项详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible.visible"
      :close-on-click-modal="false"
      :draggable="true"
      :fullscreen="isFullscreen"
      :title="detailDialogVisible.title"
      :width="'80%'"
      align-center
      destroy-on-close
      @closed="closeDetailDialogVisiblese(detailDialogVisible)"
    >
      <template #header>
        <slot name="header">
          <div class="el-dialog__title" @dblclick="setFullscreen">
            {{ detailDialogVisible.title }}
          </div>
        </slot>
        <button class="el-dialog__headerbtn" style="right: 30px" type="button" @click="setFullscreen">
          <el-icon color="#909399">
            <FullScreen />
          </el-icon>
        </button>
      </template>
      <el-scrollbar class="formClass" max-height="90vh">
        <BasePage1 />
      </el-scrollbar>
    </el-dialog>
    <!-- tab列表项详情弹窗 -->
    <el-dialog
      v-model="detailBaseTabPageDialogVisible.visible"
      :close-on-click-modal="false"
      :draggable="true"
      :fullscreen="isFullscreen"
      :title="detailBaseTabPageDialogVisible.title"
      :width="detailBaseTabPageDialogVisible.dialogWidth ?? '80%'"
      align-center
      destroy-on-close
      @closed="closeDetailDialogVisible"
    >
      <template #header>
        <slot name="header">
          <div class="el-dialog__title" @dblclick="setFullscreen">
            {{ detailDialogVisible.title }}
          </div>
        </slot>
        <button class="el-dialog__headerbtn" style="right: 30px" type="button" @click="setFullscreen">
          <el-icon color="#909399">
            <FullScreen />
          </el-icon>
        </button>
      </template>
      <el-scrollbar class="formClass" max-height="90vh">
        <BaseTabPage :new-menu-id="proxy.$sideBarStore.$state.btnMenuId" />
      </el-scrollbar>
    </el-dialog>
    <!-- 对象数据弹窗 -->
    <el-dialog
      v-model="objectDialogVisible.visible"
      :close-on-click-modal="false"
      :draggable="true"
      :fullscreen="isFullscreen"
      :title="objectDialogVisible.title"
      align-center
      destroy-on-close
      width="50%"
      @closed="closeDetailDialogVisible"
    >
      <template #header>
        <slot name="header">
          <div class="el-dialog__title" @dblclick="setFullscreen">
            {{ detailDialogVisible.title }}
          </div>
        </slot>
        <button class="el-dialog__headerbtn" style="right: 30px" type="button" @click="setFullscreen">
          <el-icon color="#909399">
            <FullScreen />
          </el-icon>
        </button>
      </template>
      <baseObjectPage />
    </el-dialog>
    <!-- 规划详情弹窗 -->
    <el-dialog
      v-model="basePageDialogVisible.visible"
      :close-on-click-modal="false"
      :draggable="true"
      :title="basePageDialogVisible.title"
      align-center
      destroy-on-close
      fullscreen
      @closed="closeDetailDialogVisible"
    >
      <el-scrollbar max-height="100vh">
        <BasePage2 />
      </el-scrollbar>
    </el-dialog>

    <!-- 菜单弹窗 -->
    <menuDialog
      ref="menuDialogRef"
      :checked-user="userInfo"
      :current-menu-column-list="currentMemuColumnList"
      :menu-dialog-visible="menuDialogVisible"
      :menu-list="menuList"
      @get-list-menu-options="getcurrentLoginUserMenuColumnlist"
      @select-menu-column="selectMenuColumn"
    />
    <!-- 数据列弹窗 -->
    <dataColumnMenuDialog
      ref="dataColumnMenuDialogRef"
      :checked-role="checkedRole"
      :menu-dialog-visible="dataColumnMenuDialogVisible"
      :menu-list="dataColumnMenuList"
      :menu-tree-loading="menuTreeLoading"
      @close-menu-dialog="closeMenuDialog"
      @select-menu-ids="selectMenuIds"
    />
    <!-- 发运管理-运单申请-申请预约 -->
    <AppointmentDialogComponent
      ref="appointmentDialogRef"
      :appointment-type="appointmentType"
      :dialog="appointmentDialog"
      :table-row="selectTableColumn"
      @close-dialog="closeAppointmentDialog"
    />
    <!-- 运单管理-运单申请-车道预约 -->
    <LaneAppointmentDialogComponent
      ref="laneAppointmentDialogRef"
      :dialog="laneAppointmentDialog"
      :lane-appointment-type="laneAppointmentType"
      :table-row="selectTableColumn"
      @close-dialog="closeLaneAppointmentDialog"
    />
    <!-- 运单确认-批量录入 -->
    <BatchShipmentConfirmDialogComponent
      ref="batchShipmentConfirmDialogRef"
      :dialog="batchShipmentConfirmDialog"
      :table-row="selectTableColumn"
      @close-dialog="closeBatchShipmentConfirmDialog"
      @shipment-order-confirm-submit="shipmentOrderConfirmSubmit"
    />
    <!-- 运单确认-一键确认 -->
    <ShipmentConfirmDialogComponent
      ref="shipmentConfirmDialogRef"
      :dialog="shipmentConfirmDialog"
      :table-row="selectTableColumn"
      :transport-type="transportType"
      @close-dialog="closeShipmentConfirmDialog"
      @shipment-order-confirm-submit="shipmentOrderConfirmSubmit"
    />
    <!-- 车辆变更弹窗 -->
    <VehicleChangeDialogComponent
      ref="exchangeVehicleComponentRef"
      :dialog-visible="vehicleChangeDialog"
      :vehicle-change-params="vehicleChangeParams"
      @close-dialog="closeVehicleChangeDialog"
      @exchange-vehicle-result="exchangeVehicleResult"
    />
    <!-- 倒板 -->
    <FlipBoardComponent
      ref="flipBoardComponentRef"
      :dialog-visible="flipBoardVisible"
      :vehicle-flip-board-params="vehicleFlipBoardParams"
      @close-dialog="flipBoardVisible.visible = false"
      @flip-board-result="filpBoardResult"
    />
    <!-- 审批设置弹窗 -->
    <OutFleetAuditSettingAddDialogComponent
      ref="outFleetAuditSettingAddDialogRef"
      :dialog="outFleetAuditSettingAddDialog"
      @close-dialog="closeOutFleetAuditSettingAddDialog"
      @out-fleet-fees-audit-setting-confirm-submit="outFleetFeesAuditSettingConfirmSubmit"
    />
    <!-- 司机计算补贴 费用修改 弹窗 -->
    <DriverEditSubsidyComponent
      ref="driverEditSubsidyDialogRef"
      :dialog="driverEditSubsidyDialog"
      @close-dialog="closeDriverEditSubsidyDialog"
      @subsidy-confirm-submit="subsidyConfirmSubmit"
    />
    <!-- 打印提货单 -->
    <!--    <iframe v-if="printBillData" frameborder="0" height="100%" style="display: contents" width="100%">-->
    <!--      <PrintBillComponent ref="printBillComponentRef" :printInfo="printBillData" />-->
    <!--    </iframe>-->
    <!--    &lt;!&ndash; 打印提货单 &ndash;&gt;-->
    <!--    <iframe v-if="printTableData" frameborder="0" height="100%" style="display: contents" width="100%">-->
    <!--      <PrintTableComponent ref="printTableComponentRef" :printInfo="printTableData" />-->
    <!--    </iframe>-->
    <!--    &lt;!&ndash; 合并运单号弹窗 &ndash;&gt;-->
    <!--    <MergeShipmentNoDialogComponent ref="mergeShipmentNoDialogRef" @closeDialog="closeDialog" />-->
    <!-- 报表中心-自定义表头 -->
    <CustomizeHeaderComponent
      v-if="tableItemDialogVisible"
      ref="customizeHeaderRef"
      :data="transferDataList"
      @handle-close-table-item="handleCloseTableItem"
      @handle-confirm-visible="handleConfirmVisible"
    />
    <!-- 新建考核模板 -->
    <ExamineTemplateDialogComponent ref="examineTemplateDialogRef" @reset-query="resetQuery" />
    <!-- 新建考核项目 -->
    <ExamineProjectDialogComponent ref="examineProjectDialogRef" @reset-query="resetQuery" />
    <!-- 考核项目打分 -->
    <ExamineScoreDialogComponent ref="examineScoreDialogRef" @reset-query="resetQuery" />
    <!-- 安全标准 -->
    <SafeStandardDialogComponent ref="safeStandardDialogRef" @open-inspection-dialog="openInspectionDialog" @reset-query="resetQuery" />
    <!-- 巡视路径弹窗 -->
    <!-- <inspection-dialog ref="inspectionDialogRef" @update:patrolPathList="setPatrolPathList" /> -->
    <!-- 干线排程-排程 -->
    <ToLineScheduleDialogComponent ref="toLineScheduleDialogRef" />
    <!-- 查看轨迹 -->
    <ToTrackMapDialogComponent ref="toTrackMapDialogRef" />
    <!-- 会议纪要 -->
    <MeetingDialogComponent ref="meetingRef" @reset-query="resetQuery" />
    <!-- 业务管理-批量调度 -->
    <BatchScheduleDialogComponent
      ref="batchScheduleDialogRef"
      :dialog="batchScheduleDialog"
      :request-uri="requestUri"
      @reset-query="resetQuery"
      @close-dialog="closeBatchScheduleDialog"
      @confirm-dispatch-success="confirmDispatchSuccess"
      @show-menu-dialog="showMenuDialog"
      @handle-query="handleQuery"
    />
    <!-- 业务管理-创建空调度 -->
    <EmptyDispatchDialogComponent
      ref="emptyDispatchDialogRef"
      :dialog="emptyDispatchDialog"
      @close-dialog="closeEmptyDispatchDialog"
      @confirm-dispatch-success="confirmEmptyDispatchSuccess"
    />
    <!-- 导入失败公共组件 importCheck-->
    <ImportFailedComponent ref="importFailedRef" :dialog="importDialogVisible" />
    <!-- 导入成功公共组件 importSuccess-->
    <ImportSuccessComponent ref="importSuccessRef" :dialog="importSuccessDialogVisible" />
    <!-- 财务管理-司机补贴-补贴对账-生成结算单 -->
    <GenerateSettlementDialogComponent ref="generateSettlementDialogRef" @confirm-settlement-success="handleQuery" @edit-dispatch="editDispatch" />
    <!-- 财务管理-客户结算-对账管理-生成对账单 -->
    <GenerateStatementDialogComponent ref="generateStatementDialogRef" @confirm-statement-success="handleQuery" />
    <!-- 财务管理-维修结算-对账管理-生成对账单 -->
    <GenerateRepairStatementDialogComponent ref="generateRepairStatementDialogRef" @confirm-statement-success="handleQuery" />
    <!-- 财务管理-外协结算-对账管理-生成对账单 -->
    <GenerateSubsidyStatementDialogComponent ref="generateSubsidyStatementDialogRef" @confirm-statement-success="handleQuery" />
    <!-- 财务管理-客户结算-开票管理-发票管理 -->
    <OpenInvoiceConfirmDialogComponent ref="openInvoiceConfirmDialogRef" @confirm-statement-success="handleQuery" />
    <!-- 财务管理-维修结算-开票管理-发票管理 -->
    <OpenInvoiceRepairConfirmDialogComponent ref="openInvoiceRepairConfirmDialogRef" @confirm-statement-success="handleQuery" />
    <!-- 质损信息 -->
    <QualityLossDialogComponent ref="qualityLossDialogRef" @close-quality-loss-dialog="handleQuery" />
    <!-- 财务管理-外协结算-开票管理-发票管理 -->
    <OpenInvoiceOutsourcingConfirmDialogComponent ref="openInvoiceOutsourcingConfirmDialogRef" @confirm-statement-success="handleQuery" />
    <!-- 罚款配置信息 -->
    <FineDialogComponent ref="fineDialogRef" @confirm-settlement-success="handleQuery" />
    <!-- 事故管理 -->
    <AccidentManageDialogComponent ref="accidentManageConfirmDialogRef" @close-dialog="closeAccidentDialog" />
    <!-- 外协结算-付款管理-付款信息 -->
    <OutsourcePaymentDialogComponent ref="outsourcePaymentDialogRef" @close-dialog="closeOutsourcePaymentDialog" />
    <!-- 外协结算-付款管理-补贴付款-打款 -->
    <SubsidyPaymentDetailDialogComponent ref="subsidyPaymentDialogRef" @close-dialog="closeSubsidyPaymentDialog" @reset-query="resetQuery" />
    <!-- 财务管理-付款管理-外协付款- 已支付金额明细 -->
    <OutsourcePaymentDetailDialogComponent ref="outsourcePaymentDetailDialogRef" />
    <!-- 变更时间弹窗 -->
    <ChangeTimeDialogComponent ref="changeTimeDialogRef" @reset-query="resetQuery" />
    <!-- 离职 -->
    <ResignationEmployeeManagementDialogComponent ref="resignationEmployeeManagementDialogRef" @reset-query="resetQuery" />
    <!-- 车队维修 -->
    <FleetReportDialogComponent ref="fleetReportDialogRef" @reset-query="resetQuery" />
    <!-- 工资管理 -->
    <EmployeeManagementDialogComponent ref="employeeManagementDialogRef" @reset-query="resetQuery" />
    <!-- 动态审批详情 -->
    <DynamicDetailDialogComponent
      ref="dynamicAuditDetailDialogRef"
      :btn-menu="btnMenu"
      :current-row="currentRow"
      :refresh-menu-count="btnMenu?.meta?.form?.refreshMenuCount ?? false"
      :request-uri="btnRequestUri ?? requestUri"
      :select-table-column="selectTableColumn"
      @reset-query="resetQuery"
      @handle-submit="handleSubmit"
      @view-generate-subsidy-statement="viewGenerateSubsidyStatement"
      @view-repair-generate-statement="viewRepairGenerateStatement"
      @view-generate-statement="viewGenerateStatement"
      @view-generate-sett="viewGenerateSett"
    />
    <!-- 订单模版配置 -->
    <OrderTemplateConfigDialogComponent ref="orderTemplateConfigDialogRef" />
    <!-- 轮胎列表-解绑司机弹窗 -->
    <UnBindTireDialogComponent ref="unBindTireDialogRef" @refresh="resetQuery" />
    <!-- 客户结算-运费计算-调整收款 -->
    <ModifyCustomerIncomeDialogComponent ref="modifyCustomerIncomeDialogRef" @refresh="resetQuery" />
    <!-- 点检标准 -->
    <InspectionConfigurationDialogComponent ref="inspectionConfigurationDialogRef" @refresh="resetQuery" />
    <!-- 订单管理 -->
    <OrderManagementDialogComponent ref="orderManagementDialogRef" @refresh="resetQuery" />
    <!-- 客户阶梯价格 -->
    <LadderPriceComponent ref="ladderPriceDialogRef" @refresh="resetQuery" />
    <!-- 外协阶梯价格 -->
    <OutsourcePriceDialogComponent ref="outsourcePriceDialogRef" @refresh="resetQuery" />

    <!-- 编辑对账金额 -->
    <ReconciliationamoutComponent ref="reconciliationAmount" @refresh="resetQuery" />

    <!-- 对账收款 -->
    <ReconciliationAndCollectionOfComponent ref="reconciliationAccounts" @refresh="resetQuery" />
    <!-- 零散订单-对账付款 -->
    <ReconciliationPaymentComponent ref="scatteredReceivePaymentref" @refresh="resetQuery" />
    <!-- 历史里程-历史-编辑 -->
    <vehicleHistoryMileageEditComponent ref="vehicleHistoryMileageEditRef" @reset-query="resetQuery" @subsidy-confirm-submit="subsidyConfirmSubmited" />
    <!-- 油费借支 -->
    <OilExpenseListBorrowComponent ref="oilExpenseListBorrowDialogRef" @refresh="resetQuery" />
    <!-- ETC借支 -->
    <EtcExpenseListBorrowComponent ref="etcExpenseListBorrowDialogRef" @refresh="resetQuery" />
    <!-- 业务管理-一键换车 -->
    <OneclickDialogComponent
      ref="OneclickDialogRef"
      :dialog="OneclickDialog"
      :request-uri="requestUri"
      @reset-query="resetQuery"
      @close-dialog="closeOneclickDialog"
      @confirm-dispatch-success="confirmDispatchSuccess"
      @show-menu-dialog="showMenuDialog"
    />
    <!-- 在途维修-新增 -->
    <AddOnRouteMaintenanceDialogComponent ref="addOnRouteMaintenanceDialogRef" @reset-query="resetQuery" @refresh="resetQuery" />
    <!-- 装载明细 -->
    <LoadDetailDialogComponent ref="loadDetailDialogRef" />
  </div>
</template>
<script lang="ts" setup>
  import BasePage1 from '@/views/Pages/basePage1.vue'
  import BasePage2 from '@/views/Pages/basePage2.vue'
  import BaseTabPage from '@/views/Pages/baseTabPage.vue'
  import baseObjectPage from '@/views/Pages/baseObjectPage.vue'
  import buttonGroupComponent from '@/components/TopButtonGroupComponent/index.vue'
  import pagination from '@/components/Pagination/index.vue'
  import formDialog from '@/components/FormDialogComponent/index.vue' //表单弹窗
  import PicDialogComponent from '@/components/PicDialogComponent/index.vue' //查看照片
  import { CheckedRole, CustomTableHeaderVO, RoleForm, RolePageVO } from '@/api/authorityManagement/RoleManagement/types'
  import {
    composeRequestParams,
    composeRequestParamsMultiRow,
    composeRequestQueryParams,
    downloadFileGlobalFun,
    getcurrentUserMenuColumnlist,
    getSelectOptions,
    resetFormGlobalFun,
    switchChangeGlobalFun,
  } from '@/utils/common'
  import {
    addItemApi,
    batchDeleteApi,
    deleteItemApi,
    downloadTemplate,
    downloadTemplateUrl,
    exportExcel,
    getDetailById,
    getListPage,
    getListPagePost,
    getListTree,
    getRecompute,
    globalDownloadTemplate,
    globalExportExcel,
    importFileGlobalBtnUriFun,
    importFileGlobalFun,
    printTableApi,
    updateItemApi,
  } from '@/api/auth'
  import { FormColumn, StatisticColumnVO, UploadImageVO } from '@/types/global'
  import {
    boardApplyCancel,
    getMenuCount,
    globalRequestApi,
    globalRequestUrlApi,
    invertedDistribution,
    transferVehicle,
    updateCarrierStatement,
  } from '@/api/planManagement'
  // 状态管理依赖
  import { useFormStore } from '@/store/modules/form.js'
  import { useSideBarStore } from '@/store/modules/sideBar'
  import { useSettingsStore } from '@/store/modules/settings'
  // *****************************以下是个别操作按钮弹窗导入代码，需要根据业务需求修改**********************
  import AppointmentDialogComponent from '@/components/OtherFormDialogComponent/AppointmentDialogComponent/index.vue' //发运管理-运单申请-申请预约
  import LaneAppointmentDialogComponent from '@/components/OtherFormDialogComponent/LaneAppointmentDialogComponent/index.vue' //发运管理-运单申请-车道预约
  import BatchShipmentConfirmDialogComponent from '@/components/OtherFormDialogComponent/BatchShipmentConfirmDialogComponent/index.vue' //发运管理-运单申请-批量录入
  import ShipmentConfirmDialogComponent from '@/components/OtherFormDialogComponent/ShipmentConfirmDialogComponent/index.vue' //发运管理-运单申请-一键确认
  import VehicleChangeDialogComponent from '@/components/OtherFormDialogComponent/VehicleChangeDialogComponent/index.vue' //车辆变更弹窗
  import FlipBoardComponent from '@/components/OtherFormDialogComponent/FlipBoardComponent/index.vue' //倒板弹窗
  import CustomizeHeaderComponent from '@/components/CustomizeHeaderComponent/index.vue' //自定义表头弹窗
  import ExamineTemplateDialogComponent from '@/components/OtherFormDialogComponent/ExamineTemplateDialogComponent/index.vue' //考核模板
  import ExamineProjectDialogComponent from '@/components/OtherFormDialogComponent/ExamineProjectDialogComponent/index.vue' //考核项目
  import ExamineScoreDialogComponent from '@/components/OtherFormDialogComponent/ExamineScoreDialogComponent/index.vue' //考核项目打分
  import ToLineScheduleDialogComponent from '@/components/OtherFormDialogComponent/ToLineScheduleDialogComponent/index.vue' //考核项目打分
  import ToTrackMapDialogComponent from '@/components/OtherFormDialogComponent/ToTrackMapDialogComponent/index.vue' //查看轨迹弹窗
  import MeetingDialogComponent from '@/components/OtherFormDialogComponent/MeetingDialogComponent/index.vue' //会议纪要弹窗
  import SafeStandardDialogComponent from '@/components/OtherFormDialogComponent/SafeStandardDialogComponent/index.vue' //安全标准弹窗
  import OutFleetAuditSettingAddDialogComponent from '@/components/OtherFormDialogComponent/OutFleetAuditSettingAddDialogComponent/index.vue' //审批设置弹窗
  import DriverEditSubsidyComponent from '@/components/OtherFormDialogComponent/DriverEditSubsidyComponent/index.vue' //司机计算补贴修改费用弹窗
  import BatchScheduleDialogComponent from '@/components/OtherFormDialogComponent/DispatchDialogComponent/BatchScheduleDialogComponent/index.vue' //批量调度
  import EmptyDispatchDialogComponent from '@/components/OtherFormDialogComponent/DispatchDialogComponent/EmptyDispatchDialogComponent/index.vue' //空调度单
  import GenerateSettlementDialogComponent from '@/components/OtherFormDialogComponent/GenerateSettlementDialogComponent/index.vue' //生成结算单弹窗
  import GenerateStatementDialogComponent from '@/components/OtherFormDialogComponent/GenerateStatementDialogComponent/index.vue' //生成对账单弹窗
  import GenerateRepairStatementDialogComponent from '@/components/OtherFormDialogComponent/GenerateRepairStatementDialogComponent/index.vue' //生成维修对账单弹窗
  import GenerateSubsidyStatementDialogComponent from '@/components/OtherFormDialogComponent/GenerateSubsidyStatementDialogComponent/index.vue' //生成外协对账单弹窗
  import OpenInvoiceConfirmDialogComponent from '@/components/OtherFormDialogComponent/OpenInvoiceConfirmDialogComponent/index.vue' //发票管理弹窗
  import OpenInvoiceRepairConfirmDialogComponent from '@/components/OtherFormDialogComponent/OpenInvoiceRepairConfirmDialogComponent/index.vue' //发票管理弹窗
  import QualityLossDialogComponent from '@/components/OtherFormDialogComponent/QualityLossDialogComponent/index.vue' //质损信息弹窗
  import OpenInvoiceOutsourcingConfirmDialogComponent from '@/components/OtherFormDialogComponent/OpenInvoiceOutsourcingConfirmDialogComponent/index.vue' //外协发票管理弹窗
  import AccidentManageDialogComponent from '@/components/OtherFormDialogComponent/AccidentManageDialogComponent/index.vue' //事故管理弹窗
  import SubsidyPaymentDetailDialogComponent from '@/components/OtherFormDialogComponent/SubsidyPaymentDetailDialogComponent/index.vue' //补贴付款-打款弹窗
  import FineDialogComponent from '@/components/OtherFormDialogComponent/PenaltyConfiguration/index.vue' //罚款配置信息弹窗
  import OutsourcePaymentDialogComponent from '@/components/OtherFormDialogComponent/OutsourcePaymentDialogComponent/index.vue' //外协结算-付款管理-付款信息
  import OutsourcePaymentDetailDialogComponent from '@/components/OtherFormDialogComponent/OutsourcePaymentDetailDialogComponent/index.vue' //外协结算-付款管理-已支付金额明细
  import ChangeTimeDialogComponent from '@/components/OtherFormDialogComponent/DispatchDialogComponent/BatchScheduleDialogComponent/components/changeTimeDialogComponent.vue' //变更时间弹窗
  import ResignationEmployeeManagementDialogComponent from '@/components/OtherFormDialogComponent/resignationEmployeeManagementDialogComponent/index.vue' //离职弹窗
  import FleetReportDialogComponent from '@/components/OtherFormDialogComponent/FleetReportDialogComponent/index.vue' //车队维修弹窗
  import EmployeeManagementDialogComponent from '@/components/OtherFormDialogComponent/EmployeeManagementDialogComponent/index.vue' //工资管理弹窗
  import DynamicDetailDialogComponent from '@/components/OtherFormDialogComponent/DynamicDetailDialogComponent/index.vue' //动态审批详情弹窗
  import OrderTemplateConfigDialogComponent from '@/components/OtherFormDialogComponent/OrderTemplateConfigDialogComponent/index.vue' //订单模版配置弹窗
  import UnBindTireDialogComponent from '@/components/OtherFormDialogComponent/unBindTireDialogComponent/index.vue' //轮胎列表-解绑司机弹窗
  import ModifyCustomerIncomeDialogComponent from '@/components/OtherFormDialogComponent/ModifyCustomerIncomeDialogComponent/index.vue' //客户结算-运费计算-调整收款弹窗
  import InspectionConfigurationDialogComponent from '@/components/OtherFormDialogComponent/InspectionConfigurationDialogComponent/index.vue' //点检标准弹窗
  import OrderManagementDialogComponent from '@/components/OtherFormDialogComponent/OrderManagementDialogComponent/index.vue' //订单管理弹窗
  import LadderPriceComponent from '@/components/OtherFormDialogComponent/LadderPriceComponent/index.vue' //客户阶梯价格弹窗
  import OutsourcePriceDialogComponent from '@/components/OtherFormDialogComponent/OutsourcePriceDialogComponent/index.vue' //客户阶梯价格弹窗
  import ReconciliationamoutComponent from '@/components/OtherFormDialogComponent/ReconciliationamoutComponent/index.vue' // 编辑对账金额
  import ReconciliationAndCollectionOfComponent from '@/components/OtherFormDialogComponent/ReconciliationAndCollectionOfAccounts/index.vue'
  import ReconciliationPaymentComponent from '@/components/OtherFormDialogComponent/ReconciliationPayment/index.vue'
  import vehicleHistoryMileageEditComponent from '@/components/OtherFormDialogComponent/vehicleHistoryMileageEditComponent/index.vue'
  import OilExpenseListBorrowComponent from '@/components/OtherFormDialogComponent/OilExpenseListBorrowComponent/index.vue'
  import EtcExpenseListBorrowComponent from '@/components/OtherFormDialogComponent/EtcExpenseListBorrowComponent/index.vue'
  import OneclickDialogComponent from '@/components/OtherFormDialogComponent/DispatchDialogComponent/OneclickDialogComponent/index.vue'
  import AddOnRouteMaintenanceDialogComponent from '@/components/OtherFormDialogComponent/OutboundMaintenanceManagement/AddOnRouteMaintenanceDialogComponent/index.vue'
  import LoadDetailDialogComponent from '@/components/OtherFormDialogComponent/DispatchDialogComponent/BatchScheduleDialogComponent/components/loadDetailDialogComponent.vue'
  // 导入失败组件 ImportFailedComponent
  import ImportFailedComponent from '@/components/OtherFormDialogComponent/ImportFailedComponent/index.vue'
  // 导入成功组件 ImportSuccessComponent
  import ImportSuccessComponent from '@/components/OtherFormDialogComponent/ImportSuccessComponent/index.vue'
  import {
    appointmentApi,
    appointmentVehicleShipmentInfoApi,
    postFormApi,
    printPickUpOrderInfo,
    refreshCheckGetApi,
    refreshCheckPostApi,
    shipmentOrderConfirmCheckApi,
    shipmentOrderConfirmSubmitApi,
  } from '@/api/shipmentManagement'
  import { shipmentOrderConfirmSubmitVO } from '@/api/shipmentManagement/type'
  import { ExchangeVehicleParamsVo } from '@/api/planManagement/type'
  import { dayjs, ElForm } from 'element-plus'
  import { examineListPublishApi } from '@/api/supplierManagement'
  import { getSafetyPatorlPathDetailApi, getSafetyStandardTemplateViewDetailApi, postSafetyStandardTemplateSaveApi } from '@/api/safetyManagement'
  import { StandardTemplateSaveVO } from '@/api/safetyManagement/types'
  import { usePatrolPathListStore } from '@/store/modules/patrolPathList'
  import { CheckedMenuColumnVO, UserForm, UserPageVO } from '@/api/authorityManagement/CorporateAccountManagement/PersonalManagement/types'
  import {
    currentLoginUserMenuColumnlist,
    distributeMenuColumn,
    updateUserPassword,
    userMenulist,
  } from '@/api/authorityManagement/CorporateAccountManagement/PersonalManagement'
  //菜单弹窗数据start
  import menuDialog from '@/views/authorityManagement/CorporateAccountManagement/PersonalManagement/components/menuDialog.vue'
  import dataColumnMenuDialog from '@/views/GlobalMenu/permissionManagement/RoleManagement/components/menuDialog.vue'
  import { typeConversion } from '@/utils'
  import bus from '@/utils/bus'
  import { getWarehouseMoveInventoryPlanDetailApi } from '@/api/ykp'
  import { listMenuOptions, setMenus } from '@/api/authorityManagement/RoleManagement'
  import defaultSettings from '@/settings'
  import { downloadCustomTemplate } from '@/api/outboundDispatchManagement'
  import { FullScreen } from '@element-plus/icons-vue'
  import VxeTableComponent from '@/components/VxeTableComponent/index.vue'
  // *****************************以上是个别操作按钮弹窗导入代码，需要根据业务需求修改**********************
  const sideBarStore = useSideBarStore()
  const patrolPathListStore = usePatrolPathListStore()
  const formStore = useFormStore()
  const settingsStore = useSettingsStore()
  const router = useRouter()
  const query = reactive(router.currentRoute.value.query)
  const params = reactive(router.currentRoute.value.params)

  //如果地址栏有携带的参数并且不包含time，说明是工程外部跳转，则存到store中
  if (JSON.stringify(query) != '{}' && !Object.keys(query).includes('time')) {
    formStore.$patch((state) => {
      state.routerParams = query
    })
  }
  const { routerParams, defaultTableIds, mergeFormData, storeFormParams } = storeToRefs(formStore)
  const { mergeDialogFormParams } = storeToRefs(sideBarStore)
  const { patrolPathList } = storeToRefs(patrolPathListStore)
  const { proxy }: any = getCurrentInstance()
  //优先使用列表中的携带的参数，再使用地址栏参数
  const routeParams =
    proxy.$sideBarStore.$state.btnMenuQuery && JSON.stringify(proxy.$sideBarStore.$state.btnMenuQuery) != '{}'
      ? proxy.$sideBarStore.$state.btnMenuQuery
      : routerParams.value
  const content = ref()
  const topQueryGroupComponentRef = ref()
  const topQueryCardRef = ref()
  const topButtonGroupComponentRef = ref()
  const statisticRef = ref()
  const importFailedRef = ref() //导入失败
  const importSuccessRef = ref() //导入成功
  const state = reactive({
    isShowMore: false, // 控制展开更多的显示与隐藏
    textHeight: '', // 框中内容的高度
    status: false, // 内容状态是否打开
    tableHeight: '60vh', // 表格高度
  })
  defineOptions({
    name: 'OutboundAdministrativeManagementSalaryManagement',
    inheritAttrs: false,
  })
  // 监听内容高度的变化
  onMounted(() => {
    nextTick(() => {
      state.tableHeight =
        document.body.clientHeight -
        (topButtonGroupComponentRef.value ? topButtonGroupComponentRef.value.offsetHeight : 0) -
        (statisticRef.value ? statisticRef.value?.offsetHeight : 0) -
        320 +
        'px'
    })
  })
  //查看图片组件
  const picDialogComponent = ref()
  const formDialogRef = ref(ElForm)
  const queryParams = reactive<any>({
    page: 1,
    limit: defaultSettings.globalLimit,
    customerOrderBy: undefined, //排序
  })
  const printBillComponentRef = ref()
  const printTableComponentRef = ref()
  const tableComponentRef = ref()
  /**
   * 统计组件数据
   */
  const statisticColumn = ref<StatisticColumnVO[]>()
  /**
   * 查看列表项详情弹窗
   */
  const detailDialogVisible = reactive<DialogOption>({
    visible: false,
    title: '详情',
    dialogWidth: '80%',
  })
  const detailBaseTabPageDialogVisible = reactive<DialogOption>({
    visible: false,
    title: '详情',
    dialogWidth: '80%',
  })
  /**
   * 查看列表项对象弹窗
   */
  const objectDialogVisible = reactive<DialogOption>({
    visible: false,
    title: '详情',
  })
  /**
   * 查看列表项对象弹窗
   */
  const basePageDialogVisible = reactive<DialogOption>({
    visible: false,
    title: '规划详情',
  })
  /**
   * 查看图片弹窗
   */
  const picDialogVisible = ref<boolean>(false)
  /**
   * 当前菜单的uri
   */
  const currentMenuUri = ref<string>()
  /**
   * menu
   */
  const btnMenu = ref<MenuVO>()
  /**
   * 按钮组
   */
  const btnGroup = ref<MenuVO[]>([])
  /**
   * 选择的列表项数据
   */
  const selectTableColumn = ref<any[]>([])
  /**
   * 按钮下的表单
   */
  const operationColumn = ref<any>()
  /**
   * 按钮的请求地址前缀
   */
  const btnRequestUri = ref<string | null>()
  //图片地址
  const imageList = ref<string[]>([])
  /**
   * 导出加载动画
   */
  const exportButtonLoading = ref<boolean>()
  /**
   * 导入加载动画
   */
  const importButtonLoading = ref<boolean>()
  /**
   * 下载加载动画
   */
  const downloadButtonLoading = ref<boolean>()

  /**
   * 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   */
  const buttonPosition = ref<string>()
  /**
   * 请求地址前缀
   */
  const requestUri = ref<string>()
  /**
   * 加载状态
   */
  const loading = ref(false)
  /**
   * 全局pageLoading
   */
  const pageLoading = ref(false)
  const pageLoadingText = ref('加载中。。。')
  /**
   * 选中的列表ids
   */
  const ids = ref<string[]>([])
  /**
   * 总数
   */
  const total = ref(0)

  /**
   * 当前是否为编辑状态
   */
  const isEdit = ref(false)
  const isCopy = ref(false)
  const listRightOperation = ref<MenuVO[]>() //列表右侧按钮权限
  const listTopOperation = ref<MenuVO[]>() //列表顶部按钮权限
  const showSearch = ref(false) //顶部搜索区域是否展示查询重置按钮
  /**
   * 新增or编辑弹窗
   */
  const dialog = reactive<DialogOption>({
    visible: false,
    dialogWidth: '80%',
  })
  const isExpandAll = ref(false) //是否展开table
  const tableBorder = ref(true) //是否展开table
  /**
   * meta
   */
  const metaInfo = ref<MetaVO>()
  /********************************自定义表头部分 start********************************/
  const customizeHeaderRef = ref()
  const transferDataList = ref() //所有数据列
  const tableItemDialogVisible = ref<boolean>(false) //显隐弹框
  //  显示列弹框
  async function setCustomTableHeader(position: string, type: string, menu: MenuVO) {
    tableItemDialogVisible.value = true
    await nextTick(async () => {
      await customizeHeaderRef.value.getFormsTemplateTypeSelectOptions()
      customizeHeaderRef.value.params.type = type === 'customTableBusinessHeader' ? '1' : '2'
      customizeHeaderRef.value.showDialog = true
      await customizeHeaderRef.value.getMainDimension()
      customizeHeaderRef.value.title = menu.meta?.title
      customizeHeaderRef.value.isTemplate = false
      //获取模板下拉
      await getTemplateList(customizeHeaderRef.value.params.type)
    })
  }

  //  显示报表模板弹窗
  async function setCustomTableTemplateHeader(position: string, type: string, menu: MenuVO) {
    tableItemDialogVisible.value = true
    await nextTick(async () => {
      customizeHeaderRef.value.params.type = type === 'customTableBusinessHeader' ? '1' : '2'
      await customizeHeaderRef.value.getFormsTemplateTypeSelectOptions()
      await customizeHeaderRef.value.getMainDimension()
      customizeHeaderRef.value.title = menu.meta?.title
      customizeHeaderRef.value.showDialog = true
      customizeHeaderRef.value.isTemplate = true
    })
  }

  //编辑报表模板弹窗
  async function customTableTemplateHeader(row: any, position: string, menu: MenuVO) {
    tableItemDialogVisible.value = true
    await nextTick(async () => {
      if (typeof row.type === 'number') {
        customizeHeaderRef.value.params.type = row.type.toString()
      }
      customizeHeaderRef.value.selectTemplate(row.id)
      await customizeHeaderRef.value.getFormsTemplateTypeSelectOptions()
      await customizeHeaderRef.value.getMainDimension()
      customizeHeaderRef.value.title = menu.meta?.title
      customizeHeaderRef.value.showDialog = true
      customizeHeaderRef.value.isTemplate = true
      customizeHeaderRef.value.params.id = row.id
      customizeHeaderRef.value.params.name = row.name
      customizeHeaderRef.value.params.remark = row.remark
    })
  }

  //编辑考核模板弹窗
  async function editExamineTemplate(row: any, position: string, menu: MenuVO) {
    examineTemplateDialogRef.value.examineTemplateDialogVisible.visible = true
    examineTemplateDialogRef.value.examineTemplateDialogVisible.title = menu?.meta.title
    examineTemplateDialogRef.value.examineTemplateDetail(row)
  }

  //编辑考核项目弹窗
  async function editExamineProject(row: any, position: string, menu: MenuVO) {
    examineProjectDialogRef.value.examineProjectDialogVisible.visible = true
    examineProjectDialogRef.value.examineProjectDialogVisible.title = menu?.meta.title
    examineProjectDialogRef.value.examineTemplateDetail(row)
  }

  //显示规划详情弹窗
  const transportPlanDetailFun = (query: any, column: TableItem) => {
    basePageDialogVisible.visible = true
    var title = column.label
    if (column.jump) {
      title = column.jump!.title ?? column.label
    }
    basePageDialogVisible.title = title
  }

  //新增审批流程弹窗
  const outFleetAuditSettingAdd = (position: string, purpose: string, menu: MenuVO) => {
    outFleetAuditSettingAddDialog.visible = true
    outFleetAuditSettingAddDialog.title = menu?.meta.title
    outFleetAuditSettingAddDialogRef.value.getDetail('add')
  }
  //编辑审批流程
  const outFleetAuditSettingEdit = (row: any, position: string, menu: MenuVO) => {
    outFleetAuditSettingAddDialog.visible = true
    outFleetAuditSettingAddDialog.title = menu?.meta.title
    outFleetAuditSettingAddDialogRef.value.getDetail('edit', row)
  }
  //复制审批流程
  const outFleetAuditSettingCopy = (row: any, position: string, menu: MenuVO) => {
    outFleetAuditSettingAddDialog.visible = true
    outFleetAuditSettingAddDialog.title = menu?.meta.title
    outFleetAuditSettingAddDialogRef.value.getDetail('copy', row)
  }

  //司机计算补贴费用编辑
  const editSubsidy = (row: any, position: string, menu: MenuVO) => {
    driverEditSubsidyDialog.visible = true
    driverEditSubsidyDialog.title = menu?.meta.title
    driverEditSubsidyDialogRef.value.dispatchId = row.id
    driverEditSubsidyDialogRef.value.getDetail(row.id)
  }

  //编辑调度
  const editDispatch = (row: any, position: string, menu: MenuVO) => {
    batchScheduleDialogRef.value.state.editScheduleDialogVisible = true
    batchScheduleDialogRef.value.state.currentDispatchNo = row.dispatchNo
    batchScheduleDialogRef.value.state.currentRow = row
    batchScheduleDialogRef.value.showScheduleDetail(row.id, row.dispatchNo, 'edit')
    batchScheduleDialogRef.value.state.showDispatchBtns = true
    batchScheduleDialogRef.value.state.isDispatch = row.isDispatch ? row.isDispatch : false //只有true和undefined
  }
  //编辑生成结算单
  const editGenerateSettlement = (row: any, position: string, menu: MenuVO) => {
    generateSettlementDialogRef.value.getFleetOrderSettlementGetSettlementInfo(row.id)
    generateSettlementDialogRef.value.state.dialogVisible.title = '编辑驾驶员结算单'
    generateSettlementDialogRef.value.state.dialogVisible.visible = true
    generateSettlementDialogRef.value.state.isView = false
  }
  //查看生成结算单
  const viewGenerateSett = (row: any, position: string, menu: MenuVO) => {
    generateSettlementDialogRef.value.state.isView = true
    generateSettlementDialogRef.value.getFleetOrderSettlementGetSettlementInfo(row.id)
    generateSettlementDialogRef.value.state.dialogVisible.title = '查看驾驶员结算单'
    generateSettlementDialogRef.value.state.dialogVisible.visible = true
  }
  //编辑生成对账单
  const editGenerateStatement = (row: any, position?: string, menu?: MenuVO) => {
    generateStatementDialogRef.value.postFleetSettlementCustomerReconciliationManagementCompute(row)
    generateStatementDialogRef.value.state.dialogVisible.title = '编辑对账单'
    generateStatementDialogRef.value.state.dialogVisible.visible = true
    generateStatementDialogRef.value.state.isView = false
  }
  //编辑维修对账单
  const editRepairStatement = (row: any, position: string, menu: MenuVO) => {
    generateRepairStatementDialogRef.value.postFleetSettlementRepairManagementCompute(row)
    generateRepairStatementDialogRef.value.state.dialogVisible.title = '编辑对账单'
    generateRepairStatementDialogRef.value.state.dialogVisible.visible = true
    generateRepairStatementDialogRef.value.state.isView = false
  }
  //查看对账单
  const viewGenerateStatement = (row: any, position?: string, menu?: MenuVO) => {
    generateStatementDialogRef.value.postFleetSettlementCustomerReconciliationManagementCompute(row)
    generateStatementDialogRef.value.state.dialogVisible.title = '查看对账单'
    generateStatementDialogRef.value.state.dialogVisible.visible = true
    generateStatementDialogRef.value.state.isView = true
  }
  //查看维修对账单
  const viewRepairGenerateStatement = (row: any, position: string, menu: MenuVO) => {
    generateRepairStatementDialogRef.value.postFleetSettlementRepairManagementCompute(row)
    generateRepairStatementDialogRef.value.state.dialogVisible.title = '查看对账单'
    generateRepairStatementDialogRef.value.state.dialogVisible.visible = true
    generateRepairStatementDialogRef.value.state.isView = true
  }
  //编辑生成外协对账单
  const editGenerateSubsidyStatement = (row: any, position: string, menu: MenuVO) => {
    generateSubsidyStatementDialogRef.value.getFleetOrderCarrierCostComputeSummaryDetail(row)
    generateSubsidyStatementDialogRef.value.state.dialogVisible.title = '编辑结算账单'
    generateSubsidyStatementDialogRef.value.state.dialogVisible.visible = true
    generateSubsidyStatementDialogRef.value.state.isView = false
    generateSubsidyStatementDialogRef.value.tableConfig.showHandleSelection = true
  }
  //查看外协对账单
  const viewGenerateSubsidyStatement = (row: any, position: string, menu: MenuVO) => {
    generateSubsidyStatementDialogRef.value.getFleetOrderCarrierCostComputeSummaryDetail(row)
    generateSubsidyStatementDialogRef.value.state.dialogVisible.title = '查看结算账单'
    generateSubsidyStatementDialogRef.value.state.dialogVisible.visible = true
    generateSubsidyStatementDialogRef.value.state.isView = true
    generateSubsidyStatementDialogRef.value.tableConfig.showHandleSelection = false
  }
  //编辑开票管理
  const editOpenInvoiceManagment = (row: any, position: string, menu: MenuVO) => {
    openInvoiceConfirmDialogRef.value.postFleetSettlementCustomerInvoicesManagementAll(row)
    openInvoiceConfirmDialogRef.value.postFleetSettlementCustomerInvoicesManagementAvalable(row)
    openInvoiceConfirmDialogRef.value.state.dialogVisible = true
  }
  //维修编辑开票管理
  const editOpenInvoiceRepairManagement = (row: any, position: string, menu: MenuVO) => {
    openInvoiceRepairConfirmDialogRef.value.postFleetSettlementRepairInvoicesManagementAll(row)
    openInvoiceRepairConfirmDialogRef.value.postFleetSettlementRepairInvoicesManagementAvalable(row)
    openInvoiceRepairConfirmDialogRef.value.state.dialogVisible = true
  }
  //外协编辑开票管理
  const editOpenInvoiceOutsourcingManagement = (row: any, position: string, menu: MenuVO) => {
    openInvoiceOutsourcingConfirmDialogRef.value.postOutsourcingFleetSettlementCustomerInvoicesManagementAll(row)
    openInvoiceOutsourcingConfirmDialogRef.value.state.dialogVisible = true
  }
  //编辑罚款配置信息
  const editFine = (row: any, position: string, menu: MenuVO) => {
    fineDialogRef.value.editItem(row)
    fineDialogRef.value.state.dialogVisible.title = '编辑罚款配置信息'
    fineDialogRef.value.state.dialogVisible.visible = true
  }
  //质损编辑
  const editQualityLoss = (row: any, position: string, menu: MenuVO) => {
    qualityLossDialogRef.value.state.dialogVisible.visible = true
    qualityLossDialogRef.value.state.dialogVisible.title = menu?.meta.title
    qualityLossDialogRef.value.state.origin = 'edit'
    qualityLossDialogRef.value.getStep1Detail(row.id)
    qualityLossDialogRef.value.state.qualityLossId = row.id
    qualityLossDialogRef.value.stepIndex = '1'
    qualityLossDialogRef.value.state.currentRow = row
  }
  //客户合同阶梯价格
  const formulaCustomerContract = (row: any, position: string, menu: MenuVO) => {
    ladderPriceDialogRef.value.state.dialogVisible.visible = true
    ladderPriceDialogRef.value.state.dialogVisible.title = menu?.meta.title
    ladderPriceDialogRef.value.formList.contractId = row.contractId
    ladderPriceDialogRef.value.formList.ladderPriceCalculationType = row.ladderPriceCalculationType || '拆分计算'
    ladderPriceDialogRef.value.formList.contractDetailId = row.id
    ladderPriceDialogRef.value.row = row
    ladderPriceDialogRef.value.getDetail(row.id)
  }
  //外协合同阶梯价格
  const formulaOutsourcingContract = (row: any, position: string, menu: MenuVO) => {
    outsourcePriceDialogRef.value.state.dialogVisible.visible = true
    outsourcePriceDialogRef.value.state.dialogVisible.title = menu?.meta.title
    outsourcePriceDialogRef.value.formList.contractId = row.contractId
    outsourcePriceDialogRef.value.formList.ladderPriceCalculationType = row.ladderPriceCalculationType || '拆分计算'
    outsourcePriceDialogRef.value.formList.contractDetailId = row.id
    outsourcePriceDialogRef.value.row = row
    outsourcePriceDialogRef.value.getDetail(row.id)
  }

  // 订单列表-编辑对账金额
  const editSettlementAmount = (row: any, position: string, menu: MenuVO) => {
    reconciliationAmount.value.state.dialogVisible.visible = true
    reconciliationAmount.value.state.dialogVisible.title = menu?.meta.title
    reconciliationAmount.value.state.uri = menu?.meta.uri
    reconciliationAmount.value.state.settlementAmount = row.settlementAmount
    reconciliationAmount.value.state.id = row.id
  }

  // 批量对账
  const scatteredReceiveSettlement = (row: any, position: string, menu: MenuVO) => {
    reconciliationAccounts.value.state.dialogVisible.visible = true
    reconciliationAccounts.value.state.dialogVisible.title = menu?.meta.title
    // reconciliationAccounts.value.getDetail(row.id)
    reconciliationAccounts.value.dropDownFun(row.id)
  }
  // 油费借支
  const oilLoanAdd = (row: any, position: string, menu: MenuVO) => {
    oilExpenseListBorrowDialogRef.value.state.oilBorrowDialogVisible = true
    oilExpenseListBorrowDialogRef.value.state.status = 'add'
  }
  // 油费借支
  const oilLoanEdit = (row: any, position: string, menu: MenuVO) => {
    oilExpenseListBorrowDialogRef.value.state.oilBorrowDialogVisible = true
    oilExpenseListBorrowDialogRef.value.state.status = 'edit'
    oilExpenseListBorrowDialogRef.value.getDetail(row.id)
  }
  // ETC借支
  const etcLoanAdd = (row: any, position: string, menu: MenuVO) => {
    etcExpenseListBorrowDialogRef.value.state.tollBorrowDialogVisible = true
    etcExpenseListBorrowDialogRef.value.state.status = 'add'
  }
  //智能调度-一键换车
  const quickChangeVehicle = () => {
    OneclickDialog.visible = true
  }
  // 在途维修-新增
  const addOnRouteMaintenance = (row: any, position: string, menu: MenuVO) => {
    addOnRouteMaintenanceDialogRef.value.state.dialogVisible.visible = true
    addOnRouteMaintenanceDialogRef.value.state.dialogVisible.title = menu?.meta.title
    addOnRouteMaintenanceDialogRef.value.state.origin = 'add'
  }
  // ETC借支
  const etcLoanEdit = (row: any, position: string, menu: MenuVO) => {
    etcExpenseListBorrowDialogRef.value.state.tollBorrowDialogVisible = true
    etcExpenseListBorrowDialogRef.value.state.status = 'edit'
    etcExpenseListBorrowDialogRef.value.getDetail(row.id)
  }
  //查看调度单详情
  const viewDispatch = (row: any, position: string, menu: MenuVO) => {
    batchScheduleDialogRef.value.state.editScheduleDialogVisible = true
    batchScheduleDialogRef.value.state.currentDispatchNo = row.dispatchNo
    batchScheduleDialogRef.value.state.currentRow = row
    batchScheduleDialogRef.value.showScheduleDetail(row.id, row.dispatchNo, 'view')
    batchScheduleDialogRef.value.state.showDispatchBtns = true
    batchScheduleDialogRef.value.state.isDispatch = row.isDispatch ? row.isDispatch : false //只有true和undefined
  }
  // table弹窗（只展示列表）
  const tableDetailDialog = (row: any, column: TableItem) => {
    loadDetailDialogRef.value.state.loadDetailDialogVisible = true
    const data = row[column.name.slice(0, -6)].split(';')
    loadDetailDialogRef.value.state.loadDetailData = data.map((item: any) => {
      return {
        address: item.split(':')[0],
        loadCount: item.split(':')[1],
      }
    })
  }
  // 编辑维修单
  const editOnRouteMaintenance = async (row: any, position: string, menu: MenuVO) => {
    addOnRouteMaintenanceDialogRef.value.state.dialogVisible.visible = true
    addOnRouteMaintenanceDialogRef.value.state.dialogVisible.title = menu?.meta.title
    addOnRouteMaintenanceDialogRef.value.state.origin = 'edit'
    const data = await getUserDetail(row.id)
    addOnRouteMaintenanceDialogRef.value.state.form = data
    addOnRouteMaintenanceDialogRef.value.setUploadImageList(data.imageUrlList)
    addOnRouteMaintenanceDialogRef.value.tableData = data.items
    addOnRouteMaintenanceDialogRef.value.remoteSelectMethod(data.dispatchNo)
  }
  // 查看在途维修-费用单号详情
  const viewOnRouteMaintenance = async (row: any, position: string, menu: MenuVO) => {
    addOnRouteMaintenanceDialogRef.value.state.dialogVisible.visible = true
    addOnRouteMaintenanceDialogRef.value.state.dialogVisible.title = '查看'
    addOnRouteMaintenanceDialogRef.value.state.origin = 'view'
    const data = await getUserDetail(row.id)
    addOnRouteMaintenanceDialogRef.value.state.form = data
    addOnRouteMaintenanceDialogRef.value.setUploadImageList(data.imageUrlList)
    addOnRouteMaintenanceDialogRef.value.tableData = data.items
    addOnRouteMaintenanceDialogRef.value.remoteSelectMethod(data.dispatchNo)
  }
  // 批量对账
  const scatteredReceivePayment = (row: any, position: string, menu: MenuVO) => {
    scatteredReceivePaymentref.value.state.dialogVisible.visible = true
    scatteredReceivePaymentref.value.state.dialogVisible.title = menu?.meta.title
    scatteredReceivePaymentref.value.dropDownFun(row.id)
  }

  // 里程管理-历史-编辑
  const vehicleHistoryMileageEdit = (position: any, purpose: string, menu: MenuVO) => {
    vehicleHistoryMileageEditRef.value.state.dialogVisible.visible = true
    vehicleHistoryMileageEditRef.value.state.dialogVisible.title = menu?.meta.title
    vehicleHistoryMileageEditRef.value.state.page = topQueryGroupComponentRef.value.queryParams.page
    vehicleHistoryMileageEditRef.value.state.plateColor = topQueryGroupComponentRef.value.queryParams.plateColor
    vehicleHistoryMileageEditRef.value.state.vehicleNo = topQueryGroupComponentRef.value.queryParams.vehicleNo
    vehicleHistoryMileageEditRef.value.getMonth(topQueryGroupComponentRef.value.queryParams.vehicleNo)
    // closeDetailDialogVisible('1')
  }

  //外协付款编辑
  const editOutsourcingPayment = (row: any, position: string, menu: MenuVO) => {
    outsourcePaymentDialogRef.value.state.dialogVisible.visible = true
    outsourcePaymentDialogRef.value.state.dialogVisible.title = menu?.meta.title
    outsourcePaymentDialogRef.value.state.origin = 'edit'
    outsourcePaymentDialogRef.value.getDetail(row.id)
  }
  //补贴付款编辑
  const editSubsidyPayment = (row: any, position: string, menu: MenuVO) => {
    subsidyPaymentDialogRef.value.state.formData.cashPaymentAmount = row.cashPaymentAmount
    subsidyPaymentDialogRef.value.state.formData.cashHavePaymentAmount = row.cashHavePaymentAmount
    subsidyPaymentDialogRef.value.state.formData.cashHaveNotPaymentAmount = row.cashHaveNotPaymentAmount
    subsidyPaymentDialogRef.value.state.formData.oilPaymentAmount = row.oilPaymentAmount
    subsidyPaymentDialogRef.value.state.formData.oilHavePaymentAmount = row.oilHavePaymentAmount
    subsidyPaymentDialogRef.value.state.formData.oilHaveNotPaymentAmount = row.oilHaveNotPaymentAmount
    subsidyPaymentDialogRef.value.state.dialogVisible.visible = true
    subsidyPaymentDialogRef.value.state.dialogVisible.title = menu?.meta.title
    subsidyPaymentDialogRef.value.state.origin = 'edit'
    subsidyPaymentDialogRef.value.getDetail(row.id)
  }

  //外协付款明细
  const paymentPart = (row: any, position: string, menu: MenuVO) => {
    outsourcePaymentDetailDialogRef.value.state.id = row.id
    outsourcePaymentDetailDialogRef.value.getDetail(row.id)
    outsourcePaymentDetailDialogRef.value.state.dialogVisible.visible = true
    outsourcePaymentDetailDialogRef.value.state.havePaymentAmount = row.havePaymentAmount
  }
  //离职
  const resignationEmployeeManagement = async (row: any, position: string, menu: MenuVO) => {
    resignationEmployeeManagementDialogRef.value.state.dialogVisible.visible = true
    resignationEmployeeManagementDialogRef.value.state.dialogVisible.title = menu?.meta.title
    resignationEmployeeManagementDialogRef.value.state.userInfo = await getUserDetail(row.id)
  }
  //车队维修
  const editFleetReport = async (row: any, position: string, menu: MenuVO) => {
    fleetReportDialogRef.value.state.dialogVisible.visible = true
    fleetReportDialogRef.value.state.dialogVisible.title = menu?.meta.title
    fleetReportDialogRef.value.state.formData.id = row.id
    fleetReportDialogRef.value.state.isView = false
  }
  //查看车队维修
  const viewFleetReport = async (row: any, position: string, menu: MenuVO) => {
    fleetReportDialogRef.value.state.dialogVisible.visible = true
    fleetReportDialogRef.value.state.dialogVisible.title = menu?.meta.title
    fleetReportDialogRef.value.state.formData.id = row.id
    fleetReportDialogRef.value.state.isView = true
  }
  //供应商维修
  const editSupplierRepair = async (row: any, position: string, menu: MenuVO) => {
    fleetReportDialogRef.value.state.dialogVisible.visible = true
    fleetReportDialogRef.value.state.dialogVisible.title = menu?.meta.title
    fleetReportDialogRef.value.state.id = row.id
    fleetReportDialogRef.value.state.isView = false
    fleetReportDialogRef.value.getDetail(row.id)
  }
  //调整费用供应商维修
  const adjustCostSupplierRepair = async (row: any, position: string, menu: MenuVO) => {
    fleetReportDialogRef.value.state.dialogVisible.visible = true
    fleetReportDialogRef.value.state.dialogVisible.title = menu?.meta.title
    fleetReportDialogRef.value.state.id = row.id
    fleetReportDialogRef.value.getDetail(row.id)
    fleetReportDialogRef.value.state.origin = 'adjustCost'
    fleetReportDialogRef.value.state.isView = false
  }
  //编辑工资管理
  const editEmployeeManagement = async (row: any, position: string, menu: MenuVO) => {
    employeeManagementDialogRef.value.state.dialogVisible.visible = true
    employeeManagementDialogRef.value.state.dialogVisible.title = menu?.meta.title
    employeeManagementDialogRef.value.state.userInfo = await getUserDetail(row.id)
  }
  //动态详情
  const dynamicAuditDetail = async (row: any, position: string, menu: MenuVO) => {
    currentRow.value = row
    btnMenu.value = menu
    await dynamicAuditDetailDialogRef.value.getDetail(queryParams, row, menu)
    dynamicAuditDetailDialogRef.value.state.dialogVisible.visible = true
  }
  //数据列动态详情
  const columnDynamicAuditDetail = async (row: any, column: TableItem, params: any) => {
    currentRow.value = row
    // 将column.jump赋值给btnMenu.meta.form
    btnMenu.value = {
      meta: {
        form: column.jump as any,
        dataColumn: [],
        menuId: '',
        dependsOn: [],
        pageType: '',
      },
    }
    await dynamicAuditDetailDialogRef.value.getColumnDetail(params, row, column)
    dynamicAuditDetailDialogRef.value.state.dialogVisible.visible = true
  }

  //轮胎列表-解绑
  const unBindTire = async (row: any, position: string, menu: MenuVO) => {
    unBindTireDialogRef.value.state.dialogVisible.visible = true
    unBindTireDialogRef.value.state.dialogVisible.title = menu?.meta.title
    unBindTireDialogRef.value.state.form.id = row.newestRecordId
    unBindTireDialogRef.value.state.form.tireNo = row.no
    unBindTireDialogRef.value.state.form.standardMileage = row.standardMileage || 0
    unBindTireDialogRef.value.state.form.currentVehicle = row.currentVehicle
    unBindTireDialogRef.value.state.form.bindMileage = row.bindMileage || 0
    unBindTireDialogRef.value.state.form.driverInfo = row.driverInfo
    unBindTireDialogRef.value.state.form.unbindMileage = row.unbindMileage
    unBindTireDialogRef.value.state.form.unbindDate = row.unbindDate
    unBindTireDialogRef.value.state.form.currentMileage = row.currentMileage
  }
  //客户结算-运费计算-调整收款
  const modifyCustomerIncome = async (row: any, position: string, menu: MenuVO, type: string) => {
    modifyCustomerIncomeDialogRef.value.state.dialogVisible.visible = true
    modifyCustomerIncomeDialogRef.value.state.dialogVisible.title = menu?.meta.title
    modifyCustomerIncomeDialogRef.value.state.formData.id = row.id
    modifyCustomerIncomeDialogRef.value.state.formData.cashAmount = row.cashAmount || 0
    modifyCustomerIncomeDialogRef.value.state.formData.wjyAmount = row.wjyAmount || 0
    modifyCustomerIncomeDialogRef.value.state.formData.dlAmount = row.dlAmount || 0
    modifyCustomerIncomeDialogRef.value.state.formData.acceptAmount = row.acceptAmount || 0
    modifyCustomerIncomeDialogRef.value.state.formData.transferAmount = row.transferAmount || 0
    modifyCustomerIncomeDialogRef.value.state.formDataRatio.cashRatio = row.cashRatio
    modifyCustomerIncomeDialogRef.value.state.formDataRatio.wjyRatio = row.wjyRatio
    modifyCustomerIncomeDialogRef.value.state.formDataRatio.dlRatio = row.dlRatio
    modifyCustomerIncomeDialogRef.value.state.formDataRatio.acceptRatio = row.acceptRatio
    modifyCustomerIncomeDialogRef.value.state.formDataRatio.transferRatio = row.transferRatio
    modifyCustomerIncomeDialogRef.value.state.outcomeUnTaxed = row.outcomeUnTaxed
    modifyCustomerIncomeDialogRef.value.state.uri = menu?.meta.uri
    modifyCustomerIncomeDialogRef.value.state.type = type
  }
  //客户结算-运费计算-客户在线对比-调整收款-对比
  const compareModifyCustomerIncome = async (row: any, position: string, menu: MenuVO, type: string) => {
    modifyCustomerIncomeDialogRef.value.state.dialogVisible.visible = true
    modifyCustomerIncomeDialogRef.value.state.dialogVisible.title = menu?.meta.title
    modifyCustomerIncomeDialogRef.value.state.formData.id = row.id
    modifyCustomerIncomeDialogRef.value.state.formData.cashAmount = row.contractCashAmount || 0
    modifyCustomerIncomeDialogRef.value.state.formData.wjyAmount = row.contractWjyAmount || 0
    modifyCustomerIncomeDialogRef.value.state.formData.dlAmount = row.contractDlAmount || 0
    modifyCustomerIncomeDialogRef.value.state.formData.acceptAmount = row.contractAcceptAmount || 0
    modifyCustomerIncomeDialogRef.value.state.formData.transferAmount = row.contractTransferAmount || 0
    modifyCustomerIncomeDialogRef.value.state.outcomeUnTaxed = row.contractOutcomeUnTaxed
    modifyCustomerIncomeDialogRef.value.state.uri = menu?.meta.uri
    modifyCustomerIncomeDialogRef.value.state.type = type
  }
  //编辑配置
  const editInspectionConfiguration = async (row: any, position: string, menu: MenuVO) => {
    inspectionConfigurationDialogRef.value.state.dialogVisible.visible = true
    inspectionConfigurationDialogRef.value.state.dialogVisible.title = menu?.meta.title
    inspectionConfigurationDialogRef.value.state.origin = 'edit'
    inspectionConfigurationDialogRef.value.getDetail(row.id)
  }
  //复制配置
  const copyInspectionConfiguration = async (row: any, position: string, menu: MenuVO) => {
    inspectionConfigurationDialogRef.value.state.dialogVisible.visible = true
    inspectionConfigurationDialogRef.value.state.dialogVisible.title = menu?.meta.title
    inspectionConfigurationDialogRef.value.state.origin = 'copy'
    inspectionConfigurationDialogRef.value.getDetail(row.id)
  }
  //订单管理-编辑
  const editOrderManagement = async (row: any, position: string, menu: MenuVO) => {
    orderManagementDialogRef.value.state.dialogVisible.visible = true
    orderManagementDialogRef.value.state.dialogVisible.title = menu?.meta.title
    orderManagementDialogRef.value.state.origin = 'edit'
    orderManagementDialogRef.value.getDetail(row.id)
  }
  // 零散订单-付款对账
  const viewReconciliationPayment = async (row: any, position: string, menu: MenuVO) => {
    scatteredReceivePaymentref.value.state.dialogVisible.visible = true
    scatteredReceivePaymentref.value.state.dialogVisible.title = '对账单号'
    scatteredReceivePaymentref.value.getDateList(row)
  }
  //考核项目发布
  const publish = async (row: any, position: string, menu: MenuVO) => {
    await examineListPublishApi({ id: row.id })
    ElMessage({
      message: '发布成功',
      type: 'success',
    })
    resetQuery()
  }
  //干线排程-排程
  const toLineSchedule = async (row: any, position: string, menu: MenuVO) => {
    //定义传递的参数
    let params = {} as any
    const _queryParams = await topQueryGroupComponentRef.value.searchQueryTemp()
    const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
    menu.meta?.form?.params?.map((_item: any) => {
      composeRequestParams(params, _item, menu, storeDataParams, row, _queryParams)
    })
    toLineScheduleDialogRef.value.dialogVisible.visible = true
    await toLineScheduleDialogRef.value.initMiniMap()
    toLineScheduleDialogRef.value.dialogVisible.title = menu?.meta.title
    toLineScheduleDialogRef.value.loading = true
    await globalRequestUrlApi(params, 'get', menu.meta.uri!).then(async (res) => {
      const { data } = res
      await toLineScheduleDialogRef.value.drawLine(data)
      toLineScheduleDialogRef.value.state.data = data
      toLineScheduleDialogRef.value.state.shipmentNo = params.shipmentNo
      toLineScheduleDialogRef.value.loading = false
    })
  }
  //查看轨迹弹窗
  const toTrackMap = async (row: any, position: string, menu: MenuVO) => {
    //定义传递的参数
    let params = {} as any
    const _queryParams = await topQueryGroupComponentRef.value.searchQueryTemp()
    const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
    menu.meta?.form?.params?.map((_item: any) => {
      composeRequestParams(params, _item, menu, storeDataParams, row, _queryParams)
    })
    toTrackMapDialogRef.value.dialogVisible.visible = true
    await toTrackMapDialogRef.value.initMiniMap()
    toTrackMapDialogRef.value.dialogVisible.title = menu?.meta.title
    toTrackMapDialogRef.value.loading = true
    await globalRequestUrlApi(params, 'get', menu.meta.uri!).then(async (res) => {
      const { data } = res
      toTrackMapDialogRef.value.clearMapOverlays()
      for (const item of data) {
        const index: number = data.indexOf(item)
        await toTrackMapDialogRef.value.drawLine(item, index)
      }

      toTrackMapDialogRef.value.state.shipmentNo = params.shipmentNo
      toTrackMapDialogRef.value.loading = false
    })
  }
  //考核项目打分
  const score = (row: any, position: string, menu: MenuVO) => {
    examineScoreDialogRef.value.examineScoreDialogVisible.visible = true
    examineScoreDialogRef.value.examineScoreDialogVisible.title = menu?.meta.title
    examineScoreDialogRef.value.examineTemplateDetail(row)
  }

  //  关闭显示列弹框
  function handleCloseTableItem() {
    tableItemDialogVisible.value = false
  }

  /** 展开/折叠操作 */
  function toggleExpandAll() {
    tableComponentRef.value.toggleExpandAll()
  }

  // 自定义数据列start
  // // 点击弹框的确认按钮事件，array就是用户自己放在右侧排好序的数组，
  /**
   * @param params 参数
   * @param isTemplate 是否从模板页进入
   * @param isSave 是否保存为模板
   * @param id
   */
  function handleConfirmVisible(params: any, isTemplate: boolean, isSave: boolean, id: string) {
    const uri = isTemplate ? (id ? requestUri.value + '/' + id : requestUri.value) : 'tms/report/forms/business/saveUserUsualIndicator'
    // 保存模板
    if (isSave) {
      globalRequestApi(params, 'post', 'tms/report/forms/template').then(async (res) => {})
    }
    globalRequestApi(params, id ? 'put' : 'post', uri!).then(async (res) => {
      ElMessage.success('操作成功')
      customizeHeaderRef.value.showDialog = false
      tableItemDialogVisible.value = false
      if (isTemplate) {
        //重新请求列表数据
        handleQuery()
      } else {
        //刷新页面更新数据列
        window.location.reload()
      }
    })
  }

  //获取报表模板下拉
  function getTemplateList(type: string | number) {
    return new Promise((resolve, reject) => {
      globalRequestApi({}, 'get', `tms/report/forms/business/template/${type}/select/option`).then(async (res) => {
        const { data } = res
        customizeHeaderRef.value.templateSelectOptions = data
        resolve(data)
      })
    })
  }

  /**
   * 获取设置过的数据列
   */
  function getDefineColumns(): Promise<CustomTableHeaderVO[]> {
    return new Promise((resolve, reject) => {
      globalRequestApi({}, 'get', requestUri.value + '/table/header/user').then(async (res) => {
        const { data } = res
        resolve(data)
      })
    })
  }

  const examineTemplateDialogVisible = reactive<DialogOption>({
    visible: false,
  })
  const examineTemplateDialogRef = ref()
  const examineProjectDialogRef = ref()
  const examineScoreDialogRef = ref()
  const safeStandardDialogRef = ref()
  const inspectionDialogRef = ref()
  const toLineScheduleDialogRef = ref()
  const toTrackMapDialogRef = ref()

  //新建考核模板
  function addExamineTemplate(purpose: string, position?: string, menu?: MenuVO) {
    examineTemplateDialogRef.value.examineTemplateDialogVisible.visible = true
    examineTemplateDialogRef.value.examineTemplateDialogVisible.title = menu?.meta.title
    examineTemplateDialogRef.value.state.formData.id = undefined
  }

  //新建考核项目
  function addExamineProject(purpose: string, position?: string, menu?: MenuVO) {
    examineProjectDialogRef.value.examineProjectDialogVisible.visible = true
    examineProjectDialogRef.value.examineProjectDialogVisible.title = menu?.meta.title
    examineProjectDialogRef.value.resetForm()
  }

  //新建移库计划
  function addYkp(purpose: string, position?: string, menu?: MenuVO) {
    addYkpFormDialogRef.value.dialogVisible.visible = true
    addYkpFormDialogRef.value.dialogVisible.title = menu?.meta.title
    addYkpFormDialogRef.value.state.origin = 'addYkp'
    addYkpFormDialogRef.value.state.row = ''
    addYkpFormDialogRef.value.state.formData.name = ''
    addYkpFormDialogRef.value.state.formData.remark = ''
    // patrolPathListStore.$patch(e => {
    //   e.patrolPathList = [
    //     {
    //       warehouseId: '',
    //       warehouseName: '',
    //       centreCoordinate: '',
    //       placeCoordinateItemList: [],
    //     },
    //   ]; //保存移库计划到全局
    // });
  }

  //新建安全标准
  function addSafeStandard(purpose: string, position?: string, menu?: MenuVO) {
    safeStandardDialogRef.value.dialogVisible.visible = true
    safeStandardDialogRef.value.dialogVisible.title = menu?.meta.title
    safeStandardDialogRef.value.state.origin = 'addSafeStandard'
    safeStandardDialogRef.value.state.row = ''
    safeStandardDialogRef.value.state.formData.name = ''
    safeStandardDialogRef.value.state.formData.remark = ''
    patrolPathListStore.$patch((e) => {
      e.patrolPathList = [
        {
          warehouseId: '',
          warehouseName: '',
          centreCoordinate: '',
          placeCoordinateItemList: [],
        },
      ] //保存巡视路径到全局
    })
  }

  /********************************自定义表头部分 end********************************/

  /**
   * 列表配置项
   */
  const tableConfig = reactive<TableConfig>({
    showHandleSelection: true,
    showSort: true,
    tableItem: [],
    operation: {
      label: '操作',
      items: [],
      width: '120px',
    },
  })
  /**
   * 顶部搜索配置项
   */
  const topQueryConfig = reactive<TableConfig>({
    tableItem: [],
    operation: undefined,
  })
  const printBillData = ref<any>()
  const printTableData = ref<any>()
  const printButtonLoading = ref<boolean>(false)
  const showAllSelection = ref<boolean>(true)
  const rightTipObj = ref<RightTipVO>()
  const rightTipDataObj = ref<RightTipDataVO>()
  const topButtonGroupComponent = ref()

  /**
   * 打印提货单
   */
  const printBillTable = async () => {
    printButtonLoading.value = true
    await getPrintBillData()
    printObj.popTitle = printBillData.value.customerName
    //获取topButtonGroupComponent组件中的printButton下的$el
    const btnPrint = topButtonGroupComponent.value.printButton[0].ref
    //触发btnPrint按钮 的点击事件
    btnPrint.click()
  }
  /**
   * 打印table
   */
  const printTable = async (position: string, type: string, item: MenuVO) => {
    printButtonLoading.value = true
    await getPrintTableData(item)
    //获取topButtonGroupComponent组件中的printButton下的$el
    const btnPrint = topButtonGroupComponent.value.printButton[0].ref
    //触发btnPrint按钮 的点击事件
    btnPrint.click()
  }
  const printObj = reactive({
    id: 'PrintView',
    popTitle: '',
    extraCss: 'https://cdn.bootcdn.net/ajax/libs/animate.css/4.1.1/animate.compat.css, https://cdn.bootcdn.net/ajax/libs/hover.css/2.3.1/css/hover-min.css',
    extraHead: '<meta http-equiv="Content-Language"content="zh-cn"/>',
    async previewBeforeOpenCallback(vue: any) {},
    beforeOpenCallback(vue: { printLoading: boolean }) {},
    openCallback(vue: { printLoading: boolean }) {
      vue.printLoading = false
    },
    closeCallback(vue: any) {},
    clickMounted() {},
  })
  /**
   * 获取打印提货单数据
   */
  const getPrintBillData = () => {
    return new Promise((resolve, reject) => {
      printPickUpOrderInfo({ orderIds: ids.value })
        .then((res: any) => {
          printBillData.value = res.data // 更新数据
          printButtonLoading.value = false
          resolve(res.data) // 确保数据加载后调用 resolve
        })
        .catch((err: any) => {
          printButtonLoading.value = false
          reject(err) // 处理错误
        })
    })
  }
  /**
   * 获取打印table数据
   */
  const getPrintTableData = (item: MenuVO) => {
    return new Promise((resolve, reject) => {
      printTableApi(item.meta?.uri ? item.meta?.uri : requestUri.value!, queryParams)
        .then((res: any) => {
          printTableData.value = res.data // 更新数据
          printButtonLoading.value = false
          resolve(res.data) // 确保数据加载后调用 resolve
        })
        .catch((err: any) => {
          printButtonLoading.value = false
          reject(err) // 处理错误
        })
    })
  }

  // *****************************以下内容为个别页面操作按钮弹窗start*****************************

  //公路发运-申请预约弹窗
  const appointmentDialog = reactive<DialogOption>({
    title: '预约申请',
    visible: false,
  })

  //公路发运-车道预约弹窗
  const laneAppointmentDialog = reactive<DialogOption>({
    title: '车道预约',
    visible: false,
  })

  //公路发运-运单确认-批量录入弹窗
  const batchShipmentConfirmDialog = reactive<DialogOption>({
    title: '批量录入',
    visible: false,
  })

  //公路发运-运单确认-一键确认弹窗
  const shipmentConfirmDialog = reactive<DialogOption>({
    title: '一键确认',
    visible: false,
  })

  //车辆变更弹窗
  const vehicleChangeDialog = reactive<DialogOption>({
    title: '车辆变更',
    visible: false,
  })
  //车辆倒板弹窗
  const flipBoardVisible = reactive<DialogOption>({
    title: '车辆倒板',
    visible: false,
  })
  //会议纪要弹窗
  const meetingVisible = reactive<DialogOption>({
    title: '会议纪要',
    visible: false,
  })
  // 审批设置弹窗
  const outFleetAuditSettingAddDialog = reactive<DialogOption>({
    title: '审批设置',
    visible: false,
  })
  // 司机计算补贴修改费用弹窗
  const driverEditSubsidyDialog = reactive<DialogOption>({
    title: '修改费用',
    visible: false,
  })
  // 批量调度弹窗
  const batchScheduleDialog = reactive<DialogOption>({
    title: '批量调度',
    visible: false,
  })
  // 智能调度-一键换车
  const OneclickDialog = reactive<DialogOption>({
    title: '一键换车',
    visible: false,
  })
  //空调度单弹窗
  const emptyDispatchDialog = reactive<DialogOption>({
    title: '创建空调度单',
    visible: false,
  })
  // 导入失败弹窗
  const importDialogVisible = reactive<DialogOption>({
    title: '导入数据校验反馈',
    visible: false,
  })
  // 导入成功弹窗
  const importSuccessDialogVisible = reactive<DialogOption>({
    title: '导入数据校验反馈',
    visible: false,
  })
  // *****************************以上内容为个别页面操作按钮弹窗end*****************************

  /**
   * 列表数据
   */
  const tableData = ref<RolePageVO[]>()
  /**
   * 分页统计
   */
  const pageSummary = ref<any>()
  /**
   * 总计统计
   */
  const totalSummary = ref<any>()
  //刷新
  const refreshPageTableColumn = async () => {
    pageLoading.value = true
    await initPage()
    pageLoading.value = false
  }
  onBeforeMount(async () => {
    loading.value = true
    document.title = settingsStore.systemName
    await initPage()
    await nextTick(() => {
      // 如果元数据中的ext属性的needRequest字段不为false，则调用handleQuery方法
      if (metaInfo.value?.ext?.needRequest !== false) {
        handleQuery()
      }
    })
  })
  onBeforeUnmount(() => {
    sideBarStore.$patch((state) => {
      state.btnMenuId = ''
    })
  })
  const initPage = async () => {
    // 动态设置菜单数据列
    const { children, meta }: { children: MenuVO[]; meta: MetaVO } = await getcurrentUserMenuColumnlist(
      proxy.$sideBarStore.$state.btnMenuId ? proxy.$sideBarStore.$state.btnMenuId : proxy.$sideBarStore.$state.menuId,
    )
    metaInfo.value = meta
    proxy.$sideBarStore.$patch((state: any) => {
      state.meta = meta
    })
    //控制多选复选框按钮显示隐藏
    showAllSelection.value = meta.ext?.tableMultiSelect ?? true
    //获取当前菜单分页条数
    queryParams.limit = meta.ext?.pageLimit || defaultSettings.globalLimit
    //右侧消息提醒文字
    rightTipObj.value = meta.ext?.rightTip
    if (rightTipObj.value) {
      refreshRightTipData()
    }

    btnGroup.value = children
    //右侧按钮组
    listRightOperation.value = children.filter((item: MenuVO) => item.meta?.position === 'listRight')
    //顶部按钮组
    listTopOperation.value = children.filter((item: MenuVO) => item.meta?.position === 'listTop')
    //顶部搜索区域是否展示查询重置按钮
    showSearch.value = listTopOperation.value.some((item: MenuVO) => item.meta?.purpose === 'search')
    //数据列权限分配弹窗中的操作按钮
    menuDialogPermissionGroup.value = listRightOperation.value?.filter((item: MenuVO) => item.meta?.purpose === 'editAuth')
    //获取form表单数据列
    const dataColumn: any = meta.dataColumn
    dataColumn.forEach((item: TableItem, index: number) => {
      //由于服务端图片校验规则下发后是字符串，不能转换，所以需要处理
      if (item.form?.imageOption && item.form?.imageOption!.required) {
        //图片校验
        const validateImage = (rule: any, value: any, callback: any) => {
          //验证器
          if (!formDialogRef.value.formData[item.form?.name!]) {
            //为true代表图片在  false报错
            callback(new Error('请上传图片'))
          } else {
            callback()
          }
        }
        item.form.rules = [{ required: true, validator: validateImage, trigger: 'change' }]
      }
      //如果item.defaultValue存在，则给表单赋值
      if (item.query?.defaultValue) {
        queryParams[item.query?.name!] = item.query?.defaultValue
        topQueryGroupComponentRef.value.queryParams[item.query?.name!] = item.query.option?.multiple
          ? item.query?.defaultValue.split(',')
          : item.query?.defaultValue
      }
      if (item.query?.dynamicValue !== undefined) {
        const dynamicValue = item.query.dynamicValue
        const type = item.query.type
        const format = item.query.format || 'YYYY-MM-DD'

        if (type === 'month') {
          queryParams[item.query.name!] = dayjs().add(dynamicValue, 'month').format(format)
        } else if (type === 'year') {
          queryParams[item.query.name!] = dayjs().add(dynamicValue, 'year').format(format)
        } else if (type === 'date') {
          queryParams[item.query.name!] = dayjs().add(dynamicValue, 'day').format(format)
        } else if (type === 'daterange') {
          const start = dayjs().add(dynamicValue, 'day')
          const end = dayjs()
          queryParams[item.query.name!] = [start.format(format), end.format(format)]
        }
      }
      // 存在默认值
      if (item.query?.showDefaultDate && item.query?.format) {
        queryParams[item.query?.name!] = dayjs().format(item.query?.format)
        topQueryGroupComponentRef.value.queryParams[item.query?.name!] = dayjs().format(item.query?.format)
      }
    })
    requestUri.value = meta.uri

    //如果listTopOperation.value中存在purpose=customTableBusinessHeader或者financeReport,则获取自定义表头
    if (
      listTopOperation.value.findIndex((item: MenuVO) => item.meta?.purpose === 'customTableBusinessHeader') !== -1 ||
      listTopOperation.value.findIndex((item: MenuVO) => item.meta?.purpose === 'financeReport') !== -1
    ) {
      const defineColumns = await getDefineColumns()

      //tableConfig.tableItem = meta.dataColumn中name与defineColumns中columnName相同的项集合，并按照columnName顺序排列
      tableConfig.tableItem = dataColumn
        .filter((item: TableItem) => defineColumns.findIndex((defineItem: any) => defineItem.columnName === item.name) !== -1)
        .sort((a: TableItem, b: TableItem) => {
          return (
            defineColumns.findIndex((defineItem: any) => defineItem.columnName === a.name) -
            defineColumns.findIndex((defineItem: any) => defineItem.columnName === b.name)
          )
        })
    } else {
      tableConfig.tableItem = dataColumn
    }
    topQueryConfig.tableItem = dataColumn
    //如果meta.operation=false 则不展示列表右侧操作列
    if (!meta.operation) {
      tableConfig.operation = undefined
    }
    //如果meta.showHandleSelection=false 则不展示列表左侧选择列
    if (meta.ext?.showHandleSelection === false) {
      tableConfig.showHandleSelection = false
    }

    if (meta.ext?.showSort === false) {
      tableConfig.showSort = false
    }
    isFullscreen.value = meta.ext?.fullScreen === true

    //遍历routeParams对象，queryParams[key] = routeParams[key]
    for (const key in routeParams) {
      if (Object.hasOwn(routeParams, key)) {
        //遍历dataColumn,如果key===item.query?.name,并且item.query?.type.indexOf('range') !== -1
        const columnItem = dataColumn.find((item: TableItem) => {
          return key === item.query?.name && item.query?.type.indexOf('range') !== -1
        })
        //如果属性中包含range字符串，则把首字母大写，然后拆分2个，分别拼上start 和 end ,并删除原有属性
        if (columnItem && routeParams[key]) {
          const startName = 'start' + key.charAt(0).toUpperCase() + key.slice(1)
          const endName = 'end' + key.charAt(0).toUpperCase() + key.slice(1)
          queryParams[startName] = routeParams[key].split(',')![0]
          queryParams[endName] = routeParams[key].split(',')![1]
        } else {
          queryParams[key] = routeParams[key]
        }
        const item = topQueryConfig.tableItem.find((item) => routeParams[key] && item.query?.name === key && item.query?.option?.multiple)
        if (item) {
          topQueryGroupComponentRef.value.queryParams[key] = routeParams[key] ? routeParams[key].split(',') : []
        } else if (columnItem) {
          topQueryGroupComponentRef.value.queryParams[key] = routeParams[key] ? routeParams[key].split(',') : []
        } else {
          topQueryGroupComponentRef.value.queryParams[key] = routeParams[key]
        }
        //初始化搜索条件
        // formStore.setSearchParams({});
      }
    }

    // 计算展开更多内容超出显示
    await nextTick(() => {
      // 这里具体行数可依据需求自定义
      let lineHeight = 61 * 2
      state.textHeight = `${lineHeight}px`

      if (content.value.offsetHeight > lineHeight) {
        state.isShowMore = true
        state.status = true
      } else {
        state.isShowMore = false
        state.status = false
      }
    })
    //初始化查询项（从handleQuery中挪过来的，不知道当时为啥要写到里面）
    await refreshQuerySelectOptions()
  }
  const isFullscreen = ref<any>(true)
  /**
   * 关闭弹窗并清除之前的全局查询条件
   */
  const closeDetailDialogVisible = () => {
    const menu = proxy.$sideBarStore.$state.menu
    //如果menu.meta.ext.refreshPage===true ,则刷新列表页
    if (menu && menu.meta?.ext?.refreshPage) {
      handleQuery()
    }
    sideBarStore.$patch((state) => {
      state.btnMenuQuery = {}
      state.mergeDialogFormParams = {}
      state.storeDialogFormParams = {}
    })
    isFullscreen.value = false
  }
  const closeDetailDialogVisiblese = (detailDialogVisible: any) => {
    if (detailDialogVisible.title == '历史详情') {
      handleQuery()
    }
    const menu = proxy.$sideBarStore.$state.menu
    //如果menu.meta.ext.refreshPage===true ,则刷新列表页
    if (menu && menu.meta?.ext?.refreshPage) {
      handleQuery()
    }
    sideBarStore.$patch((state) => {
      state.btnMenuQuery = {}
      state.mergeDialogFormParams = {}
      state.storeDialogFormParams = {}
    })
    isFullscreen.value = false
  }
  const refreshScroll = ref<boolean>(false)
  /**
   * 分页
   */
  const _pagination = () => {
    handleQuery()
  }

  /**
   * 查询
   */

  function handleQuery() {
    loading.value = true
    //获取storeData中的参数并赋值给tempQueryParams
    if (proxy.$sideBarStore.$state.storeDialogFormParams) {
      for (const key in proxy.$sideBarStore.$state.storeDialogFormParams) {
        queryParams[key] = proxy.$sideBarStore.$state.storeDialogFormParams[key]
      }
    }
    let newParams = JSON.parse(JSON.stringify(queryParams))
    //组织管理走tree,其他走page分页
    if (proxy.$sideBarStore.$state.menuId === '88010200000000') {
      newParams.page = undefined
      newParams.limit = undefined
      tableBorder.value = false
      tableConfig.showHandleSelection = false
      tableConfig.showSort = false
      getListTree(newParams, '/logistics/company/department')
        .then(async ({ data }) => {
          tableConfig.tableItem.forEach((item: TableItem) => {
            if (item.form?.imageOption!) {
              data.rows.map((_item: any) => {
                let arr: UploadImageVO[] = []
                _item[item.form?.name!]?.forEach((v: any) => {
                  arr.push({
                    url: v,
                  })
                })
                _item[item.form?.name!] = arr
              })
            }
          })
          tableData.value = data
          //如果defaultTableIds有值，则需要勾选默认的列表项
          if (tableData.value && tableData.value.length > 0) {
            if (defaultTableIds.value.ids) {
              ids.value = defaultTableIds.value.ids
              const listNew = [] as any // 定义默认勾选的行数据
              //遍历tableData,找到id在defaultTableIds.value.ids存在的，并将行数据push到listNew中
              tableData.value.forEach((item) => defaultTableIds.value.ids.includes(item.id) && listNew.push(item))
              //清空列表所有选中项
              nextTick(() => {
                listNew.forEach((item: any) => {
                  tableComponentRef.value.dataTableRef.toggleRowSelection(item, true)
                })
              })
            }
          }
          await refreshQuerySelectOptions()
        })
        .finally(() => {
          loading.value = false
          sideBarStore.$patch((state) => {
            // 弹窗关闭后，清空菜单id和菜单查询条件，防止下次打开时，数据有误，
            // 示例：列表菜单，点击详情，弹窗打开，弹窗数据再次页面跳转，导致列表数据有误（上汽大众 POC 送货单查询）
            state.btnMenuId = ''
            state.btnMenuQuery = {}
            // state.mergeDialogFormParams = {};
            // state.storeDialogFormParams = {};
          })
        })
    } else {
      const api =
        (metaInfo.value?.ext?.pageMethod && metaInfo.value?.ext?.pageMethod === 'post') || metaInfo.value?.ext?.pageMethod === 'POST'
          ? getListPagePost
          : getListPage
      api(newParams, requestUri.value!)
        .then(async ({ data }) => {
          tableConfig.tableItem.forEach((item: TableItem) => {
            if (item.form?.type === 'uploadImage' && item.form?.imageOption!) {
              data.rows.map((_item: any) => {
                let arr: UploadImageVO[] = []
                _item[item.form?.name!]?.forEach((v: any) => {
                  arr.push({
                    url: v,
                  })
                })
                _item[item.form?.name!] = arr
              })
            }
          })
          tableData.value = data.rows
          statisticColumn.value = data.statistics
          if (data.pageSummary) {
            data.pageSummary.checkbox = '当前页合计'
          }
          if (data.totalSummary) {
            data.totalSummary.checkbox = '总合计'
          }

          pageSummary.value = data.pageSummary
          totalSummary.value = data.totalSummary
          total.value = data.total
          //如果defaultTableIds有值，则需要勾选默认的列表项
          if (tableData.value && tableData.value.length > 0) {
            if (defaultTableIds.value.ids) {
              ids.value = defaultTableIds.value.ids
              const listNew = [] as any // 定义默认勾选的行数据
              //遍历tableData,找到id在defaultTableIds.value.ids存在的，并将行数据push到listNew中
              tableData.value.forEach((item) => defaultTableIds.value.ids.includes(item.id) && listNew.push(item))
              //清空列表所有选中项
              nextTick(() => {
                listNew.forEach((item: any) => {
                  tableComponentRef.value.dataTableRef.toggleRowSelection(item, true)
                })
              })
            }
          }
        })
        .finally(() => {
          loading.value = false
          sideBarStore.$patch((state) => {
            // 弹窗关闭后，清空菜单id和菜单查询条件，防止下次打开时，数据有误，
            // 示例：列表菜单，点击详情，弹窗打开，弹窗数据再次页面跳转，导致列表数据有误（上汽大众 POC 送货单查询）
            state.btnMenuId = ''
            state.btnMenuQuery = {}
            // state.mergeDialogFormParams = {};
            // state.storeDialogFormParams = {};
          })
        })
    }
    //如果右侧消息提醒存在，则更新数
    if (rightTipObj.value) {
      refreshRightTipData()
      if (sideBarStore.getMenuCountFun && typeof sideBarStore.getMenuCountFun === 'function') {
        sideBarStore.getMenuCountFun()
      }
    }
  }

  /**
   * 更新查询条件下拉
   */
  const refreshQuerySelectOptions = async () => {
    //重新获取form表单数据列，刷新筛选下拉项
    topQueryConfig.tableItem = await getSelectOptions(metaInfo.value!.dataColumn, null, '', 'topQuerySelect')
  }
  /**
   * 更新表单下拉
   */
  const refreshFormSelectOptions = async (dataColumn: FormColumn[]) => {
    //重新获取form表单数据列，刷新筛选下拉项
    return new Promise(async (resolve, reject) => {
      const newDataColumn = getSelectOptions(dataColumn, null, '', 'formSelect')
      resolve(newDataColumn)
    })
  }

  /**
   * 重置查询
   */
  function resetQuery() {
    queryParams.page = 1
    handleQuery()
  }

  function resetTopQuery() {
    topQueryGroupComponentRef.value.resetQuery()
  }

  function searchTopQuery() {
    topQueryGroupComponentRef.value.handleQuery()
  }

  //批量调度弹窗
  const batchSchedule = () => {
    batchScheduleDialog.visible = true
  }

  //空调度弹窗
  const emptyDispatch = (position: string, purpose: string, menu: MenuVO) => {
    emptyDispatchDialog.visible = true
  }

  //运单号弹窗
  const mergeShipmentNoDialogRef = ref()

  /**
   * 表单提交
   */
  const handleSubmit = useThrottleFn(async (formData: RoleForm, request: BtnRequestVO, isRefreshMenuCount: boolean) => {
    loading.value = true
    //如果mergeFormData有值，则合并到formData中
    if (JSON.stringify(mergeFormData.value) !== '{}') {
      formData = { ...formData, ...mergeFormData.value }
    }
    //如果storeFormParams有值，则合并到formData中
    if (JSON.stringify(storeFormParams.value) !== '{}') {
      formData = { ...formData, ...storeFormParams.value }
    }
    if (JSON.stringify(sideBarStore.$state.mergeDialogFormParams) !== '{}') {
      formData = { ...formData, ...sideBarStore.$state.mergeDialogFormParams }
    }
    if (JSON.stringify(sideBarStore.$state.storeDialogFormParams) !== '{}') {
      formData = { ...formData, ...sideBarStore.$state.storeDialogFormParams }
    }

    //如果是对象则使用公共请求
    if (request?.constructor === Object) {
      if (request.method === 'put' || request.method === 'PUT') {
        await updateItemApi(formData.id as string, formData, request.uri!)
          .then((res: any) => {
            ElMessage.success(res.message)
            handleQuery()
            closeDialog()
          })
          .finally(() => (loading.value = false))
      } else {
        await globalRequestApi(formData, request.method!, request.uri!)
          .then(async (res: any) => {
            //request.secondDialog.result=true->进行二次弹窗(运力指派用到)
            if (request.secondDialog && request.secondDialog.result && typeof res.data === 'object') {
              if (res.data.length > 0) {
                // 合并运单
                mergeShipmentNoDialogRef.value.mergeShipmentNo = '' //初始化合并的运单
                mergeShipmentNoDialogRef.value.mergeShipmentNoVisible = true
                mergeShipmentNoDialogRef.value.mergeShipmentNoList = res.data
                mergeShipmentNoDialogRef.value.state.submitParams = formData
                mergeShipmentNoDialogRef.value.requestInfo.methods = request.secondDialog.method
                mergeShipmentNoDialogRef.value.requestInfo.uri = request.secondDialog.uri
              } else {
                let uri = ''
                if (request.secondDialog.method === 'put' || request.secondDialog.method === 'PUT') {
                  uri = request.secondDialog.uri + '/' + formData.id
                } else {
                  uri = request.secondDialog.uri
                }
                await globalRequestApi(formData, request.secondDialog.method!, uri)
                  .then(async (res: any) => {
                    ElMessage.success(res.message)
                    handleQuery()
                    closeDialog()
                  })
                  .finally(() => (loading.value = false))
              }
            } else if (request.purpose && request.purpose === 'accidentStepOne') {
              //  事故管理 - 下一步
              accidentManageConfirmDialogRef.value.dialogVisible.title = '新增事故单'
              accidentManageConfirmDialogRef.value.dialogVisible.visible = true
              accidentManageConfirmDialogRef.value.getQualityLoss(res.data, formData)
            } else {
              ElMessage.success(res.message)
              handleQuery()
              closeDialog()
            }
            if (dynamicAuditDetailDialogRef.value) {
              dynamicAuditDetailDialogRef.value.closeDialog()
            }
          })
          .finally(() => {
            loading.value = false
          })
      }
      //否则使用老方法
    } else {
      const _id = formData.id
      if (_id) {
        await updateItemApi(_id, formData, request as any)
          .then((res: any) => {
            Reflect.deleteProperty(formData, 'id')
            ElMessage.success(res.message)
            handleQuery()
            closeDialog()
          })
          .finally(() => (loading.value = false))
      } else {
        await addItemApi(formData, request as any)
          .then((res: any) => {
            ElMessage.success(res.message)
            handleQuery()
            closeDialog()
          })
          .finally(() => (loading.value = false))
      }

      if (dynamicAuditDetailDialogRef.value) {
        dynamicAuditDetailDialogRef.value.closeDialog()
      }
    }
    nextTick(() => {
      if (isRefreshMenuCount) refreshMenuCount()
    })
  }, 3000)

  // 关闭事故弹窗
  function closeAccidentDialog() {
    accidentManageConfirmDialogRef.value.dialogVisible.visible = false
    closeDialog()
  }

  // 关闭外协结算-付款管理-付款信息弹窗
  function closeOutsourcePaymentDialog() {
    outsourcePaymentDialogRef.value.state.dialogVisible.visible = false
    closeDialog()
  }

  // 关闭外协结算-付款管理-补贴付款-打款弹窗
  function closeSubsidyPaymentDialog() {
    subsidyPaymentDialogRef.value.state.dialogVisible.visible = false
    closeDialog()
  }

  /**
   * 关闭一键换车弹窗
   */
  function closeOneclickDialog() {
    OneclickDialog.visible = false
  }

  /**
   * 关闭弹窗
   */
  function closeDialog() {
    dialog.visible = false
    if (mergeShipmentNoDialogRef.value) {
      mergeShipmentNoDialogRef.value.mergeShipmentNoVisible = false
      mergeShipmentNoDialogRef.value.state.submitParams = null
    }
    // formDialogRef.value.closeDialog();
    // //刷新列表
    // resetQuery()
  }

  /**
   * 重置表单
   */
  function resetForm() {
    formDialogRef.value.resetForm()
  }

  /**
   * 批量操作
   * @param arr 选中的列表项数据
   */
  const batchDeleteFun = (arr: any[]) => {
    //存储选中项到pinia
    formStore.$patch((state) => {
      state.selectRows = arr
    })
    selectTableColumn.value = arr
    ids.value = arr.map((item) => {
      return item.id
    })
  }
  /**
   * 删除当前行
   * @param position
   * @param type
   * @param menu
   */
  const handleDelete = (position: string, type: string, menu: MenuVO) => {
    btnRequestUri.value = menu.meta?.uri
    let _ids = ''
    //如果批量操作需要使用选中行中的某个字段（不是id)，需要使用这个
    if (menu.meta?.form?.batchCommit) {
      _ids = selectTableColumn.value
        .map((item) => {
          return item[menu.meta?.form?.batchCommit?.name!]
        })
        .join(',')
    } else {
      _ids = ids.value.join(',')
    }
    if (_ids.length <= 0) {
      ElMessage.warning('请勾选')
      return
    }
    // const params: { [key: string]: any } = {};
    // params[menu.meta?.form?.batchCommit?.targetName!] = _ids;
    loading.value = true
    batchDeleteApi(_ids, btnRequestUri.value ?? requestUri.value!)
      .then((res: any) => {
        ElMessage.success(res.message)
        resetQuery()
        //清空ids
        ids.value = []
      })
      .finally(() => (loading.value = false))
  }

  const deleteItemFun = (row: any) => {
    deleteItemApi(row.id!, requestUri.value!).then((res: any) => {
      ElMessage.success(res.message)
      resetQuery()
      console.log('清空ids')
      //清空ids
      ids.value = []
    })
  }
  /**
   * 修改状态
   */
  const handleStatusChange = async (row: { [key: string]: any }, uri?: string) => {
    await switchChangeGlobalFun(row.enable, row.id, uri ?? requestUri.value!)
      .then(async () => {
        //修改成功后刷新列表
        await handleQuery()
      })
      .catch(() => {
        //取消切换，恢复状态
        row.enable = row.enable !== true
      })
  }
  /**
   * 导入Excel
   * @param file 文件
   */
  const importExcelFun = useThrottleFn(async (file: any, meta: MetaVO) => {
    //定义传递的参数
    let formDataParams = {} as any
    const _queryParams = await topQueryGroupComponentRef.value.searchQueryTemp()
    const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
    meta?.form?.params?.map((_item: any) => {
      composeRequestParams(formDataParams, _item, null, storeDataParams, null, _queryParams)
    })
    importButtonLoading.value = true
    let uri = meta?.uri ? meta.uri : requestUri.value!
    const api = meta?.uri ? importFileGlobalBtnUriFun : importFileGlobalFun
    api(file, uri, formDataParams)
      .then(async (res: any) => {
        if (res.code === 11010) {
          sideBarStore.$patch((state) => {
            state.btnMenuId = res.data.menuId
          })
          detailDialogVisible.visible = true
          detailDialogVisible.title = res.data.label
          return
        } else if (res.code === 5001) {
          ElMessage.error('导入失败')
          importDialogVisible.visible = true
          importFailedRef.value.state.data = res.data
          return
        } else if (res.code === 201) {
          importSuccessDialogVisible.visible = true
          importSuccessRef.value.state.data = res.data
        }
        ElMessage.success('导入成功')
        handleQuery()
      })
      .catch(() => {})
      .finally(() => {
        importButtonLoading.value = false
      })
  }, 3000)
  /**
   * 导入自定义模板
   * @param file 文件
   */
  const importCustomExcelButtonLoading = ref(false)
  const importCustomExcel = useThrottleFn(async (file: any, menu: MenuVO, item: any) => {
    //定义传递的参数
    let formDataParams = {
      id: item.value,
    } as any
    importCustomExcelButtonLoading.value = true
    pageLoadingText.value = '导入中...'
    let uri = null
    if (menu.meta?.purpose === 'importList') {
      uri = metaInfo.value?.uri + '/with/params'
    } else {
      if (item.value === 2) {
        uri = 'logistics/api/out/fleet/order/scattered'
      } else if (item.value === 3) {
        uri = 'logistics/api/out/fleet/order/plan'
      } else {
        uri = 'logistics/api/out/fleet/order/custom'
      }
    }

    const api = importFileGlobalFun
    api(file, uri, formDataParams)
      .then(async (res: any) => {
        if (res.code === 5001) {
          ElMessage.error('导入失败')
          importDialogVisible.visible = true
          importFailedRef.value.state.data = res.data
          return
        } else if (res.code === 201) {
          importSuccessDialogVisible.visible = true
          importSuccessRef.value.state.data = res.data
        }
        ElMessage.success('导入成功')
        handleQuery()
      })
      .catch(() => {})
      .finally(() => {
        importCustomExcelButtonLoading.value = false
        pageLoadingText.value = '加载中。。。'
      })
  }, 3000)
  /**
   * 导出Excel
   */
  const exportExcelFun = useThrottleFn((position: string, type: string, item: MenuVO) => {
    let requestApi = item.meta?.uri ? globalExportExcel : exportExcel
    const params = {
      ...queryParams,
      ids: ids.value.join(','),
    }
    requestApi(params, item.meta?.uri ? item.meta?.uri : requestUri.value!)
      .then(async (res) => {
        exportButtonLoading.value = true
        await downloadFileGlobalFun(res)
        exportButtonLoading.value = false
        ElMessage.success('操作成功')
      })
      .catch((error) => {
        exportButtonLoading.value = false
      })
  }, 3000)
  /**
   * 下载导入模版
   */
  const downloadExcelTemplate = useThrottleFn((purpose: string, position?: string, menu?: MenuVO) => {
    const requestApi = menu?.meta?.uri ? downloadTemplate : globalDownloadTemplate
    requestApi(menu?.meta?.uri ? menu?.meta?.uri : requestUri.value!).then(async (res) => {
      exportButtonLoading.value = true
      await downloadFileGlobalFun(res)
      exportButtonLoading.value = false
      ElMessage.success('操作成功')
    })
  }, 3000)
  /**
   * 下载自定义导入模版
   */
  const customTemplateLoading = ref(false)
  const downloadCustomExcelTemplate = useThrottleFn((meta: MetaVO, item: any) => {
    let uri = null
    if (meta.purpose === 'customTemplateDownload') {
      if (item.value === 2) {
        uri = 'logistics/api/out/fleet/order/scattered/template'
      } else if (item.value === 3) {
        uri = 'logistics/api/out/fleet/order/plan/template'
      } else {
        uri = 'logistics/api/out/fleet/order/custom/template'
      }
    } else if (meta.purpose === 'templateList') {
      uri = metaInfo.value?.uri + '/template'
    } else {
      uri = meta.ext!.linkUrl
    }
    customTemplateLoading.value = true
    downloadCustomTemplate(uri!, { id: item.value, ids: ids.value.join(',') })
      .then(async (res) => {
        await downloadFileGlobalFun(res)
        ElMessage.success('操作成功')
      })
      .catch((err) => {})
      .finally(() => {
        customTemplateLoading.value = false
      })
  }, 3000)
  /**
   * 下载文件
   */
  const fileDownload = useThrottleFn((row: any, position: string, menu: MenuVO, purpose: string) => {
    //定义传递的参数
    let params = {} as any
    const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
    menu.meta?.form?.params?.map((_item: any) => {
      composeRequestParams(params, _item, menu, storeDataParams, row, queryParams)
    })
    // 文件
    if (purpose === 'fileDownload') {
      let api = menu.meta.form!.method === 'get' || menu.meta.form!.method === 'GET' ? globalRequestUrlApi : globalRequestApi
      api(params, menu?.meta?.form?.method!, menu?.meta?.form?.formUri!).then(async (res: any) => {
        // 根据res.data,进行逗号分割，依次下载每个文件
        if (res.data.indexOf(',') !== -1) {
          res.data.split(',').forEach((item: any) => {
            window.open(item)
          })
        } else {
          window.open(res.data)
        }
      })
    } else if (purpose === 'streamDownload') {
      //文件流
      let api = menu.meta.form!.method === 'get' || menu.meta.form!.method === 'GET' ? downloadTemplateUrl : downloadTemplate
      api(params, menu?.meta?.form?.method!, menu?.meta?.form?.formUri!).then(async (res: any) => {
        await downloadFileGlobalFun(res)
      })
    }
  }, 3000)
  /**
   * 筛选条件查询
   * @param searchParams 查询条件
   */
  const handleSearchQuery = (searchParams: any) => {
    //遍历queryParams对象，删除所有属性
    for (const key in queryParams) {
      if (key !== 'page' && key !== 'limit' && key !== 'customerOrderBy') {
        Reflect.deleteProperty(queryParams, key)
      }
    }
    // queryParams.limit = 10;
    //遍历searchParams对象，将所有属性赋值给queryParams
    for (const key in searchParams) {
      queryParams[key] = searchParams[key]
    }
    handleQuery()
  }
  /**
   * 展示图片
   * @param row 当前行数据
   * @param name 图片字段名
   */
  const showPic = (row: any, name: string) => {
    if (row[name]) {
      imageList.value = row[name].split(',')
      picDialogComponent.value.picDialogVisible = true
    } else {
      ElMessage.warning('当前未上传证件')
    }
  }

  /**
   * 按钮默认操作
   * @param row 当前行数据
   * @param position 按钮位置
   * @param menu
   */
  const defaultHandle = async (row: any, position: string, menu: MenuVO) => {
    try {
      pageLoading.value = true
      btnMenu.value = menu
      btnRequestUri.value = menu.meta!.uri
      isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1
      if (menu.meta?.form) {
        if (ids.value.length < menu.meta?.form?.min!) {
          ElMessage.warning(`至少选择${menu.meta?.form?.min}项`)
          return
        } else if (ids.value.length > menu.meta?.form?.max!) {
          ElMessage.warning(`最多选择${menu.meta?.form?.max}项`)
          return
        }
      }
      if (menu.meta?.form?.formUri) {
        //定义传递的参数
        let params = {} as any
        const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
        menu.meta?.form?.params?.map((_item: FormRequestParamsVO) => {
          composeRequestParamsMultiRow(params, _item, menu, storeDataParams, selectTableColumn.value, ids.value)
        })
        if (menu.meta?.form?.method!.toLowerCase() === 'get' || menu.meta?.form?.method!.toLowerCase() === 'put') {
          globalRequestUrlApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
            await globalBtnForm(res.data, menu.meta?.position!, menu)
          })
        } else {
          globalRequestApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
            await globalBtnForm(res.data, menu.meta?.position!, menu)
          })
        }
      } else {
        dialog.visible = true
        dialog.dialogWidth = menu.meta?.ext?.dialogWidth ?? '750px'
        dialog.title = menu.meta!.title

        if (menu.meta!.dataColumn.length > 0) {
          operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
        } else {
          operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
        }
      }
      //如果menu.meta.ext.closeCurrentDialog===true,关闭当前弹窗
      if (menu.meta?.ext?.closeCurrentDialog) {
        detailDialogVisible.visible = false
      }
      //如果menu.meta.ext.refreshParentList===true,刷新外层列表
      if (menu.meta?.ext?.refreshParentList) {
        handleQuery()
      }
      pageLoading.value = false
      pageLoadingText.value = '加载中。。。'
    } catch (err) {
      pageLoading.value = false
      console.log(err)
    }
  }
  /**
   * 复制表单
   */
  const copyForm = async (row: any, position: string, menu: MenuVO) => {
    currentRow.value = row
    btnMenu.value = menu
    btnRequestUri.value = menu.meta!.uri
    isEdit.value = false
    isCopy.value = true
    //存储当前按钮下表单中的btns
    if (menu.meta?.form?.btns) {
      //存储选中项到pinia
      formStore.$patch((state) => {
        state.storeFormBtns = menu.meta?.form?.btns!
      })
    }
    if (menu.meta?.form?.formUri) {
      //定义传递的参数
      let params = {} as any
      const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
      menu.meta!.form!.params?.map((_item) => {
        composeRequestParams(params, _item, menu, storeDataParams, row, queryParams)
      })
      if (menu.meta?.form?.method === 'get' || menu.meta?.form?.method === 'GET') {
        globalRequestUrlApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      } else {
        globalRequestApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      }
    } else {
      if (menu.meta!.dataColumn.length > 0) {
        operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
      } else {
        operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
      }
      await globalBtnForm(row, position, menu)
    }
    //如果menu.meta?.form?.refreshPage==true ,则刷新当前列表
    if (menu.meta?.form?.refreshPage) handleQuery()
    //如果menu.meta?.form?.refreshMenuCount==true ,则更新菜单上的数字
    if (menu.meta?.form?.refreshMenuCount) refreshMenuCount()
    //如果menu.meta?.form?.trigerUris存在，遍历menu.meta?.form?.trigerUris，请求相应接口
    if (menu.meta?.form?.trigerUris && menu.meta?.form?.trigerUris.length > 0) {
      menu.meta?.form?.trigerUris?.map((item) => {
        //定义传递的参数
        let params = {} as any
        const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
        item.params?.map((_item) => {
          composeRequestParams(params, _item, menu, storeDataParams, row, queryParams)
        })
        if (item.method === 'get' || item.method === 'GET') {
          globalRequestUrlApi(params, item.method!, item.uri!).then(async (res) => {})
        } else {
          globalRequestApi(params, item.method!, item.uri!).then(async (res) => {})
        }
      })
    }
  }
  /**
   * 根据行id查详情
   * @param row 当前行数据
   * @param position 按钮位置
   * @param menu
   */
  const getDialogDetailById = async (row: any, position: string, menu: MenuVO) => {
    btnMenu.value = menu
    //定义传递的参数
    let params = {} as any
    const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
    menu.meta?.form?.params?.map((_item) => {
      composeRequestParamsMultiRow(params, _item, menu, storeDataParams, selectTableColumn.value, ids.value)
    })
    globalRequestUrlApi(params, 'get', requestUri.value! + '/' + row.id).then(async (res) => {
      await globalBtnForm(res.data, menu.meta?.position!, menu)
    })
  }
  /**
   * 当前选中的行数据
   */
  const currentRow = ref<TableItem>()
  /**
   *  列表右侧按钮默认操作
   * @param row
   * @param position 按钮位置
   * @param menu
   */
  const defaultHandleTable = async (row: TableItem, position: string, menu: MenuVO) => {
    currentRow.value = row
    btnMenu.value = menu
    btnRequestUri.value = menu.meta!.uri
    isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1

    //存储当前按钮下表单中的btns
    if (menu.meta?.form?.btns) {
      //存储选中项到pinia
      formStore.$patch((state) => {
        state.storeFormBtns = menu.meta?.form?.btns!
      })
    }
    if (menu.meta?.form?.formUri) {
      //定义传递的参数
      let params = {} as any
      const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
      menu.meta!.form!.params?.map((_item) => {
        composeRequestParams(params, _item, menu, storeDataParams, row, queryParams)
      })
      if (menu.meta?.form?.method === 'get' || menu.meta?.form?.method === 'GET') {
        globalRequestUrlApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      } else {
        globalRequestApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri).then(async (res) => {
          await globalBtnForm(res.data, menu.meta?.position!, menu)
        })
      }
    } else {
      if (menu.meta!.dataColumn.length > 0) {
        operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
      } else {
        operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
      }
      await globalBtnForm(row, position, menu)
    }
    //如果menu.meta?.form?.refreshPage==true ,则刷新当前列表
    if (menu.meta?.form?.refreshPage) handleQuery()
    //如果menu.meta?.form?.refreshMenuCount==true ,则更新菜单上的数字
    if (menu.meta?.form?.refreshMenuCount) refreshMenuCount()
    //如果menu.meta?.form?.trigerUris存在，遍历menu.meta?.form?.trigerUris，请求相应接口
    if (menu.meta?.form?.trigerUris && menu.meta?.form?.trigerUris.length > 0) {
      menu.meta?.form?.trigerUris?.map((item) => {
        //定义传递的参数
        let params = {} as any
        const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
        item.params?.map((_item) => {
          composeRequestParams(params, _item, menu, storeDataParams, row, queryParams)
        })
        if (item.method === 'get' || item.method === 'GET') {
          globalRequestUrlApi(params, item.method!, item.uri!).then(async (res) => {})
        } else {
          globalRequestApi(params, item.method!, item.uri!).then(async (res) => {})
        }
      })
    }
  }

  /**
   * 默认按钮操作
   * @param row
   * @param position
   * @param menu
   */
  const globalBtnForm = async (row: TableItem, position: string, menu: MenuVO) => {
    btnRequestUri.value = menu.meta?.form?.formUri ?? null
    if (menu.meta?.purpose === 'copy') {
      isEdit.value = false
    } else {
      isEdit.value = menu.meta?.purpose === 'edit' || menu.meta.position!.indexOf('Right') > -1
    }
    buttonPosition.value = position //初始化表单
    const initFormData = resetFormGlobalFun(menu.meta?.dataColumn!)
    if (menu.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
    }

    // 根据按钮返回数据渲染当前表单，如果是下拉表单 或者是级联表单项，需要将返回数据设置到表单项的option.data上
    //遍历数组operationColumn.value，找到里面的form的type是select，将row中和form的name相同的项赋值他
    if (position === 'listTop' || position === 'listTabTop') {
      operationColumn.value.map((item: TableItem) => {
        if (item?.form?.type === 'select' || item?.form?.type === 'selectTree' || item?.form?.type === 'cascader') {
          if (row[item.form.name!]) {
            if (!item.form!.option!.data) {
              item.form!.option!.data = []
            }
            //如果row[item.form.name!] 类型是数组则赋值给item.form.option!.data
            if (Array.isArray(row[item.form.name!])) {
              item.form!.option!.data = row[item.form.name!]
            }
            delete row[item.form.name!]
          }
        }
      })
    }
    if (position === 'listRight' || position === 'listTabRight') {
      operationColumn.value.map((item: TableItem) => {
        if (item?.form?.type === 'select' || item?.form?.type === 'selectTree' || item?.form?.type === 'cascader') {
          if (row[item!.form.name!]) {
            if (!item!.form!.option!.data) {
              item!.form!.option!.data = []
            }
            // item!.form.option!.data = row[item!.form.name!];
            // delete row[item.form.name!];
          }
        }
      })
    }
    dialog.visible = true
    dialog.title = menu.meta?.title || '操作'
    dialog.dialogWidth = menu.meta?.ext?.dialogWidth ?? '750px'
    const deepRow = JSON.parse(JSON.stringify(row))
    const filterOperationColumn = operationColumn.value.filter((item: TableItem) => {
      return item.form?.longTerm
    })
    filterOperationColumn.forEach((item: TableItem) => {
      if (item.form?.longTerm) {
        const longTermKey = item.form.name! + 'longTerm'
        if (deepRow[item.form?.name!] && deepRow[item.form?.name!].includes('9999')) {
          deepRow[longTermKey] = true
          deepRow[item.form.name!] = ''
        } else {
          deepRow[longTermKey] = false
        }
      }
    })
    //如果是复制，并且operationColumn.value中的每项的form.canCopy为false，则将deepRow中的对应项的value设置为空
    if (menu.meta?.purpose === 'copy') {
      operationColumn.value.forEach((item: TableItem) => {
        if (item.form?.canCopy === false) {
          deepRow[item.form.name!] = null
        }
      })
      deepRow.id = undefined
    }
    await nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, deepRow)
    })
  }
  const globalTableColumnForm = async (row: TableItem, column: TableItem) => {
    //获取tableColumn
    const {
      children,
      meta,
    }: {
      children: MenuVO[]
      meta: MetaVO
    } = await getcurrentUserMenuColumnlist(column.jump?.menuId!)
    const dataColumn: any = meta.dataColumn
    btnMenu.value = {
      meta: {
        form: meta.form,
        dataColumn: meta.dataColumn,
        menuId: meta.menuId,
        pageType: meta.pageType,
        dependsOn: meta.dependsOn,
      },
    }
    dialog.visible = true
    if (dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
    }

    const initFormData = resetFormGlobalFun(dataColumn)
    operationColumn.value.map((item: TableItem) => {
      if (item?.form?.type === 'select' || item?.form?.type === 'selectTree' || item?.form?.type === 'cascader') {
        if (row[item!.form.name!]) {
          if (!item!.form!.option!.data) {
            item!.form!.option!.data = []
          }
          // item!.form.option!.data = row[item!.form.name!];
          // delete row[item.form.name!];
        }
      }
    })
    dialog.visible = true
    dialog.title = meta.title || '操作'
    dialog.dialogWidth = meta?.ext?.dialogWidth ?? '750px'
    const deepRow = JSON.parse(JSON.stringify(row))
    const filterOperationColumn = operationColumn.value.filter((item: TableItem) => {
      return item.form?.longTerm
    })
    filterOperationColumn.forEach((item: TableItem) => {
      if (item.form?.longTerm) {
        const longTermKey = item.form.name! + 'longTerm'
        if (deepRow[item.form?.name!] && deepRow[item.form?.name!].includes('9999')) {
          deepRow[longTermKey] = true
          deepRow[item.form.name!] = ''
        } else {
          deepRow[longTermKey] = false
        }
      }
    })
    await nextTick(() => {
      Object.assign(formDialogRef.value.formData, initFormData)
      Object.assign(formDialogRef.value.formData, deepRow)
    })
  }
  /**
   * 获取表单项，用于表单中级联操作
   * @param formData 表单项
   * @param dependOn 当前修改的依赖项下拉
   */
  const getQueryData = async (formData: any, dependOn: string) => {
    //获取form表单数据列
    const newOperationColumn = await getSelectOptions(metaInfo.value!.dataColumn, formData, dependOn, 'topQuerySelect')
  }
  /**
   * 获取表单项，用于表单中级联操作
   * @param formData 表单项
   * @param dependOn 当前修改的依赖项下拉
   */
  const getFormData = async (formData: any, dependOn: string) => {
    setTimeout(async () => {
      //获取form表单数据列
      const newOperationColumn = await getSelectOptions(operationColumn.value, formData, dependOn, 'formSelect')
    }, 200)
  }
  //弹窗显示菜单
  const showRowMenuDialog = (query: any, column: TableItem, menu: MenuVO) => {
    console.log(menu)
    if (menu) {
      if (menu.meta.pageType === 'basePage1') {
        detailDialogVisible.visible = true
      } else if (menu.meta.pageType === 'baseTabPage') {
        detailBaseTabPageDialogVisible.visible = true
      }
    } else {
      detailDialogVisible.visible = true
    }

    let title = column.label
    if (column.jump) {
      title = column.jump!.title ?? column.label
      if (column.jump?.dialogWidth) {
        detailDialogVisible.dialogWidth = column.jump!.dialogWidth ?? '80%'
      }
      if (column.jump?.fullScreen) {
        isFullscreen.value = column.jump!.fullScreen
      } else {
        isFullscreen.value = false
      }
    }
    detailDialogVisible.title = title
  }
  //弹窗显示对象数据
  const showRowObjectDialog = (query: any, column: TableItem) => {
    objectDialogVisible.visible = true
    objectDialogVisible.title = column.label
  }
  //弹窗显示表单
  const showRowMenuFormDialog = async (row: TableItem, column: TableItem) => {
    if (column.jump?.menuId) {
      isEdit.value = true
      await globalTableColumnForm(row, column)
    }
  }
  //查看可配板数
  const showCanAllocateDetail = (row: TableItem) => {
    if (row.canList && row.canList.length > 0) {
      configurableRef.value.canList = []
      configurableRef.value.canList = Object.assign(configurableRef.value.canList, row.canList)
      configurableRef.value.configurableDialogVisible = true
    }
  }
  /**
   * 初始化表单数据
   */
  const clearFormColumn = () => {
    operationColumn.value = []
    dialog.title = ''
  }
  /**
   * 取消
   */
  const cancel = (row: TableItem) => {
    boardApplyCancel(row.id!).then((res: any) => {
      ElMessage.success(res.message)
      handleQuery()
    })
  }
  /**
   * 批量删除
   */
  const batchRead = (row: TableItem, position: string, menu: MenuVO) => {
    btnRequestUri.value = menu.meta?.uri
    if (ids.value.length <= 0) {
      ElMessage.warning('请勾选')
      return
    }
    loading.value = true
    //定义传递的参数
    let params = {} as any
    menu.meta?.form?.params?.map((_item) => {
      if (menu.meta?.form?.dependFrom === 'listData') {
        if (_item.value === '$#$#') {
          params[_item.targetName!] = ids.value
        }
      }
    })
    globalRequestApi(params, menu.meta?.form?.method!, menu.meta?.form?.formUri ?? btnRequestUri.value!)
      .then(async (res: any) => {
        ElMessage.success(res.message)
        resetQuery()
      })
      .finally(() => (loading.value = false))
  }

  const confirmDialogParams = async (params: any, row: TableItem, position: string, menu: MenuVO) => {
    const _queryParams = await topQueryGroupComponentRef.value.searchQueryTemp()
    if (menu.meta?.form?.btns![0].params) {
      //定义传递的参数
      menu.meta?.form?.btns![0].params?.map((_item) => {
        if (_item.dependFrom === 'listData') {
          composeRequestParamsMultiRow(params, _item, menu, sideBarStore.$state.storeDialogFormParams, selectTableColumn.value, ids.value)
        } else {
          composeRequestParams(params, _item, menu, sideBarStore.$state.storeDialogFormParams, null, _queryParams)
        }
      })
    }
    composeRequestQueryParams(params, null, menu, sideBarStore.$state.storeDialogFormParams, null, _queryParams)
  }
  //弹窗确认
  const topBtnConfirmDialog = async (row: TableItem, position: string, menu: MenuVO) => {
    if (menu.meta?.form?.btns![0].params) {
      if (ids.value.length <= 0) {
        ElMessage.warning('请勾选')
        return
      }
    }
    let params = {} as any
    await confirmDialogParams(params, row, position, menu)
    let res = {} as any
    try {
      if (menu.meta.form?.btns![0].method === 'post' || menu.meta.form?.btns![0].method === 'POST') {
        res = await refreshCheckPostApi(menu.meta.form.btns![0].uri!, params)
      } else if (menu.meta.form?.btns![0].method === 'get' || menu.meta.form?.btns![0].method === 'GET') {
        res = await refreshCheckGetApi(menu.meta.form.btns![0].uri!, params)
      } else if (menu.meta.form?.btns![0].method === 'postForm') {
        const _params = {
          ...params,
        }
        res = await postFormApi(menu.meta.form.btns![0].uri!, menu.meta.form.btns![0].responseType, _params)
      }
      const download = menu.meta.form?.btns![0].download
      if (download) {
        if (download === 'file') {
          if (res.data.indexOf(',') !== -1) {
            res.data.split(',').forEach((item: any) => {
              window.open(item)
            })
          } else {
            window.open(res.data)
          }
        } else if (download === 'stream') {
          await downloadFileGlobalFun(res)
        }
      }
      //如果存在jump,则根据jump中的targetField跳转到对应的菜单页面，jump.params是需要携带的参数
      else if (menu.meta.form?.btns![0].jump) {
        const { data } = res
        //跳转到menu.meta.form?.btns![0].jump.targetField对应的菜单页面
        const jumpType = menu.meta.form?.btns![0].jump?.jumpType
        if (jumpType) {
          switch (jumpType) {
            case 'editGenerateStatement':
              editGenerateStatement(data)
              break
          }
        }
      } else {
        ElMessage.success(res.message)
      }
      resetQuery()
    } catch (error) {
      loading.value = false
    }
  }
  // 同步生成结算单
  const generateSettlement = (position: string, purpose: string, menu: MenuVO) => {
    generateSettlementDialogRef.value.state.id = ''
    generateSettlementDialogRef.value.state.dialogVisible.title = '新增驾驶员结算单'
    generateSettlementDialogRef.value.state.dialogVisible.visible = true
  }
  // 生成对账单
  const generateStatement = (position: string, purpose: string, menu: MenuVO) => {
    generateStatementDialogRef.value.state.id = ''
    generateStatementDialogRef.value.state.dialogVisible.title = '新增对账单'
    generateStatementDialogRef.value.state.dialogVisible.visible = true
    generateStatementDialogRef.value.state.isView = false
  }
  // 生成维修对账单
  const generateRepairStatement = (position: string, purpose: string, menu: MenuVO) => {
    generateRepairStatementDialogRef.value.state.id = ''
    generateRepairStatementDialogRef.value.state.dialogVisible.title = '新增结算单'
    generateRepairStatementDialogRef.value.state.dialogVisible.visible = true
    generateRepairStatementDialogRef.value.state.isView = false
  }
  // 生成外协对账单
  const generateSubsidyStatement = (position: string, purpose: string, menu: MenuVO) => {
    generateSubsidyStatementDialogRef.value.state.id = ''
    generateSubsidyStatementDialogRef.value.state.dialogVisible.title = '新增结算账单'
    generateSubsidyStatementDialogRef.value.state.dialogVisible.visible = true
    generateSubsidyStatementDialogRef.value.state.isView = false
    generateSubsidyStatementDialogRef.value.tableConfig.showHandleSelection = true
  }
  // 新增质损信息
  const addQualityLoss = (position: string, purpose: string, menu: MenuVO) => {
    qualityLossDialogRef.value.state.id = ''
    qualityLossDialogRef.value.state.dialogVisible.title = menu?.meta.title
    qualityLossDialogRef.value.state.dialogVisible.visible = true
    qualityLossDialogRef.value.state.origin = 'add'
    qualityLossDialogRef.value.stepIndex = '1'
    qualityLossDialogRef.value.state.currentRow = {} as any
  }
  // 新增罚款信息
  const addFine = (position: string, purpose: string, menu: MenuVO) => {
    fineDialogRef.value.state.dialogVisible.title = menu?.meta.title ? menu?.meta.title : '新增罚款信息'
    fineDialogRef.value.state.dialogVisible.visible = true
    fineDialogRef.value.resetForm()
  }
  //重新计算
  const recompute = async (position: string, purpose: string, menu: MenuVO) => {
    const _queryParams = await topQueryGroupComponentRef.value.searchQueryTemp()
    let newParams = JSON.parse(JSON.stringify(_queryParams))
    getRecompute(newParams, requestUri.value!).then((res: any) => {
      ElMessage.success(res.message)
      resetQuery()
    })
  }
  // 变更装车时间
  const alterLoadTime = (position: string, purpose: string, menu: MenuVO) => {
    changeTimeDialogRef.value.state.ids = ids.value
    changeTimeDialogRef.value.state.updateTimeDialog.visible = true
    changeTimeDialogRef.value.state.addressType = 'start'
  }
  // 变更交车时间
  const alterActualDropTime = (position: string, purpose: string, menu: MenuVO) => {
    changeTimeDialogRef.value.state.ids = ids.value
    changeTimeDialogRef.value.state.updateTimeDialog.visible = true
    changeTimeDialogRef.value.state.addressType = 'end'
  }
  // 新增车队维修
  const addFleetReport = (position: string, purpose: string, menu: MenuVO) => {
    fleetReportDialogRef.value.state.dialogVisible.visible = true
    fleetReportDialogRef.value.state.dialogVisible.title = menu?.meta.title
    fleetReportDialogRef.value.state.origin = 'add'
    fleetReportDialogRef.value.state.formData.sourceType = '车队'
    fleetReportDialogRef.value.state.isView = false
  }
  // 新增供应商维修
  const addSupplierRepair = (position: string, purpose: string, menu: MenuVO) => {
    fleetReportDialogRef.value.state.dialogVisible.visible = true
    fleetReportDialogRef.value.state.dialogVisible.title = menu?.meta.title
    fleetReportDialogRef.value.state.origin = 'add'
    fleetReportDialogRef.value.state.formData.sourceType = '供应商'
    fleetReportDialogRef.value.state.isView = false
  }
  // 订单模板配置
  const orderTemplateConfig = (position: string, purpose: string, menu: MenuVO) => {
    orderTemplateConfigDialogRef.value.state.dialogVisible.visible = true
    orderTemplateConfigDialogRef.value.state.dialogVisible.title = menu?.meta.title
  }
  /**
   * 新增点检标准
   */
  const addInspectionConfiguration = (position: string, purpose: string, menu: MenuVO) => {
    inspectionConfigurationDialogRef.value.state.dialogVisible.visible = true
    inspectionConfigurationDialogRef.value.state.dialogVisible.title = menu?.meta.title
    inspectionConfigurationDialogRef.value.state.origin = 'add'
  }
  /**
   * 新增订单管理
   */
  const addOrderManagement = (position: string, purpose: string, menu: MenuVO) => {
    orderManagementDialogRef.value.state.dialogVisible.visible = true
    orderManagementDialogRef.value.state.dialogVisible.title = menu?.meta.title
    orderManagementDialogRef.value.state.origin = 'add'
  }

  /**
   * 更新菜单上的数字
   */
  function refreshMenuCount() {
    getMenuCount().then((res) => {
      sideBarStore.$patch((state) => {
        state.menuCount = res.data as any
      })
    })
  }

  /**
   * 更新对账单
   */
  function updateStatement(position: string, type: string, menu: MenuVO) {
    const params = {
      id: mergeFormData.value.statementId,
      ids: ids.value,
    }
    updateCarrierStatement(params).then((res: any) => {
      ElMessage.success(res.message)
      defaultTableIds.value.ids = ids.value
      handleQuery()
    })
  }

  /**
   * 自定义提示弹窗（重新校验）
   */
  async function confirmDialog(row: TableItem, position: string, meta: MetaVO) {
    //定义传递的参数
    let params = {} as any
    const storeDataParams = proxy.$sideBarStore.$state.storeDialogFormParams
    meta?.form?.btns![0].params?.map((item) => {
      composeRequestParams(params, item, null, storeDataParams, row, queryParams)
    })
    let res = {} as any
    try {
      if (meta.form?.btns![0].method === 'post' || meta.form?.btns![0].method === 'POST') {
        res = await refreshCheckPostApi(meta.form.btns![0].uri!, params)
      } else if (meta.form?.btns![0].method === 'get' || meta.form?.btns![0].method === 'GET') {
        res = await refreshCheckGetApi(meta.form.btns![0].uri!, params)
      } else if (meta.form?.btns![0].method === 'postForm') {
        const _params = {
          ...params,
        }
        res = await postFormApi(meta.form.btns![0].uri!, meta.form.btns![0].responseType, _params)
      }
      const download = meta.form?.btns![0].download
      if (download) {
        if (download === 'file') {
          if (res.data.indexOf(',') !== -1) {
            res.data.split(',').forEach((item: any) => {
              window.open(item)
            })
          } else {
            window.open(res.data)
          }
        } else if (download === 'stream') {
        }
      } else {
        ElMessage.success(res.message)
      }

      //刷新列表
      handleQuery()
    } catch (error) {
      loading.value = false
    }
  }

  // *********************以下方法为自定义方法*********************
  const appointmentDialogRef = ref()
  const laneAppointmentDialogRef = ref()
  const batchShipmentConfirmDialogRef = ref()
  const shipmentConfirmDialogRef = ref()
  const exchangeVehicleComponentRef = ref()
  const flipBoardComponentRef = ref()
  const outFleetAuditSettingAddDialogRef = ref()
  const driverEditSubsidyDialogRef = ref()
  const meetingRef = ref()
  const appointmentType = ref()
  const laneAppointmentType = ref()
  const configurableRef = ref()
  const addYkpFormDialogRef = ref()
  const batchScheduleDialogRef = ref()
  const tableDetailDialogRef = ref()
  const generateSettlementDialogRef = ref()
  const generateStatementDialogRef = ref()
  const generateRepairStatementDialogRef = ref()
  const generateSubsidyStatementDialogRef = ref()
  const qualityLossDialogRef = ref()
  const openInvoiceConfirmDialogRef = ref()
  const openInvoiceRepairConfirmDialogRef = ref()
  const emptyDispatchRef = ref()
  const openInvoiceOutsourcingConfirmDialogRef = ref()
  const accidentManageConfirmDialogRef = ref()
  const fineDialogRef = ref()
  const outsourcePaymentDialogRef = ref()
  const subsidyPaymentDialogRef = ref()
  const outsourcePaymentDetailDialogRef = ref()
  const changeTimeDialogRef = ref()
  const fleetReportDialogRef = ref()
  const resignationEmployeeManagementDialogRef = ref()
  const employeeManagementDialogRef = ref()
  const dynamicAuditDetailDialogRef = ref()
  const orderTemplateConfigDialogRef = ref()
  const unBindTireDialogRef = ref()
  const modifyCustomerIncomeDialogRef = ref()
  const inspectionConfigurationDialogRef = ref()
  const orderManagementDialogRef = ref()
  const ladderPriceDialogRef = ref()
  const outsourcePriceDialogRef = ref()

  const reconciliationAmount = ref()
  const reconciliationAccounts = ref()
  const scatteredReceivePaymentref = ref()
  const oilExpenseListBorrowDialogRef = ref()
  const etcExpenseListBorrowDialogRef = ref()
  const vehicleHistoryMileageEditRef = ref()
  const OneclickDialogRef = ref()
  const addOnRouteMaintenanceDialogRef = ref()
  const loadDetailDialogRef = ref()
  /**
   * 公路发运 - 车道预约申请弹窗
   */
  function appointment(position: string, purpose: string, menu: MenuVO, type: string) {
    appointmentType.value = type
    appointmentApi({ orderIds: ids.value }).then((res: any) => {
      appointmentDialogRef.value.state.formData = res.data
      appointmentDialog.visible = true
    })
  }

  /**
   * 批量录入
   */
  function batchShipmentConfirm() {
    shipmentOrderConfirmCheckApi({ orderIds: ids.value }).then((res: any) => {
      batchShipmentConfirmDialogRef.value.state.batchShipmentInfo = res.data
      batchShipmentConfirmDialog.visible = true
    })
  }

  const vehicleChangeParams = ref({
    transportType: '',
    shipmentType: '',
  })
  const vehicleFlipBoardParams = ref({
    transportType: '',
    shipmentType: '',
  })

  /**
   * 车辆变更弹窗
   * @param transportType 发运方式  3-公路 6-铁路 7-水路 9-分拨
   * @param shipmentType 运单类型 1-短驳 2-干线倒板 3-干线直发  100-以上三个都要
   */
  function vehicleChange(position: string, type: string, menu: MenuVO, transportType: string, shipmentType: string) {
    vehicleChangeParams.value.transportType = transportType
    vehicleChangeParams.value.shipmentType = shipmentType
    vehicleChangeDialog.visible = true
  }

  /**
   * 车辆倒板弹窗
   * @param transportType 发运方式  3-公路 6-铁路 7-水路 9-分拨
   * @param shipmentType 运单类型 1-短驳 2-干线倒板 3-干线直发  100-以上三个都要
   */
  function vehicleFlipBoard(position: string, type: string, menu: MenuVO, transportType: string, shipmentType: string) {
    vehicleFlipBoardParams.value.transportType = transportType
    vehicleFlipBoardParams.value.shipmentType = shipmentType
    flipBoardVisible.visible = true
  }

  /**
   * 会议纪要弹窗
   * @param reportFormsType 会议纪要类型 1-业务报表 2-财务报表
   */
  function addMeeting(position: string, type: string, menu: MenuVO, reportFormsType: string) {
    meetingRef.value.meetingVisible = true
    meetingRef.value.form.reportFormsType = reportFormsType
    meetingRef.value.getMeetingDetail()
    meetingRef.value.urlParams = Object.assign(meetingRef.value.urlParams, queryParams)
  }

  /**
   * 会议纪要详情弹窗
   * @param row 当前行数据
   * @param targetField 字段名
   */
  function meetingDetail(row: TableItem, targetField: string) {
    meetingRef.value.meetingVisible = true
    meetingRef.value.getMeetingDetail(row.id)
  }

  /**
   * 编辑安全标准弹窗
   */
  async function editSafeStandard(row: any, position: string, menu: MenuVO) {
    safeStandardDialogRef.value.dialogVisible.visible = true
    safeStandardDialogRef.value.dialogVisible.title = menu?.meta.title
    const { data } = await getSafetyStandardTemplateViewDetailApi({ standardNo: row.standardNo })
    safeStandardDialogRef.value.state.formData = data
    safeStandardDialogRef.value.state.formData.standardNo = data.standardNo
    safeStandardDialogRef.value.state.row = row
    safeStandardDialogRef.value.state.origin = 'editSafeStandard'
  }

  /**
   * 查看安全标准弹窗
   */
  async function viewSafeStandard(row: any, position: string, menu: MenuVO) {
    safeStandardDialogRef.value.dialogVisible.visible = true
    safeStandardDialogRef.value.dialogVisible.title = menu?.meta.title
    const { data } = await getSafetyStandardTemplateViewDetailApi({ standardNo: row.standardNo })
    safeStandardDialogRef.value.state.formData = data
    safeStandardDialogRef.value.state.formData.standardNo = data.standardNo
    safeStandardDialogRef.value.state.row = row
    safeStandardDialogRef.value.state.origin = 'viewSafeStandard'
  }

  /**
   * 查看移库计划详情弹窗
   */
  async function viewYkp(row: any, position: string, menu: MenuVO) {
    pageLoading.value = true
    pageLoadingText.value = '加载中。。。'
    addYkpFormDialogRef.value.dialogVisible.title = menu?.meta.title
    const { data } = await getWarehouseMoveInventoryPlanDetailApi(row.id)
    await addYkpFormDialogRef.value.selectBaseId(data.baseId)
    await addYkpFormDialogRef.value.selectWarehouseId(data.warehouseParam)
    await addYkpFormDialogRef.value.selectWareColumnId(data.partitionParam ? data.partitionParam.split(',') : [])
    addYkpFormDialogRef.value.state.formData = {
      name: data.name, //移库计划名称
      moveTotal: data.moveTotal, //移库数量
      baseId: data.baseId, //基地
      brandId: data.brandParam ? data.brandParam.split(',') : [], //品牌
      leaderName: data.leaderName, //负责人
      vehicleModelId: data.vehicleModelParam ? data.vehicleModelParam.split(',') : [], //车型
      leaderId: data.leaderParam,
      warehouseId: data.warehouseParam, //库
      partitionId: data.partitionParam ? data.partitionParam.split(',') : [], //区
      columnId: data.columnParam ? data.columnParam.split(',') : [], //列
      warehouseName: data.warehouseName, //库
      partitionNames: data.partitionNames, //区
      columnNames: data.columnNames, //列
      vehicleModelNames: data.vehicleModelNames, //车型
      brandNames: data.brandNames, //品牌
      stockAge: data.stockAge, //库龄
      inStoreTime: data.inStoreDatetime, //入库日期
      planInList: data.planInList.map((item: any) => {
        return {
          warehouseParam: item.warehouseParam,
          partitionParam: item.partitionParam ? item.partitionParam.split(',') : [],
          columnParam: item.columnParam ? item.columnParam.split(',') : [],
          warehouseName: item.warehouseName,
          partitionNames: item.partitionNames,
          columnNames: item.columnNames,
        }
      }),
    }

    addYkpFormDialogRef.value.dialogVisible.visible = true
    addYkpFormDialogRef.value.state.origin = 'viewYkp'
    pageLoading.value = false
  }

  /**
   * 管理适用仓库
   */
  async function editInspectionWarehouse(row: any, position: string, menu: MenuVO) {
    await openInspectionDialog(row, 'editInspectionWarehouse')
  }

  //管理适用仓库
  const openInspectionDialog = async (row: any, origin: string) => {
    //打开巡视路径弹窗
    inspectionDialogRef.value.openDialog()
    if (origin === 'addSafeStandard') {
      inspectionDialogRef.value.state.form.patrolPathList = patrolPathList
      inspectionDialogRef.value.state.form.standardNo = undefined
      inspectionDialogRef.value.state.form.id = undefined
    }
    inspectionDialogRef.value.state.origin = origin
    await inspectionDialogRef.value.getWarehouseBaseSelectOptionFun()
    if (row.standardNo) {
      const { data } = await getSafetyPatorlPathDetailApi({ standardNo: row.standardNo })
      inspectionDialogRef.value.state.form.patrolPathList = data
      inspectionDialogRef.value.state.form.standardNo = row.standardNo
      inspectionDialogRef.value.state.form.id = row.id
    }
  }
  /**
   *
   * @param form
   * @param origin edit 关闭弹窗  否则为管理适用仓库操作，直接请求接口进行修改
   */
  const setPatrolPathList = async (form: StandardTemplateSaveVO, origin?: string) => {
    if (origin === 'editInspectionWarehouse') {
      await postSafetyStandardTemplateSaveApi(form)
      ElMessage.success('操作成功')
      //刷新列表
      handleQuery()
    } else if (origin === 'addSafeStandard') {
      safeStandardDialogRef.value.state.formData.patrolPathList = form.patrolPathList
    } else {
      safeStandardDialogRef.value.state.formData.patrolPathList = form
    }
    inspectionDialogRef.value.onClose()
  }

  /**
   * 批量录入提交
   */
  function shipmentOrderConfirmSubmit(params: shipmentOrderConfirmSubmitVO) {
    shipmentOrderConfirmSubmitApi(btnRequestUri.value ?? requestUri.value!, params).then((res: any) => {
      ElMessage.success('运单确认成功')
      batchShipmentConfirmDialog.visible = false
      shipmentConfirmDialog.visible = false
      //清除表单数据
      shipmentConfirmDialogRef.value.state.batchShipmentInfo = {}
      shipmentConfirmDialogRef.value.state.formData = {}
      batchShipmentConfirmDialogRef.value.state.batchShipmentInfo = {}
      batchShipmentConfirmDialogRef.value.state.formData = {}

      //刷新列表
      handleQuery()
    })
  }

  const transportType = ref() //3 公路 4 铁水 9 分拨
  /**
   * 运单确认-一键确认
   */
  function shipmentConfirm(row: TableItem, position: string, menu: MenuVO, type: number) {
    shipmentConfirmDialog.visible = true
    transportType.value = type
    shipmentConfirmDialogRef.value.handleCarrierChange()
    //获取承运商列表
    // carrierListApi({simple: true}, type).then((res: any) => {
    // 	shipmentConfirmDialogRef.value.carriers = res.data;
    // });
  }

  /**
   * 公路发运 - 车道预约申请弹窗
   * @param purpose 按钮类型
   * @param position 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   * @param menu 按钮信息
   */
  function appointmentVehicleShipmentInfo(purpose: string, position?: string, menu?: MenuVO) {
    laneAppointmentType.value = '3'
    appointmentVehicleShipmentInfoApi({ carrierType: '3' }).then((res: any) => {
      laneAppointmentDialogRef.value.state.carriers = res.data
      //设置默认选中第一家承运商
      if (res.data.carriers && res.data.carriers.length > 0) {
        laneAppointmentDialogRef.value.state.formData.carrierId = res.data.carriers[0].carrierId
        laneAppointmentDialogRef.value.carrierChange(res.data.carriers[0].carrierId)
      }
      laneAppointmentDialog.visible = true
    })
  }

  /**
   * 铁水 - 车道预约申请弹窗
   * @param purpose 按钮类型
   * @param position 按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   * @param menu 按钮信息
   */
  function railWaterwayAppointmentVehicleShipmentInfo(purpose: string, position?: string, menu?: MenuVO) {
    laneAppointmentType.value = '4'
    appointmentVehicleShipmentInfoApi({ carrierType: '4' }).then((res: any) => {
      laneAppointmentDialogRef.value.state.carriers = res.data
      laneAppointmentDialog.visible = true
    })
  }

  /**
   * 提交换车
   * @param data 表单数据
   * @param tabActive 选中的tab 0 同承运商 1不同承运商
   */
  const exchangeVehicleResult = (data: ExchangeVehicleParamsVo, tabActive: number) => {
    let params: ExchangeVehicleParamsVo = JSON.parse(JSON.stringify(data))
    params.body!.forEach((v) => {
      delete v.addressType
    })
    transferVehicle(params!).then((res) => {
      exchangeVehicleComponentRef.value!.formData = {
        body: [
          {
            addressType: 1,
          },
        ],
        operationType: 1, //操作类型1-倒板 2-换车
        source: 1, //申请来源：0-调度 1-司机
        origin: {
          vehicleId: '',
          driverId: '',
        },
        shipmentNo: '',
      }
      vehicleChangeDialog.visible = false
      ElMessage.success('提交成功')
      //刷新列表
      handleQuery()
    })
  }
  /**
   * 提交倒板
   */
  const filpBoardResult = (params: ExchangeVehicleParamsVo) => {
    params.body!.forEach((v) => {
      delete v.addressType
    })
    invertedDistribution(params!).then((res) => {
      flipBoardComponentRef.value!.formData = {
        body: [
          {
            addressType: 1,
            idList: [],
          },
        ],
        operationType: 1, //操作类型1-倒板 2-换车
        source: 0, //申请来源：0-调度 1-司机
      }
      flipBoardVisible.visible = false
      ElMessage.success('提交成功')

      handleQuery()
    })
  }
  //确定创建审批流
  const outFleetFeesAuditSettingConfirmSubmit = () => {
    outFleetAuditSettingAddDialog.visible = false
    //刷新列表
    handleQuery()
  }

  //司机计算补贴 - 修改费用 - 提交
  const subsidyConfirmSubmit = () => {
    driverEditSubsidyDialog.visible = false
    //刷新列表
    handleQuery()
  }
  // 编辑月份 - 提交
  const subsidyConfirmSubmited = () => {
    handleQuery()
  }

  /**
   * 关闭车道预约弹窗
   */
  function closeAppointmentDialog() {
    appointmentDialog.visible = false
    //刷新列表
    handleQuery()
  }

  /**
   * 关闭车道预约弹窗
   */
  function closeLaneAppointmentDialog() {
    laneAppointmentDialog.visible = false
    //刷新列表
    handleQuery()
  }

  /**
   * 关闭批量录入弹窗
   */
  function closeBatchShipmentConfirmDialog() {
    batchShipmentConfirmDialog.visible = false
  }

  /**
   * 关闭一键录入弹窗
   */
  function closeShipmentConfirmDialog() {
    shipmentConfirmDialog.visible = false
  }

  /**
   * 关闭车辆变更弹窗
   */
  function closeVehicleChangeDialog() {
    vehicleChangeDialog.visible = false
  }

  /**
   * 关闭审批设置弹窗
   */
  function closeOutFleetAuditSettingAddDialog() {
    outFleetAuditSettingAddDialog.visible = false
  }

  /**
   * 关闭司机计算补贴修改费用
   */
  function closeDriverEditSubsidyDialog() {
    driverEditSubsidyDialog.visible = false
  }

  /**
   * 关闭批量调度弹窗
   */
  function closeBatchScheduleDialog() {
    batchScheduleDialog.visible = false
  }

  /**
   * 关闭空调度单
   */
  function closeEmptyDispatchDialog() {
    emptyDispatchDialog.visible = false
  }

  function confirmDispatchSuccess() {
    closeBatchScheduleDialog()
    //刷新列表
    handleQuery()
  }

  function confirmEmptyDispatchSuccess() {
    closeEmptyDispatchDialog()
    //刷新列表
    handleQuery()
  }

  // *********************以上方法为自定义方法*********************
  const menuList = ref<MenuVO[]>()
  const userInfo = ref<UserPageVO>() //用户信息
  const currentMemuColumnList = ref<CheckedMenuColumnVO[]>() //当前选中的菜单下的数据列
  const menuDialogPermissionGroup = ref<MenuVO[]>() //数据列权限分配弹窗中的操作按钮
  const menuDialogRef = ref() //菜单弹窗
  /**
   * 菜单弹窗
   */
  const menuDialogVisible = reactive<DialogOption>({
    visible: false,
  })
  /**
   * 获取当前用户菜单数据
   */
  const getUserMenuList = (assignUserId?: string) => {
    userMenulist(assignUserId, requestUri.value!).then((res) => {
      menuList.value = getNewUserMenuList(res.data)
      menuDialogVisible.visible = true
    })
  }
  /**
   * 递归为菜单每项赋值title为meta中的title，columnName 为meta中的dataColumn中数组中的name
   * @param data 菜单项
   */
  const getNewUserMenuList = (data: MenuVO[]) => {
    data.forEach((item: MenuVO) => {
      item.title = item.meta?.title
      let columns: any[] = []
      item.meta?.dataColumn.forEach((i: any) => {
        columns.push(i.label)
      })
      item.columnName = columns.join(',') ? columns.join(',') : undefined
      item.type = typeConversion(item.meta?.type) //类型 用于区分菜单和目录
      if (item.children && item.children.length > 0) {
        getNewUserMenuList(item.children)
      }
    })
    return data
  }
  /**
   * 获取当前登录用户下的当前菜单下的数据列
   */
  const getcurrentLoginUserMenuColumnlist = async (row: MenuVO) => {
    currentLoginUserMenuColumnlist(row.menuId, 'logistics/system/authorization/columns')
      .then((res) => {
        const { data } = res
        //当前登录用户下的菜单数据列
        bus.emit('currentLoginUserMemuColumnList', data)
        //当前用户下的菜单数据列
        bus.emit('userMemuColumnList', row.meta?.dataColumn)
      })
      .catch((err) => {
        menuDialogRef.value.menuColumnLoading = false
      })
  }

  /**
   * 重置密码
   */
  function updatePassword(row: UserPageVO) {
    userInfo.value = row
    ElMessageBox.prompt('请输入用户「' + row.realName + '」的新密码', '重置密码', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })
      .then(({ value }) => {
        if (!value) {
          ElMessage.warning('请输入新密码')
          return false
        }
        updateUserPassword(row.id, value, requestUri.value!).then(() => {
          ElMessage.success('密码修改成功，新密码是：' + value)
        })
      })
      .catch(() => {})
  }

  /**
   * 获取用户当前菜单下的数据列
   */
  const selectMenuColumn = (selectMenuColumn: string) => {
    const params: CheckedMenuColumnVO = JSON.parse(selectMenuColumn)
    distributeMenuColumn(params, requestUri.value!).then((res) => {
      ElMessage.success('分配成功')
      menuDialogRef.value.menuColumnDialogVisible.visible = false
      getUserMenuList(userInfo.value?.id)
    })
  }
  /**
   * 操作权限
   */
  const openMenuDialog = (row: UserPageVO) => {
    getUserMenuList(row.id)
    userInfo.value = row
  }
  /**
   * 菜单树加载状态
   */
  const menuTreeLoading = ref(false)
  /**
   * 菜单弹窗
   */
  const dataColumnMenuDialogVisible = reactive<DialogOption>({
    visible: false,
  })
  /**
   * 选中的角色
   */
  const checkedRole = ref<CheckedRole>({})
  /**
   * 菜单列表
   */
  const dataColumnMenuList = ref<OptionType[]>([])

  /**
   * 关闭菜单弹窗
   */
  function closeMenuDialog() {
    dataColumnMenuDialogVisible.visible = false
  }

  const dataColumnMenuDialogRef = ref()
  /**
   * 获取当前角色选中的菜单
   */
  const selectMenuIds = (menuIds: string[], roleId: string) => {
    const params = {
      roleId,
      menuIds,
    }
    loading.value = true
    setMenus(params)
      .then((res) => {
        ElMessage.success('分配成功')
        dataColumnMenuDialogVisible.visible = false
        resetQuery()
      })
      .finally(() => {
        loading.value = false
        checkedRole.value.menuIds = []
      })
  }
  /**
   * 字段权限
   */
  const openDataColumnMenuDialog = async (row: UserPageVO) => {
    dataColumnMenuDialogVisible.visible = true
    checkedRole.value.id = row.id
    checkedRole.value.name = row.name
    checkedRole.value.menuIds = row.menuIds
    await getListMenuOptions()
    nextTick(() => {
      dataColumnMenuDialogRef.value.setFlexForChildren()
    })
  }
  /**
   * 获取菜单下拉树形列表(包含按钮)
   */
  const getListMenuOptions = () => {
    return new Promise((resolve, reject) => {
      menuTreeLoading.value = true
      listMenuOptions()
        .then((res) => {
          const { data } = res
          data.forEach((value) => {
            dataColumnMenuDialogRef.value.menuRef.setChecked(value, true, false)
          })
          dataColumnMenuList.value = data
          menuTreeLoading.value = false
          resolve(data)
        })
        .catch((err) => {
          menuTreeLoading.value = false
          reject(err)
        })
    })
  }
  /**
   * 更新数据
   * @param row 当前行数据
   * @param position
   * @param menu
   */
  const editAuth = async (row: UserForm, position: string, menu: MenuVO) => {
    if (menu.meta!.dataColumn.length > 0) {
      operationColumn.value = await refreshFormSelectOptions(menu.meta!.dataColumn)
    } else {
      operationColumn.value = await refreshFormSelectOptions(tableConfig.tableItem)
    }
    dialog.visible = true
    if (row?.id) {
      dialog.title = '修改用户'
      isEdit.value = true
      const userInfo = await getUserDetail(row.id)
      userInfo.roles = userInfo.roleIds
      //初始化表单
      const initFormData = resetFormGlobalFun(tableConfig.tableItem!)
      nextTick(() => {
        Object.assign(formDialogRef.value.formData, initFormData)
        Object.assign(formDialogRef.value.formData, userInfo)
      })
    } else {
      dialog.title = '新增用户'
      isEdit.value = false
    }
  }

  /**
   * 获取用户信息
   */
  async function getUserDetail(id: string) {
    const { data } = await getDetailById(id, requestUri.value!)
    return data
  }

  const { searchParams } = toRaw(formStore)
  // 弹窗菜单
  const showMenuDialog = (position: string, purpose: string, menu: MenuVO) => {
    //获取需要传递的formData中的属性保存到store中1
    let mergeDialogFormParams: { [key: string]: any } = {}
    let storeDialogFormParams: { [key: string]: any } = {}
    const storeData = sideBarStore.$state.storeDialogFormParams || {}
    const storeFormData = sideBarStore.$state.mergeDialogFormParams || {}
    if (menu?.meta?.form?.storeData) {
      for (let item of menu.meta.form?.storeData!) {
        composeRequestParams(storeDialogFormParams, item, null, null, null, searchParams.value)
      }
    }
    let query: { [key: string]: any } = {}
    if (menu?.meta?.form?.query) {
      for (let item of menu.meta.form?.query!) {
        composeRequestParams(query, item, null, null, null, searchParams.value)
      }
    }
    // router.push({
    //   name: column.jump?.targetField,
    //   query: query,
    // });
    sideBarStore.$patch((state) => {
      state.menu = menu
      state.btnMenuId = menu.meta.form?.menuId!
      state.btnMenuQuery = query
      state.mergeDialogFormParams = Object.assign(storeFormData, mergeDialogFormParams) //获取需要传递的formData中的属性保存到store中
      state.storeDialogFormParams = Object.assign(storeData, storeDialogFormParams) //获取需要传递的全局存储的属性保存到store中
    })
    detailDialogVisible.visible = true
    detailDialogVisible.title = menu.meta.form!.title
  }
  //菜单弹窗数据end
  const setFullscreen = () => {
    isFullscreen.value = !isFullscreen.value
  }
  const sortChange = (data: { column: string; direction: string }[]) => {
    //查询列表
    //&customerOrderBy=notInvoicedMount desc以这种形式频道拼到请求参数后
    const customerOrderBy = data.map((item) => {
      return item.column + ' ' + item.direction
    })
    queryParams.customerOrderBy = customerOrderBy.join(',')
    resetQuery()
  }
  const handleMsgQuery = () => {
    //遍历rightTipDataObj.value.timeoutParam对象，将对象中的每项拼到查询参数上，重新查询列表
    const timeoutParam = rightTipDataObj.value!.timeoutParam
    const timeoutParamList = Object.keys(timeoutParam as Record<string, any>)
    timeoutParamList.forEach((item) => {
      queryParams[item] = (timeoutParam as Record<string, any>)[item]
    })
    resetQuery()
  }
  const refreshRightTipData = () => {
    globalRequestUrlApi({ menuId: proxy.$sideBarStore.$state.menuId }, rightTipObj.value!.method, rightTipObj.value!.uri).then((res: any) => {
      const data = res.data as RightTipDataVO
      rightTipDataObj.value = data
    })
  }
</script>
<style lang="scss" scoped>
  .statusText {
    overflow: hidden;
  }
</style>
<style lang="scss">
  .el-overlay:has(.open-in-tab) {
    position: absolute;

    .el-overlay-dialog {
      position: absolute;
    }
  }
</style>
