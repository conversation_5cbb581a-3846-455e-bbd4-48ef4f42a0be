<!--
@name: 人员总表
* @author: llm
* @date: 2022/12/7 11:37
* @description: 人员总表
* @update: 2022/12/7 11:37
-->

<template>
  <div v-loading="showLoading" class="components-container" element-loading-text="加载中...">
    <el-form ref="filterForm" :inline="true" :model="searchParams" label-width="45px">
      <el-form-item label="姓名">
        <el-select v-model="searchParams.id" clearable filterable placeholder="请输入需要查询的姓名" style="width: 200px">
          <el-option v-for="item in driverList" :key="item.id" :label="item.driverRealName" :value="item.id" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="岗位">
        <el-input v-model="searchParams.positionName" placeholder="请输入岗位名称" />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="flex items-center">
      <!-- <el-button type="primary" @click="addPersonalData">新增人员</el-button> -->
      <el-button type="primary" @click="createColumn">自定义表头项</el-button>
      <upload-excel-component style="margin-right: 10px" :loading="importLoading" @importExcelFile="importExcelFile" />
      <el-button :loading="btnLoading" type="primary" @click.stop="exportTable">批量导出</el-button>
      <!-- <el-button  type="primary" @click="showCalendarDialog(true)">工作日历设定</el-button> -->
    </div>
    <div ref="topHeight" class="pagination-container mt-10px">
      <Table
        ref="filterTable"
        :data="tableData"
        :is-pagination="tableConfig.isPagination"
        :loading="tableConfig.loading"
        :max-height="tableConfig.tableHeight"
        :table-config="tableConfig"
        @deleteItem="delRow"
        @departPersonnel="departPersonnel"
        @getBindingInfoByDriverId="getBindingInfoByDriverId"
        @modifyPhone="modifyPhone"
        @showDialogVisible="showEditDialogVisible"
      />
    </div>
    <Pagination :limit="searchParams.limit" :page="searchParams.page" :total="total" @pagination="pagination" />
    <!--  动态创建表头项  -->
    <el-dialog :draggable="true" :close-on-click-modal="false" :show-close="false" v-model="showDynamicColumnDialog" title="自定义表头项" width="840px">
      <div v-loading="tableHeaderLoading">
        <el-form ref="ruleForm" :inline="true" :model="formData">
          <div class="flex-row justify-between" style="max-height: 60vh; overflow-y: scroll">
            <div>
              <el-card>
                <div>
                  <VueDraggable v-model="formData.addition" class="draggable-list">
                    <el-form-item v-for="(item, index) in formData.addition" :key="item.columnKey" :label="`名称${index + 1}`" prop="name">
                      <el-input
                        v-model="item.columnName"
                        :disabled="
                          item.columnName === '姓名' || item.columnName === '手机号码'
                          // ||
                          // item.columnName === '岗位' ||
                          // item.columnName === '入职日期' ||
                          // item.columnName === '排序' ||
                          // item.columnName === '出生日期'
                        "
                        :placeholder="`请输入名称${index + 1}`"
                        style="width: 140px; margin-right: 5px"
                      />
                      <el-select
                        v-model="item.columnType.columnTypeId"
                        :disabled="
                          item.columnName === '姓名' || item.columnName === '手机号码'
                          //  ||
                          // item.columnName === '岗位' ||
                          // item.columnName === '入职日期' ||
                          // item.columnName === '排序' ||
                          // item.columnName === '出生日期'
                        "
                        placeholder="选择类型"
                        style="width: 100px; margin-right: 10px"
                        value-key="columnTypeId"
                        @change="(e) => handleColumnTypeChange(e, item, index)"
                      >
                        <el-option v-for="_item in tableHeaderType" :key="_item.value" :label="_item.text" :value="_item.value" />
                      </el-select>
                      <el-button
                        v-if="index === formData.addition.length - 1"
                        link
                        type="primary"
                        size="small"
                        @click="addItem('A', item.columnName, item.columnType)"
                        >添加</el-button
                      >
                      <el-button
                        v-if="
                          item.columnName !== '姓名' && item.columnName !== '手机号码'
                          //  &&
                          // item.columnName !== '岗位' &&
                          // item.columnName !== '入职日期' &&
                          // item.columnName !== '排序' &&
                          // item.columnName !== '出生日期'
                        "
                        style="color: red"
                        link
                        type="primary"
                        size="small"
                        @click="delColumn('addition', index, item.id)"
                      >
                        删除
                      </el-button>
                    </el-form-item>
                  </VueDraggable>
                </div>
              </el-card>
            </div>
          </div>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="close('ruleForm')">取消</el-button>
        <el-button type="primary" @click="confirm('ruleForm')">保存</el-button>
      </template>
    </el-dialog>
    <!--  新增人员  -->
    <el-dialog
      :draggable="true"
      :close-on-click-modal="false"
      :show-close="false"
      :title="type === 'add' ? '新增人员' : '修改人员信息'"
      v-model="showAddPersonalDataDialog"
      class="personnelClass"
      top="10vh"
      width="940px"
    >
      <div>
        <el-form ref="personalDataRuleForm" :inline="true" :model="personalData" :rules="rules" label-width="120px">
          <div class="flex-row hdd-flex-justify-around">
            <el-card class="box-card">
              <el-form-item v-for="(item, index) in formData.addition" :key="index" :label="item.columnName" :prop="'dynamicColumnValues.' + item.columnKey">
                <el-date-picker
                  v-model="personalData.dynamicColumnValues[item.columnKey]"
                  type="date"
                  :clearable="false"
                  placeholder="选择日期"
                  v-if="item.columnName === '入职日期'"
                  style="width: 140px; margin-right: 5px"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                ></el-date-picker>
                <el-date-picker
                  v-model="personalData.dynamicColumnValues[item.columnKey]"
                  type="date"
                  :clearable="false"
                  placeholder="选择日期"
                  v-else-if="item.columnName === '出生日期'"
                  style="width: 140px; margin-right: 5px"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                ></el-date-picker>
                <el-input
                  v-else
                  v-model="personalData.dynamicColumnValues[item.columnKey]"
                  :disabled="(type === 'edit' && item.columnName === '姓名') || (type === 'edit' && item.columnName === '手机号码')"
                  :placeholder="`请输入${item.columnName}`"
                  style="width: 140px; margin-right: 5px"
                />
              </el-form-item>
            </el-card>
          </div>
        </el-form>
      </div>
      <!-- 按钮 -->
      <template #footer>
        <span class="submit-btn">
          <el-button @click="close('personalDataRuleForm')">取消</el-button>
          <el-button type="primary" @click="confirmPersonalData('personalDataRuleForm')">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- <el-dialog :draggable="true" v-model="showCalendar" class="calendar" title="工作日历设定" width="800px">
            <el-calendar v-model="value" style="width: 760px">
                <template slot="dateCell" slot-scope="{ date, data }">
                    <div
                        :class="{ selected: isSelected(date, data) }"
                        style="position: relative"
                        @click="calendarOnClick(data)"
                    >
                        <div class="solar">{{ data.day.split('-')[2] }}</div>
                        <div v-if="isSelected(date, data)" class="ban">班</div>
                        <div :class="{ festival: isFestival(date, data) }" class="lunar">
                            {{ solarToLunar(date, data) }}
                        </div>
                    </div>
                </template>
            </el-calendar>
            <div style="color: red">*注：请在日历中勾选实际工作日，日历默认显示的为国家工作日</div>
            <span slot="footer" class="submit-btn">
                <el-button class="submitClass" @click="cancel('form')">取消</el-button>
                <el-button class="submitClass" type="primary" @click="setWorkingCalendarForm"
                    >确定</el-button
                >
            </span>
        </el-dialog> -->
    <!--   修改手机号弹窗     -->
    <el-dialog :draggable="true" v-model="showModifyPhone" title="修改手机号" width="500px">
      <el-form ref="ruleModifyPhone" :model="modifyPhoneForm" :rules="rules">
        <el-form-item label="姓名">
          <div>{{ currentRow['姓名'] }}</div>
        </el-form-item>
        <el-form-item label="原手机号">
          <div>{{ currentRow['手机号码'] }}</div>
        </el-form-item>
        <el-form-item label="新手机号" prop="driverMobile">
          <el-input v-model="modifyPhoneForm.driverMobile" placeholder="请输入新手机号" style="width: 140px; margin-right: 5px" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div>
          <el-button class="submitClass" @click="cancelPhone('ruleModifyPhone')">取消</el-button>
          <el-button class="submitClass" type="primary" @click="setNewPhone('ruleModifyPhone')">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!--   设置离职时间弹窗     -->
    <el-dialog :draggable="true" v-model="showDepartDialog" title="离职设置" width="500px">
      <el-form :model="departForm" :rules="rules">
        <el-form-item label="姓名:">
          <div>{{ departForm.driverName }}</div>
        </el-form-item>
        <el-form-item label="离职日期:" prop="termDate">
          <el-date-picker
            v-model="departForm.termDate"
            :disabled="departForm.haveTermed"
            format="yyyy 年 MM 月 dd 日"
            placeholder="选择日期"
            style="width: 200px"
            type="date"
            value-format="yyyy-MM-dd"
            @change="changeTims"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button class="submitClass" @click="cancelDepart()">取消</el-button>
        <el-button class="submitClass" type="primary" @click="submitDepart">确定</el-button>
      </template>
    </el-dialog>

    <!--		人员绑定信息弹窗    -->
    <el-dialog :draggable="true" v-model="showBindingInfo" class="calendar" title="绑定信息" width="800px">
      <el-form>
        <el-form-item label="出勤组:">
          {{ bindInfo.enterpriseWorkCalendarGroup ? bindInfo.enterpriseWorkCalendarGroup.groupName : '未分组' }}
        </el-form-item>
        <el-form-item label="出勤日历:"></el-form-item>
        <!-- <el-calendar v-model="value" style="width: 760px">
                    <template slot="dateCell" slot-scope="{ date, data }">
                        <div :class="{ selected: isSelected(date, data) }" style="position: relative">
                            <div class="solar">{{ data.day.split('-')[2] }}</div>
                            <div v-if="isSelected(date, data)" class="ban">班</div>
                            <div :class="{ festival: isFestival(date, data) }" class="lunar">
                                {{ solarToLunar(date, data) }}
                            </div>
                        </div>
                    </template>
                </el-calendar> -->
        <el-calendar v-model="value" style="width: 100%">
          <template slot="dateCell" slot-scope="{ date, data }">
            <div :class="{ selected: isSelected(date, data) }" style="position: relative">
              <div class="solar">{{ data.day.split('-')[2] }}</div>
              <div v-if="isHalfDay(date, data)" class="ban">半</div>
              <div v-if="isAllDay(date, data)" class="ban">班</div>
              <div :class="{ festival: isFestival(date, data) }" class="lunar">
                {{ solarToLunar(date, data) }}
              </div>
            </div>
          </template>
        </el-calendar>
        <el-form-item label="绑定车辆:">
          <el-row>
            <el-col v-for="(item, index) in bindInfo.vehicles" :key="index" :span="3">
              <div>
                {{ item.vehicleNo }}
              </div>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="绑定围栏:">
          <el-row>
            <el-col v-for="(item, index) in bindInfo.punchCardStations" :key="index" :span="8">
              <div>
                {{ item.punchCardStationName }}
              </div>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <span slot="footer"></span>
    </el-dialog>
  </div>
</template>

<script>
  import Table from '@/components/TmsTableComponent/table.vue'
  import {
    getGeneralPersonalTableData,
    batchSaveDynamicColumn,
    getDynamicColumn,
    deleteColumn,
    addPersonalData,
    updatePersonalData,
    deletePersonalData,
    exportPersonalExcel,
    importPersonalExcel,
    showWorkCalendar,
    setWorkCalendar,
    modifyMobile,
    getPersonalBindingInfo,
    termedData,
  } from '@/api/basicData/organizationManage/personnelList'
  import {
    getListSingleGeoFences, //围栏选择列表
  } from '@/api/transitManagement/fanceManage'
  import Pagination from '@/components/TmsPagination/index.vue'
  import UploadExcelComponent from '@/components/TmsUploadExcel/index.vue'
  import { tableHeaderType } from '@/utils/selectJson'
  import { getScollerHeight } from '@/utils'
  import calendar from '@/utils/calendar'
  import { ElMessage } from 'element-plus'
  import { VueDraggable } from 'vue-draggable-plus'
  import { downloadFileGlobalFun } from '@/utils/common'
  import { exportExcel } from '@/api/auth'
  import defaultSettings from '@/settings'
  export default {
    name: 'GeneralPersonalTable',
    components: {
      Table,
      Pagination,
      UploadExcelComponent,
      VueDraggable,
    },
    data() {
      return {
        value: new Date(),
        selectedDates: [],
        importLoading: false, //导入加载
        showCalendar: false, //显示出勤日历
        showModifyPhone: false, //修改手机号
        showDepartDialog: false, //修改离职设置
        showBindingInfo: false, //人员绑定关系弹窗
        setSalendar: true, // 是否点击了工作日历设定
        type: 'add', // add or edit
        searchParams: {
          page: 1,
          limit: defaultSettings.globalLimit,
          driverId: '',
          positionName: '',
        },
        total: 0,
        driverList: [], //人员列表
        dynamicAddition: [
          {
            columnKey: '姓名',
            columnName: '姓名',
            columnType: {
              columnTypeId: '9',
              columnTypeName: '其他',
            },
          },
          {
            columnKey: '手机号码',
            columnName: '手机号码',
            columnType: {
              columnTypeId: '9',
              columnTypeName: '其他',
            },
          },
          // {
          //     columnKey: '出生日期',
          //     columnName: '出生日期',
          //     columnType: {
          //         columnTypeId: '9',
          //         columnTypeName: '其他'
          //     }
          // },
          // {
          //     columnKey: '岗位',
          //     columnName: '岗位',
          //     columnType: {
          //         columnTypeId: '9',
          //         columnTypeName: '其他'
          //     }
          // },
          // {
          //     columnKey: '入职日期',
          //     columnName: '入职日期',
          //     columnType: {
          //         columnTypeId: '9',
          //         columnTypeName: '其他'
          //     }
          // },
          // {
          //     columnKey: '排序',
          //     columnName: '排序',
          //     columnType: {
          //         columnTypeId: '9',
          //         columnTypeName: '其他'
          //     }
          // }
        ], //增加项表头
        formData: {
          addition: [],
        }, //工资项
        personalData: {
          driverRealName: '',
          // driverName: '',
          // positionName: '',
          driverMobile: '',
          // joinedDate: '',
          // sortNo: '',
          // birthday: '',
          // punchCardStations: [],
          dynamicColumnValues: {},
        }, //人员信息
        tableHeaderLoading: false, //自定义表头项弹窗loading
        tableData: [],
        showDialogVisible: false,
        showDynamicColumnDialog: false, //显示动态创建工资项弹窗
        showAddPersonalDataDialog: false, //显示新增人员弹窗
        tableConfig: {
          name: 'generalPersonalTable',
          showHandleSelection: false, // 显示复选框
          loading: false,
          isPagination: false, // 是否需要分页
          tableHeight: null, // 表格高度
          tableItem: [],
          operation: {
            label: '操作',
            name: [
              '编辑',
              //  '删除', '修改手机号', '离职'
            ],
            width: '120',
          },
        },
        rules: {
          'dynamicColumnValues.姓名': [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
          // 'dynamicColumnValues.岗位': [{ required: true, message: '岗位不能为空', trigger: 'blur' }],
          'dynamicColumnValues.手机号码': [
            { required: true, message: '手机号码不能为空', trigger: 'blur' },
            {
              pattern: /^(1[3456798]\d{9})$/,
              message: '请输入正确的号码',
              trigger: 'blur',
            },
          ],
          // driverMobile: [
          //     { required: true, message: '手机号码不能为空', trigger: 'blur' },
          //     {
          //         pattern: /^(1[3456798]\d{9})$/,
          //         message: '请输入正确的号码',
          //         trigger: 'blur'
          //     }
          // ]
        },
        enclosureDataList: [], //围栏列表
        btnLoading: false, //按钮loading
        tableHeaderType: null, //表头类型
        isOk: false, //点击自定义表头项保存按钮校验是否所有内容都不为空，都不为空则为true
        currentRow: false, //选中的当前行
        modifyPhoneForm: {
          driverMobile: '', //新手机号
        },
        bindingParams: {
          year: '',
          driverId: '',
        },
        // 离职设置
        departForm: {
          id: '',
          driverName: '',
          termDate: '',
          haveTermed: false,
        },
        bindInfo: {}, //绑定信息
        showLoading: false,
      }
    },
    watch: {
      'bindingParams.year': {
        handler() {
          this.getBindingCalender()
        },
      },
      showBindingInfo(newVal) {
        if (newVal) {
          this.getBindingCalender()
        }
      },
    },
    async mounted() {
      //设置table高度
      this.tableConfig.tableHeight = getScollerHeight(this.$refs.topHeight.offsetHeight - 35)
      this.tableHeaderType = tableHeaderType() //表头类型
      await this.getDriverList() // 获取司机列表
      await this.getDynamicColumnFun() //获取自定义表头
      await this.getGeneralPersonalTableDataFun() //获取表格数据
      // await this.getListSingleGeoFences() // 获取可选择围栏列表
    },
    methods: {
      //获取人员绑定的日历
      async getBindingCalender() {
        const data = await getPersonalBindingInfo(this.bindingParams)
        this.bindInfo = data
        this.selectedDates = data.workCalendarRe.workDays
      },
      cancelPhone() {
        this.showModifyPhone = false
        this.modifyPhoneForm.driverMobile = ''
      },
      async setNewPhone(formName) {
        await this.$refs[formName].validate(async (valid) => {
          if (valid) {
            let params = {
              id: this.currentRow.id,
              driverMobile: this.modifyPhoneForm.driverMobile,
            }
            await modifyMobile(params)
            ElMessage.success('修改成功')
            this.showModifyPhone = false
            await this.getGeneralPersonalTableDataFun()
          } else {
            console.log('校验失败')
          }
        })
      },
      //修改手机号
      modifyPhone(row) {
        this.currentRow = row
        this.showModifyPhone = true
      },
      // 关闭离职设置
      cancelDepart() {
        this.showDepartDialog = false
        this.departForm.id = ''
        this.departForm.driverName = ''
        this.departForm.termDate = ''
        this.departForm.haveTermed = false
      },
      // 离职时间选择
      changeTims(value) {
        if (value) {
          this.departForm.termDate = value
        }
      },
      //离职设置
      departPersonnel(row) {
        this.departForm.driverName = row.driverName
        this.departForm.termDate = row.termDate
        this.departForm.haveTermed = row.haveTermed
        this.departForm.id = row.id
        this.showDepartDialog = true
      },
      // 确定修改离职时间
      submitDepart() {
        if (!this.departForm.termDate) {
          ElMessage.success('请填写离职日期后提交！')
          return false
        }
        ElMessageBox.confirm('人员设定为离职后无法恢复，是否确认设置', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          let params = {
            termDate: this.departForm.termDate,
            haveTermed: true,
          }
          termedData(params, this.departForm.id).then((response) => {
            ElMessage.success('设置成功')
            this.showDepartDialog = false
            this.searchParams.page = 1
            // 获取人员列表
            this.getGeneralPersonalTableDataFun()
          })
        })
      },

      // 获取司机列表
      getDriverList() {
        return new Promise((resolve, reject) => {
          getGeneralPersonalTableData({
            page: 1,
            limit: 9999,
          })
            .then((response) => {
              const { data } = response
              this.driverList = data.rows
              resolve()
            })
            .catch(() => {
              this.tableConfig.loading = false
              reject()
            })
        })
      },
      //设置工作日历
      async setWorkCalendarFun() {
        const date = new Date()
        const year = date.getFullYear()
        const res = await setWorkCalendar({ year })
      },
      // 获取可选择围栏列表
      getListSingleGeoFences() {
        const params = {
          geoFenceName: '', // 围栏名称
          nameId: '', //围栏类型
          province: '', //围栏省份
        }
        getListSingleGeoFences(params).then((response) => {
          if (response.length > 0) {
            this.enclosureDataList = response
          } else {
            this.enclosureDataList = []
          }
        })
      },
      //添加项
      addItem(type, name, columnType) {
        if (!name) {
          ElMessage.error('请先把名称填写完整再增加')
          return false
        }
        if (!columnType.columnTypeId) {
          ElMessage.error('请先选择类型再添加')
          return false
        }
        //去掉自己，然后和其他项去查重
        const newArr = this.formData.addition.slice(0, -1)
        if (
          newArr.find((v) => {
            return v.columnName === name
          })
        ) {
          ElMessage.error('名称重复，请检查')
          return false
        }
        this.formData.addition.push({
          columnName: '',
          columnKey: type + new Date().getTime(),
          columnType: {
            columnTypeId: '',
            columnTypeName: '',
          },
        })
      },
      //删除项
      delColumn(type, index, id) {
        if (id) {
          ElMessageBox.confirm('删除之前请先保存当前修改，否则修改数据将会丢失', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              this.tableHeaderLoading = true
              deleteColumn(id).then(() => {
                ElMessage.success('删除成功')
                this.tableHeaderLoading = false
                this.getDynamicColumnFun()
              })
            })
            .catch(() => {
              ElMessage.success('已取消删除')
            })
            .finally(() => {
              this.tableHeaderLoading = false
            })
        } else {
          this.formData[type].splice(index, 1)
        }
      },
      //打开创建表头项弹窗
      createColumn() {
        if (this.dynamicAddition.length === 0) {
          // 初始化自定义表头项 -- 默认值
          this.formData.addition = [
            {
              columnKey: '姓名',
              columnName: '姓名',
              columnType: {
                columnTypeId: 9,
                columnTypeName: '其他',
              },
            },
            {
              columnKey: '手机号码',
              columnName: '手机号码',
              columnType: {
                columnTypeId: 9,
                columnTypeName: '其他',
              },
            },
            // {
            //     columnKey: '出生日期',
            //     columnName: '出生日期',
            //     columnType: {
            //         columnTypeId: 9,
            //         columnTypeName: '其他'
            //     }
            // },
            // {
            //     columnKey: '岗位',
            //     columnName: '岗位',
            //     columnType: {
            //         columnTypeId: 9,
            //         columnTypeName: '其他'
            //     }
            // },
            // {
            //     columnKey: '入职日期',
            //     columnName: '入职日期',
            //     columnType: {
            //         columnTypeId: 9,
            //         columnTypeName: '其他'
            //     }
            // },
            // {
            //     columnKey: '排序',
            //     columnName: '排序',
            //     columnType: {
            //         columnTypeId: 9,
            //         columnTypeName: '其他'
            //     }
            // }
          ]
        }
        this.showDynamicColumnDialog = true
      },
      /**
       * @description: 获取列表
       * @return {*}
       */
      async getGeneralPersonalTableDataFun() {
        this.tableConfig.loading = true
        this.showLoading = true
        const { data } = await getGeneralPersonalTableData(this.searchParams)
        const res = data
        if (res && res.rows) {
          res.rows.map((item) => {
            item.dynamicColumnValues = item.dynamicColumnValues ? JSON.parse(item.dynamicColumnValues) : {}
            for (const key in item.dynamicColumnValues) {
              if (key !== 'id') {
                item[key] = item.dynamicColumnValues[key]
              }
            }
            item.dynamicColumnValues['姓名'] = item['姓名'] = item.driverRealName
            item.dynamicColumnValues['手机号码'] = item['手机号码'] = item.driverMobile
            // item.dynamicColumnValues['岗位'] = item['岗位'] = item.positionName;
            // item.dynamicColumnValues['入职日期'] = item['入职日期'] = item.joinedDate ? item.joinedDate : '';
            // item.dynamicColumnValues['排序'] = item['排序'] = item.sortNo === 0 ? '0' : item.sortNo;
            // item.dynamicColumnValues['出生日期'] = item['出生日期'] = item.birthday ? item.birthday : '';
            // item['年龄'] = item.age ? item.age : '';
            // let punchCardStationsNameArr = [];
            // item.punchCardStationsName = '';
            // if (item.punchCardStations.length > 0) {
            //     item.punchCardStations.forEach(_item => {
            //         punchCardStationsNameArr.push(_item.punchCardStationName);
            //     });
            //     item.punchCardStationsName = punchCardStationsNameArr.join(',');
            // }
          })
          this.tableData = res.rows
          this.total = res.total
          this.tableConfig.loading = false
          this.showLoading = false
        } else {
          this.tableData = []
          this.total = 0
          this.tableConfig.loading = false
          this.showLoading = false
        }
      },
      async delRow(row) {
        await deletePersonalData(row.id)
        ElMessage.success('删除成功')
        await this.getGeneralPersonalTableDataFun()
      },
      //创建表头项
      async confirm(formName) {
        await this.$refs[formName].validate(async (valid) => {
          if (valid) {
            //去掉自己，然后和其他项去查重
            const newArr = this.formData.addition.slice(0, -1)
            this.formData.addition.forEach((item, index) => {
              if (!item.columnName) {
                ElMessage.error('名称' + parseInt(index + 1) + '不能为空')
                this.isOk = false
                return false
              } else {
                this.isOk = true
              }
              if (!item.columnType.columnTypeId) {
                ElMessage.error('名称' + parseInt(index + 1) + '类型不能为空')
                this.isOk = false
                return false
              } else {
                this.isOk = true
              }
            })
            //全部校验通过
            if (this.isOk) {
              if (
                newArr.find((v) => {
                  return v.columnName === this.formData.addition[this.formData.addition.length - 1].columnName
                })
              ) {
                ElMessage.error('名称重复，请检查')
                return false
              }
              let params = []
              this.formData.addition.forEach((item) => {
                if (item.columnName === '姓名') {
                  params.push({
                    columnKey: '姓名',
                    columnName: '姓名',
                    columnTypeId: item.columnType.columnTypeId,
                    columnTypeName: item.columnType.columnTypeName,
                    id: item.id,
                  })
                } else if (item.columnName === '手机号码') {
                  params.push({
                    columnKey: '手机号码',
                    columnName: '手机号码',
                    columnTypeId: item.columnType.columnTypeId,
                    columnTypeName: item.columnType.columnTypeName,
                    id: item.id,
                  })
                }
                //  else if (item.columnName === '岗位') {
                //     params.push({
                //         columnKey: '岗位',
                //         columnName: '岗位',
                //         columnTypeId: item.columnType.columnTypeId,
                //         columnTypeName: item.columnType.columnTypeName,
                //         id: item.id
                //     });
                // } else if (item.columnName === '入职日期') {
                //     params.push({
                //         columnKey: '入职日期',
                //         columnName: '入职日期',
                //         columnTypeId: item.columnType.columnTypeId,
                //         columnTypeName: item.columnType.columnTypeName,
                //         id: item.id
                //     });
                // } else if (item.columnName === '排序') {
                //     params.push({
                //         columnKey: '排序',
                //         columnName: '排序',
                //         columnTypeId: item.columnType.columnTypeId,
                //         columnTypeName: item.columnType.columnTypeName,
                //         id: item.id
                //     });
                // } else if (item.columnName === '出生日期') {
                //     params.push({
                //         columnKey: '出生日期',
                //         columnName: '出生日期',
                //         columnTypeId: item.columnType.columnTypeId,
                //         columnTypeName: item.columnType.columnTypeName,
                //         id: item.id
                //     });
                // }
                else {
                  params.push({
                    columnKey: item.columnKey,
                    columnName: item.columnName,
                    columnTypeId: item.columnType.columnTypeId,
                    columnTypeName: item.columnType.columnTypeName,
                    id: item.id,
                  })
                }
              })
              batchSaveDynamicColumn(params).then((res) => {
                ElMessage.success('创建成功')
                this.close(formName)
                this.getDynamicColumnFun()
                this.tableConfig.tableItem = []
                this.getGeneralPersonalTableDataFun()
              })
            }
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      //关闭弹窗
      close(formName) {
        this.$refs[formName].resetFields()
        this.showDynamicColumnDialog = false
        this.showAddPersonalDataDialog = false
        // 处理自定义表头 新增编辑但是没有保存的数据 -- 清除
        if (formName == 'ruleForm') {
          this.formData.addition = this.formData.addition.filter((item) => item.id)
        }
      },
      //获取创建的工资项
      async getDynamicColumnFun() {
        // eslint-disable-next-line no-async-promise-executor
        return new Promise(async (resolve) => {
          this.formData.addition = []
          const { data } = await getDynamicColumn() //增项
          this.dynamicAddition = data

          //初始化
          this.personalData = {
            driverRealName: '',
            // driverName: '',
            // positionName: '',
            driverMobile: '',
            // joinedDate: '',
            // sortNo: '',
            // birthday: '',
            // punchCardStations: [],
            dynamicColumnValues: {},
          }
          // this.tableConfig.tableItem = [];
          // 默认显示
          this.tableConfig.tableItem = [
            {
              name: '姓名',
              label: '姓名',
              width: '120px',
              fixedLeft: 'left',
            },
            {
              name: '手机号码',
              label: '手机号码',
              width: '120px',
              fixedLeft: 'left',
            },
            // {
            //     name: '岗位',
            //     label: '岗位',
            //     width: '120px',
            //     fixedLeft: 'left'
            // },
            // {
            //     name: '出生日期',
            //     label: '出生日期',
            //     width: '120px'
            // },
            // {
            //     name: '年龄',
            //     label: '年龄',
            //     width: '90px'
            // },
            // {
            //     name: '入职日期',
            //     label: '入职日期',
            //     width: '120px'
            // },
            // {
            //     name: '排序',
            //     label: '排序',
            //     width: '90px'
            // }
          ]
          if (this.dynamicAddition.length > 0) {
            let dynamicColumnValuesKeys = {}
            this.dynamicAddition.forEach((item) => {
              if (
                item.columnName !== '姓名' &&
                item.columnName !== '手机号码'
                //  &&
                // item.columnName !== '岗位' &&
                // item.columnName !== '入职日期' &&
                // item.columnName !== '排序' &&
                // item.columnName !== '出生日期'
              ) {
                this.tableConfig.tableItem.push({
                  name: item.columnKey,
                  label: item.columnName,
                  width: '120px',
                })
              }

              this.formData.addition.push({
                columnKey: item.columnKey,
                columnName: item.columnName,
                columnType: {
                  columnTypeId: item.columnTypeId,
                  columnTypeName: item.columnTypeName,
                },
                id: item.id,
              })
              dynamicColumnValuesKeys[item.columnKey] = ''
            })
            this.personalData.dynamicColumnValues = { ...dynamicColumnValuesKeys }
          } else {
            this.formData.addition = [
              {
                columnKey: '姓名',
                columnName: '姓名',
                columnType: {
                  columnTypeId: '9',
                  columnTypeName: '其他',
                },
              },
              {
                columnKey: '手机号码',
                columnName: '手机号码',
                columnType: {
                  columnTypeId: '9',
                  columnTypeName: '其他',
                },
              },
              // {
              //     columnKey: '岗位',
              //     columnName: '岗位',
              //     columnType: {
              //         columnTypeId: '9',
              //         columnTypeName: '其他'
              //     }
              // },
              // {
              //     columnKey: '入职日期',
              //     columnName: '入职日期',
              //     columnType: {
              //         columnTypeId: '9',
              //         columnTypeName: '其他'
              //     }
              // },
              // {
              //     columnKey: '排序',
              //     columnName: '排序',
              //     columnType: {
              //         columnTypeId: '9',
              //         columnTypeName: '其他'
              //     }
              // },
              // {
              //     columnKey: '出生日期',
              //     columnName: '出生日期',
              //     columnType: {
              //         columnTypeId: '9',
              //         columnTypeName: '其他'
              //     }
              // }
            ]
            this.tableConfig.tableItem = [
              {
                name: '姓名',
                label: '姓名',
                width: '120px',
                fixedLeft: 'left',
              },
              {
                name: '手机号码',
                label: '手机号码',
                width: '120px',
                fixedLeft: 'left',
              },
              // {
              //     name: '岗位',
              //     label: '岗位',
              //     width: '120px',
              //     fixedLeft: 'left'
              // },
              // {
              //     name: '出生日期',
              //     label: '出生日期',
              //     width: '120px'
              // },
              // {
              //     name: '年龄',
              //     label: '年龄',
              //     width: '90px'
              // },
              // {
              //     name: '入职日期',
              //     label: '入职日期',
              //     width: '120px'
              // },
              // {
              //     name: '排序',
              //     label: '排序',
              //     width: '90px'
              // }
            ]
          }
          resolve()
        })
      },
      //新增人员
      async confirmPersonalData(formName) {
        await this.$refs[formName].validate(async (valid) => {
          if (valid) {
            let params = JSON.parse(JSON.stringify(this.personalData)) //深拷贝
            for (let key in params.dynamicColumnValues) {
              if (key === '姓名') {
                params.driverRealName = params.dynamicColumnValues[key]
              }
              if (key === '手机号码') {
                params.driverMobile = params.dynamicColumnValues[key]
              }
              // if (key === '岗位') {
              //     params.positionName = params.dynamicColumnValues[key];
              // }
              // if (key === '入职日期') {
              //     params.joinedDate = params.dynamicColumnValues[key];
              // }
              // if (key === '排序') {
              //     params.sortNo = params.dynamicColumnValues[key];
              // }
              // if (key === '出生日期') {
              //     params.birthday = params.dynamicColumnValues[key];
              // }
            }
            // if (params.dynamicColumnValues.hasOwnProperty('birthday')) {
            //   delete params.dynamicColumnValues['birthday'];
            // }
            params.dynamicColumnValues = JSON.stringify(params.dynamicColumnValues)
            //id存在 修改数据
            if (params.id) {
              await updatePersonalData(params, params.id)
              ElMessage.success('更新成功')
            } else {
              await addPersonalData(params)
              ElMessage.success('创建成功')
            }

            await this.close(formName)
            await this.getGeneralPersonalTableDataFun()
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      //显示新增人员弹窗
      addPersonalData() {
        this.type = 'add'
        this.getDynamicColumnFun()
        this.showAddPersonalDataDialog = true
      },
      //修改人员信息
      showEditDialogVisible(row) {
        //获取所有的表头项
        let tableItem = {}
        delete this.tableConfig.tableItem['id']
        // delete this.tableConfig.tableItem['年龄'];
        this.tableConfig.tableItem.forEach((item) => {
          tableItem[item.name] = ''
        })
        this.personalData = {}
        this.type = 'edit'
        this.personalData = {
          id: row.id,
          driverRealName: row.driverRealName,
          // positionName: row.positionName,
          // driverMobile: row.driverMobile,
          // joinedDate: row.joinedDate,
          // sortNo: row.sortNo,
          // birthday: row.birthday,
          // punchCardStations: row.punchCardStations,
          dynamicColumnValues: Object.assign({}, tableItem, row.dynamicColumnValues), //合并新表头项和人员之前的表头项，防止之前的人员没有新增的表头项
        }
        this.showAddPersonalDataDialog = true
      },
      // 获取分页数据
      pagination(val) {
        this.searchParams.page = val.page
        this.searchParams.limit = val.limit
        this.getGeneralPersonalTableDataFun()
      },
      handleImport(data) {
        this.importLoading = data
      },
      // 导入
      // importExcelFile(formData) {
      //   importPersonalExcel(formData)
      //     .then(() => {
      //       this.importLoading = false;
      //       this.$notify({
      //         title: '提示',
      //         message: '导入成功',
      //         type: 'success'
      //       });
      //       // 获取人员列表
      //       this.getGeneralPersonalTableDataFun();
      //     })
      //     .catch(error => {
      //       this.importLoading = false;
      //     });
      // },
      importExcelFile(file) {
        this.importLoading = true
        importPersonalExcel(file)
          .then(async (res) => {
            ElMessage.success('导入成功')
            this.importLoading = false
            // 获取人员列表
            this.getGeneralPersonalTableDataFun()
          })
          .catch(() => {
            this.importLoading = false
          })
      },
      /**
       * 导出Excel
       */
      exportTable(position, type, item) {
        exportPersonalExcel(this.searchParams)
          .then(async (res) => {
            this.btnLoading = true
            await downloadFileGlobalFun(res)
            this.btnLoading = false
            ElMessage.success('操作成功')
          })
          .catch((error) => {
            this.btnLoading = false
          })
      },
      //查询
      search() {
        this.searchParams.page = 1
        // 获取人员列表
        this.getGeneralPersonalTableDataFun()
      },
      //重置
      reset() {
        this.searchParams.page = 1
        this.searchParams.id = ''
        // 获取人员列表
        this.getGeneralPersonalTableDataFun()
      },
      /**
       * @description: 选中日期
       * @param {*} e
       * @return {*}
       */
      // calendarOnClick(e) {
      //     const index = this.selectedDates.indexOf(e.day)
      //     // 判断数组中是否存在当前选中日期；存在就删除，没有则为添加
      //     if (index > -1) {
      //         this.selectedDates.splice(index, 1)
      //     } else {
      //         this.selectedDates.push(e.day)
      //     }
      // },
      /**
       * @description: 是否选中日期
       * @param {*} slotDate
       * @param {*} slotData
       * @return {*}
       */
      isSelected: function (slotDate, slotData) {
        const meetCondition = this.selectedDates.some((obj) => {
          return obj.date === slotData.day
        })
        return meetCondition
        // return this.selectedDates.includes(slotData.day)
      },
      isHalfDay: function (slotDate, slotData) {
        const meetHalfDay = this.selectedDates.some((obj) => {
          return obj.date === slotData.day && obj.banciType == 0
        })
        return meetHalfDay
      },
      isAllDay: function (slotDate, slotData) {
        const meetAllDay = this.selectedDates.some((obj) => {
          return obj.date === slotData.day && obj.banciType == 1
        })
        return meetAllDay
      },
      /**
       * @description: 是否节假日
       * @param {*} slotDate
       * @param {*} slotData
       * @return {*}
       */
      isFestival(slotDate, slotData) {
        const solarDayArr = slotData.day.split('-')
        const lunarDay = calendar.solar2lunar(solarDayArr[0], solarDayArr[1], solarDayArr[2])

        // 公历节日\农历节日\农历节气
        let festAndTerm = []
        festAndTerm.push(lunarDay.festival == null ? '' : ' ' + lunarDay.festival)
        festAndTerm.push(lunarDay.lunarFestival == null ? '' : '' + lunarDay.lunarFestival)
        festAndTerm.push(lunarDay.Term == null ? '' : '' + lunarDay.Term)
        festAndTerm = festAndTerm.join('')

        return festAndTerm !== ''
      },
      /**
       * @description: 公历转农历
       * @param {*} slotDate
       * @param {*} slotData
       * @return {*}
       */
      solarToLunar(slotDate, slotData) {
        const solarDayArr = slotData.day.split('-')
        const lunarDay = calendar.solar2lunar(solarDayArr[0], solarDayArr[1], solarDayArr[2])

        // 农历日期
        const lunarMD = lunarDay.IMonthCn + lunarDay.IDayCn

        // 公历节日\农历节日\农历节气
        let festAndTerm = []
        festAndTerm.push(lunarDay.festival == null ? '' : ' ' + lunarDay.festival)
        festAndTerm.push(lunarDay.lunarFestival == null ? '' : '' + lunarDay.lunarFestival)
        festAndTerm.push(lunarDay.Term == null ? '' : '' + lunarDay.Term)
        festAndTerm = festAndTerm.join('')

        return festAndTerm === '' ? lunarMD : festAndTerm
      },
      /**
       * @description: 显示日历
       * @param {*} val
       * @return {*}
       */
      // showCalendarDialog(val) {
      //     this.showCalendar = true
      //     this.setSalendar = val
      //     let params = {}
      //     // 如果点击的是工作日历设定按钮true  ，根据templateId查询日历，否则获取公司日历
      //     if (val) {
      //         params = { templateId: this.templateId }
      //     }
      //     this.getWorkingCalendar(params)
      //     // this.getWorkingTime(params)
      //     this.$nextTick(() => {
      //         // 点击前一个月
      //         const prevBtn = document.querySelector(
      //             '.el-calendar__button-group .el-button-group>button:nth-child(1)'
      //         )
      //         // 点击下一个月
      //         const nextBtn = document.querySelector(
      //             '.el-calendar__button-group .el-button-group>button:nth-child(3)'
      //         )
      //         // 点击今天
      //         const todayBtn = document.querySelector(
      //             '.el-calendar__button-group .el-button-group>button:nth-child(2)'
      //         )
      //         var date = new Date()
      //         var month = date.getMonth() + 1
      //         if (month <= 1) {
      //             prevBtn.style.display = 'none'
      //         } else if (month >= 12) {
      //             nextBtn.style.display = 'none'
      //         } else {
      //             prevBtn.style.display = 'block'
      //             nextBtn.style.display = 'block'
      //         }
      //         // 点击前一个月
      //         prevBtn.addEventListener('click', (e) => {
      //             if (this.value.getMonth() + 1 <= 1) {
      //                 prevBtn.style.display = 'none'
      //                 nextBtn.style.display = 'block'
      //             } else {
      //                 prevBtn.style.display = 'block'
      //                 nextBtn.style.display = 'block'
      //             }
      //         })
      //         // 点击下一个月
      //         nextBtn.addEventListener('click', (e) => {
      //             if (this.value.getMonth() + 1 >= 12) {
      //                 nextBtn.style.display = 'none'
      //                 prevBtn.style.display = 'block'
      //             } else {
      //                 nextBtn.style.display = 'block'
      //                 prevBtn.style.display = 'block'
      //             }
      //         })
      //         // 点击今天
      //         todayBtn.addEventListener('click', () => {
      //             if (this.value.getMonth() + 1 <= 1) {
      //                 prevBtn.style.display = 'none'
      //                 nextBtn.style.display = 'block'
      //             } else if (this.value.getMonth() + 1 >= 12) {
      //                 nextBtn.style.display = 'none'
      //                 prevBtn.style.display = 'block'
      //             } else {
      //                 nextBtn.style.display = 'block'
      //                 prevBtn.style.display = 'block'
      //             }
      //         })
      //     })
      // },
      /**
       * @description: 设置企业工作日历
       * @return {*}
       */
      // setWorkingCalendarForm() {
      //     this.disabled = true
      //     let setWorkingCalendarParams = {}
      //     // let setWorkingTimeParams = {}
      //     if (this.setSalendar) {
      //         setWorkingCalendarParams = {
      //             dates: this.selectedDates
      //         }
      //     } else {
      //         setWorkingCalendarParams = {
      //             dates: this.selectedDates
      //         }
      //     }
      //     setWorkCalendar(setWorkingCalendarParams).then((response) => {
      //         this.showCalendar = false
      //         this.disabled = false
      //         if (!this.setSalendar) {
      //             this.exportRunningTemplete()
      //         }
      //         this.$message({
      //             type: 'success',
      //             message: '设置成功'
      //         })
      //     })
      // },
      /**
       * @description: 取消
       * @param {*} formName
       * @return {*}
       */
      cancel() {
        this.showCalendar = false
        this.selectedDates = []
      },
      /**
       * @description: 获取企业工作日历
       * @return {*}
       */
      async getWorkingCalendar() {
        const date = new Date()
        const year = date.getFullYear()
        this.selectedDates = []
        const res = await showWorkCalendar({ year })
        this.selectedDates = res.workDays
      },
      /**
       * 获取人员绑定信息
       * @returns {Promise<void>}
       */
      async getBindingInfoByDriverId(driverId) {
        const date = new Date()
        this.bindingParams.year = date.getFullYear().toString()
        this.bindingParams.driverId = driverId
        this.showBindingInfo = true

        // this.getWorkingTime(params)
        this.$nextTick(() => {
          // 点击前一个月
          const prevBtn = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(1)')
          // 点击下一个月
          const nextBtn = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(3)')
          // 点击今天
          const todayBtn = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(2)')
          // 点击前一个月
          prevBtn.addEventListener('click', (e) => {
            const currentDate = document.querySelector('.el-calendar__title').innerText
            this.bindingParams.year = currentDate.slice(0, 4)
          })
          // 点击下一个月
          nextBtn.addEventListener('click', (e) => {
            const currentDate = document.querySelector('.el-calendar__title').innerText
            this.bindingParams.year = currentDate.slice(0, 4)
          })
          // 点击今天
          todayBtn.addEventListener('click', () => {})
        })
      },
      handleColumnTypeChange(e, item, index) {
        const tableHeaderType = this.tableHeaderType.find((item) => item.value === e)
        this.formData.addition[index].columnType = {
          columnTypeId: tableHeaderType.value,
          columnTypeName: tableHeaderType.text,
        }
      },
    },
  }
</script>

<style scoped>
  .is-selected {
    color: #1989fa;
  }

  /**隐藏上一月、本月、下一月*/
  /* .el-calendar__button-group {
  display: none;
} */
  /**月份居中*/
  .el-calendar__title {
    /* width: 100%; */
    text-align: center;
  }

  /**日期div的样式*/
  .calendar :deep(.el-calendar-table tr td:first-child) {
    border-left: 0px;
  }

  .calendar :deep(.el-calendar-table td) {
    min-height: 110px;
    min-width: 110px;
    /* border-right: 0px; */
  }

  .calendar :deep(.el-calendar-table td.is-selected) {
    background-color: white;
  }

  .calendar :deep(.el-calendar-table .el-calendar-day) {
    height: 100%;
    padding: 0px;
    text-align: center;
  }

  .calendar :deep(.el-calendar-table .el-calendar-day > div) {
    height: 50px;
  }

  /**日期div的样式-公历*/
  .calendar :deep(.el-calendar-table .el-calendar-day > div .solar) {
    padding-top: 5px;
  }

  /**日期div的样式-农历*/
  .calendar :deep(.el-calendar-table .el-calendar-day > div .lunar) {
    padding-top: 5px;
  }

  /**日期div的样式-选中*/
  .calendar :deep(.el-calendar-table .el-calendar-day > div.selected) {
    background-color: #fef2f2;
    /* border: 3px solid #fb0; */
    /* border-radius: 20px; */
  }

  /**本月周末设置为红色*/
  .calendar :deep(.el-calendar-table .current:nth-last-child(-n + 2) .solar) {
    color: red;
  }

  /**本月农历设置为灰色*/
  .calendar :deep(.el-calendar-table .current .lunar) {
    color: #606266;
  }

  /**本月农历节日设置为红色*/
  .calendar :deep(.el-calendar-table .current .lunar.festival) {
    color: red;
  }

  .ban {
    position: absolute;
    /* right: 5px; */
    left: 5px;
    top: 5px;
    border-radius: 50%;
    border: 2px solid green;
    color: #fff;
    background: green;
    font-size: 12px;
    width: 20px;
    height: 20px;
    line-height: 16px;
  }

  /**禁用点击效果*/
  /*.el-calendar-table td {*/
  /*pointer-events: none;*/
  /*}*/
  .calendar :deep(.el-dialog__body) {
    padding-bottom: 0;
    padding-top: 10px;
  }

  .calendar :deep(.el-calendar__body) {
    padding: 12px 20px 10px;
  }

  .calendar :deep(.el-form-item--small.el-form-item) {
    margin-bottom: 0;
  }

  .editbtn {
    margin-left: 20px;
  }

  .personnelClass :deep(.el-dialog__body) {
    max-height: 70vh;
    /* firefox IE 10+*/
    /* scrollbar-width: none;
    -ms-overflow-style: none;
    overflow-x: hidden; */
    overflow-y: scroll;
  }
  .personnelClass :deep(.el-dialog__footer) {
    padding: 15px 20px;
  }
</style>
