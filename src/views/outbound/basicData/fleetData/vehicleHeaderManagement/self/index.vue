<!--
 * @Author: llm
 * @Date: 2025-02-17 13:42:29
 * @LastEditors: llm
 * @LastEditTime: 2025-02-17 13:43:43
 * @Description: 
-->
<template>
  <GlobalPage :pageType="state.pageType" />
</template>
<script lang="ts" setup>
  import { getCurrentInstance, onBeforeMount, reactive } from 'vue'
  import GlobalPage from '@/components/GlobalPage/index.vue'
  defineOptions({
    name: 'OutboundBasicDataFleetDataVehicleHeaderManagementSelf',
    inheritAttrs: false,
  })
  const { proxy }: any = getCurrentInstance()
  const state = reactive({
    pageType: '',
    pageName: '',
  })
  onBeforeMount(() => {
    state.pageType = proxy.$sideBarStore.$state.pageType
  })
</script>
