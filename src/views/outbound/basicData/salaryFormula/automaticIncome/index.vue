<!--
 * @Author: llm
 * @Date: 2024-11-26 18:18:40
 * @LastEditors: llm
 * @LastEditTime: 2024-12-26 10:47:30
 * @Description: 自动收入
-->

<template>
  <!-- 自动工资计算 -->
  <div class="components-container">
    <div>
      <el-tag style="margin-bottom: 10px" type="warning">自动工资计算由“人员设定”中的表头项类型为—"自动计算项"组成。</el-tag>
      <!-- 表格 -->
      <el-table :data="tableData" :loading="tableLoading" border fit highlight-current-row size="default" style="width: 100%">
        <el-table-column align="center" label="序号" type="index" width="60"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" align="center" label="自动计算项" prop="subsidyItem"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" align="center" label="计算公式" prop="subsidyFormulaStr"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" align="center" label="创建时间" prop="createTime"></el-table-column>
        <el-table-column :show-overflow-tooltip="true" align="center" label="操作">
          <template #default="scope">
            <el-button size="small" type="text" @click="editItem(scope.row)">编辑</el-button>
            <el-button v-if="scope.row.id" size="small" type="text" @click="deteleRow(scope.row)">重置计算项</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog draggable :before-close="handleClose" :close-on-click-modal="false" v-model="showDialog" title="编辑自动计算项" width="960px">
      <el-form ref="formData" :inline="true" :model="formData">
        <div style="max-height: 60vh; overflow-y: scroll">
          <div style="margin-bottom: 20px; display: flex; align-items: center; justify-content: space-between">
            <div class="title">{{ formData.columnName }}</div>
            <div class="imitationInput">{{ inputValue }}</div>
            <el-button style="margin-top: 10px" type="primary" @click="refresh">清空</el-button>
          </div>
          <el-row justify="space-between" type="flex">
            <el-col :span="14">
              <el-card>
                <h4 style="padding: 0px 4px 10px">参与计算项</h4>
                <el-row :gutter="20" style="max-height: 400px; overflow: scroll">
                  <template v-for="(item, index) in columnsList" :key="index">
                    <el-col :span="6" v-if="formData.columnName != item.columnName">
                      <el-tooltip class="item" effect="dark" :content="item.columnName" placement="top">
                        <el-tag class="tagClass" effect="plain" size="large" @click="clickTag(item, 'tag')">{{ item.columnName }}</el-tag>
                      </el-tooltip>
                    </el-col>
                  </template>
                </el-row>
              </el-card>
            </el-col>
            <el-col :span="9">
              <el-card>
                <h4 style="padding: 0px 4px 10px">参与运算项</h4>
                <el-tag
                  v-for="(item, index) in operationList"
                  :key="index"
                  class="poerationClass"
                  effect="plain"
                  size="large"
                  @click="clickTag(item, 'poeration')"
                  >{{ item.name }}</el-tag
                >
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <template #footer>
        <div class="submit-btn">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="confirmForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { operationList } from '@/utils/operation'
  import { saveAutoCalculateItemApi, autoCalculateBaseInfoApi, deleteItemApi } from '@/api/salaryFormulaApi/index'
  export default {
    name: 'OutboundBasicDataSalaryFormulaAutomaticIncome',
    data() {
      return {
        showDialog: false,
        tableLoading: false,
        formData: {
          id: '',
          dynamicColumnId: '',
          dynamicColumnTypeId: '',
          subsidyFormula: '',
          columnName: '',
          subsidyType: 100, //后端定义默认
          subsidyTypeName: '按自动计算公式', //后端定义默认
        },
        tableData: [],
        columnsList: [], //所有可选择项
        operationList: [], //所有运算项
        inputValue: '',
        inputNameList: [], //中文展示列表
        newArr: [], //未拼接ID的数组
        newSubsidyFormula: [],
      }
    },
    mounted() {
      this.getSalaryData()
      this.operationList = operationList()
    },
    methods: {
      // 清空选择
      refresh() {
        this.inputValue = ''
        this.inputNameList = [] //中文展示列表
        this.newArr = []
        this.newSubsidyFormula = []
      },
      // 选择计算项或者运算项
      clickTag(itemData, type) {
        if (type === 'tag') {
          this.inputValue += itemData.columnName
          this.newArr.push(itemData.id)
          this.inputNameList.push(itemData.columnName)
          this.newSubsidyFormula.push('ID' + itemData.id + 'ID')
        } else {
          if (itemData.value !== 'delete') {
            this.inputValue += itemData.name
            this.newArr.push(itemData.value)
            this.inputNameList.push(itemData.name)
            this.newSubsidyFormula.push(itemData.value)
          } else {
            // 删除最后一项
            if (this.inputValue) {
              if (this.newArr.length > 0) {
                this.newArr.pop()
              }
              this.inputNameList = JSON.parse(JSON.stringify(this.newArr))
              for (let i = 0; i < this.inputNameList.length; i++) {
                for (let j = 0; j < this.columnsList.length; j++) {
                  if (this.inputNameList[i] === this.columnsList[j].id) {
                    this.inputNameList[i] = this.columnsList[j].columnName
                  }
                }
              }
              this.inputValue = this.inputNameList.join('')
              if (this.newSubsidyFormula.length > 0) {
                this.newSubsidyFormula.pop()
              }
            }
          }
        }
      },
      // 获取列表数据
      getSalaryData() {
        autoCalculateBaseInfoApi({})
          .then((response) => {
            if (response.data.columns && response.data.columns.length > 0) {
              this.columnsList = response.data.columns
            } else {
              this.columnsList = []
            }
            if (response.data.autoCalculateItems && response.data.autoCalculateItems.length > 0) {
              this.tableData = response.data.autoCalculateItems
            } else {
              this.tableData = [] //清空列表
            }
          })
          .catch((error) => {})
      },
      // 编辑计算项
      editItem(itemData) {
        this.inputNameList = []
        this.newArr = []
        this.newSubsidyFormula = []
        if (itemData.subsidyFormula) {
          let subsidyList = itemData.subsidyFormula.split('ID')
          //过滤掉空值项
          subsidyList = subsidyList.filter(function (s) {
            return s && s.trim()
          })
          const reg = /^\d+$/
          if (subsidyList.length > 0) {
            const newArr = []
            subsidyList.forEach((item) => {
              if (reg.test(item)) {
                newArr.push(item)
              } else {
                for (let i = 0; i < item.length; i++) {
                  newArr.push(item[i])
                }
              }
            })
            this.newArr = JSON.parse(JSON.stringify(newArr))
            for (let i = 0; i < newArr.length; i++) {
              for (let j = 0; j < this.columnsList.length; j++) {
                if (newArr[i] === this.columnsList[j].id) {
                  newArr[i] = 'ID' + newArr[i] + 'ID'
                }
              }
            }
            this.newSubsidyFormula = newArr
          }
        }
        this.formData.id = itemData.id
        this.inputValue = itemData.subsidyFormulaStr ? itemData.subsidyFormulaStr : ''
        this.formData.columnName = itemData.subsidyItem
        this.formData.dynamicColumnId = itemData.dynamicColumnId
        this.formData.dynamicColumnTypeId = itemData.dynamicColumnTypeId
        setTimeout(() => {
          this.showDialog = true
        }, 100)
      },
      // 关闭弹窗
      handleClose() {
        this.showDialog = false
      },
      // 取消弹窗
      cancel() {
        this.showDialog = false
      },
      // 确定修改
      confirmForm() {
        let newItemIds
        if (this.newSubsidyFormula.length === 0) {
          ElMessage.error('请选择自动计算方法')
          return
        } else {
          newItemIds = ''
          if (this.newSubsidyFormula.length > 0) {
            newItemIds = this.newSubsidyFormula.join('')
          } else {
            newItemIds = ''
          }
        }
        let params = {
          id: this.formData.id,
          dynamicColumnId: this.formData.dynamicColumnId,
          dynamicColumnTypeId: this.formData.dynamicColumnTypeId,
          subsidyFormula: newItemIds,
          subsidyItem: this.formData.columnName,
          subsidyType: 100, //后端定义默认
          subsidyTypeName: '按自动计算公式', //后端定义默认
        }
        // return
        saveAutoCalculateItemApi(params)
          .then(() => {
            this.showDialog = false
            ElMessage.success('修改成功')
            this.getSalaryData()
          })
          .catch(() => {})
      },

      // 删除创建工资项
      deteleRow(data) {
        let params = {
          id: data.id,
        }
        ElMessageBox.confirm('确定重置当前自动计算项？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          deleteItemApi(params)
            .then((response) => {
              ElMessage.success('重置成功')
              this.getSalaryData()
            })
            .catch((error) => {
              console.log('error')
            })
        })
      },
    },
  }
</script>

<style scoped>
  .components-container {
    margin: 20px;
    position: relative;
    background: #fff;
    padding: 20px;
    border-radius: 5px;
  }

  .flex {
    height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: #666;
  }

  .formulaItem > .el-select__tags {
    max-width: 235px !important;
  }

  .title {
    margin-right: 20px;
  }

  .imitationInput {
    flex: 1;
    min-height: 80px;
    box-sizing: border-box;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px 15px;
    margin-right: 20px;
    /*overflow: hidden;*/
    /*text-overflow: ellipsis;*/
    /*white-space: nowrap;*/
    &:before {
      content: '';
    }

    &:after {
      content: '';
      border-right: 2px solid #606266;
      height: 50%;
      opacity: 1;
      animation: focus 0.8s forwards infinite;
    }
  }

  @keyframes focus {
    from {
      opacity: 1;
    }

    to {
      opacity: 0;
    }
  }

  .tagClass {
    margin: 6px 6px 6px 0px;
    cursor: pointer;
    width: 100px;
  }

  .tagClass :deep(.el-tag__content) {
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .tagClass:hover,
  .poerationClass:hover {
    background: #0066ff;
    color: #fff;
  }

  .poerationClass {
    margin: 6px 6px 6px 0px;
    cursor: pointer;
    width: 42px;
    height: 32px;
    line-height: 30px;
    text-align: center;
  }
</style>
