<!--
 * @Author: llm
 * @Date: 2024-11-26 18:19:57
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-05-23 11:19:02
 * @Description: 变动收入
-->
<template>
  <!-- 变动收入计算 -->
  <div>
    <div class="components-container">
      <div ref="topHeight">
        <div class="flex" style="align-items: center; padding-bottom: 20px">
          <el-button :loading="exportLoading" size="default" type="primary" @click.stop="handleExport()">导出</el-button>
          <!-- <el-tag style="margin-left: 10px" type="warning"
            >变动收入计算由“人员设定”中的表头项类型为—"补贴收入"，"变动扣除"，"数据统计项"，"数据统计定义项"组成。</el-tag
          > -->
          <el-tag style="margin-left: 10px" type="warning">变动收入计算由“人员设定”中的表头项类型为—"补贴收入"，"变动扣除"组成。</el-tag>
        </div>
        <el-table :data="tableData" :loading="tableLoading" border fit highlight-current-row size="default" style="width: 100%">
          <el-table-column align="center" label="序号" type="index" width="60"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" label="变动收入项名称" prop="subsidyItem"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" label="变动收入项类型" prop="dynamicColumnTypeName"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" label="变动收入补贴项" prop="level2SubsidyNames"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" label="创建时间" prop="createTime"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" label="操作">
            <template #default="scope">
              <el-button v-if="scope.row.dynamicColumnTypeId != 2" size="small" type="text" @click="editItem(scope.row)">编辑</el-button>
              <el-button v-if="scope.row.dynamicColumnTypeId == 2" size="small" type="text" @click="setRelationship(scope.row)">计算关系</el-button>
              <el-button v-if="scope.row.id" size="small" type="text" @click="deteleRow(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 变动收入 弹窗 -->
    <el-dialog draggable :before-close="handleClose" :close-on-click-modal="false" title="编辑变动收入" v-model="showDialog" width="940px">
      <el-form ref="formData" :inline="true" :model="formData" :rules="rules">
        <el-row>
          <el-col :span="24">
            <el-form-item label="名称：" prop="subsidyItem">
              <el-input v-model="formData.subsidyItem" clearable placeholder="请输入变动收入名称"></el-input>
            </el-form-item>
            <el-form-item label="计算类型：" prop="subsidyType">
              <el-select v-model="formData.subsidyType" placeholder="请选择计算类型" clearable @change="changeFormula" style="width: 240px">
                <el-option v-for="item in editSubsidyTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="subsidyNameList.length" :span="24">
            <el-card>
              <el-row>
                <el-col :span="24">
                  <div style="padding: 6px 0">
                    <span>参与计算的编号与单位:</span>
                    <!-- 补贴项 -->
                    <span v-for="item in factorsTable" :key="item.id">
                      <el-tag
                        v-if="formData.dynamicColumnTypeId == 10 ? !filterList10.includes(item.code) : !filterList.includes(item.code)"
                        style="margin: 0px 5px 5px"
                      >
                        {{ item.code + ' - ' + item.name + '/' + item.unit }}
                      </el-tag>
                    </span>
                  </div>
                </el-col>
              </el-row>
              <el-tag style="margin-bottom: 5px" type="warning">请用上方对应的单位编号创建公式</el-tag>
              <el-tag style="margin-bottom: 5px; margin-left: 10px" type="success" size="large"
                >"&&"： 并且，"||"：或者， "=="： 等于, ">="：大于等于， "<="：小于等于</el-tag
              >
              <div v-for="(item, index) in subsidyNameList" :key="index">
                <div v-if="formData.subsidyType == 4" class="addInputValue">
                  <span>计算公式{{ index + 1 }}：</span>
                  <el-input v-model="item.stationValue" placeholder="请输入计算公式" style="width: 230px; margin-right: 10px"></el-input>
                  <el-button
                    v-if="index === subsidyNameList.length - 1 && formData.subsidyType != 0 && formData.subsidyType != 2"
                    type="text"
                    @click="addItem()"
                    >添加</el-button
                  >
                  <el-button v-if="subsidyNameList.length > 1" style="color: red" type="text" @click="delColumn(index)">删除</el-button>
                </div>
                <div v-else class="addInputValue">
                  <span>计算公式{{ index + 1 }}：</span>
                  <el-input
                    v-if="formData.subsidyType == 3"
                    v-model="item.subsidyFormula"
                    placeholder="请输入计算条件"
                    clearable
                    style="width: 230px; margin-right: 10px"
                  ></el-input>
                  <el-input v-model="item.stationValue" placeholder="请输入计算公式(数值)" clearable style="width: 230px; margin-right: 10px"></el-input>
                  <span v-if="formData.dynamicColumnTypeId == 11">对应数据项编号：</span>
                  <el-input
                    v-if="formData.dynamicColumnTypeId == 11"
                    v-model="formData.dataCalculateCode"
                    placeholder="请输入对应数据项编号"
                    clearable
                    style="width: 230px; margin-right: 10px"
                  ></el-input>

                  <el-button
                    v-if="index === subsidyNameList.length - 1 && formData.subsidyType != 0 && formData.subsidyType != 2 && formData.dynamicColumnTypeId != 11"
                    type="text"
                    @click="addItem()"
                  >
                    添加
                  </el-button>
                  <el-button v-if="subsidyNameList.length > 1" style="color: red" type="text" @click="delColumn(index)">删除</el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
      <!-- 按钮 -->
      <template #footer>
        <div class="submit-btn">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" :loading="btnLoading" @click="confirmForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 设置关系 -->
    <el-dialog draggable :before-close="handleCloseGroup" :close-on-click-modal="false" v-model="showGroupDialog" title="设置关系" width="840px">
      <el-form ref="formGroupData" :inline="true" :model="formGroupData" label-width="140px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="变动收入项名称：" prop="dynamicColumnId">
              <span style="color: #333; font-weight: bold">{{ dynamicColumnName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="补贴项：">
              <el-select
                v-model="formGroupData.itemId"
                collapse-tags
                multiple
                collapse-tags-tooltip
                placeholder="请选择补贴项"
                @change="changeGroup"
                style="width: 240px"
              >
                <el-option v-for="item in level2List" :key="item.id" :label="item.subsidyItem" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- 按钮 -->
      <template #footer>
        <div class="submit-btn">
          <el-button @click="cancelGroup">取消</el-button>
          <el-button type="primary" @click="confirmGroup">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import {
    queryChangeItemsDataApi,
    updateChangeItemDataApi,
    getLevel2SubsidyItemsApi,
    bindSubsidyItemRelationApi,
    deleteItemApi,
    baseInfoApi,
    exportChangeItemApi,
  } from '@/api/salaryFormulaApi/index'
  import { downloadFileGlobalFun } from '@/utils/common'
  export default {
    name: 'OutboundBasicDataSalaryFormulaChangeIncome',
    data() {
      return {
        exportLoading: false,
        showDialog: false, // 补贴项弹窗
        showGroupDialog: false, //关系绑定弹窗
        formData: {
          id: '',
          subsidyItem: '', // 项名称
          subsidyFormula: '', //公式
          subsidyType: '', // 公式类型id
          subsidyTypeName: '', //公式类型名称
          dynamicColumnId: '', // 项名称id
          dynamicColumnTypeId: '', //项名称typeid
          dataCalculateCode: '', //对应数据项编号
        },
        // 公式类型选择
        subsidyNameList: [
          {
            subsidyFormula: '',
            stationValue: '',
            stationId: [],
            relationship: '^&!',
          },
        ],
        formGroupData: {
          itemId: [], // 补贴项id
          itemName: [], // 补贴项名称
        },
        factorsTable: [], //示例
        tableData: [],
        subsidyItemList: [], //项列表
        subsidyTypeList: [], //公式列表
        changeSubsidyNameList: [], //变动收入项公式类型列表
        tableLoading: false,
        btnLoading: false,
        rules: {
          // dynamicColumnId: [
          //   { required: true, message: '请选择变动收入项', trigger: 'change' }
          // ],
          // subsidyFormula: [
          //   { required: true, message: '请输入公式', trigger: 'blur' }
          // ],
          subsidyItem: [{ required: true, message: '请输入补贴项名称', trigger: 'blur' }],
          subsidyType: [{ required: true, message: '请选择变动收入公式类型', trigger: 'change' }],
          subsidyTypeName: [{ required: true, message: '请输入变动收入公式类型名称', trigger: 'blur' }],
        },
        subsidyItemTitle: '创建补贴项',
        isAddGroup: '1', //默认新增
        // groupTitle: '新增补贴组', //默认新增
        intragroupItemList: [], //查看的补贴组内的补贴项列表
        showSubsidyItemDialog: false, // 控制组查看项弹窗
        level2List: [], //二级列表
        newLevel2List: [], //筛选补贴项列表
        dynamicColumnName: '', //一级补贴项名称
        dynamicColumnId: '', //一级补贴项名称id
        groupList: [], //选择补贴项列表
        parentId: '', //父id
        subsidyFormulaList: [], //回显补贴项的公式列表
        filterList: ['a', 'b', 'c', 'x', 'y', 'k', 'm', 's', 'w'], // 变动工资过滤不需要显示的
        filterList10: ['a', 'b', 'c', 'x', 'k', 'm'], // 变动工资过滤不需要显示的 10, "数据统计项" 11 自定义统计项
        subsidyTypeFilterList: [0, 2, 3], // 编辑变动项和数据统计时，下拉框只展示 按带入公式 按固定值 按条件带入公式
        editSubsidyTypeList: [], // 编辑变动项和数据统计时，下拉框只展示 按带入公式 按固定值 按条件带入公式
      }
    },
    mounted() {
      this.getLevel2List()
      this.getBaseInfo()
      this.getTableList()
    },
    methods: {
      // 获取二级补贴项列表
      async getLevel2List() {
        await getLevel2SubsidyItemsApi({})
          .then((response) => {
            if (response.data && response.data.length > 0) {
              this.subsidyFormulaList = JSON.parse(JSON.stringify(response.data))
              response.data.map((item, index) => {
                item.number = index + 1
                this.subsidyFormulaList.map((event) => {
                  if (item.id == event.id) {
                    item.subsidyFormulaStr = event.subsidyFormula.split(',')
                  }
                })
              })
              this.level2List = response.data
              this.newLevel2List = response.data
            } else {
              this.level2List = []
              this.newLevel2List = []
            }
          })
          .catch(() => {
            this.level2List = []
            this.newLevel2List = []
          })
      },
      // 选择计算类型
      changeFormula(value) {
        if (this.subsidyTypeList.length > 0) {
          this.subsidyTypeList.map((item) => {
            if (item.code == value) {
              this.formData.subsidyTypeName = item.name
              this.subsidyNameList = [
                {
                  subsidyFormula: '',
                  stationValue: '',
                  stationId: [],
                  relationship: '^&!',
                },
              ]
            }
          })
        }
      },
      // 获取项Table列表
      async getTableList() {
        this.tableLoading = true
        await queryChangeItemsDataApi({})
          .then((response) => {
            this.tableLoading = false
            if (response.data && response.data.length > 0) {
              response.data.map((item, index) => {
                item.number = index + 1
              })
              this.tableData = response.data
            } else {
              this.tableData = [] //清空列表
            }
          })
          .catch(() => {
            this.tableData = [] //清空列表
            this.tableLoading = false
          })
      },

      // 获取基础数据
      async getBaseInfo() {
        await baseInfoApi({})
          .then((response) => {
            let newData = response.data
            // 项
            if (newData && newData.dynamicItems.length > 0) {
              this.subsidyItemList = newData.dynamicItems
            } else {
              this.subsidyItemList = [] //清空列表
            }
            // 公式类型
            if (newData && newData.subsidyTypes.length > 0) {
              this.editSubsidyTypeList = []
              this.subsidyTypeList = newData.subsidyTypes
              newData.subsidyTypes.map((item) => {
                if (this.subsidyTypeFilterList.includes(item.code)) {
                  this.editSubsidyTypeList.push(item)
                }
              })
            } else {
              this.subsidyTypeList = [] //清空列表
              this.editSubsidyTypeList = []
            }
            // 示例表格
            if (newData && newData.factors.length > 0) {
              this.factorsTable = newData.factors
            } else {
              this.factorsTable = []
            }
          })
          .catch(() => {})
      },

      // 确认提交
      confirmForm() {
        // 应用字符为~@#
        // 分割字符为$*%
        // 且关系字符为^&!
        // 或者关系符为*|#
        this.$refs['formData'].validate(async (valid) => {
          if (valid) {
            if (this.subsidyNameList.length > 0) {
              var stationStr = []
              this.subsidyNameList.map((item) => {
                if (this.formData.subsidyType == 3) {
                  if (item.subsidyFormula && item.stationValue) {
                    stationStr.push(item.subsidyFormula + '~@#' + item.stationValue)
                  }
                  if (!item.subsidyFormula || !item.stationValue) {
                    ElMessage.error('请填写计算公式')
                    throw new Error()
                  }
                } else {
                  if (item.stationValue) {
                    stationStr.push(item.stationValue)
                  }
                  if (!item.stationValue) {
                    ElMessage.error('请填写计算公式')
                    throw new Error()
                  }
                }
              })
              if (stationStr.length > 0) {
                this.formData.subsidyFormula = stationStr.join('$*%')
              } else {
                this.formData.subsidyFormula = ''
              }
            }

            if (this.formData.dynamicColumnTypeId == 11) {
              // 定义匹配小写字母的正则表达式
              const lowercasePattern = /^[a-z]+$/
              if (!lowercasePattern.test(this.formData.dataCalculateCode)) {
                ElMessage.error('请输入对应的数据项编号！')
                return
              }
            }

            let params = {
              id: this.formData.id,
              subsidyItem: this.formData.subsidyItem, // 项
              subsidyFormula: this.formData.subsidyFormula, //公式
              subsidyType: this.formData.subsidyType, // 公式类型id
              subsidyTypeName: this.formData.subsidyTypeName, //公式类型名称
              dynamicColumnId: this.formData.dynamicColumnId, // 项名称id
              dynamicColumnTypeId: this.formData.dynamicColumnTypeId, // 项名称Typeid
              dataCalculateCode: this.formData.dataCalculateCode, //对应数据项编号
            }
            this.btnLoading = true
            updateChangeItemDataApi(params)
              .then((response) => {
                ElMessage.success('编辑变动工资项成功')
                this.btnLoading = false
                this.showDialog = false
                this.getTableList()
              })
              .catch((error) => {
                this.btnLoading = false
                console.log('error')
              })
          }
        })
      },

      // 编辑变动收入项
      editItem(itemData) {
        // 赋值回显编辑
        this.subsidyNameList = []

        this.subsidyItemTitle = '编辑变动收入项'
        this.formData.dynamicColumnId = itemData.dynamicColumnId // 项名称id
        this.formData.dynamicColumnTypeId = itemData.dynamicColumnTypeId // 项名称Typeid
        this.formData.dataCalculateCode = itemData.dataCalculateCode //对应数据项编号
        if (this.editSubsidyTypeList.length > 0) {
          if (this.formData.dynamicColumnTypeId == 10 || this.formData.dynamicColumnTypeId == 11) {
            this.editSubsidyTypeList = this.editSubsidyTypeList.filter((item) => item.code == 0)
          } else {
            this.getBaseInfo() //基础数据
          }
        }

        this.formData.subsidyItem = itemData.subsidyItem // 项名称
        this.formData.subsidyType = itemData.subsidyType ? itemData.subsidyType : this.subsidyTypeList.length > 0 ? this.subsidyTypeList[0].code : '' // 公式类型id
        this.formData.subsidyTypeName = itemData.subsidyTypeName
          ? itemData.subsidyTypeName
          : this.subsidyTypeList.length > 0
            ? this.subsidyTypeList[0].name
            : '' //公式类型名称
        this.formData.id = itemData.id
        this.showDialog = true
        //公式
        if (itemData.subsidyFormula) {
          var subsidyListData = itemData.subsidyFormula.split('$*%')

          if (subsidyListData.length > 0) {
            subsidyListData.map((item) => {
              if (itemData.subsidyType == 4) {
                let newStationValue = item.split('~@#')
                let stationIdList = []
                let newRelationship = '^&!'
                if (newStationValue.length > 0) {
                  if (newStationValue[0].includes('^&!')) {
                    stationIdList = newStationValue[0].split('^&!')
                    newRelationship = '^&!'
                  } else if (newStationValue[0].includes('*|#')) {
                    stationIdList = newStationValue[0].split('*|#')
                    newRelationship = '*|#'
                  } else {
                    // split() 方法如果传入不存在的可以分割的字符 返回原数据  自定义 !,
                    stationIdList = newStationValue[0].split('!,')
                  }
                }
                this.subsidyNameList.push({
                  subsidyFormula: '',
                  stationValue: newStationValue[1],
                  stationId: stationIdList,
                  relationship: newRelationship,
                })
              } else {
                let newStationValue = item.split('~@#')
                if (itemData.subsidyType == 3) {
                  this.subsidyNameList.push({
                    subsidyFormula: newStationValue[0],
                    stationValue: newStationValue[1],
                    stationId: [],
                  })
                } else {
                  if (newStationValue.length >= 1) {
                    this.subsidyNameList.push({
                      subsidyFormula: '',
                      stationValue: newStationValue[0],
                      stationId: [],
                    })
                  }
                }
              }
            })
          }
        } else {
          this.subsidyNameList = [
            {
              subsidyFormula: '',
              stationValue: '',
              stationId: [],
              relationship: '^&!',
            },
          ]
        }
      },

      // 重置
      resetForm() {
        this.formData.id = ''
        this.formData.subsidyItem = '' // 项名称
        this.formData.dynamicColumnId = '' // 项名称id
        this.formData.dynamicColumnTypeId = '' // 项名称typeId
        this.formData.subsidyFormula = '' //公式
        this.formData.subsidyType = '' // 公式类型id
        this.formData.subsidyTypeName = '' //公式类型名称
        this.subsidyNameList = [
          {
            subsidyFormula: '',
            stationValue: '',
            stationId: [],
            relationship: '^&!',
          },
        ]
      },

      // 关闭弹窗
      handleClose() {
        this.showDialog = false
      },
      // 取消弹窗
      cancel() {
        this.showDialog = false
      },
      // 取消弹窗
      cancelGroup() {
        this.showGroupDialog = false
      },
      // 关闭弹窗
      handleCloseGroup() {
        this.showGroupDialog = false
      },

      //添加项
      addItem() {
        this.subsidyNameList.push({
          subsidyFormula: '',
          stationId: [],
          stationValue: '',
          relationship: '^&!',
        })
      },
      //删除项
      delColumn(index, item) {
        this.subsidyNameList.splice(index, 1)
      },
      // 删除创建收入项
      deteleRow(data) {
        let params = {
          id: data.id,
        }
        ElMessageBox.confirm('确定删除当前变动收入项？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          deleteItemApi(params)
            .then((response) => {
              ElMessage.success('删除成功')
              this.getTableList()
            })
            .catch((error) => {
              console.log('error')
            })
        })
      },

      // 选择补贴项
      changeGroup(dataValue) {
        this.groupList = []
        if (dataValue.length > 0) {
          dataValue.map((item) => {
            this.level2List.map((event) => {
              if (item == event.id) {
                this.groupList.push({
                  itemId: event.id,
                  itemName: event.subsidyItem,
                })
              }
            })
          })
        }
      },

      // 设置计算关系
      setRelationship(rowData) {
        this.parentId = rowData.id
        this.dynamicColumnName = rowData.subsidyItem
        this.dynamicColumnId = rowData.dynamicColumnId
        let newLevelList = rowData.level2SubsidyIds ? rowData.level2SubsidyIds.split(',') : []
        let newLevelNameList = rowData.level2SubsidyNames ? rowData.level2SubsidyNames.split(',') : []
        this.formGroupData.itemId = newLevelList
        this.formGroupData.itemName = newLevelNameList
        if (newLevelList.length > 0) {
          this.groupList = []
          newLevelList.map((item, index) => {
            this.groupList.push({
              itemId: item,
              itemName: newLevelNameList[index],
            })
          })
        } else {
          this.groupList = []
        }
        this.showGroupDialog = true
      },
      // 确定设置关系
      confirmGroup() {
        if (this.groupList.length == 0) {
          ElMessage.error('请选择补贴项')
        } else {
          let params = {
            items: this.groupList,
            parentId: this.parentId,
            subsidyItem: this.dynamicColumnName,
            dynamicColumnId: this.dynamicColumnId,
          }
          bindSubsidyItemRelationApi(params)
            .then((response) => {
              ElMessage.success('补贴关系设置成功')
              this.showGroupDialog = false
              this.getTableList()
            })
            .catch((error) => {
              console.log('error')
            })
        }
      },
      // 导出
      handleExport() {
        this.exportLoading = true
        exportChangeItemApi({})
          .then((res) => {
            downloadFileGlobalFun(res)
            this.exportLoading = false
            ElMessage.success('操作成功')
          })
          .catch((error) => {
            this.exportLoading = false
          })
      },
    },
  }
</script>

<style scoped>
  .components-container {
    margin: 20px;
    position: relative;
    background: #fff;
    padding: 20px;
    border-radius: 5px;
  }
  .addInputValue {
    padding: 4px 0;
  }
  .el-input.is-disabled .el-input__inner {
    color: #999;
  }
</style>
