<!--
 * @Author: llm
 * @Date: 2024-11-26 18:19:57
 * @LastEditors: llm
 * @LastEditTime: 2025-04-28 16:35:55
 * @Description: 数据统计
-->
<template>
  <!-- 变动收入计算 -->
  <div>
    <div class="components-container">
      <div ref="topHeight">
        <div class="flex" style="align-items: center; padding-bottom: 20px">
          <el-tag style="margin-left: 10px" type="warning">数据统计由"人员设定"中的表头项为"数据统计项"组成。</el-tag>
        </div>
        <el-table :data="tableData" :loading="tableLoading" border fit highlight-current-row size="default" style="width: 100%">
          <el-table-column align="center" label="序号" type="index" width="60"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" label="统计项名称" prop="subsidyItem"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" label="对应系统数据" prop="subsidyItemLabel"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" label="创建时间" prop="createTime"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" label="操作">
            <template #default="scope">
              <el-button size="small" type="text" @click="setRelationship(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" link @click="deleteRow(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 编辑 -->
    <el-dialog draggable :before-close="handleCloseGroup" :close-on-click-modal="false" v-model="showGroupDialog" title="编辑数据统计" width="840px">
      <el-form ref="formDataRef" :rules="rules" :inline="true" :model="formData" label-width="140px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="数据统计项名称：" prop="subsidyItem">
              <span style="color: #333; font-weight: bold">{{ formData.subsidyItem }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="对应系统项" prop="subsidyFormula">
              <el-cascader filterable clearable v-model="formData.subsidyFormula" :options="systemDataList" :props="{ emitPath: false }" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- 按钮 -->
      <template #footer>
        <div class="submit-btn">
          <el-button @click="cancelGroup">取消</el-button>
          <el-button type="primary" @click="confirmGroup">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import {
    bindSubsidyItemRelationApi,
    deleteItemApi,
    postDataStatisticsListApi,
    getDataStatisticsValuesApi,
    updateChangeItemDataApi,
  } from '@/api/salaryFormulaApi/index'
  export default {
    name: 'OutboundBasicDataSalaryFormulaDataStatistics',
    data() {
      return {
        showGroupDialog: false, //关系绑定弹窗
        formData: {
          id: '',
          subsidyItem: '', // 项名称
          subsidyFormula: '', //公式
          subsidyType: '0', // 公式类型id
          subsidyTypeName: '按代入公式', //公式类型名称
          dynamicColumnId: '', // 项名称id
          dynamicColumnTypeId: '', //项名称typeid
          dataCalculateCode: '', //对应数据项编号
        },
        tableData: [],
        tableLoading: false,
        rules: {
          subsidyFormula: [{ required: true, message: '请选择对应系统项', trigger: 'change' }],
        },
        systemDataList: [], //系统项下拉
      }
    },
    mounted() {
      this.getLevel2List().then(() => {
        this.getTableList()
      })
    },
    methods: {
      // 获取对应系统项
      async getLevel2List() {
        await getDataStatisticsValuesApi({})
          .then((response) => {
            if (response.data && response.data.length > 0) {
              this.systemDataList = JSON.parse(JSON.stringify(response.data))
            } else {
              this.systemDataList = []
            }
          })
          .catch(() => {
            this.systemDataList = []
          })
      },
      // 获取项Table列表
      async getTableList() {
        this.tableLoading = true
        await postDataStatisticsListApi({ type: 'dataStatisticsItem' })
          .then((response) => {
            this.tableLoading = false
            if (response.data && response.data.length > 0) {
              response.data.map((item) => {
                // Find the matching child item in systemDataList
                let matchedChild = null
                for (const parent of this.systemDataList) {
                  const child = parent.children.find((child) => child.value === item.subsidyFormula)
                  if (child) {
                    matchedChild = child
                    break
                  }
                }
                // Set the label from the matched child item, or empty string if not found
                item.subsidyItemLabel = matchedChild ? matchedChild.label : ''
              })
              this.tableData = response.data
            } else {
              this.tableData = [] //清空列表
            }
          })
          .catch(() => {
            this.tableData = [] //清空列表
            this.tableLoading = false
          })
      },
      // 取消弹窗
      cancelGroup() {
        this.$refs.formDataRef.resetFields()
        this.showGroupDialog = false
      },
      // 关闭弹窗
      handleCloseGroup() {
        this.$refs.formDataRef.resetFields()
        this.showGroupDialog = false
      },
      // 删除创建收入项
      deleteRow(data) {
        const params = {
          id: data.id,
          subsidyItem: data.subsidyItem,
          subsidyFormula: '',
          subsidyType: '0',
          subsidyTypeName: '按代入公式',
          dynamicColumnId: data.dynamicColumnId,
          dynamicColumnTypeId: data.dynamicColumnTypeId,
          dataCalculateCode: data.dataCalculateCode,
        }
        ElMessageBox.confirm('确定删除当前数据统计项？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          updateChangeItemDataApi(params)
            .then((response) => {
              ElMessage.success('删除成功')
              this.getTableList()
            })
            .catch((error) => {})
        })
      },
      // 确定设置关系
      confirmGroup() {
        this.$refs.formDataRef.validate((valid) => {
          if (valid) {
            updateChangeItemDataApi(this.formData)
              .then((response) => {
                ElMessage.success('数据统计关系设置成功')
                //清除校验
                this.$refs.formDataRef.resetFields()
                this.showGroupDialog = false
                this.getTableList()
              })
              .catch((error) => {})
          }
        })
      },
      // 设置计算关系
      setRelationship(rowData) {
        this.formData.id = rowData.id
        this.formData.subsidyItem = rowData.subsidyItem
        this.formData.subsidyFormula = rowData.subsidyFormula
        this.formData.subsidyType = '0'
        this.formData.subsidyTypeName = '按代入公式'
        this.formData.dynamicColumnId = rowData.dynamicColumnId
        this.formData.dynamicColumnTypeId = rowData.dynamicColumnTypeId
        this.formData.dataCalculateCode = rowData.dataCalculateCode
        this.showGroupDialog = true
      },
    },
  }
</script>

<style scoped>
  .components-container {
    margin: 20px;
    position: relative;
    background: #fff;
    padding: 20px;
    border-radius: 5px;
  }
  .addInputValue {
    padding: 4px 0;
  }
  .el-input.is-disabled .el-input__inner {
    color: #999;
  }
</style>
