<!--
 * @Author: 周宗文
 * @Date: 2024-12-04 10:31:11
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-03-11 11:57:57
 * @Description: 固定收入
-->

<template>
  <!-- 固定工资计算 -->
  <div class="components-container" v-loading="showLoading" element-loading-text="加载中...">
    <el-tag v-if="errorDada" style="margin-bottom: 10px" type="warning">固定工资计算由“人员设定”中的表头项类型为—"固定收入"，"固定扣除"组成。</el-tag>
    <el-descriptions v-if="errorDada" title="固定工资计算" :column="2" size="small" border>
      <template #extra>
        <el-button type="primary" @click="refresh">刷新</el-button>
        <el-button type="primary" @click="confirmForm">确定</el-button>
      </template>
      <el-descriptions-item
        min-width="40px"
        :label="item.columnName"
        label-align="center"
        align="left"
        label-class-name="my-label"
        v-for="item in fixedSalaryList"
        :key="item.id"
      >
        <el-select v-model="item.formulaType" multiple collapse-tags collapse-tags-tooltip placeholder="请选择设定方式" style="width: 250px">
          <el-option v-for="event in formulaTypeList" :key="event.formulaType" :label="event.formulaTypeName" :value="event.formulaType"></el-option>
        </el-select>
      </el-descriptions-item>
    </el-descriptions>
    <div v-else class="flex">
      <div style="text-align: center">
        <div>请先在人员设定中设置基础数据</div>
      </div>
    </div>
  </div>
</template>

<script>
  import {
    queryFixedSalaryDataApi, // 查询
    updateFixedSalaryDataApi, // 更新
  } from '@/api/salaryFormulaApi/index'
  export default {
    name: 'OutboundBasicDataSalaryFormulaFixedIncome',
    data() {
      return {
        formData: {
          id: '',
          formulaType: '',
        },
        errorDada: true,
        fixedSalaryList: [], //固定项
        formulaTypeList: [], //选择维度
        showLoading: false,
      }
    },
    created() {
      this.getSalaryData()
    },
    mounted() {},
    methods: {
      // 获取
      getSalaryData() {
        this.showLoading = true
        queryFixedSalaryDataApi({})
          .then((response) => {
            var newData = response.data
            if (newData.fixedSalaryItems && newData.fixedSalaryItems.length > 0) {
              newData.fixedSalaryItems.map((item) => {
                item.formulaType = item.formulaType.split(',')
              })
              newData.fixedSalaryItems.map((event) => {
                event.formulaType = event.formulaType.map(Number)
              })
              this.fixedSalaryList = newData.fixedSalaryItems
            } else {
              this.fixedSalaryList = []
            }
            if (newData.formulaTypeItems && newData.formulaTypeItems.length > 0) {
              this.formulaTypeList = newData.formulaTypeItems
            } else {
              this.formulaTypeList = []
            }
            this.errorDada = true
            this.showLoading = false
          })
          .catch((error) => {
            this.errorDada = false
            this.showLoading = false
          })
      },
      refresh() {
        this.getSalaryData()
      },
      // 确定修改
      confirmForm() {
        ElMessageBox.confirm('确定提交当前设定？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            let params = {
              items: this.fixedSalaryList,
            }
            params.items.map((item) => {
              item.formulaType = item.formulaType.join(',')
            })
            updateFixedSalaryDataApi(params)
              .then((response) => {
                if (response.code === 200) {
                  ElMessage.success('更新成功')
                  this.getSalaryData()
                } else {
                  ElMessage.error(response.message)
                }
              })
              .catch(() => {})
          })
          .catch(() => {})
      },
    },
  }
</script>

<style scoped>
  .components-container {
    margin: 20px;
    position: relative;
    background: #fff;
    padding: 20px;
    border-radius: 5px;
  }
  .flex {
    height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: #666;
  }
  .formulaItem :deep(.el-select__tags) {
    max-width: 235px !important;
  }
  :deep(.my-label) {
    background: var(--el-color-white) !important;
  }
  :deep(.el-descriptions--small .el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
    padding: 10px 8px !important;
    font-size: 14px;
  }
</style>
