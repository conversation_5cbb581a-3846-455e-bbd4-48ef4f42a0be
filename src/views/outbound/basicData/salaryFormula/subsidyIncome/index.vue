<!--
 * @Author: llm
 * @Date: 2024-11-26 18:19:32
 * @LastEditors: llm
 * @LastEditTime: 2025-07-02 10:25:17
 * @Description: 补贴收入
-->
<template>
  <div class="components-container">
    <el-tabs v-model="activeName" class="tabChangeClass" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="补贴设置" name="subsidy">
        <div class="newaddbutton">
          <div class="top_left">
            <div class="lable">补贴名称</div>
            <el-input v-model="subsidyName" style="width: 100%" placeholder="模糊查询" />
          </div>
          <div>
            <el-button type="primary" @click="searchSubsidyItems">搜索</el-button>
            <el-button @click="resetSubsidyItems">重置</el-button>
          </div>
        </div>
        <div class="flex" style="align-items: center; padding-bottom: 20px">
          <el-button type="primary" style="margin-right: 10px" @click="exportClick1">模版下载</el-button>
          <el-upload
            :accept="'.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar,.7z'"
            ref="uploadFileRef"
            :show-file-list="false"
            action="#"
            :auto-upload="false"
            :multiple="false"
            :limit="1"
            :on-change="
              (file, fileList) => {
                uploadFile1(file, fileList)
              }
            "
          >
            <template #trigger>
              <el-button type="primary" style="margin-right: 10px">数据导入</el-button>
            </template>
          </el-upload>
          <el-button type="primary" @click="createColumn">创建补贴</el-button>
          <el-tag style="margin-left: 10px" type="warning">"->"： 连接条件与公式， "&&"： 并且，"||"：或者， "=="： 等于</el-tag>
        </div>
        <el-table :data="level2List" border class="subsidyTable" fit highlight-current-row size="default">
          <el-table-column align="center" label="序号" type="index" width="60"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" label="补贴名称" prop="subsidyItem"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" label="计算公式类型" prop="subsidyTypeName"></el-table-column>
          <el-table-column align="center" label="计算公式" prop="subsidyFormulaFormat">
            <template #default="scope">
              <span>{{ scope.row.subsidyFormulaFormat.join('\n') }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" label="创建时间" prop="createTime"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" label="操作">
            <template #default="scope">
              <el-button size="default" type="primary" text @click="editItem(scope.row)">编辑</el-button>
              <el-button size="default" type="primary" text @click="deteleRow(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <Pagination :total="totalSizes" :page="currentPage" :limit="pageSize" @pagination="_paginations" />
      </el-tab-pane>
      <el-tab-pane label="应用场景设置" name="application">
        <div class="newaddbutton">
          <div class="top_left">
            <div class="lable">应用场景名称</div>
            <el-input v-model="scenariosName" style="width: 100%" placeholder="模糊查询" />
          </div>
          <div>
            <el-button type="primary" @click="subsidyIncome">搜索</el-button>
            <el-button @click="restSubsidyIncome">重置</el-button>
          </div>
        </div>
        <div class="flex" style="align-items: center; padding-bottom: 20px">
          <el-button type="primary" @click="addSceneColumn">新建应用场景</el-button>
          <el-button type="primary" style="margin-right: 10px" @click="exportClick">模版下载</el-button>
          <el-upload
            :accept="'.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar,.7z'"
            ref="uploadFileRef"
            :show-file-list="false"
            action="#"
            :auto-upload="false"
            :multiple="false"
            :limit="1"
            :on-change="
              (file, fileList) => {
                uploadFile(file, fileList)
              }
            "
          >
            <template #trigger>
              <el-button type="primary" style="margin-right: 10px">数据导入</el-button>
            </template>
          </el-upload>

          <!-- <el-button type="primary" @click="addSceneColumn">数据导入</el-button>
          <el-button type="primary" @click="addSceneColumn">数据导出</el-button> -->
        </div>
        <el-table :data="tableData" :loading="tableLoading" border class="subsidyTable" fit highlight-current-row size="default">
          <el-table-column align="center" label="序号" type="index" width="60"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" label="场景名称" prop="name"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" label="司机类型" prop="driverType"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" label="车牌号" prop="vehicleNos"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" label="调度单类型" prop="dispatchType"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" label="能耗类型" prop="ecType"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" label="路线起点" prop="lineStartName"></el-table-column>
          <el-table-column align="center" :show-overflow-tooltip="true" label="路线终点" prop="lineEndName"></el-table-column>
          <el-table-column align="left" :show-overflow-tooltip="true" label="补贴项" prop="items">
            <template #default="scope">
              <span v-for="(event, _index) in scope.row.items" :key="_index">
                <span style="color: #000; font-weight: 500">{{ event.subsidyItem }} :</span>
                <span>{{ event.subsidyFormulaFormat.join('\n') }}</span>
                <br />
              </span>
            </template>
          </el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" label="开始生效时间" prop="startTime"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" label="结束生效时间" prop="endTimeDisplay"></el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" label="操作">
            <template #default="scope">
              <el-button size="default" type="primary" text @click="editSceneItem(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- v-if="totalSize >= pageSize" -->
        <Pagination :total="totalSize" :page="currentPage" :limit="pageSize" @pagination="_pagination" />
      </el-tab-pane>
    </el-tabs>

    <!-- 新增编辑 补贴 弹窗 -->
    <el-dialog draggable :before-close="handleClose" :close-on-click-modal="false" :title="subsidyItemTitle" v-model="showDialog" width="940px">
      <el-form ref="formData" :inline="true" :model="formData" :rules="rules">
        <el-row>
          <el-col :span="24">
            <el-form-item label="名称：" prop="subsidyItem">
              <el-input v-model="formData.subsidyItem" clearable placeholder="请输入补贴名称"></el-input>
            </el-form-item>

            <el-form-item label="计算类型：" prop="subsidyType">
              <el-select v-model="formData.subsidyType" placeholder="请选择计算类型" clearable @change="changeFormula" style="width: 240px">
                <el-option v-for="item in subsidyTypeList" :key="item.code" :label="item.name" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="subsidyNameList.length" :span="24">
            <el-card>
              <el-row>
                <el-col :span="24">
                  <div style="padding: 6px 0">
                    <span>参与计算的编号与单位:</span>
                    <!-- 补贴 -->
                    <span v-for="item in factorsTable" :key="item.id">
                      <el-tag v-if="!termFilterList.includes(item.code)" style="margin: 0px 5px 5px">{{
                        item.code + ' - ' + item.name + '/' + item.unit
                      }}</el-tag>
                    </span>
                  </div>
                </el-col>
              </el-row>
              <el-tag style="margin-bottom: 5px" type="warning">请用上方对应的单位编号创建公式</el-tag>
              <div v-for="(item, index) in subsidyNameList" :key="index">
                <div v-if="formData.subsidyType == 4" class="addInputValue">
                  <span>计算公式{{ index + 1 }}：</span>
                  <el-select v-model="item.relationship" class="collectionPoint" placeholder="计算关系" style="width: 100px; margin-right: 10px">
                    <el-option v-for="item in relationshipList" :key="item.value" :label="item.name" :value="item.value" />
                  </el-select>
                  <el-input v-model="item.stationValue" placeholder="请输入计算公式" style="width: 230px; margin-right: 10px"></el-input>
                  <el-button
                    v-if="index === subsidyNameList.length - 1 && formData.subsidyType != 0 && formData.subsidyType != 2"
                    type="primary"
                    text
                    @click="addItem()"
                    >添加</el-button
                  >
                  <el-button v-if="subsidyNameList.length > 1" style="color: red" type="primary" text @click="delColumn(index)">删除</el-button>
                </div>
                <div v-else class="addInputValue">
                  <span>计算公式{{ index + 1 }}：</span>
                  <el-input
                    v-if="formData.subsidyType == 3"
                    v-model="item.subsidyFormula"
                    placeholder="请输入计算条件"
                    clearable
                    style="width: 230px; margin-right: 10px"
                  ></el-input>
                  <el-input v-model="item.stationValue" placeholder="请输入计算公式(数值)" clearable style="width: 230px; margin-right: 10px"></el-input>
                  <span v-if="formData.dynamicColumnTypeId == 11">对应数据项编号：</span>
                  <el-input
                    v-if="formData.dynamicColumnTypeId == 11"
                    v-model="formData.dataCalculateCode"
                    placeholder="请输入对应数据项编号"
                    clearable
                    style="width: 230px; margin-right: 10px"
                  ></el-input>

                  <el-button
                    v-if="index === subsidyNameList.length - 1 && formData.subsidyType != 0 && formData.subsidyType != 2 && formData.dynamicColumnTypeId != 11"
                    text
                    type="primary"
                    @click="addItem()"
                  >
                    添加
                  </el-button>
                  <el-button v-if="subsidyNameList.length > 1" style="color: red" text type="primary" @click="delColumn(index)">删除</el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
      <!-- 按钮 -->
      <template #footer>
        <div class="submit-btn">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="confirmForm">确定</el-button>
        </div>
      </template>
    </el-dialog>



    <!-- 新增编辑 应用场景 弹窗 -->
    <el-dialog draggable :before-close="closeScene" :close-on-click-modal="false" :title="sceneTitle" v-model="showSceneDialog" width="840px">
      <div style="padding: 10px 40px 10px 20px">
        <el-form ref="sceneFormData" :model="sceneForm" :rules="sceneRules" label-width="110">
          <el-form-item label="场景名称：" prop="name">
            <el-input v-model="sceneForm.name" clearable placeholder="请输入场景名称"></el-input>
          </el-form-item>
          <el-form-item label="司机类型：" prop="driverType">
            <el-select v-model="sceneForm.driverType" placeholder="请选择司机类型">
              <el-option v-for="item in driverTypeList" :key="item.name" :label="item.name" :value="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="车牌号" prop="vehicleNos">
            <el-select
              v-model="sceneForm.vehicleNos"
              remote-show-suffix
              clearable
              multiple
              collapse-tags
              collapse-tags-tooltip
              remote
              filterable
              reserve-keyword
              placeholder="模糊搜索"
              :remote-method="(query) => remoteSelectVehicleNosMethod(query)"
              :loading="fuzzySelectLoading"
            >
              <el-option v-for="i in vehicleNoList" :key="i.value" :label="i.label" :value="i.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="调度单类型：" prop="dispatchType">
            <el-select v-model="sceneForm.dispatchType" placeholder="请选择调度单类型">
              <el-option v-for="item in dispatchTypeList" :key="item.name" :label="item.name" :value="item.name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="应用场景：" prop="type">
            <el-select v-model="sceneForm.type" placeholder="请选择应用场景">
              <el-option v-for="item in sceneTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="起点设置：" v-if="sceneForm.type == 1" prop="lineStartId">
            <!-- 远程模糊搜索下拉 -->
            <el-select
              v-model="sceneForm.lineStartId"
              remote-show-suffix
              clearable
              multiple
              collapse-tags
              collapse-tags-tooltip
              remote
              filterable
              reserve-keyword
              placeholder="模糊搜索"
              :remote-method="(query) => remoteSelectMethod(query, 'start')"
              :loading="fuzzySelectLoading"
            >
              <el-option v-for="i in startOptionsData" :key="i.value" :label="i.label" :value="i.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="终点设置：" v-if="sceneForm.type == 1" prop="lineEndId">
            <!-- 远程模糊搜索下拉 -->
            <el-select
              v-model="sceneForm.lineEndId"
              remote-show-suffix
              clearable
              multiple
              collapse-tags
              collapse-tags-tooltip
              remote
              filterable
              reserve-keyword
              placeholder="模糊搜索"
              :remote-method="(query) => remoteSelectMethod(query, 'end')"
              :loading="fuzzySelectLoading"
            >
              <el-option v-for="i in endOptionsData" :key="i.value" :label="i.label" :value="i.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="能耗类型：" prop="ecTypes">
            <el-select v-model="sceneForm.ecTypes" multiple collapse-tags collapse-tags-tooltip placeholder="请选择能耗类型" clearable>
              <el-option v-for="item in energyConsumptionTypeList" :key="item.name" :label="item.name" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="补贴设置：" prop="itemId">
            <el-select v-model="sceneForm.itemId" multiple collapse-tags collapse-tags-tooltip placeholder="请选择补贴设置" clearable>
              <el-option v-for="item in level2List" :key="item.id" :label="item.subsidyItem" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开始时间：" prop="startTime">
            <!-- <el-date-picker
              v-model="sceneForm.times"
              type="daterange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable
            /> -->
            <el-date-picker value-format="YYYY-MM-DD" v-model="sceneForm.startTime" type="date" :clearable="true" placeholder="开始日期"> </el-date-picker>
          </el-form-item>
          <el-form-item label="结束时间：" prop="endTime">
            <el-date-picker value-format="YYYY-MM-DD" @change="EndChange" v-model="sceneForm.endTime" type="date" :clearable="true" placeholder="结束日期">
            </el-date-picker>
            <div class="ml-5px">
              <el-checkbox v-model="isLongTerm" @change="sceneForm.endTime = ''" label="长期" />
            </div>
          </el-form-item>
          <el-form-item label="备注：" prop="remark">
            <el-input type="textarea" v-model="sceneForm.remark" autocomplete="off" placeholder="请输入备注" clearable />
          </el-form-item>
        </el-form>
      </div>
      <!-- 按钮 -->
      <template #footer>
        <div class="submit-btn">
          <el-button @click="closeScene">取消</el-button>
          <el-button type="primary" @click="confirmSceneForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>

  import { downloadFileGlobalFun } from '@/utils/common.ts'
  import { importFileGlobalFun, importFileGlobalFun1 } from '@/api/auth/index.ts'
  import {
    getLevel2SubsidyItemsApi,
    baseInfoApi,
    saveSubsidyApi,
    subsidyApplyToPageApi,
    deleteItemApi,
    startEndSelectApi,
    saveSubsidyApplyToApi,
    updateSubsidyApplyToApi,
    subsidyIncomeQueryApi,
    applicationScenariosApi,
    subsidyApplyTo,
    subsidyApplyTo1,
    subsidyApplyGo,
    vehicleNoSelectApi,
  } from '@/api/salaryFormulaApi/index'
  import defaultSettings from '@/settings'
  import Pagination from '@/components/TmsPagination/index.vue'
  export default {
    name: 'OutboundBasicDataSalaryFormulaSubsidyIncome',
    components: {
      Pagination
    },
    data() {
      return {
        butnLoading: false,
        importLoading: { value: false },
        isLongTerm: false, // 是否长期
        activeName: 'subsidy', //默认补贴
        sceneTypeList: [
          {
            id: 0,
            name: '全部线路',
          },
          {
            id: 1,
            name: '指定线路',
          },
        ],
        driverTypeList: [
          {
            name: '全部司机',
          },
          {
            name: '自有司机',
          },
          {
            name: '承包司机',
          },
        ],
        dispatchTypeList: [
          {
            name: '全部',
          },
          {
            name: '倒板',
          },
          {
            name: '正常',
          },
        ],
        energyConsumptionTypeList: [
          {
            name: '汽油',
            value: '汽油',
          },
          {
            name: '柴油',
            value: '柴油',
          },
          {
            name: '燃气（LNG）',
            value: '燃气（LNG）',
          },
          {
            name: '燃气（CNG）',
            value: '燃气（CNG）',
          },
          {
            name: '纯电动',
            value: '纯电动',
          },
          {
            name: '插电混动',
            value: '插电混动',
          },
        ],
        sceneTitle: '新建应用场景',
        fuzzySelectLoading: false,

        showDialog: false, // 补贴项弹窗
        showSceneDialog: false, // 新建场景
        formData: {
          id: '',
          subsidyItem: '', // 项名称
          subsidyFormula: '', //公式
          subsidyType: '', // 公式类型id
          subsidyTypeName: '', //公式类型名称
          dynamicColumnId: '', // 项名称id
          dynamicColumnTypeId: '', //项名称typeid
          dataCalculateCode: '', //对应数据项编号
        },
        // 公式类型选择
        subsidyNameList: [
          {
            subsidyFormula: '',
            stationValue: '',
            stationId: [],
            relationship: '^&!',
          },
        ],
        formGroupData: {
          itemId: [], // 补贴项id
          itemName: [], // 补贴项名称
        },
        factorsTable: [], //示例
        tableData: [],
        subsidyItemList: [], //项列表
        subsidyTypeList: [], //公式列表
        tableLoading: false,
        vehicleNoList: [], //车牌号列表
        rules: {
          subsidyItem: [{ required: true, message: '请输入补贴项名称', trigger: 'blur' }],
          subsidyType: [{ required: true, message: '请选择变动工资公式类型', trigger: 'change' }],
          subsidyTypeName: [{ required: true, message: '请输入变动工资公式类型名称', trigger: 'blur' }],
        },
        sceneRules: {
          name: [{ required: true, message: '请输入场景名称', trigger: 'blur' }],
          type: [{ required: true, message: '请选择应用场景', trigger: 'change' }],
          startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
          // endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
          driverType: [{ required: true, message: '请选择司机类型', trigger: 'change' }],
          dispatchType: [{ required: true, message: '请选择调度单类型', trigger: 'change' }],
          lineStartId: [{ required: true, message: '请选择起点位置', trigger: 'change' }],
          lineEndId: [{ required: true, message: '请选择终点位置', trigger: 'change' }],
          itemId: [{ required: true, message: '请选择补贴设置', trigger: 'change' }],
          // ecTypes: [{ required: true, message: '请选择能耗类型', trigger: 'change' }],
        },
        RoutedialogVisible:false,
        subsidyItemTitle: '创建补贴',
        level2List: [], //二级列表
        dynamicColumnId: '', //一级补贴项名称id
        keyWord: '', //搜索项值
        termFilterList: ['d', 'e', 'y', 's', 'z'], // 补贴项过滤不需要显示的
        subsidyTypeFilterList: [0, 2, 3], // 编辑变动项和数据统计时，下拉框只展示 按带入公式 按固定值 按条件带入公式
        editSubsidyTypeList: [], // 编辑变动项和数据统计时，下拉框只展示 按带入公式 按固定值 按条件带入公式
        currentPage: 1, //当前页
        pageSize: defaultSettings.globalLimit, //默认每页条数
        totalSize: 100, //总数
        totalSizes: 100, //总数
        sceneForm: {
          name: '',
          remark: '',
          type: 0, //应用场景
          driverType: '全部司机', //司机类型
          dispatchType: '全部', //调度单类型
          ecTypes: [], //能耗类型
          itemId: [], //补贴项
          // times: '', 时间
          startTime: '',
          endTime: '',
          lineEndId: [], //终点
          lineStartId: [], //起点
        },
        startOptionsData: [],
        endOptionsData: [],
        currentId: '', //当前编辑应用场景的id
        subsidyName: '', // 补贴名称 模糊查询
        scenariosName: '', // 场景应用名称 模糊查询
      }
    },
    created() {},
    mounted() {
      this.getBaseInfo() //基础数据
      this.getLevel2List()
    },
    watch: {},
    methods: {
      // 补贴查询
      searchSubsidyItems() {
        let param = {
          name: this.subsidyName,
        }
        applicationScenariosApi(param).then((res) => {
          this.level2List = res.data
        })
      },
      EndChange() {
        this.isLongTerm = false
      },
      // 重置
      resetSubsidyItems() {
        this.subsidyName = ''
        this.getLevel2List()
      },
      // 应用查询
      subsidyIncome() {
        let param = {
          page: 1,
          limit: 50,
          name: this.scenariosName,
        }
        subsidyIncomeQueryApi(param).then((res) => {
          this.tableData = res.data.rows
        })
      },
      async uploadFile(file, fileList) {
        importFileGlobalFun(file.raw, '/logistics/api/out/fleet/subsidy/subsidyApplyTo')
          .then(async (res) => {
            this.currentPage = 1
            this.getTableList()
            ElMessage.success('导入成功')
            this.$refs['uploadFileRef'].clearFiles()
          })
          .catch((err) => {
            this.$refs['uploadFileRef'].clearFiles()
          })
        if (file && file.raw) {
          if (this.$refs['uploadFileRef'][0]) {
            this.$refs['uploadFileRef'].clearFiles()
          }
        }
      },

      async uploadFile1(file, fileList) {
        importFileGlobalFun1(file.raw, '/logistics/api/out/fleet/subsidy/level2Import')
          .then(async (res) => {
            this.currentPage = 1
            this.getLevel2List()
            ElMessage.success('导入成功')
            this.$refs['uploadFileRef'].clearFiles()
          })
          .catch((err) => {
            this.$refs['uploadFileRef'].clearFiles()
          })
        if (file && file.raw) {
          if (this.$refs['uploadFileRef'][0]) {
            this.$refs['uploadFileRef'].clearFiles()
          }
        }
      },

      exportClick() {
        // window.open(row)
        subsidyApplyTo().then((res) => {
          downloadFileGlobalFun(res)
        })
      },
      exportClick1() {
        // window.open(row)
        subsidyApplyTo1().then((res) => {
          downloadFileGlobalFun(res)
        })
      },
      restSubsidyIncome() {
        this.scenariosName = ''
        let param = {
          page: 1,
          limit: 50,
          name: '',
        }
        subsidyIncomeQueryApi(param).then((res) => {
          this.tableData = res.data.rows
        })
      },
      handleClick(tab) {
        if (tab.name == 'subsidy') {
          this.currentPages = 1
          this.getLevel2List()
        } else {
          this.currentPage = 1
          this.getTableList()
        }
      },

      // 补贴项选择公式类型
      changeFormula(value) {
        if (this.subsidyTypeList.length > 0) {
          this.subsidyTypeList.map((item) => {
            if (item.code == value) {
              this.formData.subsidyTypeName = item.name
              this.subsidyNameList = [
                {
                  subsidyFormula: '',
                  stationValue: '',
                  stationId: [],
                  relationship: '^&!',
                },
              ]
            }
          })
        }
      },

      // 模糊搜索起终点选择
      remoteSelectMethod(value, type) {
        if (value) {
          let params = {
            label: 'name',
            dataSource: '围栏',
            value: 'id',
            fuzzy: true,
            keyword: value,
          }
          startEndSelectApi(params)
            .then((response) => {
              if (response.data && response.data.length > 0) {
                if (type == 'start') {
                  this.startOptionsData = response.data.map((item) => {
                    return { value: item.value, label: item.label }
                  })
                } else {
                  this.endOptionsData = response.data.map((item) => {
                    return { value: item.value, label: item.label }
                  })
                }
              }
            })
            .catch(() => {})
        } else {
          if (type == 'start') {
            this.startOptionsData = []
          } else {
            this.endOptionsData = []
          }
        }
      },
      // 模糊搜索车牌号
      remoteSelectVehicleNosMethod(value) {
        if (value) {
          let params = {
            label: 'name',
            dataSource: '车队-车辆',
            value: 'vehicleNo',
            keyword: value,
            carrierType: '自有',
          }
          vehicleNoSelectApi(params).then((response) => {
            if (response.data && response.data.length > 0) {
              this.vehicleNoList = response.data.map((item) => {
                return { value: item.value, label: item.label }
              })
            }
          })
        }
      },

      // 获取应用场景列表
      async getTableList() {
        this.tableLoading = true
        const params = {
          page: this.currentPage,
          limit: this.pageSize,
        }
        await subsidyApplyToPageApi(params)
          .then((response) => {
            this.tableLoading = false
            if (response.code == 200) {
              this.tableData = response.data.rows
              this.totalSize = response.data.total
            } else {
              this.tableData = [] //清空列表
            }
          })
          .catch(() => {
            this.tableData = [] //清空列表
            this.tableLoading = false
          })
      },

      /**
       * 分页
       */
      _pagination(params) {
        console.log('%c [ params ] -> ', 'font-size:16px; background:#cb9d4d; color:#ffe191;', params)
        this.currentPage = params.page
        this.pageSize = params.limit
        this.getTableList()
      },
      _paginations(params) {
        console.log('%c [ params ] -> ', 'font-size:16px; background:#cb9d4d; color:#ffe191;', params)
        this.currentPage = params.page
        this.pageSize = params.limit
        this.getLevel2List()
      },

      // 获取二级补贴项列表
      getLevel2List() {
        const params = {
          page: this.currentPage,
          limit: this.pageSize,
        }
        getLevel2SubsidyItemsApi(params)
          .then((response) => {
            if (response.data && response.data.length > 0) {
              this.level2List = response.data
              this.totalSizes = response.data.length
            } else {
              this.level2List = []
            }
          })
          .catch(() => {
            this.level2List = []
          })
      },
      // 获取基础数据
      async getBaseInfo() {
        await baseInfoApi({})
          .then((response) => {
            let newData = response.data
            // 项
            if (newData && newData.dynamicItems.length > 0) {
              this.subsidyItemList = newData.dynamicItems
            } else {
              this.subsidyItemList = [] //清空列表
            }
            // 公式类型
            if (newData && newData.subsidyTypes.length > 0) {
              this.editSubsidyTypeList = []
              this.subsidyTypeList = newData.subsidyTypes
              newData.subsidyTypes.map((item) => {
                if (this.subsidyTypeFilterList.includes(item.code)) {
                  this.editSubsidyTypeList.push(item)
                }
              })
            } else {
              this.subsidyTypeList = [] //清空列表
              this.editSubsidyTypeList = []
            }
            // 示例表格
            if (newData && newData.factors.length > 0) {
              this.factorsTable = newData.factors
            } else {
              this.factorsTable = []
            }
          })
          .catch(() => {})
      },

      // 项 新增编辑提交
      confirmForm() {
        // 应用字符为~@#
        // 分割字符为$*%
        // 且关系字符为^&!
        // 或者关系符为*|#
        this.$refs['formData'].validate(async (valid) => {
          if (valid) {
            if (this.subsidyNameList.length > 0) {
              var stationStr = []
              this.subsidyNameList.map((item) => {
                if (this.formData.subsidyType == 3) {
                  if (item.subsidyFormula && item.stationValue) {
                    stationStr.push(item.subsidyFormula + '~@#' + item.stationValue)
                  }
                  if (!item.subsidyFormula || !item.stationValue) {
                    ElMessage.error('请填写计算公式')
                    throw new Error()
                  }
                } else {
                  if (item.stationValue) {
                    stationStr.push(item.stationValue)
                  }
                  if (!item.stationValue) {
                    ElMessage.error('请填写计算公式')
                    throw new Error()
                  }
                }
              })
              if (stationStr.length > 0) {
                this.formData.subsidyFormula = stationStr.join('$*%')
              } else {
                this.formData.subsidyFormula = ''
              }
            }

            if (this.formData.dynamicColumnTypeId == 11) {
              // 定义匹配小写字母的正则表达式
              const lowercasePattern = /^[a-z]+$/
              if (!lowercasePattern.test(this.formData.dataCalculateCode)) {
                ElMessage.error('请输入对应的数据项编号！')
                return
              }
            }

            let params = {
              id: this.formData.id,
              subsidyItem: this.formData.subsidyItem, // 项
              subsidyFormula: this.formData.subsidyFormula, //公式
              subsidyType: this.formData.subsidyType, // 公式类型id
              subsidyTypeName: this.formData.subsidyTypeName, //公式类型名称
            }
            saveSubsidyApi(params)
              .then((response) => {
                ElMessage.success(this.formData.id ? '编辑补贴项成功' : '新增补贴项成功')
                this.showDialog = false
                this.getLevel2List()
              })
              .catch((error) => {
                console.log('error')
              })
          }
        })
      },

      // 编辑补贴项
      editItem(itemData) {
        // 赋值回显编辑
        this.subsidyNameList = []
        this.subsidyItemTitle = '编辑补贴'
        this.formData.dynamicColumnId = '' // 置空
        this.formData.dynamicColumnTypeId = '' // 置空

        this.formData.subsidyItem = itemData.subsidyItem // 项名称
        this.formData.subsidyType = itemData.subsidyType // 公式类型id
        this.formData.subsidyTypeName = itemData.subsidyTypeName //公式类型名称
        this.formData.id = itemData.id
        this.showDialog = true
        //公式
        if (itemData.subsidyFormula) {
          var subsidyListData = itemData.subsidyFormula.split('$*%')
          if (subsidyListData.length > 0) {
            subsidyListData.map((item) => {
              if (itemData.subsidyType == 4) {
                let newStationValue = item.split('~@#')
                let stationIdList = []
                let newRelationship = '^&!'
                if (newStationValue.length > 0) {
                  if (newStationValue[0].includes('^&!')) {
                    stationIdList = newStationValue[0].split('^&!')
                    newRelationship = '^&!'
                  } else if (newStationValue[0].includes('*|#')) {
                    stationIdList = newStationValue[0].split('*|#')
                    newRelationship = '*|#'
                  } else {
                    // split() 方法如果传入不存在的可以分割的字符 返回原数据  自定义 !,
                    stationIdList = newStationValue[0].split('!,')
                  }
                }
                this.subsidyNameList.push({
                  subsidyFormula: '',
                  stationValue: newStationValue[1],
                  stationId: stationIdList,
                  relationship: newRelationship,
                })
              } else {
                let newStationValue = item.split('~@#')
                if (itemData.subsidyType == 3) {
                  this.subsidyNameList.push({
                    subsidyFormula: newStationValue[0],
                    stationValue: newStationValue[1],
                    stationId: [],
                  })
                } else {
                  if (newStationValue.length >= 1) {
                    this.subsidyNameList.push({
                      subsidyFormula: '',
                      stationValue: newStationValue[0],
                      stationId: [],
                    })
                  }
                }
              }
            })
          }
        } else {
          this.subsidyNameList = [
            {
              subsidyFormula: '',
              stationValue: '',
              stationId: [],
              relationship: '^&!',
            },
          ]
        }
      },

      // 重置
      resetForm() {
        this.formData.id = ''
        this.formData.subsidyItem = '' // 项名称
        this.formData.dynamicColumnId = '' // 项名称id
        this.formData.dynamicColumnTypeId = '' // 项名称typeId
        this.formData.subsidyFormula = '' //公式
        this.formData.subsidyType = '' // 公式类型id
        this.formData.subsidyTypeName = '' //公式类型名称
        this.subsidyNameList = [
          {
            subsidyFormula: '',
            stationValue: '',
            stationId: [],
            relationship: '^&!',
          },
        ]
      },

      // 创建项
      createColumn() {
        this.subsidyItemTitle = '创建补贴'
        this.showDialog = true
        this.resetForm()
      },
      // 关闭弹窗
      handleClose() {
        this.showDialog = false
      },

      //添加项
      addItem() {
        this.subsidyNameList.push({
          subsidyFormula: '',
          stationId: [],
          stationValue: '',
          relationship: '^&!',
        })
      },
      //删除项
      delColumn(index, item) {
        this.subsidyNameList.splice(index, 1)
      },

      // 删除创建补贴设置
      deteleRow(data) {
        let params = {
          id: data.id,
        }
        ElMessageBox.confirm('确定删除当前补贴项？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          deleteItemApi(params)
            .then((response) => {
              ElMessage.success('删除成功')
              this.getLevel2List() //二级列表
            })
            .catch((error) => {
              console.log('error')
            })
        })
      },

      // 新增场景设置
      addSceneColumn() {
        this.sceneTitle = '新增应用场景'
        this.currentId = ''
        this.sceneForm.name = ''
        this.sceneForm.type = 0
        this.sceneForm.driverType = '全部司机'
        this.sceneForm.dispatchType = '全部'
        this.sceneForm.ecTypes = []
        this.sceneForm.startTime = ''
        this.sceneForm.endTime = ''
        this.sceneForm.lineEndId = []
        this.sceneForm.lineStartId = []
        this.sceneForm.itemId = []
        this.sceneForm.remark = ''
        this.showSceneDialog = true
        this.$refs['sceneFormData'].resetFields()
      },
      closeScene() {
        this.$refs['sceneFormData'].resetFields()
        this.showSceneDialog = false
      },

      // 编辑应用场景
      editSceneItem(rowData) {
        this.sceneTitle = '编辑应用场景'
        this.currentId = rowData.id
        this.sceneForm.name = rowData.name
        this.sceneForm.type = rowData.type ? 1 : 0
        this.sceneForm.driverType = rowData.driverType
        this.sceneForm.dispatchType = rowData.dispatchType
        this.sceneForm.ecTypes = rowData.ecTypes
        this.sceneForm.startTime = rowData.startTime
        if (rowData.endTime === '9999-12-31') {
          this.isLongTerm = true
          this.sceneForm.endTime = ''
        } else {
          this.isLongTerm = false
          this.sceneForm.endTime = rowData.endTime
        }

        this.remoteSelectMethod(rowData.lineStartName, 'start')
        this.remoteSelectMethod(rowData.lineEndName, 'end')
        this.remoteSelectVehicleNosMethod(rowData.vehicleNos, 'vehicleNos')
        this.sceneForm.lineStartId = rowData.lineStartId
        this.sceneForm.lineEndId = rowData.lineEndId
        this.sceneForm.vehicleNos = rowData.vehicleNos ? rowData.vehicleNos.split(',') : []
        this.sceneForm.remark = rowData.remark
        this.sceneForm.itemId = rowData.itemId
        this.showSceneDialog = true
      },

      // 确认提交
      confirmSceneForm() {
        const start = new Date(this.sceneForm.startTime).getTime()
        const end = new Date(this.sceneForm.endTime).getTime()
        const vehicleNos = this.sceneForm.vehicleNos ? this.sceneForm.vehicleNos.join(',') : ''
        if (end <= start) {
          // 结束时间必须大于开始时间
          ElMessage.error('结束时间必须大于开始时间')
          return
        }
        this.$refs['sceneFormData'].validate(async (valid) => {
          if (valid) {
            let params = {
              name: this.sceneForm.name,
              remark: this.sceneForm.remark,
              type: this.sceneForm.type, //应用场景
              driverType: this.sceneForm.driverType, //司机类型
              dispatchType: this.sceneForm.dispatchType, //调度单类型
              ecTypes: this.sceneForm.ecTypes, //能耗类型
              itemId: this.sceneForm.itemId, //补贴项
              startTime: this.sceneForm.startTime, //时间
              endTime: this.isLongTerm ? '9999-12-31' : this.sceneForm.endTime, //时间
              lineEndId: this.sceneForm.lineEndId, //终点
              lineStartId: this.sceneForm.lineStartId, //起点
              vehicleNos: vehicleNos, //车牌号
            }
            if (this.currentId) {
              updateSubsidyApplyToApi(this.currentId, params)
                .then((response) => {
                  ElMessage.success('编辑成功')
                  this.showSceneDialog = false
                  this.currentPage = 1
                  this.getTableList()
                })
                .catch((error) => {
                  console.log('error')
                })
            } else {
              saveSubsidyApplyToApi(params)
                .then((response) => {
                  if (response.code === 200) {
                    ElMessage.success('保存成功')
                    this.showSceneDialog = false
                    this.currentPage = 1
                    this.getTableList()
                  }
                })
                .catch(() => {})
            }
          }
        })
      },
    },
  }
</script>

<style scoped lang="scss">
  .components-container {
    margin: 20px;
    position: relative;
    background: #fff;
    padding: 20px;
    border-radius: 5px;
  }
  .subsidyTable :deep(.cell) {
    white-space: pre-wrap;
    display: inline-block !important;
  }
  .addInputValue {
    padding: 4px 0;
  }
  .newaddbutton {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  .top_left {
    width: 360px;
    border: #000;
    background: #f2f2f2;
    display: flex;
    align-items: center;
    border: 1px solid #dbdbdb;
    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;
      border-radius: 0;
      cursor: default;
      .el-input__inner {
        cursor: default !important;
        height: 28px;
        width: 100%;
      }
    }
    :deep(.el-textarea__inner) {
      box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;
      resize: none;
      border-radius: 0;
      cursor: default;
    }
    :deep(.el-select__wrapper) {
      box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;
      border-radius: 0;
    }
  }
  .lable {
    width: 140px;
    text-align: center;
  }
</style>
