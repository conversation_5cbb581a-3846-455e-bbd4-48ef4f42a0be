<!--
 * @Author: llm
 * @Date: 2024-02-28 15:55:17
 * @LastEditors: llm
 * @LastEditTime: 2025-07-16 17:50:28
 * @Description:
-->
<template>
  <div v-loading="state.boxLoading" element-loading-text="加载中...">
    <div :class="state.isShowTable ? 'flexContent showTableHight' : 'flexContent hiddenTableHight'">
      <!-- 左侧查询盒子 -->
      <div :class="state.isShowBox ? 'leftForm' : 'leftForm1'">
        <el-form :model="formData" :rules="rules" label-width="100px" ref="ruleFormRef" :inline="true">
          <div>
            <el-form-item label="选择位置方式">
              <el-radio-group v-model="locationType" @change="selectLocationType">
                <el-radio value="1">位置选点</el-radio>
                <el-radio value="2">下拉选点</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <el-form-item label="起点位置" prop="fromCity" class="w-300px" v-if="locationType === '1'">
            <el-input readonly v-model="formData.fromCity" placeholder="起点位置" class="w-300px" @click="chooseAddress('start')" />
          </el-form-item>
          <el-form-item label="起点位置" prop="fromCityCode" class="w-300px" v-else>
            <el-cascader class="w-300px" v-model="formData.fromCityCode" :options="citySelectList" :props="props" @change="handleCityChange($event, 1)" />
          </el-form-item>

          <el-form-item label="终点位置" prop="toCity" class="w-300px" v-if="locationType === '1'">
            <el-input readonly v-model="formData.toCity" placeholder="终点位置" class="w-300px" v-if="locationType === '1'" @click="chooseAddress('end')" />
          </el-form-item>
          <el-form-item label="终点位置" prop="toCityCode" class="w-300px" v-else>
            <el-cascader v-model="formData.toCityCode" :options="citySelectList" :props="props" @change="handleCityChange($event, 2)" />
          </el-form-item>
          <el-form-item label="油/气单价" prop="oilPricePerLiter" class="w-300px">
            <el-input v-model="formData.oilPricePerLiter" placeholder="油/汽单价">
              <template #append>
                <el-select v-model="formData.powerType" placeholder="选择单位" style="width: 80px">
                  <el-option label="元/升" value="1" />
                  <el-option label="元/m³" value="2" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="品牌" prop="truckBrandName" class="w-300px">
            <el-select v-model="formData.truckBrandName" label="品牌" filterable placeholder="请选择品牌">
              <el-option :label="item.name" :value="item.name" v-for="(item, index) in brandList" :key="index" />
            </el-select>
          </el-form-item>
          <el-form-item label="轴数" prop="vehicleAxes" class="w-300px">
            <el-select v-model="formData.vehicleAxes" label="轴数" filterable placeholder="请选择轴数">
              <el-option :label="item.name" :value="item.name" v-for="(item, index) in axisList" :key="index" />
            </el-select>
          </el-form-item>
          <div class="flex justify-center items-center">
            <el-button style="width: 28%" @click="resetForm(ruleFormRef)">重置</el-button>
            <el-button type="primary" style="width: 50%" @click="submitForm(ruleFormRef)">查询</el-button>
          </div>
        </el-form>
        <div v-if="state.isShowBox" class="tipTagContraction" @click="changeLeft">
          <el-icon>
            <ArrowLeft />
          </el-icon>
        </div>
        <div v-if="!state.isShowBox" class="tipTagContraction" @click="changeLeft">
          <el-icon>
            <ArrowRight />
          </el-icon>
        </div>
      </div>
      <!-- 地图 -->
      <div id="containerMap"></div>
      <!-- 其他服务信息 -->
      <div v-if="showPointButton" class="slectionOpen">
        <!-- 服务区 -->
        <div v-if="JSON.stringify(newTrajectoryData) != '{}'">
          <el-tag size="default" :effect="isEffect == 0 ? 'dark' : 'plain'" @click="showExtInfo(newTrajectoryData.extInfo.serviceAreas, 'serviceAreas')">
            <div class="textContent">
              <span>服务区</span>
              <span v-if="newTrajectoryData.extInfo.serviceAreas">{{ newTrajectoryData.extInfo.serviceAreas.length }}</span>
              <span v-else>0</span>
            </div>
          </el-tag>
        </div>
        <!-- 收费站 -->
        <div v-if="JSON.stringify(newTrajectoryData) != '{}'">
          <el-tag size="default" :effect="isEffect == 1 ? 'dark' : 'plain'" @click="showExtInfo(newTrajectoryData.extInfo.feeStations, 'feeStations')">
            <div class="textContent">
              <span>收费站</span>
              <span v-if="newTrajectoryData.extInfo.feeStations">{{ newTrajectoryData.extInfo.feeStations.length }}</span>
              <span v-else>0</span>
            </div>
          </el-tag>
        </div>
        <!-- 加油站 -->
        <div v-if="JSON.stringify(newTrajectoryData) != '{}'">
          <el-tag size="default" :effect="isEffect == 2 ? 'dark' : 'plain'" @click="showExtInfo(newTrajectoryData.extInfo.gasStations, 'gasStations')">
            <div class="textContent">
              <span>加油站</span>
              <span v-if="newTrajectoryData.extInfo.gasStations">{{ newTrajectoryData.extInfo.gasStations.length }}</span>
              <span v-else>0</span>
            </div>
          </el-tag>
        </div>
        <!-- 四限 -->
        <div v-if="restrictsInfoList.length > 0">
          <el-tag size="default" v-for="(item, index) in restrictsInfoList" :key="index" :effect="item.effectType" @click="showExtInfo(item, '')">
            <div class="textContent">
              <span>{{ item.tagName }}</span>
              <span>{{ item.count }}</span>
            </div>
          </el-tag>
        </div>
        <!-- 国省高县 -->
        <div v-if="roadInfoList.length > 0">
          <el-tag size="default" v-for="(item, index) in roadInfoList" :key="index" :effect="item.effectType" @click="showExtInfo(item, '')">
            <div class="textContent">
              <span>{{ item.typeName }}</span>
              <span>{{ item.distanceStr }}</span>
            </div>
          </el-tag>
        </div>
      </div>
    </div>
    <!-- 底部 -->
    <div :class="state.isShowTable ? 'bottomTable' : 'bottomTable1'">
      <div class="m-[4px_0] pl-4px">
        <el-button size="small" @click="routeContrast" :type="routeSelectionData.length < 2 ? 'info' : 'primary'" :disabled="routeSelectionData.length < 2"
          >路线对比</el-button
        >
        <el-button size="small" :type="isRouteView ? 'info' : 'primary'" :disabled="isRouteView" @click="cancelContrast">取消对比</el-button>
      </div>
      <el-table
        ref="tableRef"
        :highlight-current-row="true"
        :data="tableData"
        style="width: 100%"
        :max-height="state.tableMaxHeight"
        @selection-change="routeSelectionChange"
        size="small"
      >
        <el-table-column type="selection" align="center" width="60"></el-table-column>
        <el-table-column type="index" label="序号" width="80" align="center" />
        <el-table-column label="线路信息" align="center">
          <el-table-column prop="totalDistanceStr" label="总里程(km)" align="center" width="130" sortable />
          <el-table-column prop="tollTotalDistanceStr" label="高速里程(km)" align="center" width="130" sortable />
          <el-table-column prop="totalDurationStr" label="总时间(h)" align="center" width="120" sortable />
          <el-table-column prop="driveTimeStr" label="驾驶时间(h)" align="center" width="130" sortable />
        </el-table-column>
        <el-table-column label="成本明细(元/公里)" align="center">
          <el-table-column prop="oilFeesCostPerKilometer" label="油/气费" align="center" />
          <el-table-column prop="highwayCostPerKilometer" label="高速费" align="center" />
          <el-table-column prop="rjFeesCostPerKilometer" label="人件费" align="center" />
          <el-table-column prop="zjFeesCostPerKilometer" label="车辆折旧" align="center" />
          <el-table-column prop="bxFeesCostPerKilometer" label="车辆保险" align="center" />
          <el-table-column prop="ltFeesCostPerKilometer" label="轮胎" align="center" />
          <el-table-column prop="wbFeesCostPerKilometer" label="维修保养" align="center" />
          <el-table-column prop="nsFeesCostPerKilometer" label="尿素" align="center" />
          <el-table-column prop="zxFeesCostPerKilometer" label="杂费" align="center" />
          <el-table-column prop="totalCostPerKilometer" label="每公里成本" align="center" sortable />
          <el-table-column prop="totalCost" label="总成本" align="center" sortable />
        </el-table-column>
        <el-table-column align="center" fixed="right" label="操作" width="140">
          <template #default="{ row, $index }">
            <el-button v-if="isRouteView" size="small" type="text" @click="showRoute(row)">查看路线</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="state.isShowTable" class="topBottomContraction" @click="changeUpDown">
        <el-icon>
          <ArrowDown />
        </el-icon>
      </div>
      <div v-if="!state.isShowTable" class="topBottomContraction" @click="changeUpDown">
        <el-icon>
          <ArrowUp />
        </el-icon>
      </div>
    </div>
    <!-- 地图位置搜索 -->
    <map-search-dialog
      v-if="searchDialog.visible"
      :dialog="searchDialog"
      :pointData="currentChooseAddress"
      @closeMapDialog="closeMapDialog"
      @submitLocation="submitLocation"
    ></map-search-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { IAIQuote } from '@/api/AIQuote/types'
  import { AIOfferSingleApi, axisListApi, brandListApi, citySelectApi, oilPriceApi } from '@/api/AIQuote'
  import { ElNotification, FormInstance, FormRules } from 'element-plus'
  import MapSearchDialog from '@/components/MapSearchDialog/index.vue'
  declare const AMap: any
  const formData = reactive<IAIQuote>({
    powerType: '1',
    fromAddress: '',
    fromCity: '',
    fromCityCode: '',
    fromCoordinate: '',
    oilPricePerLiter: 8,
    returnLine: 0,
    toAddress: '',
    toCity: '',
    toCityCode: '',
    toCoordinate: '',
    truckBrandId: '',
    truckBrandName: '',
    vehicleAxes: '',
  })
  const state = reactive({
    boxLoading: false, //查询轨迹loading
    isShowBox: true, //控制显示隐藏
    isShowTable: true, //控制table显示隐藏
    isShowHide: true, //控制右侧信息显示隐藏
    tableMaxHeight: '0px', //表格最大高度
    coordinate: [] as any[], //坐标
  })
  const tableRef = ref()
  const tableData = ref()
  const props = {
    expandTrigger: 'hover' as const,
  }
  const ruleFormRef = ref<FormInstance>()
  const axisList = ref()
  const brandList = ref()
  const citySelectList = ref()
  const rules = reactive<FormRules<IAIQuote>>({
    fromCityCode: [{ required: true, message: '请输入起点位置', trigger: 'blur' }],
    toCityCode: [{ required: true, message: '请输入终点位置', trigger: 'blur' }],
    fromCity: [{ required: true, message: '请输入起点位置', trigger: 'change' }],
    toCity: [{ required: true, message: '请输入终点位置', trigger: 'change' }],
    oilPricePerLiter: [{ required: true, message: '请输入油/汽单价', trigger: 'blur' }],
    truckBrandName: [{ required: true, message: '请选择品牌', trigger: 'change' }],
    vehicleAxes: [{ required: true, message: '请选择轴数', trigger: 'change' }],
  })
  const map = ref()
  const center = ref([116.397428, 39.90923])
  const locationType = ref('1')
  const currentChooseAddressType = ref('1') // 1起点 2终点
  const currentChooseAddress = ref({
    address: '',
    coordinate: '',
  })
  function selectLocationType(e: any) {
    locationType.value = e
    //清除展示信息
    currentChooseAddress.value.address = ''
    currentChooseAddress.value.coordinate = ''
    formData.fromCity = ''
    formData.fromAddress = ''
    formData.fromCityCode = ''
    formData.fromCoordinate = ''
    formData.toCity = ''
    formData.toAddress = ''
    formData.toCityCode = ''
    formData.toCoordinate = ''
  }

  // 改变左侧筛选的显示隐藏
  const changeLeft = async () => {
    state.isShowBox = !state.isShowBox
  }

  // 改变上下charts的显示隐藏
  const changeUpDown = async () => {
    state.isShowTable = !state.isShowTable
  }
  /**
   * 二级弹窗
   */
  const searchDialog = reactive<DialogOption>({
    visible: false,
  })
  // 关闭子弹窗
  function closeMapDialog() {
    searchDialog.visible = false //关闭弹窗
  }
  function chooseAddress(type: string) {
    if (type !== currentChooseAddressType.value) {
      currentChooseAddress.value.address = ''
      currentChooseAddress.value.coordinate = ''
    }

    currentChooseAddressType.value = type
    searchDialog.visible = true
  }
  // 子弹窗确认
  const submitLocation = (data: any) => {
    if (data) {
      currentChooseAddress.value.address = data.address
      currentChooseAddress.value.coordinate = data.coordinate
      if (currentChooseAddressType.value === 'start') {
        formData.fromCity = data.address
        formData.fromCoordinate = data.coordinate
        //替换之前选择的marker
        if (innerMapMarkerList.value.length > 0) {
          map.value.remove(innerMapMarkerList.value[0])
        }
        addMarker([formData.fromCoordinate.split(',')[0], formData.fromCoordinate.split(',')[1]], 'start', 0)
        getOilPriceData(data.coordinate)
      } else if (currentChooseAddressType.value === 'end') {
        formData.toCity = data.address
        formData.toCoordinate = data.coordinate
        if (innerMapMarkerList.value.length > 1) {
          map.value.remove(innerMapMarkerList.value[1])
        }
        addMarker([formData.toCoordinate.split(',')[0], formData.toCoordinate.split(',')[1]], 'end', 1)
      }
      closeMapDialog()
    }
  }
  function initMap() {
    map.value = new AMap.Map('containerMap', {
      center: center.value,
      zoom: 12,
      plugin: ['AMap.MapType', 'AMap.PlaceSearch'],
    })
    // 缩放地图到合适的视野级别
    map.value.setFitView()
  }

  onMounted(async () => {
    initMap()
    state.tableMaxHeight = `${Math.max(window.innerHeight * 0.3 - 30, 0)}px`
    axisList.value = await getAxisList()
    brandList.value = await getBrandList()
    citySelectList.value = await getCitySelectList()
  })
  /**
   *
   * @param value code
   * @param type 1-起点 2-终点
   */
  const handleCityChange = (value: any, type: number) => {
    switch (type) {
      case 1:
        formData.fromCityCode = value[1]
        break
      case 2:
        formData.toCityCode = value[1]
        break
      default:
        break
    }
  }
  async function getAxisList() {
    const { data } = await axisListApi({})
    return data
  }
  async function getBrandList() {
    const { data } = await brandListApi({})
    return data
  }
  async function getCitySelectList() {
    const { data } = await citySelectApi({})
    return data
  }
  // 获取油价
  async function getOilPriceData(position: String) {
    let params = {
      startCoordinate: position,
      type: formData.powerType,
    }
    const { data } = await oilPriceApi(params)
    formData.oilPricePerLiter = data
  }

  // 点击查询
  function submitForm(formEl: FormInstance | undefined) {
    if (!formEl) return
    formEl.validate((valid, fields) => {
      if (valid) {
        state.boxLoading = true
        newTrajectoryData.value = {} //清除当前路线信息
        showPointButton.value = false //隐藏右侧信息栏
        clearMapShowInfo() //清除所有对比信息
        tableRef.value?.clearSort() // 清除排序状态
        AIOfferSingleApi(formData)
          .then((res) => {
            const { data } = res
            tableData.value = data
            state.boxLoading = false
          })
          .catch(() => {
            state.boxLoading = false
          })
      } else {
        console.log('error submit!', fields)
      }
    })
  }
  function resetForm(formEl: FormInstance | undefined) {
    if (!formEl) return
    formEl.resetFields()
  }
  const dingwei = reactive({
    qidian: 'http://api.haodaoda.com/static/wx/mini/dingwei_qidian.png',
    zhongdian: 'http://api.haodaoda.com/static/wx/mini/dingwei_zhongdian.png',
  })
  const innerMapMarkerList = ref<any[]>([])
  // marker 图标展示
  function addMarker(location: any[], type: string, index: number) {
    var innerMapMarker = new AMap.Marker({
      icon: new AMap.Icon({
        image: type == 'start' ? dingwei.qidian : type == 'end' ? dingwei.zhongdian : '',
        size: new AMap.Size(36, 48), // 图标所处区域大小
        imageSize: new AMap.Size(36, 48), // 图标大小
      }),
      isType: type,
      position: location,
      offset: new AMap.Pixel(-18, -36),
    })
    innerMapMarkerList.value[index] = innerMapMarker
    innerMapMarker.setMap(map.value)
    // 缩放地图到合适的视野级别
    map.value.setFitView()
    if (type == 'start') {
      const newLocation = location.join(',')
    }
  }
  const routeSelectionData = ref<any[]>([])
  // 路线多选
  function routeSelectionChange(data: any[]) {
    if (data && data.length > 0) {
      routeSelectionData.value = data
    } else {
      routeSelectionData.value = []
    }
  }
  const isRouteView = ref(true)
  const polyline = ref()
  const routeLayerList = ref<any[]>([])

  // 取消对比
  function cancelContrast() {
    newTrajectoryData.value = {} //清除当前路线信息
    showPointButton.value = false //隐藏右侧信息栏
    clearMapShowInfo() //清除所有对比信息
  }
  // 路线对比
  function routeContrast() {
    if (routeSelectionData.value.length > 3) {
      ElNotification({
        title: '提示',
        message: '路线对比功能，支持同时选择三条线路进行查看！您当前选择的线路数已超过上限！',
        type: 'error',
      })
      return
    } else {
      isRouteView.value = false
      if (polyline.value) {
        map.value.remove(polyline.value)
      }
      if (routeLayerList.value) {
        map.value.remove(routeLayerList.value)
        routeLayerList.value = []
      }

      routeSelectionData.value.forEach((item: { track: string }, index: any) => {
        if (item.track) {
          var pathList: string[][] = []
          var trackList = item.track.split(';')
          trackList.forEach((event: string) => {
            pathList.push(event.split(','))
          })
          //轨迹点
          var routeLayer = new AMap.Polyline({
            path: pathList,
            borderWeight: 2, // 线条宽度，默认为 1
            strokeColor: '#0066ff', // 线条颜色
            lineJoin: 'round', // 拐点连接处样式
            showDir: true, //是否显示箭头
            strokeOpacity: 0.9, //线透明度
            strokeWeight: 8, //线宽
            strokeStyle: 'solid', //线样式
          })
          // 轨迹点击事件
          routeLayer.on('click', (e: any) => {
            highlightRoute(item, index)
          })
          routeLayer.setMap(map.value)
          routeLayerList.value.push(routeLayer)
          map.value.setFitView(routeLayerList.value) //设置缩放大小
        }
      })
      // 默认第一条高亮
      highlightRoute(routeSelectionData.value[0], 0)
    }
  }
  const newTrajectoryData = ref()
  const showPointButton = ref(false)
  const newTrackList = ref([] as any[])
  const markerTypeList = ref([] as any[])
  const polygonList = ref([] as any[])
  const polylineList = ref([] as any[])
  const allTypeMarkerTip = ref()
  const roadInfoList = ref([] as any[])
  const restrictsInfoList = ref([] as any[])
  const isEffect = ref()

  // 清除展示信息
  function clearMapShowInfo() {
    // 清除覆盖的路线
    if (polylineList.value.length > 0) {
      map.value.remove(polylineList.value)
      polylineList.value = []
    }
    // 清除限行区域数据
    if (polygonList.value) {
      map.value.remove(polygonList.value)
      polygonList.value = [] // 清空
    }
    // 清除所有的marker标记
    if (markerTypeList.value) {
      map.value.remove(markerTypeList.value)
      markerTypeList.value = [] // 清空
    }
    // 清除高速国省高覆盖轨迹
    if (newTrackList.value) {
      map.value.remove(newTrackList.value)
      newTrackList.value = [] // 清空
    }
    // 清除轨迹
    if (polyline.value) {
      map.value.remove(polyline.value)
    }
    // 判断是否有未关闭的信息窗口
    if (allTypeMarkerTip.value) {
      allTypeMarkerTip.value.close() // 关闭提示
    }
    // 清除起终点marker
    if (innerMapMarkerList.value.length > 0) {
      map.value.remove(innerMapMarkerList.value)
    }
    // 路线对比
    isRouteView.value = true //初始化按钮状态
    routeSelectionData.value = [] //清除选择的数据
    //清除对比线路
    if (routeLayerList.value) {
      map.value.remove(routeLayerList.value)
      routeLayerList.value = []
    }
    if (tableRef.value) {
      // 默认选中高亮清除
      tableRef.value.setCurrentRow()
      // 清空所有选中
      tableRef.value.clearSelection()
    }
  }

  // 高亮路线
  function highlightRoute(row: { track?: string; extInfo?: any }, index: number) {
    if (routeLayerList.value.length > 0) {
      // 赋值当前路线数据
      newTrajectoryData.value = row
      // 选中的路线当前行高亮
      tableRef.value.setCurrentRow(row)
      routeLayerList.value.map((item: { setOptions: (arg0: { strokeColor: string; zIndex: number }) => void }, _index: number) => {
        if (_index === index) {
          // 高亮当前路线
          item.setOptions({ strokeColor: '0066ff', zIndex: 10 }) // 或者其他高亮样式
          showPointButton.value = true
          // highlightRow(index)
          // 清除所有的marker标记
          if (markerTypeList.value) {
            map.value.remove(markerTypeList.value)
            markerTypeList.value = [] // 清空
          }
          // 对应路段的信息点位
          if (newTrackList.value) {
            map.value.remove(newTrackList.value)
            newTrackList.value = [] // 清空
          }
          // 清除限行区域数据
          if (polygonList.value) {
            map.value.remove(polygonList.value)
            polygonList.value = [] // 清空
          }
          // 清除高速国省高覆盖轨迹
          if (polylineList.value) {
            map.value.remove(polylineList.value)
            polylineList.value = [] // 清空
          }
          // 判断是否有未关闭的信息窗口
          if (allTypeMarkerTip.value) {
            allTypeMarkerTip.value.close() // 关闭提示
          }
          roadInfoList.value = []
          restrictsInfoList.value = []
          isEffect.value = null
          // 高速省道下道其他道路信息
          if (row.extInfo && row.extInfo.roadInfos.length > 0) {
            roadInfoList.value = row.extInfo.roadInfos.map((item: any) => {
              return { ...item, effectType: 'plain' }
            })
          }
          if (row.extInfo && row.extInfo.restricts.length > 0) {
            // 四限
            restrictsInfoList.value = row.extInfo.restricts.map((item: any) => {
              return { ...item, effectType: 'plain' }
            })
          }
        } else {
          // 设置其他路线为灰色
          item.setOptions({ strokeColor: 'gray', zIndex: 5 }) // 或者其他灰色样式
        }
      })
    }
  }

  // 查看路线 - 展示轨迹
  function showRoute(newData: any) {
    // console.log('newData', newData);
    // 路线
    newTrajectoryData.value = newData
    // 清除所有展示信息
    clearMapShowInfo()
    state.coordinate = []
    if (newData.track) {
      var trackList = newData.track.split(';')
      trackList.map((item: any) => {
        state.coordinate.push(item.split(','))
      })
      addMarker([trackList[0].split(',')[0], trackList[0].split(',')[1]], 'start', 0)
      addMarker([trackList[trackList.length - 1].split(',')[0], trackList[trackList.length - 1].split(',')[1]], 'end', 1)
      // 轨迹点
      polyline.value = new AMap.Polyline({
        path: state.coordinate,
        borderWeight: 2, // 线条宽度，默认为 1
        strokeColor: '#0066ff', // 线条颜色
        lineJoin: 'round', // 拐点连接处样式
        showDir: true, // 是否显示箭头
        strokeOpacity: 0.9, // 线透明度
        strokeWeight: 8, // 线宽
        strokeStyle: 'solid', // 线样式
      })
      // 将路线添加至地图实例
      map.value.add(polyline.value)
      map.value.setFitView() // 缩放合适比例
    }
    // 展示国省高四限等信息
    showPointButton.value = true
    // 处理信息
    roadInfoList.value = []
    restrictsInfoList.value = []
    isEffect.value = null
    // 高速省道下道其他道路信息
    if (newData.extInfo && newData.extInfo.roadInfos) {
      roadInfoList.value = newData.extInfo.roadInfos.map((item: any) => {
        return { ...item, effectType: 'plain' }
      })
    }
    if (newData.extInfo && newData.extInfo.restricts) {
      // 四限
      restrictsInfoList.value = newData.extInfo.restricts.map((item: any) => {
        return { ...item, effectType: 'plain' }
      })
    }
  }

  // 查看更多信息 服务区 加油站 收费站 等
  function showExtInfo(data: any, type: string) {
    // 判断是否有未关闭的信息窗口
    if (allTypeMarkerTip.value) {
      allTypeMarkerTip.value.close() // 关闭提示
    }
    // 清除高速国省高覆盖轨迹
    if (polylineList.value) {
      map.value.remove(polylineList.value)
      polylineList.value = [] // 清空
    }
    if (markerTypeList.value) {
      map.value.remove(markerTypeList.value) // 清除所有的marker标记
      markerTypeList.value = [] // 清空
    }
    if (markerTypeList.value) {
      map.value.remove(polygonList.value) // 清除限行区域数据
      polygonList.value = [] // 清空
    }
    switch (type) {
      case 'serviceAreas': //服务区
        isEffect.value = 0
        break
      case 'feeStations': //收费站
        isEffect.value = 1
        break
      case 'gasStations': //加油站
        isEffect.value = 2
        break
    }
    // 加油站 服务区 收费站
    if (type) {
      if (data && data.length > 0) {
        data.map((item: any) => {
          markerTypeTip(item, type)
        })
      }
    }
    // 点击四限数据
    if (restrictsInfoList.value.length > 0) {
      restrictsInfoList.value.map((event: any) => {
        if (data && event.tagName == data.tagName) {
          event.effectType = 'dark'
          roadInfoList.value.map((item: any) => item.effectType == 'plain')
          isEffect.value = null
        } else {
          event.effectType = 'plain'
        }
      })
      if (data && data.restricts && data.restricts.length > 0) {
        data.restricts.map((item: any) => {
          markerTypeTip(item, data.tagName)
        })
      }
    }
    // 点击高速国省道等
    if (roadInfoList.value.length > 0) {
      roadInfoList.value.map((event: any) => {
        if (data && event.typeName == data.typeName) {
          event.effectType = 'dark'
          roadInfoList.value.map((item: any) => item.effectType == 'plain')
          isEffect.value = null
          showTypeRoute(event)
        } else {
          event.effectType = 'plain'
        }
      })
    }
  }

  // 查看对应的路段
  function showTypeRoute(row: any) {
    var rowPolyline = null
    newTrackList.value = []
    if (row.tracks && row.tracks.length > 0) {
      row.tracks.map((item: any) => {
        let newList = item.split(';')
        let trackRow = newList.map((rowItem: any) => {
          return rowItem.split(',')
        })
        newTrackList.value.push(trackRow)
      })
      if (newTrackList.value.length > 0) {
        newTrackList.value.map((item: any) => {
          //轨迹点
          rowPolyline = new AMap.Polyline({
            path: item,
            borderWeight: 2, // 线条宽度，默认为 1
            strokeColor: '#FF00FF', // 线条颜色
            lineJoin: 'round', // 拐点连接处样式
            showDir: true, //是否显示箭头
            strokeOpacity: 0.9, //线透明度
            strokeWeight: 8, //线宽
            strokeStyle: 'solid', //线样式
          })
          polylineList.value.push(rowPolyline) //保存每一个生成的线
          // 将路线添加至地图实例
          map.value.add(rowPolyline)
          map.value.setFitView(polylineList.value) //缩放合适比例
        })
      }
    }
  }

  // 各类marker展示
  const markerTypeTip = async (rowData: any, type: string) => {
    let icon = '' as any
    let newPosition = []
    switch (type) {
      case 'serviceAreas': //服务区
        icon = new URL('@/assets/images/fuwuqu.png', import.meta.url).href
        newPosition = rowData.location.split(',')
        break
      case 'feeStations': //收费站
        icon = new URL('@/assets/images/biao_shoufeizhan.png', import.meta.url).href
        newPosition = rowData.location.split(',')
        break
      case 'gasStations': //加油站
        icon = new URL('@/assets/images/biao_jiayouzhan.png', import.meta.url).href
        newPosition = rowData.coordinate.split(',')
        break
      case '限高': //限高
        icon = new URL('@/assets/images/common_ic_xiangao.png', import.meta.url).href
        newPosition = rowData.location.split(',')
        break
      case '限宽': //限宽
        icon = new URL('@/assets/images/common_ic_xiankuan.png', import.meta.url).href
        newPosition = rowData.location.split(',')
        break
      case '限行': //限行
        icon = new URL('@/assets/images/common_ic_xianxing.png', import.meta.url).href
        newPosition = rowData.location.split(',')
        break
      case '电子眼': //电子眼
        icon = new URL('@/assets/images/common_ic_dianziyan.png', import.meta.url).href
        newPosition = rowData.location.split(',')
        break
    }
    let typeMarker = new AMap.Marker({
      position: newPosition,
      icon: new AMap.Icon({
        image: icon, // 添加 Icon 图标 URL
        size: new AMap.Size(22, 28), // 图标尺寸
        imageSize: new AMap.Size(22, 28), // 根据所设置的大小拉伸或压缩图片
      }),
      achor: 'center',
      offset: new AMap.Pixel(-11, -28),
    })
    markerTypeList.value.push(typeMarker)
    typeMarker.on('click', (e: any) => {
      // 添加点击事件
      addMarkerTip(rowData, type)
    })
    if (type == '限高' || type == '限宽' || type == '限行' || type == '电子眼') {
      if (rowData.quyu) {
        var path = [] as any[]
        var newQuyu = rowData.quyu.split(';')
        newQuyu.forEach((event: any) => {
          path.push(event.split(','))
        })
        var polygon = new AMap.Polygon({
          path: path,
          strokeWeight: 4,
          strokeColor: '#FF33FF',
          strokeStyle: 'slide',
          strokeOpacity: 0.4,
          fillOpacity: 0.3,
          fillColor: '#FF0000',
          zIndex: 50,
          bubble: true,
        })
        polygonList.value.push(polygon)
        // 地图上添加矢量多边形
        map.value.add(polygonList.value)
      }
    }
    map.value.add(markerTypeList.value)
    map.value.setFitView() //缩放合适比例
  }

  // 异常点信息
  function addMarkerTip(item: any, type: string) {
    // item 为当前点击的内容
    if (allTypeMarkerTip.value) {
      allTypeMarkerTip.value.close() // 关闭提示
    }
    let content = []
    let newLngLat = []
    let title = ''
    switch (type) {
      case 'serviceAreas': //服务区
        title = '服务区信息'
        content.push('<span class="title">服务区名称：</span>' + (item.name ? item.name : ' - '))
        content.push('<span class="title">预计里程：</span>' + (item.distanceStr ? "<span class='contentInfo'>" + item.distanceStr + '</span>' : ' - '))
        content.push('<span class="title">预计行驶：</span>' + (item.timeStr ? item.timeStr : ' - '))
        newLngLat = item.location.split(',')
        break
      case 'feeStations': //收费站
        title = '收费站信息'
        content.push('<span class="title">收费站名称：</span>' + (item.name ? item.name : ' - '))
        content.push('<span class="title">预计里程：</span>' + (item.distanceStr ? "<span class='contentInfo'>" + item.distanceStr + '</span>' : ' - '))
        content.push('<span class="title">预计行驶：</span>' + (item.timeStr ? item.timeStr : ' - '))
        newLngLat = item.location.split(',')
        break
      case 'gasStations': //加油站
        title = '加油站信息'
        content.push('<span class="title">加油站名称：</span>' + (item.gasName ? item.gasName : ' - '))
        content.push('<span class="title">加油站位置：</span>' + (item.gasAddress ? item.gasAddress : ' - '))
        content.push(
          '<span class="title">' + item.oilNo + '：</span>' + "<span class='contentInfo'>" + (item.priceGun ? item.priceGun + '元/升' : ' - ') + '</span>',
        )
        newLngLat = item.coordinate.split(',')
        break
      case '限高': //限高
        title = '限高信息'
        // content.push('<span class="title">名称：</span>' + (item.name ? item.name : ' - '))
        content.push('<span class="title">时间：</span>' + (item.limitTime ? item.limitTime : ' - '))
        content.push('<span class="title">类型：</span>' + "<span class='contentInfo'>" + (item.typeName ? item.typeName : ' - ') + '</span>')
        newLngLat = item.location.split(',')
        break
      case '限宽': //限宽
        title = '限宽信息'
        // content.push('<span class="title">名称：</span>' + (item.name ? item.name : ' - '))
        content.push('<span class="title">时间：</span>' + (item.limitTime ? item.limitTime : ' - '))
        content.push('<span class="title">类型：</span>' + "<span class='contentInfo'>" + (item.typeName ? item.typeName : ' - ') + '</span>')
        newLngLat = item.location.split(',')
        break
      case '限行': //限行
        title = '限行信息'
        // content.push('<span class="title">名称：</span>' + (item.name ? item.name : ' - '))
        content.push('<span class="title">时间：</span>' + (item.limitTime ? item.limitTime : ' - '))
        content.push('<span class="title">类型：</span>' + "<span class='contentInfo'>" + (item.typeName ? item.typeName : ' - ') + '</span>')
        newLngLat = item.location.split(',')
        break
      case '电子眼': //电子眼
        title = '电子眼信息'
        content.push('<span class="title">名称：</span>' + (item.name ? item.name : ' - '))
        content.push('<span class="title">时间：</span>' + (item.limitTime ? item.limitTime : ' - '))
        content.push('<span class="title">类型：</span>' + "<span class='contentInfo'>" + (item.typeName ? item.typeName : ' - ') + '</span>')
        newLngLat = item.location.split(',')
        break
    }

    allTypeMarkerTip.value = new AMap.InfoWindow({
      // 提示插件
      position: new AMap.LngLat(newLngLat[0], newLngLat[1]), // 经纬度
      content: createInfoWindow(title, content.join('<br/>')),
      offset: new AMap.Pixel(0, -28),
    })
    allTypeMarkerTip.value.open(map.value)
  }
  // 构建自定义信息窗体
  function createInfoWindow(title: string, content: any) {
    var info = document.createElement('div')
    info.className = 'custom-info input-card content-window-card'
    // 可以通过下面的方式修改自定义窗体的宽高
    // 定义顶部标题
    var top = document.createElement('div')
    var titleD = document.createElement('div')
    top.className = 'info-top'
    titleD.innerHTML = title
    top.appendChild(titleD)
    info.appendChild(top)

    // 定义中部内容
    var middle = document.createElement('div')
    middle.className = 'info-middle'
    middle.style.backgroundColor = 'white'
    middle.innerHTML = content
    info.appendChild(middle)
    return info
  }
</script>
<style scoped>
  .flexContent {
    position: relative;
    display: flex;
    overflow: hidden;
  }

  .showTableHight {
    height: calc(100vh - 30vh - 84px);
  }

  .hiddenTableHight {
    height: calc(100vh - 88px);
  }

  .flexContent .leftForm {
    position: relative;
    width: 351px;
    height: 100%;
    padding: 10px;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-bottom: none;
    border-radius: 2px;
  }

  .flexContent .leftForm1 {
    position: relative;
    width: 0;
    background: #fff;
  }

  .showTableHight #containerMap {
    position: relative;
    flex: 1;
    height: calc(100vh - 30vh - 84px);
    padding: 0;
    margin: 0;
    overflow: hidden;
  }

  .hiddenTableHight #containerMap {
    position: relative;
    flex: 1;
    height: calc(100vh - 84px);
    padding: 0;
    margin: 0;
    overflow: hidden;
  }

  .flexContent .leftForm1 ::v-deep(.el-input-number__decrease),
  .flexContent .leftForm1 ::v-deep(.el-input-number__increase) {
    z-index: 0;
  }

  ::v-deep(.BMap_bubble_pop) {
    padding: 0 !important;
    border: none !important;
    border-radius: 0 !important;
  }

  ::v-deep(.BMap_bubble_title) {
    padding-left: 10px;
    font-size: 14px;
    font-weight: bold;
    color: white !important;
    text-align: left;
    background-color: #409eff;
    border-bottom: 1px solid rgb(191 191 192);
  }

  ::v-deep(.BMap_bubble_content) {
    top: 0 !important;
    width: 100% !important;
    padding: 0 10px;
  }

  ::v-deep(.BMap_bubble_content) .title {
    font-weight: 500;
    color: #409eff;
  }

  /* tip关闭按钮 */
  ::v-deep(.BMap_bubble_buttons) {
    top: 0 !important;
    height: 28px !important;
  }

  ::v-deep(.BMap_bubble_buttons) div {
    font-size: 24px !important;
    line-height: 22px !important;
    color: #fff;
  }

  /* 行驶信息展示 */
  .statisticsInfo {
    position: absolute;
    top: 10px;
    right: 20px;
    z-index: 9;
    width: 450px;
    height: auto;
    padding: 0 16px 16px;
    background: #f9f9f9;
    border: 1px solid #999;
    border-radius: 10px;
  }

  .statisticsInfo .topInfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
  }

  .statisticsInfo .topInfo .title {
    padding: 0 4px;
    font-size: 16px;
    font-weight: bold;
  }

  .statisticsInfo ::v-deep(.el-row) {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
  }

  .statisticsInfo .colItem {
    padding: 3px 0 3px 10px;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
  }

  .statisticsInfo .lableText {
    color: #0081ff;
  }

  .bottomTable {
    position: relative;
    width: 100%;
    height: 30vh;
    background: #fff;
    border: 1px solid #e4e7ed;
  }

  .bottomTable1 {
    position: relative;
    width: 100%;
    height: 0;
    padding: 0;
  }

  ::v-deep(.amap-info-content) {
    padding: 0;
  }

  ::v-deep(.info-top) {
    background: rgb(63, 145, 107);
    padding: 4px 20px;
    font-size: 14px;
    color: #fff;
    font-weight: 600;
  }

  ::v-deep(.amap-info-close) {
    position: absolute;
    right: 0px;
    top: 0px;
    color: #fff;
    text-decoration: none;
    font:
      700 16px/14px Tahoma,
      Verdana,
      sans-serif;
    width: 32px;
    height: 26px;
    line-height: 26px;
    cursor: pointer;
    font-size: 20px;
    background: rgb(63, 145, 107);
    text-align: center;
  }

  ::v-deep(.info-middle) {
    padding: 10px 20px;
    font-size: 14px;
    width: 260px;
  }

  ::v-deep(.info-middle) .title {
    color: #228b22;
    font-weight: 500;
    float: left;
  }

  ::v-deep(.info-middle) .contentInfo {
    display: inline-block;
    max-width: 70%;
  }

  /* 右侧国省高四限等信息展示 */
  .slectionOpen {
    position: absolute;
    top: 2vh;
    right: 10px;
    border-radius: 4px;
    z-index: 0;
  }

  .slectionOpen .textContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    line-height: 18px;
    padding: 4px 0;
  }

  .slectionOpen ::v-deep(.el-tag--default) {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    height: auto;
    margin-bottom: 4px;
  }

  .slectionOpen ::v-deep(.el-tag--plain) {
    color: #ff5511;
    border-color: red;
  }

  .slectionOpen ::v-deep(.el-tag--dark) {
    color: #fff;
  }
  ::v-deep(.el-form--inline .el-form-item .el-input) {
    width: 100% !important;
  }
</style>
