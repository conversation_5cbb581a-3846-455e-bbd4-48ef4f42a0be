<template>
  <!-- 批量算路 -->
  <div class="multiline">
    <el-button type="primary" @click="downloadTemplate" :loading="butnLoading" v-no-more-click>下载导入模板</el-button>
    <upload-excel-component
      style="margin-right: 10px"
      :loading="importLoading"
      @importClick="handleImport"
      @importExcelFile="importExcelFile"
      title="模板导入"
    />
    <el-button type="primary" @click="refresh">刷新列表</el-button>
    <div ref="topHeight" class="pagination-container">
      <el-table v-loading="tableLoading" :data="tableData" border fit size="medium" highlight-current-row :max-height="tableHeight" style="width: 100%">
        <el-table-column label="序号" type="index" width="60" align="center">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="业务名称" prop="name" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="业务线条数" prop="totalDetails" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="创建时间" prop="createTime" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="算路状态" prop="state" align="center" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span v-if="scope.row.state == 'COMPUTED'">已完成</span>
            <span v-else-if="scope.row.state == 'WAITING'">未算路</span>
            <span v-else-if="scope.row.state == 'COMPUTING'" style="color: red">算路中...</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-button v-if="scope.row.state == 'COMPUTED'" @click="downloadData(scope.row)" type="text" size="small">下载</el-button>
            <el-button v-if="scope.row.state == 'WAITING'" @click="batchRouting(scope.row)" type="text" size="small">批量算路</el-button>
            <el-button v-if="scope.row.state != 'COMPUTING'" @click="deleteForm(scope.row)" type="text" size="small">删除</el-button>
            <el-button v-if="scope.row.state == 'COMPUTED'" @click="viewDetails(scope.row)" type="text" size="small">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <Pagination :total="total" :page="page" :limit="limit" @pagination="pagination"></Pagination>
    </div>
    <!-- 业务线确认及修改业务线弹窗 -->
    <el-dialog
      class="templateListDialog"
      :visible.sync="showTable"
      title="业务线确认"
      :close-on-click-modal="false"
      v-dialogDrag
      :before-close="businessDialogClose"
      top="10vh"
      width="70%"
    >
      <div style="color: orange; padding: 6px 0">* 状态为异常的业务线、请修改异常项或者删除异常项、油价合理范围(3 - 15)元/升、 再进行批量算路</div>
      <!--table表格开始-->
      <el-table v-loading="businessLoading" :data="tableListData" border size="mini" highlight-current-row :max-height="importTableHeight" style="width: 100%">
        <el-table-column label="序号" fixed="left" type="index" width="60" align="center">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="fromAddress" label="出发地点" width="180" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.isValidFrom">{{ scope.row.fromAddress }}</span>
            <span v-else style="color: red">{{ scope.row.fromAddress }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="toAddress" label="到达地点" width="180" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.isValidTo">{{ scope.row.toAddress }}</span>
            <span v-else style="color: red">{{ scope.row.toAddress }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="vehicleClassification" label="车辆类型" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.isValidClassification">{{ scope.row.vehicleClassification }}</span>
            <span v-else style="color: red">{{ scope.row.vehicleClassification }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="vehicleLength" label="载货长度" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.isValidLength">{{ scope.row.vehicleLength }}</span>
            <span v-else style="color: red">{{ scope.row.vehicleLength }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="vehicleAxes" label="车辆轴数" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.isValidAxes">{{ scope.row.vehicleAxes }}</span>
            <span v-else style="color: red">{{ scope.row.vehicleAxes }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="goodsTypeName" label="货物属性" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.isValidGoodType">{{ scope.row.goodsTypeName }}</span>
            <span v-else style="color: red">{{ scope.row.goodsTypeName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="truckBrandName" label="车辆品牌" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.isValidBrandName">{{ scope.row.truckBrandName }}</span>
            <span v-else style="color: red">{{ scope.row.truckBrandName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="powerTypeName" label="能源类型" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.powerTypeName">{{ scope.row.powerTypeName }}</span>
            <span v-else style="color: red">{{ scope.row.powerTypeName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="oilPricePerLiter" label="油/气单价" width="100" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.isValidOilPricePerLiter">{{ scope.row.oilPricePerLiter }}</span>
            <span v-else style="color: red">{{ scope.row.oilPricePerLiter }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="returnLineName" label="报价类型" width="100" align="center"></el-table-column>
        <el-table-column
          prop="batchrRoutePlanningStatus"
          columnKey="batchrRoutePlanningStatus"
          :filter-multiple="false"
          label="状态"
          align="center"
          :filters="[
            { text: '通过', value: 'pass' },
            { text: '异常', value: 'fail' },
          ]"
          :filter-method="filterHandler"
        >
          <!-- 起终点油价是否都正常 -->
          <template slot-scope="scope">
            <span v-if="scope.row.batchrRoutePlanningStatus == 'pass'" style="color: green">通过</span>
            <span v-else style="color: red">异常</span>
          </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" align="center" width="120" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-button @click="showEditVisible(scope.row)" type="text" size="small">修改</el-button>
            <el-button @click="delRow(scope.row)" type="text" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer">
        <el-button type="primary" v-if="tableListData.length > 0" @click="confirmForm">批量算路</el-button>
      </span>
      <el-dialog
        class="templateListDialog"
        :visible.sync="showMap"
        title="修改业务线"
        :close-on-click-modal="false"
        v-dialogDrag
        append-to-body
        :before-close="handleClose"
        top="10vh"
        width="70%"
      >
        <div style="color: orange; padding: 6px 40px 6px 10px">
          *请填写所有必填项、红色部分为异常项、请先选择车辆类型后方可选择车辆长度、然后选择车辆长度后、其他属性再进行选择、请修改后提交
        </div>
        <el-form class="formClass" :model="formData" ref="editForm" :rules="rules" :inline="true" label-width="90px">
          <el-form-item label="起点位置" prop="fromAddress">
            <remote-search-address-select-component @selectPointInfo="selectPointInfo($event, 'start')" ref="remoteSearchAddressSelectStartComponentRef" />
          </el-form-item>
          <el-form-item label="终点位置" prop="toAddress">
            <remote-search-address-select-component @selectPointInfo="selectPointInfo($event, 'end')" ref="remoteSearchAddressSelectEndComponentRef" />
          </el-form-item>
          <el-form-item label="能源类型" prop="powerTypeName">
            <el-select v-model="formData.powerTypeName" clearable placeholder="请选择能源类型">
              <el-option v-for="(item, index) in oilAndGasList" :key="index" :label="item.label" :value="item.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="油/气单价" prop="oilPricePerLiter">
            <el-input type="text" placeholder="请输入油价" clearable v-model="formData.oilPricePerLiter"></el-input>
          </el-form-item>
          <el-form-item label="报价类型" prop="returnLine" style="width: 274px">
            <el-radio v-model="formData.returnLine" label="0">单程</el-radio>
            <el-radio v-model="formData.returnLine" label="1">往返</el-radio>
          </el-form-item>
          <el-form-item label="车辆类型" prop="vehicleClassification">
            <el-select v-model="formData.vehicleClassification" placeholder="请选择车辆类型" value-key="code" @change="changeType">
              <el-option v-for="item in icationsList" :key="item.id" :label="item.name" :value="item.name" :disabled="!item.canClick"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="车辆长度" prop="vehicleLength">
            <el-select
              :disabled="vehicleLengthList.length > 0 && formData.vehicleClassification ? false : true"
              v-model="formData.vehicleLength"
              placeholder="请选择车辆长度"
              value-key="code"
              @change="changeLength"
            >
              <el-option
                v-for="(item, index) in vehicleLengthList"
                :key="index"
                :label="Number(item.name)"
                :value="Number(item.name)"
                :disabled="!item.canClick"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="车辆品牌" prop="truckBrandName">
            <!-- filterable 可搜索 -->
            <el-select
              :disabled="brandList.length > 0 && formData.vehicleLength ? false : true"
              v-model="formData.truckBrandName"
              placeholder="请选择车辆品牌"
              value-key="code"
              @change="changeBrand"
            >
              <el-option v-for="(item, index) in brandList" :key="index" :label="item.name" :value="item.name" :disabled="!item.canClick"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="车辆轴数" prop="vehicleAxes">
            <el-select
              :disabled="axlesList.length > 0 && formData.truckBrandName ? false : true"
              v-model="formData.vehicleAxes"
              placeholder="请选择车辆轴数"
              value-key="code"
              @change="changeAxes"
            >
              <el-option v-for="(item, index) in axlesList" :key="index" :label="item.name" :value="item.name" :disabled="!item.canClick"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="货物属性" prop="goodsTypeName">
            <el-select
              :disabled="goodsList.length > 0 && formData.vehicleAxes ? false : true"
              v-model="formData.goodsTypeName"
              placeholder="请选择货物属性"
              value-key="code"
              @change="changeGoods"
            >
              <el-option v-for="(item, index) in goodsList" :key="index" :label="item.name" :value="item.name" :disabled="!item.canClick"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <baidu-map
          id="mapCenter"
          :style="{ width: '100%', height: '500px' }"
          :enableMapClick="false"
          :scroll-wheel-zoom="true"
          :continuous-zoom="true"
          :center="center"
          :zoom="zoom"
          @ready="initMap"
        ></baidu-map>
        <span slot="footer">
          <el-button type="primary" @click="confirmEdit">确认修改</el-button>
        </span>
      </el-dialog>
    </el-dialog>
    <!-- 查看业务线详情及路线弹窗 -->
    <el-dialog class="templateListDialog" :visible.sync="showBusinessLine" title="查看业务线" :close-on-click-modal="false" v-dialogDrag top="10vh" width="80%">
      <el-table :data="viewTable" style="width: 100%" max-height="500px">
        <el-table-column label="序号" fixed="left" type="index" width="60" align="center">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="fromAddress" label="出发地点" min-width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="toAddress" label="到达地点" min-width="150" align="center" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="vehicleClassification" label="车辆类型" width="120" align="center"></el-table-column>
        <el-table-column prop="vehicleLength" label="载货长度(m)" width="120" align="center"></el-table-column>
        <el-table-column prop="goodsTypeName" label="货物属性" width="120" align="center"></el-table-column>
        <el-table-column prop="truckBrandName" label="车辆品牌" width="120" align="center"></el-table-column>
        <el-table-column prop="powerTypeName" label="能源类型" width="120" align="center"></el-table-column>
        <el-table-column prop="oilPricePerLiter" label="油/气单价" width="120" align="center"></el-table-column>
        <el-table-column prop="returnLineName" label="报价类型" width="120" align="center"></el-table-column>
        <el-table-column label="运输费总成本" align="center">
          <el-table-column prop="lowTotalCost" label="路线最低成本(元)" width="120" align="center"></el-table-column>
          <el-table-column prop="highTotalCost" label="路线最高成本(元)" width="120" align="center"></el-table-column>
        </el-table-column>
        <!-- <el-table-column label="报价明细" align="center">
          <el-table-column label="最低报价" align="center">
            <el-table-column prop="lowOilFeesCost" label="油费(元)" width="120" align="center">
            </el-table-column>
            <el-table-column prop="lowHighwayCost" label="高速费(元)" width="120" align="center">
            </el-table-column>
            <el-table-column prop="lowRjFeesCost" label="人件费(元)" width="120" align="center">
            </el-table-column>
            <el-table-column prop="lowOtherFeesCost" label="其他费用(元)" width="120" align="center">
            </el-table-column>
          </el-table-column>
          <el-table-column label="最高报价" align="center">
            <el-table-column prop="highOilFeesCost" label="油费(元)" width="120" align="center">
            </el-table-column>
            <el-table-column prop="highHighwayCost" label="高速费(元)" width="120" align="center">
            </el-table-column>
            <el-table-column prop="highRjFeesCost" label="人件费(元)" width="120" align="center">
            </el-table-column>
            <el-table-column prop="highOtherFeesCost" label="其他费用(元)" width="120" align="center">
            </el-table-column>
          </el-table-column>
        </el-table-column> -->
        <el-table-column fixed="right" label="操作" align="center" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-button @click="showTrackDetails(scope.row)" type="text" size="small">路线详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer"></span>
      <el-dialog
        class="templateListDialog"
        :visible.sync="showMapTrajectory"
        title="路线详情"
        :close-on-click-modal="false"
        v-dialogDrag
        append-to-body
        :before-close="detailClose"
        top="6vh"
        width="80%"
      >
        <el-row :gutter="20" class="basicInformation" v-if="newDetailData">
          <el-col :span="10" style="border-right: 1px solid #ccc; padding: 6px 15px">
            <div class="titleText">
              <span style="color: #0000cd">起点名称：</span>
              <el-tooltip :content="newDetailData.fromAddress" placement="top">
                <span>{{ newDetailData.fromAddress }}</span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="10" style="border-right: 1px solid #ccc; padding: 6px 15px">
            <div class="titleText">
              <span style="color: #0000cd">终点名称：</span>
              <el-tooltip :content="newDetailData.toAddress" placement="top">
                <span>{{ newDetailData.toAddress }}</span>
              </el-tooltip>
            </div>
          </el-col>
          <!-- <el-col :span="4" style="border-right: 1px solid #ccc; padding: 6px 15px">
            <div class="titleText">
              <span style="color: #0000cd">车辆品牌：</span>
              <span>{{ newDetailData.truckBrandName }}</span>
            </div>
          </el-col>
          <el-col :span="4" style="border-right: 1px solid #ccc; padding: 6px 15px">
            <div class="titleText">
              <span style="color: #0000cd">货物属性：</span>
              <span>{{ newDetailData.goodsTypeName }}</span>
            </div>
          </el-col> -->
          <el-col :span="4" style="padding: 6px 15px">
            <div class="titleText">
              <span style="color: #0000cd">油/气单价：</span>
              <span>{{ newDetailData.oilPricePerLiter }}</span>
            </div>
          </el-col>
        </el-row>
        <baidu-map
          id="detailMapCenter"
          :style="{ width: '100%', height: detailMapHeight + 'px' }"
          :enableMapClick="false"
          :scroll-wheel-zoom="true"
          :continuous-zoom="true"
          :center="center"
          :zoom="zoom"
          @ready="initDetailMap"
        ></baidu-map>
        <div class="bottomInfo">
          <!--table表格开始-->
          <el-table
            :data="detailListData"
            border
            size="mini"
            highlight-current-row
            :max-height="detailsTableHeight"
            style="width: 100%"
            v-loading="isShowDetaileTable"
          >
            <el-table-column label="序号" fixed="left" type="index" width="60" align="center">
              <template slot-scope="scope">
                <span>{{ scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="路线信息" align="center">
              <el-table-column
                label="路线总里程(km)"
                width="150"
                prop="totalDistanceStr"
                sortable
                align="center"
                fixed="left"
                :show-overflow-tooltip="true"
              ></el-table-column>
              <el-table-column
                label="高速路占比"
                width="120"
                prop="highwayRatio"
                sortable
                :sort-method="
                  (a, b) => {
                    return a.highwayRatio - b.highwayRatio
                  }
                "
                align="center"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">{{ scope.row.highwayRatio }}%</template>
              </el-table-column>
              <el-table-column
                label="国省高占比"
                width="120"
                prop="gshRatio"
                sortable
                :sort-method="
                  (a, b) => {
                    return a.gshRatio - b.gshRatio
                  }
                "
                align="center"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">{{ scope.row.gshRatio }}%</template>
              </el-table-column>
              <el-table-column
                label="路线总时间(h)"
                width="140"
                prop="totalDurationStr"
                sortable
                :sort-method="
                  (a, b) => {
                    return a.totalDurationStr - b.totalDurationStr
                  }
                "
                align="center"
                :show-overflow-tooltip="true"
              ></el-table-column>
              <el-table-column label="驾驶时间(h)" width="140" prop="driveTimeStr" align="center" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column label="休息时间(h)" width="140" prop="restTimeStr" align="center" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column label="红绿灯数" width="110" prop="trafficLights" align="center" :show-overflow-tooltip="true"></el-table-column>
            </el-table-column>
            <el-table-column label="成本明细" align="center">
              <el-table-column label="油/气费(元)" width="110" prop="oilFeesCost" align="center" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column label="高速费(元)" width="110" prop="highwayCost" align="center" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column label="人件费(元)" width="110" prop="rjFeesCost" align="center" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column label="车辆折旧(元)" width="110" prop="zjFeesCost" align="center" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column label="车辆保险(元)" width="110" prop="bxFeesCost" align="center" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column label="轮胎(元)" width="110" prop="ltFeesCost" align="center" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column label="维修保养(元)" width="110" prop="wbFeesCost" align="center" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column label="尿素(元)" width="110" prop="nsFeesCost" align="center" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column label="杂费(元)" width="110" prop="zxFeesCost" align="center" :show-overflow-tooltip="true"></el-table-column>
              <el-table-column
                label="总成本(元)"
                width="120"
                prop="totalCost"
                sortable
                :sort-method="
                  (a, b) => {
                    return a.totalCost - b.totalCost
                  }
                "
                align="center"
                :show-overflow-tooltip="true"
              ></el-table-column>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center" width="120" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <el-button @click="rowTable(scope.row)" type="text" size="small">查看路线</el-button>
                <el-button type="text" size="small" @click="sharedObject(scope.row)">分享</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <span slot="footer"></span>
        <!-- 共享对象 -->
        <el-dialog
          v-if="sharedDialog"
          class="enclosureDialog"
          title="共享对象"
          :visible.sync="sharedDialog"
          :before-close="handleCloseShare"
          :close-on-click-modal="false"
          append-to-body
          width="600px"
        >
          <el-form ref="shareForm" class="enclosureForm" :model="shareForm" label-width="110px">
            <el-form-item label="路线名称：" prop="name">
              <el-input class="maxInputStyle" v-model="shareForm.name" placeholder="请输入路线名称" maxlength="32" show-word-limit />
            </el-form-item>
            <el-form-item label="共享对象：" prop="shareUser">
              <el-cascader v-model="shareForm.shareUser" :options="optionList" collapse-tags :props="props" clearable filterable />
            </el-form-item>
          </el-form>
          <!-- 按钮 -->
          <span slot="footer" class="submit-btn">
            <el-button @click="cancel">取消</el-button>
            <el-button type="primary" @click="determine">确定</el-button>
          </span>
        </el-dialog>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
  import RemoteSearchAddressSelectComponent from '@/components/RemoteSearchAddressSelectComponent/index.vue'
  import {
    getOilPrice, //油价
    getClassificationsV2, //车辆类型等
  } from '@/api/AIQuote/variableFeeBudget'
  import {
    getBatchRoadCompute, // 列表
    downloadTemplateFile, //下载模版
    uploadTemplateFile, //上传模版
    downloadCompleteFile, //下载已完成算路的任务列表
    startUpTemplate, // 启动批量算路
    getBatchDetailList, // 详情列表数据
    deleteTemplate, // 删除已导入业务
    deleteDetail, //删除单条业务线
    editDetail, //修改单条业务线
    getResultDetailList, // 查询路线列表数据
  } from '@/api/multiRoute/multiRoute'
  import { getFleetUsers, addShareTrack } from '@/api/transitManagement/addressTrack'
  import Table from '@/components/TableComponent/table.vue'
  import UploadExcelComponent from '@/components/UploadExcel/index.vue'
  import Pagination from '@/components/Pagination/index.vue'
  import { getMyDate, getScollerHeight, gcj02tobd09, bd09togcj02 } from '@/utils'
  export default {
    name: 'batchTransportQuote',
    components: {
      Table,
      UploadExcelComponent,
      Pagination,
      RemoteSearchAddressSelectComponent,
    },
    data() {
      const numberValidate = (rule, value, callback) => {
        if (!value && rule.MSG) {
          callback(new Error(rule.MSG))
        } else if (!/^[0-9]+(.[0-9]{1,2})?$/.test(value)) {
          callback(new Error('请输入数字,小数点后最多保留2位!'))
        } else {
          callback()
        }
      }
      const validateFromAddress = (rule, value, callback) => {
        if (!value.trim()) {
          callback(new Error('请输入出发地点'))
        } else {
          callback()
        }
      }
      const validateToAddress = (rule, value, callback) => {
        if (!value.trim()) {
          callback(new Error('请输入到达地点'))
        } else {
          callback()
        }
      }
      return {
        map: null, //地图
        detailMap: null, //详情地图
        zoom: 5,
        center: {
          lng: '116.397451',
          lat: '39.909187',
        }, // 页面地图初始点位
        startEndMarker: [], //起止点marker
        infoWindow: null, // 信息窗口
        butnLoading: false, //下载模板按钮加载动画
        tableLoading: false, //表格loading
        importLoading: false, //导入加载
        businessLoading: false, //业务线列表loading
        showTable: false, //table列表弹窗
        showMap: false, //地图弹窗
        FullScreen: false, // 是否全屏
        startMarker: [],
        endMarker: [],
        formData: {
          id: '',
          oilPricePerLiter: null, // 油/气单价
          truckBrandName: '', // 车辆品牌
          fromAddress: '', // 出发地点
          toAddress: '', // 到达地点
          fromCoordinate: '', // 出发地点经纬度
          toCoordinate: '', // 到达地点经纬度
          goodsTypeName: '', // 货物属性
          vehicleClassification: '', //车辆类型
          vehicleLength: '', //车辆长度
          vehicleAxes: '', // 车辆轴数
          returnLine: '0', //默认不是往返
          powerTypeName: '', //能耗类型
        },
        newGoodsTypeName: '', //
        tableId: '', // 详情列表id
        page: 1, //第几页
        limit: 20, //每页条数
        total: 0, // 总量
        tableHeight: null, //表格高度

        tableData: [], //表格列表
        tableListData: [], //ab点列表数据
        showBusinessLine: false, //控制业务线显示隐藏
        showMapTrajectory: false, //控制地图轨迹弹窗
        detailMapHeight: window.innerHeight * 0.45, //详情地图高度
        detailsTableHeight: window.innerHeight * 0.3, //详情信息展示高度
        importTableHeight: window.innerHeight * 0.5, //详情信息展示高度
        newDetailData: {}, //当前业务线信息
        polyline: [],
        coordinate: [], //轨迹点
        detailListData: [], //详情轨迹路线列表
        isShowDetaileTable: false, //
        // 查看业务线列表
        viewTable: [],
        icationsList: [], //选择车辆类型列表
        vehicleLengthList: [], //选择车辆长度列表
        brandList: [], //车辆品牌列表
        axlesList: [], //选择车辆轴数列表
        goodsList: [], // 货物属性列表
        rules: {
          fromAddress: [{ validator: validateFromAddress, required: true, message: '请输入起点位置', trigger: 'blur' }],
          toAddress: [{ validator: validateToAddress, required: true, message: '请输入终点位置', trigger: 'blur' }],
          powerTypeName: [{ required: true, message: '请选择能源类型', trigger: 'change' }],
          vehicleClassification: [{ required: true, message: '请选择车辆类型', trigger: 'change' }],
          vehicleLength: [{ required: true, message: '请选择车辆长度', trigger: 'change' }],
          vehicleAxes: [{ required: true, message: '请选择车辆轴数', trigger: 'change' }],
          goodsTypeName: [{ required: true, message: '请选择货物属性', trigger: 'change' }],
          truckBrandName: [{ required: true, message: '请选择车辆品牌', trigger: 'change' }],
          returnLine: [{ required: true, message: '请选择报价类型', trigger: 'change' }],
          oilPricePerLiter: [
            {
              validator: numberValidate,
              required: true,
              MSG: '油价不能为空',
              trigger: 'blur',
            },
          ],
        },
        shoufeizhan: 'http://api.haodaoda.com/static/wx/mini/biao_shoufeizhan.png',
        qidian: 'http://api.haodaoda.com/static/wx/mini/dingwei_qidian.png',
        zhongdian: 'http://api.haodaoda.com/static/wx/mini/dingwei_zhongdian.png',
        markerTip: null, //收费站tip
        flag: true,
        times: null,
        sharedDialog: false, // 分享弹窗
        shareForm: {
          type: 2,
          trackPoints: null,
          start: null,
          end: null,
          distance: null,
          duration: null,
          name: null,
          seriesAdcode: '',
          extInfo: null,
          shareUser: [],
        }, // 分享form
        optionList: [], // 共享对象列表
        props: { multiple: true }, // 可以多选
        oilAndGasList: [
          {
            value: '柴油',
            label: '柴油',
          },
          {
            value: '燃气',
            label: '燃气',
          },
        ], //油天然气选择列表
      }
    },
    created() {
      this.getBatchRoadComputeList()
    },
    mounted() {
      //动态设置table 高度
      this.$nextTick(() => {
        this.tableHeight = getScollerHeight(this.$refs.topHeight.offsetHeight - 200)
      })
      // 自定义关闭按钮的功能
      window.closeInfoWindow = () => {
        if (this.detailMap && this.infoWindow) {
          if (this.infoWindow.isOpen()) {
            this.detailMap.closeInfoWindow()
          }
        }
      }
    },
    watch: {
      // 监听是否有算路中的数据
      tableData: {
        handler(newData, oldData) {
          if (newData && newData.length > 0) {
            let newStateList = []
            newData.forEach((item) => {
              newStateList.push(item.state)
            })
            if (newStateList.includes('COMPUTING')) {
              if (this.times) {
                clearTimeout(this.times)
              }
              this.times = setTimeout(() => {
                // 处理快捷建切换再次调用
                if (this.flag) {
                  this.page = 1
                  this.limit = 20
                  this.getBatchRoadComputeList()
                }
                this.flag = true
              }, 1000 * 30)
              return
            }
          }
        },
      },
    },
    methods: {
      selectPointInfo(event, type) {
        if (event.latitude && event.longitude) {
          if (this.startEndMarker.length > 0) {
            this.startEndMarker.map((item) => {
              if (item.data.isType == type) {
                this.map.removeOverlay(item)
              }
            })
          }
          if (this.pointList.length > 0) {
            this.pointList.map((item) => {
              if (item.lng == event.longitude && item.lat == event.latitude) {
                this.map.removeOverlay(item)
              }
            })
          }
          // 增加起点marker点位置
          var markerIcon = new BMap.Icon(
            type === 'start' ? this.qidian : this.zhongdian, // 图标图片的url
            new BMap.Size(32, 48), // 图标大小
            {
              anchor: new BMap.Size(16, 48), // 设置图标的定位点（偏移）
              imageOffset: new BMap.Size(0, 0), // 图标图片的偏移
              imageSize: { width: 32, height: 48 },
            },
          )
          var point = new BMap.Point(event.longitude, event.latitude) // 经纬度
          var marker = new BMap.Marker(point, { icon: markerIcon, offset: new BMap.Size(0, 8) })
          marker.data = {
            isType: type,
          }
          this.pointList.push(point)
          this.startEndMarker.push(marker)
          this.map.addOverlay(marker) // 添加到地图

          if (type === 'start') {
            this.formData.fromAddress = event.address
            this.formData.fromCoordinate = [event.longitude, event.latitude].toString()
          } else if (type === 'end') {
            this.formData.toAddress = event.address //设置终点位置名称
            this.formData.toCoordinate = [event.longitude, event.latitude].toString()
          }
          this.$refs.editForm.validateField('fromAddress')
          // 处理起终点自适应地图
          this.map.setViewport(this.pointList)
        }
      },
      // 选择车型
      changeType(value) {
        const selectedData = this.icationsList.filter((option) => option.name === value)
        if (selectedData && selectedData.length > 0) {
          this.radioSrc = selectedData[0].icon
          this.vehicleLengthList = selectedData[0].children
          this.brandList = []
          this.axlesList = []
          this.goodsList = []
          this.formData.vehicleLength = ''
          this.formData.vehicleAxes = ''
          this.formData.goodsTypeName = ''
          this.formData.truckBrandName = ''
        }
      },
      // 选择载货长度
      changeLength(value) {
        const selectedData = this.vehicleLengthList.filter((option) => option.name == value)
        if (selectedData && selectedData.length > 0) {
          this.brandList = selectedData[0].children
          this.axlesList = []
          this.goodsList = []
          this.formData.vehicleAxes = ''
          this.formData.goodsTypeName = ''
          this.formData.truckBrandName = ''
        }
      },
      // 选择车辆品牌
      changeBrand(value) {
        const selectedData = this.brandList.filter((option) => option.name === value)
        if (selectedData && selectedData.length > 0) {
          this.axlesList = selectedData[0].children
          this.goodsList = []
          this.formData.vehicleAxes = ''
          this.formData.goodsTypeName = ''
        }
      },
      // 选择车辆轴数
      changeAxes(value) {
        const selectedData = this.axlesList.filter((option) => option.name === value)
        if (selectedData && selectedData.length > 0) {
          this.goodsList = selectedData[0].children
          this.formData.goodsTypeName = ''
        }
      },
      // 选择货物属性
      changeGoods(value) {
        if (value) {
          this.formData.goodsTypeName = value
        } else {
          this.formData.goodsTypeName = ''
        }
      },

      //筛选
      filterHandler(value, row, column) {
        const property = column['property']
        return row[property] === value
      },
      // 下载模板文件
      downloadTemplate() {
        this.butnLoading = true
        downloadTemplateFile({})
          .then((response) => {
            this.butnLoading = false
            // 转化为blob对象
            let blob = new Blob([response.data], {
              type: 'application/octet-stream',
            })
            let fileName = decodeURI(response.headers['content-disposition'].split(';')[1].split('=')[1])
            // 将blob对象转为一个URL
            var blobURL = window.URL.createObjectURL(blob)
            // 创建一个a标签
            var tempLink = document.createElement('a')
            // 隐藏a标签
            tempLink.style.display = 'none'
            // 设置a标签的href属性为blob对象转化的URL
            tempLink.href = blobURL
            // 给a标签添加下载属性
            tempLink.setAttribute('download', fileName)
            if (typeof tempLink.download === 'undefined') {
              tempLink.setAttribute('target', '_blank')
            }
            // 将a标签添加到body当中
            document.body.appendChild(tempLink)
            // 启动下载
            tempLink.click()
            // 下载完毕删除a标签
            document.body.removeChild(tempLink)
            window.URL.revokeObjectURL(blobURL)
            this.$notify({
              title: '提示',
              message: '下载模板成功',
              type: 'success',
            })
          })
          .catch(() => {
            this.butnLoading = false
          })
      },
      //获取分页数据
      pagination(val) {
        this.limit = val.limit
        this.page = val.page
        this.getBatchRoadComputeList()
      },
      // 获取算路列表
      getBatchRoadComputeList() {
        let params = {
          page: this.page,
          limit: this.limit,
        }
        this.tableLoading = true
        getBatchRoadCompute({}, params)
          .then((response) => {
            // console.log(response)
            this.tableLoading = false
            if (response.code == 200) {
              // 1、 未计算(waiting) 2、计算中(computing) 3、计算完成(computed)
              this.total = response.data.total
              this.tableData = response.data.rows
            } else {
              this.total = 0
              this.tableData = [] //清空列表
            }
          })
          .catch(() => {
            this.total = 0
            this.tableData = [] //清空列表
            this.tableLoading = false
          })
      },
      // 删除已完成或者未算路的任务
      deleteForm(row) {
        this.$confirm('确定删除当前业务？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            deleteTemplate({}, row.id)
              .then((response) => {
                this.$notify({
                  title: '提示',
                  message: '删除成功',
                  type: 'success',
                })
                this.limit = 20
                this.page = 1
                this.getBatchRoadComputeList()
              })
              .catch(() => {})
          })
          .catch(() => {})
      },
      // 初始化地图
      initMap({ BMap, map }) {
        console.log('123456', map)
        this.map = map
        this.center.lng = '116.403322'
        this.center.lat = '39.920255'
        this.zoom = 5 // 地图缩放级别
      },

      // 初始化详情地图
      initDetailMap({ BMap, map }) {
        this.detailMap = map
        this.center.lng = '116.403322'
        this.center.lat = '39.920255'
        this.zoom = 5 // 地图缩放级别
      },
      // 刷新列表
      refresh() {
        this.page = 1
        this.limit = 20
        this.getBatchRoadComputeList()
      },
      // 关闭业务线列表弹窗
      businessDialogClose() {
        this.showTable = false
        this.page = 1
        this.limit = 20
        this.getBatchRoadComputeList()
      },
      // 关闭弹窗
      handleClose() {
        this.showMap = false
        this.page = 1
        this.limit = 20
        this.getBatchRoadComputeList()
      },
      // 关闭详情弹窗
      detailClose() {
        this.showMapTrajectory = false
      },

      // 获取油价
      getOilPriceData(position) {
        let params = {
          startCoordinate: position,
          type: 1,
        }
        getOilPrice(params)
          .then((response) => {
            this.formData.oilPricePerLiter = response
          })
          .catch(() => {
            this.formData.oilPricePerLiter = null
          })
      },
      // 点击查看详情
      viewDetails(rowData) {
        this.showBusinessLine = true
        this.getBatchDetailList(rowData.id)
      },
      //展示轨迹及路线详情
      showTrackDetails(newData) {
        // console.log(newData);
        this.newDetailData = newData //当前业务线信息
        this.showMapTrajectory = true
        this.isShowDetaileTable = true
        this.$nextTick(() => {
          getResultDetailList({}, newData.id).then((response) => {
            this.isShowDetaileTable = false
            this.detailListData = response
          })
        })
      },
      // 点击查看路线
      rowTable(data) {
        this.detailMap.clearOverlays()
        // 信息窗关闭
        if (this.detailMap && this.infoWindow) {
          if (this.infoWindow.isOpen()) {
            this.detailMap.closeInfoWindow()
          }
        }
        if (data.track) {
          this.coordinate = []
          var trackList = data.track.split(';')
          let options = {
            strokeColor: '#0066ff', //线颜色
            strokeOpacity: 0.9, //线透明度
            strokeWeight: 8, //线宽
            strokeStyle: 'solid', //线样式
          }
          trackList.map((item) => {
            const coords = item.split(',')
            if (coords.length == 2) {
              const lng = parseFloat(coords[0])
              const lat = parseFloat(coords[1])
              if (!isNaN(lng) && !isNaN(lat)) {
                const bdPoint = gcj02tobd09(lng, lat)
                if (bdPoint.length > 1) {
                  this.coordinate.push(new BMap.Point(bdPoint[0], bdPoint[1]))
                }
              }
            }
          })
          //轨迹点
          this.polyline = new BMap.Polyline(this.coordinate, options)
          // 将路线添加至地图实例
          this.detailMap.addOverlay(this.polyline)
        }
        if (this.coordinate.length > 0) {
          // 定义图标
          var qidianIcon = new BMap.Icon(
            this.qidian, // 图标图片的url
            new BMap.Size(32, 48), // 图标大小
            {
              anchor: new BMap.Size(16, 48), // 设置图标的定位点（偏移）
              imageOffset: new BMap.Size(0, 0), // 图标图片的偏移
              imageSize: { width: 32, height: 48 },
            },
          )
          var zhongdianIcon = new BMap.Icon(
            this.zhongdian, // 图标图片的url
            new BMap.Size(32, 48), // 图标大小
            {
              anchor: new BMap.Size(16, 48), // 设置图标的定位点（偏移）
              imageOffset: new BMap.Size(0, 0), // 图标图片的偏移
              imageSize: { width: 32, height: 48 },
            },
          )
          var markerStart = new BMap.Marker(this.coordinate[0], { icon: qidianIcon, offset: new BMap.Size(0, 8) })
          var markerEnd = new BMap.Marker(this.coordinate[this.coordinate.length - 1], { icon: zhongdianIcon, offset: new BMap.Size(0, 8) })
          this.detailMap.addOverlay(markerStart) // 添加到地图
          this.detailMap.addOverlay(markerEnd) // 添加到地图
          // 缩放地图到合适的视野级别
          this.detailMap.setViewport([this.coordinate[0], this.coordinate[this.coordinate.length - 1]])
        }
        if (data.tollGateList && data.tollGateList.length > 0) {
          data.tollGateList.map((every, index) => {
            // 定义图标
            var myIcon = new BMap.Icon(
              this.shoufeizhan, // 图标图片的url
              new BMap.Size(26, 38), // 图标大小
              {
                anchor: new BMap.Size(13, 38), // 设置图标的定位点（偏移）
                imageOffset: new BMap.Size(0, 0), // 图标图片的偏移
                imageSize: { width: 26, height: 38 },
              },
            )
            let newPoint = every.coordinate ? every.coordinate.split(',') : []
            if (newPoint.length == 2) {
              const lng = parseFloat(newPoint[0])
              const lat = parseFloat(newPoint[1])
              if (!isNaN(lng) && !isNaN(lat)) {
                const bdPoint = gcj02tobd09(lng, lat)
                if (bdPoint.length > 1) {
                  var point = new BMap.Point(newPoint[0], newPoint[1]) // 经纬度
                  var tipMarker = new BMap.Marker(point, { icon: myIcon, offset: new BMap.Size(0, 8) })
                  this.detailMap.addOverlay(tipMarker) // 添加到地图
                  // 添加点击事件
                  tipMarker.addEventListener('click', (e) => {
                    this.addMarkerTip(every, e)
                  })
                }
              }
            }
          })
        }
      },
      // 异常点信息
      addMarkerTip(item) {
        // console.log(item);
        if (this.detailMap && this.infoWindow) {
          if (this.infoWindow.isOpen()) {
            this.detailMap.closeInfoWindow()
          }
        }
        var opts = {
          width: 260, // 信息窗口宽度
          enableCloseOnClick: false, // 点击地图时不关闭
        }
        let positionMarker = item.coordinate.split(',')
        var content = `
            <div class="titleBox">
              <h4>收费站信息</h4>
              <button class="close-btn" onclick="closeInfoWindow()">X</button>
            </div>
            <div class="contentBox">
              <span class="title">收费站名称：</span><span class="contentInfo">${item.gateName}</span><br>
              <span class="title">收费站出入：</span><span class="contentInfo">${item.typeStr}</span>
            </div>
            `
        this.infoWindow = new BMap.InfoWindow(content, opts) // 创建信息窗口对象
        var newPoint = new BMap.Point(Number(positionMarker[0]), Number(positionMarker[1]))
        this.detailMap.openInfoWindow(this.infoWindow, newPoint) //开启信息窗口
      },
      //接收table组件传回任务下载
      downloadData(row) {
        downloadCompleteFile({}, row.id)
          .then((response) => {
            // 转化为blob对象
            let blob = new Blob([response.data], {
              type: 'application/octet-stream',
            })
            let fileName = decodeURI(response.headers['content-disposition'].split(';')[1].split('=')[1])
            // 将blob对象转为一个URL
            var blobURL = window.URL.createObjectURL(blob)
            // 创建一个a标签
            var tempLink = document.createElement('a')
            // 隐藏a标签
            tempLink.style.display = 'none'
            // 设置a标签的href属性为blob对象转化的URL
            tempLink.href = blobURL
            // 给a标签添加下载属性
            tempLink.setAttribute('download', fileName)
            if (typeof tempLink.download === 'undefined') {
              tempLink.setAttribute('target', '_blank')
            }
            // 将a标签添加到body当中
            document.body.appendChild(tempLink)
            // 启动下载
            tempLink.click()
            // 下载完毕删除a标签
            document.body.removeChild(tempLink)
            window.URL.revokeObjectURL(blobURL)
            this.$notify({
              title: '提示',
              message: '下载成功',
              type: 'success',
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      // 点击详情编辑 编辑导入路线弹窗
      showEditVisible(item) {
        this.formData.id = item.id //当前id
        this.showMap = true //显示编辑地图弹窗
        this.formData.vehicleClassification = ''
        this.formData.vehicleLength = ''
        this.formData.vehicleAxes = ''
        this.formData.goodsTypeName = ''
        this.formData.truckBrandName = ''
        this.pointList = [] //清空起始点
        this.startEndMarker = [] //清空起始点

        // 赋值能源类型
        if (item.powerTypeName && item.powerTypeName) {
          this.formData.powerTypeName = item.powerTypeName
        } else {
          this.formData.powerTypeName = ''
        }
        // 赋值油价
        if (item.oilPricePerLiter && item.isValidOilPricePerLiter) {
          this.formData.oilPricePerLiter = item.oilPricePerLiter
        } else {
          this.formData.oilPricePerLiter = ''
        }
        // 赋值起点
        if (item.fromCoordinate && item.isValidFrom) {
          this.formData.fromAddress = item.fromAddress
          this.formData.fromCoordinate = item.fromCoordinate
          this.$nextTick(() => {
            this.$refs.remoteSearchAddressSelectStartComponentRef.value = item.fromAddress
          })
        } else {
          this.formData.fromAddress = '' //重置起点位置
          this.formData.fromCoordinate = '' // 起点marker
          this.$nextTick(() => {
            this.$refs.remoteSearchAddressSelectStartComponentRef.value = ''
          })
        }
        // 赋值终点
        if (item.toCoordinate && item.isValidTo) {
          this.formData.toAddress = item.toAddress
          this.formData.toCoordinate = item.toCoordinate
          this.$nextTick(() => {
            this.$refs.remoteSearchAddressSelectEndComponentRef.value = item.toAddress
          })
        } else {
          this.formData.toAddress = '' //重置终点位置
          this.formData.toCoordinate = '' // 终点marker
          this.$nextTick(() => {
            this.$refs.remoteSearchAddressSelectEndComponentRef.value = ''
          })
        }
        // 赋值报价类型
        if (item.returnLine) {
          this.formData.returnLine = item.returnLine + ''
        } else {
          this.formData.returnLine = '0' // 默认非往返
        }
        // 请求接口
        this.getClassificationsData()
        setTimeout(() => {
          // 赋值上一次选中的
          if (item.vehicleClassification && item.vehicleClassification != '--') {
            this.changeType(item.vehicleClassification)
            this.formData.vehicleClassification = item.vehicleClassification
          }
          if (item.vehicleLength && item.vehicleLength != '--') {
            this.changeLength(item.vehicleLength)
            this.formData.vehicleLength = item.vehicleLength
          }
          if (item.truckBrandName && item.truckBrandName != '--') {
            this.changeBrand(item.truckBrandName)
            this.formData.truckBrandName = item.truckBrandName
          }
          if (item.vehicleAxes && item.vehicleAxes != '--') {
            this.changeAxes(item.vehicleAxes)
            this.formData.vehicleAxes = item.vehicleAxes
          }
          if (item.goodsTypeName && item.goodsTypeName != '--') {
            this.formData.goodsTypeName = item.goodsTypeName
          }
        }, 300)

        // 初始化地图及marker点
        setTimeout(() => {
          this.$nextTick(() => {
            this.map.clearOverlays()
            this.$refs['editForm'].validate()
            // 起点marker
            if (item.fromCoordinate) {
              var qidianIcon = new BMap.Icon(
                this.qidian, // 图标图片的url
                new BMap.Size(32, 48), // 图标大小
                {
                  anchor: new BMap.Size(16, 48), // 设置图标的定位点（偏移）
                  imageOffset: new BMap.Size(0, 0), // 图标图片的偏移
                  imageSize: { width: 32, height: 48 },
                },
              )
              let startPoint = item.fromCoordinate.split(',')
              var point = new BMap.Point(startPoint[0], startPoint[1]) // 经纬度
              this.pointList.push(point)
              var marker = new BMap.Marker(point, { icon: qidianIcon, offset: new BMap.Size(0, 8) })
              marker.data = {
                isType: 'start',
              }
              this.startEndMarker.push(marker)
              this.map.addOverlay(marker) // 添加到地图
            }
            // 终点marker
            if (item.toCoordinate) {
              var zhongdianIcon = new BMap.Icon(
                this.zhongdian, // 图标图片的url
                new BMap.Size(32, 48), // 图标大小
                {
                  anchor: new BMap.Size(16, 48), // 设置图标的定位点（偏移）
                  imageOffset: new BMap.Size(0, 0), // 图标图片的偏移
                  imageSize: { width: 32, height: 48 },
                },
              )
              let endPoint = item.toCoordinate.split(',')
              var point = new BMap.Point(endPoint[0], endPoint[1]) // 经纬度
              this.pointList.push(point)
              var marker = new BMap.Marker(point, { icon: zhongdianIcon, offset: new BMap.Size(0, 8) })
              marker.data = {
                isType: 'end',
              }
              this.startEndMarker.push(marker)
              this.map.addOverlay(marker) // 添加到地图
            }
            // 缩放地图到合适的视野级别
            this.map.setViewport(this.pointList)
          })
        }, 200)
      },
      //删除导入路线行
      delRow(row) {
        deleteDetail({}, row.id).then((response) => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
          })
          this.getBatchDetailList(this.tableId)
        })
      },
      // 点击编辑业务线起终点及油价品牌
      confirmEdit() {
        // console.log(this.formData);
        this.$refs['editForm'].validate((valid) => {
          if (valid) {
            if (this.formData.fromCoordinate == '') {
              this.$message({
                type: 'warning',
                message: '起点位置经纬度不能为空！',
                duration: 3 * 1000,
              })
              return
            }
            if (this.formData.toCoordinate == '') {
              this.$message({
                type: 'warning',
                message: '终点位置经纬度不能为空！',
                duration: 3 * 1000,
              })
              return
            }
            let startLoaction = this.formData.fromCoordinate.split(',')
            let endLoaction = this.formData.toCoordinate.split(',')
            let params = {
              fromAddress: this.formData.fromAddress, // 开始地址
              toAddress: this.formData.toAddress, // 结束地址
              fromCoordinate: bd09togcj02(startLoaction[0], startLoaction[1]).join(','), // 开始坐标
              toCoordinate: bd09togcj02(endLoaction[0], endLoaction[1]).join(','), // 结束坐标
              truckBrandName: this.formData.truckBrandName, // 车辆品牌
              oilPricePerLiter: this.formData.oilPricePerLiter, // 油单价
              goodsTypeName: this.formData.goodsTypeName, // 货物属性
              vehicleLength: this.formData.vehicleLength, //载货长度
              vehicleClassification: this.formData.vehicleClassification, //车辆类型
              vehicleAxes: this.formData.vehicleAxes, //轴数
              returnLine: Number(this.formData.returnLine), //报价类型
              powerTypeName: this.formData.powerTypeName, // 能源类型
            }
            // 编辑单业务线
            editDetail(params, this.formData.id).then((response) => {
              this.showMap = false
              this.getBatchDetailList(this.tableId)
            })
          }
        })
      },
      // 主页业务列表中的批量算路 --- 点击批量算路
      batchRouting(row) {
        this.showTable = true
        this.tableId = row.id
        this.getBatchDetailList(this.tableId)
      },
      // 获取批量算路详情列表数据
      getBatchDetailList(valueId) {
        this.businessLoading = true
        getBatchDetailList({}, valueId)
          .then((response) => {
            this.businessLoading = false
            response.details.forEach((item) => {
              if (item.isValid) {
                item.batchrRoutePlanningStatus = 'pass'
              } else {
                item.batchrRoutePlanningStatus = 'fail'
              }
            })
            this.viewTable = response.details
            this.tableListData = response.details //也赋值给导入的业务线列表
          })
          .catch(() => {
            this.viewTable = []
            this.tableListData = []
            this.businessLoading = false
          })
      },
      // 请求车辆类型及其他项数据
      getClassificationsData() {
        getClassificationsV2({}, 'aioffer')
          .then((response) => {
            this.icationsList = response
          })
          .catch(() => {
            this.icationsList = []
          })
      },
      //提交算路
      confirmForm() {
        startUpTemplate({}, this.tableId)
          .then((response) => {
            // console.log(response)
            this.showTable = false //关闭弹窗
            this.limit = 20
            this.page = 1
            this.getBatchRoadComputeList()
          })
          .catch((error) => {})
      },
      handleImport(data) {
        this.importLoading = data
      },
      // 模板导入
      async importExcelFile(formData) {
        this.showTable = true
        this.businessLoading = true
        this.tableListData = []
        await uploadTemplateFile(formData, '')
          .then((response) => {
            this.businessLoading = false
            this.importLoading = false
            this.tableId = response.id
            response.details.forEach((item) => {
              if (item.isValid) {
                item.batchrRoutePlanningStatus = 'pass'
              } else {
                item.batchrRoutePlanningStatus = 'fail'
              }
            })
            this.tableListData = response.details
          })
          .catch(() => {
            this.businessLoading = false
            this.importLoading = false
            this.tableListData = []
          })
      },

      // 点击共享对象
      sharedObject(row) {
        // 赋值
        this.shareForm.trackPoints = row.track
        this.shareForm.start = row.fromAddress
        this.shareForm.end = row.toAddress
        this.shareForm.distance = Number(row.totalDistanceStr)
        this.shareForm.duration = row.totalDurationSeconds + ''
        this.shareForm.seriesAdcode = row.seriesAdcode
        this.shareForm.extInfo = null
        // this.shareForm.name = row.name
        if (row.shareUser && row.shareUser.length > 0) {
          var newParams = []
          row.shareUser.forEach((item) => {
            if (item.fleetId == item.fleetId) {
              newParams.push([item.fleetId, item.userIds])
            }
          })
          this.shareForm.shareUser = newParams
        } else {
          this.shareForm.shareUser = []
        }
        this.sharedDialog = true
        getFleetUsers({}).then((response) => {
          var newData = []
          if (response.length > 0) {
            response.forEach((item) => {
              const childrenList = []
              if (item.userList.length > 0) {
                item.userList.forEach((event) => {
                  const childrenObj = {
                    value: event.userId,
                    label: event.userName,
                  }
                  childrenList.push(childrenObj)
                })
              }
              const newObj = {
                value: item.fleetId,
                label: item.fleetName,
                children: childrenList,
              }
              newData.push(newObj)
            })
            this.optionList = newData
          } else {
            this.optionList = []
          }
        })
      },
      // 共享对象确定
      determine() {
        const params = {
          ...this.shareForm,
          shareUser: [],
        }
        if (!this.shareForm.name) {
          this.$notify({
            title: '提示',
            message: '路线名称不能为空',
            type: 'error',
          })
          return
        }
        if (this.shareForm.shareUser.length > 0) {
          const newData = []
          this.shareForm.shareUser.forEach((item, index) => {
            const shareList = {
              fleetId: item[0],
              userIds: item[1],
            }
            newData.push(shareList)
          })
          params.shareUser = newData
        } else {
          params.shareUser = []
          this.$notify({
            title: '提示',
            message: '请选择共享对象',
            type: 'error',
          })
          return
        }
        addShareTrack(params)
          .then((res) => {
            this.sharedDialog = false
            this.$notify({
              title: '提示',
              message: '分享成功',
              type: 'success',
            })
            this.shareForm.name = ''
          })
          .catch(() => {
            this.$notify({
              title: '提示',
              message: '分享失败',
              type: 'error',
            })
          })
      },
      // 关闭分享弹窗
      handleCloseShare(done) {
        this.sharedDialog = false // 密码弹窗
        this.$refs.shareForm.resetFields()
        done()
      },
      // 取消弹窗
      cancel() {
        this.sharedDialog = false
        if (this.$refs.shareForm) {
          this.$refs.shareForm.resetFields()
        }
      },
    },
  }
</script>

<style scoped>
  #mapCenter,
  #initDetailMap {
    width: 100%;
    flex: 1;
    position: relative;
  }

  .multiline {
    margin: 18px;
  }

  /* 业务线确认弹窗样式 */
  .templateListDialog >>> .el-dialog__body {
    padding: 0 20px;
  }

  /* 轨迹详情弹窗 */
  .basicInformation {
    font-weight: 600;
    color: #000;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-left: 0px !important;
    margin-right: 0px !important;
    margin-bottom: 4px;
  }

  .titleText {
    overflow: hidden;
    /*隐藏*/
    white-space: nowrap;
    /*不换行*/
    text-overflow: ellipsis;
    /* 超出部分省略号 */
  }

  /* 底部信息 */
  .bottomInfo {
    background: #fff;
  }

  #detailMapCenter >>> .amap-info-content {
    padding: 0;
  }

  #detailMapCenter >>> .info-top {
    background: rgb(63, 145, 107);
    padding: 4px 20px;
    font-size: 14px;
    color: #fff;
    font-weight: 600;
  }

  #detailMapCenter >>> .amap-info-close {
    position: absolute;
    right: 0px;
    top: 0px;
    color: #fff;
    text-decoration: none;
    font:
      700 16px/14px Tahoma,
      Verdana,
      sans-serif;
    width: 32px;
    height: 26px;
    line-height: 26px;
    cursor: pointer;
    font-size: 20px;
    background: rgb(63, 145, 107);
    text-align: center;
  }

  #detailMapCenter >>> .info-middle {
    padding: 10px 20px;
    font-size: 14px;
    width: 260px;
  }

  #detailMapCenter >>> .info-middle .title {
    color: #228b22;
    font-weight: 500;
    width: 25%;
  }

  #detailMapCenter >>> .info-middle .contentInfo {
    width: 75%;
  }

  .enclosureForm >>> .el-cascader--small {
    width: 90%;
  }
  .maxInputStyle {
    width: 90%;
  }

  .maxInputStyle >>> .el-input__inner {
    padding-right: 42px;
  }

  .maxInputStyle >>> .el-input__suffix {
    right: 2px;
  }
</style>
