<!--
 * @Author: llm
 * @Date: 2025-05-29 09:50:05
 * @LastEditors: llm
 * @LastEditTime: 2025-05-29 10:39:57
 * @Description: 智能调度-表格组件
-->
<template>
  <div>
    <el-table
      class="custom-summary-table"
      :data="tableData"
      style="width: 100%"
      ref="dataTableRef"
      v-loading="loading"
      :max-height="height"
      :stripe="false"
      size="small"
      tooltip
      :highlight-current-row="highlightCurrentRow"
      :border="tableBorder"
      :header-cell-style="{ backgroundColor: '#FAFAFA', textAlign: 'center' }"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      table-layout="auto"
    >
      <el-table-column :selectable="selectable" type="selection" :width="45" align="center" v-if="tableConfig.showHandleSelection" />
      <template #empty>{{ loading ? '' : '暂无数据' }}</template>

      <template v-for="item in tableConfig.tableItem">
        <template v-if="item.listEnable">
          <!-- 富文本显示 -->
          <el-table-column
            :fixed="item.attributes ? item.attributes!.fixed : false"
            :prop="item.name"
            :min-width="item.minWidth"
            :width="item.width"
            :align="item.align"
            show-overflow-tooltip
            :sortable="item.sortAble ? 'custom' : false"
            v-if="item.type === 'textRich'"
          >
            <template #header>
              <div style="display: inline-block; text-align: center">
                {{ item.label }}
                <el-tooltip
                  v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))"
                  effect="dark"
                  :content="`优先级排序:${state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1}`"
                  placement="top"
                  :show-after="100"
                >
                  <span v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))" class="sort-priority">
                    {{ state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1 }}
                  </span>
                </el-tooltip>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="item.attributes!.content ?? '--'"
                  placement="top-start"
                  v-if="item.attributes && item.attributes!.content"
                >
                  <el-icon>
                    <WarningFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="scope">
              <div style="display: flex; align-items: center; justify-content: space-between">
                <div v-html="scope.row[item.name] === null ? '--' : scope.row[item.name]"></div>
              </div>
            </template>
          </el-table-column>
          <!-- 默认显示 -->
          <el-table-column
            v-else
            :fixed="item.attributes ? item.attributes!.fixed : false"
            :prop="item.name"
            :min-width="item.minWidth"
            :width="item.width"
            :align="item.align"
            show-overflow-tooltip
            :sortable="item.sortAble ? 'custom' : false"
          >
            <template #header>
              <div style="display: flex; align-items: center; justify-content: center">
                {{ item.label }}
                <el-tooltip
                  v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))"
                  effect="dark"
                  :content="`优先级排序:${state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1}`"
                  placement="top"
                  :show-after="100"
                >
                  <span v-if="state.orderBys.find((order: any) => order.column === (item.attributes?.sortColumn || item.name))" class="sort-priority">
                    {{ state.orderBys.findIndex((order: any) => order.column === (item.attributes?.sortColumn || item.name)) + 1 }}
                  </span>
                </el-tooltip>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="item.attributes!.content ?? '--'"
                  placement="top-start"
                  v-if="item.attributes && item.attributes!.content"
                >
                  <el-icon>
                    <WarningFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="scope">
              <div style="display: flex; align-items: center; justify-content: space-between">
                <!-- 如果值是boolean类型，则显示是否 -->
                <div v-if="typeof scope.row[item.name] === 'boolean'">
                  <!-- 如果 item.name==='applyFlag' 则显示配板状态 需要显示 配/常 -->
                  <div v-if="item.name === 'applyFlag'">
                    <el-switch
                      :inline-prompt="true"
                      v-model="scope.row[item.name]"
                      style="--el-switch-on-color: #409eff; --el-switch-off-color: #13ce66"
                      active-text="配"
                      inactive-text="常"
                    />
                  </div>
                  <!-- 否则显示 是/否 -->
                  <div v-else>{{ scope.row[item.name] ? '是' : '否' }}</div>
                </div>
                <!-- 否则直接显示返回文字 -->
                <div v-else>
                  <div class="flex-row items-center" v-if="item.name === 'title' && showReadStatus">
                    <div class="read-status" v-if="scope.row.readStatus !== 1"></div>
                    {{ scope.row[item.name] === null ? '--' : scope.row[item.name] }}
                  </div>
                  <div v-else style="display: flex; align-items: center">
                    <span>{{ scope.row[item.name] === null ? '--' : scope.row[item.name] }}</span>
                    <svg-icons
                      :icon-class="scope.row.styleItem.find((v: any) => v.name === item.name).icon"
                      :color="scope.row.styleItem.find((v: any) => v.name === item.name).color"
                      style="margin-left: 10px"
                      size="18"
                      v-if="scope.row.styleItem && scope.row.styleItem.find((v: any) => v.name === item.name)"
                    />
                  </div>
                </div>
                <el-tooltip content="点击复制" style="display: flex; align-items: center">
                  <div class="ml-2 mt-1">
                    <el-icon @click.stop="copy(scope.row[item.name])" v-if="item.attributes?.copy" color="#409eff">
                      <CopyDocument />
                    </el-icon>
                  </div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </template>
      </template>
      <el-table-column v-if="tableConfig.operation" type="operation" fixed="right" min-width="80px" :width="operationWidth" align="center">
        <template #header>
          <div class="flex items-center justify-center">
            <span class="mr-4px" :data-width="operationWidth">操作</span>
            <el-icon size="16" @click="showCustomTableHeader" v-if="showCustomTableHeaderBtn">
              <Tools />
            </el-icon>
          </div>
        </template>
        <template #default="scope">
          <div class="flex items-center justify-center">
            <!-- 显示按钮 -->
            <template v-for="(item, index) in getVisibleButtons(props.buttonPermissionGroup, scope.row)" :key="index">
              <el-button
                type="primary"
                size="small"
                link
                @click.native.stop="operation(item.meta?.purpose, scope.row, item.meta?.position, item.meta, item)"
                :style="{ color: item.meta.background }"
                :data-uri="item.meta.uri ?? '全局的'"
              >
                <div>{{ item.meta?.title }}</div>
              </el-button>
            </template>

            <!-- 如果有更多按钮，显示下拉菜单 -->
            <el-dropdown placement="left-end" v-if="getHiddenButtons(props.buttonPermissionGroup, scope.row).length > 0" trigger="hover">
              <el-button type="primary" size="small" link>
                更多
                <el-icon>
                  <ArrowDown />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="(item, index) in getHiddenButtons(props.buttonPermissionGroup, scope.row)"
                    :key="index"
                    @click="operation(item.meta?.purpose, scope.row, item.meta?.position, item.meta, item)"
                  >
                    <el-button type="primary" size="small" link :style="{ color: item.meta.background }" :data-uri="item.meta.uri ?? '全局的'">
                      {{ item.meta?.title }}
                    </el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 自定义表头弹窗 -->
    <CustomTableHeaderComponent ref="customTableHeaderRef" :requestUri="requestUri" @refreshPageTableColumn="refreshPageTableColumn" />
    <!-- 查看照片 -->
    <PicDialogComponent ref="picDialogComponent" :image-list="imageList" />
    <!-- 查看文件 -->
    <ShowFileDialogComponent ref="showFileDialogComponentRef" />
    <!-- 视频播放弹窗 -->
    <el-dialog v-model="videoDialogVisible" title="视频播放" width="60%" destroy-on-close>
      <video ref="videoPlayer" controls style="width: 100%; max-height: 70vh"></video>
    </el-dialog>
    <!-- 鼠标悬浮展示 -->
    <el-dialog v-model="mouseOverShowDialogVisible" :title="mouseOverShowInfo.title" width="400px" destroy-on-close>
      <div v-html="mouseOverShowInfo.data"></div>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import { useButtonGroupStore } from '@/store/modules/buttonGroup'
  import { useSideBarStore } from '@/store/modules/sideBar'
  import { composeRequestParams, operatorCalculate } from '@/utils/common'
  import { useFormStore } from '@/store/modules/form'
  import { globalRequestUrlApi } from '@/api/planManagement'
  import PicDialogComponent from '@/components/PicDialogComponent/index.vue'
  import CustomTableHeaderComponent from '@/components/CustomTableHeaderComponent/index.vue'
  import ShowFileDialogComponent from '@/components/TableComponent/components/showFileDialogComponent.vue'
  const router = useRouter()
  const operationWidth = ref('80px')
  const videoDialogVisible = ref(false)
  const mouseOverShowDialogVisible = ref(false)
  const showCustomTableHeaderBtn = ref(false)
  const dataTableRef = ref<any>(null)
  const refreshTable = ref(true)
  const mouseOverShowInfo = ref({
    title: '',
    data: '',
  })
  const imageList = ref([])
  const emit = defineEmits([
    'handleSelectRows', //选择的行列表
    'handleSelectionChange', //选择的行列表
    'refreshPageTableColumn', //刷新页面数据列
    'copy', //复制表单
    'addItem', //新增数据
    'batchDelete', //批量删除
    'deleteItem', //单项删除
    'updateItem', //更新数据
    'editById', //更新数据，调接口
    'openMenuDialog', //操作权限
    'openDataColumnMenuDialog', //字段权限
    'handleStatusChange', //更改状态
    'handleQuery', //刷新列表数据
    'updatePassword', //修改密码
    'viewPosition', //查看位置
    'showRowDetail', //弹窗展示
    'confirm', //确认操作
    'showPic', //展示图片
    'cancel', //取消
    'defaultHandle', //默认操作
    'showRowMenuDialog', //弹窗显示菜单
    'handleCellClick', //选中当前行
    'confirmDialog', //自定义提示弹窗
    'showRowObjectDialog', //对象弹窗
    'approvalOperation', //下级审批操作
    'customTableTemplateHeader', //编辑报表模板弹窗
    'fileDownload', //下载文件
    'editExamineTemplate', //编辑考核模板
    'editExamineProject', //编辑考核项目
    'score', //考核项目打分
    'publish', //考核项目发布
    'toLineSchedule', //排程
    'toTrackMap', //查看轨迹
    'meeting', //查看会议纪要
    'editSafeStandard', //编辑安全标准
    'viewSafeStandard', //查看安全标准
    'editInspectionWarehouse', //管理适用仓库
    'editAuth', //编辑带权限的
    'showCanAllocateDetail', //查看可配板数
    'viewYkp', //查看移库计划详情
    'viewPlanLimit', //查看约束调整
    'transportPlanDetail', //规划详情
    'viewInfo', //查看详情
    'outFleetAuditSettingEdit', //审批流程详情
    'editSubsidy', //司机计算编辑补贴
    'editDispatch', //编辑调度
    'editGenerateSettlement', //编辑生成结算单
    'viewGenerateSett', //查看生成结算单
    'editGenerateStatement', //编辑对账单
    'editRepairStatement', //编辑维修对账单
    'viewGenerateStatement', //查看对账单
    'viewRepairGenerateStatement', //查看维修结算单
    'editGenerateSubsidyStatement', //编辑外协对账单
    'viewGenerateSubsidyStatement', //查看外协对账单
    'editOpenInvoiceManagment', //编辑开票管理
    'editOpenInvoiceRepairManagement', //维修编辑开票管理
    'outFleetAuditSettingCopy', //复制审批流程
    'editOpenInvoiceOutsourcingManagement', //外协编辑开票管理
    'editFine', //新增
    'editQualityLoss', //质损编辑
    'editOutsourcingPayment', //编辑外协付款
    'editSubsidyPayment', //编辑补贴付款
    'paymentPart', //外协付款明细
    'resignationEmployeeManagement', //离职
    'editFleetReport', //编辑车队维修
    'viewFleetReport', //查看车队维修
    'editSupplierRepair', //编辑供应商维修
    'adjustCostSupplierRepair', //调整费用供应商维修
    'editEmployeeManagement', //编辑工资管理
    'dynamicAuditDetail', //动态详情
    'columnDynamicAuditDetail', //数据列动态详情
    'unBindTire', //轮胎列表-解绑
    'modifyCustomerIncome', //客户结算-运费计算-调整收款
    'compareModifyCustomerIncome', //客户结算-运费计算-调整收款-对比
    'modifyCarrierPayment', //外协结算-运费计算-调整收款
    'editInspectionConfiguration', //编辑配置
    'copyInspectionConfiguration', //复制配置
    'editOrderManagement', //订单管理-编辑
    'formulaCustomerContract', //客户阶梯价格
    'formulaOutsourcingContract', //外协阶梯价格
    'showRowMenuFormDialog', //弹窗显示表单
    'viewReconciliationPayment', //
    'editSettlementAmount', //编辑对账金额
    'importFile', //车队管理-里程管理-导入模版
    'resetQuery', //重置查询，刷新列表
    'sort-change', // 添加排序事件
    'oilLoanEdit', // 油费借支
    'etcLoanEdit', // 路桥费借支
  ])
  const props = defineProps({
    requestUri: {
      type: String,
      default: '',
    },
    tableData: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    /**
     * table 配置项
     */
    tableConfig: {
      require: true,
      type: Object as PropType<TableConfig>,
      default: () => {
        return {
          showSort: true,
        }
      },
    },
    highlightCurrentRow: {
      type: Boolean,
      default: false,
    },
    /**
     * 加载中
     */
    loading: {
      require: true,
      type: Boolean,
      default: true,
    },
    /**
     * 高度
     */
    height: {
      type: String,
      default: '60vh',
    },
    tableBorder: {
      type: Boolean,
      default: true,
    },
    /**
     * 操作按钮权限
     */
    buttonPermissionGroup: {
      require: false,
      type: Array as PropType<MenuVO[]>,
      default: () => {
        return []
      },
    },
    /**
     * 顶部按钮权限
     */
    topButtonPermissionGroup: {
      require: false,
      type: Array as PropType<MenuVO[]>,
      default: () => {
        return []
      },
    },
    /**
     * 禁用行复选框
     */
    selectable: {
      require: false,
      default: () => {
        return (row: any, index: number) => {
          return true
        }
      },
    },
    /**
     * 是否需要显示已读未读标记
     */
    showReadStatus: {
      require: false,
      type: Boolean,
      default: false,
    },
  })
  const state = reactive({
    sortField: {} as Record<string, string | null>,
    orderBys: [] as { column: string; direction: string | undefined }[],
  })
  const picDialogComponent = ref()
  const showPic = (row: any, name: string) => {
    if (row[name]) {
      imageList.value = row[name] ? row[name].map((item: any) => item.url) : []
      picDialogComponent.value.picDialogVisible = true
    } else {
      ElMessage.warning('当前未上传证件')
    }
  }
  // Calculate operation width function moved here, before its usage
  // 计算操作列宽度的方法
  const calculateOperationWidth = () => {
    //如果按钮数<=3,则获取所有按钮的字符数*单个字符数的宽度
    if (props.buttonPermissionGroup.length <= 3) {
      const width = props.buttonPermissionGroup.reduce((acc, item) => acc + (item.meta?.title?.length ?? 0) * 12, 20)
      operationWidth.value = width < 80 ? '80px' : width + 'px'
    } else {
      //如果按钮数>3,则操作列宽度为前三个按钮的宽度
      const width = props.buttonPermissionGroup.slice(0, 3).reduce((acc, item) => acc + (item.meta?.title?.length ?? 0) * 12, 30)
      operationWidth.value = width < 80 ? '80px' : width + 'px'
    }
  }

  // 监听按钮组变化
  watch(
    () => props.buttonPermissionGroup,
    () => {
      calculateOperationWidth()
    },
    { immediate: true },
  )

  watch(
    () => props.topButtonPermissionGroup,
    () => {
      //props.topButtonPermissionGroup 存在item.meta?.purpose === 'definePageHeader'项则showCustomTableHeaderBtn=true
      showCustomTableHeaderBtn.value = props.topButtonPermissionGroup.some((item) => item.meta?.purpose === 'definePageHeader')
    },
    {
      immediate: true,
      deep: true,
    },
  )
  // 组件挂载时计算宽度
  onMounted(() => {
    calculateOperationWidth()
    resetTableColumnWidths()
  })

  // 组件被激活时重新计算宽度
  onActivated(() => {
    calculateOperationWidth()
    resetTableColumnWidths()
  })
  /**
   * 选中的列表项
   */
  const selectRowIds = ref<any[]>([])
  const selectRowData = ref<any[]>([])
  /**
   * 选中的列表项
   * @param e 选中的列表数组
   */
  const handleSelectionChange = (e: any[]) => {
    selectRowIds.value = e.map((item) => item.rowIndex)
    selectRowData.value = e

    emit('batchDelete', e)
    emit('handleSelectionChange', e)
  }
  const handleSortChange = ({ column, order, prop }: { column: any; order: string; prop: string }) => {
    // 触发的排序和缓存的排序相同时，取消该字段的排序
    if (!order || state.sortField[prop] === order) {
      state.sortField[prop] = null
    } else {
      state.sortField[prop] = order
    }

    // 查找对应的表格项，检查是否有自定义排序列名
    let sortColumn = prop
    const findTableItem = (items: any[]): string => {
      for (const item of items) {
        if (item.name === prop) {
          if (item.attributes?.sortColumn) {
            return item.attributes.sortColumn
          }
          return prop
        }
        if (item.children && item.children.length > 0) {
          const childResult: string = findTableItem(item.children)
          if (childResult !== prop) {
            return childResult
          }
        }
      }
      return prop
    }

    if (props.tableConfig.tableItem && props.tableConfig.tableItem.length > 0) {
      sortColumn = findTableItem(props.tableConfig.tableItem)
    }

    let direction = undefined
    // 更新或移除对应的排序项
    const existingIndex = state.orderBys.findIndex((item) => item.column === sortColumn)

    if (existingIndex !== -1) {
      // 如果存在该列，则更新或移除
      if (!order) {
        // 取消排序，移除该项
        state.orderBys.splice(existingIndex, 1)
      } else {
        // 更新排序方向
        direction = order === 'ascending' ? 'asc' : 'desc'
        state.orderBys[existingIndex].direction = direction
      }
    } else if (order) {
      // 如果不存在且需要排序，则添加新项
      direction = order === 'ascending' ? 'asc' : 'desc'
      state.orderBys.push({
        column: sortColumn,
        direction: direction,
      })
    }
    emit('sort-change', state.orderBys) //调用后端查询接口
  }
  // 重置表格列宽度
  const resetTableColumnWidths = () => {
    if (dataTableRef.value) {
      // 强制重新渲染表格
      refreshTable.value = false
      nextTick(() => {
        refreshTable.value = true
      })
    }
  }
  /**
   * 复制
   */
  const copy = (name: string) => {
    navigator.clipboard
      .writeText(name)
      .then((res) => {
        ElMessage.success('复制成功')
      })
      .catch((err) => {})
  }
  const customTableHeaderRef = ref()
  //自定义表头
  const showCustomTableHeader = () => {
    customTableHeaderRef.value.state.visible = true
    customTableHeaderRef.value.getCustomTableHeader(props.requestUri)
  }
  // 获取可见的按钮
  const getVisibleButtons = (buttons: any[], row: any) => {
    const filteredButtons = buttons.filter(
      (item) =>
        item.meta?.operation &&
        (!item.meta.dependsOn ||
          (item.meta.dependsOn &&
            item.meta.dependsOn.every((depend: DependsOn) => {
              if (depend.dependFrom === 'listData') {
                let target = row[depend.field!]
                if (!depend.operator) {
                  depend.operator = 'eq'
                }
                let when = depend.when!
                return operatorCalculate(target, when, depend.operator)
              }
            }))) &&
        ((item.meta.purpose === 'editAuth' && row.type !== '目录') || item.meta.purpose !== 'editAuth'),
    )

    // 如果按钮数量小于等于3，显示所有按钮
    if (filteredButtons.length <= 3) {
      return filteredButtons
    }
    // 否则只显示前2个按钮
    return filteredButtons.slice(0, 2)
  }

  // 获取隐藏的按钮
  const getHiddenButtons = (buttons: any[], row: any) => {
    const filteredButtons = buttons.filter(
      (item) =>
        item.meta?.operation &&
        (!item.meta.dependsOn ||
          (item.meta.dependsOn &&
            item.meta.dependsOn.every((depend: DependsOn) => {
              if (depend.dependFrom === 'listData') {
                let target = row[depend.field!]
                if (!depend.operator) {
                  depend.operator = 'eq'
                }
                let when = depend.when!
                return operatorCalculate(target, when, depend.operator)
              }
            }))) &&
        ((item.meta.purpose === 'editAuth' && row.type !== '目录') || item.meta.purpose !== 'editAuth'),
    )

    // 如果按钮数量小于等于3，不显示更多按钮
    if (filteredButtons.length <= 3) {
      return []
    }
    // 否则返回第3个及以后的按钮
    return filteredButtons.slice(2)
  }
  const sideBarStore = useSideBarStore()
  const buttonGroupStore = useButtonGroupStore()
  const formStore = useFormStore()
  const { searchParams } = toRaw(formStore)
  /**
   * 操作
   * @param type 类型 add、edit、delete、view、auth、password
   * @param row 行数据
   * @param position  按钮位置（listTop-顶部按钮；listRight-列表右侧按钮;listTabTop-列表tab顶部按钮；listTabRight-列表tab右侧按钮）
   * @param meta 获取按钮中meta中的uri,如果存在，则使用按钮的uri,没有则使用页面级uri
   * @param menu
   */
  const operation = async (type: string | undefined, row: any, position?: string, meta?: MetaVO, menu?: MenuVO) => {
    //设置表单中是否需要展示地图，mapType=geoFence 的时候显示地图
    buttonGroupStore.setShowMap(meta!.mapType!)
    let query: { [key: string]: any } = {}
    const storeData = sideBarStore.$state.storeDialogFormParams || {}
    const storeFormData = sideBarStore.$state.mergeDialogFormParams || {}
    //获取需要传递的formData中的属性保存到store中
    let mergeDialogFormParams: { [key: string]: any } = {}
    let storeDialogFormParams: { [key: string]: any } = {}
    if (meta!.form?.query) {
      for (let item of meta!.form?.query!) {
        composeRequestParams(query, item, menu, storeData, row, searchParams.value)
      }
    }
    //需要传递的formData中的属性保存到store中
    if (meta!.form?.formData) {
      for (let item of meta!.form!.formData!) {
        composeRequestParams(mergeDialogFormParams, item, menu, storeData, row, searchParams.value)
      }
    }
    //需要传递的storeData中的属性保存到store中
    if (meta!.form?.storeData) {
      for (let item of meta!.form!.storeData!) {
        composeRequestParams(storeDialogFormParams, item, menu, storeData, row, searchParams.value)
      }
    }
    sideBarStore.$patch((state) => {
      state.btnMenuId = meta!.form?.menuId!
      state.btnMenuQuery = query
      state.mergeDialogFormParams = Object.assign(storeFormData, mergeDialogFormParams) //获取需要传递的formData中的属性保存到store中
      state.storeDialogFormParams = Object.assign(storeData, storeDialogFormParams) //获取需要传递的全局存储的属性保存到store中
    })
    switch (type) {
      case 'topAdd':
        emit('addItem', row, position, meta)
        break
      //修改当前行
      case 'topEdit':
        emit('updateItem', row, position, meta)
        break
      //修改当前行,调接口
      case 'editById':
        emit('editById', row, position, menu)
        break
      //删除当前行
      case 'delete':
        ElMessageBox.confirm('确认删除已选中的数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          emit('deleteItem', row)
        })
        break
      //查看
      case 'view':
        emit('addItem', row, position, meta)
        break
      //操作权限
      case 'assignMenuPermission':
        emit('openDataColumnMenuDialog', row)
        break
      //字段权限
      case 'assignDataColumnPermission':
        emit('openMenuDialog', row)
        break
      //修改密码
      case 'password':
        emit('updatePassword', row)
        break
      //查看位置
      case 'viewPosition':
        emit('viewPosition', row)
        break
      //确认
      case 'confirm':
        emit('confirm', row, position, meta)
        break
      //自定义提示弹窗
      case 'confirmDialog':
        if (meta?.form?.confirmContent) {
          ElMessageBox.confirm(meta?.form?.confirmContent, meta?.form?.confirmTitle, {
            confirmButtonText: meta?.form?.btns![0].label,
            cancelButtonText: meta?.form?.btns![1].label,
            showCancelButton: !!meta?.form?.btns![1],
            showConfirmButton: !!meta?.form?.btns![0],
            type: 'warning',
          }).then(() => {
            emit('confirmDialog', row, position, meta)
          })
        } else {
          emit('confirmDialog', row, position, meta)
        }
        break
      //取消
      case 'circleCloseFilled':
        ElMessageBox.confirm('确认取消已选中的数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          emit('cancel', row, position, meta)
        })
        break
      //跳转页面
      case 'btnJump':
        let listSelect = {} as any
        //获取查询条件并跳转
        let btnJumpQuery: { [key: string]: any } = {}
        //获取需要传递的formData中的属性保存到store中
        let mergeFormParams: { [key: string]: any } = {}
        let storeFormParams: { [key: string]: any } = {}
        if (meta?.form?.formUri) {
          //定义传递的参数
          let params = {} as any
          meta?.form?.params?.map((_item) => {
            composeRequestParams(params, _item, null, null, row, null)
          })

          const resData = await globalRequestUrlApi(params, meta?.form?.method!, meta?.form?.formUri).then(async (res) => {
            return res.data
          })
          //
          //遍历listSelect，取第一项的数据
          if (meta.form?.listSelect?.length > 0) {
            if (meta.form.listSelect[0].dependFrom === 'responseData') {
              listSelect[meta.form.listSelect[0].name!] = resData[meta.form.listSelect[0].name!]
            } else if (meta.form.listSelect[0].dependFrom === 'listData') {
              if (meta.form.listSelect[0].value === '$#$#') {
                listSelect[meta.form.listSelect[0].name!] = row[meta.form.listSelect[0].name!]
              }
            }
          }

          // formStore.$patch(state => {
          //   state.defaultTableIds = listSelect; //将默认选中的列表ids存到store,切换菜单要重置
          //   state.routerParams = btnJumpQuery; //获取查询条件并跳转
          //   state.mergeFormData = mergeFormParams; //获取需要传递的formData中的属性保存到store中
          // });
          // router.push({
          //   path: meta.form?.targetField!,
          //   query: {
          //     time: new Date().getTime(), //获取当前时间戳,防止不刷新页面
          //   },
          // });
        }
        //需要传递的formData中的属性保存到store中
        if (meta!.form?.formData) {
          for (let item of meta!.form?.formData!) {
            composeRequestParams(mergeFormParams, item, null, null, row, searchParams.value)
          }
        }
        //需要传递的查询条件保存到store中
        if (meta!.form?.query) {
          for (let item of meta!.form?.query!) {
            composeRequestParams(btnJumpQuery, item, null, null, row, searchParams.value)
          }
        }
        //需要传递的storeData中的属性保存到store中
        if (meta!.form?.storeData) {
          for (let item of meta!.form?.storeData!) {
            composeRequestParams(storeFormParams, item, null, null, row, searchParams.value)
          }
        }

        formStore.$patch((state) => {
          state.defaultTableIds = listSelect //将默认选中的列表ids存到store,切换菜单要重置
          state.routerParams = btnJumpQuery //获取查询条件并跳转
          state.mergeFormData = mergeFormParams //获取需要传递的formData中的属性保存到store中
          state.storeFormParams = storeFormParams //获取需要传递的全局存储的属性保存到store中
        })
        router.push({
          path: meta!.form?.targetField!,
          query: {
            time: new Date().getTime(), //获取当前时间戳,防止不刷新页面
          },
        })
        // linkTo(row);
        break

      case 'menuDialog':
        emit('showRowMenuDialog', query, { label: meta!.form?.title }, menu)
        break
      // 下级审批操作
      case 'approvalOperation':
        emit('approvalOperation', row, position, menu)
        break
      // 编辑报表模板
      case 'customTableTemplateHeader':
        emit('customTableTemplateHeader', row, position, menu)
        break
      // 下载文件
      case 'fileDownload':
        emit('fileDownload', row, position, menu, type)
        break
      // 下载文件流
      case 'streamDownload':
        emit('fileDownload', row, position, menu, type)
        break
      // 编辑考核模板
      case 'editExamineTemplate':
        emit('editExamineTemplate', row, position, menu)
        break
      // 编辑考核项目
      case 'editExamineProject':
        emit('editExamineProject', row, position, menu)
        break
      // 复制表单
      case 'copy':
        emit('copy', row, position, menu)
        break
      // 考核项目打分
      case 'score':
        emit('score', row, position, menu)
        break
      // 考核项目发布
      case 'publish':
        ElMessageBox.confirm('是否确认发布?', '发布确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          emit('publish', row, position, meta)
        })
        break
      // 排程
      case 'toLineSchedule':
        emit('toLineSchedule', row, position, menu)
        break
      // 查看轨迹弹窗
      case 'toTrackMap':
        emit('toTrackMap', row, position, menu)
        break
      // 编辑安全标准
      case 'editSafeStandard':
        emit('editSafeStandard', row, position, menu)
        break
      // 查看安全标准
      case 'viewSafeStandard':
        emit('viewSafeStandard', row, position, menu)
        break
      // 管理适用仓库
      case 'editInspectionWarehouse':
        emit('editInspectionWarehouse', row, position, menu)
        break
      // 编辑带权限的
      case 'editAuth':
        emit('editAuth', row, position, menu)
        break
      // 查看移库计划详情
      case 'viewYkp':
        emit('viewYkp', row, position, menu)
        break
      // 查看约束调整
      case 'viewPlanLimit':
        emit('viewPlanLimit', row, position, menu)
        break
      // 长安民生 - 规划详情
      case 'transportPlanDetail':
        emit('transportPlanDetail', query, { label: '规划详情' })
        break
      // 长安民生 - 查看失败信息
      case 'viewInfo':
        emit('viewInfo', row, position, menu)
        break
      // 编辑审批流程
      case 'outFleetAuditSettingEdit':
        emit('outFleetAuditSettingEdit', row, position, menu)
        break
      // 编辑审批流程
      case 'editSubsidy':
        emit('editSubsidy', row, position, menu)
        break
      // 智能调度-编辑调度
      case 'editDispatch':
        emit('editDispatch', row, position, menu)
        break
      // 财务管理-司机补贴-补贴对账-编辑
      case 'editGenerateSettlement':
        emit('editGenerateSettlement', row, position, menu)
        break
      // 财务管理-客户结算-对账管理-编辑
      case 'editGenerateStatement':
        emit('editGenerateStatement', row, position, menu)
        break
      // 财务管理-维修结算-对账管理-编辑
      case 'editRepairStatement':
        emit('editRepairStatement', row, position, menu)
        break
      // 财务管理-外协结算-对账管理-编辑
      case 'editGenerateSubsidyStatement':
        emit('editGenerateSubsidyStatement', row, position, menu)
        break
      //财务管理-客户结算-开票管理-发票管理
      case 'editOpenInvoiceManagment':
        emit('editOpenInvoiceManagment', row, position, menu)
        break
      //财务管理-维修结算-开票管理-发票管理
      case 'editOpenInvoiceRepairManagement':
        emit('editOpenInvoiceRepairManagement', row, position, menu)
        break
      //系统配置-配置管理-审批流程-复制
      case 'outFleetAuditSettingCopy':
        emit('outFleetAuditSettingCopy', row, position, menu)
        break
      //外协编辑开票管理
      case 'editOpenInvoiceOutsourcingManagement':
        emit('editOpenInvoiceOutsourcingManagement', row, position, menu)
        break
      //罚款配置-编辑
      case 'editFine':
        emit('editFine', row, position, menu)
        break
      // 质损编辑
      case 'editQualityLoss':
        emit('editQualityLoss', row, position, menu)
        break
      // 外协付款-编辑
      case 'editOutsourcingPayment':
        emit('editOutsourcingPayment', row, position, menu)
        break
      // 补贴付款-编辑
      case 'editSubsidyPayment':
        emit('editSubsidyPayment', row, position, menu)
        break
      // 行政管理-员工管理-离职
      case 'resignationEmployeeManagement':
        emit('resignationEmployeeManagement', row, position, menu)
        break
      // 车队管理-维修管理-车队维修
      case 'editFleetReport':
        emit('editFleetReport', row, position, menu)
        break
      // 车队管理-维修管理-供应商维修
      case 'editSupplierRepair':
        emit('editSupplierRepair', row, position, menu)
        break
      // 车队管理-维修管理-调整费用供应商维修
      case 'adjustCostSupplierRepair':
        emit('adjustCostSupplierRepair', row, position, menu)
        break
      // 行政管理-工资管理-编辑
      case 'editEmployeeManagement':
        emit('editEmployeeManagement', row, position, menu)
        break
      // 动态审批详情
      case 'dynamicAuditDetail':
        emit('dynamicAuditDetail', row, position, menu)
        break
      // 解绑轮胎
      case 'unBindTire':
        emit('unBindTire', row, position, menu)
        break
      // 客户结算-运费计算-调整收款
      case 'modifyCustomerIncome':
        emit('modifyCustomerIncome', row, position, menu, 'customer')
        break
      // 客户结算-运费计算-客户在线对比-调整收款-对比
      case 'compareModifyCustomerIncome':
        emit('compareModifyCustomerIncome', row, position, menu, 'customer')
        break
      // 外协结算-运费计算-调整收款
      case 'modifyCarrierPayment':
        emit('modifyCarrierPayment', row, position, menu, 'carrier')
        break
      // 编辑配置
      case 'editInspectionConfiguration':
        emit('editInspectionConfiguration', row, position, menu)
        break
      // 复制配置
      case 'copyInspectionConfiguration':
        emit('copyInspectionConfiguration', row, position, menu)
        break
      // 订单管理-编辑
      case 'editOrderManagement':
        emit('editOrderManagement', row, position, menu)
        break
      //客户阶梯价格
      case 'formulaCustomerContract':
        emit('formulaCustomerContract', row, position, menu)
        break
      //外协阶梯价格
      case 'formulaOutsourcingContract':
        emit('formulaOutsourcingContract', row, position, menu)
        break
      // 编辑对账金额
      case 'editSettlementAmount':
        emit('editSettlementAmount', row, position, menu)
        break
      // 油费借支
      case 'oilLoanEdit':
        emit('oilLoanEdit', row, position, menu)
        break
      // 路桥费借支
      case 'etcLoanEdit':
        emit('etcLoanEdit', row, position, menu)
        break
      // 默认操作
      default:
        emit('defaultHandle', row, position, menu)
        break
    }
  }
  const refreshPageTableColumn = () => {
    emit('refreshPageTableColumn')
  }
</script>
<style scoped lang="scss">
  .read-status {
    width: 8px;
    height: 8px;
    margin-right: 3px;
    background-color: darkseagreen;
    border-radius: 50%;
  }

  :deep(.el-table .el-table__header-wrapper .el-checkbox) {
    display: none;
  }

  :deep(.el-tag--dark.el-tag--success) {
    border-color: transparent !important;
  }

  :deep(.primary-row) {
    --el-table-tr-bg-color: var(--el-color-primary-light-9);
  }

  :deep(.el-table__header tr th:nth-child(1) .cell) {
    display: var(--hideFirstColumn);
  }

  // 单元格样式
  :deep(.el-table__cell) {
    position: static !important; // 解决el-image 和 el-table冲突层级冲突问题
  }

  :deep(.el-button + .el-button) {
    margin-left: 0 !important;
  }

  /* 调整合计行样式 */
  .custom-summary-table .el-table__footer td {
    background-color: #fafafa;
    font-weight: bold;
  }

  /* 第二行合计特殊样式 */
  .custom-summary-table .el-table__footer tr:nth-child(2) td {
    background-color: #e6f7ff;
  }

  /* 视频缩略图样式 */
  .video-thumbnail {
    width: 50px;
    height: 50px;
    background-color: #f5f7fa;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      background-color: #e6f7ff;

      .play-icon {
        transform: scale(1.2);
        color: var(--el-color-primary);
      }
    }

    .play-icon {
      font-size: 24px;
      color: #909399;
      transition: all 0.3s;
    }
  }

  .sort-priority {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    margin-left: 4px;
    background-color: var(--el-color-primary);
    color: white;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
  }

  :deep(.sort-column) {
    .sort-priority {
      background-color: var(--el-color-primary);
    }
  }
</style>
