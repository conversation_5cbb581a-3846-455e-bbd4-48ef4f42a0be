<!--
 * @Author: llm
 * @Date: 2025-04-22 17:05:30
 * @LastEditors: llm
 * @LastEditTime: 2025-04-22 17:05:50
 * @Description: ETC票管理
-->
<template>
  <GlobalPage :pageType="state.pageType" />
</template>
<script lang="ts" setup>
  import { getCurrentInstance, onBeforeMount, reactive } from 'vue'
  import GlobalPage from '@/components/GlobalPage/index.vue'
  defineOptions({
    name: 'OutboundFinancialManagementLoanManagementEtcManagementEtcTicketManagement',
    inheritAttrs: false,
  })
  const { proxy }: any = getCurrentInstance()
  const state = reactive({
    pageType: '',
    pageName: '',
  })
  onBeforeMount(() => {
    state.pageType = proxy.$sideBarStore.$state.pageType
  })
</script>
