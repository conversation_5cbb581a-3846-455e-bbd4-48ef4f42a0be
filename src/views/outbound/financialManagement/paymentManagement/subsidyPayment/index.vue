<!--
 * @Author: llm
 * @Date: 2025-02-18 10:05:46
 * @LastEditors: llm
 * @LastEditTime: 2025-02-18 10:05:59
 * @Description: 
-->
<template>
  <GlobalPage :pageType="state.pageType" />
</template>
<script lang="ts" setup>
  import { getCurrentInstance, onBeforeMount, reactive } from 'vue'
  import GlobalPage from '@/components/GlobalPage/index.vue'
  defineOptions({
    name: 'OutboundFinancialManagementPaymentManagementSubsidyPayment',
    inheritAttrs: false,
  })
  const { proxy }: any = getCurrentInstance()
  const state = reactive({
    pageType: '',
    pageName: '',
  })
  onBeforeMount(() => {
    state.pageType = proxy.$sideBarStore.$state.pageType
  })
</script>
