<!--
 * @Author: llm
 * @Date: 2025-04-11 16:01:01
 * @LastEditors: llm
 * @LastEditTime: 2025-04-11 16:03:51
 * @Description: 
-->
<template>
  <GlobalPage :pageType="state.pageType" />
</template>
<script lang="ts" setup>
  import { getCurrentInstance, onBeforeMount, reactive } from 'vue'
  import GlobalPage from '@/components/GlobalPage/index.vue'
  defineOptions({
    name: 'OutboundFinancialManagementScatteredOrderManagementPaymentManagementReconciliationPayment',
    inheritAttrs: false,
  })
  const { proxy }: any = getCurrentInstance()
  const state = reactive({
    pageType: '',
    pageName: '',
  })
  onBeforeMount(() => {
    state.pageType = proxy.$sideBarStore.$state.pageType
  })
</script>
