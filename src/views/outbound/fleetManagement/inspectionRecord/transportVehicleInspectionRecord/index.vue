<!--
 * @Author: llm
 * @Date: 2025-03-10 17:44:01
 * @LastEditors: llm
 * @LastEditTime: 2025-03-10 17:44:40
 * @Description: 
-->
<!-- GlobalPage -->
<template>
  <GlobalPage :pageType="state.pageType" />
</template>
<script lang="ts" setup>
  import { getCurrentInstance, onBeforeMount, reactive } from 'vue'
  import GlobalPage from '@/components/GlobalPage/index.vue'
  defineOptions({
    name: 'OutboundFleetManagementTransportVehicleInspectionRecord',
    inheritAttrs: false,
  })
  const { proxy }: any = getCurrentInstance()
  const state = reactive({
    pageType: '',
    pageName: '',
  })
  onBeforeMount(() => {
    state.pageType = proxy.$sideBarStore.$state.pageType
  })
</script>
