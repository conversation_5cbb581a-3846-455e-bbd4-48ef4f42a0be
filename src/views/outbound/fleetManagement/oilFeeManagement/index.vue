<!--
 * @Author: llm
 * @Date: 2024-11-28 15:13:15
 * @LastEditors: llm
 * @LastEditTime: 2025-04-08 17:03:48
 * @Description:
-->

<template>
  <GlobalPage :pageType="state.pageType" />
</template>
<script lang="ts" setup>
  import { getCurrentInstance, onBeforeMount, reactive } from 'vue'
  import GlobalPage from '@/components/GlobalPage/index.vue'
  defineOptions({
    name: 'OutboundFleetManagementOilFeeManagement',
    inheritAttrs: false,
  })
  const { proxy }: any = getCurrentInstance()
  const state = reactive({
    pageType: '',
    pageName: '',
  })
  onBeforeMount(() => {
    state.pageType = proxy.$sideBarStore.$state.pageType
  })
</script>
