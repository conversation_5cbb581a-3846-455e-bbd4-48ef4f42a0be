<!--
 * @Author: llm
 * @Date: 2025-01-17 16:41:45
 * @LastEditors: llm
 * @LastEditTime: 2025-01-17 16:41:58
 * @Description:
-->
<template>
  <GlobalPage :pageType="state.pageType" />
</template>
<script lang="ts" setup>
  import { getCurrentInstance, onBeforeMount, reactive } from 'vue'
  import GlobalPage from '@/components/GlobalPage/index.vue'
  defineOptions({
    name: 'OutboundFleetManagementSupplierRepairSupplierRepair',
    inheritAttrs: false,
  })
  const { proxy }: any = getCurrentInstance()
  const state = reactive({
    pageType: '',
    pageName: '',
  })
  onBeforeMount(() => {
    state.pageType = proxy.$sideBarStore.$state.pageType
  })
</script>
