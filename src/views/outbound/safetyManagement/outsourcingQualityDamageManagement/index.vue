<!--
 * @Author: llm
 * @Date: 2025-07-11 17:53:34
 * @LastEditors: llm
 * @LastEditTime: 2025-07-11 18:27:02
 * @Description: 
-->
<template>
  <GlobalPage :pageType="state.pageType" />
</template>
<script lang="ts" setup>
  import { getCurrentInstance, onBeforeMount, reactive } from 'vue'
  import GlobalPage from '@/components/GlobalPage/index.vue'
  defineOptions({
    name: 'OutboundSafetyManagementOutsourcingQualityDamageManagement',
    inheritAttrs: false,
  })
  const { proxy }: any = getCurrentInstance()
  const state = reactive({
    pageType: '',
    pageName: '',
  })
  onBeforeMount(() => {
    state.pageType = proxy.$sideBarStore.$state.pageType
  })
</script>
