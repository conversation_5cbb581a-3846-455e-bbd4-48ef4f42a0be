<!--
 * @Author: llm
 * @Date: 2025-06-17 09:52:26
 * @LastEditors: llm
 * @LastEditTime: 2025-06-30 10:07:36
 * @Description:
-->
<template>
  <GlobalPage :pageType="state.pageType" />
</template>
<script lang="ts" setup>
  import { getCurrentInstance, onBeforeMount, reactive } from 'vue'
  import GlobalPage from '@/components/GlobalPage/index.vue'

  defineOptions({
    name: 'OutboundStatisticsCenterOwnDispatchStatistics',
    inheritAttrs: false,
  })
  const { proxy }: any = getCurrentInstance()
  const state = reactive({
    pageType: '',
    pageName: '',
  })
  onBeforeMount(() => {
    state.pageType = proxy.$sideBarStore.$state.pageType
  })
</script>
