<!--
 * @Author: llm
 * @Date: 2024-02-28 15:55:17
 * @LastEditors: llm
 * @LastEditTime: 2025-03-27 11:36:52
 * @Description: 路线规划
-->
<template>
  <div v-loading="boxLoading" :element-loading-text="routeLoadingText" class="TrackQuery">
    <div class="gutterRow">
      <div :class="isShowBox ? 'leftForm' : 'leftForm1'">
        <el-form :model="formData" :rules="rules" ref="ruleFormRef" class="queryCondition" :inline="true">
          <el-form-item label="选择位置方式" label-width="100px">
            <el-radio-group v-model="locationType" @change="selectLocationType">
              <el-radio value="1">位置选点</el-radio>
              <el-radio value="2">下拉选点</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="起点位置" prop="fromCity" label-width="80px" v-if="locationType === '1'">
            <div class="flex items-center">
              <el-input readonly v-model="formData.fromCity" placeholder="起点位置" @click="chooseAddress('start')" />
            </div>
          </el-form-item>
          <el-form-item label="终点位置" prop="toCity" label-width="80px" v-if="locationType === '1'">
            <div class="flex items-center">
              <el-input v-model="formData.toCity" placeholder="终点位置" @click="chooseAddress('end')" />
            </div>
          </el-form-item>
          <el-form-item label="起点位置" prop="fromCityCode" label-width="80px" v-if="locationType === '2'">
            <el-cascader
              ref="startCascader"
              v-model="formData.fromCityCode"
              :options="citySelectList"
              :props="propsHover"
              @change="handleCityChange($event, 1)"
            >
              <template #default="{ node, data }">
                <span>{{ data.label }}</span>
                <span v-if="!node.isLeaf">({{ data.children.length }})</span>
              </template>
            </el-cascader>
          </el-form-item>
          <el-form-item label="终点位置" prop="toCityCode" label-width="80px" v-if="locationType === '2'">
            <el-cascader ref="endCascader" v-model="formData.toCityCode" :options="citySelectList" :props="propsHover" @change="handleCityChange($event, 2)">
              <template #default="{ node, data }">
                <span>{{ data.label }}</span>
                <span v-if="!node.isLeaf">({{ data.children.length }})</span>
              </template>
            </el-cascader>
          </el-form-item>
          <div style="width: 100%; padding: 0 12px" class="flex justify-end">
            <el-button style="width: 120px" @click="resetForm">重置</el-button>
            <el-button style="width: 180px" type="primary" @click="submitForm">查询</el-button>
          </div>
        </el-form>
        <div class="tip-tag" @click="changeLeft">
          <ArrowLeft v-if="isShowBox" />
          <ArrowRight v-else />
        </div>
      </div>
      <baidu-map
        :class="isShowTopBottomBox ? 'bm-view-up' : 'bm-view-down'"
        :zoom="zoom"
        :center="{ lng: 105.01177, lat: 38.419774 }"
        :scroll-wheel-zoom="true"
        :continuous-zoom="true"
        :map-click="false"
        @init="handler"
      ></baidu-map>
    </div>

    <div style="position: relative">
      <div class="topBottomContraction" @click="changeChartBox">
        <el-icon v-if="isShowTopBottomBox"><ArrowDown /></el-icon>
        <el-icon v-if="!isShowTopBottomBox"><ArrowUp /></el-icon>
      </div>
      <div :class="isShowTopBottomBox ? 'topBox' : 'bottomBox'">
        <div class="buttonBox">
          <el-button
            size="small"
            :type="routeSelectionData.length < 2 ? 'info' : 'primary'"
            :disabled="routeSelectionData.length < 2"
            @click.stop="routeContrast"
            >路线对比</el-button
          >
          <el-button size="small" :type="isRouteView ? 'info' : 'primary'" :disabled="isRouteView" @click.stop="cancelContrast">取消对比</el-button>
        </div>
        <el-table
          ref="tableRef"
          border
          :highlight-current-row="true"
          :data="tableData"
          style="width: 100%"
          fit
          height="calc(30vh - 42px)"
          max-height="calc(30vh - 42px)"
          v-loading="false"
          element-loading-text="正在计算中..."
          :row-key="getRowKey"
          @selection-change="routeSelectionChange"
          size="small"
        >
          <el-table-column type="selection" align="center" width="60"></el-table-column>
          <el-table-column type="index" label="序号" width="80" align="center" />
          <el-table-column prop="no" label="子路线编号" align="center" />
          <el-table-column prop="totalDistanceKm" label="路线总里程(km)" align="center" />
          <el-table-column prop="highDistanceKm" label="高速路段里程(km)" align="center" />
          <el-table-column prop="highRatio" label="高速路占比" align="center" />
          <el-table-column prop="gsDistanceKm" label="国省道路里程(km)" align="center" />
          <el-table-column prop="gsRatio" label="国省道占比" align="center" />
          <el-table-column prop="trafficLights" label="红绿灯数" align="center" />
          <el-table-column prop="totalTimeStr" label="预计总耗时" align="center" />
          <el-table-column prop="driveTimeStr" label="行驶时间" align="center" />
          <el-table-column prop="restTimeStr" label="休息时间" align="center" />
          <el-table-column align="center" fixed="right" label="操作" width="180">
            <template #default="scope">
              <div class="operation">
                <el-button v-if="isRouteView" size="small" type="text" @click.stop="showRoute(scope.row)">查看路线</el-button>
                <el-button
                  v-if="JSON.stringify(newTrajectoryData) != '{}' && scope.row.no == newTrajectoryData.no"
                  size="small"
                  type="text"
                  @click.stop="shareTrajectory(scope.row)"
                >
                  分享路线
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 地图位置搜索 -->
    <map-search-dialog
      v-if="searchDialog.visible"
      :dialog="searchDialog"
      :pointData="currentChooseAddress"
      @closeMapDialog="closeMapDialog"
      @submitLocation="submitLocation"
    ></map-search-dialog>

    <!-- 共享路线弹窗 -->
    <el-dialog :draggable="true" v-model="sharedDialog" :before-close="sharedHandleClose" :close-on-click-modal="false" title="共享对象" width="550px">
      <el-form ref="shareFormData" :model="shareFormData" label-width="110px">
        <el-form-item label="路线名称：" required>
          <el-input v-model="shareFormData.name" class="maxInputStyle" maxlength="255" placeholder="请输入路线名称" show-word-limit />
        </el-form-item>
        <el-form-item label="共享对象：">
          <el-select v-model="shareFormData.shardDriverIdList" filterable multiple collapse-tags collapse-tags-tooltip clearable style="width: 360px">
            <el-option v-for="item in optionsList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <!-- 按钮 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="sharedCancel">取消</el-button>
          <el-button type="primary" @click="determine">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
  import { getFleetOutFleetDriverSelectOptionApi } from '@/api/financialManagement'
  import { routePlanDataApi, saveShareLine, districtSelectOptionApi } from '@/api/transitManagement/addressTrack'
  import { gcj02tobd09, bd09togcj02 } from '@/utils'
  export default {
    name: 'OutboundTransportInTransitRoutePlanning',
    data() {
      return {
        // 请求路线参数
        formData: {
          fromCity: '',
          fromCityCode: '',
          fromCoordinate: '',
          toCity: '',
          toCityCode: '',
          toCoordinate: '',
        },
        isShowBox: true, //控制显示隐藏
        isShowTopBottomBox: true,
        customMap: null,
        BMap: null,
        zoom: 5,
        boxLoading: false,
        rules: {
          fromCityCode: [{ required: true, message: '请选择起点位置', trigger: 'blur' }],
          fromCity: [{ required: true, message: '请选择起点位置', trigger: 'change' }],
          toCityCode: [{ required: true, message: '请选择终点位置', trigger: 'blur' }],
          toCity: [{ required: true, message: '请选择终点位置', trigger: 'change' }],
          routeName: [{ required: true, message: '请输入路线名称', trigger: 'blur' }],
        },
        locationType: '1',
        currentChooseAddressType: '1',
        currentChooseAddress: {
          address: '',
          coordinate: '',
        },
        searchDialog: {
          visible: false,
        },
        tableData: [], // table表格
        routeLoadingText: '路线正在规划中，请稍等......',
        routeSelectionData: [], //选中的路线
        newTrajectoryData: {}, // 当前点击的轨迹数据
        markers: [],
        sharedDialog: false, //控制选择共享对象
        optionsList: [], //共享对象列表
        shareFormData: {
          shardDriverIdList: [],
          name: '',
        },
        isRouteView: true, //控制查看路线按钮是否可见
        citySelectList: [],
        dingwei: {
          qidian: 'http://api.haodaoda.com/static/wx/mini/dingwei_qidian.png',
          tujingdian: 'http://api.haodaoda.com/static/wx/mini/dingwei_tujingdian.png',
          zhongdian: 'http://api.haodaoda.com/static/wx/mini/dingwei_zhongdian.png',
        },
        propsHover: {
          expandTrigger: 'hover',
        },
        routeLayerList: [], //多条路线信息
        // 以上新的
        routeList: [], // 路线信息列表
        dialogVisible: false, // 控制地图选点弹窗显示
        polyline: [], //轨迹线
      }
    },
    mounted() {
      this.getCitySelectList()
    },
    methods: {
      handler({ BMap, map }) {
        this.BMap = BMap
        this.customMap = map
      },
      // 改变左侧筛选的现实隐藏
      changeLeft() {
        this.isShowBox = !this.isShowBox
      },
      // 底部切换显示隐藏
      changeChartBox() {
        this.isShowTopBottomBox = !this.isShowTopBottomBox
      },

      // 获取省市区下拉
      getCitySelectList() {
        districtSelectOptionApi({})
          .then((res) => {
            const { data } = res
            this.citySelectList = data ? data : []
          })
          .catch(() => {
            this.citySelectList = false
          })
      },

      // 省市区选择
      handleCityChange(value, type) {
        switch (type) {
          case 1:
            const startNodes = this.$refs.startCascader.getCheckedNodes()
            this.formData.fromCity = startNodes[0].pathLabels.join('')
            this.formData.fromCityCode = startNodes[0].value
            this.formData.fromCoordinate = startNodes[0].data.desc
            this.addMarker([this.formData.fromCoordinate.split(',')[0], this.formData.fromCoordinate.split(',')[1]], 'start')
            break
          case 2:
            const endNodes = this.$refs.endCascader.getCheckedNodes()
            this.formData.toCity = endNodes[0].pathLabels.join('')
            this.formData.toCityCode = endNodes[0].value
            this.formData.toCoordinate = endNodes[0].data.desc
            this.addMarker([this.formData.toCoordinate.split(',')[0], this.formData.toCoordinate.split(',')[1]], 'end')
            break
          default:
            break
        }
      },

      // 获取设置的共享对象列表
      getUsersData() {
        getFleetOutFleetDriverSelectOptionApi({}).then((response) => {
          if (response && response.data.length > 0) {
            this.optionsList = response.data
          } else {
            this.optionsList = []
          }
        })
      },

      // 关闭子弹窗
      closeMapDialog() {
        this.searchDialog.visible = false //关闭弹窗
      },

      // 起终点选择
      chooseAddress(type) {
        if (type !== this.currentChooseAddressType) {
          this.currentChooseAddress.address = ''
          this.currentChooseAddress.coordinate = ''
        }
        this.currentChooseAddressType = type
        this.searchDialog.visible = true
      },

      // 每行唯一标识key
      getRowKey(row) {
        return row.no
      },

      // 子弹窗确认
      submitLocation(data) {
        if (data) {
          this.currentChooseAddress.address = data.address
          this.currentChooseAddress.coordinate = data.coordinate
          if (this.currentChooseAddressType === 'start') {
            this.formData.fromCity = data.address
            this.formData.fromCoordinate = data.coordinate
            this.addMarker([data.coordinate.split(',')[0], data.coordinate.split(',')[1]], 'start')
          } else if (this.currentChooseAddressType === 'end') {
            this.formData.toCity = data.address
            this.formData.toCoordinate = data.coordinate
            this.addMarker([data.coordinate.split(',')[0], data.coordinate.split(',')[1]], 'end')
          }
          this.closeMapDialog()
        }
      },

      // 点击查询 - 路线规划
      submitForm() {
        this.$refs.ruleFormRef.validate((valid) => {
          if (valid) {
            //清除地图
            this.customMap.clearOverlays()
            this.tableData = []
            if (this.markers.length > 0) {
              this.markers.forEach((marker) => {
                this.customMap.addOverlay(marker)
              })
            }
            let params = {
              fromCoordinate: this.formData.fromCoordinate,
              toCoordinate: this.formData.toCoordinate,
            }
            // 坐标转换
            if (this.formData.fromCoordinate && this.formData.toCoordinate) {
              let startPoint = this.formData.fromCoordinate.split(',')
              let endPoint = this.formData.toCoordinate.split(',')
              params.fromCoordinate = bd09togcj02(startPoint[0], startPoint[1]).join(',') // 开始坐标
              params.toCoordinate = bd09togcj02(endPoint[0], endPoint[1]).join(',') // 结束坐标
            }
            this.boxLoading = true
            routePlanDataApi(params)
              .then((res) => {
                const { data } = res
                this.boxLoading = false
                this.tableData = data.routeList ? data.routeList : []
              })
              .catch(() => {
                this.boxLoading = false
              })
          } else {
            console.log('error submit!')
          }
        })
      },

      // 重置
      resetForm() {
        this.$refs.ruleFormRef.resetFields()
        this.currentChooseAddress.address = ''
        this.currentChooseAddress.coordinate = ''
        this.formData.fromCity = ''
        this.formData.fromCityCode = ''
        this.formData.fromCoordinate = ''
        this.formData.toCity = ''
        this.formData.toCityCode = ''
        this.formData.toCoordinate = ''
        //清除地图所有覆盖物
        this.customMap.clearOverlays()
        this.markers = []
        this.tableData = []
      },

      // marker 图标展示
      addMarker(location, type) {
        // 先删除
        if (type === 'start') {
          this.customMap.removeOverlay(this.markers[0])
          this.markers.splice(0, 1)
        }
        if (type === 'end') {
          this.customMap.removeOverlay(this.markers[1])
          this.markers.splice(1, 1)
        }
        let _marker = new BMap.Icon(
          type == 'start' ? this.dingwei.qidian : type == 'end' ? this.dingwei.zhongdian : this.dingwei.tujingdian, // 图标路径
          new BMap.Size(36, 48), // 图标大小
          {
            anchor: new BMap.Size(18, 48), // 设置图标的定位点（偏移）
            imageOffset: new BMap.Size(0, 0), // 图标图片的偏移
            imageSize: { width: 35, height: 48 },
          },
        )
        const bdPoint = gcj02tobd09(Number(location[0]), Number(location[1]))
        const point = new BMap.Point(bdPoint[0], bdPoint[1]) // 经纬度
        const marker = new BMap.Marker(point, { icon: _marker, offset: new BMap.Size(0, 12) })
        // 后添加
        if (type == 'start') {
          this.markers[0] = marker
        } else {
          this.markers[1] = marker
        }
        this.customMap.addOverlay(marker)
      },

      // 位置选择切换
      selectLocationType(e) {
        this.locationType = e
        this.resetForm()
      },

      // 路线多选
      routeSelectionChange(data) {
        if (data && data.length > 0) {
          this.routeSelectionData = data
        } else {
          this.routeSelectionData = []
        }
      },

      // 清除路线
      cancelContrast() {
        this.customMap.clearOverlays() //清除所有
        this.clearMapShowInfo() //清除所有对比信息
        this.newTrajectoryData = {}
        if (this.markers.length > 0) {
          this.markers.forEach((marker) => {
            this.customMap.addOverlay(marker)
          })
        }
      },
      // 路线对比
      routeContrast() {
        if (this.routeSelectionData.length > 3) {
          ElNotification({
            title: '提示',
            message: '路线查看功能，支持同时选择三条线路进行查看！您当前选择的线路数已超过上限！',
            type: 'error',
          })
          return
        } else {
          this.isRouteView = false
          let options = {
            strokeColor: '#0066ff', //线颜色
            strokeOpacity: 0.8, //线透明度
            strokeWeight: 6, //线宽
            strokeStyle: 'solid', //线样式
          }
          if (this.polyline) {
            this.customMap.removeOverlay(this.polyline)
          }
          this.customMap.removeOverlay(this.routeLayerList)
          this.routeLayerList = []
          this.routeSelectionData.forEach((item, index) => {
            if (item.track) {
              var pathList = []
              var trackList = item.track.split(';')
              trackList.map((item) => {
                const coords = item.split(',')
                if (coords.length == 2) {
                  const lng = parseFloat(coords[0])
                  const lat = parseFloat(coords[1])
                  if (!isNaN(lng) && !isNaN(lat)) {
                    const bdPoint = gcj02tobd09(lng, lat)
                    if (bdPoint.length > 1) {
                      pathList.push(new BMap.Point(bdPoint[0], bdPoint[1]))
                    }
                  }
                }
              })
              //轨迹点
              var routeLayer = new BMap.Polyline(pathList, options)
              this.routeLayerList.push(routeLayer)
              // 将路线添加至地图实例
              this.customMap.addOverlay(routeLayer)
              // 轨迹点击事件
              routeLayer.addEventListener('click', (e) => {
                this.highlightRoute(item, index)
              })
            }
          })
          // 默认第一条高亮
          this.highlightRoute(this.routeSelectionData[0], 0)
        }
      },

      // 清除展示信息
      clearMapShowInfo() {
        // 路线对比
        this.isRouteView = true //初始化按钮状态
        this.routeSelectionData = [] //清除选择的数据
        if (this.$refs.tableRef) {
          // 默认选中高亮清除
          this.$refs.tableRef.setCurrentRow()
          // 清空所有选中
          this.$refs.tableRef.clearSelection()
        }
      },

      // 高亮路线
      highlightRoute(row, index) {
        if (this.routeLayerList.length > 0) {
          // 赋值当前路线数据
          this.newTrajectoryData = row
          // 选中的路线当前行高亮
          this.$refs.tableRef.setCurrentRow(row)
          this.routeLayerList.map((item, _index) => {
            if (_index === index) {
              this.customMap.removeOverlay(item)
              this.customMap.addOverlay(item)
              item.setStrokeColor('#0066ff') // 或者其他高亮样式
            } else {
              item.setStrokeColor('#8a8c8e') // 或者其他灰色样式
            }
          })
        }
      },

      // 查看路线
      showRoute(row) {
        // 路线
        this.newTrajectoryData = row
        // 清除所有展示信息
        this.customMap.clearOverlays() //清除所有
        this.clearMapShowInfo() //清除所有对比信息
        if (this.markers.length > 0) {
          this.markers.forEach((marker) => {
            this.customMap.addOverlay(marker)
          })
        }
        // 画路线轨迹
        this.choiceMode(row)
      },

      // 路线轨迹
      choiceMode(newData) {
        var path = []
        if (newData.track) {
          let options = {
            strokeColor: '#0066ff', //线颜色
            strokeOpacity: 0.8, //线透明度
            strokeWeight: 6, //线宽
            strokeStyle: 'solid', //线样式
          }
          var trackList = newData.track.split(';')
          trackList.forEach((item) => {
            const rowItem = item.split(',')
            const bdPoint = gcj02tobd09(Number(rowItem[0]), Number(rowItem[1]))
            let currentPoint = new BMap.Point(bdPoint[0], bdPoint[1])
            path.push(currentPoint)
          })
          const _newPolyline = new BMap.Polyline(path, options)
          // 将路线添加至地图实例
          this.customMap.addOverlay(_newPolyline)
          this.customMap.setViewport(path)
        }
      },

      // 分享轨迹
      shareTrajectory(row) {
        this.newTrajectoryData = row
        this.sharedDialog = true
        this.getUsersData()
      },

      // 关闭弹窗
      sharedHandleClose() {
        this.shareFormData.shardDriverIdList = []
        this.sharedDialog = false
      },
      // 取消弹窗
      sharedCancel() {
        this.shareFormData.shardDriverIdList = []
        this.sharedDialog = false
      },

      // 共享对象确定
      determine() {
        let params = {
          ...this.shareFormData,
          distance: this.newTrajectoryData.totalDistance ? this.newTrajectoryData.totalDistance / 1000 : 0,
          duration: this.newTrajectoryData.totalTime ? this.newTrajectoryData.totalTime / 3600 : 0,
          start: this.newTrajectoryData.start,
          end: this.newTrajectoryData.end,
          track: this.newTrajectoryData.track,
        }
        if (this.shareFormData.name.trim() === '') {
          ElMessage.error('路线名称不能为空!')
          return
        }
        if (this.shareFormData.shardDriverIdList.length === 0) {
          ElMessage.error('请选择共享对象!')
          return
        }
        if (!this.newTrajectoryData.track) {
          this.$notify({
            title: '提示',
            message: '暂无可分享轨迹',
            type: 'error',
          })
          return
        }
        saveShareLine(params)
          .then((res) => {
            this.sharedDialog = false
            this.shareFormData.shardDriverIdList = []
            this.shareFormData.name = ''
            ElMessage.success('路线分享成功')
          })
          .catch(() => {
            ElMessage.error('路线分享失败!')
          })
      },

      // 结束
    },
  }
</script>
<style scoped>
  .TrackQuery {
    height: 100%;
    /* margin: 0px 10px; */
  }

  .gutterRow {
    display: flex;
  }

  .TrackQuery .leftForm {
    background: #fff;
    padding: 20px 10px;
    width: 350px;
    position: relative;
  }

  .TrackQuery .leftForm1 {
    width: 0px;
    background: #fff;
    position: relative;
  }

  .topBox {
    background: #fff;
    width: 100%;
    height: 30vh;
    padding: 0px;
    z-index: 10;
    border-top: 1px solid #eee;
  }

  .bottomBox {
    background: #fff;
    width: 100%;
    height: 0px;
    padding: 0px;
    z-index: 10;
  }

  .TrackQuery :deep(.BMap_pop div:nth-child(9)) {
    top: 28px !important;
  }

  .TrackQuery .queryCondition {
    width: 330px;
    height: -webkit-fill-available;
    background: #fff;
    position: relative;
    overflow: scroll;
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;
    z-index: 0;
  }

  .queryCondition :deep(.el-tabs--border-card > .el-tabs__header) {
    border-bottom: none;
    border-radius: 6px;
  }

  .queryCondition .queryBtn {
    display: block;
    width: 70%;
    margin: 8px auto 16px;
  }

  .maxInputStyle {
    width: 360px;
  }

  .maxInputStyle :deep(.el-input__inner) {
    padding-right: 42px;
  }

  .maxInputStyle :deep(.el-input__suffix) {
    right: 2px;
  }

  .buttonBox {
    padding: 5px;
  }

  .operation :deep(.el-button--small) {
    padding: 0px 0px;
  }

  .bm-view-up {
    width: 100%;
    height: calc(70vh - 84px);
  }

  .bm-view-down {
    width: 100%;
    height: calc(100vh - 84px);
  }
</style>
