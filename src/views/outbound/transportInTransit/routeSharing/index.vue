<!--
 * @Author: llm
 * @Date: 2024-11-28 15:10:50
 * @LastEditors: llm
 * @LastEditTime: 2025-03-27 11:42:08
 * @Description: 路线共享
-->
<template>
  <!-- 路线共享 -->
  <div class="TrackShare">
    <baidu-map
      ref="mapHeight"
      :class="isShowTopBottomBox ? 'bm-view-up' : 'bm-view-down'"
      :zoom="zoom"
      :center="{ lng: parseFloat(bdMapCenter.longitude), lat: parseFloat(bdMapCenter.latitude) }"
      :scroll-wheel-zoom="true"
      :continuous-zoom="true"
      :map-click="false"
      @init="handler"
    >
    </baidu-map>
    <div style="position: relative">
      <div class="topBottomContraction" @click="changeTableBox">
        <el-icon v-if="isShowTopBottomBox"><ArrowDown /></el-icon>
        <el-icon v-if="!isShowTopBottomBox"><ArrowUp /></el-icon>
      </div>
      <div :class="isShowTopBottomBox ? 'topBox' : 'bottomBox'">
        <div id="bottom" class="trackTable">
          <!-- 搜索项 -->
          <el-form class="searchBox" ref="searchData" :model="searchData" :inline="true">
            <el-form-item label="路线名称" label-width="80px">
              <el-input type="text" placeholder="请输入路线名称" clearable v-model="searchData.name" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="出发地" label-width="60px">
              <el-input type="text" placeholder="请输入出发地" clearable v-model="searchData.start" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item label="目的地" label-width="60px">
              <el-input type="text" placeholder="请输入目的地" clearable v-model="searchData.end" style="width: 200px"></el-input>
            </el-form-item>
            <el-form-item style="margin-left: 10px">
              <el-button type="primary" @click="searchList">查询</el-button>
              <el-button type="primary" @click="resetList">重置</el-button>
            </el-form-item>
          </el-form>
          <!--table表格开始-->
          <Table
            :data="tableData"
            :isPagination="tableConfig.isPagination"
            :loading="tableConfig.loading"
            :tableConfig="tableConfig"
            :tableHeight="tableConfig.tableHeight"
            @sharedObject="sharedObject"
            @showTrajectory="viewTrack"
          ></Table>
          <Pagination :total="total" :page="page" :limit="limit" @pagination="pagination"></Pagination>
        </div>
      </div>
    </div>

    <!-- 共享对象 -->
    <el-dialog :draggable="true" :close-on-click-modal="false" v-model="sharedDialog" title="共享对象" width="600px" @close="cancel">
      <el-form ref="formData" :model="formData" class="enclosureForm" label-width="110px">
        <el-form-item label="共享对象：" prop="shardDriverIdList">
          <el-select filterable clearable multiple collapse-tags collapse-tags-tooltip v-model="formData.shardDriverIdList" placeholder="请选择">
            <el-option v-for="item in driverList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <!-- 按钮 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="determine">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
  import { getCollectionDriverPage, saveShareLine } from '@/api/transitManagement/addressTrack'
  import { getFleetOutFleetDriverSelectOptionApi } from '@/api/financialManagement'
  import RightTopWindowInfo from '@/components/RightTopWindowInfo/index.vue'
  import { gcj02tobd09, bd09togcj02 } from '@/utils'
  import Table from '@/components/TmsTableComponent/table.vue'
  export default {
    name: 'OutboundTransportInTransitRouteSharing',
    components: {
      Table,
      RightTopWindowInfo,
    },
    data() {
      return {
        bdMapCenter: { longitude: 105.01177, latitude: 38.419774 },
        zoom: 5,
        driverList: [], // 司机列表
        customMap: null,
        searchData: {
          name: '',
          start: '',
          end: '',
        },
        page: 1, //第几页
        limit: 20, //每页条数
        total: 0, // 总量
        formData: {
          id: '',
          shardDriverIdList: [],
          distance: '',
          duration: '',
          start: '',
          end: '',
          name: '',
          track: '',
        },
        tableData: [],
        tableConfig: {
          // pageNo: 1,
          // pageSize: 20,
          loading: false,
          isPagination: false, //是否需要分页
          tableItem: [
            { name: 'name', label: '路线名称', width: '200' },
            { name: 'createTime', label: '共享时间', width: '200' },
            { name: 'start', label: '出发地', width: '200' },
            { name: 'end', label: '目的地', width: '200' },
            { name: 'distance', label: '行驶里程(km)', width: '200' },
            { name: 'duration', label: '驾驶时长(h)', width: '200' },
          ],
          operation: {
            label: '操作',
            name: ['查看轨迹', '共享对象'],
            width: '170',
          },
          tableHeight: 'calc(40vh - 130px)',
        },
        trajectoryId: '', // 查看轨迹获取的id 删除用来判断是否需要清除轨迹
        sharedDialog: false, //控制选择共享对象
        coordinate: [], // 轨迹点位列表
        polyline: [], // 路线对象
        isShowTopBottomBox: true,
      }
    },

    mounted() {
      this.getList()
    },

    methods: {
      handler({ BMap, map }) {
        this.customMap = map
      },

      // 请求数据列表
      getList() {
        let params = {
          page: this.page,
          limit: this.limit,
          start: this.searchData.start,
          end: this.searchData.end,
          name: this.searchData.name,
        }
        this.tableConfig.loading = true
        getCollectionDriverPage(params)
          .then((res) => {
            this.tableData = res.data.rows
            this.total = res.data.total
            this.tableConfig.loading = false
          })
          .catch(() => {
            this.tableData = []
            this.total = 0
            this.tableConfig.loading = false
          })
      },

      // 查询列表数据
      searchList() {
        this.page = 1
        this.limit = 20
        // 清空所有地图覆盖物
        if (this.customMap) {
          this.customMap.clearOverlays()
        }
        this.getList()
      },
      // 重置
      resetList() {
        this.page = 1
        this.limit = 20
        this.searchData.start = ''
        this.searchData.end = ''
        this.searchData.name = ''
        if (this.customMap) {
          this.customMap.clearOverlays()
        }
        this.getList()
      },

      // 点击共享对象
      sharedObject(row) {
        // 赋值
        this.formData.id = row.id
        this.formData.shardDriverIdList = row.shardDriverIdList ? row.shardDriverIdList : []
        this.formData.distance = row.distance
        this.formData.duration = row.duration
        this.formData.start = row.start
        this.formData.end = row.end
        this.formData.name = row.name
        this.formData.track = row.track
        getFleetOutFleetDriverSelectOptionApi({}).then((response) => {
          if (response && response.data.length > 0) {
            this.driverList = response.data
          } else {
            this.driverList = []
          }
          this.sharedDialog = true
        })
      },

      // 取消弹窗
      cancel() {
        this.sharedDialog = false
      },

      // 共享对象确定
      determine() {
        let params = {
          ...this.formData,
        }
        if (this.formData.shardDriverIdList.length === 0) {
          ElMessage.error('请选择共享对象!')
          return
        }
        saveShareLine(params)
          .then((res) => {
            this.sharedDialog = false
            this.getList() //刷新列表
            ElMessage.success('共享对象设置成功')
          })
          .catch(() => {
            ElMessage.error('共享对象设置失败!')
          })
      },

      // 上下收缩视图
      changeTableBox() {
        this.isShowTopBottomBox = !this.isShowTopBottomBox
      },

      // 查看轨迹
      viewTrack(row) {
        if (this.customMap) {
          this.customMap.clearOverlays()
        }
        this.trajectoryId = row.id
        this.coordinate = [] //初始化轨迹
        let tracesData = row.track //轨迹
        if (tracesData) {
          var trackList = tracesData.split(';')
          trackList.forEach((event, index) => {
            if (event) {
              const bdPoint = gcj02tobd09(Number(event.split(',')[0]), Number(event.split(',')[1]))
              this.coordinate.push(new BMap.Point(bdPoint[0], bdPoint[1]))
            }
          })
          let options = {
            strokeColor: '#0066ff', //线颜色
            strokeOpacity: 0.9, //线透明度
            strokeWeight: 6, //线宽
            strokeStyle: 'solid', //线样式
          }
          //轨迹点
          this.polyline = new BMap.Polyline(this.coordinate, options)
          // 将路线添加至地图实例
          this.customMap.addOverlay(this.polyline)
          // 起点终点marker
          let newStartPoint = trackList[0].split(',')
          let newEndPoint = trackList[trackList.length - 1].split(',')

          let startIcon = 'http://api.haodaoda.com/static/wx/mini/dingwei_qidian.png'
          let endIcon = 'http://api.haodaoda.com/static/wx/mini/dingwei_zhongdian.png'
          // 起点marker
          if (newStartPoint) {
            // 定义图标
            this.startMarker = new BMap.Icon(
              startIcon, // 图标路径
              new BMap.Size(32, 40), // 图标大小
              {
                anchor: new BMap.Size(16, 40), // 设置图标的定位点（偏移）
                imageOffset: new BMap.Size(0, 0), // 图标图片的偏移
                imageSize: { width: 32, height: 40 },
              },
            )
            var point = new BMap.Point(newStartPoint[0], newStartPoint[1]) // 经纬度
            var marker = new BMap.Marker(point, { icon: this.startMarker, offset: new BMap.Size(0, 12) })
            // 将路线添加至地图实例
            this.customMap.addOverlay(marker)
          }
          // 终点marker
          if (newEndPoint) {
            // 定义图标
            this.endMarker = new BMap.Icon(
              endIcon, // 图标路径
              new BMap.Size(32, 40), // 图标大小
              {
                anchor: new BMap.Size(16, 40), // 设置图标的定位点（偏移）
                imageOffset: new BMap.Size(0, 0), // 图标图片的偏移
                imageSize: { width: 32, height: 40 },
              },
            )
            var point = new BMap.Point(newEndPoint[0], newEndPoint[1]) // 经纬度
            var marker = new BMap.Marker(point, { icon: this.endMarker, offset: new BMap.Size(0, 12) })
            // 将路线添加至地图实例
            this.customMap.addOverlay(marker)
          }

          const points = [new BMap.Point(Number(newStartPoint[0]), Number(newStartPoint[1])), new BMap.Point(Number(newEndPoint[0]), Number(newEndPoint[1]))]
          // 缩放地图到合适的视野级别
          var view = this.customMap.getViewport(points) // markers中包含了所有点
          this.zoom = view.zoom
          this.bdMapCenter = {
            longitude: view.center.lng,
            latitude: view.center.lat,
          }
        } else {
          this.coordinate = []
          if (this.customMap) {
            this.customMap.clearOverlays()
          }
          this.$message({
            type: 'warning',
            message: '当前暂无轨迹数据！',
            duration: 3 * 1000,
          })
          return
        }
      },
    },
  }
</script>

<style scoped>
  .TrackShare {
    height: calc(100vh - 84px);
  }

  #mapCenter {
    width: 100%;
    height: 100%;
    position: relative;
  }

  #mapCenter :deep(.BMap_pop div:nth-child(9)) {
    top: 28px !important;
  }

  .trackTable {
    padding: 20px;
  }

  .searchBox :deep(.el-form-item--small.el-form-item) {
    margin: 10px 0;
  }

  .topBox {
    background: #fff;
    width: 100%;
    height: 40vh;
    padding: 0px;
    z-index: 10;
    border-top: 1px solid #eee;
  }

  .bottomBox {
    background: #fff;
    width: 100%;
    height: 0px;
    padding: 0px;
    z-index: 10;
  }

  .bm-view-up {
    width: 100%;
    height: calc(60vh - 84px);
  }

  .bm-view-down {
    width: 100%;
    height: calc(100vh - 84px);
  }
</style>
