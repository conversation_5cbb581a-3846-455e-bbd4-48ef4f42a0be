<!--
 * @Author: llm
 * @Date: 2024-11-28 15:10:50
 * @LastEditors: llm
 * @LastEditTime: 2025-04-16 15:56:33
 * @Description: 运力雷达
-->
<template>
  <div>
    <div class="left">
      <el-card>
        <div class="flex justify-between items-center mb-20px">
          <img src="@/assets/images/radar/<EMAIL>" alt="" class="w-30px h-40px mr-10px" />
          <div class="flex flex-col items-center">
            <div class="text-28px fw-bold color-#00417E">{{ state.summaryData.totalVehicleCount || 0 }}</div>
            <div class="text-14px lh-14px color-#00417E">车辆总数</div>
          </div>
        </div>
        <div class="flex justify-between items-center">
          <img src="@/assets/images/radar/<EMAIL>" alt="" class="w-40px h-38px mr-10px" />
          <div class="flex flex-col items-center">
            <div class="text-28px fw-bold color-#FF0404">{{ state.summaryData.abnormalVehicleCount || 0 }}</div>
            <div class="text-14px lh-14px color-#00417E">在途异常</div>
          </div>
        </div>
        <!-- <el-divider />
        <div class="text-center">
          <el-button type="primary" link>
            查看详情
            <el-icon>
              <CaretRight />
            </el-icon>
          </el-button>
        </div> -->
      </el-card>
    </div>
    <div class="center">
      <el-input v-model="state.searchValue" placeholder="" class="search-input">
        <template #prepend>
          <el-select v-model="state.selectValue" placeholder="" style="width: 100px">
            <el-option label="搜车牌号" value="1" />
            <el-option label="搜围栏" value="2" />
          </el-select>
        </template>
        <template #append>
          <el-button icon="Search" @click="handleSearch(state.searchValue)" />
        </template>
      </el-input>
    </div>
    <div class="right">
      <div class="flex items-center justify-end">
        <div class="btn btn1 mr-20px flex items-center" @click="searchAllVehicle">
          <img src="@/assets/images/radar/<EMAIL>" alt="" class="w-18px h-20px mr-10px" />
          查看所有在途车辆
        </div>
        <div class="btn btn2" @click="state.hideRightArea = !state.hideRightArea">
          <img src="@/assets/images/radar/<EMAIL>" alt="" class="w-20px h-20px mr-10px" />
          运力雷达
        </div>
      </div>
      <el-card v-if="state.hideRightArea">
        <div class="flex items-center">
          <el-table :data="state.summaryData.summaryList" height="200px" max-height="200px" size="small" align="center">
            <el-table-column prop="childCompanyName" label="办事处" show-overflow-tooltip align="center" />
            <el-table-column prop="waitForCount" label="待分配" align="center">
              <template #default="{ row }">
                <div v-if="!row.waitForCount">0（{{ row.waitForVinCount }}）</div>
                <el-link type="primary" :underline="true" v-else @click="linkToOrder(row)">{{ row.waitForCount }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="stayVehicleCount" label="停留中" align="center">
              <template #default="{ row }">
                <div v-if="!row.stayVehicleCount">0（{{ row.stayVinCount }}）</div>
                <div v-else>
                  <el-link type="primary" :underline="true" @click="viewAddress(row, 'stayVehicleNos')">{{ row.stayVehicleCount }}</el-link>
                  ({{ row.stayVinCount }})
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="waitVehicleCount" label="待命中" align="center">
              <template #default="{ row }">
                <div v-if="!row.waitVehicleCount">0</div>
                <div v-else>
                  <el-link type="primary" :underline="true" @click="viewAddress(row, 'waitVehicleNos')">{{ row.waitVehicleCount }}</el-link>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="twoDayVehicleCount" label="预估2天到达" align="center">
              <template #default="{ row }">
                <div v-if="!row.twoDayVehicleCount">0（{{ row.twoDayVinCount }}）</div>
                <div v-else>
                  <el-link type="primary" :underline="true" @click="viewAddress(row, 'twoDayVehicleNos')">{{ row.twoDayVehicleCount }}</el-link>
                  ({{ row.twoDayVinCount }})
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="ml-10px">
            <el-link type="primary" class="vertical-text" :underline="false" @click="state.moreVehicleVisible = true">
              <span class="text-12px">更多预返车辆</span>
              <el-icon>
                <CaretRight />
              </el-icon>
            </el-link>
          </div>
        </div>
        <div class="text-center">
          <el-link :underline="false" @click="state.hideRightArea = false">
            <span class="text-12px color-#999">收起</span>
            <el-icon color="#999">
              <CaretTop />
            </el-icon>
          </el-link>
        </div>
      </el-card>
    </div>
    <!-- 地图 -->
    <baidu-map
      class="bm-view"
      ref="mapContainer"
      :zoom="5"
      :scroll-wheel-zoom="true"
      :center="{ lng: 116.404, lat: 39.915 }"
      v-loading="state.mapLoading"
      :map-click="false"
      @init="handler"
    >
      <bml-marker-clusterer :averageCenter="true" v-if="state.markers.length > 0">
        <bm-marker
          v-for="(marker, index) in state.markers"
          :icon="{ url: marker.icon, size: { width: 20, height: 32 }, opts: { imageSize: { width: 20, height: 32 } } }"
          :position="{ lng: parseFloat(marker.bd_currentCoordinate.bd_lng), lat: parseFloat(marker.bd_currentCoordinate.bd_lat) }"
          @click="infoWindowOpen(marker, index)"
        >
        </bm-marker>
      </bml-marker-clusterer>
      <!-- <bm-polygon v-for="(item, index) in state.polygonFenceList" :key="index" :path="item" stroke-color="blue"
        :stroke-opacity="0.5" :stroke-weight="2" :editing="false" />
      <bm-circle v-for="(item, index) in state.pointFenceList" :key="index" :center="{ lng: item.lng, lat: item.lat }"
        :radius="item.radius" stroke-color="blue" :stroke-opacity="0.5" :stroke-weight="2" :editing="false"></bm-circle> -->
    </baidu-map>

    <el-dialog title="运力雷达" width="80%" v-model="state.moreVehicleVisible">
      <el-table :data="state.summaryData.summaryList" show-summary max-height="60vh" size="small" align="center" :summary-method="getSummaries">
        <el-table-column prop="childCompanyName" label="分子公司" show-overflow-tooltip align="center" />
        <el-table-column prop="waitForCount" label="待分配" align="center">
          <template #default="{ row }">
            <div v-if="!row.waitForCount">0（{{ row.waitForVinCount }}）</div>
            <el-link type="primary" :underline="true" v-else @click="linkToOrder(row)">{{ row.waitForCount }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="waitVehicleCount" label="待命中" align="center">
          <template #default="{ row }">
            <div v-if="!row.waitVehicleCount">0</div>
            <div v-else>
              <el-link type="primary" :underline="true" @click="viewAddress(row, 'waitVehicleNos')">{{ row.waitVehicleCount }}</el-link>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="预达时间" align="center">
          <el-table-column prop="stayVehicleCount" label="停留中" align="center">
            <template #default="{ row }">
              <div v-if="!row.stayVehicleCount">0（{{ row.stayVinCount }}）</div>
              <div v-else>
                <el-link type="primary" :underline="true" @click="viewAddress(row, 'stayVehicleNos')">{{ row.stayVehicleCount }}</el-link>
                （{{ row.stayVinCount }}）
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="oneDayVehicleCount" label="未来1天内" align="center">
            <template #default="{ row }">
              <div v-if="!row.oneDayVehicleCount">0（{{ row.oneDayVinCount }}）</div>
              <div v-else>
                <el-link type="primary" :underline="true" @click="viewAddress(row, 'oneDayVehicleNos')">{{ row.oneDayVehicleCount }}</el-link>
                （{{ row.oneDayVinCount }}）
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="twoDayVehicleCount" label="未来2天内" align="center">
            <template #default="{ row }">
              <div v-if="!row.twoDayVehicleCount">0（{{ row.twoDayVinCount }}）</div>
              <div v-else>
                <el-link type="primary" :underline="true" @click="viewAddress(row, 'twoDayVehicleNos')">{{ row.twoDayVehicleCount }}</el-link>
                （{{ row.twoDayVinCount }}）
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="fiveDayVehicleCount" label="未来5天内" align="center">
            <template #default="{ row }">
              <div v-if="!row.fiveDayVehicleCount">0（{{ row.fiveDayVinCount }}）</div>
              <div v-else>
                <el-link type="primary" :underline="true" @click="viewAddress(row, 'fiveDayVehicleNos')">{{ row.fiveDayVehicleCount }}</el-link>
                （{{ row.fiveDayVinCount }}）
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="overFiveDayVehicleCount" label="未来5天以上" align="center">
            <template #default="{ row }">
              <div v-if="!row.overFiveDayVehicleCount">0（{{ row.overFiveDayVinCount }}）</div>
              <div v-else>
                <el-link type="primary" :underline="true" @click="viewAddress(row, 'overFiveDayVehicleNos')">{{ row.overFiveDayVehicleCount }}</el-link>
                （{{ row.overFiveDayVinCount }}）
              </div>
            </template>
          </el-table-column>
        </el-table-column>
        <!-- <el-table-column prop="vehicleCount" label="空载/满载" align="center">
          <template #default="{ row }">
            <div v-if="!row.vehicleCount">0（{{ row.vehicleVinCount }}）</div>
            <div v-else>
              <el-link type="primary" :underline="true">{{ row.vehicleCount }}</el-link>
              ({{ row.vehicleVinCount }})
            </div>
          </template>
        </el-table-column> -->
      </el-table>
    </el-dialog>

    <!-- windowinfo展示 -->
    <MapWindowInfo
      :dataInfo="state.currentInfo"
      :isShowHide="state.showInfoWindow"
      :popupPixel="state.popupPixel"
      @closeInformation="closeInformation"
    ></MapWindowInfo>
  </div>
</template>
<script setup>
  import {
    getFleetTransportCapacitySummaryQueryApi,
    getFleetTransportCapacityAllVehicleApi,
    getFleetTransportCapacityFenceQueryApi,
    getVehicleDispatchInfoApi,
  } from '@/api/transitManagement/transportRadar.js'
  import router from '@/router'
  import { bd_encrypt, gcj02tobd09, setBdMapViewPort } from '@/utils/index'
  import { useFormStore } from '@/store/modules/form'
  import MapWindowInfo from '@/components/MapWindowInfo/index.vue'
  const formStore = useFormStore()

  defineOptions({
    name: 'OutboundTransportInTransitTransportRadar',
    inheritAttrs: false,
  })

  const mapContainer = ref(null)
  const state = reactive({
    map: null,
    BMap: null,
    mapCenter: [116.397451, 39.909187], // 默认位置
    summaryData: {},
    hideRightArea: true,
    searchValue: '',
    selectValue: '1',
    polygonFenceList: [],
    pointFenceList: [],
    markers: [],
    mapLoading: false,
    showInfoWindow: false,
    moreVehicleVisible: false,
    infoWindow: null, // 信息窗口
    circleObj: null,
    newPath: null,
    currentInfo: {},
    popupPixel: {},
  })

  const handler = ({ BMap, map }) => {
    state.map = map
    state.BMap = BMap
    searchAllVehicle()

    // // 监听地图移动事件
    // map.addEventListener('moveend', addMarkerTip)
    // // 监听地图缩放事件
    // map.addEventListener('zoomend', addMarkerTip)
  }

  onMounted(() => {
    getFleetTransportCapacitySummaryQuery()

    // 自定义关闭按钮的功能
    window.closeInfoWindow = () => {
      if (state.map && state.infoWindow) {
        if (state.infoWindow.isOpen()) {
          state.map.closeInfoWindow()
        }
      }
    }
  })
  const getSummaries = (param) => {
    const { columns, data } = param
    const sums = []

    columns.forEach((column, index) => {
      if (index === 0) {
        sums[index] = '合计'
        return
      }
      const values = data.map((item) => Number(item[column.property]))
      if (!values.every((value) => isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr)
          if (!isNaN(value)) {
            return prev + curr
          } else {
            return prev
          }
        }, 0)}${
          index === 1
            ? ''
            : `（${data.reduce((prev, curr) => {
                const vinValue = Number(curr[`${column.property.replace('VehicleCount', 'VinCount')}`])
                if (!isNaN(vinValue)) {
                  return prev + vinValue
                } else {
                  return prev
                }
              }, 0)}）`
        } `
      } else {
        sums[index] = ''
      }
    })

    return sums
  }
  const viewAddress = (row, key) => {
    handleSearch(row[key], 'clearSearchValue')
    state.moreVehicleVisible = false
  }
  const infoWindowOpen = (marker, index) => {
    let params = {
      vehicleId: state.markers[index].vehicleWholeId,
      vehicleNo: state.markers[index].vehicleNo,
    }
    // 获取信息
    getVehicleDispatchInfoApi(params)
      .then((res) => {
        if (res.code === 200) {
          state.currentInfo = res.data
          state.currentInfo.lng = state.markers[index].lng
          state.currentInfo.lat = state.markers[index].lat
          // addMarkerTip()
          state.showInfoWindow = true
        } else {
          state.currentInfo = {}
          state.showInfoWindow = false
        }
      })
      .catch((err) => {})
  }
  // 添加信息窗口
  function addMarkerTip() {
    if (JSON.stringify(state.currentInfo) != '{}') {
      // 获取地图容器的 DOM 元素
      const mapContainerDom = document.querySelector('.bm-view')
      // 获取地图容器的位置和大小
      const mapRect = mapContainerDom.getBoundingClientRect()
      // 获取标记点的经纬度
      const position = new state.BMap.Point(parseFloat(state.currentInfo.lng), parseFloat(state.currentInfo.lat))
      // 将经纬度转换为像素坐标
      const pixel = state.map.pointToPixel(position)
      // 计算弹窗的位置
      // state.popupLeft = mapRect.left + pixel.x
      // state.popupTop = mapRect.top + pixel.y

      // 计算水平居中的偏移量
      const offsetX = 450
      // 计算垂直偏移量，让弹窗显示在 marker 点下方
      const offsetY = 12

      // 计算弹窗的位置
      state.popupPixel = {
        x: mapRect.left + pixel.x - offsetX,
        y: mapRect.top + pixel.y + offsetY,
      }

      // state.popupPixel = {
      //   x: mapRect.left + pixel.x - 460,
      //   y: mapRect.top + pixel.y + 10,
      // }
    }

    // if (state.map && state.infoWindow) {
    //   if (state.infoWindow.isOpen()) {
    //     state.map.closeInfoWindow()
    //   }
    // }
    // var opts = {
    //   width: 260, // 信息窗口宽度
    //   enableCloseOnClick: false, // 点击地图时不关闭
    // }
    // let positionMarker = item.bd_currentCoordinate
    // var content = ''
    // //如果是车辆marker点
    // if (item.vehicleNo) {
    //   content = `
    //   <div class="statisticsInfo">
    //   <div class="customizeBox">
    //     <h4>车辆信息</h4>
    //     <button class="close-btn" onclick="closeInfoWindow()">X</button>
    //   </div>
    //   <div class="buttonBox">
    //   <el-tabs v-model="${state.newTypeName || 0}" class="tabChangeClass" type="border-card" @tab-change="handleChange">
    //     <el-tab-pane label="车辆信息" name="vehicle">
    //       <div class="vehicleInfo">
    //         <el-row>
    //           <el-col :span="12" class="colItem">
    //             <span class="lableText">承运商：</span>
    //             <span>${item.carrierName || '--'}</span>
    //           </el-col>
    //           <el-col :span="12" class="colItem">
    //             <span class="lableText">车牌号：</span>
    //             <span>${item.vehicleNo || '--'}</span>
    //           </el-col>
    //           <el-col :span="12" class="colItem">
    //             <span class="lableText">行驶里程：</span>
    //             <span>${item.navTotalMileage || '--'}</span>
    //           </el-col>
    //           <el-col :span="12" class="colItem">
    //             <span class="lableText">驾驶时长：</span>
    //             <span>${item.drivingTotalTime || '--'}</span>
    //           </el-col>
    //           <el-col :span="12" class="colItem">
    //             <span class="lableText">停车时长：</span>
    //             <span>${item.parkingTotalTime || '--'}</span>
    //           </el-col>
    //           <el-col :span="12" class="colItem">
    //             <span class="lableText">平均速度：</span>
    //             <span>${item.averageSpeed || '--'}</span>
    //           </el-col>
    //         </el-row>
    //       </div>
    //     </el-tab-pane>
    //   </el-tabs>
    // </div>
    // </div>
    //         `
    // } else {
    //   content = `
    //         <div class="titleBox">
    //           <h4>围栏信息</h4>
    //           <button class="close-btn" onclick="closeInfoWindow()">X</button>
    //         </div>
    //         <div class="contentBox">
    //           <span class="title">围栏名称: </span><span class="contentInfo">${item.name}</span><br>
    //           <span class="title">围栏地址: </span><span class="contentInfo">${item.address}</span><br>
    //         </div>
    //         `
    //   clearAllOverlays()
    //   if (item.radius) {
    //     circleData(item)
    //   } else {
    //     areasData(item)
    //   }
    // }
    // state.infoWindow = new BMap.InfoWindow(content, opts) // 创建信息窗口对象
    // var newPoint = new BMap.Point(positionMarker.bd_lng, positionMarker.bd_lat)
    // state.map.openInfoWindow(state.infoWindow, newPoint) //开启信息窗口
  }

  // 展开隐藏信息窗
  const closeInformation = (value) => {
    state.showInfoWindow = value
  }

  const clearAllOverlays = () => {
    // 清空所有圆形围栏
    if (state.circleObj) {
      state.map.removeOverlay(state.circleObj)
      state.circleObj = null
    }
    // 清空所有多边形围栏
    if (state.newPath && state.newPath.length > 0) {
      for (let i = 0; i < state.newPath.length; i++) {
        state.map.removeOverlay(state.newPath[i])
      }
    }
    //关闭窗体
    if (state.infoWindow && state.infoWindow.isOpen()) {
      state.map.closeInfoWindow()
    }
  }
  const getFleetTransportCapacitySummaryQuery = async () => {
    const { data } = await getFleetTransportCapacitySummaryQueryApi({})
    state.summaryData = data
  }
  const handleSearch = async (params, type) => {
    if (params) {
      clearAllOverlays()
      if (type && type === 'clearSearchValue') {
        state.searchValue = ''
      }
      if (state.selectValue === '1') {
        // 搜车牌号
        const data = await getFleetTransportCapacityAllVehicle({ vehicleNos: params })
        if (data.length === 0) {
          ElMessage.error('未查询到当前车辆')
        }
        state.markers = data
      } else {
        state.markers = []
        // 搜围栏
        const { data } = await getFleetTransportCapacityFenceQueryApi({ name: params })
        if (data.length === 0) {
          ElMessage.error('未查询到当前围栏')
        }
        state.markers = data.map((item) => {
          const bd_point = bd_encrypt(item.coordinate.split(',')[0], item.coordinate.split(',')[1])
          item.bd_currentCoordinate = {
            bd_lng: bd_point.bd_lng,
            bd_lat: bd_point.bd_lat,
          }
          item.icon = 'https://webapi.amap.com/theme/v1.3/markers/b/mark_bs.png'
          // 多边形围栏坐标转换
          if (item.quyu) {
            const quyu = item.quyu.split(';')
            if (quyu && quyu.length > 0) {
              item.quyu = quyu.map((qyItem) => {
                let quyuPoint = gcj02tobd09(qyItem.split(',')[0], qyItem.split(',')[1])
                return (qyItem = {
                  lng: quyuPoint[0],
                  lat: quyuPoint[1],
                })
              })
            }
          }
          return item
        })
        setBdMapViewPort(state.markers, 'coordinate', state.map, state.BMap)
      }
    }
  }
  // 回显圆形数据
  const circleData = (valueObj) => {
    if (JSON.stringify(valueObj) != '{}') {
      var newPoint = new BMap.Point(Number(valueObj.bd_currentCoordinate.bd_lng), Number(valueObj.bd_currentCoordinate.bd_lat))
      // this.circlePath.center = newPoint;
      // this.circlePath.radius = valueObj.radius;
      let options = {
        strokeColor: '#FF33FF',
        fillColor: '#1791fc',
        fillOpacity: 0.5, // 透明度
        strokeStyle: 'dashed',
        strokeWeight: 3,
        strokeOpacity: 1,
        enableEditing: false,
      }
      if (state.circleObj && state.map) {
        state.map.removeOverlay(state.circleObj)
      }
      state.circleObj = new BMap.Circle(newPoint, valueObj.radius, options) // 创建圆形覆盖物
      state.map.addOverlay(state.circleObj) // 添加到地图
      // 获取圆形覆盖物的视图边界
      var bounds = state.circleObj.getBounds()
      // 获取圆形的四个边界点：左上角 (southWest) 和右下角 (northEast)
      const sw = bounds.getSouthWest() // 左下角
      const ne = bounds.getNorthEast() // 右上角
      // 使用 setViewport 调整地图视野，确保圆形完全显示
      state.map.setViewport([sw, ne]) // 设置地图视野区域
    }
  }
  // 回显多边形
  const areasData = (data) => {
    let path = []
    if (data.quyu.length <= 0) {
      return
    }
    for (let index = 0; index < data.quyu.length; index++) {
      let currentPoint = new BMap.Point(Number(data.quyu[index].lng), Number(data.quyu[index].lat))
      path.push(currentPoint)
    }
    let options = {
      strokeColor: '#FF33FF',
      fillColor: '#1791fc',
      fillOpacity: 0.5, // 透明度
      strokeStyle: 'dashed',
      strokeWeight: 3,
      strokeOpacity: 1,
      enableEditing: false,
    }
    if (state.map) {
      state.map.removeOverlay(state.newPath)
    }
    state.newPath = new BMap.Polygon(path, options) // 创建多边形覆盖物
    state.map.addOverlay(state.newPath) // 添加到地图
    state.map.setViewport(path)
  }

  //查询所有在途车辆
  const searchAllVehicle = async () => {
    const params = {
      vehicleNos: undefined,
    }
    state.mapLoading = true
    state.markers = []
    state.searchValue = ''
    clearAllOverlays()
    const data = await getFleetTransportCapacityAllVehicle(params)
    state.markers = data
    setBdMapViewPort(data, 'currentCoordinate', state.map, state.BMap)
    state.mapLoading = false
  }
  const getFleetTransportCapacityAllVehicle = async (params) => {
    const { data } = await getFleetTransportCapacityAllVehicleApi(params)
    const ownVehicles = data.filter((item) => item.currentCoordinate)
    ownVehicles.map((item) => {
      const bd_point = bd_encrypt(item.currentCoordinate.split(',')[0], item.currentCoordinate.split(',')[1])
      item.bd_currentCoordinate = {
        bd_lng: bd_point.bd_lng,
        bd_lat: bd_point.bd_lat,
      }
      item.icon = 'https://webapi.amap.com/theme/v1.3/markers/b/mark_bs.png'
    })
    return ownVehicles
  }
  const linkToOrder = (row) => {
    //不刷新查询条件
    formStore.setIsRefreshTopQueryParams(false)
    formStore.$patch((state) => {
      state.routerParams = {
        orderStatus: '1',
        childCompanyName: row.childCompanyName,
      }
    })
    router.push({
      path: '/outboundDispatchManagement/orderManagement',
      query: {
        time: new Date().getTime(), //获取当前时间戳,防止不刷新页面·
      },
    })
  }
</script>
<style lang="scss" scoped>
  .bm-view {
    height: calc(100vh - 84px);
    padding: 0;
    margin: 0;
  }

  .left {
    position: absolute;
    left: 40px;
    top: 120px;
    z-index: 8;
  }

  .right {
    position: absolute;
    right: 40px;
    top: 120px;
    z-index: 8;
    width: 460px;
    height: 282px;
  }

  .center {
    position: absolute;
    left: 50%;
    top: 140px;
    transform: translate(-50%, -50%);
    z-index: 9;
  }

  :deep(.el-divider--horizontal) {
    margin: 20px 0 10px !important;
  }

  .vertical-text {
    writing-mode: vertical-rl;
    /* 从右到左的垂直书写模式 */
    text-orientation: upright;
    /* 文字方向保持直立 */
  }

  .btn {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url('@/assets/images/radar/btn-bg.png') no-repeat;
    background-size: 100% 100%;
    font-size: 14px;
    font-weight: bold;
    color: #00417e;
    cursor: pointer;
  }

  .btn1 {
    width: 180px;
    height: 39px;
  }

  .btn2 {
    width: 130px;
    height: 39px;
  }
</style>

<style scoped>
  .bm-view :deep(.BMap_pop div:nth-child(9)) {
    top: 28px !important;
  }
  .customizeBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #228b22;
    font-weight: 500;
    color: #fff;
    padding: 5px 10px;
    font-size: 16px;
  }
  .customizeBox .close-btn {
    border: none;
    padding: 0 4px;
    cursor: pointer;
    background: transparent;
    color: #fff;
  }
</style>
