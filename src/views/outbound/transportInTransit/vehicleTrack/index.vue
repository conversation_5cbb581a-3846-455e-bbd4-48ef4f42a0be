<!--
 * @Author: llm
 * @Date: 2024-12-09 10:30:45
 * @LastEditors: llm
 * @LastEditTime: 2025-06-21 10:35:01
 * @Description: 轨迹查询
-->
<template>
  <!-- 轨迹查询 -->
  <div v-loading="boxLoading" class="TrackQuery">
    <div id="top" class="gutterRow">
      <div :class="isShowBox ? 'leftForm' : 'leftForm1'">
        <!--选项卡 开始-->
        <div class="common_tab_border_card">
          <el-tabs v-model="statusType" :stretch="true" type="border-card" @tab-click="handleClick">
            <el-tab-pane v-for="(item, index) in statusList" :key="index" :label="item.label" :name="item.value"></el-tab-pane>
          </el-tabs>
        </div>
        <!--选项卡 结束-->
        <el-form ref="form" :inline="true" :model="formData" class="queryCondition" label-width="80px">
          <el-form-item label="开始时间" prop="startTime" v-show="statusType == 'CHELIANG'">
            <el-date-picker
              size="small"
              v-model="formData.startTime"
              :editable="false"
              :disabled-date="dataTime.startTimeData"
              align="right"
              :default-time="defaultTimeStart"
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择开始时间"
              popper-class="no-atTheMoment"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="getStartDate"
              style="width: 111px"
            ></el-date-picker>
            <el-button size="small" style="margin-left: 5px" type="primary" @click="Manual('StartTime')"> 手动输入 </el-button>
          </el-form-item>
          <el-form-item label="结束时间" prop="endTime" v-show="statusType == 'CHELIANG'">
            <el-date-picker
              size="small"
              
              v-model="formData.endTime"
              :editable="false"
              :disabled-date="dataTime.endTimeData"
              align="right"
              :default-time="defaultTimeEnd"
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择结束时间"
              popper-class="no-atTheMoment"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="getEndDate"
              style="width: 111px"
            ></el-date-picker>
            <el-button size="small" style="margin-left: 5px" type="primary" @click="Manual('EndTime')"> 手动输入 </el-button>
          </el-form-item>
          <el-form-item label="选择车辆" prop="vehicle" v-show="statusType == 'CHELIANG'">
            <el-select
              size="small"
              v-model="formData.vehicle"
              clearable
              filterable
              placeholder="模糊查询并选择车辆"
              value-key="vehicle"
              remote
              :remote-method="getVehicleListData"
              :loading="vehicleListLoading"
              style="width: 200px"
            >
              <el-option v-for="item in vehicleList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="调度单号" prop="dispatchNo" v-show="statusType == 'DIAODU'">
            <!-- 远程模糊搜索下拉 -->
            <el-select
              size="small"
              v-model="formData.dispatchNo"
              clearable
              filterable
              placeholder="模糊查询并选择调度单号"
              value-key="dispatchNo"
              remote
              :remote-method="remoteSelectMethod"
              :loading="fuzzySelectLoading"
              style="width: 200px"
            >
              <el-option v-for="i in dispatchDataList" :key="i.value" :label="i.label" :value="i.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="速度阈值" prop="speed">
            <el-input-number v-model="formData.speed" :max="100" :min="5" :step="5" @change="speedChange" style="width: 120px"></el-input-number>
            <span style="padding: 0 10px; color: #666">km/h</span>
          </el-form-item>
          <el-form-item label="停车阈值" prop="parking">
            <el-input-number v-model="formData.parking" :max="100" :min="5" :step="5" @change="parkingChange" style="width: 120px"></el-input-number>
            <span style="padding: 0 10px; color: #666">分钟</span>
          </el-form-item>
          <div class="flex align-center justify-center">
            <el-button size="default" @click.native.prevent="resetQuery">重置</el-button>
            <el-button class="queryBtn" type="primary" size="default" @click.native.prevent="handleQuery">查询</el-button>
          </div>
        </el-form>
        <div class="tip-tag" @click="changeLeft">
          <ArrowLeft v-if="isShowBox" />
          <ArrowRight v-else />
        </div>
      </div>
      <baidu-map
        ref="mapHeight"
        :class="isShowTopBottomBox ? 'bm-view-up' : 'bm-view-down'"
        :zoom="zoom"
        :center="{ lng: parseFloat(bdMapCenter.longitude), lat: parseFloat(bdMapCenter.latitude) }"
        :scroll-wheel-zoom="true"
        :continuous-zoom="true"
        :map-click="false"
        @init="handler"
      >
        <bml-lushu
          @start="changeBtnText('暂停')"
          @stop="changeBtnText('播放')"
          @pause="changeBtnText('播放')"
          :path="coordinate"
          :rotation="rotation"
          :content="lusContent"
          :infoWindow="true"
          :speed="speed"
          :icon="icon"
          :play="play"
        >
        </bml-lushu
      ></baidu-map>
    </div>
    <div style="position: relative">
      <div class="topBottomContraction" @click="changeChartBox">
        <el-icon v-if="isShowTopBottomBox"><ArrowDown /></el-icon>
        <el-icon v-if="!isShowTopBottomBox"><ArrowUp /></el-icon>
      </div>
      <div :class="isShowTopBottomBox ? 'topBox' : 'bottomBox'">
        <div id="speedChart" :style="{ width: '100%', padding: '10px 20px', height: isShowTopBottomBox ? '30vh' : '0px' }"></div>
      </div>
    </div>

    <el-dialog v-model="TimedialogVisible" title="手动输入" width="500">
      <el-form-item label="手动输入时间" label-width="100px">
        <el-input v-model="ManualTime" />
      </el-form-item>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="TimedialogVisible = false">取消</el-button>
          <el-button type="primary" @click="enter"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- windowinfo展示 -->
    <right-top-window-info
      :btnText="btnText"
      :dataInfo="currentInfo"
      :isShowHide="showInfoWindow"
      :currentTaskList="currentTaskList"
      :vinInfoList="vinInfoList"
      @closeInformation="closeInformation"
      @viewTrajectory="toggle"
      @speedChange="handleSpeedChange"
    ></right-top-window-info>
  </div>
</template>

<script>
  import { getSegmentTrace, inTransitVehicleSelect } from '@/api/transitManagement/addressTrack'
  import RightTopWindowInfo from '@/components/RightTopWindowInfo/index.vue'
  import { BmlLushu } from 'vue-baidu-map-3x'
  import * as echarts from 'echarts'
  import { gcj02tobd09, bd09togcj02 } from '@/utils'
  import { getRepairProjectSelectOptionApi } from '@/api/fleetManagement'
  export default {
    name: 'OutboundTransportInTransitVehicleTrack',
    components: {
      RightTopWindowInfo,
      BmlLushu,
    },
    data() {
      return {
        bdMapCenter: { longitude: 105.01177, latitude: 38.419774 },
        zoom: 5,
        tracesData: [], //还原后的轨迹点
        currentTaskList: [], //任务列表
        chartId: 'speedChart',
        showInfoWindow: false, //控制infoWindow显示隐藏
        defaultTimeStart: new Date(2000, 1, 1, 0, 0, 0),
        defaultTimeEnd: new Date(2000, 2, 1, 23, 59, 59),
        center: [116.397451, 39.909187],
        formData: {
          speed: localStorage.getItem('speed') ? Number(localStorage.getItem('speed')) : 80,
          parking: localStorage.getItem('parking') ? Number(localStorage.getItem('parking')) : 5,
          dispatchNo: '', //调度单号
          startTime: '',
          endTime: '',
          vehicle: '',
        },
        dispatchDataList: [], //调度单号列表
        vehicleList: [], //车辆列表
        chartData_option: {}, //图表
        coordinate: [], // 轨迹点位列表
        polyline: [],
        markers: [], // 异常点
        boxLoading: false,
        dataTime: {
          // 开始时间范围限制
          startTimeData: (time) => {
            if (this.formData.endTime !== '' && this.formData.endTime != null) {
              var times = new Date(this.formData.endTime).getTime()
              return time.getTime() > times
            }
          },
          // 结束时间范围限制
          endTimeData: (time) => {
            // 获取当前日期的23:59:59
            const today = new Date()
            today.setHours(23, 59, 59, 999)
            return time.getTime() > today.getTime()
          },
        },
        statusType: 'CHELIANG',
        statusList: [
          {
            label: '车辆查询',
            value: 'CHELIANG',
          },
          {
            label: '调度单查询',
            value: 'DIAODU',
          },
        ],
        distinguish: '',
        TimedialogVisible: false,
        boxHeight: 0,
        topContent: null, //上部分
        bottomContent: null, //下部分
        drivingDataList: [], // 轨迹点列表
        carMarkerList: [],
        analysisList: [],
        vinInfoList: [], //调度单信息
        isShowBox: true, //控制显示隐藏
        isShowTopBottomBox: true,
        ManualTime: '',
        currentInfo: {}, // 当前车辆信息
        customMap: null,
        infoWindow: null,
        vehicleListLoading: false,
        fuzzySelectLoading: false,
        lastUpdateTime: 0, // 添加最后更新时间
        updateInterval: 200, // 更新间隔时间（毫秒）
        rotation: true,
        lusContent: '',
        speed: 50000,
        icon: {
          url: 'http://api.map.baidu.com/library/LuShu/1.2/examples/car.png',
          size: {
            width: 52,
            height: 26,
          },
          opts: {
            anchor: {
              width: 27,
              height: 13,
            },
          },
        },
        play: false,
        btnText: '播放',
      }
    },
    watch: {},

    created() {},

    mounted() {
      // 获取从别的页面跳转过来的数据 如果携带了参数 就赋值给表单
      let newData = localStorage.getItem('form')
      if (newData) {
        let newRouterParams = JSON.parse(newData)
        if (newRouterParams.routerParams && newRouterParams.routerParams.dispatchNo) {
          this.statusType = 'DIAODU'
          this.formData.dispatchNo = newRouterParams.routerParams.dispatchNo
          this.handleQuery()
        }
      }
      this.getChartData()
      // 自定义关闭按钮的功能
      window.closeInfoWindow = () => {
        if (this.customMap && this.infoWindow) {
          if (this.infoWindow.isOpen()) {
            this.customMap.closeInfoWindow()
          }
        }
      }
    },
    methods: {
      enter() {
        if (this.distinguish === 'StartTime') {
          this.formData.startTime = this.formatDateToYYYYMMDD(this.ManualTime)
        } else if (this.distinguish === 'EndTime') {
          this.formData.endTime = this.formatDateToYYYYMMDD(this.ManualTime)
        }
        this.TimedialogVisible = false
      },
      Manual(str) {
        console.log(str,'str');
        if (str === 'StartTime') {
          this.ManualTime = this.formData.startTime
        } else if (str === 'EndTime') {
          this.ManualTime = this.formData.endTime
        }
        this.distinguish = str
        this.TimedialogVisible = true
      },
      formatDateToYYYYMMDD(input) {
        let date
        let hasTime = false
        let hours = '00',
          minutes = '00',
          seconds = '00'

        // 1. 处理中文日期格式（可能含时间）
        if (input.includes('年')) {
          // 匹配格式：2025年6月7日 或 2025年6月7日11:58:33
          const cnPattern = /(\d+)年(\d+)月(\d+)日(?:(\d+):(\d+)(?::(\d+))?)?/
          const match = input.match(cnPattern)

          if (!match) throw new Error('Invalid Chinese date format')

          const [, year, month, day, h, m, s] = match
          date = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`)

          // 如果有时分秒
          if (h) {
            hasTime = true
            hours = h.padStart(2, '0')
            minutes = m.padStart(2, '0')
            seconds = (s || '00').padStart(2, '0')
          }
        }
        // 2. 处理特殊格式 YYYY-MM-DD-HH:mm
        else if (/^\d{4}-\d{2}-\d{2}-\d{2}:\d{2}$/.test(input)) {
          const [datePart, timePart] = input.split('-').slice(-2)
          const dateStr = input.split('-').slice(0, 3).join('-')
          date = new Date(dateStr)
          hasTime = true
          ;[hours, minutes] = timePart.split(':')
        }
        // 3. 处理其他格式（原逻辑）
        else {
          if (input.includes('-')) {
            date = new Date(input)
          } else if (input.includes('/')) {
            date = new Date(input.replace(/\//g, '-'))
          } else {
            date = new Date(input)
          }

          // 检查原始输入是否包含时间部分
          hasTime = /(\d{1,2}:\d{1,2}(:\d{1,2})?)/.test(input)
          if (hasTime) {
            hours = String(date.getHours()).padStart(2, '0')
            minutes = String(date.getMinutes()).padStart(2, '0')
            seconds = String(date.getSeconds()).padStart(2, '0')
          }
        }

        // 验证日期有效性
        if (isNaN(date.getTime())) throw new Error('Invalid date')

        // 统一格式化
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      },
      // 动态设置播放速度
      handleSpeedChange(value) {
        console.log(value)
        this.speed = value
        // 重新设置轨迹播放速度
        if (this.lusContent) {
          this.lusContent.setSpeed(value)
        }
      },

      handler({ BMap, map }) {
        this.customMap = map
      },

      // 改变左侧筛选的现实隐藏
      changeLeft() {
        this.isShowBox = !this.isShowBox
      },

      changeChartBox() {
        this.isShowTopBottomBox = !this.isShowTopBottomBox
      },

      // tab切换
      handleClick(tab, event) {
        this.statusType = tab.paneName
        this.currentInfo = {}
        this.closeInformation() //关闭信息窗
        // 初始化地图 清空所有覆盖物
        this.customMap.clearOverlays()
        if (tab.paneName == 'CHELIANG') {
          this.formData.dispatchNo = '' //调度单号
          localStorage.setItem('form', JSON.stringify({ routerParams: null }))
        } else {
          this.formData.startTime = ''
          this.formData.endTime = ''
          this.formData.vehicle = ''
        }
        this.getChartData() // 初始化速度曲线图
      },

      //远程模糊搜索下拉
      remoteSelectMethod(query) {
        if (query) {
          this.fuzzySelectLoading = true
          getRepairProjectSelectOptionApi({
            dataSource: '调度单',
            label: 'name',
            value: 'name',
            keyword: query,
          })
            .then((response) => {
              if (response && response.code == 200) {
                this.fuzzySelectLoading = false
                if (response.data) {
                  this.dispatchDataList = response.data
                }
              }
            })
            .catch((error) => {
              this.fuzzySelectLoading = false
              this.dispatchDataList = []
            })
        }
      },

      // 查看轨迹
      viewTrack(params) {
        this.boxLoading = true
        if (this.coordinate.length > 0) {
          this.coordinate = []
          this.customMap.clearOverlays() // 清除所有的地图覆盖物
        }
        this.getChartData() // 初始化速度曲线图
        this.coordinate = [] //初始化轨迹
        getSegmentTrace(params)
          .then((response) => {
            this.boxLoading = false
            if (response.code == 200) {
              let newData = response.data
              if (!newData) {
                ElMessage.error('查询数据为空！')
                return
              }
              this.analysisList = newData.analysis
              this.drivingDataList = newData.driving //获取轨迹点列表
              this.customPopUps(newData.statistics, newData.tasks, newData.vinInfos, newData.belongInfo)
              let speedsList = newData.statistics.speeds
              this.tracesData = newData.traces //轨迹
              if (this.tracesData.length > 0) {
                this.tracesData.forEach((itemDriving) => {
                  const bdPoint = gcj02tobd09(Number(itemDriving.lng), Number(itemDriving.lat))
                  this.coordinate.push(new BMap.Point(bdPoint[0], bdPoint[1]))
                })
                let options = {
                  strokeColor: '#0066ff', //线颜色
                  strokeOpacity: 0.9, //线透明度
                  strokeWeight: 6, //线宽
                  strokeStyle: 'solid', //线样式
                }
                //轨迹点
                this.polyline = new BMap.Polyline(this.coordinate, options)
                // 将路线添加至地图实例
                this.customMap.addOverlay(this.polyline)
              } else {
                this.coordinate = []
                this.polyline = []
              }
              if (this.analysisList.length > 0) {
                this.customMap.removeOverlay(this.markers) // 清除所有的marker标记
                this.markers = [] // 清空
                // 异常点
                this.analysisList.forEach((every, index) => {
                  if (every.eventType != 20) {
                    if (every.eventDesc.start) {
                      const lnglat = gcj02tobd09(Number(every.eventDesc.start.split(',')[0]), Number(every.eventDesc.start.split(',')[1]))
                      // 逆地理解析
                      var myGeo = new BMap.Geocoder({ extensions_town: true })
                      myGeo.getLocation(new BMap.Point(lnglat[0], lnglat[1]), function (result) {
                        if (result) {
                          every.eventDesc.pointName = result.address
                        } else {
                          console.error('根据经纬度查询地址失败')
                        }
                      })
                    }
                  } else {
                    //不解析收费站
                    every.eventDesc.pointName = every.eventDesc.address
                  }

                  let icon = ''
                  switch (every.eventType) {
                    case 2: //超速
                      icon = 'http://api.haodaoda.com/static/wx/mini/chao_icon.png'
                      break
                    case 3: //正常休息
                      icon = 'http://api.haodaoda.com/static/wx/mini/xiuxi_icon.png'
                      break
                    case 4: //睡觉
                      icon = 'http://api.haodaoda.com/static/wx/mini/shuijiao_icon.png'
                      break
                    case 5: //急刹车
                      icon = 'http://api.haodaoda.com/static/wx/mini/ji_icon.png'
                      break
                    case 6: //急加速
                      icon = 'http://api.haodaoda.com/static/wx/mini/ji_icon.png'
                      break
                    case 7: //急转弯
                      icon = 'http://api.haodaoda.com/static/wx/mini/ji_icon.png'
                      break
                    case 8: //疲劳驾驶
                      icon = 'http://api.haodaoda.com/static/wx/mini/pilao_icon.png'
                      break
                    case 9: //短暂停留
                      icon = 'http://api.haodaoda.com/static/wx/mini/tingche_icon.png'
                      break
                    case 10: //结束导航
                      icon = 'http://api.haodaoda.com/static/wx/mini/dingwei_zhongdian.png'
                      break
                    case 11: //偏航
                      icon = 'http://api.haodaoda.com/static/wx/mini/pianli_icon.png'
                      break
                    case 12: //开始导航点
                      icon = 'http://api.haodaoda.com/static/wx/mini/dingwei_qidian.png'
                      break
                    case 13: //进入高速
                      icon = 'http://api.haodaoda.com/static/wx/mini/shirugaosu_icon.png'
                      break
                    case 14: //离开高速
                      icon = 'http://api.haodaoda.com/static/wx/mini/shichugaosu_icon.png'
                      break
                    case 15: //离开高速
                      icon = 'http://api.haodaoda.com/static/wx/mini/changchangtingche_icon.png'
                      break
                    case 16: //继续导航
                      icon = 'http://api.haodaoda.com/static/wx/mini/qita_icon.png'
                      break
                    case 17: //刷新路线
                      icon = 'http://api.haodaoda.com/static/wx/mini/qita_icon.png'
                      break
                    case 18: //动态规划
                      icon = 'http://api.haodaoda.com/static/wx/mini/qita_icon.png'
                      break
                    case 19: //调整策略路线
                      icon = 'http://api.haodaoda.com/static/wx/mini/qita_icon.png'
                      break
                    case 20: // 收费站
                      icon = 'http://api.haodaoda.com/static/wx/mini/feestation_icon.png'
                      break
                    case 21: // 离线
                      icon = 'https://api.haodaoda.com/static/wx/mini/phone_offline.png'
                      break
                    case 22: // 在线
                      icon = 'https://api.haodaoda.com/static/wx/mini/phone_online.png'
                      break
                    case 26: // 路线偏离
                      icon = 'https://api.haodaoda.com/static/wx/mini/luxianpianli.png'
                      break
                    case 27: // 违规倒板
                      icon = 'https://api.haodaoda.com/static/wx/mini/weiguidaoban.png'
                      break
                    case 28: // 车辆离线
                      icon = 'https://api.haodaoda.com/static/wx/mini/chelianglixian.png'
                      break
                    case 30: // 装车
                      icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/zhuang_icon.png'
                      break
                    case 31: // 卸车
                      icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/xie_icon.png'
                      break
                    default:
                      icon = 'http://api.haodaoda.com/static/wx/mini/qita_icon.png'
                      break
                  }
                  // 指定单个marker 标记的内容
                  if (every.eventDesc) {
                    const bdPoint = gcj02tobd09(Number(every.lng), Number(every.lat))
                    var myIcon = null
                    // 定义图标
                    if (every.eventType === 12 || every.eventType === 10) {
                      myIcon = new BMap.Icon(
                        icon, // 图标图片的url
                        new BMap.Size(32, 36), // 图标大小
                        {
                          anchor: new BMap.Size(16, 36), // 设置图标的定位点（偏移）
                          imageOffset: new BMap.Size(0, 0), // 图标图片的偏移
                          imageSize: { width: 32, height: 36 },
                        },
                      )
                    } else {
                      myIcon = new BMap.Icon(
                        icon, // 图标图片的url
                        new BMap.Size(24, 26), // 图标大小
                        {
                          anchor: new BMap.Size(12, 26), // 设置图标的定位点（偏移）
                          imageOffset: new BMap.Size(0, 0), // 图标图片的偏移
                          imageSize: { width: 24, height: 26 },
                        },
                      )
                    }
                    var point = new BMap.Point(bdPoint[0], bdPoint[1]) // 经纬度
                    var marker = new BMap.Marker(point, { icon: myIcon, offset: new BMap.Size(0, 8) })
                    // 添加点击事件
                    marker.addEventListener('click', (e) => {
                      this.addMarkerTip(every)
                    })
                    this.markers.push(point)
                    this.customMap.addOverlay(marker) // 添加到地图
                  }
                })
              } else {
                this.customMap.removeOverlay(this.markers) // 清除所有的marker标记
                this.markers = [] // 清空
              }
              var view = this.customMap.getViewport(this.markers) // markers中包含了所有点
              // 缩放地图到合适的视野级别
              this.zoom = view.zoom
              this.bdMapCenter = {
                longitude: view.center.lng,
                latitude: view.center.lat,
              }
              this.getChartData(speedsList)
            }
          })
          .catch(() => {
            this.boxLoading = false
            this.drivingDataList = []
            this.analysisList = []
            this.vinInfoList = [] //调度单信息
          })
      },
      // 速度曲线图
      getChartData(data) {
        let newSpeeds = []
        // let newHeights = [];
        // let heightList = []; //海拔
        // let maxHeight = 5;
        let newOilFuel = []
        let oilFuelList = [] //油耗
        let newPosTime = []
        let areaList = [] //区域
        let oilFuelHeight = 5
        if (data && data.length > 0) {
          data.forEach((item, index) => {
            newSpeeds.push([index, item.posSpeed])
            // newHeights.push([index, item.height]);
            // heightList.push(item.height); //海拔
            newOilFuel.push([index, item.oilFuel]) //油耗
            oilFuelList.push(item.oilFuel) //油耗
            areaList.push(item.area) //区域
            if (item.posTime) {
              newPosTime.push(item.posTime.substring(0, 10) + '\n' + item.posTime.substring(11, 16))
            }
          })
          // maxHeight = Math.max(...heightList) * 1.2;
          oilFuelHeight = Math.max(...oilFuelList) * 1.2
        }
        this.getOptionData(newSpeeds, newOilFuel, oilFuelHeight, Number(this.formData.speed), newPosTime, areaList)
      },

      //获取车辆列表
      getVehicleListData(value) {
        let data = {
          dataSource: '车队-车辆',
          label: 'carrierType,carrierName,vehicleNo',
          value: 'vehicleId,vehicleNo,plateColor,carrierId,carrierName,carrierType',
          keyword: value ? value : '',
        }
        this.vehicleListLoading = true
        inTransitVehicleSelect(data)
          .then((response) => {
            this.vehicleListLoading = false
            if (response.code == 200) {
              this.vehicleList = response.data
            }
          })
          .catch(() => {
            this.vehicleListLoading = false
            this.vehicleList = []
          })
      },
      //获取时间查询 开始的时间
      getStartDate(e) {
        this.formData.startTime = e
      },
      //获取时间查询 结束的时间
      getEndDate(e) {
        this.formData.endTime = e
      },
      // 速度阀值
      speedChange(e) {
        this.formData.speed = e
      },
      // 停车阀值
      parkingChange(e) {
        this.formData.parking = e
      },
      // 获取图表
      getOptionData(data, oilFuelData, maxHeight, value, xData, areaList) {
        this.chartData_option = {
          animation: true,
          tooltip: {
            trigger: 'axis',
            formatter: (params) => {
              if (this.drivingDataList.length > 0) {
                this.drivingDataList.forEach((item, index) => {
                  let receiveTime = item.receiveTimeStr ? item.receiveTimeStr.substring(0, 10) + '\n' + item.receiveTimeStr.substring(11, 16) : ''
                  if (params[0].name === receiveTime) {
                    // 如果有就删除
                    if (this.carMarkerList.length > 0) {
                      this.carMarkerList.forEach((current, i) => {
                        this.customMap.removeOverlay(current)
                        this.carMarkerList.splice(i, 1)
                      })
                    }
                    var newIcon = new BMap.Icon(
                      'https://api.haodaoda.com/static/wx/mini/car_green.png', // 图标图片的url
                      new BMap.Size(14, 26), // 图标大小
                      {
                        anchor: new BMap.Size(7, 26), // 设置图标的定位点（偏移）
                        imageOffset: new BMap.Size(0, 0), // 图标图片的偏移
                        imageSize: { width: 14, height: 26 },
                      },
                    )
                    const pointInfo = gcj02tobd09(Number(item.lng), Number(item.lat))
                    var point = new BMap.Point(pointInfo[0], pointInfo[1]) // 经纬度
                    var carMarker = new BMap.Marker(point, { icon: newIcon, offset: new BMap.Size(0, 8), rotation: item.direction })
                    this.carMarkerList[index] = carMarker
                    carMarker.addEventListener('click', (e) => {
                      this.addMarkerTip(item, 'car')
                    })
                    this.customMap.addOverlay(carMarker) // 添加到地图
                  }
                })
              }
              var relVal = params[0].name
              for (var i = 0, l = params.length; i < l; i++) {
                if (params[i].seriesName === '实时速度') {
                  relVal +=
                    '<br/>' +
                    params[i].marker +
                    (params[i].value ? '实时速度' + "<span style='color: #102b6a;padding: 0 4px;'>" + params[i].value[1] + '</span>km/h' : ' -')
                } else if (params[i].seriesName === '实时油耗') {
                  relVal +=
                    '<br/>' +
                    params[i].marker +
                    (params[i].value ? '实时油耗 ' + "<span style='color: #102b6a;padding: 0 4px;'>" + params[i].value[1] + '</span>L/100km' : ' -')
                } else if (params[i].seriesName === '所在区域') {
                  relVal +=
                    '<br/>' + params[i].marker + (params[i].value ? '所在区域' + "<span style='color: #102b6a;padding: 0 4px;'>" + params[i].value : ' -')
                }
              }
              return relVal
            },
          },
          grid: {
            top: 40,
            left: 40,
            right: 40,
            bottom: 65,
          },
          dataZoom: [
            {
              type: 'slider',
              show: true,
              xAxisIndex: [0],
              start: 0,
              end: 100,
              bottom: 10,
              height: 20,
              borderColor: 'transparent',
              backgroundColor: '#f5f5f5',
              fillerColor: 'rgba(167,183,204,0.4)',
              handleStyle: {
                color: '#fff',
                borderColor: '#ACB8D1',
              },
              textStyle: {
                color: '#333',
              },
            },
            {
              type: 'inside',
              xAxisIndex: [0],
              start: 0,
              end: 100,
              zoomOnMouseWheel: true,
              moveOnMouseMove: true,
              moveOnMouseWheel: true,
            },
          ],
          legend: {
            top: 10,
            left: 60,
            data: ['实时速度', '实时油耗'],
          },
          xAxis: {
            name: '',
            type: 'category',
            splitLine: {
              show: false, //网格线不显示
            },
            axisTick: {
              show: false,
            },
            minorSplitLine: {
              show: true,
              lineStyle: {
                color: '#f0f0f0',
              },
            },
            data: xData,
          },
          yAxis: [
            {
              type: 'value',
              name: 'km/h',
              min: 0,
              max: 100,
              splitNumber: 5,
              interval: 100 / 5,
              // splitLine: {
              //   show: true, //网格线不显示
              //   lineStyle: {
              //     color: '#f0f0f0',
              //   },
              // },
              axisTick: {
                show: false,
              },
              axisLine: {
                show: true,
              },
              // minorSplitLine: {
              //   show: true,
              //   lineStyle: {
              //     color: '#f0f0f0',
              //   },
              // },
            },
            {
              type: 'value',
              name: 'L/100km',
              scale: false,
              max: 75, //Math.ceil(maxHeight),
              min: 0,
              interval: 75 / 5, // Math.ceil(maxHeight) / 5,
              splitNumber: 5,
              // splitLine: {
              //   show: true, //网格线不显示
              //   lineStyle: {
              //     color: '#f0f0f0',
              //   },
              // },
              axisLine: {
                show: true,
              },
              axisTick: {
                show: false,
              },
              boundaryGap: [0.2, 0.2], //边界间隙
            },
          ],
          series: [
            {
              type: 'line',
              name: '实时速度',
              showSymbol: false,
              markLine: {
                silent: true, // 不触发鼠标事件
                symbol: 'none', //去掉箭头
                label: {
                  position: 'end', // 表现内容展示的位置
                  formatter: '', // 标线展示的内容
                  color: '#8C8C8C', // 展示内容颜色
                },
                data: [
                  {
                    yAxis: value,
                    lineStyle: {
                      width: 2,
                      color: '#FD0100',
                    },
                  },
                ],
              },
              itemStyle: {
                lineStyle: {
                  width: 1, //折线宽度
                  color: '#0081ff', //折线颜色
                  // color: data.map(function (item) {
                  //  if(item[1] > 50) {
                  //    return '#FD0100';
                  //  } else {
                  //    return '#5470c6';
                  //  }
                  // }), //折线颜色
                },
              },
              lineStyle: {
                color: '#5470C6',
                width: 1,
              },
              data: data,
            },
            {
              type: 'bar',
              name: '实时油耗',
              showSymbol: false,
              // areaStyle: {},
              yAxisIndex: 1,
              data: oilFuelData,
            },
            {
              type: 'line',
              name: '所在区域',
              showSymbol: false,
              yAxisIndex: 1,
              data: areaList,
            },
          ],
        }
        this.$nextTick(() => {
          this.initChart(this.chartData_option)
        })
      },

      initChart(options) {
        let getchart = echarts.getInstanceByDom(document.getElementById(this.chartId))
        if (getchart == null) {
          getchart = echarts.init(document.getElementById(this.chartId))
        }
        getchart.setOption(options, true)

        // 添加dataZoom事件监听
        getchart.on('datazoom', (params) => {
          // 清除所有车辆标记
          if (this.carMarkerList.length > 0) {
            this.carMarkerList.forEach((current) => {
              this.customMap.removeOverlay(current)
            })
            this.carMarkerList = []
          }

          // 获取当前显示的数据范围
          const startValue = getchart.getOption().dataZoom[0].startValue
          const endValue = getchart.getOption().dataZoom[0].endValue

          // 获取可视区域内的数据
          // const visibleData = this.drivingDataList.filter((item, index) => {
          //   return index >= startValue && index <= endValue
          // })

          // 打印可视区域内的数据
          //     time: item.receiveTimeStr,
          //     speed: item.speed,
          //     location: `${item.lng}, ${item.lat}`,
          //     address: item.address,
          //     direction: item.direction,
          //   })),
          // })

          // const newData = {
          //   startIndex: startValue,
          //   endIndex: endValue,
          //   dataCount: visibleData.length,
          //   timeRange: {
          //     start: visibleData[0]?.receiveTimeStr,
          //     end: visibleData[visibleData.length - 1]?.receiveTimeStr,
          //   },
          //   data: visibleData.map((item) => ({
          //     time: item.receiveTimeStr,
          //     speed: item.speed,
          //     lng: item.lng,
          //     lat: item.lat,
          //     address: item.address,
          //     direction: item.direction,
          //   })),
          // }

          // // 添加节流处理
          // const currentTime = Date.now()
          // if (currentTime - this.lastUpdateTime >= this.updateInterval) {
          //   this.updateMapTrajectory(newData)
          //   this.lastUpdateTime = currentTime
          // }
        })

        //随着屏幕大小调节图表
        window.addEventListener('resize', () => {
          getchart.resize()
        })
      },

      // 添加更新地图轨迹的方法
      updateMapTrajectory(newData) {
        this.customMap.clearOverlays() // 清空地图上所有覆盖物
        this.coordinate = [] // 重置坐标数组

        // 重画轨迹
        this.drawTrajectory(newData.data)

        // 筛选时间范围内的异常点
        const rangeAnalysisList = this.filterRangeAnalysisList(newData.timeRange)

        // 在地图上渲染异常点
        this.renderAbnormalMarkers(rangeAnalysisList)
      },

      // 绘制轨迹线
      drawTrajectory(trajectoryData) {
        if (trajectoryData.length > 0) {
          trajectoryData.forEach((itemDriving) => {
            const bdPoint = gcj02tobd09(Number(itemDriving.lng), Number(itemDriving.lat))
            this.coordinate.push(new BMap.Point(bdPoint[0], bdPoint[1]))
          })
          let options = {
            strokeColor: '#0066ff', //线颜色
            strokeOpacity: 0.9, //线透明度
            strokeWeight: 6, //线宽
            strokeStyle: 'solid', //线样式
          }
          //轨迹点
          this.polyline = new BMap.Polyline(this.coordinate, options)
          // 将路线添加至地图实例
          this.customMap.addOverlay(this.polyline)
        } else {
          this.coordinate = []
          this.polyline = []
        }
      },

      // 筛选时间范围内的异常点
      filterRangeAnalysisList(timeRange) {
        return this.analysisList.filter((item) => {
          if (!item.positionTimeStr || !timeRange.start || !timeRange.end) return false

          const eventTime = new Date(item.positionTimeStr).getTime()
          const startTime = new Date(timeRange.start).getTime()
          const endTime = new Date(timeRange.end).getTime()

          return eventTime >= startTime && eventTime <= endTime
        })
      },

      // 获取异常点图标
      getAbnormalIcon(eventType) {
        let icon = ''
        switch (eventType) {
          case 2: //超速
            icon = 'http://api.haodaoda.com/static/wx/mini/chao_icon.png'
            break
          case 3: //正常休息
            icon = 'http://api.haodaoda.com/static/wx/mini/xiuxi_icon.png'
            break
          case 4: //睡觉
            icon = 'http://api.haodaoda.com/static/wx/mini/shuijiao_icon.png'
            break
          case 5: //急刹车
            icon = 'http://api.haodaoda.com/static/wx/mini/ji_icon.png'
            break
          case 6: //急加速
            icon = 'http://api.haodaoda.com/static/wx/mini/ji_icon.png'
            break
          case 7: //急转弯
            icon = 'http://api.haodaoda.com/static/wx/mini/ji_icon.png'
            break
          case 8: //疲劳驾驶
            icon = 'http://api.haodaoda.com/static/wx/mini/pilao_icon.png'
            break
          case 9: //短暂停留
            icon = 'http://api.haodaoda.com/static/wx/mini/tingche_icon.png'
            break
          case 10: //结束导航
            icon = 'http://api.haodaoda.com/static/wx/mini/dingwei_zhongdian.png'
            break
          case 11: //偏航
            icon = 'http://api.haodaoda.com/static/wx/mini/pianli_icon.png'
            break
          case 12: //开始导航点
            icon = 'http://api.haodaoda.com/static/wx/mini/dingwei_qidian.png'
            break
          case 13: //进入高速
            icon = 'http://api.haodaoda.com/static/wx/mini/shirugaosu_icon.png'
            break
          case 14: //离开高速
            icon = 'http://api.haodaoda.com/static/wx/mini/shichugaosu_icon.png'
            break
          case 15: //长时间停车
            icon = 'http://api.haodaoda.com/static/wx/mini/changchangtingche_icon.png'
            break
          case 20: // 收费站
            icon = 'http://api.haodaoda.com/static/wx/mini/feestation_icon.png'
            break
          case 21: // 离线
            icon = 'https://api.haodaoda.com/static/wx/mini/phone_offline.png'
            break
          case 22: // 在线
            icon = 'https://api.haodaoda.com/static/wx/mini/phone_online.png'
            break
          case 26: // 路线偏离
            icon = 'https://api.haodaoda.com/static/wx/mini/luxianpianli.png'
            break
          case 27: // 违规倒板
            icon = 'https://api.haodaoda.com/static/wx/mini/weiguidaoban.png'
            break
          case 28: // 车辆离线
            icon = 'https://api.haodaoda.com/static/wx/mini/chelianglixian.png'
            break
          case 30: // 装车
            icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/zhuang_icon.png'
            break
          case 31: // 卸车
            icon = 'https://haodaoda-public.oss-cn-beijing.aliyuncs.com/weixin/icon/xie_icon.png'
            break
          default:
            icon = 'http://api.haodaoda.com/static/wx/mini/qita_icon.png'
            break
        }
        return icon
      },

      // 创建标记图标
      createMarkerIcon(iconUrl, isSpecial = false) {
        return new BMap.Icon(iconUrl, isSpecial ? new BMap.Size(32, 36) : new BMap.Size(24, 26), {
          anchor: isSpecial ? new BMap.Size(16, 36) : new BMap.Size(12, 26),
          imageOffset: new BMap.Size(0, 0),
          imageSize: isSpecial ? { width: 32, height: 36 } : { width: 24, height: 26 },
        })
      },

      // 渲染异常点标记
      renderAbnormalMarkers(abnormalPoints) {
        this.markers = [] // 清空标记点数组

        abnormalPoints.forEach((every, index) => {
          // 处理地理位置信息
          this.processLocationInfo(every)

          // 获取对应的图标URL
          const iconUrl = this.getAbnormalIcon(every.eventType)

          // 创建标记
          if (every.eventDesc) {
            const bdPoint = gcj02tobd09(Number(every.lng), Number(every.lat))
            const isSpecialMarker = every.eventType === 12 || every.eventType === 10
            const myIcon = this.createMarkerIcon(iconUrl, isSpecialMarker)

            var point = new BMap.Point(bdPoint[0], bdPoint[1]) // 经纬度
            var marker = new BMap.Marker(point, { icon: myIcon, offset: new BMap.Size(0, 8) })

            // 添加点击事件
            marker.addEventListener('click', (e) => {
              this.addMarkerTip(every)
            })

            this.markers.push(point)
            this.customMap.addOverlay(marker) // 添加到地图
          }
        })
      },

      // 处理地理位置信息
      processLocationInfo(point) {
        if (point.eventType != 20) {
          if (point.eventDesc.start) {
            const lnglat = gcj02tobd09(Number(point.eventDesc.start.split(',')[0]), Number(point.eventDesc.start.split(',')[1]))
            // 逆地理解析
            var myGeo = new BMap.Geocoder({ extensions_town: true })
            myGeo.getLocation(new BMap.Point(lnglat[0], lnglat[1]), function (result) {
              if (result) {
                point.eventDesc.pointName = result.address
              } else {
                console.error('根据经纬度查询地址失败')
              }
            })
          }
        } else {
          //不解析收费站
          point.eventDesc.pointName = point.eventDesc.address
        }
      },

      //年月日时分秒转时间戳
      getTimestamp(time) {
        if (time) {
          //把时间日期转成时间戳
          return new Date(time.replace(/\-/g, '/')).getTime() / 1000
        }
      },

      // 重置查询条件
      resetQuery() {
        this.formData = {
          vehicle: '',
          startTime: '',
          endTime: '',
          speed: 80,
          parking: 5,
          dispatchNo: '',
        }
        this.currentInfo = {}
        this.closeInformation() //关闭信息窗
        // 初始化地图 清空所有覆盖物
        this.customMap.clearOverlays()
        this.getChartData() // 初始化速度曲线图
        // 存储速度阀值，停车阀值
        localStorage.setItem('speed', this.formData.speed)
        localStorage.setItem('parking', this.formData.parking)
      },
      // 点击查询
      handleQuery() {
        var params = {}
        if (this.statusType == 'CHELIANG') {
          // 时间查询
          if (this.formData.startTime === '' || this.formData.startTime == null) {
            ElMessage.warning('请选择开始时间')
            return
          }
          if (this.formData.endTime === '' || this.formData.endTime == null) {
            ElMessage.warning('请选择结束时间')
            return
          }
          if (this.getTimestamp(this.formData.startTime) > this.getTimestamp(this.formData.endTime)) {
            ElMessage.warning('查询开始时间不可晚于结束时间')
            return
          }
          if (this.formData.vehicle === '' || this.formData.vehicle == null) {
            ElMessage.warning('请选择车辆')
            return
          }
          params = {
            startTime: this.formData.startTime ? this.formData.startTime : '',
            endTime: this.formData.endTime ? this.formData.endTime : '',
            speedLimit: this.formData.speed,
            stayLimit: this.formData.parking,
            vehicle: this.formData.vehicle,
          }
        } else {
          // 调度单号
          if (this.formData.dispatchNo === '' || this.formData.dispatchNo == null) {
            ElMessage.warning('请选择调度单号')
            return
          }
          params = {
            dispatchNo: this.formData.dispatchNo,
            speedLimit: this.formData.speed,
            stayLimit: this.formData.parking,
          }
        }
        this.currentInfo = {}
        this.showInfoWindow = false
        this.viewTrack(params) //查询轨迹
        // 存储速度阀值，停车阀值
        localStorage.setItem('speed', this.formData.speed)
        localStorage.setItem('parking', this.formData.parking)
      },
      // 异常点信息
      addMarkerTip(item, type) {
        // item 为当前点击的内容
        var currentItem = item
        if (this.customMap && this.infoWindow) {
          if (this.infoWindow.isOpen()) {
            this.customMap.closeInfoWindow()
          }
        }
        var opts = {
          width: 420, // 信息窗口宽度
          enableCloseOnClick: false, // 点击地图时不关闭
        }
        let content = []
        if (type === 'car') {
          content.push('<div class="titleBox">')
          content.push('<h4>车辆信息</h4>')
          content.push('<button class="close-btn" onclick="closeInfoWindow()">X</button>')
          content.push('</div>')
          content.push('<div class="contentBox">')
          content.push(
            '<span class="title">当时速度：</span><span class="contentInfo">' + currentItem.speed ? currentItem.speed + 'km/h' : ' - ' + '</span><br>',
          )
          content.push('<span class="title">发生位置：</span> <span class=\'contentInfo\'>' + currentItem.address + '</span><br>')
          content.push('<span class="title">当时时间：</span>' + currentItem.positionTimeStr + '<br>')
          content.push('<div >')
        } else if (item.eventType === 30 || item.eventType === 31) {
          content.push('<div class="titleBox">')
          content.push('<h4>' + currentItem.eventTypeName + '</h4>')
          content.push('<button class="close-btn" onclick="closeInfoWindow()">X</button>')
          content.push('</div>')
          content.push('<div class="contentBox">')
          content.push('<span class="title">地点：</span><span class="contentInfo">' + currentItem.address + '</span><br>')
          content.push('<span class="title">时间：</span><span class="contentInfo">' + currentItem.positionTimeStr + '</span><br>')
          content.push('<span class="title">VIN：</span><span class="contentInfo">' + currentItem.eventDesc.content + '</span><br>')
        } else {
          content.push('<div class="titleBox">')
          content.push('<h4>' + currentItem.vehicleNo + '</h4>')
          content.push('<button class="close-btn" onclick="closeInfoWindow()">X</button>')
          content.push('</div>')
          content.push('<div class="contentBox">')
          content.push('<span class="title">点位状态：</span><span class="contentInfo">' + currentItem.eventTypeName + '</span><br>')
          content.push('<span class="title">发生位置：</span> <span class="contentInfo">' + currentItem.eventDesc.pointName + '</span><br>')
          content.push('<span class="title">发生时间：</span>' + currentItem.eventDesc.timeStartStr + '<br>')
          if (item.eventType === 9 || item.eventType === 3 || item.eventType === 4 || item.eventType === 8 || item.eventType === 28) {
            content.push('<span class="title">持续时间：</span>' + currentItem.eventDesc.timeStr + '<br>')
          }

          if (item.eventType === 2) {
            content.push('<span class="title">超速速度：</span><span class="contentInfo">' + currentItem.eventDesc.speed + 'km/h</span>')
          }
          content.push('<div >')
        }
        this.infoWindow = new BMap.InfoWindow(content.join(''), opts) // 创建信息窗口对象
        const pointInfo = gcj02tobd09(Number(currentItem.lng), Number(currentItem.lat))
        var newPoint = new BMap.Point(Number(pointInfo[0]), Number(pointInfo[1]))
        this.customMap.openInfoWindow(this.infoWindow, newPoint) //开启信息窗口
      },
      // 获取当前日期
      getMyDate(value) {
        let date = new Date()
        let y = date.getFullYear()
        let m = date.getMonth() + 1
        m = m < 10 ? '0' + m : m
        let d = date.getDate()
        d = d < 10 ? '0' + d : d
        let hh = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
        let mm = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
        let ss = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
        var time
        if (value === 'start') {
          time = y + '-' + m + '-' + d + ' 00:00:00'
        } else {
          time = y + '-' + m + '-' + d + ' ' + hh + ':' + mm + ':' + ss
        }
        return time
      },

      // 自定义信息窗
      customPopUps(row, taskRow, vinInfoRow, belongInfo) {
        this.currentInfo = { ...row, ...belongInfo } // 存储当前点击的车辆信息
        this.currentTaskList = taskRow || [] // 存储任务信息
        this.vinInfoList = vinInfoRow ? vinInfoRow : [] //调度单信息
        this.showInfoWindow = true
      },

      // 展开隐藏信息窗
      closeInformation(value) {
        this.showInfoWindow = value
      },

      changeBtnText(val) {
        this.btnText = val
        if (val === '播放') {
          this.play = false
        }
      },

      toggle() {
        // 播放时，显示车辆编号
        this.lusContent = this.currentInfo.vehicleNo
        this.play = !this.play
      },
    },
  }
</script>

<style scoped>
  .TrackQuery {
    height: 100%;
    /* margin: 0px 10px; */
  }

  .gutterRow {
    display: flex;
  }

  .TrackQuery .leftForm {
    background: #fff;
    padding: 20px 10px;
    width: 350px;
    position: relative;
  }

  .TrackQuery .leftForm1 {
    width: 0px;
    background: #fff;
    position: relative;
  }

  .topBox {
    background: #fff;
    width: 100%;
    height: 30vh;
    padding: 0px;
    z-index: 10;
    border-top: 1px solid #eee;
  }

  .bottomBox {
    background: #fff;
    width: 100%;
    height: 0px;
    padding: 0px;
    z-index: 10;
  }

  .TrackQuery :deep(div:nth-child(9)) {
    top: 28px !important;
  }

  .TrackQuery .queryCondition {
    width: 300px;
    height: -webkit-fill-available;
    background: #fff;
    position: relative;
    overflow: scroll;
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;
    z-index: 0;
  }

  .common_tab_border_card {
    margin-bottom: 20px;
  }

  .common_tab_border_card :deep(.el-tabs--border-card > .el-tabs__header) {
    margin: 0px;
    border-bottom: none;
  }

  .common_tab_border_card :deep(.el-tabs--border-card) {
    border: 1px solid #dcdfe6;
    border-radius: 2px;
  }

  .common_tab_border_card :deep(.el-tabs__content) {
    padding: 0px;
  }

  .common_tab_border_card :deep(.el-tabs__nav-wrap) {
    margin-bottom: 0px;
    margin-right: -1px;
  }

  .queryCondition .queryBtn {
    width: 50%;
  }

  .bm-view-up {
    width: 100%;
    height: calc(70vh - 84px);
  }

  .bm-view-down {
    width: 100%;
    height: calc(100vh - 84px);
  }
  /*
  .bm-view :deep(div:nth-child(9)) {
    top: 30px !important;
  } */
</style>
