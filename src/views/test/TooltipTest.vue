<!--
 * @Author: llm
 * @Date: 2025-01-08
 * @LastEditors: llm
 * @LastEditTime: 2025-01-08
 * @Description: Tooltip 层级问题测试页面
-->
<template>
  <div class="tooltip-test-container">
    <h2>Tooltip 层级问题测试</h2>
    
    <!-- 背景表格 -->
    <div class="background-table">
      <h3>背景表格（应该被弹窗遮挡）</h3>
      <vxe-table :data="backgroundTableData" border>
        <vxe-column field="name" title="姓名">
          <template #default="{ row }">
            <el-tooltip content="这是背景表格的 tooltip，不应该在弹窗中显示" placement="top">
              <span>{{ row.name }}</span>
            </el-tooltip>
          </template>
        </vxe-column>
        <vxe-column field="age" title="年龄">
          <template #default="{ row }">
            <SmartTooltip content="这是背景表格的智能 tooltip" placement="top">
              <span>{{ row.age }}</span>
            </SmartTooltip>
          </template>
        </vxe-column>
      </vxe-table>
    </div>

    <!-- 打开弹窗按钮 -->
    <div class="button-group">
      <el-button type="primary" @click="dialogVisible = true">
        打开弹窗测试 Tooltip 层级
      </el-button>
    </div>

    <!-- 测试弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="弹窗中的表格"
      width="80%"
      :z-index="2000"
    >
      <div class="dialog-content">
        <h4>弹窗中的表格（tooltip 应该正确显示在最上层）</h4>
        <vxe-table :data="dialogTableData" border>
          <vxe-column field="name" title="姓名">
            <template #default="{ row }">
              <el-tooltip content="这是弹窗中的普通 tooltip，应该显示在最上层" placement="top">
                <span>{{ row.name }}</span>
              </el-tooltip>
            </template>
          </vxe-column>
          <vxe-column field="age" title="年龄">
            <template #default="{ row }">
              <SmartTooltip content="这是弹窗中的智能 tooltip，应该显示在最上层" placement="top">
                <span>{{ row.age }}</span>
              </SmartTooltip>
            </template>
          </vxe-column>
          <vxe-column field="city" title="城市">
            <template #default="{ row }">
              <el-tooltip 
                content="这是弹窗中的长文本 tooltip，用于测试层级是否正确。如果层级正确，这个 tooltip 应该显示在弹窗的最上方，而不会被背景内容遮挡。" 
                placement="top"
              >
                <span>{{ row.city }}</span>
              </el-tooltip>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogVisible = false">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SmartTooltip from '@/components/SmartTooltip/index.vue'

const dialogVisible = ref(false)

// 背景表格数据
const backgroundTableData = ref([
  { name: '张三', age: 25 },
  { name: '李四', age: 30 },
  { name: '王五', age: 28 },
])

// 弹窗表格数据
const dialogTableData = ref([
  { name: '赵六', age: 32, city: '北京' },
  { name: '钱七', age: 27, city: '上海' },
  { name: '孙八', age: 35, city: '广州' },
])
</script>

<style scoped>
.tooltip-test-container {
  padding: 20px;
}

.background-table {
  margin-bottom: 20px;
}

.button-group {
  margin: 20px 0;
  text-align: center;
}

.dialog-content {
  padding: 10px 0;
}

h2, h3, h4 {
  color: #333;
  margin-bottom: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
