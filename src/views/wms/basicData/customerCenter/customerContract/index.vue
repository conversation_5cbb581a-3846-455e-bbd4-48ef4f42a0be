<!--
 * @Author: llm
 * @Date: 2024-12-31 10:37:20
 * @LastEditors: llm
 * @LastEditTime: 2024-12-31 12:31:34
 * @Description:
-->
<template>
  <GlobalPage :pageType="state.pageType" />
</template>
<script lang="ts" setup>
  import { getCurrentInstance, onBeforeMount, reactive } from 'vue'
  import { BasePage, BasePage1, BaseTabPage, BaseObjectPage } from '@/utils/globalImport'
  import GlobalPage from '@/components/GlobalPage/index.vue'
  defineOptions({
    name: 'WmsBasicDataCustomerCenterCustomerContract',
    inheritAttrs: false,
  })
  const { proxy }: any = getCurrentInstance()
  const state = reactive({
    pageType: '',
    pageName: '',
  })
  onBeforeMount(() => {
    state.pageType = proxy.$sideBarStore.$state.pageType
  })
</script>
