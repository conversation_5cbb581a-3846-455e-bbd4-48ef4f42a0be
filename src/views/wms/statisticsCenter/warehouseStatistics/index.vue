<!--
 * @Author: llm
 * @Date: 2024-12-31 10:37:20
 * @LastEditors: llm
 * @LastEditTime: 2024-12-31 12:47:04
 * @Description:
-->
<template>
  <GlobalPage :pageType="state.pageType" />
</template>
<script lang="ts" setup>
  import { getCurrentInstance, onBeforeMount, reactive } from 'vue'
  import GlobalPage from '@/components/GlobalPage/index.vue'
  defineOptions({
    name: 'WmsStatisticsCenterWarehouseStatistics',
    inheritAttrs: false,
  })
  const { proxy }: any = getCurrentInstance()
  const state = reactive({
    pageType: '',
    pageName: '',
  })
  onBeforeMount(() => {
    state.pageType = proxy.$sideBarStore.$state.pageType
  })
</script>
