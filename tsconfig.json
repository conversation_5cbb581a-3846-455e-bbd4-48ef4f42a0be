{
  "compilerOptions": {
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "baseUrl": "./",
    "allowJs": true,
    "paths": {
      "@/*": ["src/*"],
      "@@/*": ["src/components/OtherFormDialogComponent/LowFlowComponent/*"]
    },
    "types": ["vite/client", "unplugin-icons/types/vue", "element-plus/global"],
    "skipLibCheck": true /* Skip type checking all .d.ts files. */,
    "allowSyntheticDefaultImports": true /* 允许默认导入 */,
    "forceConsistentCasingInFileNames": true /* Ensure that casing is correct in imports. */,
    // "suppressImplicitAnyIndexErrors": true,
    "ignoreDeprecations": "5.0"
  },
  "include": [
    "src/**/*.js",
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.jsx",
    "src/**/*.vue",
    "./types/auto-imports.d.ts" // 和 AutoImport dts保持一致 引入即可
  ],
  "exclude": ["node_modules", "dist", "**/*.js"],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}
