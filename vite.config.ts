import vue from '@vitejs/plugin-vue'

import { UserConfig, ConfigEnv, loadEnv, defineConfig } from 'vite'

import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'

import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'

import UnoCSS from 'unocss/vite'
import { viteStaticCopy } from 'vite-plugin-static-copy'
import { createHtmlPlugin } from 'vite-plugin-html'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { fileURLToPath, URL } from 'node:url'
// import legacy from 'vite-plugin-legacy-swc'
import path from 'path'
const pathSrc = path.resolve(__dirname, 'src')
import EnhanceLog from 'vite-plugin-enhance-log'
import viteCompression from 'vite-plugin-compression'
// 打包分析
import { visualizer } from 'rollup-plugin-visualizer'
import removeConsole from 'vite-plugin-remove-console'
export default defineConfig(({ mode }: ConfigEnv): UserConfig => {
  const env = loadEnv(mode, process.cwd())
  return {
    base: '/',
    resolve: {
      alias: {
        '@': pathSrc,
        '@@': fileURLToPath(new URL('./src/components/OtherFormDialogComponent/LowFlowComponent', import.meta.url)),
        // 'vue-i18n': 'vue-i18n/dist/vue-i18n.runtime.esm-bundler.js'
      },
    },
    build: {
      // 启用代码分块和懒加载
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes('node_modules')) {
              // 将 Vue 核心包单独打包
              if (id.includes('vue/dist') || id.includes('@vue/runtime')) {
                return 'vendor-vue-core'
              }
              // 将 Vue 生态系统包单独打包
              if (id.includes('@vue/') && !id.includes('@vue/runtime')) {
                return 'vendor-vue-ecosystem'
              }
              // 将 Element Plus 相关的包单独打包
              if (id.includes('element-plus')) {
                return 'vendor-element-plus'
              }
              // 将 ECharts 相关的包单独打包
              if (id.includes('echarts') || id.includes('zrender')) {
                return 'vendor-echarts'
              }
              // 将地图相关的包单独打包
              if (id.includes('vue-baidu-map')) {
                return 'vendor-map'
              }
              // 将编辑器相关的包单独打包
              if (id.includes('@wangeditor') || id.includes('@vueup/vue-quill') || id.includes('quill')) {
                return 'vendor-editor'
              }
              // 将核心工具包单独打包
              if (id.includes('lodash') || id.includes('axios') || id.includes('vue-router') || id.includes('pinia')) {
                return 'vendor-core'
              }
              // 将大型库单独打包
              if (id.includes('html2canvas') || id.includes('jspdf') || id.includes('xlsx') || id.includes('bwip') || id.includes('vue-plugin-hiprint')) {
                return 'vendor-large-libs'
              }
              // 将数据可视化库单独打包
              if (id.includes('datav') || id.includes('canvg')) {
                return 'vendor-datav'
              }
              // 将 VXETable 相关的包单独打包
              if (id.includes('vxe-table') || id.includes('vxe-pc-ui') || id.includes('@vxe-ui')) {
                return 'vendor-vxe-table'
              }
              // jQuery单独打包
              if (id.includes('jquery')) {
                return 'vendor-jquery'
              }
              // 其他依赖打包到一起
              return 'vendor-others'
            }
          },
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: ({ name }) => {
            if (/\.(ttf|woff2?|eot|svg)(\?.*)?$/.test(name ?? '')) {
              return 'assets/fonts/[name]-[hash].[ext]'
            }
            return 'assets/[ext]/[name]-[hash].[ext]'
          },
        },
      },
      // 根据环境动态配置 sourcemap
      sourcemap: mode === 'development',
      // 使用 esbuild 进行压缩，比 terser 更快
      minify: 'esbuild',
      // 启用 gzip 压缩
      reportCompressedSize: false,
      // 设置 chunk 大小警告限制
      chunkSizeWarningLimit: 1500,
      // 增加构建性能
      target: 'esnext',
      // 启用并行构建
      cssCodeSplit: true,
    },
    // 配置 esbuild 选项
    esbuild: {
      // 禁用 eval
      legalComments: 'none',
      // 启用 sourcemap
      sourcemap: mode === 'development',
      // 优化构建
      treeShaking: true,
      // 启用并行处理
      target: 'esnext',
      // 移除调试信息
      drop: mode === 'production' ? ['console', 'debugger'] : [],
    },
    css: {
      // CSS 预处理器
      preprocessorOptions: {
        //define global scss variable
        scss: {
          additionalData: `@use "@/styles/variables.scss" as *;`,
        },
      },
    },
    server: {
      host: '0.0.0.0',
      port: Number(env.VITE_APP_PORT),
      open: true, // 运行是否自动打开浏览器
      proxy: {
        // 反向代理解决跨域
        [env.VITE_APP_BASE_API]: {
          target: 'https://tlogistics.haodaoda.com', // 线上接口地址1
          // target: 'http://*************:18610', // 线上接口地址1/
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp('^' + env.VITE_APP_BASE_API), ''), // 替换 /dev-api 为 target 接口地
        },
      },
    },
    plugins: [
      removeConsole(), // 移除console
      // legacy({
      //   targets: ['defaults', 'not IE 11'],
      // }),
      vue({
        template: {
          compilerOptions: {
            isCustomElement: (tag) => ['AdvancedFilter', 'PrintTableComponent', 'inspection-dialog', 'SimpleCondition'].includes(tag),
          },
        },
      }),
      createHtmlPlugin({
        inject: {
          data: {
            printLockCss: '<link rel="stylesheet" media="print" href="/css/print-lock.css" />',
          },
        },
      }),
      viteStaticCopy({
        targets: [
          {
            src: 'node_modules/vue-plugin-hiprint/dist/print-lock.css',
            dest: 'css',
          },
        ],
      }),
      vueJsx(),
      UnoCSS({
        /* options */
      }),
      AutoImport({
        // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
        imports: ['vue', '@vueuse/core', 'pinia', 'vue-router'],
        eslintrc: {
          enabled: false, //  Default `false`
          filepath: './.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
          globalsPropValue: true, // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
        },
        resolvers: [
          // 自动导入 Element Plus 相关函数，如：ElMessage, ElMessageBox... (带样式)
          ElementPlusResolver(),
          // 自动导入图标组件
          IconsResolver({
            prefix: 'Icon',
          }),
        ],
        vueTemplate: true, // 是否在 vue 模板中自动导入
        dts: path.resolve(pathSrc, 'types', 'auto-imports.d.ts'), //  自动导入组件类型声明文件位置，默认根目录; false 关闭自动生成
      }),

      Components({
        extensions: ['vue', 'tsx', 'md'],
        globs: ['src/components/*/*.vue', 'src/components/*/*.tsx'],
        include: [/\.vue$/, /\.vue\?vue/, /\.md$/, /\.[tj]sx?$/],
        exclude: [/[\\/]assets[\\/]icons[\\/]/, /SvgIcons/], // Exclude SvgIcons from auto-import
        resolvers: [
          // 自动注册图标组件
          IconsResolver({
            enabledCollections: ['ep'], //@iconify-json/ep 是 Element Plus 的图标库
          }),
          // 自动导入 Element Plus 组件
          ElementPlusResolver(),
        ],
        // 指定自定义组件位置(默认:src/components)
        dirs: ['src/components', 'src/**/components'],
        dts: path.resolve(pathSrc, 'types', 'components.d.ts'), //  自动导入组件类型声明文件位置，默认根目录; false 关闭自动生成
      }),

      Icons({
        // 自动安装图标库
        autoInstall: true,
        compiler: 'vue3', // 指定编译器
      }),

      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [path.resolve(pathSrc, 'assets/icons')],
        // 指定symbolId格式
        symbolId: 'icon-[dir]-[name]',
      }),
      // 添加 gzip 压缩配置
      viteCompression({
        verbose: true, // 是否在控制台输出压缩结果
        disable: false, // 是否禁用
        threshold: 10240, // 体积大于 threshold 才会被压缩，单位是 b
        algorithm: 'gzip', // 压缩算法，可选 ['gzip','brotli']
        ext: '.gz', // 生成的压缩包后缀
        deleteOriginFile: false, // 压缩后是否删除源文件
      }),
      // EnhanceLog({
      //   splitBy: ';',
      //   preTip: '',
      //   enableFileName: { enableDir: true },
      //   endLine: true,
      // })
      // 打包分析 - 只在需要时启用
      ...(process.env.ANALYZE
        ? [
            visualizer({
              filename: 'dist/stats.html',
              open: true,
              gzipSize: true,
              brotliSize: true,
            }),
          ]
        : []),
    ],
    ssr: {
      noExternal: ['workbox-window', /vue-i18n/, /element-plus/, /view-ui-plus/],
    },
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'axios',
        '@vueuse/core',
        'element-plus/es/components/message/style/css',
        'element-plus/es/components/message-box/style/css',
        'element-plus/es/components/loading/style/css',
        'lodash',
        'mitt',
        'nprogress',
        'screenfull',
        'sortablejs',
        'clipboard',
        'file-saver',
        'qrcode',
        'uuid',
      ],
      exclude: ['vue-baidu-map', 'echarts', 'html2canvas', 'jspdf'],
      // 强制预构建
      force: false,
      // 启用 esbuild 优化
      esbuildOptions: {
        target: 'esnext',
      },
    },
  }
})
